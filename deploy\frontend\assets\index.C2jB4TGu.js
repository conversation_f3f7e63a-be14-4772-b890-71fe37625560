const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.BlTuaSlh.js","assets/index.BHZI5pdK.js","assets/vue.BNx9QYep.js","assets/index.Dg-OhEXY.css","assets/authFunction.D3Be3hRy.js","assets/crud.DBBATLVN.js","assets/RoleUserStores.B27TR7Mx.js","assets/RoleDrawer.DhWadCLS.js","assets/api.CRWbL0zW.js","assets/RoleUsersStores.CCf4JEgz.js","assets/_plugin-vue_export-helper.DlAUqK2U.js","assets/RoleDrawer.DGVUQ7zx.css"])))=>i.map(i=>d[i]);
import{a as x,_ as s}from"./index.BHZI5pdK.js";import{createCrudOptions as C}from"./crud.6ZzHzdW3.js";import{R as D}from"./api.CRWbL0zW.js";import{R as P}from"./RoleMenuBtnStores.WLEOorzd.js";import{R as g}from"./RoleMenuFieldStores.CgsZ7drj.js";import{R as k}from"./RoleUsersStores.CCf4JEgz.js";import{R as B}from"./RoleUserStores.B27TR7Mx.js";import{d as a,h as E,j as M,k as t,b as S,o as h,w as v,l as e,x as y,u as o,P as n}from"./vue.BNx9QYep.js";import"./dictionary.DNsEqk19.js";import"./authFunction.D3Be3hRy.js";const A=a({name:"role"}),J=a({...A,setup(O){const _=n(()=>s(()=>import("./index.BlTuaSlh.js"),__vite__mapDeps([0,1,2,3,4,5,6]))),r=E(),c=n(()=>s(()=>import("./RoleDrawer.DhWadCLS.js"),__vite__mapDeps([7,1,2,3,8,9,10,11]))),l=D(),m=P(),f=g(),p=k(),i=B(),{crudBinding:u,crudRef:R,crudExpose:d}=x({createCrudOptions:C,context:{RoleDrawer:l,RoleMenuBtn:m,RoleMenuField:f,RoleUserDrawer:i,RoleUserRef:r}});return M(async()=>{d.doRefresh(),p.get_all_users()}),(V,F)=>{const U=t("fs-crud"),w=t("fs-page");return h(),S(w,null,{default:v(()=>[e(U,y({ref_key:"crudRef",ref:R},o(u)),null,16),e(o(c)),e(o(_),{ref_key:"RoleUserRef",ref:r},null,512)]),_:1})}}});export{J as default};
