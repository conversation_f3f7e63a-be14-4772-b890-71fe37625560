const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/aside.PpDlMa_w.js","assets/index.BHZI5pdK.js","assets/vue.BNx9QYep.js","assets/index.Dg-OhEXY.css","assets/header.DFPmrsWF.js","assets/main.6V4YA0vm.js","assets/tagsView.B9jqfD_7.js","assets/_plugin-vue_export-helper.DlAUqK2U.js","assets/tagsView.B_TLRSQC.css"])))=>i.map(i=>d[i]);
import{u as g,_ as e}from"./index.BHZI5pdK.js";import{d as m,h as E,Q as L,M as V,c as k,j as w,v as l,k as M,b as _,o as d,w as f,l as t,u as o,P as a,e as x,m as A,I as P}from"./vue.BNx9QYep.js";const b={class:"flex-center layout-backtop"},I=m({name:"layoutClassic"}),N=m({...I,setup(B){const p=a(()=>e(()=>import("./aside.PpDlMa_w.js"),__vite__mapDeps([0,1,2,3]))),y=a(()=>e(()=>import("./header.DFPmrsWF.js"),__vite__mapDeps([4,1,2,3]))),v=a(()=>e(()=>import("./main.6V4YA0vm.js"),__vite__mapDeps([5,1,2,3]))),T=a(()=>e(()=>import("./tagsView.B9jqfD_7.js"),__vite__mapDeps([6,1,2,3,7,8]))),s=E(),h=L(),R=g(),{themeConfig:r}=V(R),C=k(()=>r.value.isTagsview),c=()=>{var n;(n=s.value)==null||n.layoutMainScrollbarRef.update()},u=()=>{P(()=>{setTimeout(()=>{c(),s.value.layoutMainScrollbarRef.wrapRef.scrollTop=0},500)})};return w(()=>{u()}),l(()=>h.path,()=>{u()}),l(r,()=>{c()},{deep:!0}),(n,D)=>{const i=M("el-container");return d(),_(i,{class:"layout-container flex-center"},{default:f(()=>[t(o(y)),t(i,{class:"layout-mian-height-50"},{default:f(()=>[t(o(p)),x("div",b,[C.value?(d(),_(o(T),{key:0})):A("",!0),t(o(v),{ref_key:"layoutMainRef",ref:s},null,512)])]),_:1})]),_:1})}}});export{N as default};
