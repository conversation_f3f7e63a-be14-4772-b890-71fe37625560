{"version": 3, "sources": ["../../.pnpm/monaco-editor@0.52.2/node_modules/monaco-editor/esm/vs/basic-languages/elixir/elixir.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/elixir/elixir.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"'\", close: \"'\" },\n    { open: '\"', close: '\"' }\n  ],\n  autoClosingPairs: [\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"comment\"] },\n    { open: '\"\"\"', close: '\"\"\"' },\n    { open: \"`\", close: \"`\", notIn: [\"string\", \"comment\"] },\n    { open: \"(\", close: \")\" },\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"<<\", close: \">>\" }\n  ],\n  indentationRules: {\n    increaseIndentPattern: /^\\s*(after|else|catch|rescue|fn|[^#]*(do|<\\-|\\->|\\{|\\[|\\=))\\s*$/,\n    decreaseIndentPattern: /^\\s*((\\}|\\])\\s*$|(after|else|catch|rescue|end)\\b)/\n  }\n};\nvar language = {\n  defaultToken: \"source\",\n  tokenPostfix: \".elixir\",\n  brackets: [\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"<<\", close: \">>\", token: \"delimiter.angle.special\" }\n  ],\n  // Below are lists/regexps to which we reference later.\n  declarationKeywords: [\n    \"def\",\n    \"defp\",\n    \"defn\",\n    \"defnp\",\n    \"defguard\",\n    \"defguardp\",\n    \"defmacro\",\n    \"defmacrop\",\n    \"defdelegate\",\n    \"defcallback\",\n    \"defmacrocallback\",\n    \"defmodule\",\n    \"defprotocol\",\n    \"defexception\",\n    \"defimpl\",\n    \"defstruct\"\n  ],\n  operatorKeywords: [\"and\", \"in\", \"not\", \"or\", \"when\"],\n  namespaceKeywords: [\"alias\", \"import\", \"require\", \"use\"],\n  otherKeywords: [\n    \"after\",\n    \"case\",\n    \"catch\",\n    \"cond\",\n    \"do\",\n    \"else\",\n    \"end\",\n    \"fn\",\n    \"for\",\n    \"if\",\n    \"quote\",\n    \"raise\",\n    \"receive\",\n    \"rescue\",\n    \"super\",\n    \"throw\",\n    \"try\",\n    \"unless\",\n    \"unquote_splicing\",\n    \"unquote\",\n    \"with\"\n  ],\n  constants: [\"true\", \"false\", \"nil\"],\n  nameBuiltin: [\"__MODULE__\", \"__DIR__\", \"__ENV__\", \"__CALLER__\", \"__STACKTRACE__\"],\n  // Matches any of the operator names:\n  // <<< >>> ||| &&& ^^^ ~~~ === !== ~>> <~> |~> <|> == != <= >= && || \\\\ <> ++ -- |> =~ -> <- ~> <~ :: .. = < > + - * / | . ^ & !\n  operator: /-[->]?|!={0,2}|\\*{1,2}|\\/|\\\\\\\\|&{1,3}|\\.\\.?|\\^(?:\\^\\^)?|\\+\\+?|<(?:-|<<|=|>|\\|>|~>?)?|=~|={1,3}|>(?:=|>>)?|\\|~>|\\|>|\\|{1,3}|~>>?|~~~|::/,\n  // See https://hexdocs.pm/elixir/syntax-reference.html#variables\n  variableName: /[a-z_][a-zA-Z0-9_]*[?!]?/,\n  // See https://hexdocs.pm/elixir/syntax-reference.html#atoms\n  atomName: /[a-zA-Z_][a-zA-Z0-9_@]*[?!]?|@specialAtomName|@operator/,\n  specialAtomName: /\\.\\.\\.|<<>>|%\\{\\}|%|\\{\\}/,\n  aliasPart: /[A-Z][a-zA-Z0-9_]*/,\n  moduleName: /@aliasPart(?:\\.@aliasPart)*/,\n  // Sigil pairs are: \"\"\" \"\"\", ''' ''', \" \", ' ', / /, | |, < >, { }, [ ], ( )\n  sigilSymmetricDelimiter: /\"\"\"|'''|\"|'|\\/|\\|/,\n  sigilStartDelimiter: /@sigilSymmetricDelimiter|<|\\{|\\[|\\(/,\n  sigilEndDelimiter: /@sigilSymmetricDelimiter|>|\\}|\\]|\\)/,\n  sigilModifiers: /[a-zA-Z0-9]*/,\n  decimal: /\\d(?:_?\\d)*/,\n  hex: /[0-9a-fA-F](_?[0-9a-fA-F])*/,\n  octal: /[0-7](_?[0-7])*/,\n  binary: /[01](_?[01])*/,\n  // See https://hexdocs.pm/elixir/master/String.html#module-escape-characters\n  escape: /\\\\u[0-9a-fA-F]{4}|\\\\x[0-9a-fA-F]{2}|\\\\./,\n  // The keys below correspond to tokenizer states.\n  // We start from the root state and match against its rules\n  // until we explicitly transition into another state.\n  // The `include` simply brings in all operations from the given state\n  // and is useful for improving readability.\n  tokenizer: {\n    root: [\n      { include: \"@whitespace\" },\n      { include: \"@comments\" },\n      // Keywords start as either an identifier or a string,\n      // but end with a : so it's important to match this first.\n      { include: \"@keywordsShorthand\" },\n      { include: \"@numbers\" },\n      { include: \"@identifiers\" },\n      { include: \"@strings\" },\n      { include: \"@atoms\" },\n      { include: \"@sigils\" },\n      { include: \"@attributes\" },\n      { include: \"@symbols\" }\n    ],\n    // Whitespace\n    whitespace: [[/\\s+/, \"white\"]],\n    // Comments\n    comments: [[/(#)(.*)/, [\"comment.punctuation\", \"comment\"]]],\n    // Keyword list shorthand\n    keywordsShorthand: [\n      [/(@atomName)(:)(\\s+)/, [\"constant\", \"constant.punctuation\", \"white\"]],\n      // Use positive look-ahead to ensure the string is followed by :\n      // and should be considered a keyword.\n      [\n        /\"(?=([^\"]|#\\{.*?\\}|\\\\\")*\":)/,\n        { token: \"constant.delimiter\", next: \"@doubleQuotedStringKeyword\" }\n      ],\n      [\n        /'(?=([^']|#\\{.*?\\}|\\\\')*':)/,\n        { token: \"constant.delimiter\", next: \"@singleQuotedStringKeyword\" }\n      ]\n    ],\n    doubleQuotedStringKeyword: [\n      [/\":/, { token: \"constant.delimiter\", next: \"@pop\" }],\n      { include: \"@stringConstantContentInterpol\" }\n    ],\n    singleQuotedStringKeyword: [\n      [/':/, { token: \"constant.delimiter\", next: \"@pop\" }],\n      { include: \"@stringConstantContentInterpol\" }\n    ],\n    // Numbers\n    numbers: [\n      [/0b@binary/, \"number.binary\"],\n      [/0o@octal/, \"number.octal\"],\n      [/0x@hex/, \"number.hex\"],\n      [/@decimal\\.@decimal([eE]-?@decimal)?/, \"number.float\"],\n      [/@decimal/, \"number\"]\n    ],\n    // Identifiers\n    identifiers: [\n      // Tokenize identifier name in function-like definitions.\n      // Note: given `def a + b, do: nil`, `a` is not a function name,\n      // so we use negative look-ahead to ensure there's no operator.\n      [\n        /\\b(defp?|defnp?|defmacrop?|defguardp?|defdelegate)(\\s+)(@variableName)(?!\\s+@operator)/,\n        [\n          \"keyword.declaration\",\n          \"white\",\n          {\n            cases: {\n              unquote: \"keyword\",\n              \"@default\": \"function\"\n            }\n          }\n        ]\n      ],\n      // Tokenize function calls\n      [\n        // In-scope call - an identifier followed by ( or .(\n        /(@variableName)(?=\\s*\\.?\\s*\\()/,\n        {\n          cases: {\n            // Tokenize as keyword in cases like `if(..., do: ..., else: ...)`\n            \"@declarationKeywords\": \"keyword.declaration\",\n            \"@namespaceKeywords\": \"keyword\",\n            \"@otherKeywords\": \"keyword\",\n            \"@default\": \"function.call\"\n          }\n        }\n      ],\n      [\n        // Referencing function in a module\n        /(@moduleName)(\\s*)(\\.)(\\s*)(@variableName)/,\n        [\"type.identifier\", \"white\", \"operator\", \"white\", \"function.call\"]\n      ],\n      [\n        // Referencing function in an Erlang module\n        /(:)(@atomName)(\\s*)(\\.)(\\s*)(@variableName)/,\n        [\"constant.punctuation\", \"constant\", \"white\", \"operator\", \"white\", \"function.call\"]\n      ],\n      [\n        // Piping into a function (tokenized separately as it may not have parentheses)\n        /(\\|>)(\\s*)(@variableName)/,\n        [\n          \"operator\",\n          \"white\",\n          {\n            cases: {\n              \"@otherKeywords\": \"keyword\",\n              \"@default\": \"function.call\"\n            }\n          }\n        ]\n      ],\n      [\n        // Function reference passed to another function\n        /(&)(\\s*)(@variableName)/,\n        [\"operator\", \"white\", \"function.call\"]\n      ],\n      // Language keywords, builtins, constants and variables\n      [\n        /@variableName/,\n        {\n          cases: {\n            \"@declarationKeywords\": \"keyword.declaration\",\n            \"@operatorKeywords\": \"keyword.operator\",\n            \"@namespaceKeywords\": \"keyword\",\n            \"@otherKeywords\": \"keyword\",\n            \"@constants\": \"constant.language\",\n            \"@nameBuiltin\": \"variable.language\",\n            \"_.*\": \"comment.unused\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      // Module names\n      [/@moduleName/, \"type.identifier\"]\n    ],\n    // Strings\n    strings: [\n      [/\"\"\"/, { token: \"string.delimiter\", next: \"@doubleQuotedHeredoc\" }],\n      [/'''/, { token: \"string.delimiter\", next: \"@singleQuotedHeredoc\" }],\n      [/\"/, { token: \"string.delimiter\", next: \"@doubleQuotedString\" }],\n      [/'/, { token: \"string.delimiter\", next: \"@singleQuotedString\" }]\n    ],\n    doubleQuotedHeredoc: [\n      [/\"\"\"/, { token: \"string.delimiter\", next: \"@pop\" }],\n      { include: \"@stringContentInterpol\" }\n    ],\n    singleQuotedHeredoc: [\n      [/'''/, { token: \"string.delimiter\", next: \"@pop\" }],\n      { include: \"@stringContentInterpol\" }\n    ],\n    doubleQuotedString: [\n      [/\"/, { token: \"string.delimiter\", next: \"@pop\" }],\n      { include: \"@stringContentInterpol\" }\n    ],\n    singleQuotedString: [\n      [/'/, { token: \"string.delimiter\", next: \"@pop\" }],\n      { include: \"@stringContentInterpol\" }\n    ],\n    // Atoms\n    atoms: [\n      [/(:)(@atomName)/, [\"constant.punctuation\", \"constant\"]],\n      [/:\"/, { token: \"constant.delimiter\", next: \"@doubleQuotedStringAtom\" }],\n      [/:'/, { token: \"constant.delimiter\", next: \"@singleQuotedStringAtom\" }]\n    ],\n    doubleQuotedStringAtom: [\n      [/\"/, { token: \"constant.delimiter\", next: \"@pop\" }],\n      { include: \"@stringConstantContentInterpol\" }\n    ],\n    singleQuotedStringAtom: [\n      [/'/, { token: \"constant.delimiter\", next: \"@pop\" }],\n      { include: \"@stringConstantContentInterpol\" }\n    ],\n    // Sigils\n    // See https://elixir-lang.org/getting-started/sigils.html\n    // Sigils allow for typing values using their textual representation.\n    // All sigils start with ~ followed by a letter or\n    // multi-letter uppercase starting at Elixir v1.15.0, indicating sigil type\n    // and then a delimiter pair enclosing the textual representation.\n    // Optional modifiers are allowed after the closing delimiter.\n    // For instance a regular expressions can be written as:\n    // ~r/foo|bar/ ~r{foo|bar} ~r/foo|bar/g\n    //\n    // In general lowercase sigils allow for interpolation\n    // and escaped characters, whereas uppercase sigils don't\n    //\n    // During tokenization we want to distinguish some\n    // specific sigil types, namely string and regexp,\n    // so that they cen be themed separately.\n    //\n    // To reasonably handle all those combinations we leverage\n    // dot-separated states, so if we transition to @sigilStart.interpol.s.{.}\n    // then \"sigilStart.interpol.s\" state will match and also all\n    // the individual dot-separated parameters can be accessed.\n    sigils: [\n      [/~[a-z]@sigilStartDelimiter/, { token: \"@rematch\", next: \"@sigil.interpol\" }],\n      [/~([A-Z]+)@sigilStartDelimiter/, { token: \"@rematch\", next: \"@sigil.noInterpol\" }]\n    ],\n    sigil: [\n      [/~([a-z]|[A-Z]+)\\{/, { token: \"@rematch\", switchTo: \"@sigilStart.$S2.$1.{.}\" }],\n      [/~([a-z]|[A-Z]+)\\[/, { token: \"@rematch\", switchTo: \"@sigilStart.$S2.$1.[.]\" }],\n      [/~([a-z]|[A-Z]+)\\(/, { token: \"@rematch\", switchTo: \"@sigilStart.$S2.$1.(.)\" }],\n      [/~([a-z]|[A-Z]+)\\</, { token: \"@rematch\", switchTo: \"@sigilStart.$S2.$1.<.>\" }],\n      [\n        /~([a-z]|[A-Z]+)(@sigilSymmetricDelimiter)/,\n        { token: \"@rematch\", switchTo: \"@sigilStart.$S2.$1.$2.$2\" }\n      ]\n    ],\n    // The definitions below expect states to be of the form:\n    //\n    // sigilStart.<interpol-or-noInterpol>.<sigil-letter>.<start-delimiter>.<end-delimiter>\n    // sigilContinue.<interpol-or-noInterpol>.<sigil-letter>.<start-delimiter>.<end-delimiter>\n    //\n    // The sigilStart state is used only to properly classify the token (as string/regex/sigil)\n    // and immediately switches to the sigilContinue sate, which handles the actual content\n    // and waits for the corresponding end delimiter.\n    \"sigilStart.interpol.s\": [\n      [\n        /~s@sigilStartDelimiter/,\n        {\n          token: \"string.delimiter\",\n          switchTo: \"@sigilContinue.$S2.$S3.$S4.$S5\"\n        }\n      ]\n    ],\n    \"sigilContinue.interpol.s\": [\n      [\n        /(@sigilEndDelimiter)@sigilModifiers/,\n        {\n          cases: {\n            \"$1==$S5\": { token: \"string.delimiter\", next: \"@pop\" },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      { include: \"@stringContentInterpol\" }\n    ],\n    \"sigilStart.noInterpol.S\": [\n      [\n        /~S@sigilStartDelimiter/,\n        {\n          token: \"string.delimiter\",\n          switchTo: \"@sigilContinue.$S2.$S3.$S4.$S5\"\n        }\n      ]\n    ],\n    \"sigilContinue.noInterpol.S\": [\n      // Ignore escaped sigil end\n      [/(^|[^\\\\])\\\\@sigilEndDelimiter/, \"string\"],\n      [\n        /(@sigilEndDelimiter)@sigilModifiers/,\n        {\n          cases: {\n            \"$1==$S5\": { token: \"string.delimiter\", next: \"@pop\" },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      { include: \"@stringContent\" }\n    ],\n    \"sigilStart.interpol.r\": [\n      [\n        /~r@sigilStartDelimiter/,\n        {\n          token: \"regexp.delimiter\",\n          switchTo: \"@sigilContinue.$S2.$S3.$S4.$S5\"\n        }\n      ]\n    ],\n    \"sigilContinue.interpol.r\": [\n      [\n        /(@sigilEndDelimiter)@sigilModifiers/,\n        {\n          cases: {\n            \"$1==$S5\": { token: \"regexp.delimiter\", next: \"@pop\" },\n            \"@default\": \"regexp\"\n          }\n        }\n      ],\n      { include: \"@regexpContentInterpol\" }\n    ],\n    \"sigilStart.noInterpol.R\": [\n      [\n        /~R@sigilStartDelimiter/,\n        {\n          token: \"regexp.delimiter\",\n          switchTo: \"@sigilContinue.$S2.$S3.$S4.$S5\"\n        }\n      ]\n    ],\n    \"sigilContinue.noInterpol.R\": [\n      // Ignore escaped sigil end\n      [/(^|[^\\\\])\\\\@sigilEndDelimiter/, \"regexp\"],\n      [\n        /(@sigilEndDelimiter)@sigilModifiers/,\n        {\n          cases: {\n            \"$1==$S5\": { token: \"regexp.delimiter\", next: \"@pop\" },\n            \"@default\": \"regexp\"\n          }\n        }\n      ],\n      { include: \"@regexpContent\" }\n    ],\n    // Fallback to the generic sigil by default\n    \"sigilStart.interpol\": [\n      [\n        /~([a-z]|[A-Z]+)@sigilStartDelimiter/,\n        {\n          token: \"sigil.delimiter\",\n          switchTo: \"@sigilContinue.$S2.$S3.$S4.$S5\"\n        }\n      ]\n    ],\n    \"sigilContinue.interpol\": [\n      [\n        /(@sigilEndDelimiter)@sigilModifiers/,\n        {\n          cases: {\n            \"$1==$S5\": { token: \"sigil.delimiter\", next: \"@pop\" },\n            \"@default\": \"sigil\"\n          }\n        }\n      ],\n      { include: \"@sigilContentInterpol\" }\n    ],\n    \"sigilStart.noInterpol\": [\n      [\n        /~([a-z]|[A-Z]+)@sigilStartDelimiter/,\n        {\n          token: \"sigil.delimiter\",\n          switchTo: \"@sigilContinue.$S2.$S3.$S4.$S5\"\n        }\n      ]\n    ],\n    \"sigilContinue.noInterpol\": [\n      // Ignore escaped sigil end\n      [/(^|[^\\\\])\\\\@sigilEndDelimiter/, \"sigil\"],\n      [\n        /(@sigilEndDelimiter)@sigilModifiers/,\n        {\n          cases: {\n            \"$1==$S5\": { token: \"sigil.delimiter\", next: \"@pop\" },\n            \"@default\": \"sigil\"\n          }\n        }\n      ],\n      { include: \"@sigilContent\" }\n    ],\n    // Attributes\n    attributes: [\n      // Module @doc* attributes - tokenized as comments\n      [\n        /\\@(module|type)?doc (~[sS])?\"\"\"/,\n        {\n          token: \"comment.block.documentation\",\n          next: \"@doubleQuotedHeredocDocstring\"\n        }\n      ],\n      [\n        /\\@(module|type)?doc (~[sS])?'''/,\n        {\n          token: \"comment.block.documentation\",\n          next: \"@singleQuotedHeredocDocstring\"\n        }\n      ],\n      [\n        /\\@(module|type)?doc (~[sS])?\"/,\n        {\n          token: \"comment.block.documentation\",\n          next: \"@doubleQuotedStringDocstring\"\n        }\n      ],\n      [\n        /\\@(module|type)?doc (~[sS])?'/,\n        {\n          token: \"comment.block.documentation\",\n          next: \"@singleQuotedStringDocstring\"\n        }\n      ],\n      [/\\@(module|type)?doc false/, \"comment.block.documentation\"],\n      // Module attributes\n      [/\\@(@variableName)/, \"variable\"]\n    ],\n    doubleQuotedHeredocDocstring: [\n      [/\"\"\"/, { token: \"comment.block.documentation\", next: \"@pop\" }],\n      { include: \"@docstringContent\" }\n    ],\n    singleQuotedHeredocDocstring: [\n      [/'''/, { token: \"comment.block.documentation\", next: \"@pop\" }],\n      { include: \"@docstringContent\" }\n    ],\n    doubleQuotedStringDocstring: [\n      [/\"/, { token: \"comment.block.documentation\", next: \"@pop\" }],\n      { include: \"@docstringContent\" }\n    ],\n    singleQuotedStringDocstring: [\n      [/'/, { token: \"comment.block.documentation\", next: \"@pop\" }],\n      { include: \"@docstringContent\" }\n    ],\n    // Operators, punctuation, brackets\n    symbols: [\n      // Code point operator (either with regular character ?a or an escaped one ?\\n)\n      [/\\?(\\\\.|[^\\\\\\s])/, \"number.constant\"],\n      // Anonymous function arguments\n      [/&\\d+/, \"operator\"],\n      // Bitshift operators (must go before delimiters, so that << >> don't match first)\n      [/<<<|>>>/, \"operator\"],\n      // Delimiter pairs\n      [/[()\\[\\]\\{\\}]|<<|>>/, \"@brackets\"],\n      // Triple dot is a valid name (must go before operators, so that .. doesn't match instead)\n      [/\\.\\.\\./, \"identifier\"],\n      // Punctuation => (must go before operators, so it's not tokenized as = then >)\n      [/=>/, \"punctuation\"],\n      // Operators\n      [/@operator/, \"operator\"],\n      // Punctuation\n      [/[:;,.%]/, \"punctuation\"]\n    ],\n    // Generic helpers\n    stringContentInterpol: [\n      { include: \"@interpolation\" },\n      { include: \"@escapeChar\" },\n      { include: \"@stringContent\" }\n    ],\n    stringContent: [[/./, \"string\"]],\n    stringConstantContentInterpol: [\n      { include: \"@interpolation\" },\n      { include: \"@escapeChar\" },\n      { include: \"@stringConstantContent\" }\n    ],\n    stringConstantContent: [[/./, \"constant\"]],\n    regexpContentInterpol: [\n      { include: \"@interpolation\" },\n      { include: \"@escapeChar\" },\n      { include: \"@regexpContent\" }\n    ],\n    regexpContent: [\n      // # may be a regular regexp char, so we use a heuristic\n      // assuming a # surrounded by whitespace is actually a comment.\n      [/(\\s)(#)(\\s.*)$/, [\"white\", \"comment.punctuation\", \"comment\"]],\n      [/./, \"regexp\"]\n    ],\n    sigilContentInterpol: [\n      { include: \"@interpolation\" },\n      { include: \"@escapeChar\" },\n      { include: \"@sigilContent\" }\n    ],\n    sigilContent: [[/./, \"sigil\"]],\n    docstringContent: [[/./, \"comment.block.documentation\"]],\n    escapeChar: [[/@escape/, \"constant.character.escape\"]],\n    interpolation: [[/#{/, { token: \"delimiter.bracket.embed\", next: \"@interpolationContinue\" }]],\n    interpolationContinue: [\n      [/}/, { token: \"delimiter.bracket.embed\", next: \"@pop\" }],\n      // Interpolation brackets may contain arbitrary code,\n      // so we simply match against all the root rules,\n      // until we reach interpolation end (the above matches).\n      { include: \"@root\" }\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AASA,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,SAAS,EAAE;AAAA,IAC5C,EAAE,MAAM,OAAO,OAAO,MAAM;AAAA,IAC5B,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,MAAM,OAAO,KAAK;AAAA,EAC5B;AAAA,EACA,kBAAkB;AAAA,IAChB,uBAAuB;AAAA,IACvB,uBAAuB;AAAA,EACzB;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,UAAU;AAAA,IACR,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,IACnD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,IACxD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,IAClD,EAAE,MAAM,MAAM,OAAO,MAAM,OAAO,0BAA0B;AAAA,EAC9D;AAAA;AAAA,EAEA,qBAAqB;AAAA,IACnB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,kBAAkB,CAAC,OAAO,MAAM,OAAO,MAAM,MAAM;AAAA,EACnD,mBAAmB,CAAC,SAAS,UAAU,WAAW,KAAK;AAAA,EACvD,eAAe;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,WAAW,CAAC,QAAQ,SAAS,KAAK;AAAA,EAClC,aAAa,CAAC,cAAc,WAAW,WAAW,cAAc,gBAAgB;AAAA;AAAA;AAAA,EAGhF,UAAU;AAAA;AAAA,EAEV,cAAc;AAAA;AAAA,EAEd,UAAU;AAAA,EACV,iBAAiB;AAAA,EACjB,WAAW;AAAA,EACX,YAAY;AAAA;AAAA,EAEZ,yBAAyB;AAAA,EACzB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,gBAAgB;AAAA,EAChB,SAAS;AAAA,EACT,KAAK;AAAA,EACL,OAAO;AAAA,EACP,QAAQ;AAAA;AAAA,EAER,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMR,WAAW;AAAA,IACT,MAAM;AAAA,MACJ,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,YAAY;AAAA;AAAA;AAAA,MAGvB,EAAE,SAAS,qBAAqB;AAAA,MAChC,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,eAAe;AAAA,MAC1B,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,SAAS;AAAA,MACpB,EAAE,SAAS,UAAU;AAAA,MACrB,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,WAAW;AAAA,IACxB;AAAA;AAAA,IAEA,YAAY,CAAC,CAAC,OAAO,OAAO,CAAC;AAAA;AAAA,IAE7B,UAAU,CAAC,CAAC,WAAW,CAAC,uBAAuB,SAAS,CAAC,CAAC;AAAA;AAAA,IAE1D,mBAAmB;AAAA,MACjB,CAAC,uBAAuB,CAAC,YAAY,wBAAwB,OAAO,CAAC;AAAA;AAAA;AAAA,MAGrE;AAAA,QACE;AAAA,QACA,EAAE,OAAO,sBAAsB,MAAM,6BAA6B;AAAA,MACpE;AAAA,MACA;AAAA,QACE;AAAA,QACA,EAAE,OAAO,sBAAsB,MAAM,6BAA6B;AAAA,MACpE;AAAA,IACF;AAAA,IACA,2BAA2B;AAAA,MACzB,CAAC,MAAM,EAAE,OAAO,sBAAsB,MAAM,OAAO,CAAC;AAAA,MACpD,EAAE,SAAS,iCAAiC;AAAA,IAC9C;AAAA,IACA,2BAA2B;AAAA,MACzB,CAAC,MAAM,EAAE,OAAO,sBAAsB,MAAM,OAAO,CAAC;AAAA,MACpD,EAAE,SAAS,iCAAiC;AAAA,IAC9C;AAAA;AAAA,IAEA,SAAS;AAAA,MACP,CAAC,aAAa,eAAe;AAAA,MAC7B,CAAC,YAAY,cAAc;AAAA,MAC3B,CAAC,UAAU,YAAY;AAAA,MACvB,CAAC,uCAAuC,cAAc;AAAA,MACtD,CAAC,YAAY,QAAQ;AAAA,IACvB;AAAA;AAAA,IAEA,aAAa;AAAA;AAAA;AAAA;AAAA,MAIX;AAAA,QACE;AAAA,QACA;AAAA,UACE;AAAA,UACA;AAAA,UACA;AAAA,YACE,OAAO;AAAA,cACL,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAEA;AAAA;AAAA,QAEE;AAAA,QACA;AAAA,UACE,OAAO;AAAA;AAAA,YAEL,wBAAwB;AAAA,YACxB,sBAAsB;AAAA,YACtB,kBAAkB;AAAA,YAClB,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA;AAAA,QAEE;AAAA,QACA,CAAC,mBAAmB,SAAS,YAAY,SAAS,eAAe;AAAA,MACnE;AAAA,MACA;AAAA;AAAA,QAEE;AAAA,QACA,CAAC,wBAAwB,YAAY,SAAS,YAAY,SAAS,eAAe;AAAA,MACpF;AAAA,MACA;AAAA;AAAA,QAEE;AAAA,QACA;AAAA,UACE;AAAA,UACA;AAAA,UACA;AAAA,YACE,OAAO;AAAA,cACL,kBAAkB;AAAA,cAClB,YAAY;AAAA,YACd;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA;AAAA,QAEE;AAAA,QACA,CAAC,YAAY,SAAS,eAAe;AAAA,MACvC;AAAA;AAAA,MAEA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,wBAAwB;AAAA,YACxB,qBAAqB;AAAA,YACrB,sBAAsB;AAAA,YACtB,kBAAkB;AAAA,YAClB,cAAc;AAAA,YACd,gBAAgB;AAAA,YAChB,OAAO;AAAA,YACP,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAEA,CAAC,eAAe,iBAAiB;AAAA,IACnC;AAAA;AAAA,IAEA,SAAS;AAAA,MACP,CAAC,OAAO,EAAE,OAAO,oBAAoB,MAAM,uBAAuB,CAAC;AAAA,MACnE,CAAC,OAAO,EAAE,OAAO,oBAAoB,MAAM,uBAAuB,CAAC;AAAA,MACnE,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,sBAAsB,CAAC;AAAA,MAChE,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,sBAAsB,CAAC;AAAA,IAClE;AAAA,IACA,qBAAqB;AAAA,MACnB,CAAC,OAAO,EAAE,OAAO,oBAAoB,MAAM,OAAO,CAAC;AAAA,MACnD,EAAE,SAAS,yBAAyB;AAAA,IACtC;AAAA,IACA,qBAAqB;AAAA,MACnB,CAAC,OAAO,EAAE,OAAO,oBAAoB,MAAM,OAAO,CAAC;AAAA,MACnD,EAAE,SAAS,yBAAyB;AAAA,IACtC;AAAA,IACA,oBAAoB;AAAA,MAClB,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,OAAO,CAAC;AAAA,MACjD,EAAE,SAAS,yBAAyB;AAAA,IACtC;AAAA,IACA,oBAAoB;AAAA,MAClB,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,OAAO,CAAC;AAAA,MACjD,EAAE,SAAS,yBAAyB;AAAA,IACtC;AAAA;AAAA,IAEA,OAAO;AAAA,MACL,CAAC,kBAAkB,CAAC,wBAAwB,UAAU,CAAC;AAAA,MACvD,CAAC,MAAM,EAAE,OAAO,sBAAsB,MAAM,0BAA0B,CAAC;AAAA,MACvE,CAAC,MAAM,EAAE,OAAO,sBAAsB,MAAM,0BAA0B,CAAC;AAAA,IACzE;AAAA,IACA,wBAAwB;AAAA,MACtB,CAAC,KAAK,EAAE,OAAO,sBAAsB,MAAM,OAAO,CAAC;AAAA,MACnD,EAAE,SAAS,iCAAiC;AAAA,IAC9C;AAAA,IACA,wBAAwB;AAAA,MACtB,CAAC,KAAK,EAAE,OAAO,sBAAsB,MAAM,OAAO,CAAC;AAAA,MACnD,EAAE,SAAS,iCAAiC;AAAA,IAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAsBA,QAAQ;AAAA,MACN,CAAC,8BAA8B,EAAE,OAAO,YAAY,MAAM,kBAAkB,CAAC;AAAA,MAC7E,CAAC,iCAAiC,EAAE,OAAO,YAAY,MAAM,oBAAoB,CAAC;AAAA,IACpF;AAAA,IACA,OAAO;AAAA,MACL,CAAC,qBAAqB,EAAE,OAAO,YAAY,UAAU,yBAAyB,CAAC;AAAA,MAC/E,CAAC,qBAAqB,EAAE,OAAO,YAAY,UAAU,yBAAyB,CAAC;AAAA,MAC/E,CAAC,qBAAqB,EAAE,OAAO,YAAY,UAAU,yBAAyB,CAAC;AAAA,MAC/E,CAAC,qBAAqB,EAAE,OAAO,YAAY,UAAU,yBAAyB,CAAC;AAAA,MAC/E;AAAA,QACE;AAAA,QACA,EAAE,OAAO,YAAY,UAAU,2BAA2B;AAAA,MAC5D;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASA,yBAAyB;AAAA,MACvB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AAAA,IACA,4BAA4B;AAAA,MAC1B;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,WAAW,EAAE,OAAO,oBAAoB,MAAM,OAAO;AAAA,YACrD,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,EAAE,SAAS,yBAAyB;AAAA,IACtC;AAAA,IACA,2BAA2B;AAAA,MACzB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AAAA,IACA,8BAA8B;AAAA;AAAA,MAE5B,CAAC,iCAAiC,QAAQ;AAAA,MAC1C;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,WAAW,EAAE,OAAO,oBAAoB,MAAM,OAAO;AAAA,YACrD,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,EAAE,SAAS,iBAAiB;AAAA,IAC9B;AAAA,IACA,yBAAyB;AAAA,MACvB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AAAA,IACA,4BAA4B;AAAA,MAC1B;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,WAAW,EAAE,OAAO,oBAAoB,MAAM,OAAO;AAAA,YACrD,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,EAAE,SAAS,yBAAyB;AAAA,IACtC;AAAA,IACA,2BAA2B;AAAA,MACzB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AAAA,IACA,8BAA8B;AAAA;AAAA,MAE5B,CAAC,iCAAiC,QAAQ;AAAA,MAC1C;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,WAAW,EAAE,OAAO,oBAAoB,MAAM,OAAO;AAAA,YACrD,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,EAAE,SAAS,iBAAiB;AAAA,IAC9B;AAAA;AAAA,IAEA,uBAAuB;AAAA,MACrB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AAAA,IACA,0BAA0B;AAAA,MACxB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,WAAW,EAAE,OAAO,mBAAmB,MAAM,OAAO;AAAA,YACpD,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,EAAE,SAAS,wBAAwB;AAAA,IACrC;AAAA,IACA,yBAAyB;AAAA,MACvB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AAAA,IACA,4BAA4B;AAAA;AAAA,MAE1B,CAAC,iCAAiC,OAAO;AAAA,MACzC;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,WAAW,EAAE,OAAO,mBAAmB,MAAM,OAAO;AAAA,YACpD,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,EAAE,SAAS,gBAAgB;AAAA,IAC7B;AAAA;AAAA,IAEA,YAAY;AAAA;AAAA,MAEV;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,QACR;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,QACR;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,QACR;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,QACR;AAAA,MACF;AAAA,MACA,CAAC,6BAA6B,6BAA6B;AAAA;AAAA,MAE3D,CAAC,qBAAqB,UAAU;AAAA,IAClC;AAAA,IACA,8BAA8B;AAAA,MAC5B,CAAC,OAAO,EAAE,OAAO,+BAA+B,MAAM,OAAO,CAAC;AAAA,MAC9D,EAAE,SAAS,oBAAoB;AAAA,IACjC;AAAA,IACA,8BAA8B;AAAA,MAC5B,CAAC,OAAO,EAAE,OAAO,+BAA+B,MAAM,OAAO,CAAC;AAAA,MAC9D,EAAE,SAAS,oBAAoB;AAAA,IACjC;AAAA,IACA,6BAA6B;AAAA,MAC3B,CAAC,KAAK,EAAE,OAAO,+BAA+B,MAAM,OAAO,CAAC;AAAA,MAC5D,EAAE,SAAS,oBAAoB;AAAA,IACjC;AAAA,IACA,6BAA6B;AAAA,MAC3B,CAAC,KAAK,EAAE,OAAO,+BAA+B,MAAM,OAAO,CAAC;AAAA,MAC5D,EAAE,SAAS,oBAAoB;AAAA,IACjC;AAAA;AAAA,IAEA,SAAS;AAAA;AAAA,MAEP,CAAC,mBAAmB,iBAAiB;AAAA;AAAA,MAErC,CAAC,QAAQ,UAAU;AAAA;AAAA,MAEnB,CAAC,WAAW,UAAU;AAAA;AAAA,MAEtB,CAAC,sBAAsB,WAAW;AAAA;AAAA,MAElC,CAAC,UAAU,YAAY;AAAA;AAAA,MAEvB,CAAC,MAAM,aAAa;AAAA;AAAA,MAEpB,CAAC,aAAa,UAAU;AAAA;AAAA,MAExB,CAAC,WAAW,aAAa;AAAA,IAC3B;AAAA;AAAA,IAEA,uBAAuB;AAAA,MACrB,EAAE,SAAS,iBAAiB;AAAA,MAC5B,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,iBAAiB;AAAA,IAC9B;AAAA,IACA,eAAe,CAAC,CAAC,KAAK,QAAQ,CAAC;AAAA,IAC/B,+BAA+B;AAAA,MAC7B,EAAE,SAAS,iBAAiB;AAAA,MAC5B,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,yBAAyB;AAAA,IACtC;AAAA,IACA,uBAAuB,CAAC,CAAC,KAAK,UAAU,CAAC;AAAA,IACzC,uBAAuB;AAAA,MACrB,EAAE,SAAS,iBAAiB;AAAA,MAC5B,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,iBAAiB;AAAA,IAC9B;AAAA,IACA,eAAe;AAAA;AAAA;AAAA,MAGb,CAAC,kBAAkB,CAAC,SAAS,uBAAuB,SAAS,CAAC;AAAA,MAC9D,CAAC,KAAK,QAAQ;AAAA,IAChB;AAAA,IACA,sBAAsB;AAAA,MACpB,EAAE,SAAS,iBAAiB;AAAA,MAC5B,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,gBAAgB;AAAA,IAC7B;AAAA,IACA,cAAc,CAAC,CAAC,KAAK,OAAO,CAAC;AAAA,IAC7B,kBAAkB,CAAC,CAAC,KAAK,6BAA6B,CAAC;AAAA,IACvD,YAAY,CAAC,CAAC,WAAW,2BAA2B,CAAC;AAAA,IACrD,eAAe,CAAC,CAAC,MAAM,EAAE,OAAO,2BAA2B,MAAM,yBAAyB,CAAC,CAAC;AAAA,IAC5F,uBAAuB;AAAA,MACrB,CAAC,KAAK,EAAE,OAAO,2BAA2B,MAAM,OAAO,CAAC;AAAA;AAAA;AAAA;AAAA,MAIxD,EAAE,SAAS,QAAQ;AAAA,IACrB;AAAA,EACF;AACF;", "names": []}