import{a as c}from"./index.BHZI5pdK.js";import{createCrudOptions as p}from"./crud.Cb6lX2z0.js";import{d as o,j as f,k as e,b as u,o as _,w as d,l as m,x as l,u as i}from"./vue.BNx9QYep.js";const g=o({name:"operationLog"}),w=o({...g,setup(x){const{crudBinding:r,crudRef:n,crudExpose:s}=c({createCrudOptions:p});return f(()=>{s.doRefresh()}),(k,C)=>{const t=e("fs-crud"),a=e("fs-page");return _(),u(a,null,{default:d(()=>[m(t,l({ref_key:"crudRef",ref:n},i(r)),null,16)]),_:1})}}});export{w as default};
