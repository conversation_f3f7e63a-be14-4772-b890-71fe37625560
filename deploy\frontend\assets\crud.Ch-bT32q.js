import{r as o,d as p}from"./index.BHZI5pdK.js";import{a as r}from"./authFunction.D3Be3hRy.js";import"./vue.BNx9QYep.js";const a="/api/VIEWSETNAME/";function E(t){return o({url:a,method:"get",params:t})}function l(t){return o({url:a,method:"post",data:t})}function h(t){return o({url:a+t.id+"/",method:"put",data:t})}function w(t){return o({url:a+t+"/",method:"delete",data:{id:t}})}function m(t){return p({url:a+"export_data/",params:t,method:"get"})}function A({crudExpose:t}){const d=async e=>await E(e),u=async({form:e,row:n})=>(n.id&&(e.id=n.id),await h(e)),i=async({row:e})=>await w(e.id),s=async({form:e})=>await l(e),c=async e=>await m(e);return{crudOptions:{request:{pageRequest:d,addRequest:s,editRequest:u,delRequest:i},actionbar:{buttons:{export:{show:r("VIEWSETNAME:Export"),text:"导出",title:"导出",click(){return c(t.getSearchFormData())}},add:{show:r("VIEWSETNAME:Create")}}},rowHandle:{fixed:"right",width:200,buttons:{view:{type:"text",order:1,show:r("VIEWSETNAME:Retrieve")},edit:{type:"text",order:2,show:r("VIEWSETNAME:Update")},copy:{type:"text",order:3,show:r("VIEWSETNAME:Copy")},remove:{type:"text",order:4,show:r("VIEWSETNAME:Delete")}}},columns:{}}}}export{A as default};
