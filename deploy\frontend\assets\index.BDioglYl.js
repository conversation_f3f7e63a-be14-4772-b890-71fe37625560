import{H as f,I as m}from"./index.BHZI5pdK.js";import{createCrudOptions as i}from"./crud.ClgTIr3o.js";import{d as c,h as r,j as g,k as o,b as x,o as C,w as t,l as a,x as h,q as k,s as w}from"./vue.BNx9QYep.js";const y=c({name:"downloadCenter"}),b=c({...y,setup(B){const s=r(),n=r(),{crudExpose:e}=f({crudRef:s,crudBinding:n}),{crudOptions:_}=i({crudExpose:e});return m({crudExpose:e,crudOptions:_,context:{}}),g(async()=>{e.doRefresh()}),(R,v)=>{const d=o("el-tag"),l=o("fs-crud"),u=o("fs-page");return C(),x(u,null,{default:t(()=>[a(l,h({ref_key:"crudRef",ref:s},n.value),{cell_url:t(p=>[a(d,{size:"small"},{default:t(()=>[k(w(p.row.url),1)]),_:2},1024)]),_:1},16)]),_:1})}}});export{b as default};
