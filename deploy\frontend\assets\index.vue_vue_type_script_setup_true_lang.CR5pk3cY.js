import{a as B,c as y,b as T,E as D,X as u,e as E}from"./index.BHZI5pdK.js";import{B as M,c as O}from"./crud.BYCW0fyj.js";import{d as S,h as N,c as V,k as o,b as X,o as $,x as q,u as c,w as t,l as a,q as z,s as A}from"./vue.BNx9QYep.js";const J=S({__name:"index",setup(P,{expose:f}){let i=N({name:null});const{crudRef:m,crudBinding:g,crudExpose:s,context:U,selectedRows:l}=B({createCrudOptions:O,context:{selectOptions:i}}),{doRefresh:h,setTableData:b}=s,r=V(()=>l.value.length),x=async()=>{await D.confirm(`确定要批量删除这${l.value.length}条记录吗`,"确认",{distinguishCancelAndClose:!0,confirmButtonText:"确定",cancelButtonText:"取消",closeOnClickModal:!1}),await M(u.pluck(l.value,"id")),E.info("删除成功"),l.value=[],await s.doRefresh()},v=e=>{const p=s.getBaseTableRef(),n=s.getTableData();u.pluck(n,"id").includes(e.id)?p.toggleRowSelection(e,!1):l.value=u.remove(l.value,d=>d.id!==e.id)};return f({selectOptions:i,handleRefreshTable:e=>{!e.is_catalog&&e.id?(i.value=e,h()):b([])}}),(e,p)=>{const n=o("el-button"),d=o("el-tooltip"),_=o("el-table-column"),R=o("el-table"),k=o("el-popover"),w=o("fs-crud");return $(),X(w,q({ref_key:"crudRef",ref:m},c(g)),{"pagination-left":t(()=>[a(d,{content:"批量删除"},{default:t(()=>[a(n,{text:"",type:"danger",disabled:r.value===0,icon:c(T),circle:"",onClick:x},null,8,["disabled","icon"])]),_:1})]),"pagination-right":t(()=>[a(k,{placement:"top",width:400,trigger:"click"},{reference:t(()=>[a(n,{text:"",type:r.value>0?"primary":""},{default:t(()=>[z("已选中"+A(r.value)+"条数据",1)]),_:1},8,["type"])]),default:t(()=>[a(R,{data:c(l),size:"small"},{default:t(()=>[a(_,{width:"150",property:"id",label:"id"}),a(_,{fixed:"right",label:"操作","min-width":"60"},{default:t(C=>[a(n,{text:"",type:"info",icon:c(y),onClick:F=>v(C.row),circle:""},null,8,["icon","onClick"])]),_:1})]),_:1},8,["data"])]),_:1})]),_:1},16)}}});export{J as _};
