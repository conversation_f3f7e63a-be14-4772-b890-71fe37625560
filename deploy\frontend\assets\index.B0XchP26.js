import{a as m}from"./index.BHZI5pdK.js";import{G as u,c as d}from"./crud.Do6ArA10.js";import{h as f}from"./columnPermission.Dqg2nzcy.js";import{d as s,j as _,k as o,b as l,o as C,w as h,l as x,x as g,u as k}from"./vue.BNx9QYep.js";import"./dictionary.DNsEqk19.js";import"./authFunction.D3Be3hRy.js";import"./index.tdFpgju_.js";import"./_plugin-vue_export-helper.DlAUqK2U.js";const w=s({name:"areas"}),M=s({...w,setup(O){const{crudBinding:r,crudRef:t,crudExpose:n,crudOptions:a,resetCrudOptions:c}=m({createCrudOptions:d});return _(async()=>{const e=await f(u,a);c(e),n.doRefresh()}),(e,B)=>{const p=o("fs-crud"),i=o("fs-page");return C(),l(i,null,{default:h(()=>[x(p,g({ref_key:"crudRef",ref:t},k(r)),null,16)]),_:1})}}});export{M as default};
