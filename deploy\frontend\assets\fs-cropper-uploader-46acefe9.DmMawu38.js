import{j as $,k as G,C as J}from"./index.BHZI5pdK.js";import{d as K,k as R,a as c,o as l,e as m,l as g,b as w,w as T,m as h,F as M,p as X,r as S,x as Y,f as z,h as d,v as Z,c as ee,g as te,C as ae}from"./vue.BNx9QYep.js";const oe=K({name:"FsCropperUploader",props:{modelValue:{type:[String,Object,Array]},img:{},type:{type:String},uploadTip:{type:String},title:String,cropperHeight:{type:[String,Number]},dialogWidth:{type:[String,Number],default:"50%"},maxSize:{type:Number,default:5},limit:{type:Number,default:1},accept:{type:String,default:".jpg, .jpeg, .png, .gif, .webp"},cropper:{type:Object},uploader:{type:Object},compressQuality:{type:Number,default:.8},buildUrl:{type:Function,default:async function(e){return typeof e=="object"?e.url:e}},valueType:{type:String,default:"url"},disabled:{}},emits:["update:modelValue","change","ready"],setup(e,n){const{ui:b}=G(),f=d(),I=d(),C=d(),s=d([]),p=b.formItem.injectFormItemContext();let v=e.modelValue;r(e.modelValue);async function r(t){const a=[];if(t==null||t===""){s.value=a;return}if(typeof t=="string")a.push({url:await e.buildUrl(t),value:t,status:"done"});else if(Array.isArray(t))for(const o of t)a.push({url:await e.buildUrl(o),value:o,status:"done"});else if(typeof t=="object")a.push({url:await e.buildUrl(t),value:t,status:"done"});else for(const o of t)a.push({url:await e.buildUrl(o),value:o,status:"done"});s.value=a}function y(){e.disabled||(C.value=void 0,f.value.clear(),f.value.open())}function P(t){s.value.splice(t,1),j()}function A(){const t=s.value;if(t&&t.length>0){for(const a of t)if(a.status==="uploading")return!0}return!1}async function D(t){const a=t.blob,o=t.dataUrl,B=t.file.name,Q=new File([a],B,{type:a.type}),u=te({url:void 0,dataUrl:o,status:"uploading",progress:0}),W=i=>{u.progress=i.percent},F=i=>{u.status="error",u.message="文件上传出错:"+i.message,console.error(i)},L={file:Q,onProgress:W,onError:F,fileName:B};s.value.push(u);try{const i=await V(L);let N=i;e.valueType!=="object"&&(N=i[e.valueType]),u.url=await e.buildUrl(N),u.value=N,u.status="done",j()}catch(i){F(i)}}async function V(t){t.options=e.uploader||{};const{getUploaderImpl:a}=J();let o=await a(t.options.type);if(o==null)throw new Error("Sorry，The component is not ready yet");return await(o==null?void 0:o.upload(t))}async function j(){const t=[];for(const o of s.value)typeof o=="string"?t.push(o):t.push(o.value);let a=t;e.limit===1&&(a=t&&t.length>0?t[0]:void 0),v=a,n.emit("update:modelValue",a),await p.onChange(),await p.onBlur()}function x(t){return t.dataUrl?t.dataUrl:t.url}const k=d(!1),U=d();function O(t){k.value=!0,U.value=x(t)}function _(){k.value=!1,U.value=null}Z(()=>e.modelValue,async t=>{n.emit("change",t),t!==v&&await r(t)});const q=ae();function E(t){n.emit("ready",{uploaderRef:q,...t})}const H=ee(()=>({...e}));return{ui:b,cropperRef:f,uploaderImplRef:I,indexRef:C,listRef:s,addNewImage:y,hasUploading:A,cropComplete:D,doUpload:V,removeImage:P,getImageSrc:x,previewUrl:U,previewVisible:k,preview:O,closePreview:_,doReady:E,computedProps:H}}}),re={class:"image-list"},le={class:"image-slot"},se={class:"delete"},ie={key:0,class:"status-uploading"},ne={key:1,class:"status-done"},pe={class:"fs-cropper-preview-content"},ue=["src"];function ce(e,n,b,f,I,C){const s=R("fs-loading"),p=R("fs-icon"),v=R("fs-cropper");return l(),c("div",{class:z(["fs-cropper-uploader",{"is-disabled":e.computedProps.disabled}])},[m("div",re,[(l(),w(S(e.ui.imageGroup.name),null,{default:T(()=>[(l(!0),c(M,null,X(e.listRef,(r,y)=>(l(),c("div",{key:y,class:"image-item"},[(l(),w(S(e.ui.image.name),Y({class:"image",src:e.getImageSrc(r),ref_for:!0},e.computedProps.img),{placeholder:T(()=>[m("div",le,[g(s,{loading:!0})])]),_:2},1040,["src"])),m("div",se,[e.computedProps.disabled?h("",!0):(l(),w(p,{key:0,icon:e.ui.icons.remove,onClick:P=>e.removeImage(y)},null,8,["icon","onClick"])),g(p,{icon:e.ui.icons.search,onClick:P=>e.preview(r)},null,8,["icon","onClick"])]),r.status==="uploading"?(l(),c("div",ie,[(l(),w(S(e.ui.progress.name),{type:"circle",percentage:r.progress,width:70},null,8,["percentage"]))])):r.status==="done"?(l(),c("div",ne,[g(p,{icon:e.ui.icons.check,class:"status-down-icon"},null,8,["icon"])])):h("",!0)]))),128)),e.computedProps.limit<=0||e.computedProps.limit>e.listRef.length?(l(),c("div",{key:0,class:"image-item image-plus",onClick:n[0]||(n[0]=(...r)=>e.addNewImage&&e.addNewImage(...r))},[g(p,{icon:e.ui.icons.plus,class:"cropper-uploader-icon"},null,8,["icon"])])):h("",!0)]),_:1}))]),g(v,{ref:"cropperRef",title:e.computedProps.title,"cropper-height":e.computedProps.cropperHeight,"dialog-width":e.computedProps.dialogWidth,accept:e.computedProps.accept,"upload-tip":e.computedProps.uploadTip,"max-size":e.computedProps.maxSize,cropper:e.computedProps.cropper,"compress-quality":e.computedProps.compressQuality,output:"all",onDone:e.cropComplete,onReady:e.doReady},null,8,["title","cropper-height","dialog-width","accept","upload-tip","max-size","cropper","compress-quality","onDone","onReady"]),m("div",{class:z(["fs-cropper-preview",{open:e.previewVisible}]),onClick:n[1]||(n[1]=(...r)=>e.closePreview&&e.closePreview(...r))},[m("div",pe,[e.previewUrl?(l(),c("img",{key:0,src:e.previewUrl,class:"preview-image"},null,8,ue)):h("",!0)])],2)],2)}const ge=$(oe,[["render",ce]]);export{ge as default};
