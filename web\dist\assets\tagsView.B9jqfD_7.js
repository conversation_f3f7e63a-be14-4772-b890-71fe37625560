const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/contextmenu.DlveCinl.js","assets/vue.BNx9QYep.js","assets/_plugin-vue_export-helper.DlAUqK2U.js","assets/contextmenu.D-H-VSW3.css"])))=>i.map(i=>d[i]);
import{T as qt,u as vn,z as wn,V as Xt,l as ye,U as bn,_ as yn,S as Ee,e as En,as as Bt}from"./index.BHZI5pdK.js";import{d as tn,h as Ge,M as Yt,Q as _n,R as Dn,g as Sn,c as gt,N as Tn,O as Cn,a1 as In,j as On,X as An,v as Vn,k as Ht,a as Le,o as he,l as mt,w as xn,e as Wt,f as vt,F as Ut,p as Pn,Z as Re,m as Ne,b as wt,s as Ln,u as Rn,P as Nn,I as bt}from"./vue.BNx9QYep.js";import{_ as Mn}from"./_plugin-vue_export-helper.DlAUqK2U.js";/**!
* Sortable 1.15.6
* <AUTHOR>   <<EMAIL>>
* <AUTHOR>    <<EMAIL>>
* @license MIT
*/function jt(i,e){var t=Object.keys(i);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(i);e&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(i,a).enumerable})),t.push.apply(t,n)}return t}function re(i){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?jt(Object(t),!0).forEach(function(n){kn(i,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(i,Object.getOwnPropertyDescriptors(t)):jt(Object(t)).forEach(function(n){Object.defineProperty(i,n,Object.getOwnPropertyDescriptor(t,n))})}return i}function et(i){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?et=function(e){return typeof e}:et=function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},et(i)}function kn(i,e,t){return e in i?Object.defineProperty(i,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):i[e]=t,i}function fe(){return fe=Object.assign||function(i){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(i[n]=t[n])}return i},fe.apply(this,arguments)}function Fn(i,e){if(i==null)return{};var t={},n=Object.keys(i),a,r;for(r=0;r<n.length;r++)a=n[r],!(e.indexOf(a)>=0)&&(t[a]=i[a]);return t}function qn(i,e){if(i==null)return{};var t=Fn(i,e),n,a;if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(i);for(a=0;a<r.length;a++)n=r[a],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(i,n)&&(t[n]=i[n])}return t}var Xn="1.15.6";function ce(i){if(typeof window<"u"&&window.navigator)return!!navigator.userAgent.match(i)}var de=ce(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),We=ce(/Edge/i),$t=ce(/firefox/i),qe=ce(/safari/i)&&!ce(/chrome/i)&&!ce(/android/i),Lt=ce(/iP(ad|od|hone)/i),nn=ce(/chrome/i)&&ce(/android/i),an={capture:!1,passive:!1};function C(i,e,t){i.addEventListener(e,t,!de&&an)}function T(i,e,t){i.removeEventListener(e,t,!de&&an)}function rt(i,e){if(e){if(e[0]===">"&&(e=e.substring(1)),i)try{if(i.matches)return i.matches(e);if(i.msMatchesSelector)return i.msMatchesSelector(e);if(i.webkitMatchesSelector)return i.webkitMatchesSelector(e)}catch{return!1}return!1}}function rn(i){return i.host&&i!==document&&i.host.nodeType?i.host:i.parentNode}function Q(i,e,t,n){if(i){t=t||document;do{if(e!=null&&(e[0]===">"?i.parentNode===t&&rt(i,e):rt(i,e))||n&&i===t)return i;if(i===t)break}while(i=rn(i))}return null}var zt=/\s+/g;function j(i,e,t){if(i&&e)if(i.classList)i.classList[t?"add":"remove"](e);else{var n=(" "+i.className+" ").replace(zt," ").replace(" "+e+" "," ");i.className=(n+(t?" "+e:"")).replace(zt," ")}}function b(i,e,t){var n=i&&i.style;if(n){if(t===void 0)return document.defaultView&&document.defaultView.getComputedStyle?t=document.defaultView.getComputedStyle(i,""):i.currentStyle&&(t=i.currentStyle),e===void 0?t:t[e];!(e in n)&&e.indexOf("webkit")===-1&&(e="-webkit-"+e),n[e]=t+(typeof t=="string"?"":"px")}}function Ve(i,e){var t="";if(typeof i=="string")t=i;else do{var n=b(i,"transform");n&&n!=="none"&&(t=n+" "+t)}while(!e&&(i=i.parentNode));var a=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return a&&new a(t)}function on(i,e,t){if(i){var n=i.getElementsByTagName(e),a=0,r=n.length;if(t)for(;a<r;a++)t(n[a],a);return n}return[]}function ae(){var i=document.scrollingElement;return i||document.documentElement}function M(i,e,t,n,a){if(!(!i.getBoundingClientRect&&i!==window)){var r,s,u,f,d,m,g;if(i!==window&&i.parentNode&&i!==ae()?(r=i.getBoundingClientRect(),s=r.top,u=r.left,f=r.bottom,d=r.right,m=r.height,g=r.width):(s=0,u=0,f=window.innerHeight,d=window.innerWidth,m=window.innerHeight,g=window.innerWidth),(e||t)&&i!==window&&(a=a||i.parentNode,!de))do if(a&&a.getBoundingClientRect&&(b(a,"transform")!=="none"||t&&b(a,"position")!=="static")){var E=a.getBoundingClientRect();s-=E.top+parseInt(b(a,"border-top-width")),u-=E.left+parseInt(b(a,"border-left-width")),f=s+r.height,d=u+r.width;break}while(a=a.parentNode);if(n&&i!==window){var D=Ve(a||i),c=D&&D.a,I=D&&D.d;D&&(s/=I,u/=c,g/=c,m/=I,f=s+m,d=u+g)}return{top:s,left:u,bottom:f,right:d,width:g,height:m}}}function Gt(i,e,t){for(var n=me(i,!0),a=M(i)[e];n;){var r=M(n)[t],s=void 0;if(s=a>=r,!s)return n;if(n===ae())break;n=me(n,!1)}return!1}function xe(i,e,t,n){for(var a=0,r=0,s=i.children;r<s.length;){if(s[r].style.display!=="none"&&s[r]!==y.ghost&&(n||s[r]!==y.dragged)&&Q(s[r],t.draggable,i,!1)){if(a===e)return s[r];a++}r++}return null}function Rt(i,e){for(var t=i.lastElementChild;t&&(t===y.ghost||b(t,"display")==="none"||e&&!rt(t,e));)t=t.previousElementSibling;return t||null}function z(i,e){var t=0;if(!i||!i.parentNode)return-1;for(;i=i.previousElementSibling;)i.nodeName.toUpperCase()!=="TEMPLATE"&&i!==y.clone&&(!e||rt(i,e))&&t++;return t}function Kt(i){var e=0,t=0,n=ae();if(i)do{var a=Ve(i),r=a.a,s=a.d;e+=i.scrollLeft*r,t+=i.scrollTop*s}while(i!==n&&(i=i.parentNode));return[e,t]}function Bn(i,e){for(var t in i)if(i.hasOwnProperty(t)){for(var n in e)if(e.hasOwnProperty(n)&&e[n]===i[t][n])return Number(t)}return-1}function me(i,e){if(!i||!i.getBoundingClientRect)return ae();var t=i,n=!1;do if(t.clientWidth<t.scrollWidth||t.clientHeight<t.scrollHeight){var a=b(t);if(t.clientWidth<t.scrollWidth&&(a.overflowX=="auto"||a.overflowX=="scroll")||t.clientHeight<t.scrollHeight&&(a.overflowY=="auto"||a.overflowY=="scroll")){if(!t.getBoundingClientRect||t===document.body)return ae();if(n||e)return t;n=!0}}while(t=t.parentNode);return ae()}function Yn(i,e){if(i&&e)for(var t in e)e.hasOwnProperty(t)&&(i[t]=e[t]);return i}function yt(i,e){return Math.round(i.top)===Math.round(e.top)&&Math.round(i.left)===Math.round(e.left)&&Math.round(i.height)===Math.round(e.height)&&Math.round(i.width)===Math.round(e.width)}var Xe;function sn(i,e){return function(){if(!Xe){var t=arguments,n=this;t.length===1?i.call(n,t[0]):i.apply(n,t),Xe=setTimeout(function(){Xe=void 0},e)}}}function Hn(){clearTimeout(Xe),Xe=void 0}function ln(i,e,t){i.scrollLeft+=e,i.scrollTop+=t}function un(i){var e=window.Polymer,t=window.jQuery||window.Zepto;return e&&e.dom?e.dom(i).cloneNode(!0):t?t(i).clone(!0)[0]:i.cloneNode(!0)}function cn(i,e,t){var n={};return Array.from(i.children).forEach(function(a){var r,s,u,f;if(!(!Q(a,e.draggable,i,!1)||a.animated||a===t)){var d=M(a);n.left=Math.min((r=n.left)!==null&&r!==void 0?r:1/0,d.left),n.top=Math.min((s=n.top)!==null&&s!==void 0?s:1/0,d.top),n.right=Math.max((u=n.right)!==null&&u!==void 0?u:-1/0,d.right),n.bottom=Math.max((f=n.bottom)!==null&&f!==void 0?f:-1/0,d.bottom)}}),n.width=n.right-n.left,n.height=n.bottom-n.top,n.x=n.left,n.y=n.top,n}var W="Sortable"+new Date().getTime();function Wn(){var i=[],e;return{captureAnimationState:function(){if(i=[],!!this.options.animation){var n=[].slice.call(this.el.children);n.forEach(function(a){if(!(b(a,"display")==="none"||a===y.ghost)){i.push({target:a,rect:M(a)});var r=re({},i[i.length-1].rect);if(a.thisAnimationDuration){var s=Ve(a,!0);s&&(r.top-=s.f,r.left-=s.e)}a.fromRect=r}})}},addAnimationState:function(n){i.push(n)},removeAnimationState:function(n){i.splice(Bn(i,{target:n}),1)},animateAll:function(n){var a=this;if(!this.options.animation){clearTimeout(e),typeof n=="function"&&n();return}var r=!1,s=0;i.forEach(function(u){var f=0,d=u.target,m=d.fromRect,g=M(d),E=d.prevFromRect,D=d.prevToRect,c=u.rect,I=Ve(d,!0);I&&(g.top-=I.f,g.left-=I.e),d.toRect=g,d.thisAnimationDuration&&yt(E,g)&&!yt(m,g)&&(c.top-g.top)/(c.left-g.left)===(m.top-g.top)/(m.left-g.left)&&(f=jn(c,E,D,a.options)),yt(g,m)||(d.prevFromRect=m,d.prevToRect=g,f||(f=a.options.animation),a.animate(d,c,g,f)),f&&(r=!0,s=Math.max(s,f),clearTimeout(d.animationResetTimer),d.animationResetTimer=setTimeout(function(){d.animationTime=0,d.prevFromRect=null,d.fromRect=null,d.prevToRect=null,d.thisAnimationDuration=null},f),d.thisAnimationDuration=f)}),clearTimeout(e),r?e=setTimeout(function(){typeof n=="function"&&n()},s):typeof n=="function"&&n(),i=[]},animate:function(n,a,r,s){if(s){b(n,"transition",""),b(n,"transform","");var u=Ve(this.el),f=u&&u.a,d=u&&u.d,m=(a.left-r.left)/(f||1),g=(a.top-r.top)/(d||1);n.animatingX=!!m,n.animatingY=!!g,b(n,"transform","translate3d("+m+"px,"+g+"px,0)"),this.forRepaintDummy=Un(n),b(n,"transition","transform "+s+"ms"+(this.options.easing?" "+this.options.easing:"")),b(n,"transform","translate3d(0,0,0)"),typeof n.animated=="number"&&clearTimeout(n.animated),n.animated=setTimeout(function(){b(n,"transition",""),b(n,"transform",""),n.animated=!1,n.animatingX=!1,n.animatingY=!1},s)}}}}function Un(i){return i.offsetWidth}function jn(i,e,t,n){return Math.sqrt(Math.pow(e.top-i.top,2)+Math.pow(e.left-i.left,2))/Math.sqrt(Math.pow(e.top-t.top,2)+Math.pow(e.left-t.left,2))*n.animation}var Ce=[],Et={initializeByDefault:!0},Ue={mount:function(e){for(var t in Et)Et.hasOwnProperty(t)&&!(t in e)&&(e[t]=Et[t]);Ce.forEach(function(n){if(n.pluginName===e.pluginName)throw"Sortable: Cannot mount plugin ".concat(e.pluginName," more than once")}),Ce.push(e)},pluginEvent:function(e,t,n){var a=this;this.eventCanceled=!1,n.cancel=function(){a.eventCanceled=!0};var r=e+"Global";Ce.forEach(function(s){t[s.pluginName]&&(t[s.pluginName][r]&&t[s.pluginName][r](re({sortable:t},n)),t.options[s.pluginName]&&t[s.pluginName][e]&&t[s.pluginName][e](re({sortable:t},n)))})},initializePlugins:function(e,t,n,a){Ce.forEach(function(u){var f=u.pluginName;if(!(!e.options[f]&&!u.initializeByDefault)){var d=new u(e,t,e.options);d.sortable=e,d.options=e.options,e[f]=d,fe(n,d.defaults)}});for(var r in e.options)if(e.options.hasOwnProperty(r)){var s=this.modifyOption(e,r,e.options[r]);typeof s<"u"&&(e.options[r]=s)}},getEventProperties:function(e,t){var n={};return Ce.forEach(function(a){typeof a.eventProperties=="function"&&fe(n,a.eventProperties.call(t[a.pluginName],e))}),n},modifyOption:function(e,t,n){var a;return Ce.forEach(function(r){e[r.pluginName]&&r.optionListeners&&typeof r.optionListeners[t]=="function"&&(a=r.optionListeners[t].call(e[r.pluginName],n))}),a}};function $n(i){var e=i.sortable,t=i.rootEl,n=i.name,a=i.targetEl,r=i.cloneEl,s=i.toEl,u=i.fromEl,f=i.oldIndex,d=i.newIndex,m=i.oldDraggableIndex,g=i.newDraggableIndex,E=i.originalEvent,D=i.putSortable,c=i.extraEventProperties;if(e=e||t&&t[W],!!e){var I,O=e.options,G="on"+n.charAt(0).toUpperCase()+n.substr(1);window.CustomEvent&&!de&&!We?I=new CustomEvent(n,{bubbles:!0,cancelable:!0}):(I=document.createEvent("Event"),I.initEvent(n,!0,!0)),I.to=s||t,I.from=u||t,I.item=a||t,I.clone=r,I.oldIndex=f,I.newIndex=d,I.oldDraggableIndex=m,I.newDraggableIndex=g,I.originalEvent=E,I.pullMode=D?D.lastPutMode:void 0;var R=re(re({},c),Ue.getEventProperties(n,e));for(var L in R)I[L]=R[L];t&&t.dispatchEvent(I),O[G]&&O[G].call(e,I)}}var zn=["evt"],H=function(e,t){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},a=n.evt,r=qn(n,zn);Ue.pluginEvent.bind(y)(e,t,re({dragEl:p,parentEl:P,ghostEl:S,rootEl:V,nextEl:Se,lastDownEl:tt,cloneEl:x,cloneHidden:ge,dragStarted:Me,putSortable:F,activeSortable:y.active,originalEvent:a,oldIndex:Ae,oldDraggableIndex:Be,newIndex:$,newDraggableIndex:pe,hideGhostForTarget:pn,unhideGhostForTarget:gn,cloneNowHidden:function(){ge=!0},cloneNowShown:function(){ge=!1},dispatchSortableEvent:function(u){Y({sortable:t,name:u,originalEvent:a})}},r))};function Y(i){$n(re({putSortable:F,cloneEl:x,targetEl:p,rootEl:V,oldIndex:Ae,oldDraggableIndex:Be,newIndex:$,newDraggableIndex:pe},i))}var p,P,S,V,Se,tt,x,ge,Ae,$,Be,pe,Ke,F,Oe=!1,ot=!1,st=[],_e,Z,_t,Dt,Zt,Qt,Me,Ie,Ye,He=!1,Ze=!1,nt,B,St=[],At=!1,lt=[],ct=typeof document<"u",Qe=Lt,Jt=We||de?"cssFloat":"float",Gn=ct&&!nn&&!Lt&&"draggable"in document.createElement("div"),fn=function(){if(ct){if(de)return!1;var i=document.createElement("x");return i.style.cssText="pointer-events:auto",i.style.pointerEvents==="auto"}}(),dn=function(e,t){var n=b(e),a=parseInt(n.width)-parseInt(n.paddingLeft)-parseInt(n.paddingRight)-parseInt(n.borderLeftWidth)-parseInt(n.borderRightWidth),r=xe(e,0,t),s=xe(e,1,t),u=r&&b(r),f=s&&b(s),d=u&&parseInt(u.marginLeft)+parseInt(u.marginRight)+M(r).width,m=f&&parseInt(f.marginLeft)+parseInt(f.marginRight)+M(s).width;if(n.display==="flex")return n.flexDirection==="column"||n.flexDirection==="column-reverse"?"vertical":"horizontal";if(n.display==="grid")return n.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(r&&u.float&&u.float!=="none"){var g=u.float==="left"?"left":"right";return s&&(f.clear==="both"||f.clear===g)?"vertical":"horizontal"}return r&&(u.display==="block"||u.display==="flex"||u.display==="table"||u.display==="grid"||d>=a&&n[Jt]==="none"||s&&n[Jt]==="none"&&d+m>a)?"vertical":"horizontal"},Kn=function(e,t,n){var a=n?e.left:e.top,r=n?e.right:e.bottom,s=n?e.width:e.height,u=n?t.left:t.top,f=n?t.right:t.bottom,d=n?t.width:t.height;return a===u||r===f||a+s/2===u+d/2},Zn=function(e,t){var n;return st.some(function(a){var r=a[W].options.emptyInsertThreshold;if(!(!r||Rt(a))){var s=M(a),u=e>=s.left-r&&e<=s.right+r,f=t>=s.top-r&&t<=s.bottom+r;if(u&&f)return n=a}}),n},hn=function(e){function t(r,s){return function(u,f,d,m){var g=u.options.group.name&&f.options.group.name&&u.options.group.name===f.options.group.name;if(r==null&&(s||g))return!0;if(r==null||r===!1)return!1;if(s&&r==="clone")return r;if(typeof r=="function")return t(r(u,f,d,m),s)(u,f,d,m);var E=(s?u:f).options.group.name;return r===!0||typeof r=="string"&&r===E||r.join&&r.indexOf(E)>-1}}var n={},a=e.group;(!a||et(a)!="object")&&(a={name:a}),n.name=a.name,n.checkPull=t(a.pull,!0),n.checkPut=t(a.put),n.revertClone=a.revertClone,e.group=n},pn=function(){!fn&&S&&b(S,"display","none")},gn=function(){!fn&&S&&b(S,"display","")};ct&&!nn&&document.addEventListener("click",function(i){if(ot)return i.preventDefault(),i.stopPropagation&&i.stopPropagation(),i.stopImmediatePropagation&&i.stopImmediatePropagation(),ot=!1,!1},!0);var De=function(e){if(p){e=e.touches?e.touches[0]:e;var t=Zn(e.clientX,e.clientY);if(t){var n={};for(var a in e)e.hasOwnProperty(a)&&(n[a]=e[a]);n.target=n.rootEl=t,n.preventDefault=void 0,n.stopPropagation=void 0,t[W]._onDragOver(n)}}},Qn=function(e){p&&p.parentNode[W]._isOutsideThisEl(e.target)};function y(i,e){if(!(i&&i.nodeType&&i.nodeType===1))throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(i));this.el=i,this.options=e=fe({},e),i[W]=this;var t={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(i.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return dn(i,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(s,u){s.setData("Text",u.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:y.supportPointer!==!1&&"PointerEvent"in window&&(!qe||Lt),emptyInsertThreshold:5};Ue.initializePlugins(this,i,t);for(var n in t)!(n in e)&&(e[n]=t[n]);hn(e);for(var a in this)a.charAt(0)==="_"&&typeof this[a]=="function"&&(this[a]=this[a].bind(this));this.nativeDraggable=e.forceFallback?!1:Gn,this.nativeDraggable&&(this.options.touchStartThreshold=1),e.supportPointer?C(i,"pointerdown",this._onTapStart):(C(i,"mousedown",this._onTapStart),C(i,"touchstart",this._onTapStart)),this.nativeDraggable&&(C(i,"dragover",this),C(i,"dragenter",this)),st.push(this.el),e.store&&e.store.get&&this.sort(e.store.get(this)||[]),fe(this,Wn())}y.prototype={constructor:y,_isOutsideThisEl:function(e){!this.el.contains(e)&&e!==this.el&&(Ie=null)},_getDirection:function(e,t){return typeof this.options.direction=="function"?this.options.direction.call(this,e,t,p):this.options.direction},_onTapStart:function(e){if(e.cancelable){var t=this,n=this.el,a=this.options,r=a.preventOnFilter,s=e.type,u=e.touches&&e.touches[0]||e.pointerType&&e.pointerType==="touch"&&e,f=(u||e).target,d=e.target.shadowRoot&&(e.path&&e.path[0]||e.composedPath&&e.composedPath()[0])||f,m=a.filter;if(oi(n),!p&&!(/mousedown|pointerdown/.test(s)&&e.button!==0||a.disabled)&&!d.isContentEditable&&!(!this.nativeDraggable&&qe&&f&&f.tagName.toUpperCase()==="SELECT")&&(f=Q(f,a.draggable,n,!1),!(f&&f.animated)&&tt!==f)){if(Ae=z(f),Be=z(f,a.draggable),typeof m=="function"){if(m.call(this,e,f,this)){Y({sortable:t,rootEl:d,name:"filter",targetEl:f,toEl:n,fromEl:n}),H("filter",t,{evt:e}),r&&e.preventDefault();return}}else if(m&&(m=m.split(",").some(function(g){if(g=Q(d,g.trim(),n,!1),g)return Y({sortable:t,rootEl:g,name:"filter",targetEl:f,fromEl:n,toEl:n}),H("filter",t,{evt:e}),!0}),m)){r&&e.preventDefault();return}a.handle&&!Q(d,a.handle,n,!1)||this._prepareDragStart(e,u,f)}}},_prepareDragStart:function(e,t,n){var a=this,r=a.el,s=a.options,u=r.ownerDocument,f;if(n&&!p&&n.parentNode===r){var d=M(n);if(V=r,p=n,P=p.parentNode,Se=p.nextSibling,tt=n,Ke=s.group,y.dragged=p,_e={target:p,clientX:(t||e).clientX,clientY:(t||e).clientY},Zt=_e.clientX-d.left,Qt=_e.clientY-d.top,this._lastX=(t||e).clientX,this._lastY=(t||e).clientY,p.style["will-change"]="all",f=function(){if(H("delayEnded",a,{evt:e}),y.eventCanceled){a._onDrop();return}a._disableDelayedDragEvents(),!$t&&a.nativeDraggable&&(p.draggable=!0),a._triggerDragStart(e,t),Y({sortable:a,name:"choose",originalEvent:e}),j(p,s.chosenClass,!0)},s.ignore.split(",").forEach(function(m){on(p,m.trim(),Tt)}),C(u,"dragover",De),C(u,"mousemove",De),C(u,"touchmove",De),s.supportPointer?(C(u,"pointerup",a._onDrop),!this.nativeDraggable&&C(u,"pointercancel",a._onDrop)):(C(u,"mouseup",a._onDrop),C(u,"touchend",a._onDrop),C(u,"touchcancel",a._onDrop)),$t&&this.nativeDraggable&&(this.options.touchStartThreshold=4,p.draggable=!0),H("delayStart",this,{evt:e}),s.delay&&(!s.delayOnTouchOnly||t)&&(!this.nativeDraggable||!(We||de))){if(y.eventCanceled){this._onDrop();return}s.supportPointer?(C(u,"pointerup",a._disableDelayedDrag),C(u,"pointercancel",a._disableDelayedDrag)):(C(u,"mouseup",a._disableDelayedDrag),C(u,"touchend",a._disableDelayedDrag),C(u,"touchcancel",a._disableDelayedDrag)),C(u,"mousemove",a._delayedDragTouchMoveHandler),C(u,"touchmove",a._delayedDragTouchMoveHandler),s.supportPointer&&C(u,"pointermove",a._delayedDragTouchMoveHandler),a._dragStartTimer=setTimeout(f,s.delay)}else f()}},_delayedDragTouchMoveHandler:function(e){var t=e.touches?e.touches[0]:e;Math.max(Math.abs(t.clientX-this._lastX),Math.abs(t.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){p&&Tt(p),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var e=this.el.ownerDocument;T(e,"mouseup",this._disableDelayedDrag),T(e,"touchend",this._disableDelayedDrag),T(e,"touchcancel",this._disableDelayedDrag),T(e,"pointerup",this._disableDelayedDrag),T(e,"pointercancel",this._disableDelayedDrag),T(e,"mousemove",this._delayedDragTouchMoveHandler),T(e,"touchmove",this._delayedDragTouchMoveHandler),T(e,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(e,t){t=t||e.pointerType=="touch"&&e,!this.nativeDraggable||t?this.options.supportPointer?C(document,"pointermove",this._onTouchMove):t?C(document,"touchmove",this._onTouchMove):C(document,"mousemove",this._onTouchMove):(C(p,"dragend",this),C(V,"dragstart",this._onDragStart));try{document.selection?it(function(){document.selection.empty()}):window.getSelection().removeAllRanges()}catch{}},_dragStarted:function(e,t){if(Oe=!1,V&&p){H("dragStarted",this,{evt:t}),this.nativeDraggable&&C(document,"dragover",Qn);var n=this.options;!e&&j(p,n.dragClass,!1),j(p,n.ghostClass,!0),y.active=this,e&&this._appendGhost(),Y({sortable:this,name:"start",originalEvent:t})}else this._nulling()},_emulateDragOver:function(){if(Z){this._lastX=Z.clientX,this._lastY=Z.clientY,pn();for(var e=document.elementFromPoint(Z.clientX,Z.clientY),t=e;e&&e.shadowRoot&&(e=e.shadowRoot.elementFromPoint(Z.clientX,Z.clientY),e!==t);)t=e;if(p.parentNode[W]._isOutsideThisEl(e),t)do{if(t[W]){var n=void 0;if(n=t[W]._onDragOver({clientX:Z.clientX,clientY:Z.clientY,target:e,rootEl:t}),n&&!this.options.dragoverBubble)break}e=t}while(t=rn(t));gn()}},_onTouchMove:function(e){if(_e){var t=this.options,n=t.fallbackTolerance,a=t.fallbackOffset,r=e.touches?e.touches[0]:e,s=S&&Ve(S,!0),u=S&&s&&s.a,f=S&&s&&s.d,d=Qe&&B&&Kt(B),m=(r.clientX-_e.clientX+a.x)/(u||1)+(d?d[0]-St[0]:0)/(u||1),g=(r.clientY-_e.clientY+a.y)/(f||1)+(d?d[1]-St[1]:0)/(f||1);if(!y.active&&!Oe){if(n&&Math.max(Math.abs(r.clientX-this._lastX),Math.abs(r.clientY-this._lastY))<n)return;this._onDragStart(e,!0)}if(S){s?(s.e+=m-(_t||0),s.f+=g-(Dt||0)):s={a:1,b:0,c:0,d:1,e:m,f:g};var E="matrix(".concat(s.a,",").concat(s.b,",").concat(s.c,",").concat(s.d,",").concat(s.e,",").concat(s.f,")");b(S,"webkitTransform",E),b(S,"mozTransform",E),b(S,"msTransform",E),b(S,"transform",E),_t=m,Dt=g,Z=r}e.cancelable&&e.preventDefault()}},_appendGhost:function(){if(!S){var e=this.options.fallbackOnBody?document.body:V,t=M(p,!0,Qe,!0,e),n=this.options;if(Qe){for(B=e;b(B,"position")==="static"&&b(B,"transform")==="none"&&B!==document;)B=B.parentNode;B!==document.body&&B!==document.documentElement?(B===document&&(B=ae()),t.top+=B.scrollTop,t.left+=B.scrollLeft):B=ae(),St=Kt(B)}S=p.cloneNode(!0),j(S,n.ghostClass,!1),j(S,n.fallbackClass,!0),j(S,n.dragClass,!0),b(S,"transition",""),b(S,"transform",""),b(S,"box-sizing","border-box"),b(S,"margin",0),b(S,"top",t.top),b(S,"left",t.left),b(S,"width",t.width),b(S,"height",t.height),b(S,"opacity","0.8"),b(S,"position",Qe?"absolute":"fixed"),b(S,"zIndex","100000"),b(S,"pointerEvents","none"),y.ghost=S,e.appendChild(S),b(S,"transform-origin",Zt/parseInt(S.style.width)*100+"% "+Qt/parseInt(S.style.height)*100+"%")}},_onDragStart:function(e,t){var n=this,a=e.dataTransfer,r=n.options;if(H("dragStart",this,{evt:e}),y.eventCanceled){this._onDrop();return}H("setupClone",this),y.eventCanceled||(x=un(p),x.removeAttribute("id"),x.draggable=!1,x.style["will-change"]="",this._hideClone(),j(x,this.options.chosenClass,!1),y.clone=x),n.cloneId=it(function(){H("clone",n),!y.eventCanceled&&(n.options.removeCloneOnHide||V.insertBefore(x,p),n._hideClone(),Y({sortable:n,name:"clone"}))}),!t&&j(p,r.dragClass,!0),t?(ot=!0,n._loopId=setInterval(n._emulateDragOver,50)):(T(document,"mouseup",n._onDrop),T(document,"touchend",n._onDrop),T(document,"touchcancel",n._onDrop),a&&(a.effectAllowed="move",r.setData&&r.setData.call(n,a,p)),C(document,"drop",n),b(p,"transform","translateZ(0)")),Oe=!0,n._dragStartId=it(n._dragStarted.bind(n,t,e)),C(document,"selectstart",n),Me=!0,window.getSelection().removeAllRanges(),qe&&b(document.body,"user-select","none")},_onDragOver:function(e){var t=this.el,n=e.target,a,r,s,u=this.options,f=u.group,d=y.active,m=Ke===f,g=u.sort,E=F||d,D,c=this,I=!1;if(At)return;function O(we,be){H(we,c,re({evt:e,isOwner:m,axis:D?"vertical":"horizontal",revert:s,dragRect:a,targetRect:r,canSort:g,fromSortable:E,target:n,completed:R,onMove:function(je,ft){return Je(V,t,p,a,je,M(je),e,ft)},changed:L},be))}function G(){O("dragOverAnimationCapture"),c.captureAnimationState(),c!==E&&E.captureAnimationState()}function R(we){return O("dragOverCompleted",{insertion:we}),we&&(m?d._hideClone():d._showClone(c),c!==E&&(j(p,F?F.options.ghostClass:d.options.ghostClass,!1),j(p,u.ghostClass,!0)),F!==c&&c!==y.active?F=c:c===y.active&&F&&(F=null),E===c&&(c._ignoreWhileAnimating=n),c.animateAll(function(){O("dragOverAnimationComplete"),c._ignoreWhileAnimating=null}),c!==E&&(E.animateAll(),E._ignoreWhileAnimating=null)),(n===p&&!p.animated||n===t&&!n.animated)&&(Ie=null),!u.dragoverBubble&&!e.rootEl&&n!==document&&(p.parentNode[W]._isOutsideThisEl(e.target),!we&&De(e)),!u.dragoverBubble&&e.stopPropagation&&e.stopPropagation(),I=!0}function L(){$=z(p),pe=z(p,u.draggable),Y({sortable:c,name:"change",toEl:t,newIndex:$,newDraggableIndex:pe,originalEvent:e})}if(e.preventDefault!==void 0&&e.cancelable&&e.preventDefault(),n=Q(n,u.draggable,t,!0),O("dragOver"),y.eventCanceled)return I;if(p.contains(e.target)||n.animated&&n.animatingX&&n.animatingY||c._ignoreWhileAnimating===n)return R(!1);if(ot=!1,d&&!u.disabled&&(m?g||(s=P!==V):F===this||(this.lastPutMode=Ke.checkPull(this,d,p,e))&&f.checkPut(this,d,p,e))){if(D=this._getDirection(e,n)==="vertical",a=M(p),O("dragOverValid"),y.eventCanceled)return I;if(s)return P=V,G(),this._hideClone(),O("revert"),y.eventCanceled||(Se?V.insertBefore(p,Se):V.appendChild(p)),R(!0);var q=Rt(t,u.draggable);if(!q||ni(e,D,this)&&!q.animated){if(q===p)return R(!1);if(q&&t===e.target&&(n=q),n&&(r=M(n)),Je(V,t,p,a,n,r,e,!!n)!==!1)return G(),q&&q.nextSibling?t.insertBefore(p,q.nextSibling):t.appendChild(p),P=t,L(),R(!0)}else if(q&&ti(e,D,this)){var oe=xe(t,0,u,!0);if(oe===p)return R(!1);if(n=oe,r=M(n),Je(V,t,p,a,n,r,e,!1)!==!1)return G(),t.insertBefore(p,oe),P=t,L(),R(!0)}else if(n.parentNode===t){r=M(n);var U=0,J,se=p.parentNode!==t,X=!Kn(p.animated&&p.toRect||a,n.animated&&n.toRect||r,D),le=D?"top":"left",ee=Gt(n,"top","top")||Gt(p,"top","top"),ve=ee?ee.scrollTop:void 0;Ie!==n&&(J=r[le],He=!1,Ze=!X&&u.invertSwap||se),U=ii(e,n,r,D,X?1:u.swapThreshold,u.invertedSwapThreshold==null?u.swapThreshold:u.invertedSwapThreshold,Ze,Ie===n);var K;if(U!==0){var te=z(p);do te-=U,K=P.children[te];while(K&&(b(K,"display")==="none"||K===S))}if(U===0||K===n)return R(!1);Ie=n,Ye=U;var ue=n.nextElementSibling,ne=!1;ne=U===1;var Te=Je(V,t,p,a,n,r,e,ne);if(Te!==!1)return(Te===1||Te===-1)&&(ne=Te===1),At=!0,setTimeout(ei,30),G(),ne&&!ue?t.appendChild(p):n.parentNode.insertBefore(p,ne?ue:n),ee&&ln(ee,0,ve-ee.scrollTop),P=p.parentNode,J!==void 0&&!Ze&&(nt=Math.abs(J-M(n)[le])),L(),R(!0)}if(t.contains(p))return R(!1)}return!1},_ignoreWhileAnimating:null,_offMoveEvents:function(){T(document,"mousemove",this._onTouchMove),T(document,"touchmove",this._onTouchMove),T(document,"pointermove",this._onTouchMove),T(document,"dragover",De),T(document,"mousemove",De),T(document,"touchmove",De)},_offUpEvents:function(){var e=this.el.ownerDocument;T(e,"mouseup",this._onDrop),T(e,"touchend",this._onDrop),T(e,"pointerup",this._onDrop),T(e,"pointercancel",this._onDrop),T(e,"touchcancel",this._onDrop),T(document,"selectstart",this)},_onDrop:function(e){var t=this.el,n=this.options;if($=z(p),pe=z(p,n.draggable),H("drop",this,{evt:e}),P=p&&p.parentNode,$=z(p),pe=z(p,n.draggable),y.eventCanceled){this._nulling();return}Oe=!1,Ze=!1,He=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),Vt(this.cloneId),Vt(this._dragStartId),this.nativeDraggable&&(T(document,"drop",this),T(t,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),qe&&b(document.body,"user-select",""),b(p,"transform",""),e&&(Me&&(e.cancelable&&e.preventDefault(),!n.dropBubble&&e.stopPropagation()),S&&S.parentNode&&S.parentNode.removeChild(S),(V===P||F&&F.lastPutMode!=="clone")&&x&&x.parentNode&&x.parentNode.removeChild(x),p&&(this.nativeDraggable&&T(p,"dragend",this),Tt(p),p.style["will-change"]="",Me&&!Oe&&j(p,F?F.options.ghostClass:this.options.ghostClass,!1),j(p,this.options.chosenClass,!1),Y({sortable:this,name:"unchoose",toEl:P,newIndex:null,newDraggableIndex:null,originalEvent:e}),V!==P?($>=0&&(Y({rootEl:P,name:"add",toEl:P,fromEl:V,originalEvent:e}),Y({sortable:this,name:"remove",toEl:P,originalEvent:e}),Y({rootEl:P,name:"sort",toEl:P,fromEl:V,originalEvent:e}),Y({sortable:this,name:"sort",toEl:P,originalEvent:e})),F&&F.save()):$!==Ae&&$>=0&&(Y({sortable:this,name:"update",toEl:P,originalEvent:e}),Y({sortable:this,name:"sort",toEl:P,originalEvent:e})),y.active&&(($==null||$===-1)&&($=Ae,pe=Be),Y({sortable:this,name:"end",toEl:P,originalEvent:e}),this.save()))),this._nulling()},_nulling:function(){H("nulling",this),V=p=P=S=Se=x=tt=ge=_e=Z=Me=$=pe=Ae=Be=Ie=Ye=F=Ke=y.dragged=y.ghost=y.clone=y.active=null,lt.forEach(function(e){e.checked=!0}),lt.length=_t=Dt=0},handleEvent:function(e){switch(e.type){case"drop":case"dragend":this._onDrop(e);break;case"dragenter":case"dragover":p&&(this._onDragOver(e),Jn(e));break;case"selectstart":e.preventDefault();break}},toArray:function(){for(var e=[],t,n=this.el.children,a=0,r=n.length,s=this.options;a<r;a++)t=n[a],Q(t,s.draggable,this.el,!1)&&e.push(t.getAttribute(s.dataIdAttr)||ri(t));return e},sort:function(e,t){var n={},a=this.el;this.toArray().forEach(function(r,s){var u=a.children[s];Q(u,this.options.draggable,a,!1)&&(n[r]=u)},this),t&&this.captureAnimationState(),e.forEach(function(r){n[r]&&(a.removeChild(n[r]),a.appendChild(n[r]))}),t&&this.animateAll()},save:function(){var e=this.options.store;e&&e.set&&e.set(this)},closest:function(e,t){return Q(e,t||this.options.draggable,this.el,!1)},option:function(e,t){var n=this.options;if(t===void 0)return n[e];var a=Ue.modifyOption(this,e,t);typeof a<"u"?n[e]=a:n[e]=t,e==="group"&&hn(n)},destroy:function(){H("destroy",this);var e=this.el;e[W]=null,T(e,"mousedown",this._onTapStart),T(e,"touchstart",this._onTapStart),T(e,"pointerdown",this._onTapStart),this.nativeDraggable&&(T(e,"dragover",this),T(e,"dragenter",this)),Array.prototype.forEach.call(e.querySelectorAll("[draggable]"),function(t){t.removeAttribute("draggable")}),this._onDrop(),this._disableDelayedDragEvents(),st.splice(st.indexOf(this.el),1),this.el=e=null},_hideClone:function(){if(!ge){if(H("hideClone",this),y.eventCanceled)return;b(x,"display","none"),this.options.removeCloneOnHide&&x.parentNode&&x.parentNode.removeChild(x),ge=!0}},_showClone:function(e){if(e.lastPutMode!=="clone"){this._hideClone();return}if(ge){if(H("showClone",this),y.eventCanceled)return;p.parentNode==V&&!this.options.group.revertClone?V.insertBefore(x,p):Se?V.insertBefore(x,Se):V.appendChild(x),this.options.group.revertClone&&this.animate(p,x),b(x,"display",""),ge=!1}}};function Jn(i){i.dataTransfer&&(i.dataTransfer.dropEffect="move"),i.cancelable&&i.preventDefault()}function Je(i,e,t,n,a,r,s,u){var f,d=i[W],m=d.options.onMove,g;return window.CustomEvent&&!de&&!We?f=new CustomEvent("move",{bubbles:!0,cancelable:!0}):(f=document.createEvent("Event"),f.initEvent("move",!0,!0)),f.to=e,f.from=i,f.dragged=t,f.draggedRect=n,f.related=a||e,f.relatedRect=r||M(e),f.willInsertAfter=u,f.originalEvent=s,i.dispatchEvent(f),m&&(g=m.call(d,f,s)),g}function Tt(i){i.draggable=!1}function ei(){At=!1}function ti(i,e,t){var n=M(xe(t.el,0,t.options,!0)),a=cn(t.el,t.options,S),r=10;return e?i.clientX<a.left-r||i.clientY<n.top&&i.clientX<n.right:i.clientY<a.top-r||i.clientY<n.bottom&&i.clientX<n.left}function ni(i,e,t){var n=M(Rt(t.el,t.options.draggable)),a=cn(t.el,t.options,S),r=10;return e?i.clientX>a.right+r||i.clientY>n.bottom&&i.clientX>n.left:i.clientY>a.bottom+r||i.clientX>n.right&&i.clientY>n.top}function ii(i,e,t,n,a,r,s,u){var f=n?i.clientY:i.clientX,d=n?t.height:t.width,m=n?t.top:t.left,g=n?t.bottom:t.right,E=!1;if(!s){if(u&&nt<d*a){if(!He&&(Ye===1?f>m+d*r/2:f<g-d*r/2)&&(He=!0),He)E=!0;else if(Ye===1?f<m+nt:f>g-nt)return-Ye}else if(f>m+d*(1-a)/2&&f<g-d*(1-a)/2)return ai(e)}return E=E||s,E&&(f<m+d*r/2||f>g-d*r/2)?f>m+d/2?1:-1:0}function ai(i){return z(p)<z(i)?1:-1}function ri(i){for(var e=i.tagName+i.className+i.src+i.href+i.textContent,t=e.length,n=0;t--;)n+=e.charCodeAt(t);return n.toString(36)}function oi(i){lt.length=0;for(var e=i.getElementsByTagName("input"),t=e.length;t--;){var n=e[t];n.checked&&lt.push(n)}}function it(i){return setTimeout(i,0)}function Vt(i){return clearTimeout(i)}ct&&C(document,"touchmove",function(i){(y.active||Oe)&&i.cancelable&&i.preventDefault()});y.utils={on:C,off:T,css:b,find:on,is:function(e,t){return!!Q(e,t,e,!1)},extend:Yn,throttle:sn,closest:Q,toggleClass:j,clone:un,index:z,nextTick:it,cancelNextTick:Vt,detectDirection:dn,getChild:xe,expando:W};y.get=function(i){return i[W]};y.mount=function(){for(var i=arguments.length,e=new Array(i),t=0;t<i;t++)e[t]=arguments[t];e[0].constructor===Array&&(e=e[0]),e.forEach(function(n){if(!n.prototype||!n.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(n));n.utils&&(y.utils=re(re({},y.utils),n.utils)),Ue.mount(n)})};y.create=function(i,e){return new y(i,e)};y.version=Xn;var N=[],ke,xt,Pt=!1,Ct,It,ut,Fe;function si(){function i(){this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0};for(var e in this)e.charAt(0)==="_"&&typeof this[e]=="function"&&(this[e]=this[e].bind(this))}return i.prototype={dragStarted:function(t){var n=t.originalEvent;this.sortable.nativeDraggable?C(document,"dragover",this._handleAutoScroll):this.options.supportPointer?C(document,"pointermove",this._handleFallbackAutoScroll):n.touches?C(document,"touchmove",this._handleFallbackAutoScroll):C(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(t){var n=t.originalEvent;!this.options.dragOverBubble&&!n.rootEl&&this._handleAutoScroll(n)},drop:function(){this.sortable.nativeDraggable?T(document,"dragover",this._handleAutoScroll):(T(document,"pointermove",this._handleFallbackAutoScroll),T(document,"touchmove",this._handleFallbackAutoScroll),T(document,"mousemove",this._handleFallbackAutoScroll)),en(),at(),Hn()},nulling:function(){ut=xt=ke=Pt=Fe=Ct=It=null,N.length=0},_handleFallbackAutoScroll:function(t){this._handleAutoScroll(t,!0)},_handleAutoScroll:function(t,n){var a=this,r=(t.touches?t.touches[0]:t).clientX,s=(t.touches?t.touches[0]:t).clientY,u=document.elementFromPoint(r,s);if(ut=t,n||this.options.forceAutoScrollFallback||We||de||qe){Ot(t,this.options,u,n);var f=me(u,!0);Pt&&(!Fe||r!==Ct||s!==It)&&(Fe&&en(),Fe=setInterval(function(){var d=me(document.elementFromPoint(r,s),!0);d!==f&&(f=d,at()),Ot(t,a.options,d,n)},10),Ct=r,It=s)}else{if(!this.options.bubbleScroll||me(u,!0)===ae()){at();return}Ot(t,this.options,me(u,!1),!1)}}},fe(i,{pluginName:"scroll",initializeByDefault:!0})}function at(){N.forEach(function(i){clearInterval(i.pid)}),N=[]}function en(){clearInterval(Fe)}var Ot=sn(function(i,e,t,n){if(e.scroll){var a=(i.touches?i.touches[0]:i).clientX,r=(i.touches?i.touches[0]:i).clientY,s=e.scrollSensitivity,u=e.scrollSpeed,f=ae(),d=!1,m;xt!==t&&(xt=t,at(),ke=e.scroll,m=e.scrollFn,ke===!0&&(ke=me(t,!0)));var g=0,E=ke;do{var D=E,c=M(D),I=c.top,O=c.bottom,G=c.left,R=c.right,L=c.width,q=c.height,oe=void 0,U=void 0,J=D.scrollWidth,se=D.scrollHeight,X=b(D),le=D.scrollLeft,ee=D.scrollTop;D===f?(oe=L<J&&(X.overflowX==="auto"||X.overflowX==="scroll"||X.overflowX==="visible"),U=q<se&&(X.overflowY==="auto"||X.overflowY==="scroll"||X.overflowY==="visible")):(oe=L<J&&(X.overflowX==="auto"||X.overflowX==="scroll"),U=q<se&&(X.overflowY==="auto"||X.overflowY==="scroll"));var ve=oe&&(Math.abs(R-a)<=s&&le+L<J)-(Math.abs(G-a)<=s&&!!le),K=U&&(Math.abs(O-r)<=s&&ee+q<se)-(Math.abs(I-r)<=s&&!!ee);if(!N[g])for(var te=0;te<=g;te++)N[te]||(N[te]={});(N[g].vx!=ve||N[g].vy!=K||N[g].el!==D)&&(N[g].el=D,N[g].vx=ve,N[g].vy=K,clearInterval(N[g].pid),(ve!=0||K!=0)&&(d=!0,N[g].pid=setInterval((function(){n&&this.layer===0&&y.active._onTouchMove(ut);var ue=N[this.layer].vy?N[this.layer].vy*u:0,ne=N[this.layer].vx?N[this.layer].vx*u:0;typeof m=="function"&&m.call(y.dragged.parentNode[W],ne,ue,i,ut,N[this.layer].el)!=="continue"||ln(N[this.layer].el,ne,ue)}).bind({layer:g}),24))),g++}while(e.bubbleScroll&&E!==f&&(E=me(E,!1)));Pt=d}},30),mn=function(e){var t=e.originalEvent,n=e.putSortable,a=e.dragEl,r=e.activeSortable,s=e.dispatchSortableEvent,u=e.hideGhostForTarget,f=e.unhideGhostForTarget;if(t){var d=n||r;u();var m=t.changedTouches&&t.changedTouches.length?t.changedTouches[0]:t,g=document.elementFromPoint(m.clientX,m.clientY);f(),d&&!d.el.contains(g)&&(s("spill"),this.onSpill({dragEl:a,putSortable:n}))}};function Nt(){}Nt.prototype={startIndex:null,dragStart:function(e){var t=e.oldDraggableIndex;this.startIndex=t},onSpill:function(e){var t=e.dragEl,n=e.putSortable;this.sortable.captureAnimationState(),n&&n.captureAnimationState();var a=xe(this.sortable.el,this.startIndex,this.options);a?this.sortable.el.insertBefore(t,a):this.sortable.el.appendChild(t),this.sortable.animateAll(),n&&n.animateAll()},drop:mn};fe(Nt,{pluginName:"revertOnSpill"});function Mt(){}Mt.prototype={onSpill:function(e){var t=e.dragEl,n=e.putSortable,a=n||this.sortable;a.captureAnimationState(),t.parentNode&&t.parentNode.removeChild(t),a.animateAll()},drop:mn};fe(Mt,{pluginName:"removeOnSpill"});y.mount(new si);y.mount(Mt,Nt);const li=["data-url","onContextmenu","onMousedown","onClick"],ui={key:0,class:"iconfont icon-webicon318 layout-navbars-tagsview-ul-li-iconfont"},ci=tn({name:"layoutTagsView"}),fi=tn({...ci,setup(i){const e=Nn(()=>yn(()=>import("./contextmenu.DlveCinl.js"),__vite__mapDeps([0,1,2,3]))),t=Ge([]),n=Ge(),a=Ge(),r=Ge(),s=qt(),u=vn(),f=qt(),{themeConfig:d}=Yt(u),{tagsViewRoutes:m}=Yt(f),g=wn(),E=_n(),D=Dn(),c=Sn({routeActive:"",routePath:E.path,dropdown:{x:"",y:""},sortable:"",tagsRefsIndex:0,tagsViewList:[],tagsViewRoutesList:[]}),I=gt(()=>d.value.tagsStyle),O=gt(()=>d.value),G=gt(()=>l=>Xt.setTagsViewNameI18n(l)),R=l=>O.value.isShareTagsView?l.path===c.routePath:l.query&&Object.keys(l.query).length||l.params&&Object.keys(l.params).length?l.url?l.url===c.routeActive:l.path===c.routeActive:l.path===c.routePath,L=l=>{Ee.set("tagsViewList",l)},q=async()=>{c.routeActive=await ie(E),c.routePath=await E.meta.isDynamic?E.meta.isDynamicPath:E.path,c.tagsViewList=[],c.tagsViewRoutesList=m.value,oe()},oe=async()=>{Ee.get("tagsViewList")&&O.value.isCacheTagsView?c.tagsViewList=await Ee.get("tagsViewList"):(await c.tagsViewRoutesList.map(l=>{var o;(o=l.meta)!=null&&o.isAffix&&!l.meta.isHide&&(l.url=ie(l),c.tagsViewList.push({...l}),g.addCachedView(l))}),await se(E.path,E)),kt(O.value.isShareTagsView?c.routePath:c.routeActive)},U=async(l,o)=>{var v,A;let h=(v=o==null?void 0:o.meta)!=null&&v.isDynamic?o.meta.isDynamicPath:l;if(c.tagsViewList.filter(_=>{var k,Pe;return _.path===h&&Bt((k=o==null?void 0:o.meta)!=null&&k.isDynamic?_.params?_.params:null:_.query?_.query:null,(Pe=o==null?void 0:o.meta)!=null&&Pe.isDynamic?o!=null&&o.params?o==null?void 0:o.params:null:o!=null&&o.query?o==null?void 0:o.query:null)}).length<=0){let _=c.tagsViewRoutesList.find(k=>k.path===h);if(!_||_.meta.isAffix||_.meta.isLink&&!_.meta.isIframe)return!1;(A=o==null?void 0:o.meta)!=null&&A.isDynamic?_.params=o.params:_.query=o==null?void 0:o.query,_.url=ie(_),c.tagsViewList.push({..._}),await g.addCachedView(_),L(c.tagsViewList)}},J=(l,o)=>{var w;let h=(w=o==null?void 0:o.meta)!=null&&w.isDynamic?o.meta.isDynamicPath:l;c.tagsViewList.forEach(v=>{var A,_,k;v.path===h&&!Bt((A=o==null?void 0:o.meta)!=null&&A.isDynamic?v.params?v.params:null:v.query?v.query:null,(_=o==null?void 0:o.meta)!=null&&_.isDynamic?o!=null&&o.params?o==null?void 0:o.params:null:o!=null&&o.query?o==null?void 0:o.query:null)&&((k=o==null?void 0:o.meta)!=null&&k.isDynamic?v.params=o.params:v.query=o==null?void 0:o.query,v.url=ie(v),L(c.tagsViewList))})},se=(l,o)=>{bt(async()=>{var w,v,A;let h;if((w=o==null?void 0:o.meta)!=null&&w.isDynamic){if(O.value.isShareTagsView?await J(l,o):await U(l,o),c.tagsViewList.some(_=>{var k;return _.path===((k=o==null?void 0:o.meta)==null?void 0:k.isDynamicPath)}))return L(c.tagsViewList),!1;h=c.tagsViewRoutesList.find(_=>{var k;return _.path===((k=o==null?void 0:o.meta)==null?void 0:k.isDynamicPath)})}else{if(O.value.isShareTagsView?await J(l,o):await U(l,o),c.tagsViewList.some(_=>_.path===l))return L(c.tagsViewList),!1;h=c.tagsViewRoutesList.find(_=>_.path===l)}if(!h||(v=h==null?void 0:h.meta)!=null&&v.isLink&&!h.meta.isIframe)return!1;(A=o==null?void 0:o.meta)!=null&&A.isDynamic?h.params=o!=null&&o.params?o==null?void 0:o.params:E.params:h.query=o!=null&&o.query?o==null?void 0:o.query:E.query,h.url=ie(h),await g.addCachedView(h),await c.tagsViewList.push({...h}),await L(c.tagsViewList)})},X=async l=>{var w;const o=decodeURI(l);let h={};if(c.tagsViewList.forEach(v=>{v.transUrl=be(v),v.transUrl?v.transUrl===be(v)&&(h=v):v.path===o&&(h=v)}),!h)return!1;await g.delCachedView(h),ye.emit("onTagsViewRefreshRouterView",l),(w=h.meta)!=null&&w.isKeepAlive&&g.addCachedView(h)},le=l=>{c.tagsViewList.map((o,h,w)=>{var v;(v=o.meta)!=null&&v.isAffix||(O.value.isShareTagsView?o.path===l:o.url===l)&&(g.delCachedView(o),c.tagsViewList.splice(h,1),setTimeout(()=>{(c.tagsViewList.length===h&&O.value.isShareTagsView?c.routePath===l:c.routeActive===l)?w[w.length-1].meta.isDynamic?h!==w.length?D.push({name:w[h].name,params:w[h].params}):D.push({name:w[w.length-1].name,params:w[w.length-1].params}):h!==w.length?D.push({path:w[h].path,query:w[h].query}):D.push({path:w[w.length-1].path,query:w[w.length-1].query}):(c.tagsViewList.length!==h&&O.value.isShareTagsView?c.routePath===l:c.routeActive===l)&&(w[h].meta.isDynamic?D.push({name:w[h].name,params:w[h].params}):D.push({path:w[h].path,query:w[h].query}))},0))}),L(c.tagsViewList)},ee=l=>{Ee.get("tagsViewList")&&(c.tagsViewList=[],Ee.get("tagsViewList").map(o=>{var h;(h=o.meta)!=null&&h.isAffix&&!o.meta.isHide&&(o.url=ie(o),g.delOthersCachedViews(o),c.tagsViewList.push({...o}))}),se(l,E),L(c.tagsViewList))},ve=()=>{Ee.get("tagsViewList")&&(g.delAllCachedViews(),c.tagsViewList=[],Ee.get("tagsViewList").map(l=>{var o;(o=l.meta)!=null&&o.isAffix&&!l.meta.isHide&&(l.url=ie(l),c.tagsViewList.push({...l}),D.push({path:c.tagsViewList[c.tagsViewList.length-1].path}))}),L(c.tagsViewList))},K=async l=>{const o=c.tagsViewList.find(h=>O.value.isShareTagsView?h.path===l:h.url===l);o.meta.isDynamic?await D.push({name:o.name,params:o.params}):await D.push({name:o.name,query:o.query}),s.setCurrenFullscreen(!0)},te=l=>{let o={};return c.tagsViewList.forEach(h=>{h.transUrl=be(h),h.transUrl?h.transUrl===be(h)&&h.transUrl===l.commonUrl&&(o=h):h.path===decodeURI(l.path)&&(o=h)}),o||null},ue=async l=>{if(l.commonUrl=be(l),!te(l))return En({type:"warning",message:"请正确输入路径及完整参数（query、params）"});const{path:o,name:h,params:w,query:v,meta:A,url:_}=te(l);switch(l.contextMenuClickId){case 0:A.isDynamic?await D.push({name:h,params:w}):await D.push({path:o,query:v}),X(E.fullPath);break;case 1:le(O.value.isShareTagsView?o:_);break;case 2:A.isDynamic?await D.push({name:h,params:w}):await D.push({path:o,query:v}),ee(o);break;case 3:ve();break;case 4:K(O.value.isShareTagsView?o:_);break}},ne=(l,o)=>{const{clientX:h,clientY:w}=o;c.dropdown.x=h,c.dropdown.y=w,a.value.openContextmenu(l)},Te=(l,o)=>{var h;if(!((h=l.meta)!=null&&h.isAffix)&&o.button===1){const w=Object.assign({},{contextMenuClickId:1,...l});ue(w)}},we=(l,o)=>{c.tagsRefsIndex=o,D.push(l)},be=l=>{var w,v;let o=l.query&&Object.keys(l.query).length>0?l.query:l.params;if(!o)return"";let h="";for(let[A,_]of Object.entries(o))(w=l.meta)!=null&&w.isDynamic?h+=`/${_}`:h+=`&${A}=${_}`;return(v=l.meta)!=null&&v.isDynamic?l.isFnClick?decodeURI(l.path):`${l.path.split(":")[0]}${h.replace(/^\//,"")}`:`${l.path}${h.replace(/^&/,"?")}`},ie=l=>{var w;let o=l.query&&Object.keys(l.query).length>0?l.query:l.params;if(!o||Object.keys(o).length<=0)return l.path;let h="";for(let v in o)h+=o[v];return`${(w=l.meta)!=null&&w.isDynamic?l.meta.isDynamicPath:l.path}-${h}`},je=l=>{n.value.$refs.wrapRef.scrollLeft+=l.wheelDelta/4},ft=()=>{bt(()=>{if(t.value.length<=0)return!1;let l=t.value[c.tagsRefsIndex],o=c.tagsRefsIndex,h=t.value.length,w=t.value[0],v=t.value[t.value.length-1],A=n.value.$refs.wrapRef,_=A.scrollWidth,k=A.offsetWidth,Pe=A.scrollLeft,pt=t.value[c.tagsRefsIndex-1],Ft=t.value[c.tagsRefsIndex+1],$e=0,ze=0;l===w?A.scrollLeft=0:l===v?A.scrollLeft=_-k:(o===0?$e=w.offsetLeft-5:$e=(pt==null?void 0:pt.offsetLeft)-5,o===h?ze=v.offsetLeft+v.offsetWidth+5:ze=Ft.offsetLeft+Ft.offsetWidth+5,ze>Pe+k?A.scrollLeft=ze-k:$e<Pe&&(A.scrollLeft=$e)),n.value.update()})},kt=l=>{bt(async()=>{let o=await c.tagsViewList;c.tagsRefsIndex=o.findIndex(h=>O.value.isShareTagsView?h.path===l:h.url===l),ft()})},dt=async()=>{const l=document.querySelector(".layout-navbars-tagsview-ul");if(!l)return!1;c.sortable.el&&c.sortable.destroy(),c.sortable=y.create(l,{animation:300,dataIdAttr:"data-url",disabled:!O.value.isSortableTagsView,onEnd:()=>{const o=[];c.sortable.toArray().map(h=>{c.tagsViewList.map(w=>{w.url===h&&o.push({...w})})}),L(o)}})},ht=async()=>{await dt(),Xt.isMobile()&&c.sortable.el&&c.sortable.destroy()};return Tn(()=>{ht(),window.addEventListener("resize",ht),ye.on("onCurrentContextmenuClick",l=>{l.isFnClick=!0,ue(l)}),ye.on("openOrCloseSortable",()=>{dt()}),ye.on("openShareTagsView",()=>{O.value.isShareTagsView&&(D.push("/home"),c.tagsViewList=[],c.tagsViewRoutesList.map(l=>{var o;(o=l.meta)!=null&&o.isAffix&&!l.meta.isHide&&(l.url=ie(l),c.tagsViewList.push({...l}))}))})}),Cn(()=>{ye.off("onCurrentContextmenuClick",()=>{}),ye.off("openOrCloseSortable",()=>{}),ye.off("openShareTagsView",()=>{}),window.removeEventListener("resize",ht)}),In(()=>{t.value=[]}),On(()=>{q(),dt()}),An(async l=>{c.routeActive=ie(l),c.routePath=l.meta.isDynamic?l.meta.isDynamicPath:l.path,await se(l.path,l),kt(O.value.isShareTagsView?c.routePath:c.routeActive)}),Vn(bn.state,l=>{if(l.tagsViewRoutes.tagsViewRoutes.length===c.tagsViewRoutesList.length)return!1;q()},{deep:!0}),(l,o)=>{const h=Ht("SvgIcon"),w=Ht("el-scrollbar");return he(),Le("div",{class:vt(["layout-navbars-tagsview",{"layout-navbars-tagsview-shadow":O.value.layout==="classic"}])},[mt(w,{ref_key:"scrollbarRef",ref:n,onWheel:Re(je,["prevent"])},{default:xn(()=>[Wt("ul",{class:vt(["layout-navbars-tagsview-ul",I.value]),ref_key:"tagsUlRef",ref:r},[(he(!0),Le(Ut,null,Pn(c.tagsViewList,(v,A)=>(he(),Le("li",{key:A,class:vt(["layout-navbars-tagsview-ul-li",{"is-active":R(v)}]),"data-url":v.url,onContextmenu:Re(_=>ne(v,_),["prevent"]),onMousedown:_=>Te(v,_),onClick:_=>we(v,A),ref_for:!0,ref:_=>{_&&(t.value[A]=_)}},[R(v)?(he(),Le("i",ui)):Ne("",!0),!R(v)&&O.value.isTagsviewIcon?(he(),wt(h,{key:1,name:v.meta.icon,class:"pr5"},null,8,["name"])):Ne("",!0),Wt("span",null,Ln(G.value(v)),1),R(v)?(he(),Le(Ut,{key:2},[mt(h,{name:"ele-RefreshRight",class:"ml5 layout-navbars-tagsview-ul-li-refresh",onClick:o[0]||(o[0]=Re(_=>X(l.$route.fullPath),["stop"]))}),v.meta.isAffix?Ne("",!0):(he(),wt(h,{key:0,name:"ele-Close",class:"layout-navbars-tagsview-ul-li-icon layout-icon-active",onClick:Re(_=>le(O.value.isShareTagsView?v.path:v.url),["stop"])},null,8,["onClick"]))],64)):Ne("",!0),v.meta.isAffix?Ne("",!0):(he(),wt(h,{key:3,name:"ele-Close",class:"layout-navbars-tagsview-ul-li-icon layout-icon-three",onClick:Re(_=>le(O.value.isShareTagsView?v.path:v.url),["stop"])},null,8,["onClick"]))],42,li))),128))],2)]),_:1},512),mt(Rn(e),{dropdown:c.dropdown,ref_key:"contextmenuRef",ref:a,onCurrentContextmenuClick:ue},null,8,["dropdown"])],2)}}}),gi=Mn(fi,[["__scopeId","data-v-4a6a63bb"]]);export{gi as default};
