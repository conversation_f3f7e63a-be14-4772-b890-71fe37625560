import {
  Ct,
  bt
} from "./chunk-KSP3GDI3.js";
import "./chunk-NXSV4UZF.js";
import "./chunk-NROEEZWH.js";
import "./chunk-TMUISJ4F.js";
import "./chunk-ISH6AKKV.js";
import "./chunk-WPKI25LT.js";
import "./chunk-UOYEQWWP.js";
import {
  cloneDeep_default,
  merge_default
} from "./chunk-YNRHTVZR.js";
import "./chunk-YLGFFQGZ.js";
import "./chunk-NHLVTWLD.js";
import "./chunk-LK32TJAX.js";

// node_modules/.pnpm/@fast-crud+fast-extends@1.2_48d83ac4990201ef9a8c3e5855a79123/node_modules/@fast-crud/fast-extends/dist/uploader-form-c58cffc5.mjs
function p(e, n, s) {
  let t;
  s.response ? t = `${s.response.error || s.response}` : s.responseText ? t = `${s.responseText}` : t = `fail to post ${e} ${s.status}`;
  const r = new Error(t);
  return r.status = s.status, r.method = "post", r.url = e, r;
}
function m(e) {
  const n = e.responseText || e.response;
  if (!n)
    return n;
  try {
    return JSON.parse(n);
  } catch {
    return n;
  }
}
function w(e, n, s) {
  if (typeof XMLHttpRequest > "u")
    return;
  const t = new XMLHttpRequest(), r = e.action;
  t.timeout = e.timeout, t.upload && (t.upload.onprogress = function(o) {
    o.total > 0 && (o.percent = o.loaded / o.total * 100), e.onProgress(o);
  });
  const i = new FormData();
  e.data && Object.keys(e.data).forEach((a) => {
    i.append(a, e.data[a]);
  }), i.append(e.name, e.file, e.file.name), t.onerror = function(o) {
    s(o);
  }, t.onload = function() {
    if (t.status < 200 || t.status >= 300)
      return e.onError(p(r, e, t));
    n(m(t));
  }, t.open("post", r, true), e.withCredentials && "withCredentials" in t && (t.withCredentials = true);
  const u = e.headers || {};
  for (const a in u)
    u.hasOwnProperty(a) && u[a] !== null && t.setRequestHeader(a, u[a]);
  return t.send(i), t;
}
function y(e) {
  return new Promise((n, s) => {
    w(e, async (t) => {
      n(t);
    }, (t) => {
      s(t);
    });
  });
}
async function g(e) {
  const { file: n, fileName: s, onProgress: t } = e, r = e.options, i = await bt(n, s, r);
  r.data == null && (r.data = {}), r.data.key = i;
  const u = {
    file: n,
    onProgress: t,
    timeout: 6e4,
    ...r
  };
  delete u.uploadRequest;
  let o = await (r.uploadRequest ?? y)(u);
  if (r.successHandle && (o = await r.successHandle(o, u)), !o)
    throw new Error("上传成功，successHandle处理后必须返回数据，格式为 url 或{url} 或 {key}等");
  return o && typeof o == "object" && o.key == null && (o.key = i), o;
}
async function E(e) {
  const { getConfig: n } = Ct(), s = n("form");
  return e.options = merge_default({}, cloneDeep_default(s), e.options), await g(e);
}
export {
  E as upload
};
//# sourceMappingURL=uploader-form-c58cffc5-FYWZJQWN.js.map
