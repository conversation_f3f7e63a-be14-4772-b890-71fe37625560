import{d as h,g as C,j as _,k as y,a as i,o as l,e as m,m as v,s as r,F as k,p as x,n as S,l as z,f as B}from"./vue.BNx9QYep.js";import{_ as D}from"./_plugin-vue_export-helper.DlAUqK2U.js";const P={class:"item-com"},b={class:"item-com-title"},w=["onClick"],N={key:0,class:"item-com-pagination"},V=h({__name:"index",props:{type:{type:String,default:"role"},title:{type:String,default:"标题"},label:{type:String,default:"name"},value:{type:String,default:"id"},showPagination:{type:Boolean,default:!1}},emits:["fetchData","itemClick"],setup(s,{emit:d}){const n=s,c=d,t=C({current:"",page:1,limit:20,data:[],total:10}),p=()=>{c("fetchData",{page:t.page,limit:t.limit},e=>{(e==null?void 0:e.code)===2e3&&(t.data=e.data,t.total=(e==null?void 0:e.total)||10)})},g=e=>{t.current=e[n.value],c("itemClick",n.type,e)},u=e=>{t.page=e,p()};return _(()=>{p()}),(e,o)=>{const f=y("el-pagination");return l(),i("div",P,[m("p",b,r(n.title),1),m("ul",{class:"item-com-list",style:S({height:s.showPagination?"calc(100% - 75px)":"calc(100% - 45px)"})},[(l(!0),i(k,null,x(t.data,a=>(l(),i("li",{key:a[n.value],onClick:F=>g(a),class:B(t.current===a[n.value]?"item-com-item active":"item-com-item")},r(a[n.label]),11,w))),128))],4),s.showPagination?(l(),i("div",N,[z(f,{background:"",small:"","hide-on-single-page":"","current-page":t.page,"onUpdate:currentPage":o[0]||(o[0]=a=>t.page=a),"page-size":t.limit,"onUpdate:pageSize":o[1]||(o[1]=a=>t.limit=a),layout:"prev, pager, next","pager-count":5,total:t.total,onCurrentChange:u},null,8,["current-page","page-size","total"])])):v("",!0)])}}}),j=D(V,[["__scopeId","data-v-98323c74"]]);export{j as default};
