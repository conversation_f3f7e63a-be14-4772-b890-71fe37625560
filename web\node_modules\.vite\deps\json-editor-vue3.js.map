{"version": 3, "sources": ["../../.pnpm/json-editor-vue3@1.1.1/node_modules/json-editor-vue3/lib/index.js"], "sourcesContent": ["import JsonEditorVue from \"./json-editor.vue\";\n\nexport default JsonEditorVue;\n\nJsonEditorVue.install = function install(Vue) {\n  Vue.component(JsonEditorVue.name, JsonEditorVue);\n};\n\n// if (typeof window !== \"undefined\" && window.Vue) {\n//   window.Vue.component(JsonEditorVue.name, JsonEditorVue);\n// }\n"], "mappings": ";;;AAAA,OAAO,mBAAmB;AAE1B,IAAO,cAAQ;AAEf,cAAc,UAAU,SAAS,QAAQ,KAAK;AAC5C,MAAI,UAAU,cAAc,MAAM,aAAa;AACjD;", "names": []}