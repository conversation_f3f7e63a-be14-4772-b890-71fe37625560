import{C as i,m as p,f as c,h as l}from"./index.BHZI5pdK.js";import"./vue.BNx9QYep.js";function d(t,s,r){let e;r.response?e=`${r.response.error||r.response}`:r.responseText?e=`${r.responseText}`:e=`fail to post ${t} ${r.status}`;const o=new Error(e);return o.status=r.status,o.method="post",o.url=t,o}function f(t){const s=t.responseText||t.response;if(!s)return s;try{return JSON.parse(s)}catch{return s}}function m(t,s,r){if(typeof XMLHttpRequest>"u")return;const e=new XMLHttpRequest,o=t.action;e.timeout=t.timeout,e.upload&&(e.upload.onprogress=function(n){n.total>0&&(n.percent=n.loaded/n.total*100),t.onProgress(n)});const u=new FormData;t.data&&Object.keys(t.data).forEach(n=>{u.append(n,t.data[n])}),u.append(t.name,t.file,t.file.name),e.onerror=function(n){r(n)},e.onload=function(){if(e.status<200||e.status>=300)return t.onError(d(o,t,e));s(f(e))},e.open("post",o,!0),t.withCredentials&&"withCredentials"in e&&(e.withCredentials=!0);const a=t.headers||{};for(const n in a)a.hasOwnProperty(n)&&a[n]!==null&&e.setRequestHeader(n,a[n]);return e.send(u),e}function w(t){return new Promise((s,r)=>{m(t,async e=>{s(e)},e=>{r(e)})})}async function y(t){const{file:s,fileName:r,onProgress:e}=t,o=t.options,u=await l(s,r,o);o.data==null&&(o.data={}),o.data.key=u;const a={file:s,onProgress:e,timeout:6e4,...o};delete a.uploadRequest;let n=await(o.uploadRequest??w)(a);if(o.successHandle&&(n=await o.successHandle(n,a)),!n)throw new Error("上传成功，successHandle处理后必须返回数据，格式为 url 或{url} 或 {key}等");return n&&typeof n=="object"&&n.key==null&&(n.key=u),n}async function C(t){const{getConfig:s}=i(),r=s("form");return t.options=p({},c(r),t.options),await y(t)}export{C as upload};
