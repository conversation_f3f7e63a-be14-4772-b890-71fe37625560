/* 第三方图标字体间距/大小设置
------------------------------- */
@mixin generalIcon {
	font-size: 14px !important;
	display: inline-block;
	vertical-align: middle;
	margin-right: 5px;
	width: 24px;
	text-align: center;
	justify-content: center;
}

/* 文本不换行
------------------------------- */
@mixin text-no-wrap() {
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
}

/* 多行文本溢出
  ------------------------------- */
@mixin text-ellipsis($line: 2) {
	overflow: hidden;
	word-break: break-all;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: $line;
	-webkit-box-orient: vertical;
}

/* 滚动条(页面未使用) div 中使用：
  ------------------------------- */
// .test {
//   @include scrollBar;
// }
@mixin scrollBar {
	// 滚动条凹槽的颜色，还可以设置边框属性
	&::-webkit-scrollbar-track-piece {
		background-color: #f8f8f8;
	}
	// 滚动条的宽度
	&::-webkit-scrollbar {
		width: 9px;
		height: 9px;
	}
	// 滚动条的设置
	&::-webkit-scrollbar-thumb {
		background-color: #dddddd;
		background-clip: padding-box;
		min-height: 28px;
	}
	&::-webkit-scrollbar-thumb:hover {
		background-color: #bbb;
	}
}
