import{r as o,d as w,v as s,A as g,s as y}from"./index.BHZI5pdK.js";import{d as i}from"./dictionary.DNsEqk19.js";import{a as l}from"./authFunction.D3Be3hRy.js";const n="/api/system/user/";function b(e){return o({url:n,method:"get",params:e})}function v(e){return o({url:n,method:"post",data:e})}function u(e){return o({url:n+e.id+"/",method:"put",data:e})}function _(e){return o({url:n+e+"/",method:"delete",data:{id:e}})}function q(e){return w({url:n+"export_data/",params:e,method:"get"})}function W(e,t){return o({url:`/api/system/dept/dept_info/?dept_id=${e}&show_all=${t}`,method:"get"})}function S(e,t){return o({url:`/api/system/user/${e}/reset_password/`,method:"put",data:t})}const O=function({crudExpose:e,context:t}){const d=async r=>{const a=t!=null&&t.isShowChildFlag.value?"1":"0";return await b({...r,show_all:a})},p=async({form:r,row:a})=>(r.id=a.id,await u(r)),c=async({row:r})=>{const a=await _(r.id);return t==null||t.getDeptInfo(),a},m=async({form:r})=>{const a=await v(r);return t==null||t.getDeptInfo(),a},h=async r=>await q(r);return{crudOptions:{table:{remove:{confirmMessage:"是否删除该用户？"}},request:{pageRequest:d,addRequest:m,editRequest:p,delRequest:c},actionbar:{buttons:{add:{show:l("user:Create")},export:{text:"导出",title:"导出",show:l("user:Export"),click(){return h(e.getSearchFormData())}}}},search:{container:{layout:"multi-line",action:{col:{span:10}}}},rowHandle:{fixed:"right",width:250,buttons:{view:{show:!1},edit:{show:l("user:Update")},remove:{show:l("user:Delete")},custom:{text:"重设密码",type:"primary",show:l("user:ResetPassword"),tooltip:{placement:"top",content:"重设密码"},click:r=>t==null?void 0:t.handleResetPwdOpen(r.row)}}},columns:{_index:{title:"序号",form:{show:!1},column:{type:"index",align:"center",width:"70px",columnSetDisabled:!0}},search:{title:"关键词",column:{show:!1},search:{show:!0,component:{props:{clearable:!0},placeholder:"请输入关键词"}},form:{show:!1,component:{props:{clearable:!0}}}},username:{title:"账号",type:"input",column:{minWidth:100},form:{rules:[{required:!0,message:"账号必填项"}],component:{placeholder:"请输入账号"}}},password:{title:"密码",type:"input",column:{show:!1},editForm:{show:!1},form:{rules:[{required:!0,message:"密码必填项"}],component:{span:12,showPassword:!0,placeholder:"请输入密码"}}},name:{title:"姓名",type:"input",column:{minWidth:100},form:{rules:[{required:!0,message:"姓名必填项"}],component:{span:12,placeholder:"请输入姓名"}}},dept:{title:"部门",type:"dict-tree",dict:s({isTree:!0,url:"/api/system/dept/all_dept/",value:"id",label:"name"}),column:{minWidth:200,formatter({value:r,row:a,index:f}){return a.dept_name_all}},form:{rules:[{required:!0,message:"必填项"}],component:{filterable:!0,placeholder:"请选择",props:{props:{value:"id",label:"name"}}}}},role:{title:"角色",search:{show:!0,component:{props:{clearable:!0}}},type:"dict-select",dict:s({url:"/api/system/role/",value:"id",label:"name"}),column:{minWidth:200},form:{rules:[{required:!0,message:"必填项"}],component:{multiple:!0,filterable:!0,placeholder:"请选择角色"}}},mobile:{title:"手机号码",type:"input",column:{minWidth:120},form:{rules:[{max:20,message:"请输入正确的手机号码",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码"}],component:{placeholder:"请输入手机号码"}}},email:{title:"邮箱",column:{width:260},form:{rules:[{type:"email",message:"请输入正确的邮箱地址",trigger:["blur","change"]}],component:{placeholder:"请输入邮箱"}}},gender:{title:"性别",type:"dict-select",dict:s({data:i("gender")}),form:{value:1,component:{span:12}},component:{props:{color:"auto"}}},user_type:{title:"用户类型",search:{show:!0},type:"dict-select",dict:s({data:i("user_type")}),column:{minWidth:100},form:{show:!1,value:0,component:{span:12}}},is_active:{title:"锁定",search:{show:!0},type:"dict-radio",column:{component:{name:"fs-dict-switch",activeText:"",inactiveText:"",style:"--el-switch-on-color: var(--el-color-primary); --el-switch-off-color: #dcdfe6",onChange:g(r=>()=>{u(r.row).then(a=>{y(a.msg)})})}},dict:s({data:i("button_status_bool")})},avatar:{title:"头像",type:"avatar-uploader",form:{show:!1},column:{width:100,showOverflowTooltip:!0}}}}}},T=Object.freeze(Object.defineProperty({__proto__:null,createCrudOptions:O},Symbol.toStringTag,{value:"Module"}));export{T as a,O as c,W as g,S as r};
