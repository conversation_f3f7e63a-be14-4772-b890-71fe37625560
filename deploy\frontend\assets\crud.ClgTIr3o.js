import{r as a,v as i,A as l,g as o}from"./index.BHZI5pdK.js";import"./vue.BNx9QYep.js";const s="/api/system/download_center/";function r(e){return a({url:s,method:"get",params:e})}function u(e){return a({url:s,method:"post",data:e})}function d(e){return a({url:s+e.id+"/",method:"put",data:e})}function c(e){return a({url:s+e+"/",method:"delete",data:{id:e}})}const x=function({crudExpose:e}){return{crudOptions:{request:{pageRequest:async t=>await r(t),addRequest:async({form:t})=>await u(t),editRequest:async({form:t,row:n})=>(t.id=n.id,await d(t)),delRequest:async({row:t})=>await c(t.id)},pagination:{show:!0},actionbar:{buttons:{add:{show:!1}}},toolbar:{buttons:{export:{show:!1}}},rowHandle:{fixed:"right",width:120,buttons:{view:{show:!1},edit:{show:!1},remove:{show:!1},download:{show:l(t=>t.row.task_status===2),text:"下载文件",type:"warning",click:t=>window.open(o(t.row.url),"_blank")}}},form:{col:{span:24},labelWidth:"100px",wrapper:{is:"el-dialog",width:"600px"}},columns:{_index:{title:"序号",form:{show:!1},column:{type:"index",align:"center",width:"70px",columnSetDisabled:!0}},task_name:{title:"任务名",type:"text",column:{minWidth:160,align:"left"},search:{show:!0}},file_name:{title:"文件名",type:"text",column:{minWidth:160,align:"left"},search:{show:!0}},size:{title:"文件大小(b)",type:"number",column:{width:100}},task_status:{title:"任务状态",type:"dict-select",dict:i({data:[{label:"任务已创建",value:0},{label:"任务进行中",value:1},{label:"任务完成",value:2},{label:"任务失败",value:3}]}),column:{width:120},search:{show:!0}},create_datetime:{title:"创建时间",column:{width:160}},update_datetime:{title:"创建时间",column:{width:160}}}}}};export{x as createCrudOptions};
