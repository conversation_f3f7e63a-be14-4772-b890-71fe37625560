/**
 * Excel导出工具
 * 支持标题、格式化、样式等功能
 */

/**
 * 格式化货币
 */
function formatCurrency(value) {
  if (!value && value !== 0) return '¥0.00';
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(value);
}

/**
 * 格式化数字
 */
function formatNumber(value) {
  if (value === null || value === undefined || value === '') return '0';
  return Number(value).toLocaleString('zh-CN', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  });
}

/**
 * 创建Excel工作表
 */
function createWorksheet(data, options = {}) {
  const {
    title = '',
    subtitle = '',
    headers = [],
    filename = 'export',
    showSummary = false,
    summaryData = null
  } = options;

  // 创建HTML表格
  let html = '<table border="1" cellspacing="0" cellpadding="5">';
  
  // 添加标题
  if (title) {
    html += `<tr><td colspan="${headers.length}" style="text-align: center; font-size: 18px; font-weight: bold; background-color: #4472C4; color: white; padding: 10px;">${title}</td></tr>`;
  }
  
  // 添加副标题
  if (subtitle) {
    html += `<tr><td colspan="${headers.length}" style="text-align: center; font-size: 14px; background-color: #D9E2F3; padding: 8px;">${subtitle}</td></tr>`;
  }
  
  // 添加空行
  if (title || subtitle) {
    html += `<tr><td colspan="${headers.length}" style="height: 10px;"></td></tr>`;
  }
  
  // 添加表头
  if (headers.length > 0) {
    html += '<tr>';
    headers.forEach(header => {
      html += `<td style="background-color: #4472C4; color: white; font-weight: bold; text-align: center; padding: 8px;">${header}</td>`;
    });
    html += '</tr>';
  }
  
  // 添加数据行
  data.forEach((row, index) => {
    html += '<tr>';
    Object.values(row).forEach((cell, cellIndex) => {
      let cellValue = cell;
      let cellStyle = 'padding: 5px; border: 1px solid #ccc;';
      
      // 格式化数字和货币
      if (typeof cell === 'number') {
        if (headers[cellIndex] && (headers[cellIndex].includes('金额') || headers[cellIndex].includes('欠款'))) {
          cellValue = formatCurrency(cell);
          cellStyle += ' text-align: right; font-family: Arial;';
        } else {
          cellValue = formatNumber(cell);
          cellStyle += ' text-align: right; font-family: Arial;';
        }
      }
      
      // 特殊行样式（如合计行）
      if (row['客户名称'] === '合计' || row['序号'] === '') {
        cellStyle += ' background-color: #E7E6E6; font-weight: bold;';
      }
      
      // 斑马纹效果
      if (index % 2 === 0 && row['客户名称'] !== '合计') {
        cellStyle += ' background-color: #F2F2F2;';
      }
      
      html += `<td style="${cellStyle}">${cellValue}</td>`;
    });
    html += '</tr>';
  });
  
  html += '</table>';
  
  return html;
}

/**
 * 导出客户欠款查询数据
 */
export function exportCustomerDebt(data, queryParams, summaryData) {
  if (!data || data.length === 0) {
    throw new Error('没有数据可导出');
  }
  
  const title = '客户欠款查询报表';
  const subtitle = `查询日期：${queryParams.end_date} | 生成时间：${new Date().toLocaleString('zh-CN')}`;
  
  // 准备导出数据
  const exportData = data.map((item, index) => ({
    '序号': index + 1,
    '客户名称': item['客户名称'] || '',
    '业务员': item['业务员'] || '',
    '地区': item['地区'] || '',
    '4月欠款': item['4月欠款'] || 0,
    '销售汇总': item['销售汇总'] || 0,
    '回款汇总': item['回款汇总'] || 0,
    '当前欠款': item['当前欠款'] || 0
  }));
  
  // 添加汇总行
  if (summaryData) {
    exportData.push({
      '序号': '',
      '客户名称': '合计',
      '业务员': '',
      '地区': '',
      '4月欠款': summaryData.totalAprilDebt || 0,
      '销售汇总': summaryData.totalSales || 0,
      '回款汇总': summaryData.totalPayments || 0,
      '当前欠款': summaryData.totalCurrentDebt || 0
    });
  }
  
  const headers = ['序号', '客户名称', '业务员', '地区', '4月欠款', '销售汇总', '回款汇总', '当前欠款'];
  
  const html = createWorksheet(exportData, {
    title,
    subtitle,
    headers
  });
  
  // 创建下载
  downloadExcel(html, `客户欠款查询_${queryParams.end_date}`);
}

/**
 * 导出销售明细数据
 */
export function exportSalesDetail(data, customerName, queryParams, statistics) {
  if (!data || data.length === 0) {
    throw new Error('没有数据可导出');
  }
  
  const title = `${customerName} - 销售明细报表`;
  const subtitle = `查询期间：2025-05-01 至 ${queryParams.end_date} | 生成时间：${new Date().toLocaleString('zh-CN')}`;
  
  // 准备导出数据
  const exportData = data.map((item, index) => ({
    '序号': index + 1,
    '订单号': item['订单号'] || '',
    '销售日期': item['销售日期'] || '',
    '销售组织': item['销售组织'] || '',
    '订单行数': item['订单行数'] || 0,
    '总数量': item['总数量'] || 0,
    '订单金额': item['订单金额'] || 0,
    '备注': item['备注'] || ''
  }));
  
  // 添加统计行
  if (statistics) {
    exportData.push({
      '序号': '',
      '订单号': '统计汇总',
      '销售日期': '',
      '销售组织': '',
      '订单行数': `共${statistics['订单总数']}个订单`,
      '总数量': statistics['总数量'] || 0,
      '订单金额': statistics['销售总额'] || 0,
      '备注': `平均订单金额：${formatCurrency(statistics['平均订单金额'] || 0)}`
    });
  }
  
  const headers = ['序号', '订单号', '销售日期', '销售组织', '订单行数', '总数量', '订单金额', '备注'];
  
  const html = createWorksheet(exportData, {
    title,
    subtitle,
    headers
  });
  
  downloadExcel(html, `${customerName}_销售明细_${queryParams.end_date}`);
}

/**
 * 导出回款明细数据
 */
export function exportPaymentDetail(data, customerName, queryParams, statistics) {
  if (!data || data.length === 0) {
    throw new Error('没有数据可导出');
  }
  
  const title = `${customerName} - 回款明细报表`;
  const subtitle = `查询期间：2025-05-01 至 ${queryParams.end_date} | 生成时间：${new Date().toLocaleString('zh-CN')}`;
  
  // 准备导出数据
  const exportData = data.map((item, index) => ({
    '序号': index + 1,
    '往来单位': item['往来单位'] || '',
    '收款组织': item['收款组织'] || '',
    '收款日期': item['收款日期'] || '',
    '收款金额': item['收款金额'] || 0,
    '收款单号': item['收款单号'] || '',
    '收款方式': item['收款方式'] || '',
    '备注': item['备注'] || ''
  }));
  
  // 添加统计行
  if (statistics) {
    exportData.push({
      '序号': '',
      '往来单位': '统计汇总',
      '收款组织': '',
      '收款日期': '',
      '收款金额': statistics['收款总额'] || 0,
      '收款单号': `共${statistics['收款笔数']}笔`,
      '收款方式': '',
      '备注': `平均收款金额：${formatCurrency(statistics['平均收款金额'] || 0)}`
    });
  }
  
  const headers = ['序号', '往来单位', '收款组织', '收款日期', '收款金额', '收款单号', '收款方式', '备注'];
  
  const html = createWorksheet(exportData, {
    title,
    subtitle,
    headers
  });
  
  downloadExcel(html, `${customerName}_回款明细_${queryParams.end_date}`);
}

/**
 * 导出订单明细数据
 */
export function exportOrderDetail(data, orderNo, statistics) {
  if (!data || data.length === 0) {
    throw new Error('没有数据可导出');
  }
  
  const title = `订单 ${orderNo} - 详细明细报表`;
  const subtitle = `生成时间：${new Date().toLocaleString('zh-CN')}`;
  
  // 准备导出数据
  const exportData = data.map((item, index) => ({
    '序号': index + 1,
    '物料名称': item['物料名称'] || '',
    '数量': item['数量'] || 0,
    '基础数量': item['基础数量'] || 0,
    '单位': item['单位'] || '',
    '单价': item['单价'] || 0,
    '金额': item['金额'] || 0,
    '订单备注': item['订单备注'] || ''
  }));
  
  // 添加统计行
  if (statistics) {
    exportData.push({
      '序号': '',
      '物料名称': '统计汇总',
      '数量': statistics['总数量'] || 0,
      '基础数量': '',
      '单位': '',
      '单价': '',
      '金额': statistics['订单总额'] || 0,
      '订单备注': `共${statistics['明细行数']}行明细`
    });
  }
  
  const headers = ['序号', '物料名称', '数量', '基础数量', '单位', '单价', '金额', '订单备注'];
  
  const html = createWorksheet(exportData, {
    title,
    subtitle,
    headers
  });
  
  downloadExcel(html, `订单${orderNo}_明细`);
}

/**
 * 导出剩余货款告知TXT文件
 */
export function exportDebtNotificationTxt(data, queryParams) {
  if (!data || data.length === 0) {
    throw new Error('没有数据可导出');
  }

  // 定义公司名称到地区的映射
  const companyRegionMap = {
    '浙江华实生物科技有限公司': '浙江华实',
    '江苏华绿生物科技集团股份有限公司': '江苏华绿',
    '江苏华骏生物科技有限公司': '江苏华绿',
    '江苏省华蕈农业发展有限公司': '江苏华绿',
    '泗阳华盛生物科技有限公司': '江苏华绿',
    '泗阳华茂农业发展有限公司': '江苏华绿'
  };

  // 根据公司名称确定地区
  function getRegionByCompany(customerName) {
    // 首先检查是否是已知的公司
    if (companyRegionMap[customerName]) {
      return companyRegionMap[customerName];
    }

    // 如果不是已知公司，根据客户名称中的关键词判断
    if (customerName.includes('浙江') || customerName.includes('杭州') || customerName.includes('嘉兴') ||
        customerName.includes('湖州') || customerName.includes('绍兴') || customerName.includes('金华') ||
        customerName.includes('衢州') || customerName.includes('舟山') || customerName.includes('台州') ||
        customerName.includes('丽水') || customerName.includes('温州') || customerName.includes('宁波')) {
      return '浙江华实';
    }

    if (customerName.includes('江苏') || customerName.includes('南京') || customerName.includes('苏州') ||
        customerName.includes('无锡') || customerName.includes('常州') || customerName.includes('镇江') ||
        customerName.includes('南通') || customerName.includes('泰州') || customerName.includes('扬州') ||
        customerName.includes('盐城') || customerName.includes('连云港') || customerName.includes('徐州') ||
        customerName.includes('淮安') || customerName.includes('宿迁') || customerName.includes('泗阳')) {
      return '江苏华绿';
    }

    // 默认根据地区字段判断
    const region = data.find(item => item['客户名称'] === customerName)?.['地区'] || '';
    if (region.includes('浙江')) {
      return '浙江华实';
    } else if (region.includes('江苏')) {
      return '江苏华绿';
    }

    // 默认返回浙江华实
    return '浙江华实';
  }

  // 按地区分组数据 - 根据每条记录的实际地区字段
  const regionGroups = {};
  data.forEach(item => {
    const customerName = item['客户名称'] || '';
    const originalRegion = item['地区'] || '';

    // 根据实际地区字段确定显示的地区名称
    let regionName;
    if (originalRegion.includes('浙江')) {
      regionName = '浙江华实';
    } else if (originalRegion.includes('江苏')) {
      regionName = '江苏华绿';
    } else {
      // 如果地区字段不明确，则根据客户名称判断
      regionName = getRegionByCompany(customerName);
    }

    if (!regionGroups[regionName]) {
      regionGroups[regionName] = [];
    }
    regionGroups[regionName].push(item);
  });

  let txtContent = '';

  // 为每个地区生成告知内容
  Object.keys(regionGroups).forEach(regionName => {
    const customers = regionGroups[regionName];

    customers.forEach(customer => {
      const customerName = customer['客户名称'] || '';
      const currentDebt = customer['当前欠款'] || 0;

      // 为每个客户生成一行告知（包括欠款为0的情况）
      let debtAmount = `￥${currentDebt.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })}`;

      // 生成告知文本：一个客户一个基地一行，只显示剩余货款
      const notificationText = `截止${queryParams.end_date}[ ${customerName} ]${regionName}剩余货款为：${debtAmount}请尽快安排回款，谢谢。`;
      txtContent += notificationText + '\r\n';
    });

    // 地区之间添加空行分隔
    if (txtContent && !txtContent.endsWith('\r\n\r\n')) {
      txtContent += '\r\n';
    }
  });

  // 如果没有内容，添加提示
  if (!txtContent.trim()) {
    txtContent = `截止${queryParams.end_date}，暂无需要催收的货款。\r\n`;
  }

  // 下载TXT文件
  downloadTxt(txtContent, `剩余货款告知_${queryParams.end_date}`);
}

/**
 * 下载TXT文件
 */
function downloadTxt(content, filename) {
  // 创建Blob对象，使用UTF-8编码
  const blob = new Blob(['\ufeff' + content], {
    type: 'text/plain;charset=utf-8'
  });

  // 创建下载链接
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);

  link.href = url;
  link.download = `${filename}.txt`;
  link.style.display = 'none';

  // 触发下载
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  // 清理URL对象
  URL.revokeObjectURL(url);
}

/**
 * 下载Excel文件
 */
function downloadExcel(html, filename) {
  // 创建Blob对象
  const blob = new Blob([html], {
    type: 'application/vnd.ms-excel;charset=utf-8'
  });

  // 创建下载链接
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);

  link.href = url;
  link.download = `${filename}.xls`;
  link.style.display = 'none';

  // 触发下载
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  // 清理URL对象
  URL.revokeObjectURL(url);
}
