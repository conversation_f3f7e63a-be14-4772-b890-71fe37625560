from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from datetime import datetime, timedelta
from django.db import connections
import logging

logger = logging.getLogger(__name__)

@api_view(['POST'])
@permission_classes([AllowAny])
def customer_analysis_query(request):
    """
    客户分析查询接口 - 分析客户下单情况
    """
    # 初始化默认值
    threshold_days = 7

    try:
        # 获取查询参数
        data = request.data
        threshold_days = data.get('thresholdDays', 7)  # 默认7天
        company_filter = data.get('company', '')  # 公司筛选
        customer_name_filter = data.get('customerName', '')  # 客户名称筛选
        page = data.get('page', 1)  # 当前页，默认第1页
        page_size = data.get('pageSize', 20)  # 每页大小，默认20条
        
        # 计算阈值日期
        threshold_date = datetime.now() - timedelta(days=threshold_days)
        threshold_date_str = threshold_date.strftime('%Y-%m-%d')
        
        # 构建基础CTE查询
        base_cte = f"""
        WITH CustomerLastOrder AS (
            -- 获取每个客户的最后下单时间（只统计有订单的客户）
            SELECT
                c.FCUSTID,
                c_l.FNAME AS 客户名称,
                org_l.FNAME AS 公司名称,
                MAX(o.FDATE) AS 最后下单时间,
                COUNT(DISTINCT o.FID) AS 历史订单总数,
                ROUND(SUM(f.FALLAMOUNT), 2) AS 历史订单总额
            FROM T_BD_CUSTOMER c WITH (NOLOCK)
            INNER JOIN T_BD_CUSTOMER_L c_l WITH (NOLOCK) ON c.FCUSTID = c_l.FCUSTID AND c_l.FLOCALEID = 2052
            INNER JOIN T_SAL_ORDER o WITH (NOLOCK) ON c.FCUSTID = o.FCUSTID  -- 改为INNER JOIN，只包含有订单的客户
            INNER JOIN T_SAL_ORDERENTRY e WITH (NOLOCK) ON o.FID = e.FID
            INNER JOIN T_SAL_ORDERENTRY_F f WITH (NOLOCK) ON e.FENTRYID = f.FENTRYID
            LEFT JOIN T_ORG_ORGANIZATIONS org WITH (NOLOCK) ON o.FSALEORGID = org.FORGID
            LEFT JOIN T_ORG_ORGANIZATIONS_L org_l WITH (NOLOCK) ON org.FORGID = org_l.FORGID AND org_l.FLOCALEID = 2052
            WHERE c.FDOCUMENTSTATUS = 'C'  -- 只查询已审核的客户
                AND ISNULL(e.F_JSHL_CHECKBOX_QTR, '0') != '1'
                AND e.FBASEUNITQTY > 0
                AND f.FALLAMOUNT > 0
                {f"AND org_l.FNAME LIKE '%{company_filter}%'" if company_filter else ""}
                {f"AND c_l.FNAME LIKE '%{customer_name_filter}%'" if customer_name_filter else ""}
            GROUP BY c.FCUSTID, c_l.FNAME, org_l.FNAME
            HAVING COUNT(DISTINCT o.FID) > 0  -- 确保有订单记录
        ),
        CustomerAnalysis AS (
            SELECT
                客户名称,
                公司名称,
                最后下单时间,
                历史订单总数,
                历史订单总额,
                DATEDIFF(DAY, 最后下单时间, GETDATE()) AS 距离最后下单天数,
                CASE
                    WHEN DATEDIFF(DAY, 最后下单时间, GETDATE()) = 0 THEN '今天下单'
                    WHEN DATEDIFF(DAY, 最后下单时间, GETDATE()) = 1 THEN '昨天下单'
                    WHEN DATEDIFF(DAY, 最后下单时间, GETDATE()) <= {threshold_days} THEN '活跃客户'
                    WHEN DATEDIFF(DAY, 最后下单时间, GETDATE()) <= {threshold_days * 2} THEN '一般客户'
                    WHEN DATEDIFF(DAY, 最后下单时间, GETDATE()) <= {threshold_days * 4} THEN '沉睡客户'
                    ELSE '流失客户'
                END AS 客户状态,
                CASE
                    WHEN DATEDIFF(DAY, 最后下单时间, GETDATE()) > {threshold_days} THEN '高'
                    WHEN DATEDIFF(DAY, 最后下单时间, GETDATE()) > {threshold_days // 2} THEN '中'
                    ELSE '低'
                END AS 流失风险
            FROM CustomerLastOrder
        )
        """

        # 查询总数
        count_query = base_cte + """
        SELECT COUNT(*) as total_count FROM CustomerAnalysis
        """

        # 查询风险统计数据
        risk_stats_query = base_cte + """
        SELECT
            SUM(CASE WHEN 流失风险 = '高' THEN 1 ELSE 0 END) as high_risk_customers,
            SUM(CASE WHEN 流失风险 = '中' THEN 1 ELSE 0 END) as medium_risk_customers,
            SUM(CASE WHEN 流失风险 = '低' THEN 1 ELSE 0 END) as low_risk_customers
        FROM CustomerAnalysis
        """

        # 查询状态统计数据
        status_stats_query = base_cte + """
        SELECT
            客户状态,
            COUNT(*) as status_count
        FROM CustomerAnalysis
        GROUP BY 客户状态
        """

        # 计算分页参数
        offset = (page - 1) * page_size

        # 查询分页数据 - 使用ROW_NUMBER()方式实现分页
        data_query = base_cte + f"""
        SELECT * FROM (
            SELECT
                客户名称,
                公司名称,
                最后下单时间,
                距离最后下单天数,
                客户状态,
                流失风险,
                历史订单总数,
                历史订单总额,
                ROW_NUMBER() OVER (ORDER BY 距离最后下单天数 DESC, 历史订单总额 DESC) as rn
            FROM CustomerAnalysis
        ) t
        WHERE rn > {offset} AND rn <= {offset + page_size}
        """
        
        # 执行查询
        with connections['sqlserver'].cursor() as cursor:
            # 先查询总数
            cursor.execute(count_query)
            total_count = cursor.fetchone()[0]

            # 查询风险统计数据
            cursor.execute(risk_stats_query)
            risk_stats = cursor.fetchone()
            high_risk_customers = risk_stats[0] if risk_stats[0] else 0
            medium_risk_customers = risk_stats[1] if risk_stats[1] else 0
            low_risk_customers = risk_stats[2] if risk_stats[2] else 0

            # 查询状态统计数据
            cursor.execute(status_stats_query)
            status_columns = [col[0] for col in cursor.description]
            status_results = cursor.fetchall()

            # 再查询分页数据
            cursor.execute(data_query)
            columns = [col[0] for col in cursor.description]
            results = cursor.fetchall()

        # 转换为字典列表
        customer_data = []
        for row in results:
            row_dict = dict(zip(columns, row))
            # 格式化日期
            if row_dict['最后下单时间']:
                row_dict['最后下单时间'] = row_dict['最后下单时间'].strftime('%Y-%m-%d')
            else:
                row_dict['最后下单时间'] = '从未下单'

            # 格式化金额
            if row_dict['历史订单总额']:
                row_dict['历史订单总额'] = float(row_dict['历史订单总额'])
            else:
                row_dict['历史订单总额'] = 0.0

            customer_data.append(row_dict)
        
        # 处理统计数据
        total_customers = total_count
        status_stats = {}

        # 处理状态统计查询结果
        for row in status_results:
            status_dict = dict(zip(status_columns, row))
            status_stats[status_dict['客户状态']] = status_dict['status_count']
        
        # 构建响应数据
        response_data = {
            'code': 200,
            'message': '查询成功',
            'data': {
                'statistics': {
                    'totalCustomers': total_customers,
                    'highRiskCustomers': high_risk_customers,
                    'mediumRiskCustomers': medium_risk_customers,
                    'lowRiskCustomers': low_risk_customers,
                    'thresholdDays': threshold_days,
                    'statusStats': status_stats
                },
                'list': customer_data,
                'total': total_count
            }
        }
        
        return Response(response_data)
        
    except Exception as e:
        logger.error(f"客户分析查询失败: {str(e)}")
        return Response({
            'code': 500,
            'message': f'查询失败: {str(e)}',
            'data': {
                'statistics': {
                    'totalCustomers': 0,
                    'highRiskCustomers': 0,
                    'mediumRiskCustomers': 0,
                    'lowRiskCustomers': 0,
                    'thresholdDays': threshold_days,
                    'statusStats': {}
                },
                'list': []
            }
        }, status=500)


@api_view(['POST'])
@permission_classes([AllowAny])
def customer_analysis_export(request):
    """
    客户分析数据导出Excel接口
    """
    try:
        import pandas as pd
        from django.http import HttpResponse
        import io

        # 获取查询参数
        data = request.data
        threshold_days = data.get('thresholdDays', 7)
        company_filter = data.get('company', '')
        customer_name_filter = data.get('customerName', '')

        # 直接调用查询接口获取数据（导出所有数据，不分页）
        from django.test import RequestFactory
        import json

        factory = RequestFactory()
        post_request = factory.post('/api/customer-analysis/query/',
            json.dumps({
                'thresholdDays': threshold_days,
                'company': company_filter,
                'customerName': customer_name_filter,
                'page': 1,
                'pageSize': 999999  # 导出所有数据
            }),
            content_type='application/json'
        )

        # 调用查询接口
        query_response = customer_analysis_query(post_request)
        query_data = query_response.data

        if query_data['code'] != 200:
            return Response({
                'code': 500,
                'message': f'数据查询失败: {query_data.get("message", "未知错误")}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        customer_data = query_data['data']['list']

        if not customer_data:
            return Response({
                'code': 404,
                'message': '没有找到符合条件的数据',
                'data': None
            })

        # 创建DataFrame
        df = pd.DataFrame(customer_data)
        
        # 重命名列以便导出
        column_mapping = {
            '客户名称': '客户名称',
            '公司名称': '公司名称', 
            '最后下单时间': '最后下单时间',
            '距离最后下单天数': '距离最后下单天数',
            '客户状态': '客户状态',
            '流失风险': '流失风险',
            '历史订单总数': '历史订单总数',
            '历史订单总额': '历史订单总额'
        }
        
        df = df.rename(columns=column_mapping)

        # 创建Excel文件
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='客户分析报告', index=False)

        output.seek(0)

        # 设置响应
        response = HttpResponse(
            output.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
        # 生成文件名
        current_time = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'客户分析报告_{threshold_days}天阈值_{current_time}.xlsx'
        response['Content-Disposition'] = f'attachment; filename="{filename}"'

        return response

    except Exception as e:
        logger.error(f"客户分析导出失败: {str(e)}")
        return Response({
            'code': 500,
            'message': f'导出失败: {str(e)}',
            'data': None
        }, status=500)


@api_view(['GET'])
@permission_classes([AllowAny])
def get_company_list(request):
    """获取所有公司列表"""
    try:
        # 查询所有有销售订单的公司
        query = """
        SELECT DISTINCT org_l.FNAME AS 公司名称
        FROM T_SAL_ORDER o WITH (NOLOCK)
        LEFT JOIN T_ORG_ORGANIZATIONS org WITH (NOLOCK) ON o.FSALEORGID = org.FORGID
        LEFT JOIN T_ORG_ORGANIZATIONS_L org_l WITH (NOLOCK) ON org.FORGID = org_l.FORGID AND org_l.FLOCALEID = 2052
        WHERE org_l.FNAME IS NOT NULL AND org_l.FNAME != ''
        ORDER BY org_l.FNAME
        """

        # 执行查询
        cursor = connections['sqlserver'].cursor()
        cursor.execute(query)
        results = cursor.fetchall()

        # 构建公司列表
        company_list = []
        for row in results:
            company_name = row[0]
            if company_name:  # 确保公司名称不为空
                company_list.append({
                    'label': company_name,
                    'value': company_name
                })

        return Response({
            'code': 200,
            'message': '获取公司列表成功',
            'data': company_list
        })

    except Exception as e:
        logger.error(f"获取公司列表失败: {e}")
        return Response({
            'code': 500,
            'message': f'获取公司列表失败: {str(e)}',
            'data': []
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
