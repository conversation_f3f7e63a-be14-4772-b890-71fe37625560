class n{constructor(){this._dataLength=0,this._bufferLength=0,this._state=new Int32Array(4),this._buffer=new ArrayBuffer(68),this._buffer8=new Uint8Array(this._buffer,0,68),this._buffer32=new Uint32Array(this._buffer,0,17),this.start()}static hashStr(f,h=!1){return this.onePassHasher.start().appendStr(f).end(h)}static hashAsciiStr(f,h=!1){return this.onePassHasher.start().appendAsciiStr(f).end(h)}static _hex(f){const h=n.hexChars,s=n.hexOut;let t,e,r,i;for(i=0;i<4;i+=1)for(e=i*8,t=f[i],r=0;r<8;r+=2)s[e+1+r]=h.charAt(t&15),t>>>=4,s[e+0+r]=h.charAt(t&15),t>>>=4;return s.join("")}static _md5cycle(f,h){let s=f[0],t=f[1],e=f[2],r=f[3];s+=(t&e|~t&r)+h[0]-680876936|0,s=(s<<7|s>>>25)+t|0,r+=(s&t|~s&e)+h[1]-389564586|0,r=(r<<12|r>>>20)+s|0,e+=(r&s|~r&t)+h[2]+606105819|0,e=(e<<17|e>>>15)+r|0,t+=(e&r|~e&s)+h[3]-1044525330|0,t=(t<<22|t>>>10)+e|0,s+=(t&e|~t&r)+h[4]-176418897|0,s=(s<<7|s>>>25)+t|0,r+=(s&t|~s&e)+h[5]+1200080426|0,r=(r<<12|r>>>20)+s|0,e+=(r&s|~r&t)+h[6]-1473231341|0,e=(e<<17|e>>>15)+r|0,t+=(e&r|~e&s)+h[7]-45705983|0,t=(t<<22|t>>>10)+e|0,s+=(t&e|~t&r)+h[8]+1770035416|0,s=(s<<7|s>>>25)+t|0,r+=(s&t|~s&e)+h[9]-1958414417|0,r=(r<<12|r>>>20)+s|0,e+=(r&s|~r&t)+h[10]-42063|0,e=(e<<17|e>>>15)+r|0,t+=(e&r|~e&s)+h[11]-1990404162|0,t=(t<<22|t>>>10)+e|0,s+=(t&e|~t&r)+h[12]+1804603682|0,s=(s<<7|s>>>25)+t|0,r+=(s&t|~s&e)+h[13]-40341101|0,r=(r<<12|r>>>20)+s|0,e+=(r&s|~r&t)+h[14]-1502002290|0,e=(e<<17|e>>>15)+r|0,t+=(e&r|~e&s)+h[15]+1236535329|0,t=(t<<22|t>>>10)+e|0,s+=(t&r|e&~r)+h[1]-165796510|0,s=(s<<5|s>>>27)+t|0,r+=(s&e|t&~e)+h[6]-1069501632|0,r=(r<<9|r>>>23)+s|0,e+=(r&t|s&~t)+h[11]+643717713|0,e=(e<<14|e>>>18)+r|0,t+=(e&s|r&~s)+h[0]-373897302|0,t=(t<<20|t>>>12)+e|0,s+=(t&r|e&~r)+h[5]-701558691|0,s=(s<<5|s>>>27)+t|0,r+=(s&e|t&~e)+h[10]+38016083|0,r=(r<<9|r>>>23)+s|0,e+=(r&t|s&~t)+h[15]-660478335|0,e=(e<<14|e>>>18)+r|0,t+=(e&s|r&~s)+h[4]-405537848|0,t=(t<<20|t>>>12)+e|0,s+=(t&r|e&~r)+h[9]+568446438|0,s=(s<<5|s>>>27)+t|0,r+=(s&e|t&~e)+h[14]-1019803690|0,r=(r<<9|r>>>23)+s|0,e+=(r&t|s&~t)+h[3]-187363961|0,e=(e<<14|e>>>18)+r|0,t+=(e&s|r&~s)+h[8]+1163531501|0,t=(t<<20|t>>>12)+e|0,s+=(t&r|e&~r)+h[13]-1444681467|0,s=(s<<5|s>>>27)+t|0,r+=(s&e|t&~e)+h[2]-51403784|0,r=(r<<9|r>>>23)+s|0,e+=(r&t|s&~t)+h[7]+1735328473|0,e=(e<<14|e>>>18)+r|0,t+=(e&s|r&~s)+h[12]-1926607734|0,t=(t<<20|t>>>12)+e|0,s+=(t^e^r)+h[5]-378558|0,s=(s<<4|s>>>28)+t|0,r+=(s^t^e)+h[8]-2022574463|0,r=(r<<11|r>>>21)+s|0,e+=(r^s^t)+h[11]+1839030562|0,e=(e<<16|e>>>16)+r|0,t+=(e^r^s)+h[14]-35309556|0,t=(t<<23|t>>>9)+e|0,s+=(t^e^r)+h[1]-1530992060|0,s=(s<<4|s>>>28)+t|0,r+=(s^t^e)+h[4]+1272893353|0,r=(r<<11|r>>>21)+s|0,e+=(r^s^t)+h[7]-155497632|0,e=(e<<16|e>>>16)+r|0,t+=(e^r^s)+h[10]-1094730640|0,t=(t<<23|t>>>9)+e|0,s+=(t^e^r)+h[13]+681279174|0,s=(s<<4|s>>>28)+t|0,r+=(s^t^e)+h[0]-358537222|0,r=(r<<11|r>>>21)+s|0,e+=(r^s^t)+h[3]-722521979|0,e=(e<<16|e>>>16)+r|0,t+=(e^r^s)+h[6]+76029189|0,t=(t<<23|t>>>9)+e|0,s+=(t^e^r)+h[9]-640364487|0,s=(s<<4|s>>>28)+t|0,r+=(s^t^e)+h[12]-421815835|0,r=(r<<11|r>>>21)+s|0,e+=(r^s^t)+h[15]+530742520|0,e=(e<<16|e>>>16)+r|0,t+=(e^r^s)+h[2]-995338651|0,t=(t<<23|t>>>9)+e|0,s+=(e^(t|~r))+h[0]-198630844|0,s=(s<<6|s>>>26)+t|0,r+=(t^(s|~e))+h[7]+1126891415|0,r=(r<<10|r>>>22)+s|0,e+=(s^(r|~t))+h[14]-1416354905|0,e=(e<<15|e>>>17)+r|0,t+=(r^(e|~s))+h[5]-57434055|0,t=(t<<21|t>>>11)+e|0,s+=(e^(t|~r))+h[12]+1700485571|0,s=(s<<6|s>>>26)+t|0,r+=(t^(s|~e))+h[3]-1894986606|0,r=(r<<10|r>>>22)+s|0,e+=(s^(r|~t))+h[10]-1051523|0,e=(e<<15|e>>>17)+r|0,t+=(r^(e|~s))+h[1]-2054922799|0,t=(t<<21|t>>>11)+e|0,s+=(e^(t|~r))+h[8]+1873313359|0,s=(s<<6|s>>>26)+t|0,r+=(t^(s|~e))+h[15]-30611744|0,r=(r<<10|r>>>22)+s|0,e+=(s^(r|~t))+h[6]-1560198380|0,e=(e<<15|e>>>17)+r|0,t+=(r^(e|~s))+h[13]+1309151649|0,t=(t<<21|t>>>11)+e|0,s+=(e^(t|~r))+h[4]-145523070|0,s=(s<<6|s>>>26)+t|0,r+=(t^(s|~e))+h[11]-1120210379|0,r=(r<<10|r>>>22)+s|0,e+=(s^(r|~t))+h[2]+718787259|0,e=(e<<15|e>>>17)+r|0,t+=(r^(e|~s))+h[9]-343485551|0,t=(t<<21|t>>>11)+e|0,f[0]=s+f[0]|0,f[1]=t+f[1]|0,f[2]=e+f[2]|0,f[3]=r+f[3]|0}start(){return this._dataLength=0,this._bufferLength=0,this._state.set(n.stateIdentity),this}appendStr(f){const h=this._buffer8,s=this._buffer32;let t=this._bufferLength,e,r;for(r=0;r<f.length;r+=1){if(e=f.charCodeAt(r),e<128)h[t++]=e;else if(e<2048)h[t++]=(e>>>6)+192,h[t++]=e&63|128;else if(e<55296||e>56319)h[t++]=(e>>>12)+224,h[t++]=e>>>6&63|128,h[t++]=e&63|128;else{if(e=(e-55296)*1024+(f.charCodeAt(++r)-56320)+65536,e>1114111)throw new Error("Unicode standard supports code points up to U+10FFFF");h[t++]=(e>>>18)+240,h[t++]=e>>>12&63|128,h[t++]=e>>>6&63|128,h[t++]=e&63|128}t>=64&&(this._dataLength+=64,n._md5cycle(this._state,s),t-=64,s[0]=s[16])}return this._bufferLength=t,this}appendAsciiStr(f){const h=this._buffer8,s=this._buffer32;let t=this._bufferLength,e,r=0;for(;;){for(e=Math.min(f.length-r,64-t);e--;)h[t++]=f.charCodeAt(r++);if(t<64)break;this._dataLength+=64,n._md5cycle(this._state,s),t=0}return this._bufferLength=t,this}appendByteArray(f){const h=this._buffer8,s=this._buffer32;let t=this._bufferLength,e,r=0;for(;;){for(e=Math.min(f.length-r,64-t);e--;)h[t++]=f[r++];if(t<64)break;this._dataLength+=64,n._md5cycle(this._state,s),t=0}return this._bufferLength=t,this}getState(){const f=this._state;return{buffer:String.fromCharCode.apply(null,Array.from(this._buffer8)),buflen:this._bufferLength,length:this._dataLength,state:[f[0],f[1],f[2],f[3]]}}setState(f){const h=f.buffer,s=f.state,t=this._state;let e;for(this._dataLength=f.length,this._bufferLength=f.buflen,t[0]=s[0],t[1]=s[1],t[2]=s[2],t[3]=s[3],e=0;e<h.length;e+=1)this._buffer8[e]=h.charCodeAt(e)}end(f=!1){const h=this._bufferLength,s=this._buffer8,t=this._buffer32,e=(h>>2)+1;this._dataLength+=h;const r=this._dataLength*8;if(s[h]=128,s[h+1]=s[h+2]=s[h+3]=0,t.set(n.buffer32Identity.subarray(e),e),h>55&&(n._md5cycle(this._state,t),t.set(n.buffer32Identity)),r<=4294967295)t[14]=r;else{const i=r.toString(16).match(/(.*?)(.{0,8})$/);if(i===null)return;const a=parseInt(i[2],16),u=parseInt(i[1],16)||0;t[14]=a,t[15]=u}return n._md5cycle(this._state,t),f?this._state:n._hex(this._state)}}n.stateIdentity=new Int32Array([1732584193,-271733879,-1732584194,271733878]);n.buffer32Identity=new Int32Array([0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]);n.hexChars="0123456789abcdef";n.hexOut=[];n.onePassHasher=new n;if(n.hashStr("hello")!=="5d41402abc4b2a76b9719d911017c592")throw new Error("Md5 self test failed.");export{n as M};
