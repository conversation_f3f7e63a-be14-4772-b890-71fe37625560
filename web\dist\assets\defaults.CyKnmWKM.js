const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/aside.PpDlMa_w.js","assets/index.BHZI5pdK.js","assets/vue.BNx9QYep.js","assets/index.Dg-OhEXY.css","assets/header.DFPmrsWF.js","assets/main.6V4YA0vm.js"])))=>i.map(i=>d[i]);
import{u as b,Q as k,_ as a}from"./index.BHZI5pdK.js";import{d as p,h as _,Q as C,M as L,j as M,v as i,k as f,b as w,o as E,w as n,l as e,u as s,P as r,I as S}from"./vue.BNx9QYep.js";const g=p({name:"layoutDefaults"}),V=p({...g,setup(x){const d=r(()=>a(()=>import("./aside.PpDlMa_w.js"),__vite__mapDeps([0,1,2,3]))),m=r(()=>a(()=>import("./header.DFPmrsWF.js"),__vite__mapDeps([4,1,2,3]))),y=r(()=>a(()=>import("./main.6V4YA0vm.js"),__vite__mapDeps([5,1,2,3]))),o=_(""),t=_(),R=C(),v=b(),{themeConfig:h}=L(v),l=()=>{o.value.update(),t.value.layoutMainScrollbarRef.update()},c=()=>{S(()=>{setTimeout(()=>{l(),o.value.wrapRef.scrollTop=0,t.value.layoutMainScrollbarRef.wrapRef.scrollTop=0},500)})};return M(()=>{c(),k.done(600)}),i(()=>R.path,()=>{c()}),i(h,()=>{l()},{deep:!0}),(A,P)=>{const T=f("el-scrollbar"),u=f("el-container");return E(),w(u,{class:"layout-container"},{default:n(()=>[e(s(d)),e(u,{class:"layout-container-view h100"},{default:n(()=>[e(T,{ref_key:"layoutScrollbarRef",ref:o,class:"layout-backtop"},{default:n(()=>[e(s(m)),e(s(y),{ref_key:"layoutMainRef",ref:t},null,512)]),_:1},512)]),_:1})]),_:1})}}});export{V as default};
