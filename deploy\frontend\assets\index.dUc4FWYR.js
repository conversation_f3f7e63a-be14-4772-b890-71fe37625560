const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/defaults.CyKnmWKM.js","assets/index.BHZI5pdK.js","assets/vue.BNx9QYep.js","assets/index.Dg-OhEXY.css","assets/classic.BspuZjk6.js","assets/transverse.EnBgnIST.js","assets/columns.BSK2byCI.js"])))=>i.map(i=>d[i]);
import{u as _,_ as t,L as s,l as i}from"./index.BHZI5pdK.js";import{d as l,M as d,N as m,O as c,b as f,o as y,r as p,P as a,u as v}from"./vue.BNx9QYep.js";const L=l({name:"layout"}),g=l({...L,setup(E){const u={defaults:a(()=>t(()=>import("./defaults.CyKnmWKM.js"),__vite__mapDeps([0,1,2,3]))),classic:a(()=>t(()=>import("./classic.BspuZjk6.js"),__vite__mapDeps([4,1,2,3]))),transverse:a(()=>t(()=>import("./transverse.EnBgnIST.js"),__vite__mapDeps([5,1,2,3]))),columns:a(()=>t(()=>import("./columns.BSK2byCI.js"),__vite__mapDeps([6,1,2,3])))},r=_(),{themeConfig:e}=d(r),n=()=>{s.get("oldLayout")||s.set("oldLayout",e.value.layout);const o=document.body.clientWidth;o<1e3?(e.value.isCollapse=!1,i.emit("layoutMobileResize",{layout:"defaults",clientWidth:o})):i.emit("layoutMobileResize",{layout:s.get("oldLayout")?s.get("oldLayout"):e.value.layout,clientWidth:o})};return m(()=>{n(),window.addEventListener("resize",n)}),c(()=>{window.removeEventListener("resize",n)}),(o,R)=>(y(),f(p(u[v(e).layout])))}});export{g as default};
