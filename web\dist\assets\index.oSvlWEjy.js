import{X as R,E as V,J as g}from"./index.BHZI5pdK.js";import B from"./index.Bb5CeGfD.js";import N from"./index.pN7qy9sr.js";import F from"./index.Cf2DX0Ad.js";import{G as M,D as A}from"./api.B8LmnnXE.js";import{d as x,h as l,j as E,k as d,b as v,o as C,w as c,l as s,e as b,u as n,$ as j,m as G}from"./vue.BNx9QYep.js";import{_ as X}from"./_plugin-vue_export-helper.DlAUqK2U.js";import"./index.es.DmevZXPX.js";import"./md5.DLPczxzP.js";import"./crud.CDheULph.js";import"./dictionary.DNsEqk19.js";import"./authFunction.D3Be3hRy.js";import"./index.vue_vue_type_script_setup_true_name_importExcel_lang.COJSjT1E.js";import"./echarts.BiCAFTQd.js";const z={class:"dept-box dept-left"},I={class:"dept-box dept-table"},J=x({name:"dept"}),K=x({...J,setup(L){let p=l([]),_=l([]),o=l(!1),i=l({}),m=l(null),D=l(null);const f=async()=>{let e=await M({});if((e==null?void 0:e.code)===2e3&&Array.isArray(e.data)){const t=R.toArrayTree(e.data,{parentKey:"parent",children:"children"});p.value=t}},y=e=>{var t;(t=m.value)==null||t.handleDoRefreshUser(e.id)},T=(e,t)=>{V.confirm("您确认删除该部门吗?","温馨提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(async()=>{var r;const a=await A(e);t(),(a==null?void 0:a.code)===2e3&&(g(a.msg),f(),(r=m.value)==null||r.handleDoRefreshUser(""))})},k=(e,t)=>{var a,r;if(e==="update"&&t){const u=((r=(a=D.value)==null?void 0:a.treeRef)==null?void 0:r.currentNode.parent.data)||{};_.value=[u],i.value=t}o.value=!0},h=e=>{e==="submit"&&f(),o.value=!1,i.value={}};return E(()=>{f()}),(e,t)=>{const a=d("el-col"),r=d("el-row"),u=d("el-drawer"),U=d("fs-page");return C(),v(U,null,{default:c(()=>[s(r,{class:"dept-el-row"},{default:c(()=>[s(a,{span:6},{default:c(()=>[b("div",z,[s(B,{ref_key:"deptTreeRef",ref:D,treeData:n(p),onTreeClick:y,onUpdateDept:k,onDeleteDept:T},null,8,["treeData"])])]),_:1}),s(a,{span:18},{default:c(()=>[b("div",I,[s(F,{ref_key:"deptUserRef",ref:m},null,512)])]),_:1})]),_:1}),s(u,{modelValue:n(o),"onUpdate:modelValue":t[0]||(t[0]=w=>j(o)?o.value=w:o=w),title:"部门配置",direction:"rtl",size:"500px","close-on-click-modal":!1,"before-close":h},{default:c(()=>[n(o)?(C(),v(N,{key:0,initFormData:n(i),treeData:n(p),cacheData:n(_),onDrawerClose:h},null,8,["initFormData","treeData","cacheData"])):G("",!0)]),_:1},8,["modelValue"])]),_:1})}}}),re=X(K,[["__scopeId","data-v-24cbbb3f"]]);export{re as default};
