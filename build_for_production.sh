#!/bin/bash
# 华绿系统 - 生产环境构建脚本 (Linux版)

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}====================================================${NC}"
echo -e "${BLUE}华绿系统 - 生产环境构建脚本${NC}"
echo -e "${BLUE}====================================================${NC}"
echo

# 检查Node.js环境
echo -e "${YELLOW}[1/6] 检查Node.js环境...${NC}"
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ 错误：未检测到Node.js，请先安装Node.js${NC}"
    echo "安装命令："
    echo "  Ubuntu/Debian: sudo apt install nodejs npm"
    echo "  CentOS/RHEL: sudo yum install nodejs npm"
    exit 1
fi
echo -e "${GREEN}✅ Node.js环境检查通过 ($(node --version))${NC}"

# 检查Python环境
echo
echo -e "${YELLOW}[2/6] 检查Python环境...${NC}"
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}❌ 错误：未检测到Python3，请先安装Python3${NC}"
    exit 1
fi
echo -e "${GREEN}✅ Python环境检查通过 ($(python3 --version))${NC}"

# 构建前端
echo
echo -e "${YELLOW}[3/6] 构建Vue3前端...${NC}"
cd web

echo "正在安装前端依赖..."
if ! npm install; then
    echo -e "${RED}❌ 前端依赖安装失败${NC}"
    exit 1
fi

echo "正在构建生产版本..."
if ! npm run build; then
    echo -e "${RED}❌ 前端构建失败${NC}"
    exit 1
fi

# 检查构建结果
if [ ! -d "dist" ]; then
    echo -e "${RED}❌ 错误：构建目录不存在${NC}"
    exit 1
fi
echo -e "${GREEN}✅ 前端构建完成${NC}"

# 处理后端
cd ../backend
echo
echo -e "${YELLOW}[4/6] 准备Django后端...${NC}"

# 创建虚拟环境（如果不存在）
if [ ! -d "venv" ]; then
    echo "创建Python虚拟环境..."
    python3 -m venv venv
fi

# 激活虚拟环境
source venv/bin/activate

# 安装后端依赖
echo "正在安装后端依赖..."
if ! pip install -r requirements.txt; then
    echo -e "${RED}❌ 后端依赖安装失败${NC}"
    exit 1
fi

# 收集静态文件
echo "正在收集静态文件..."
if ! python manage.py collectstatic --noinput; then
    echo -e "${RED}❌ 静态文件收集失败${NC}"
    exit 1
fi
echo -e "${GREEN}✅ 后端准备完成${NC}"

# 创建部署包
cd ..
echo
echo -e "${YELLOW}[5/6] 创建部署包...${NC}"

# 创建部署目录
rm -rf deploy
mkdir -p deploy/backend
mkdir -p deploy/frontend

# 复制后端文件
echo "正在复制后端文件..."
cp -r backend/* deploy/backend/
# 排除不必要的文件
rm -rf deploy/backend/__pycache__
rm -rf deploy/backend/*/__pycache__
rm -rf deploy/backend/*/*/__pycache__
rm -rf deploy/backend/.git
find deploy/backend -name "*.pyc" -delete
echo -e "${GREEN}✅ 后端文件复制完成${NC}"

# 复制前端构建文件
echo "正在复制前端构建文件..."
cp -r web/dist/* deploy/frontend/
echo -e "${GREEN}✅ 前端文件复制完成${NC}"

# 创建部署说明文件
echo
echo -e "${YELLOW}[6/6] 生成部署文档...${NC}"
cat > deploy/部署说明.md << 'EOF'
# 华绿系统 - 部署包说明

## 目录结构
```
deploy/
├── backend/          # Django后端代码
├── frontend/         # Vue3前端构建文件
├── 部署说明.md       # 本文件
├── start_server.sh   # Linux启动脚本
└── nginx.conf        # Nginx配置示例
```

## 部署步骤

### 1. 环境准备
- Python 3.8+
- pip
- 数据库 (MySQL/PostgreSQL/SQLite)
- Web服务器 (Nginx推荐)

### 2. 后端部署
```bash
cd backend
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
python manage.py migrate
python manage.py collectstatic --noinput
```

### 3. 前端部署
将 frontend/ 目录下的文件部署到Web服务器根目录

### 4. 配置Web服务器
参考 nginx.conf 配置文件

### 5. 启动服务
```bash
./start_server.sh
```

## 注意事项
1. 修改 backend/application/settings.py 中的数据库配置
2. 设置 DEBUG = False
3. 配置 ALLOWED_HOSTS
4. 设置正确的静态文件路径

EOF

# 创建启动脚本
cat > deploy/start_server.sh << 'EOF'
#!/bin/bash
# 华绿系统服务器启动脚本

echo "启动华绿系统..."
cd backend

# 激活虚拟环境
if [ -d "venv" ]; then
    source venv/bin/activate
fi

# 启动Django服务
python manage.py runserver 0.0.0.0:8000
EOF

chmod +x deploy/start_server.sh

# 创建Nginx配置示例
cat > deploy/nginx.conf << 'EOF'
server {
    listen 80;
    server_name your-domain.com;
    
    # 前端静态文件
    location / {
        root /path/to/deploy/frontend;
        try_files $uri $uri/ /index.html;
        index index.html;
    }
    
    # API代理到Django
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Django静态文件
    location /static/ {
        alias /path/to/deploy/backend/static/;
        expires 30d;
    }
    
    # Django媒体文件
    location /media/ {
        alias /path/to/deploy/backend/media/;
        expires 30d;
    }
}
EOF

echo -e "${GREEN}✅ 部署文档生成完成${NC}"

# 显示结果
echo
echo -e "${BLUE}====================================================${NC}"
echo -e "${GREEN}🎉 构建完成！${NC}"
echo -e "${BLUE}====================================================${NC}"
echo
echo -e "${YELLOW}📁 部署文件位置：${NC}$(pwd)/deploy"
echo -e "${YELLOW}📄 部署说明：${NC}$(pwd)/deploy/部署说明.md"
echo
echo -e "${YELLOW}📊 构建统计：${NC}"
echo "  后端文件：$(find deploy/backend -type f | wc -l) 个"
echo "  前端文件：$(find deploy/frontend -type f | wc -l) 个"
echo "  总大小：$(du -sh deploy | cut -f1)"
echo
echo -e "${YELLOW}🚀 下一步：${NC}"
echo "  1. 将 deploy 文件夹上传到服务器"
echo "  2. 按照部署说明配置服务器环境"
echo "  3. 运行 ./start_server.sh 启动系统"
echo
echo -e "${GREEN}✅ 准备就绪，可以部署到生产环境！${NC}"
