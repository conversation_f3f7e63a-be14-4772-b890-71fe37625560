import{d,M as m,R as u,c as f,k as p,a as g,o as _,e,s as i,l as v,w as h,q as T,n as w}from"./vue.BNx9QYep.js";import{u as C,T as V}from"./index.BHZI5pdK.js";import{_ as $}from"./_plugin-vue_export-helper.DlAUqK2U.js";const b=d({name:"404",setup(){const t=C(),s=V(),{themeConfig:n}=m(t),{isTagsViewCurrenFull:a}=m(s),r=u(),l=()=>{r.push("/")},o=f(()=>{let{isTagsview:c}=n.value;return a.value?"30px":c?"114px":"80px"});return{onGoHome:l,initTagViewHeight:o}}}),y="/assets/img404.DizYJBZK.png",k={class:"error-flex"},B={class:"left"},R={class:"left-item"},x={class:"left-item-animation left-item-title"},F={class:"left-item-animation left-item-msg"},H={class:"left-item-animation left-item-btn"};function N(t,s,n,a,r,l){const o=p("el-button");return _(),g("div",{class:"error layout-view-bg-white",style:w({height:`calc(100vh - ${t.initTagViewHeight}`})},[e("div",k,[e("div",B,[e("div",R,[s[0]||(s[0]=e("div",{class:"left-item-animation left-item-num"},"404",-1)),e("div",x,i(t.$t("message.notFound.foundTitle")),1),e("div",F,i(t.$t("message.notFound.foundMsg")),1),e("div",H,[v(o,{type:"primary",round:"",onClick:t.onGoHome},{default:h(()=>[T(i(t.$t("message.notFound.foundBtn")),1)]),_:1},8,["onClick"])])])]),s[1]||(s[1]=e("div",{class:"right"},[e("img",{src:y})],-1))])],4)}const M=$(b,[["render",N],["__scopeId","data-v-fdbbc0ab"]]);export{M as default};
