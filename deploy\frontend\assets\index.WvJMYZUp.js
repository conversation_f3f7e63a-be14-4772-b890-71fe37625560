import{d as V,a as S,o as Y,m as k,e as C,A as N,D as $,n as E,f as q,s as A,C as j,h as z,c as G,g as Q,k as W,l as x,u as B,w as b,b as Z,q as _,I as J}from"./vue.BNx9QYep.js";import{a6 as K,aq as tt,g as et,ar as st}from"./index.BHZI5pdK.js";import{_ as it}from"./_plugin-vue_export-helper.DlAUqK2U.js";const R={};R.getData=t=>new Promise((e,s)=>{let i={};rt(t).then(r=>{i.arrayBuffer=r;try{i.orientation=ct(r)}catch{i.orientation=-1}e(i)}).catch(r=>{s(r)})});function rt(t){let e=null;return new Promise((s,i)=>{if(t.src)if(/^data\:/i.test(t.src))e=ht(t.src),s(e);else if(/^blob\:/i.test(t.src)){var r=new FileReader;r.onload=function(h){e=h.target.result,s(e)},ot(t.src,function(h){r.readAsArrayBuffer(h)})}else{var o=new XMLHttpRequest;o.onload=function(){if(this.status==200||this.status===0)e=o.response,s(e);else throw"Could not load image";o=null},o.open("GET",t.src,!0),o.responseType="arraybuffer",o.send(null)}else i("img error")})}function ot(t,e){var s=new XMLHttpRequest;s.open("GET",t,!0),s.responseType="blob",s.onload=function(i){(this.status==200||this.status===0)&&e(this.response)},s.send()}function ht(t,e){e=e||t.match(/^data\:([^\;]+)\;base64,/mi)[1]||"",t=t.replace(/^data\:([^\;]+)\;base64,/gmi,"");for(var s=atob(t),i=s.length%2==0?s.length:s.length+1,r=new ArrayBuffer(i),o=new Uint16Array(r),h=0;h<i;h++)o[h]=s.charCodeAt(h);return r}function at(t,e,s){var i="",r;for(r=e,s+=e;r<s;r++)i+=String.fromCharCode(t.getUint8(r));return i}function ct(t){var e=new DataView(t),s=e.byteLength,i,r,o,h,a,c,n,l,u,p;if(e.getUint8(0)===255&&e.getUint8(1)===216)for(u=2;u<s;){if(e.getUint8(u)===255&&e.getUint8(u+1)===225){n=u;break}u++}if(n&&(r=n+4,o=n+10,at(e,r,4)==="Exif"&&(c=e.getUint16(o),a=c===18761,(a||c===19789)&&e.getUint16(o+2,a)===42&&(h=e.getUint32(o+4,a),h>=8&&(l=o+h)))),l){for(s=e.getUint16(l,a),p=0;p<s;p++)if(u=l+p*12+2,e.getUint16(u,a)===274){u+=8,i=e.getUint16(u,a);break}}return i}const nt=(t,e)=>{const s=t.__vccOpts||t;for(const[i,r]of e)s[i]=r;return s},lt=V({data:function(){return{w:0,h:0,scale:1,x:0,y:0,loading:!0,trueWidth:0,trueHeight:0,move:!0,moveX:0,moveY:0,crop:!1,cropping:!1,cropW:0,cropH:0,cropOldW:0,cropOldH:0,canChangeX:!1,canChangeY:!1,changeCropTypeX:1,changeCropTypeY:1,cropX:0,cropY:0,cropChangeX:0,cropChangeY:0,cropOffsertX:0,cropOffsertY:0,support:"",touches:[],touchNow:!1,rotate:0,isIos:!1,orientation:0,imgs:"",coe:.2,scaling:!1,scalingSet:"",coeStatus:"",isCanShow:!0,imgIsQqualCrop:!1}},props:{img:{type:[String,Blob,null,File],default:""},outputSize:{type:Number,default:1},outputType:{type:String,default:"jpeg"},info:{type:Boolean,default:!0},canScale:{type:Boolean,default:!0},autoCrop:{type:Boolean,default:!1},autoCropWidth:{type:[Number,String],default:0},autoCropHeight:{type:[Number,String],default:0},fixed:{type:Boolean,default:!1},fixedNumber:{type:Array,default:()=>[1,1]},fixedBox:{type:Boolean,default:!1},full:{type:Boolean,default:!1},canMove:{type:Boolean,default:!0},canMoveBox:{type:Boolean,default:!0},original:{type:Boolean,default:!1},centerBox:{type:Boolean,default:!1},high:{type:Boolean,default:!0},infoTrue:{type:Boolean,default:!1},maxImgSize:{type:[Number,String],default:2e3},enlarge:{type:[Number,String],default:1},preW:{type:[Number,String],default:0},mode:{type:String,default:"contain"},limitMinSize:{type:[Number,Array,String],default:()=>10,validator:function(t){return Array.isArray(t)?Number(t[0])>=0&&Number(t[1])>=0:Number(t)>=0}},fillColor:{type:String,default:""}},computed:{cropInfo(){let t={};if(t.top=this.cropOffsertY>21?"-21px":"0px",t.width=this.cropW>0?this.cropW:0,t.height=this.cropH>0?this.cropH:0,this.infoTrue){let e=1;this.high&&!this.full&&(e=window.devicePixelRatio),this.enlarge!==1&!this.full&&(e=Math.abs(Number(this.enlarge))),t.width=t.width*e,t.height=t.height*e,this.full&&(t.width=t.width/this.scale,t.height=t.height/this.scale)}return t.width=t.width.toFixed(0),t.height=t.height.toFixed(0),t},isIE(){return!!window.ActiveXObject||"ActiveXObject"in window},passive(){return this.isIE?null:{passive:!1}},isRotateRightOrLeft(){return[1,-1,3,-3].includes(this.rotate)}},watch:{img(){this.checkedImg()},imgs(t){t!==""&&this.reload()},cropW(){this.showPreview()},cropH(){this.showPreview()},cropOffsertX(){this.showPreview()},cropOffsertY(){this.showPreview()},scale(t,e){this.showPreview()},x(){this.showPreview()},y(){this.showPreview()},autoCrop(t){t&&this.goAutoCrop()},autoCropWidth(){this.autoCrop&&this.goAutoCrop()},autoCropHeight(){this.autoCrop&&this.goAutoCrop()},mode(){this.checkedImg()},rotate(){this.showPreview(),this.autoCrop?this.goAutoCrop(this.cropW,this.cropH):(this.cropW>0||this.cropH>0)&&this.goAutoCrop(this.cropW,this.cropH)}},methods:{getVersion(t){var e=navigator.userAgent.split(" "),s="";let i=0;const r=new RegExp(t,"i");for(var o=0;o<e.length;o++)r.test(e[o])&&(s=e[o]);return s?i=s.split("/")[1].split("."):i=["0","0","0"],i},checkOrientationImage(t,e,s,i){if(this.getVersion("chrome")[0]>=81)e=-1;else if(this.getVersion("safari")[0]>=605){const h=this.getVersion("version");h[0]>13&&h[1]>1&&(e=-1)}else{const h=navigator.userAgent.toLowerCase().match(/cpu iphone os (.*?) like mac os/);if(h){let a=h[1];a=a.split("_"),(a[0]>13||a[0]>=13&&a[1]>=4)&&(e=-1)}}let r=document.createElement("canvas"),o=r.getContext("2d");switch(o.save(),e){case 2:r.width=s,r.height=i,o.translate(s,0),o.scale(-1,1);break;case 3:r.width=s,r.height=i,o.translate(s/2,i/2),o.rotate(180*Math.PI/180),o.translate(-s/2,-i/2);break;case 4:r.width=s,r.height=i,o.translate(0,i),o.scale(1,-1);break;case 5:r.height=s,r.width=i,o.rotate(.5*Math.PI),o.scale(1,-1);break;case 6:r.width=i,r.height=s,o.translate(i/2,s/2),o.rotate(90*Math.PI/180),o.translate(-s/2,-i/2);break;case 7:r.height=s,r.width=i,o.rotate(.5*Math.PI),o.translate(s,-i),o.scale(-1,1);break;case 8:r.height=s,r.width=i,o.translate(i/2,s/2),o.rotate(-90*Math.PI/180),o.translate(-s/2,-i/2);break;default:r.width=s,r.height=i}o.drawImage(t,0,0,s,i),o.restore(),r.toBlob(h=>{let a=URL.createObjectURL(h);URL.revokeObjectURL(this.imgs),this.imgs=a},"image/"+this.outputType,1)},checkedImg(){if(this.img===null||this.img===""){this.imgs="",this.clearCrop();return}this.loading=!0,this.scale=1,this.rotate=0,this.imgIsQqualCrop=!1,this.clearCrop();let t=new Image;if(t.onload=()=>{if(this.img==="")return this.$emit("img-load",new Error("图片不能为空")),!1;let s=t.width,i=t.height;R.getData(t).then(r=>{this.orientation=r.orientation||1;let o=Number(this.maxImgSize);if(!this.orientation&&s<o&i<o){this.imgs=this.img;return}s>o&&(i=i/s*o,s=o),i>o&&(s=s/i*o,i=o),this.checkOrientationImage(t,this.orientation,s,i)}).catch(r=>{this.$emit("img-load","error"),this.$emit("img-load-error",r)})},t.onerror=s=>{this.$emit("img-load","error"),this.$emit("img-load-error",s)},this.img.substr(0,4)!=="data"&&(t.crossOrigin=""),this.isIE){var e=new XMLHttpRequest;e.onload=function(){var s=URL.createObjectURL(this.response);t.src=s},e.open("GET",this.img,!0),e.responseType="blob",e.send()}else t.src=this.img},startMove(t){if(t.preventDefault(),this.move&&!this.crop){if(!this.canMove)return!1;this.moveX=("clientX"in t?t.clientX:t.touches[0].clientX)-this.x,this.moveY=("clientY"in t?t.clientY:t.touches[0].clientY)-this.y,t.touches?(window.addEventListener("touchmove",this.moveImg),window.addEventListener("touchend",this.leaveImg),t.touches.length==2&&(this.touches=t.touches,window.addEventListener("touchmove",this.touchScale),window.addEventListener("touchend",this.cancelTouchScale))):(window.addEventListener("mousemove",this.moveImg),window.addEventListener("mouseup",this.leaveImg)),this.$emit("img-moving",{moving:!0,axis:this.getImgAxis()})}else this.cropping=!0,window.addEventListener("mousemove",this.createCrop),window.addEventListener("mouseup",this.endCrop),window.addEventListener("touchmove",this.createCrop),window.addEventListener("touchend",this.endCrop),this.cropOffsertX=t.offsetX?t.offsetX:t.touches[0].pageX-this.$refs.cropper.offsetLeft,this.cropOffsertY=t.offsetY?t.offsetY:t.touches[0].pageY-this.$refs.cropper.offsetTop,this.cropX="clientX"in t?t.clientX:t.touches[0].clientX,this.cropY="clientY"in t?t.clientY:t.touches[0].clientY,this.cropChangeX=this.cropOffsertX,this.cropChangeY=this.cropOffsertY,this.cropW=0,this.cropH=0},touchScale(t){t.preventDefault();let e=this.scale;var s={x:this.touches[0].clientX,y:this.touches[0].clientY},i={x:t.touches[0].clientX,y:t.touches[0].clientY},r={x:this.touches[1].clientX,y:this.touches[1].clientY},o={x:t.touches[1].clientX,y:t.touches[1].clientY},h=Math.sqrt(Math.pow(s.x-r.x,2)+Math.pow(s.y-r.y,2)),a=Math.sqrt(Math.pow(i.x-o.x,2)+Math.pow(i.y-o.y,2)),c=a-h,n=1;n=n/this.trueWidth>n/this.trueHeight?n/this.trueHeight:n/this.trueWidth,n=n>.1?.1:n;var l=n*c;if(!this.touchNow){if(this.touchNow=!0,c>0?e+=Math.abs(l):c<0&&e>Math.abs(l)&&(e-=Math.abs(l)),this.touches=t.touches,setTimeout(()=>{this.touchNow=!1},8),!this.checkoutImgAxis(this.x,this.y,e))return!1;this.scale=e}},cancelTouchScale(t){window.removeEventListener("touchmove",this.touchScale)},moveImg(t){if(t.preventDefault(),t.touches&&t.touches.length===2)return this.touches=t.touches,window.addEventListener("touchmove",this.touchScale),window.addEventListener("touchend",this.cancelTouchScale),window.removeEventListener("touchmove",this.moveImg),!1;let e="clientX"in t?t.clientX:t.touches[0].clientX,s="clientY"in t?t.clientY:t.touches[0].clientY,i,r;i=e-this.moveX,r=s-this.moveY,this.$nextTick(()=>{if(this.centerBox){let o=this.getImgAxis(i,r,this.scale),h=this.getCropAxis(),a=this.trueHeight*this.scale,c=this.trueWidth*this.scale,n,l,u,p;switch(this.rotate){case 1:case-1:case 3:case-3:n=this.cropOffsertX-this.trueWidth*(1-this.scale)/2+(a-c)/2,l=this.cropOffsertY-this.trueHeight*(1-this.scale)/2+(c-a)/2,u=n-a+this.cropW,p=l-c+this.cropH;break;default:n=this.cropOffsertX-this.trueWidth*(1-this.scale)/2,l=this.cropOffsertY-this.trueHeight*(1-this.scale)/2,u=n-c+this.cropW,p=l-a+this.cropH;break}o.x1>=h.x1&&(i=n),o.y1>=h.y1&&(r=l),o.x2<=h.x2&&(i=u),o.y2<=h.y2&&(r=p)}this.x=i,this.y=r,this.$emit("img-moving",{moving:!0,axis:this.getImgAxis()})})},leaveImg(t){window.removeEventListener("mousemove",this.moveImg),window.removeEventListener("touchmove",this.moveImg),window.removeEventListener("mouseup",this.leaveImg),window.removeEventListener("touchend",this.leaveImg),this.$emit("img-moving",{moving:!1,axis:this.getImgAxis()})},scaleImg(){this.canScale&&window.addEventListener(this.support,this.changeSize,this.passive)},cancelScale(){this.canScale&&window.removeEventListener(this.support,this.changeSize)},changeSize(t){t.preventDefault();let e=this.scale;var s=t.deltaY||t.wheelDelta,i=navigator.userAgent.indexOf("Firefox");s=i>0?s*30:s,this.isIE&&(s=-s);var r=this.coe;r=r/this.trueWidth>r/this.trueHeight?r/this.trueHeight:r/this.trueWidth;var o=r*s;o<0?e+=Math.abs(o):e>Math.abs(o)&&(e-=Math.abs(o));let h=o<0?"add":"reduce";if(h!==this.coeStatus&&(this.coeStatus=h,this.coe=.2),this.scaling||(this.scalingSet=setTimeout(()=>{this.scaling=!1,this.coe=this.coe+=.01},50)),this.scaling=!0,!this.checkoutImgAxis(this.x,this.y,e))return!1;this.scale=e},changeScale(t){let e=this.scale;t=t||1;var s=20;if(s=s/this.trueWidth>s/this.trueHeight?s/this.trueHeight:s/this.trueWidth,t=t*s,t>0?e+=Math.abs(t):e>Math.abs(t)&&(e-=Math.abs(t)),!this.checkoutImgAxis(this.x,this.y,e))return!1;this.scale=e},createCrop(t){t.preventDefault();var e="clientX"in t?t.clientX:t.touches?t.touches[0].clientX:0,s="clientY"in t?t.clientY:t.touches?t.touches[0].clientY:0;this.$nextTick(()=>{var i=e-this.cropX,r=s-this.cropY;if(i>0?(this.cropW=i+this.cropChangeX>this.w?this.w-this.cropChangeX:i,this.cropOffsertX=this.cropChangeX):(this.cropW=this.w-this.cropChangeX+Math.abs(i)>this.w?this.cropChangeX:Math.abs(i),this.cropOffsertX=this.cropChangeX+i>0?this.cropChangeX+i:0),!this.fixed)r>0?(this.cropH=r+this.cropChangeY>this.h?this.h-this.cropChangeY:r,this.cropOffsertY=this.cropChangeY):(this.cropH=this.h-this.cropChangeY+Math.abs(r)>this.h?this.cropChangeY:Math.abs(r),this.cropOffsertY=this.cropChangeY+r>0?this.cropChangeY+r:0);else{var o=this.cropW/this.fixedNumber[0]*this.fixedNumber[1];o+this.cropOffsertY>this.h?(this.cropH=this.h-this.cropOffsertY,this.cropW=this.cropH/this.fixedNumber[1]*this.fixedNumber[0],i>0?this.cropOffsertX=this.cropChangeX:this.cropOffsertX=this.cropChangeX-this.cropW):this.cropH=o,this.cropOffsertY=this.cropOffsertY}})},changeCropSize(t,e,s,i,r){t.preventDefault(),window.addEventListener("mousemove",this.changeCropNow),window.addEventListener("mouseup",this.changeCropEnd),window.addEventListener("touchmove",this.changeCropNow),window.addEventListener("touchend",this.changeCropEnd),this.canChangeX=e,this.canChangeY=s,this.changeCropTypeX=i,this.changeCropTypeY=r,this.cropX="clientX"in t?t.clientX:t.touches[0].clientX,this.cropY="clientY"in t?t.clientY:t.touches[0].clientY,this.cropOldW=this.cropW,this.cropOldH=this.cropH,this.cropChangeX=this.cropOffsertX,this.cropChangeY=this.cropOffsertY,this.fixed&&this.canChangeX&&this.canChangeY&&(this.canChangeY=0),this.$emit("change-crop-size",{width:this.cropW,height:this.cropH})},changeCropNow(t){t.preventDefault();var e="clientX"in t?t.clientX:t.touches?t.touches[0].clientX:0,s="clientY"in t?t.clientY:t.touches?t.touches[0].clientY:0;let i=this.w,r=this.h,o=0,h=0;if(this.centerBox){let n=this.getImgAxis(),l=n.x2,u=n.y2;o=n.x1>0?n.x1:0,h=n.y1>0?n.y1:0,i>l&&(i=l),r>u&&(r=u)}const[a,c]=this.checkCropLimitSize();this.$nextTick(()=>{var n=e-this.cropX,l=s-this.cropY;if(this.canChangeX&&(this.changeCropTypeX===1?this.cropOldW-n<a?(this.cropW=a,this.cropOffsertX=this.cropOldW+this.cropChangeX-o-a):this.cropOldW-n>0?(this.cropW=i-this.cropChangeX-n<=i-o?this.cropOldW-n:this.cropOldW+this.cropChangeX-o,this.cropOffsertX=i-this.cropChangeX-n<=i-o?this.cropChangeX+n:o):(this.cropW=Math.abs(n)+this.cropChangeX<=i?Math.abs(n)-this.cropOldW:i-this.cropOldW-this.cropChangeX,this.cropOffsertX=this.cropChangeX+this.cropOldW):this.changeCropTypeX===2&&(this.cropOldW+n<a?this.cropW=a:this.cropOldW+n>0?(this.cropW=this.cropOldW+n+this.cropOffsertX<=i?this.cropOldW+n:i-this.cropOffsertX,this.cropOffsertX=this.cropChangeX):(this.cropW=i-this.cropChangeX+Math.abs(n+this.cropOldW)<=i-o?Math.abs(n+this.cropOldW):this.cropChangeX-o,this.cropOffsertX=i-this.cropChangeX+Math.abs(n+this.cropOldW)<=i-o?this.cropChangeX-Math.abs(n+this.cropOldW):o))),this.canChangeY&&(this.changeCropTypeY===1?this.cropOldH-l<c?(this.cropH=c,this.cropOffsertY=this.cropOldH+this.cropChangeY-h-c):this.cropOldH-l>0?(this.cropH=r-this.cropChangeY-l<=r-h?this.cropOldH-l:this.cropOldH+this.cropChangeY-h,this.cropOffsertY=r-this.cropChangeY-l<=r-h?this.cropChangeY+l:h):(this.cropH=Math.abs(l)+this.cropChangeY<=r?Math.abs(l)-this.cropOldH:r-this.cropOldH-this.cropChangeY,this.cropOffsertY=this.cropChangeY+this.cropOldH):this.changeCropTypeY===2&&(this.cropOldH+l<c?this.cropH=c:this.cropOldH+l>0?(this.cropH=this.cropOldH+l+this.cropOffsertY<=r?this.cropOldH+l:r-this.cropOffsertY,this.cropOffsertY=this.cropChangeY):(this.cropH=r-this.cropChangeY+Math.abs(l+this.cropOldH)<=r-h?Math.abs(l+this.cropOldH):this.cropChangeY-h,this.cropOffsertY=r-this.cropChangeY+Math.abs(l+this.cropOldH)<=r-h?this.cropChangeY-Math.abs(l+this.cropOldH):h))),this.canChangeX&&this.fixed){var u=this.cropW/this.fixedNumber[0]*this.fixedNumber[1];u<c?(this.cropH=c,this.cropW=this.fixedNumber[0]*c/this.fixedNumber[1],this.changeCropTypeX===1&&(this.cropOffsertX=this.cropChangeX+(this.cropOldW-this.cropW))):u+this.cropOffsertY>r?(this.cropH=r-this.cropOffsertY,this.cropW=this.cropH/this.fixedNumber[1]*this.fixedNumber[0],this.changeCropTypeX===1&&(this.cropOffsertX=this.cropChangeX+(this.cropOldW-this.cropW))):this.cropH=u}if(this.canChangeY&&this.fixed){var p=this.cropH/this.fixedNumber[1]*this.fixedNumber[0];p<a?(this.cropW=a,this.cropH=this.fixedNumber[1]*a/this.fixedNumber[0],this.cropOffsertY=this.cropOldH+this.cropChangeY-this.cropH):p+this.cropOffsertX>i?(this.cropW=i-this.cropOffsertX,this.cropH=this.cropW/this.fixedNumber[0]*this.fixedNumber[1]):this.cropW=p}})},checkCropLimitSize(){let{cropW:t,cropH:e,limitMinSize:s}=this,i=new Array;return Array.isArray(s)?i=s:i=[s,s],t=parseFloat(i[0]),e=parseFloat(i[1]),[t,e]},changeCropEnd(t){window.removeEventListener("mousemove",this.changeCropNow),window.removeEventListener("mouseup",this.changeCropEnd),window.removeEventListener("touchmove",this.changeCropNow),window.removeEventListener("touchend",this.changeCropEnd)},calculateSize(t,e,s,i,r,o){const h=t/e;let a=r,c=o;return a<s&&(a=s,c=Math.ceil(a/h)),c<i&&(c=i,a=Math.ceil(c*h),a<s&&(a=s,c=Math.ceil(a/h))),a<r&&(a=r,c=Math.ceil(a/h)),c<o&&(c=o,a=Math.ceil(c*h)),{width:a,height:c}},endCrop(){this.cropW===0&&this.cropH===0&&(this.cropping=!1);let[t,e]=this.checkCropLimitSize();const{width:s,height:i}=this.fixed?this.calculateSize(this.fixedNumber[0],this.fixedNumber[1],t,e,this.cropW,this.cropH):{width:t,height:e};s>this.cropW&&(this.cropW=s,this.cropOffsertX+s>this.w&&(this.cropOffsertX=this.w-s)),i>this.cropH&&(this.cropH=i,this.cropOffsertY+i>this.h&&(this.cropOffsertY=this.h-i)),window.removeEventListener("mousemove",this.createCrop),window.removeEventListener("mouseup",this.endCrop),window.removeEventListener("touchmove",this.createCrop),window.removeEventListener("touchend",this.endCrop)},startCrop(){this.crop=!0},stopCrop(){this.crop=!1},clearCrop(){this.cropping=!1,this.cropW=0,this.cropH=0},cropMove(t){if(t.preventDefault(),!this.canMoveBox)return this.crop=!1,this.startMove(t),!1;if(t.touches&&t.touches.length===2)return this.crop=!1,this.startMove(t),this.leaveCrop(),!1;window.addEventListener("mousemove",this.moveCrop),window.addEventListener("mouseup",this.leaveCrop),window.addEventListener("touchmove",this.moveCrop),window.addEventListener("touchend",this.leaveCrop);let e="clientX"in t?t.clientX:t.touches[0].clientX,s="clientY"in t?t.clientY:t.touches[0].clientY,i,r;i=e-this.cropOffsertX,r=s-this.cropOffsertY,this.cropX=i,this.cropY=r,this.$emit("crop-moving",{moving:!0,axis:this.getCropAxis()})},moveCrop(t,e){let s=0,i=0;t&&(t.preventDefault(),s="clientX"in t?t.clientX:t.touches[0].clientX,i="clientY"in t?t.clientY:t.touches[0].clientY),this.$nextTick(()=>{let r,o,h=s-this.cropX,a=i-this.cropY;if(e&&(h=this.cropOffsertX,a=this.cropOffsertY),h<=0?r=0:h+this.cropW>this.w?r=this.w-this.cropW:r=h,a<=0?o=0:a+this.cropH>this.h?o=this.h-this.cropH:o=a,this.centerBox){let c=this.getImgAxis();r<=c.x1&&(r=c.x1),r+this.cropW>c.x2&&(r=c.x2-this.cropW),o<=c.y1&&(o=c.y1),o+this.cropH>c.y2&&(o=c.y2-this.cropH)}this.cropOffsertX=r,this.cropOffsertY=o,this.$emit("crop-moving",{moving:!0,axis:this.getCropAxis()})})},getImgAxis(t,e,s){t=t||this.x,e=e||this.y,s=s||this.scale;let i={x1:0,x2:0,y1:0,y2:0},r=this.trueWidth*s,o=this.trueHeight*s;switch(this.rotate){case 0:i.x1=t+this.trueWidth*(1-s)/2,i.x2=i.x1+this.trueWidth*s,i.y1=e+this.trueHeight*(1-s)/2,i.y2=i.y1+this.trueHeight*s;break;case 1:case-1:case 3:case-3:i.x1=t+this.trueWidth*(1-s)/2+(r-o)/2,i.x2=i.x1+this.trueHeight*s,i.y1=e+this.trueHeight*(1-s)/2+(o-r)/2,i.y2=i.y1+this.trueWidth*s;break;default:i.x1=t+this.trueWidth*(1-s)/2,i.x2=i.x1+this.trueWidth*s,i.y1=e+this.trueHeight*(1-s)/2,i.y2=i.y1+this.trueHeight*s;break}return i},getCropAxis(){let t={x1:0,x2:0,y1:0,y2:0};return t.x1=this.cropOffsertX,t.x2=t.x1+this.cropW,t.y1=this.cropOffsertY,t.y2=t.y1+this.cropH,t},leaveCrop(t){window.removeEventListener("mousemove",this.moveCrop),window.removeEventListener("mouseup",this.leaveCrop),window.removeEventListener("touchmove",this.moveCrop),window.removeEventListener("touchend",this.leaveCrop),this.$emit("crop-moving",{moving:!1,axis:this.getCropAxis()})},getCropChecked(t){let e=document.createElement("canvas"),s=e.getContext("2d"),i=new Image,r=this.rotate,o=this.trueWidth,h=this.trueHeight,a=this.cropOffsertX,c=this.cropOffsertY;i.onload=()=>{if(this.cropW!==0){let p=1;this.high&!this.full&&(p=window.devicePixelRatio),this.enlarge!==1&!this.full&&(p=Math.abs(Number(this.enlarge)));let m=this.cropW*p,H=this.cropH*p,g=o*this.scale*p,f=h*this.scale*p,w=(this.x-a+this.trueWidth*(1-this.scale)/2)*p,v=(this.y-c+this.trueHeight*(1-this.scale)/2)*p;switch(u(m,H),s.save(),r){case 0:this.full?(u(m/this.scale,H/this.scale),s.drawImage(i,w/this.scale,v/this.scale,g/this.scale,f/this.scale)):s.drawImage(i,w,v,g,f);break;case 1:case-3:this.full?(u(m/this.scale,H/this.scale),w=w/this.scale+(g/this.scale-f/this.scale)/2,v=v/this.scale+(f/this.scale-g/this.scale)/2,s.rotate(r*90*Math.PI/180),s.drawImage(i,v,-w-f/this.scale,g/this.scale,f/this.scale)):(w=w+(g-f)/2,v=v+(f-g)/2,s.rotate(r*90*Math.PI/180),s.drawImage(i,v,-w-f,g,f));break;case 2:case-2:this.full?(u(m/this.scale,H/this.scale),s.rotate(r*90*Math.PI/180),w=w/this.scale,v=v/this.scale,s.drawImage(i,-w-g/this.scale,-v-f/this.scale,g/this.scale,f/this.scale)):(s.rotate(r*90*Math.PI/180),s.drawImage(i,-w-g,-v-f,g,f));break;case 3:case-1:this.full?(u(m/this.scale,H/this.scale),w=w/this.scale+(g/this.scale-f/this.scale)/2,v=v/this.scale+(f/this.scale-g/this.scale)/2,s.rotate(r*90*Math.PI/180),s.drawImage(i,-v-g/this.scale,w,g/this.scale,f/this.scale)):(w=w+(g-f)/2,v=v+(f-g)/2,s.rotate(r*90*Math.PI/180),s.drawImage(i,-v-g,w,g,f));break;default:this.full?(u(m/this.scale,H/this.scale),s.drawImage(i,w/this.scale,v/this.scale,g/this.scale,f/this.scale)):s.drawImage(i,w,v,g,f)}s.restore()}else{let p=o*this.scale,m=h*this.scale;switch(s.save(),r){case 0:u(p,m),s.drawImage(i,0,0,p,m);break;case 1:case-3:u(m,p),s.rotate(r*90*Math.PI/180),s.drawImage(i,0,-m,p,m);break;case 2:case-2:u(p,m),s.rotate(r*90*Math.PI/180),s.drawImage(i,-p,-m,p,m);break;case 3:case-1:u(m,p),s.rotate(r*90*Math.PI/180),s.drawImage(i,-p,0,p,m);break;default:u(p,m),s.drawImage(i,0,0,p,m)}s.restore()}t(e)};var n=this.img.substr(0,4);n!=="data"&&(i.crossOrigin="Anonymous"),i.src=this.imgs;const l=this.fillColor;function u(p,m){e.width=Math.round(p),e.height=Math.round(m),l&&(s.fillStyle=l,s.fillRect(0,0,e.width,e.height))}},getCropData(t){this.getCropChecked(e=>{t(e.toDataURL("image/"+this.outputType,this.outputSize))})},getCropBlob(t){this.getCropChecked(e=>{e.toBlob(s=>t(s),"image/"+this.outputType,this.outputSize)})},showPreview(){if(this.isCanShow)this.isCanShow=!1,setTimeout(()=>{this.isCanShow=!0},16);else return!1;let t=this.cropW,e=this.cropH,s=this.scale;var i={};i.div={width:`${t}px`,height:`${e}px`};let r=(this.x-this.cropOffsertX)/s,o=(this.y-this.cropOffsertY)/s,h=0;i.w=t,i.h=e,i.url=this.imgs,i.img={width:`${this.trueWidth}px`,height:`${this.trueHeight}px`,transform:`scale(${s})translate3d(${r}px, ${o}px, ${h}px)rotateZ(${this.rotate*90}deg)`},i.html=`
      <div class="show-preview" style="width: ${i.w}px; height: ${i.h}px,; overflow: hidden">
        <div style="width: ${t}px; height: ${e}px">
          <img src=${i.url} style="width: ${this.trueWidth}px; height: ${this.trueHeight}px; transform:
          scale(${s})translate3d(${r}px, ${o}px, ${h}px)rotateZ(${this.rotate*90}deg)">
        </div>
      </div>`,this.$emit("real-time",i)},reload(){let t=new Image;t.onload=()=>{this.w=parseFloat(window.getComputedStyle(this.$refs.cropper).width),this.h=parseFloat(window.getComputedStyle(this.$refs.cropper).height),this.trueWidth=t.width,this.trueHeight=t.height,this.original?this.scale=1:this.scale=this.checkedMode(),this.$nextTick(()=>{this.x=-(this.trueWidth-this.trueWidth*this.scale)/2+(this.w-this.trueWidth*this.scale)/2,this.y=-(this.trueHeight-this.trueHeight*this.scale)/2+(this.h-this.trueHeight*this.scale)/2,this.loading=!1,this.autoCrop&&this.goAutoCrop(),this.$emit("img-load","success"),setTimeout(()=>{this.showPreview()},20)})},t.onerror=()=>{this.$emit("img-load","error")},t.src=this.imgs},checkedMode(){let t=1,e=this.trueWidth,s=this.trueHeight;const i=this.mode.split(" ");switch(i[0]){case"contain":this.trueWidth>this.w&&(t=this.w/this.trueWidth),this.trueHeight*t>this.h&&(t=this.h/this.trueHeight);break;case"cover":e=this.w,t=e/this.trueWidth,s=s*t,s<this.h&&(s=this.h,t=s/this.trueHeight);break;default:try{let r=i[0];if(r.search("px")!==-1){r=r.replace("px",""),e=parseFloat(r);const o=e/this.trueWidth;let h=1,a=i[1];a.search("px")!==-1&&(a=a.replace("px",""),s=parseFloat(a),h=s/this.trueHeight),t=Math.min(o,h)}if(r.search("%")!==-1&&(r=r.replace("%",""),e=parseFloat(r)/100*this.w,t=e/this.trueWidth),i.length===2&&r==="auto"){let o=i[1];o.search("px")!==-1&&(o=o.replace("px",""),s=parseFloat(o),t=s/this.trueHeight),o.search("%")!==-1&&(o=o.replace("%",""),s=parseFloat(o)/100*this.h,t=s/this.trueHeight)}}catch{t=1}}return t},goAutoCrop(t,e){if(this.imgs===""||this.imgs===null)return;this.clearCrop(),this.cropping=!0;let s=this.w,i=this.h;if(this.centerBox){const h=Math.abs(this.rotate)%2>0;let a=(h?this.trueHeight:this.trueWidth)*this.scale,c=(h?this.trueWidth:this.trueHeight)*this.scale;s=a<s?a:s,i=c<i?c:i}var r=t||parseFloat(this.autoCropWidth),o=e||parseFloat(this.autoCropHeight);(r===0||o===0)&&(r=s*.8,o=i*.8),r=r>s?s:r,o=o>i?i:o,this.fixed&&(o=r/this.fixedNumber[0]*this.fixedNumber[1]),o>this.h&&(o=this.h,r=o/this.fixedNumber[1]*this.fixedNumber[0]),this.changeCrop(r,o)},changeCrop(t,e){if(this.centerBox){let s=this.getImgAxis();t>s.x2-s.x1&&(t=s.x2-s.x1,e=t/this.fixedNumber[0]*this.fixedNumber[1]),e>s.y2-s.y1&&(e=s.y2-s.y1,t=e/this.fixedNumber[1]*this.fixedNumber[0])}this.cropW=t,this.cropH=e,this.checkCropLimitSize(),this.$nextTick(()=>{this.cropOffsertX=(this.w-this.cropW)/2,this.cropOffsertY=(this.h-this.cropH)/2,this.centerBox&&this.moveCrop(null,!0)})},refresh(){this.img,this.imgs="",this.scale=1,this.crop=!1,this.rotate=0,this.w=0,this.h=0,this.trueWidth=0,this.trueHeight=0,this.imgIsQqualCrop=!1,this.clearCrop(),this.$nextTick(()=>{this.checkedImg()})},rotateLeft(){this.rotate=this.rotate<=-3?0:this.rotate-1},rotateRight(){this.rotate=this.rotate>=3?0:this.rotate+1},rotateClear(){this.rotate=0},checkoutImgAxis(t,e,s){t=t||this.x,e=e||this.y,s=s||this.scale;let i=!0;if(this.centerBox){let r=this.getImgAxis(t,e,s),o=this.getCropAxis();r.x1>=o.x1&&(i=!1),r.x2<=o.x2&&(i=!1),r.y1>=o.y1&&(i=!1),r.y2<=o.y2&&(i=!1),i||this.changeImgScale(r,o,s)}return i},changeImgScale(t,e,s){let i=this.trueWidth,r=this.trueHeight,o=i*s,h=r*s;if(o>=this.cropW&&h>=this.cropH)this.scale=s;else{const a=this.cropW/i,c=this.cropH/r,n=this.cropH<=r*a?a:c;this.scale=n,o=i*n,h=r*n}this.imgIsQqualCrop||(t.x1>=e.x1&&(this.isRotateRightOrLeft?this.x=e.x1-(i-o)/2-(o-h)/2:this.x=e.x1-(i-o)/2),t.x2<=e.x2&&(this.isRotateRightOrLeft?this.x=e.x1-(i-o)/2-(o-h)/2-h+this.cropW:this.x=e.x2-(i-o)/2-o),t.y1>=e.y1&&(this.isRotateRightOrLeft?this.y=e.y1-(r-h)/2-(h-o)/2:this.y=e.y1-(r-h)/2),t.y2<=e.y2&&(this.isRotateRightOrLeft?this.y=e.y2-(r-h)/2-(h-o)/2-o:this.y=e.y2-(r-h)/2-h)),(o<this.cropW||h<this.cropH)&&(this.imgIsQqualCrop=!0)}},mounted(){this.support="onwheel"in document.createElement("div")?"wheel":document.onmousewheel!==void 0?"mousewheel":"DOMMouseScroll";let t=this;var e=navigator.userAgent;this.isIOS=!!e.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/),HTMLCanvasElement.prototype.toBlob||Object.defineProperty(HTMLCanvasElement.prototype,"toBlob",{value:function(s,i,r){for(var o=atob(this.toDataURL(i,r).split(",")[1]),h=o.length,a=new Uint8Array(h),c=0;c<h;c++)a[c]=o.charCodeAt(c);s(new Blob([a],{type:t.type||"image/png"}))}}),this.showPreview(),this.checkedImg()},unmounted(){window.removeEventListener("mousemove",this.moveCrop),window.removeEventListener("mouseup",this.leaveCrop),window.removeEventListener("touchmove",this.moveCrop),window.removeEventListener("touchend",this.leaveCrop),this.cancelScale()}}),pt={key:0,class:"cropper-box"},ut=["src"],dt={class:"cropper-view-box"},gt=["src"],ft={key:1};function mt(t,e,s,i,r,o){return Y(),S("div",{class:"vue-cropper",ref:"cropper",onMouseover:e[28]||(e[28]=(...h)=>t.scaleImg&&t.scaleImg(...h)),onMouseout:e[29]||(e[29]=(...h)=>t.cancelScale&&t.cancelScale(...h))},[t.imgs?(Y(),S("div",pt,[N(C("div",{class:"cropper-box-canvas",style:E({width:t.trueWidth+"px",height:t.trueHeight+"px",transform:"scale("+t.scale+","+t.scale+") translate3d("+t.x/t.scale+"px,"+t.y/t.scale+"px,0)rotateZ("+t.rotate*90+"deg)"})},[C("img",{src:t.imgs,alt:"cropper-img",ref:"cropperImg"},null,8,ut)],4),[[$,!t.loading]])])):k("",!0),C("div",{class:q(["cropper-drag-box",{"cropper-move":t.move&&!t.crop,"cropper-crop":t.crop,"cropper-modal":t.cropping}]),onMousedown:e[0]||(e[0]=(...h)=>t.startMove&&t.startMove(...h)),onTouchstart:e[1]||(e[1]=(...h)=>t.startMove&&t.startMove(...h))},null,34),N(C("div",{class:"cropper-crop-box",style:E({width:t.cropW+"px",height:t.cropH+"px",transform:"translate3d("+t.cropOffsertX+"px,"+t.cropOffsertY+"px,0)"})},[C("span",dt,[C("img",{style:E({width:t.trueWidth+"px",height:t.trueHeight+"px",transform:"scale("+t.scale+","+t.scale+") translate3d("+(t.x-t.cropOffsertX)/t.scale+"px,"+(t.y-t.cropOffsertY)/t.scale+"px,0)rotateZ("+t.rotate*90+"deg)"}),src:t.imgs,alt:"cropper-img"},null,12,gt)]),C("span",{class:"cropper-face cropper-move",onMousedown:e[2]||(e[2]=(...h)=>t.cropMove&&t.cropMove(...h)),onTouchstart:e[3]||(e[3]=(...h)=>t.cropMove&&t.cropMove(...h))},null,32),t.info?(Y(),S("span",{key:0,class:"crop-info",style:E({top:t.cropInfo.top})},A(t.cropInfo.width)+" × "+A(t.cropInfo.height),5)):k("",!0),t.fixedBox?k("",!0):(Y(),S("span",ft,[C("span",{class:"crop-line line-w",onMousedown:e[4]||(e[4]=h=>t.changeCropSize(h,!1,!0,0,1)),onTouchstart:e[5]||(e[5]=h=>t.changeCropSize(h,!1,!0,0,1))},null,32),C("span",{class:"crop-line line-a",onMousedown:e[6]||(e[6]=h=>t.changeCropSize(h,!0,!1,1,0)),onTouchstart:e[7]||(e[7]=h=>t.changeCropSize(h,!0,!1,1,0))},null,32),C("span",{class:"crop-line line-s",onMousedown:e[8]||(e[8]=h=>t.changeCropSize(h,!1,!0,0,2)),onTouchstart:e[9]||(e[9]=h=>t.changeCropSize(h,!1,!0,0,2))},null,32),C("span",{class:"crop-line line-d",onMousedown:e[10]||(e[10]=h=>t.changeCropSize(h,!0,!1,2,0)),onTouchstart:e[11]||(e[11]=h=>t.changeCropSize(h,!0,!1,2,0))},null,32),C("span",{class:"crop-point point1",onMousedown:e[12]||(e[12]=h=>t.changeCropSize(h,!0,!0,1,1)),onTouchstart:e[13]||(e[13]=h=>t.changeCropSize(h,!0,!0,1,1))},null,32),C("span",{class:"crop-point point2",onMousedown:e[14]||(e[14]=h=>t.changeCropSize(h,!1,!0,0,1)),onTouchstart:e[15]||(e[15]=h=>t.changeCropSize(h,!1,!0,0,1))},null,32),C("span",{class:"crop-point point3",onMousedown:e[16]||(e[16]=h=>t.changeCropSize(h,!0,!0,2,1)),onTouchstart:e[17]||(e[17]=h=>t.changeCropSize(h,!0,!0,2,1))},null,32),C("span",{class:"crop-point point4",onMousedown:e[18]||(e[18]=h=>t.changeCropSize(h,!0,!1,1,0)),onTouchstart:e[19]||(e[19]=h=>t.changeCropSize(h,!0,!1,1,0))},null,32),C("span",{class:"crop-point point5",onMousedown:e[20]||(e[20]=h=>t.changeCropSize(h,!0,!1,2,0)),onTouchstart:e[21]||(e[21]=h=>t.changeCropSize(h,!0,!1,2,0))},null,32),C("span",{class:"crop-point point6",onMousedown:e[22]||(e[22]=h=>t.changeCropSize(h,!0,!0,1,2)),onTouchstart:e[23]||(e[23]=h=>t.changeCropSize(h,!0,!0,1,2))},null,32),C("span",{class:"crop-point point7",onMousedown:e[24]||(e[24]=h=>t.changeCropSize(h,!1,!0,0,2)),onTouchstart:e[25]||(e[25]=h=>t.changeCropSize(h,!1,!0,0,2))},null,32),C("span",{class:"crop-point point8",onMousedown:e[26]||(e[26]=h=>t.changeCropSize(h,!0,!0,2,2)),onTouchstart:e[27]||(e[27]=h=>t.changeCropSize(h,!0,!0,2,2))},null,32)]))],4),[[$,t.cropping]])],544)}const wt=nt(lt,[["render",mt],["__scopeId","data-v-a742df44"]]),vt={__name:"index",props:{modelValue:{type:Boolean,default:!1,required:!0}},emits:["uploadImg"],setup(t,{expose:e,emit:s}){const i=K(),{proxy:r}=j(),o=z(!1),h=z("修改头像"),a=s,c=t,n=G({get(){return c.modelValue},set(y){a("update:modelValue",y)}}),l=Q({img:i.userInfos.avatar||tt,fileName:"",autoCrop:!0,autoCropWidth:200,autoCropHeight:200,fixedBox:!0,outputType:"png"});function u(){n.value=!0}function p(){J(()=>{o.value=!0})}function m(){}function H(){r.$refs.cropper.rotateLeft()}function g(){r.$refs.cropper.rotateRight()}function f(y){if(y.type.indexOf("image/")==-1)r.$modal.msgError("文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。");else{const d=new FileReader;d.readAsDataURL(y),d.onload=()=>{l.img=d.result,l.fileName=y.name}}}function w(){r.$refs.cropper.getCropData(y=>{let d=new Image;d.src=y,d.onload=async()=>{let X=v(d);const O=st(X,l.fileName);a("uploadImg",O)}})}function v(y){let d=document.createElement("canvas"),X=d.getContext("2d"),O=y.width,M=y.height;return d.width=O,d.height=M,X.fillStyle="#fff",X.fillRect(0,0,d.width,d.height),X.drawImage(y,0,0,O,M),d.toDataURL("image/jpeg",.8)}function P(){l.visible=!1,l.img=i.userInfos.avatar}return e({updateAvatar:y=>{l.img=y},editCropper:u}),(y,d)=>{const X=W("el-avatar"),O=W("el-col"),M=W("el-row"),T=W("Plus"),U=W("el-icon"),L=W("el-button"),D=W("el-upload"),F=W("el-dialog");return Y(),S("div",{class:"user-info-head",onClick:d[4]||(d[4]=I=>u())},[x(X,{size:100,src:B(et)(l.img)},null,8,["src"]),x(F,{title:h.value,modelValue:n.value,"onUpdate:modelValue":d[3]||(d[3]=I=>n.value=I),width:"600px","append-to-body":"",onOpened:p,onClose:P},{default:b(()=>[x(M,null,{default:b(()=>[x(O,{class:"flex justify-center"},{default:b(()=>[o.value?(Y(),Z(B(wt),{key:0,ref:"cropper",img:l.img,info:!0,autoCrop:l.autoCrop,autoCropWidth:l.autoCropWidth,autoCropHeight:l.autoCropHeight,fixedBox:l.fixedBox,outputType:l.outputType,onRealTime:y.realTime,centerBox:!0,class:"cropper"},null,8,["img","autoCrop","autoCropWidth","autoCropHeight","fixedBox","outputType","onRealTime"])):k("",!0)]),_:1})]),_:1}),d[7]||(d[7]=C("br",null,null,-1)),x(M,{class:"flex justify-center"},{default:b(()=>[x(O,{lg:2,md:2},{default:b(()=>[x(D,{action:"#","http-request":m,"show-file-list":!1,"before-upload":f},{default:b(()=>[x(L,{type:"success"},{default:b(()=>[d[5]||(d[5]=_(" 选择 ")),x(U,{class:"el-icon--right"},{default:b(()=>[x(T)]),_:1})]),_:1,__:[5]})]),_:1})]),_:1}),x(O,{lg:{span:1,offset:2},md:2},{default:b(()=>[x(L,{icon:"RefreshLeft",onClick:d[0]||(d[0]=I=>H())})]),_:1}),x(O,{lg:{span:1,offset:2},md:2},{default:b(()=>[x(L,{icon:"RefreshRight",onClick:d[1]||(d[1]=I=>g())})]),_:1}),x(O,{lg:{span:2,offset:2},md:2},{default:b(()=>[x(L,{type:"primary",onClick:d[2]||(d[2]=I=>w())},{default:b(()=>d[6]||(d[6]=[_("更新头像")])),_:1,__:[6]})]),_:1})]),_:1})]),_:1,__:[7]},8,["title","modelValue"])])}}},Ot=it(vt,[["__scopeId","data-v-f79b9fec"]]);export{Ot as default};
