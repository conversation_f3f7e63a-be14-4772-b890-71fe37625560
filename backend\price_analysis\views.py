from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from datetime import datetime
from django.db import connections
import logging

logger = logging.getLogger(__name__)

@api_view(['POST'])
@permission_classes([AllowAny])
def price_analysis_query(request):
    """
    价格分析查询接口 - 使用真实SQL查询
    """
    try:
        # 获取查询参数
        data = request.data
        query_year = data.get('year', datetime.now().year)
        query_month = data.get('month', datetime.now().month)
        deviation_threshold = data.get('threshold', 0.2)
        company_filter = data.get('company', '')
        material_category_filter = data.get('materialCategory', '')
        
        # 使用简化但正确的价格分析SQL查询
        sql_query = f"""
        -- 简化版价格分析查询，减少复杂度但保持功能完整
        WITH BaseData AS (
            SELECT
                org_l.FNAME AS 公司简称,
                m.FNUMBER AS 物料编码,
                m_l.FNAME AS 物料名称,
                ISNULL(m_l.FSPECIFICATION, '') AS 规格型号,
                mc_l.FNAME AS 物料类别,
                ISNULL(mg_l.FNAME, '') AS 物料组别,
                c_l.FNAME AS 客户名称,
                ROUND(f.FALLAMOUNT / NULLIF(e.FBASEUNITQTY, 0), 4) AS 单价,
                e.FBASEUNITQTY AS 基础数量,
                f.FALLAMOUNT AS 价税合计
            FROM T_SAL_ORDER o WITH (NOLOCK)
            INNER JOIN T_SAL_ORDERENTRY e WITH (NOLOCK) ON o.FID = e.FID
            INNER JOIN T_SAL_ORDERENTRY_F f WITH (NOLOCK) ON e.FENTRYID = f.FENTRYID
            INNER JOIN T_BD_MATERIAL m WITH (NOLOCK) ON e.FMATERIALID = m.FMATERIALID
            INNER JOIN T_BD_MATERIAL_L m_l WITH (NOLOCK) ON m.FMATERIALID = m_l.FMATERIALID AND m_l.FLOCALEID = 2052
            INNER JOIN T_BD_MATERIALBASE mb WITH (NOLOCK) ON m.FMATERIALID = mb.FMATERIALID
            INNER JOIN T_BD_MATERIALCATEGORY mc WITH (NOLOCK) ON mb.FCATEGORYID = mc.FCATEGORYID
            INNER JOIN T_BD_MATERIALCATEGORY_L mc_l WITH (NOLOCK) ON mc.FCATEGORYID = mc_l.FCATEGORYID AND mc_l.FLOCALEID = 2052
            LEFT JOIN T_BD_MATERIALGROUP mg WITH (NOLOCK) ON m.FMATERIALGROUP = mg.FID
            LEFT JOIN T_BD_MATERIALGROUP_L mg_l WITH (NOLOCK) ON mg.FID = mg_l.FID AND mg_l.FLocaleID = 2052
            INNER JOIN T_BD_CUSTOMER c WITH (NOLOCK) ON o.FCUSTID = c.FCUSTID
            INNER JOIN T_BD_CUSTOMER_L c_l WITH (NOLOCK) ON c.FCUSTID = c_l.FCUSTID AND c_l.FLOCALEID = 2052
            INNER JOIN T_ORG_ORGANIZATIONS org WITH (NOLOCK) ON o.FSALEORGID = org.FORGID
            INNER JOIN T_ORG_ORGANIZATIONS_L org_l WITH (NOLOCK) ON org.FORGID = org_l.FORGID AND org_l.FLOCALEID = 2052
            WHERE o.FDATE >= '{query_year}-{query_month:02d}-01'
                AND o.FDATE < DATEADD(MONTH, 1, '{query_year}-{query_month:02d}-01')
                AND org_l.FNAME IN (
                    '江苏华绿生物科技集团股份有限公司',
                    '江苏华骏生物科技有限公司',
                    '江苏华蕈生物科技有限公司',
                    '泗阳华盛生物科技有限公司',
                    '泗阳华茂生物科技有限公司',
                    '浙江华实生物科技有限公司'
                )
                {f"AND org_l.FNAME = '{company_filter}'" if company_filter else ""}
                {f"AND m.FNUMBER LIKE '{material_category_filter}%'" if material_category_filter else ""}
                AND ISNULL(e.F_JSHL_CHECKBOX_QTR, '0') != '1'
                AND e.FBASEUNITQTY > 0
                AND f.FALLAMOUNT > 0
        )

        SELECT TOP 500
            cd.公司简称,
            cd.物料编码,
            cd.物料名称,
            cd.规格型号,
            cd.物料类别,
            cd.物料组别,
            cd.客户名称,
            cd.客户交易次数 AS 交易次数,
            CAST(ROUND(cd.客户购买总量, 2) AS DECIMAL(18,2)) AS 购买总量,
            CAST(ROUND(cd.客户购买总额, 2) AS DECIMAL(18,2)) AS 购买总额,
            CAST(ROUND(cd.客户平均单价, 2) AS DECIMAL(18,2)) AS 客户平均单价,
            CAST(ROUND(ma.市场平均单价, 2) AS DECIMAL(18,2)) AS 市场平均单价,
            ma.市场总客户数 AS 市场客户数,
            ma.市场总交易次数 AS 市场交易次数,
            CAST(ROUND(cd.客户平均单价 - ma.市场平均单价, 2) AS DECIMAL(18,2)) AS 价格差异,
            CASE
                WHEN ma.市场平均单价 > 0 AND (cd.客户平均单价 - ma.市场平均单价) >= 0 THEN
                    '+' + CAST(CAST(ROUND((cd.客户平均单价 - ma.市场平均单价) / ma.市场平均单价 * 100, 2) AS DECIMAL(10,2)) AS VARCHAR(10)) + '%'
                WHEN ma.市场平均单价 > 0 THEN
                    CAST(CAST(ROUND((cd.客户平均单价 - ma.市场平均单价) / ma.市场平均单价 * 100, 2) AS DECIMAL(10,2)) AS VARCHAR(10)) + '%'
                ELSE '0.00%'
            END AS 偏离百分比,
            CASE
                WHEN ma.市场平均单价 > 0 AND ABS((cd.客户平均单价 - ma.市场平均单价) / ma.市场平均单价) >= 0.5 THEN '严重偏离(≥50%)'
                WHEN ma.市场平均单价 > 0 AND ABS((cd.客户平均单价 - ma.市场平均单价) / ma.市场平均单价) >= 0.3 THEN '高度偏离(≥30%)'
                WHEN ma.市场平均单价 > 0 AND ABS((cd.客户平均单价 - ma.市场平均单价) / ma.市场平均单价) >= 0.2 THEN '显著偏离(≥20%)'
                WHEN ma.市场平均单价 > 0 AND ABS((cd.客户平均单价 - ma.市场平均单价) / ma.市场平均单价) >= 0.1 THEN '轻微偏离(≥10%)'
                ELSE '正常范围'
            END AS 偏离等级,
            CASE
                WHEN cd.客户平均单价 > ma.市场平均单价 THEN '高于市场价'
                WHEN cd.客户平均单价 < ma.市场平均单价 THEN '低于市场价'
                ELSE '等于市场价'
            END AS 偏离方向,
            CASE
                WHEN cd.客户购买总额 >= 100000 THEN '高价值客户'
                WHEN cd.客户购买总额 >= 50000 THEN '重要客户'
                WHEN cd.客户购买总额 >= 10000 THEN '一般客户'
                ELSE '小客户'
            END AS 客户价值等级,
            CASE
                WHEN ma.市场平均单价 > 0 AND cd.客户平均单价 < ma.市场平均单价 * 0.7 THEN '价格过低风险'
                WHEN ma.市场平均单价 > 0 AND cd.客户平均单价 > ma.市场平均单价 * 1.5 THEN '价格过高风险'
                ELSE '正常'
            END AS 风险提示,
            cd.客户购买总额 AS 原始购买总额,
            CASE
                WHEN ma.市场平均单价 > 0 THEN (cd.客户平均单价 - ma.市场平均单价) / ma.市场平均单价
                ELSE 0
            END AS 原始偏离比例
        FROM (
            SELECT
                公司简称, 物料编码, 物料名称, 规格型号, 物料类别, 物料组别, 客户名称,
                COUNT(*) AS 客户交易次数,
                SUM(基础数量) AS 客户购买总量,
                SUM(价税合计) AS 客户购买总额,
                ROUND(AVG(单价), 4) AS 客户平均单价
            FROM BaseData
            GROUP BY 公司简称, 物料编码, 物料名称, 规格型号, 物料类别, 物料组别, 客户名称
        ) cd
        INNER JOIN (
            SELECT 物料编码, 物料名称, 物料类别,
                   COUNT(*) AS 市场总交易次数,
                   COUNT(DISTINCT 客户名称) AS 市场总客户数,
                   ROUND(AVG(单价), 4) AS 市场平均单价
            FROM BaseData
            GROUP BY 物料编码, 物料名称, 物料类别
        ) ma ON cd.物料编码 = ma.物料编码
        WHERE ma.市场平均单价 > 0
            AND ABS((cd.客户平均单价 - ma.市场平均单价) / ma.市场平均单价) >= {deviation_threshold}
            AND ma.市场总客户数 >= 2
        ORDER BY ABS((cd.客户平均单价 - ma.市场平均单价) / ma.市场平均单价) DESC
        """

        # 连接SQL Server数据库并执行查询
        with connections['sqlserver'].cursor() as cursor:
            cursor.execute(sql_query)
            columns = [col[0] for col in cursor.description]
            results = cursor.fetchall()

            # 转换查询结果为字典列表
            filtered_data = []
            for row in results:
                row_dict = dict(zip(columns, row))
                filtered_data.append(row_dict)
        
        # 计算统计数据
        high_risk_customers = len([
            item for item in filtered_data
            if abs(item.get('原始偏离比例', 0)) >= 0.5 or '高价值客户' in str(item.get('客户价值等级', ''))
        ])
        
        material_count = len(set(item.get('物料编码', '') for item in filtered_data))
        total_amount = sum(item.get('原始购买总额', 0) for item in filtered_data)
        
        # 构建响应数据
        response_data = {
            'code': 200,
            'message': '查询成功',
            'data': {
                'statistics': {
                    'abnormalCustomers': len(filtered_data),
                    'highRiskCustomers': high_risk_customers,
                    'materialCount': material_count,
                    'totalAmount': total_amount
                },
                'list': filtered_data
            }
        }
        
        return Response(response_data)
        
    except Exception as e:
        logger.error(f"价格分析查询失败: {str(e)}")
        return Response({
            'code': 500,
            'message': f'查询失败: {str(e)}',
            'data': {
                'statistics': {
                    'abnormalCustomers': 0,
                    'highRiskCustomers': 0,
                    'materialCount': 0,
                    'totalAmount': 0
                },
                'list': []
            }
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([AllowAny])
def price_analysis_export(request):
    """
    价格分析数据导出Excel接口
    """
    try:
        import pandas as pd
        from django.http import HttpResponse
        import io

        # 获取查询参数
        data = request.data
        query_year = data.get('year', datetime.now().year)
        query_month = data.get('month', datetime.now().month)
        deviation_threshold = data.get('threshold', 0.2)
        company_filter = data.get('company', '')
        material_category_filter = data.get('materialCategory', '')

        # 直接调用查询接口获取数据，避免重复SQL逻辑
        from django.test import RequestFactory
        import json

        factory = RequestFactory()
        post_request = factory.post('/api/price-analysis/query/',
            json.dumps({
                'year': query_year,
                'month': query_month,
                'threshold': deviation_threshold,
                'company': company_filter,
                'materialCategory': material_category_filter
            }),
            content_type='application/json'
        )

        # 调用查询接口
        query_response = price_analysis_query(post_request)
        query_data = query_response.data

        if query_data['code'] != 200:
            return Response({
                'code': 500,
                'message': f'数据查询失败: {query_data.get("message", "未知错误")}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        filtered_data = query_data['data']['list']

        if not filtered_data:
            return Response({
                'code': 404,
                'message': '没有找到符合条件的数据',
                'data': None
            })

        # 转换为DataFrame
        df = pd.DataFrame(filtered_data)

        # 创建Excel文件
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            # 写入数据
            df.to_excel(writer, sheet_name='价格分析报告', index=False)

            # 获取工作表
            worksheet = writer.sheets['价格分析报告']

            # 设置列宽
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width

        output.seek(0)

        # 生成文件名
        filename = f"价格分析报告_{query_year}年{query_month}月_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        # 返回Excel文件
        response = HttpResponse(
            output.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        return response

    except Exception as e:
        logger.error(f"价格分析导出失败: {str(e)}")
        return Response({
            'code': 500,
            'message': f'导出失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
