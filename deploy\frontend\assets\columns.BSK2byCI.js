const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/aside.PpDlMa_w.js","assets/index.BHZI5pdK.js","assets/vue.BNx9QYep.js","assets/index.Dg-OhEXY.css","assets/header.DFPmrsWF.js","assets/main.6V4YA0vm.js","assets/columnsAside.Dm3Es6LR.js","assets/_plugin-vue_export-helper.DlAUqK2U.js","assets/columnsAside.czFKTVW3.css"])))=>i.map(i=>d[i]);
import{u as C,_ as o}from"./index.BHZI5pdK.js";import{d as p,h as _,Q as E,M as k,j as w,v as i,k as f,b as A,o as L,w as r,l as e,u as t,P as a,I as M}from"./vue.BNx9QYep.js";const P=p({name:"layoutColumns"}),D=p({...P,setup(S){const d=a(()=>o(()=>import("./aside.PpDlMa_w.js"),__vite__mapDeps([0,1,2,3]))),m=a(()=>o(()=>import("./header.DFPmrsWF.js"),__vite__mapDeps([4,1,2,3]))),y=a(()=>o(()=>import("./main.6V4YA0vm.js"),__vite__mapDeps([5,1,2,3]))),R=a(()=>o(()=>import("./columnsAside.Dm3Es6LR.js"),__vite__mapDeps([6,2,1,3,7,8]))),s=_(""),n=_(),v=E(),T=C(),{themeConfig:h}=k(T),l=()=>{s.value.update(),n.value.layoutMainScrollbarRef.update()},c=()=>{M(()=>{setTimeout(()=>{l(),s.value.wrapRef.scrollTop=0,n.value.layoutMainScrollbarRef.wrapRef.scrollTop=0},500)})};return w(()=>{c()}),i(()=>v.path,()=>{c()}),i(h,()=>{l()},{deep:!0}),(I,V)=>{const b=f("el-scrollbar"),u=f("el-container");return L(),A(u,{class:"layout-container"},{default:r(()=>[e(t(R)),e(u,{class:"layout-columns-warp layout-container-view h100"},{default:r(()=>[e(t(d)),e(b,{ref_key:"layoutScrollbarRef",ref:s,class:"layout-backtop"},{default:r(()=>[e(t(m)),e(t(y),{ref_key:"layoutMainRef",ref:n},null,512)]),_:1},512)]),_:1})]),_:1})}}});export{D as default};
