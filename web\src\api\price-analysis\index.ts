import { request } from '/@/utils/service';

/**
 * 价格分析API接口
 */

// API路径前缀
export const apiPrefix = '/api/price-analysis/';

/**
 * 价格分析查询
 * @param params 查询参数
 */
export function priceAnalysisQuery(params: {
  year: number;
  month: number;
  threshold: number;
  company?: string;
  materialCategory?: string;
}) {
  return request({
    url: apiPrefix + 'query/',
    method: 'post',
    data: params,
  });
}

/**
 * 价格分析数据导出
 * @param params 导出参数
 */
export function priceAnalysisExport(params: {
  year: number;
  month: number;
  threshold: number;
  company?: string;
  materialCategory?: string;
}) {
  return request({
    url: apiPrefix + 'export/',
    method: 'post',
    data: params,
    responseType: 'blob',
    timeout: 60000  // 增加超时时间到60秒
  });
}

/**
 * 获取年份选项
 */
export function getYearOptions() {
  const currentYear = new Date().getFullYear();
  const years = [];
  for (let i = currentYear; i >= currentYear - 5; i--) {
    years.push(i);
  }
  return years;
}

/**
 * 获取月份选项
 */
export function getMonthOptions() {
  return [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
}

/**
 * 格式化货币
 */
export function formatCurrency(amount: number): string {
  if (!amount) return '¥0.00';
  return `¥${Number(amount).toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`;
}

/**
 * 获取偏离标签类型
 */
export function getDeviationTagType(deviation: string): string {
  const value = parseFloat(deviation.replace('%', '').replace('+', ''));
  if (Math.abs(value) >= 50) return 'danger';
  if (Math.abs(value) >= 30) return 'warning';
  if (Math.abs(value) >= 20) return 'info';
  return 'success';
}

/**
 * 获取风险标签类型
 */
export function getRiskTagType(risk: string): string {
  if (risk.includes('严重')) return 'danger';
  if (risk.includes('高度')) return 'danger';
  if (risk.includes('显著')) return 'warning';
  if (risk.includes('轻微')) return 'info';
  return 'success';
}
