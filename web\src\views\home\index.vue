<template>
  <div class="home-container">
    <el-card class="welcome-card">
      <template #header>
        <div class="card-header">
          <el-icon size="24" color="#409EFF">
            <House />
          </el-icon>
          <span class="title">欢迎使用华绿数据分析系统</span>
        </div>
      </template>

      <div class="welcome-content">
        <h2>数据驱动决策，智能分析未来</h2>
        <p>实时监控销售数据，深度分析价格趋势，助力企业科学决策</p>
        
        <div class="quick-actions">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-card class="action-card" shadow="hover" @click="goToDataAnalysis">
                <div class="action-content">
                  <el-icon size="48" color="#409EFF">
                    <DataAnalysis />
                  </el-icon>
                  <h3>数据分析</h3>
                  <p>查看销售数据概览和趋势分析</p>
                </div>
              </el-card>
            </el-col>
            
            <el-col :span="8">
              <el-card class="action-card" shadow="hover" @click="goToPriceAnalysis">
                <div class="action-content">
                  <el-icon size="48" color="#67C23A">
                    <PriceTag />
                  </el-icon>
                  <h3>价格分析</h3>
                  <p>分析客户价格偏离和异常情况</p>
                </div>
              </el-card>
            </el-col>
            
            <el-col :span="8">
              <el-card class="action-card" shadow="hover">
                <div class="action-content">
                  <el-icon size="48" color="#E6A23C">
                    <TrendCharts />
                  </el-icon>
                  <h3>报表中心</h3>
                  <p>生成各类业务报表和统计图表</p>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <div class="system-info">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <h4>系统特色</h4>
                <ul>
                  <li>实时数据同步，确保数据准确性</li>
                  <li>多维度分析，支持灵活筛选</li>
                  <li>智能异常检测，及时发现问题</li>
                  <li>可视化图表，直观展示趋势</li>
                </ul>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <h4>使用指南</h4>
                <ul>
                  <li>点击"数据分析"查看销售概览</li>
                  <li>使用"价格分析"监控价格异常</li>
                  <li>支持数据导出和报表生成</li>
                  <li>可按公司、时间等维度筛选</li>
                </ul>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import {
  House,
  DataAnalysis,
  PriceTag,
  TrendCharts
} from '@element-plus/icons-vue'

const router = useRouter()

// 跳转到数据分析页面
const goToDataAnalysis = () => {
  router.push('/data-analysis')
}

// 跳转到价格分析页面
const goToPriceAnalysis = () => {
  router.push('/price-analysis')
}
</script>

<style scoped>
.home-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: calc(100vh - 84px);
}

.welcome-card {
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  align-items: center;
}

.card-header .title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
  margin-left: 10px;
}

.welcome-content {
  text-align: center;
}

.welcome-content h2 {
  color: #303133;
  margin-bottom: 10px;
  font-size: 28px;
}

.welcome-content p {
  color: #606266;
  margin-bottom: 40px;
  font-size: 16px;
}

.quick-actions {
  margin-bottom: 40px;
}

.action-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
}

.action-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.action-content {
  text-align: center;
  padding: 20px;
}

.action-content h3 {
  color: #303133;
  margin: 15px 0 10px 0;
  font-size: 18px;
}

.action-content p {
  color: #909399;
  margin: 0;
  font-size: 14px;
}

.system-info {
  text-align: left;
  margin-top: 40px;
}

.info-item h4 {
  color: #303133;
  margin-bottom: 15px;
  font-size: 16px;
  border-bottom: 2px solid #409EFF;
  padding-bottom: 5px;
}

.info-item ul {
  list-style: none;
  padding: 0;
}

.info-item li {
  color: #606266;
  margin-bottom: 8px;
  padding-left: 20px;
  position: relative;
}

.info-item li::before {
  content: '•';
  color: #409EFF;
  position: absolute;
  left: 0;
  font-weight: bold;
}
</style>
