import{R as m,c as p}from"./api.CRWbL0zW.js";import{R as u}from"./RoleUsersStores.CCf4JEgz.js";import{e as d}from"./index.BHZI5pdK.js";import{d as _,k as c,b as f,o as i,u as o}from"./vue.BNx9QYep.js";const C=_({__name:"RoleUsers",setup(R){const r=m(),t=u(),l=(n,e,a)=>{p(r.$state.roleId,{direction:e,movedKeys:a}).then(s=>{r.set_state(s.data),d({message:s.msg,type:"success"})})};return(n,e)=>{const a=c("el-transfer");return i(),f(a,{modelValue:o(t).$state.right_users,"onUpdate:modelValue":e[0]||(e[0]=s=>o(t).$state.right_users=s),filterable:"",titles:["未授权用户","已授权用户"],data:o(t).$state.all_users,props:{key:"id",label:"name"},onChange:l},null,8,["modelValue","data"])}}});export{C as default};
