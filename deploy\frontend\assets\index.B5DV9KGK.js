const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/account.B3PNj4W4.js","assets/vue.BNx9QYep.js","assets/index.BHZI5pdK.js","assets/index.Dg-OhEXY.css","assets/formatTime.in1fXasu.js","assets/api.DfN45YxI.js","assets/md5.DLPczxzP.js","assets/_plugin-vue_export-helper.DlAUqK2U.js","assets/account.Dyggtdcj.css","assets/changePwd.8W6dKNn8.js","assets/changePwd.CnwGxupR.css"])))=>i.map(i=>d[i]);
import{a6 as I,u as T,O as z,ah as m,Q as L,g as f,_ as p}from"./index.BHZI5pdK.js";import{d as k,M as c,g as R,v as D,c as i,j as U,k as v,a as _,o as n,e,m as h,s as M,u as s,l as u,w as d,b,P as w,q as y,F as O}from"./vue.BNx9QYep.js";import{l as F}from"./favicon.nG7g_iy4.js";import{_ as $}from"./_plugin-vue_export-helper.DlAUqK2U.js";const C="/assets/login-bg.DC65RvUL.png",j={class:"login-container flex z-10"},q={class:"login-right flex z-10"},Q={class:"login-right-warp flex-margin"},G={class:"login-right-warp-mian"},H={class:"login-right-warp-main-title"},J={class:"login-right-warp-main-form"},K={key:0},W={class:"login-authorization z-10"},X={class:"la-other",style:{"margin-top":"5px"}},Y=["href"],Z=["href"],ee=["href"],te={key:0},oe=["src"],se=k({name:"loginIndex"}),ae=k({...se,setup(ne){const x=w(()=>p(()=>import("./account.B3PNj4W4.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8]))),N=w(()=>p(()=>import("./changePwd.8W6dKNn8.js"),__vite__mapDeps([9,1,2,3,4,5,6,7,10]))),{userInfos:r}=c(I()),A=T(),{themeConfig:V}=c(A),a=R({tabsActiveName:"account",isScan:!1});D(()=>r.value.pwd_change_count,l=>{l===0?a.tabsActiveName="changePwd":a.tabsActiveName="account"},{deep:!0,immediate:!0}),i(()=>V.value);const P=z(),{systemConfig:B}=c(P),t=i(()=>B.value);return i(()=>m.isEmpty(t.value["login.site_logo"])?F:t.value["login.site_logo"]),i(()=>{if(!m.isEmpty(t.value["login.login_background"]))return t.value["login.login_background"]}),U(()=>{L.done()}),(l,o)=>{const g=v("el-tab-pane"),E=v("el-tabs");return n(),_(O,null,[e("div",j,[o[4]||(o[4]=e("div",{class:"login-left"},null,-1)),e("div",q,[e("div",Q,[e("div",G,[e("div",H,M(s(r).pwd_change_count===0?"初次登录修改密码":"欢迎登录"),1),e("div",J,[a.isScan?h("",!0):(n(),_("div",K,[u(E,{modelValue:a.tabsActiveName,"onUpdate:modelValue":o[0]||(o[0]=S=>a.tabsActiveName=S)},{default:d(()=>[s(r).pwd_change_count===0?(n(),b(g,{key:0,label:l.$t("message.label.changePwd"),name:"changePwd"},{default:d(()=>[u(s(N))]),_:1},8,["label"])):(n(),b(g,{key:1,label:l.$t("message.label.one1"),name:"account"},{default:d(()=>[u(s(x))]),_:1},8,["label"]))]),_:1},8,["modelValue"])]))])])])]),e("div",W,[o[3]||(o[3]=e("p",null,null,-1)),e("p",X,[e("a",{href:t.value["login.help_url"]?t.value["login.help_url"]:"#",target:"_blank"},"帮助",8,Y),o[1]||(o[1]=y(" | ")),e("a",{href:t.value["login.privacy_url"]?s(f)(t.value["login.privacy_url"]):"#"},"隐私",8,Z),o[2]||(o[2]=y(" | ")),e("a",{href:t.value["login.clause_url"]?s(f)(t.value["login.clause_url"]):"#"},"条款",8,ee)])])]),s(C)?(n(),_("div",te,[e("img",{src:s(C),class:"loginBg fixed inset-0 z-1 w-full h-full"},null,8,oe)])):h("",!0)],64)}}}),_e=$(ae,[["__scopeId","data-v-818edd18"]]);export{_e as default};
