import{d as r,c as s,a as c,o as n,n as a,b as g,r as f,e as S,f as z}from"./vue.BNx9QYep.js";const _=["src"],I=r({name:"svgIcon"}),x=r({...I,props:{name:{type:String},size:{type:Number,default:()=>14},color:{type:String}},setup(u){const e=u,m=["https","http","/src","/assets","data:image","/"],l=s(()=>e==null?void 0:e.name),p=s(()=>{var t;return(t=e==null?void 0:e.name)==null?void 0:t.startsWith("ele-")}),h=s(()=>m.find(t=>{var o;return(o=e.name)==null?void 0:o.startsWith(t)})),i=s(()=>`font-size: ${e.size}px;color: ${e.color};`),d=s(()=>`width: ${e.size}px;height: ${e.size}px;display: inline-block;overflow: hidden;`),v=s(()=>{const t=[];return["-webkit","-ms","-o","-moz"].forEach(y=>t.push(`${y}-filter: drop-shadow(${e.color} 30px 0);`)),`width: ${e.size}px;height: ${e.size}px;position: relative;left: -${e.size}px;${t.join("")}`});return(t,o)=>p.value?(n(),c("i",{key:0,class:"el-icon",style:a(i.value)},[(n(),g(f(l.value)))],4)):h.value?(n(),c("div",{key:1,style:a(d.value)},[S("img",{src:l.value,style:a(v.value)},null,12,_)],4)):(n(),c("i",{key:2,class:z(l.value),style:a(i.value)},null,6))}});export{x as _};
