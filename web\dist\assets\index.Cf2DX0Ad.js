import{d as M,h as i,g as K,j as Q,k as f,y as X,a as O,o as C,A as F,l as d,D as Y,u as o,e as s,s as x,$ as V,w as u,m as ee,b as te,q as k,x as oe,F as se}from"./vue.BNx9QYep.js";import{H as ae,I as ne,g as L,a1 as y,J as le}from"./index.BHZI5pdK.js";import{M as E}from"./md5.DLPczxzP.js";import{c as re,g as ie,r as de}from"./crud.CDheULph.js";import{_ as pe}from"./index.vue_vue_type_script_setup_true_name_importExcel_lang.COJSjT1E.js";import{i as H,L as ue}from"./echarts.BiCAFTQd.js";import{_ as ce}from"./_plugin-vue_export-helper.DlAUqK2U.js";import"./dictionary.DNsEqk19.js";import"./authFunction.D3Be3hRy.js";const me={class:"dept-user-com-box dept-info"},we={class:"di-left"},fe={class:"di-cell"},_e={class:"content"},ve={class:"di-cell"},ge={class:"content"},he={class:"di-cell"},xe={class:"content"},ye={class:"di-cell"},Pe={key:0,style:{display:"flex","justify-content":"center","align-items":"center"}},be={class:"dialog-footer"},Ce=M({name:"user"}),Ve=M({...Ce,setup(ke,{expose:W}){let S,D;const B=i(),R=i(),{crudExpose:v}=ae({crudRef:B,crudBinding:R});let g=i(""),I=i(),U=i(),c=i(!1),l=i({}),p=i(!1),m=i(!1),t=K({id:0,newPassword:"",newPassword2:""});const j=()=>{var r,h;const a=((r=l.value.sub_dept_map)==null?void 0:r.map(_=>_.name))||[],e=((h=l.value.sub_dept_map)==null?void 0:h.map(_=>_.count))||[],w={tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},xAxis:{type:"category",data:a,axisTick:{alignWithLabel:!0}},yAxis:{type:"value"},dataZoom:[{type:"inside"}],grid:{top:"6%",right:"5%",bottom:"10%",left:"10%"},series:[{data:e,type:"bar",barWidth:"60%",showBackground:!0,itemStyle:{color:new ue(0,0,0,1,[{offset:0,color:"#83bff6"},{offset:.5,color:"#188df0"},{offset:1,color:"#188df0"}])}}]};S.setOption(w)},T=()=>{var e,w,r;const a={tooltip:{trigger:"item"},legend:{orient:"vertical",right:"0%",left:"65%",top:"center",itemWidth:12,itemHeight:12},series:[{type:"pie",radius:"65%",center:["32%","50%"],label:{show:!1,position:"center"},color:["#188df0","#f56c6c","#dcdfe6"],data:[{value:((e=l.value.gender)==null?void 0:e.male)||0,name:"男"},{value:((w=l.value.gender)==null?void 0:w.female)||0,name:"女"},{value:((r=l.value.gender)==null?void 0:r.unknown)||0,name:"未知"}]}]};D.setOption(a)},P=async()=>{const a=await ie(g.value,c.value?"1":"0");(a==null?void 0:a.code)===2e3&&(l.value=a.data,j(),T())},A=a=>{g.value=a,v.doSearch({form:{dept:a}}),P()},Z=()=>{A(g.value)},q=({id:a})=>{t.id=a,m.value=!0},b=()=>{m.value=!1,t.id=0,t.newPassword="",t.newPassword2=""},z=async()=>{if(!t.id){y("请选择用户！");return}if(!t.newPassword||!t.newPassword2){y("请输入密码！");return}if(t.newPassword!==t.newPassword2){y("两次输入密码不一致");return}const a=new RegExp("(?=.*[0-9])(?=.*[a-zA-Z]).{8,30}");if(!a.test(t.newPassword)||!a.test(t.newPassword2)){y("您的密码复杂度太低(密码中必须包含字母、数字)");return}const e=await de(t.id,{newPassword:E.hashStr(t.newPassword),newPassword2:E.hashStr(t.newPassword2)});(e==null?void 0:e.code)===2e3&&(le(e.msg||"修改成功！"),b())};Q(()=>{S=H(I.value),D=H(U.value),P(),v.doRefresh()}),W({handleDoRefreshUser:A});const{crudOptions:G}=re({crudExpose:v,context:{getDeptInfo:P,isShowChildFlag:c,handleResetPwdOpen:q}});return ne({crudExpose:v,crudOptions:G,context:{}}),(a,e)=>{const w=f("el-switch"),r=f("el-button"),h=f("el-image"),_=f("fs-crud"),N=f("el-input"),J=f("el-dialog"),$=X("auth");return C(),O(se,null,[F(s("div",me,[s("div",we,[s("h3",null,x(o(l).dept_name||""),1),s("div",fe,[e[5]||(e[5]=s("p",null,"部门负责人：",-1)),s("p",_e,x(o(l).owner||"无"),1)]),s("div",ve,[e[6]||(e[6]=s("p",null,"部门人数：",-1)),s("p",ge,x(o(l).dept_user||0)+"人",1)]),s("div",he,[e[7]||(e[7]=s("p",null,"部门简介：",-1)),s("p",xe,x(o(l).description||"无"),1)]),s("div",ye,[e[8]||(e[8]=s("p",null,"显示子级：",-1)),d(w,{modelValue:o(c),"onUpdate:modelValue":e[0]||(e[0]=n=>V(c)?c.value=n:c=n),"inline-prompt":"","active-text":"是","inactive-text":"否",disabled:!o(g),onChange:Z,style:{"--el-switch-on-color":"var(--el-color-primary)"}},null,8,["modelValue","disabled"])])]),s("div",{style:{height:"180px",width:"380px"},ref_key:"deptCountBar",ref:I},null,512),s("div",{style:{height:"180px",width:"200px"},ref_key:"deptSexPie",ref:U},null,512)],512),[[Y,!o(p)]]),d(_,oe({ref_key:"crudRef",ref:B},R.value,{customClass:o(p)?"dept-user-com-box dept-user-com-table-cover":"dept-user-com-box dept-user-com-table"}),{"toolbar-left":u(()=>[d(r,{icon:o(p)?"View":"Hide",circle:"",onClick:e[1]||(e[1]=n=>V(p)?p.value=!o(p):p=!o(p))},null,8,["icon"])]),"actionbar-right":u(()=>[F((C(),te(pe,{api:"api/system/user/"},{default:u(()=>e[9]||(e[9]=[k("导入 ")])),_:1,__:[9]})),[[$,"user:Import"]])]),cell_avatar:u(n=>[n.row.avatar?(C(),O("div",Pe,[d(h,{style:{width:"50px",height:"50px","border-radius":"50%","aspect-ratio":"1 /1"},src:o(L)(n.row.avatar),"preview-src-list":[o(L)(n.row.avatar)],"preview-teleported":!0},null,8,["src","preview-src-list"])])):ee("",!0)]),_:1},16,["customClass"]),d(J,{modelValue:o(m),"onUpdate:modelValue":e[4]||(e[4]=n=>V(m)?m.value=n:m=n),title:"重设密码",width:"400px",draggable:"","before-close":b},{footer:u(()=>[s("span",be,[d(r,{onClick:b},{default:u(()=>e[10]||(e[10]=[k("取消")])),_:1,__:[10]}),d(r,{type:"primary",onClick:z},{default:u(()=>e[11]||(e[11]=[k(" 保存 ")])),_:1,__:[11]})])]),default:u(()=>[s("div",null,[d(N,{modelValue:o(t).newPassword,"onUpdate:modelValue":e[2]||(e[2]=n=>o(t).newPassword=n),type:"password",placeholder:"请输入密码","show-password":"",style:{"margin-bottom":"20px"}},null,8,["modelValue"]),d(N,{modelValue:o(t).newPassword2,"onUpdate:modelValue":e[3]||(e[3]=n=>o(t).newPassword2=n),type:"password",placeholder:"请再次输入密码","show-password":""},null,8,["modelValue"])])]),_:1},8,["modelValue"])],64)}}}),Fe=ce(Ve,[["__scopeId","data-v-6a53335b"]]);export{Fe as default};
