{"version": 3, "sources": ["../../.pnpm/monaco-editor@0.52.2/node_modules/monaco-editor/esm/vs/language/json/monaco.contribution.js"], "sourcesContent": ["import '../../editor/editor.api.js';\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, monaco_editor_core_star);\nimport * as monaco_editor_core_star from \"../../editor/editor.api.js\";\n\n// src/language/json/monaco.contribution.ts\nvar LanguageServiceDefaultsImpl = class {\n  constructor(languageId, diagnosticsOptions, modeConfiguration) {\n    this._onDidChange = new monaco_editor_core_exports.Emitter();\n    this._languageId = languageId;\n    this.setDiagnosticsOptions(diagnosticsOptions);\n    this.setModeConfiguration(modeConfiguration);\n  }\n  get onDidChange() {\n    return this._onDidChange.event;\n  }\n  get languageId() {\n    return this._languageId;\n  }\n  get modeConfiguration() {\n    return this._modeConfiguration;\n  }\n  get diagnosticsOptions() {\n    return this._diagnosticsOptions;\n  }\n  setDiagnosticsOptions(options) {\n    this._diagnosticsOptions = options || /* @__PURE__ */ Object.create(null);\n    this._onDidChange.fire(this);\n  }\n  setModeConfiguration(modeConfiguration) {\n    this._modeConfiguration = modeConfiguration || /* @__PURE__ */ Object.create(null);\n    this._onDidChange.fire(this);\n  }\n};\nvar diagnosticDefault = {\n  validate: true,\n  allowComments: true,\n  schemas: [],\n  enableSchemaRequest: false,\n  schemaRequest: \"warning\",\n  schemaValidation: \"warning\",\n  comments: \"error\",\n  trailingCommas: \"error\"\n};\nvar modeConfigurationDefault = {\n  documentFormattingEdits: true,\n  documentRangeFormattingEdits: true,\n  completionItems: true,\n  hovers: true,\n  documentSymbols: true,\n  tokens: true,\n  colors: true,\n  foldingRanges: true,\n  diagnostics: true,\n  selectionRanges: true\n};\nvar jsonDefaults = new LanguageServiceDefaultsImpl(\n  \"json\",\n  diagnosticDefault,\n  modeConfigurationDefault\n);\nvar getWorker = () => getMode().then((mode) => mode.getWorker());\nmonaco_editor_core_exports.languages.json = { jsonDefaults, getWorker };\nfunction getMode() {\n  if (false) {\n    return new Promise((resolve, reject) => {\n      __require([\"vs/language/json/jsonMode\"], resolve, reject);\n    });\n  } else {\n    return import(\"./jsonMode.js\");\n  }\n}\nmonaco_editor_core_exports.languages.register({\n  id: \"json\",\n  extensions: [\".json\", \".bowerrc\", \".jshintrc\", \".jscsrc\", \".eslintrc\", \".babelrc\", \".har\"],\n  aliases: [\"JSON\", \"json\"],\n  mimetypes: [\"application/json\"]\n});\nmonaco_editor_core_exports.languages.onLanguage(\"json\", () => {\n  getMode().then((mode) => mode.setupMode(jsonDefaults));\n});\nexport {\n  getWorker,\n  jsonDefaults\n};\n"], "mappings": ";;;;;AAQA,IAAI,YAAY,OAAO;AACvB,IAAI,mBAAmB,OAAO;AAC9B,IAAI,oBAAoB,OAAO;AAC/B,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,MAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,aAAS,OAAO,kBAAkB,IAAI;AACpC,UAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,kBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,EACvH;AACA,SAAO;AACT;AACA,IAAI,aAAa,CAAC,QAAQ,KAAK,kBAAkB,YAAY,QAAQ,KAAK,SAAS,GAAG,gBAAgB,YAAY,cAAc,KAAK,SAAS;AAG9I,IAAI,6BAA6B,CAAC;AAClC,WAAW,4BAA4B,kBAAuB;AAI9D,IAAI,8BAA8B,MAAM;AAAA,EACtC,YAAY,YAAY,oBAAoB,mBAAmB;AAC7D,SAAK,eAAe,IAAI,2BAA2B,QAAQ;AAC3D,SAAK,cAAc;AACnB,SAAK,sBAAsB,kBAAkB;AAC7C,SAAK,qBAAqB,iBAAiB;AAAA,EAC7C;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK,aAAa;AAAA,EAC3B;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,oBAAoB;AACtB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,qBAAqB;AACvB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,sBAAsB,SAAS;AAC7B,SAAK,sBAAsB,WAA2B,uBAAO,OAAO,IAAI;AACxE,SAAK,aAAa,KAAK,IAAI;AAAA,EAC7B;AAAA,EACA,qBAAqB,mBAAmB;AACtC,SAAK,qBAAqB,qBAAqC,uBAAO,OAAO,IAAI;AACjF,SAAK,aAAa,KAAK,IAAI;AAAA,EAC7B;AACF;AACA,IAAI,oBAAoB;AAAA,EACtB,UAAU;AAAA,EACV,eAAe;AAAA,EACf,SAAS,CAAC;AAAA,EACV,qBAAqB;AAAA,EACrB,eAAe;AAAA,EACf,kBAAkB;AAAA,EAClB,UAAU;AAAA,EACV,gBAAgB;AAClB;AACA,IAAI,2BAA2B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,8BAA8B;AAAA,EAC9B,iBAAiB;AAAA,EACjB,QAAQ;AAAA,EACR,iBAAiB;AAAA,EACjB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,aAAa;AAAA,EACb,iBAAiB;AACnB;AACA,IAAI,eAAe,IAAI;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAI,YAAY,MAAM,QAAQ,EAAE,KAAK,CAAC,SAAS,KAAK,UAAU,CAAC;AAC/D,2BAA2B,UAAU,OAAO,EAAE,cAAc,UAAU;AACtE,SAAS,UAAU;AACjB,MAAI,OAAO;AACT,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,gBAAU,CAAC,2BAA2B,GAAG,SAAS,MAAM;AAAA,IAC1D,CAAC;AAAA,EACH,OAAO;AACL,WAAO,OAAO,wBAAe;AAAA,EAC/B;AACF;AACA,2BAA2B,UAAU,SAAS;AAAA,EAC5C,IAAI;AAAA,EACJ,YAAY,CAAC,SAAS,YAAY,aAAa,WAAW,aAAa,YAAY,MAAM;AAAA,EACzF,SAAS,CAAC,QAAQ,MAAM;AAAA,EACxB,WAAW,CAAC,kBAAkB;AAChC,CAAC;AACD,2BAA2B,UAAU,WAAW,QAAQ,MAAM;AAC5D,UAAQ,EAAE,KAAK,CAAC,SAAS,KAAK,UAAU,YAAY,CAAC;AACvD,CAAC;", "names": []}