{"version": 3, "sources": ["../../.pnpm/luxon@3.6.1/node_modules/luxon/src/errors.js", "../../.pnpm/luxon@3.6.1/node_modules/luxon/src/impl/formats.js", "../../.pnpm/luxon@3.6.1/node_modules/luxon/src/zone.js", "../../.pnpm/luxon@3.6.1/node_modules/luxon/src/zones/systemZone.js", "../../.pnpm/luxon@3.6.1/node_modules/luxon/src/zones/IANAZone.js", "../../.pnpm/luxon@3.6.1/node_modules/luxon/src/impl/locale.js", "../../.pnpm/luxon@3.6.1/node_modules/luxon/src/zones/fixedOffsetZone.js", "../../.pnpm/luxon@3.6.1/node_modules/luxon/src/zones/invalidZone.js", "../../.pnpm/luxon@3.6.1/node_modules/luxon/src/impl/zoneUtil.js", "../../.pnpm/luxon@3.6.1/node_modules/luxon/src/impl/digits.js", "../../.pnpm/luxon@3.6.1/node_modules/luxon/src/settings.js", "../../.pnpm/luxon@3.6.1/node_modules/luxon/src/impl/invalid.js", "../../.pnpm/luxon@3.6.1/node_modules/luxon/src/impl/conversions.js", "../../.pnpm/luxon@3.6.1/node_modules/luxon/src/impl/util.js", "../../.pnpm/luxon@3.6.1/node_modules/luxon/src/impl/english.js", "../../.pnpm/luxon@3.6.1/node_modules/luxon/src/impl/formatter.js", "../../.pnpm/luxon@3.6.1/node_modules/luxon/src/impl/regexParser.js", "../../.pnpm/luxon@3.6.1/node_modules/luxon/src/duration.js", "../../.pnpm/luxon@3.6.1/node_modules/luxon/src/interval.js", "../../.pnpm/luxon@3.6.1/node_modules/luxon/src/info.js", "../../.pnpm/luxon@3.6.1/node_modules/luxon/src/impl/diff.js", "../../.pnpm/luxon@3.6.1/node_modules/luxon/src/impl/tokenParser.js", "../../.pnpm/luxon@3.6.1/node_modules/luxon/src/datetime.js", "../../.pnpm/luxon@3.6.1/node_modules/luxon/src/luxon.js", "../../.pnpm/cron-parser@4.9.0/node_modules/cron-parser/lib/date.js", "../../.pnpm/cron-parser@4.9.0/node_modules/cron-parser/lib/field_compactor.js", "../../.pnpm/cron-parser@4.9.0/node_modules/cron-parser/lib/field_stringify.js", "../../.pnpm/cron-parser@4.9.0/node_modules/cron-parser/lib/expression.js", "browser-external:fs", "../../.pnpm/cron-parser@4.9.0/node_modules/cron-parser/lib/parser.js"], "sourcesContent": ["// these aren't really private, but nor are they really useful to document\n\n/**\n * @private\n */\nclass LuxonError extends Error {}\n\n/**\n * @private\n */\nexport class InvalidDateTimeError extends LuxonError {\n  constructor(reason) {\n    super(`Invalid DateTime: ${reason.toMessage()}`);\n  }\n}\n\n/**\n * @private\n */\nexport class InvalidIntervalError extends LuxonError {\n  constructor(reason) {\n    super(`Invalid Interval: ${reason.toMessage()}`);\n  }\n}\n\n/**\n * @private\n */\nexport class InvalidDurationError extends LuxonError {\n  constructor(reason) {\n    super(`Invalid Duration: ${reason.toMessage()}`);\n  }\n}\n\n/**\n * @private\n */\nexport class ConflictingSpecificationError extends LuxonError {}\n\n/**\n * @private\n */\nexport class InvalidUnitError extends LuxonError {\n  constructor(unit) {\n    super(`Invalid unit ${unit}`);\n  }\n}\n\n/**\n * @private\n */\nexport class InvalidArgumentError extends LuxonError {}\n\n/**\n * @private\n */\nexport class ZoneIsAbstractError extends LuxonError {\n  constructor() {\n    super(\"Zone is an abstract class\");\n  }\n}\n", "/**\n * @private\n */\n\nconst n = \"numeric\",\n  s = \"short\",\n  l = \"long\";\n\nexport const DATE_SHORT = {\n  year: n,\n  month: n,\n  day: n,\n};\n\nexport const DATE_MED = {\n  year: n,\n  month: s,\n  day: n,\n};\n\nexport const DATE_MED_WITH_WEEKDAY = {\n  year: n,\n  month: s,\n  day: n,\n  weekday: s,\n};\n\nexport const DATE_FULL = {\n  year: n,\n  month: l,\n  day: n,\n};\n\nexport const DATE_HUGE = {\n  year: n,\n  month: l,\n  day: n,\n  weekday: l,\n};\n\nexport const TIME_SIMPLE = {\n  hour: n,\n  minute: n,\n};\n\nexport const TIME_WITH_SECONDS = {\n  hour: n,\n  minute: n,\n  second: n,\n};\n\nexport const TIME_WITH_SHORT_OFFSET = {\n  hour: n,\n  minute: n,\n  second: n,\n  timeZoneName: s,\n};\n\nexport const TIME_WITH_LONG_OFFSET = {\n  hour: n,\n  minute: n,\n  second: n,\n  timeZoneName: l,\n};\n\nexport const TIME_24_SIMPLE = {\n  hour: n,\n  minute: n,\n  hourCycle: \"h23\",\n};\n\nexport const TIME_24_WITH_SECONDS = {\n  hour: n,\n  minute: n,\n  second: n,\n  hourCycle: \"h23\",\n};\n\nexport const TIME_24_WITH_SHORT_OFFSET = {\n  hour: n,\n  minute: n,\n  second: n,\n  hourCycle: \"h23\",\n  timeZoneName: s,\n};\n\nexport const TIME_24_WITH_LONG_OFFSET = {\n  hour: n,\n  minute: n,\n  second: n,\n  hourCycle: \"h23\",\n  timeZoneName: l,\n};\n\nexport const DATETIME_SHORT = {\n  year: n,\n  month: n,\n  day: n,\n  hour: n,\n  minute: n,\n};\n\nexport const DATETIME_SHORT_WITH_SECONDS = {\n  year: n,\n  month: n,\n  day: n,\n  hour: n,\n  minute: n,\n  second: n,\n};\n\nexport const DATETIME_MED = {\n  year: n,\n  month: s,\n  day: n,\n  hour: n,\n  minute: n,\n};\n\nexport const DATETIME_MED_WITH_SECONDS = {\n  year: n,\n  month: s,\n  day: n,\n  hour: n,\n  minute: n,\n  second: n,\n};\n\nexport const DATETIME_MED_WITH_WEEKDAY = {\n  year: n,\n  month: s,\n  day: n,\n  weekday: s,\n  hour: n,\n  minute: n,\n};\n\nexport const DATETIME_FULL = {\n  year: n,\n  month: l,\n  day: n,\n  hour: n,\n  minute: n,\n  timeZoneName: s,\n};\n\nexport const DATETIME_FULL_WITH_SECONDS = {\n  year: n,\n  month: l,\n  day: n,\n  hour: n,\n  minute: n,\n  second: n,\n  timeZoneName: s,\n};\n\nexport const DATETIME_HUGE = {\n  year: n,\n  month: l,\n  day: n,\n  weekday: l,\n  hour: n,\n  minute: n,\n  timeZoneName: l,\n};\n\nexport const DATETIME_HUGE_WITH_SECONDS = {\n  year: n,\n  month: l,\n  day: n,\n  weekday: l,\n  hour: n,\n  minute: n,\n  second: n,\n  timeZoneName: l,\n};\n", "import { ZoneIsAbstractError } from \"./errors.js\";\n\n/**\n * @interface\n */\nexport default class Zone {\n  /**\n   * The type of zone\n   * @abstract\n   * @type {string}\n   */\n  get type() {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * The name of this zone.\n   * @abstract\n   * @type {string}\n   */\n  get name() {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * The IANA name of this zone.\n   * Defaults to `name` if not overwritten by a subclass.\n   * @abstract\n   * @type {string}\n   */\n  get ianaName() {\n    return this.name;\n  }\n\n  /**\n   * Returns whether the offset is known to be fixed for the whole year.\n   * @abstract\n   * @type {boolean}\n   */\n  get isUniversal() {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * Returns the offset's common name (such as EST) at the specified timestamp\n   * @abstract\n   * @param {number} ts - Epoch milliseconds for which to get the name\n   * @param {Object} opts - Options to affect the format\n   * @param {string} opts.format - What style of offset to return. Accepts 'long' or 'short'.\n   * @param {string} opts.locale - What locale to return the offset name in.\n   * @return {string}\n   */\n  offsetName(ts, opts) {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * Returns the offset's value as a string\n   * @abstract\n   * @param {number} ts - Epoch milliseconds for which to get the offset\n   * @param {string} format - What style of offset to return.\n   *                          Accepts 'narrow', 'short', or 'techie'. Returning '+6', '+06:00', or '+0600' respectively\n   * @return {string}\n   */\n  formatOffset(ts, format) {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * Return the offset in minutes for this zone at the specified timestamp.\n   * @abstract\n   * @param {number} ts - Epoch milliseconds for which to compute the offset\n   * @return {number}\n   */\n  offset(ts) {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * Return whether this Zone is equal to another zone\n   * @abstract\n   * @param {Zone} otherZone - the zone to compare\n   * @return {boolean}\n   */\n  equals(otherZone) {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * Return whether this Zone is valid.\n   * @abstract\n   * @type {boolean}\n   */\n  get isValid() {\n    throw new ZoneIsAbstractError();\n  }\n}\n", "import { formatOffset, parseZoneInfo } from \"../impl/util.js\";\nimport Zone from \"../zone.js\";\n\nlet singleton = null;\n\n/**\n * Represents the local zone for this JavaScript environment.\n * @implements {Zone}\n */\nexport default class SystemZone extends Zone {\n  /**\n   * Get a singleton instance of the local zone\n   * @return {SystemZone}\n   */\n  static get instance() {\n    if (singleton === null) {\n      singleton = new SystemZone();\n    }\n    return singleton;\n  }\n\n  /** @override **/\n  get type() {\n    return \"system\";\n  }\n\n  /** @override **/\n  get name() {\n    return new Intl.DateTimeFormat().resolvedOptions().timeZone;\n  }\n\n  /** @override **/\n  get isUniversal() {\n    return false;\n  }\n\n  /** @override **/\n  offsetName(ts, { format, locale }) {\n    return parseZoneInfo(ts, format, locale);\n  }\n\n  /** @override **/\n  formatOffset(ts, format) {\n    return formatOffset(this.offset(ts), format);\n  }\n\n  /** @override **/\n  offset(ts) {\n    return -new Date(ts).getTimezoneOffset();\n  }\n\n  /** @override **/\n  equals(otherZone) {\n    return otherZone.type === \"system\";\n  }\n\n  /** @override **/\n  get isValid() {\n    return true;\n  }\n}\n", "import { formatOffset, parseZoneInfo, isUndefined, objToLocalTS } from \"../impl/util.js\";\nimport Zone from \"../zone.js\";\n\nconst dtfCache = new Map();\nfunction makeDTF(zoneName) {\n  let dtf = dtfCache.get(zoneName);\n  if (dtf === undefined) {\n    dtf = new Intl.DateTimeFormat(\"en-US\", {\n      hour12: false,\n      timeZone: zoneName,\n      year: \"numeric\",\n      month: \"2-digit\",\n      day: \"2-digit\",\n      hour: \"2-digit\",\n      minute: \"2-digit\",\n      second: \"2-digit\",\n      era: \"short\",\n    });\n    dtfCache.set(zoneName, dtf);\n  }\n  return dtf;\n}\n\nconst typeToPos = {\n  year: 0,\n  month: 1,\n  day: 2,\n  era: 3,\n  hour: 4,\n  minute: 5,\n  second: 6,\n};\n\nfunction hackyOffset(dtf, date) {\n  const formatted = dtf.format(date).replace(/\\u200E/g, \"\"),\n    parsed = /(\\d+)\\/(\\d+)\\/(\\d+) (AD|BC),? (\\d+):(\\d+):(\\d+)/.exec(formatted),\n    [, fMonth, fDay, fYear, fadOrBc, fHour, fMinute, fSecond] = parsed;\n  return [fYear, fMonth, fDay, fadOrBc, fHour, fMinute, fSecond];\n}\n\nfunction partsOffset(dtf, date) {\n  const formatted = dtf.formatToParts(date);\n  const filled = [];\n  for (let i = 0; i < formatted.length; i++) {\n    const { type, value } = formatted[i];\n    const pos = typeToPos[type];\n\n    if (type === \"era\") {\n      filled[pos] = value;\n    } else if (!isUndefined(pos)) {\n      filled[pos] = parseInt(value, 10);\n    }\n  }\n  return filled;\n}\n\nconst ianaZoneCache = new Map();\n/**\n * A zone identified by an IANA identifier, like America/New_York\n * @implements {Zone}\n */\nexport default class IANAZone extends Zone {\n  /**\n   * @param {string} name - Zone name\n   * @return {IANAZone}\n   */\n  static create(name) {\n    let zone = ianaZoneCache.get(name);\n    if (zone === undefined) {\n      ianaZoneCache.set(name, (zone = new IANAZone(name)));\n    }\n    return zone;\n  }\n\n  /**\n   * Reset local caches. Should only be necessary in testing scenarios.\n   * @return {void}\n   */\n  static resetCache() {\n    ianaZoneCache.clear();\n    dtfCache.clear();\n  }\n\n  /**\n   * Returns whether the provided string is a valid specifier. This only checks the string's format, not that the specifier identifies a known zone; see isValidZone for that.\n   * @param {string} s - The string to check validity on\n   * @example IANAZone.isValidSpecifier(\"America/New_York\") //=> true\n   * @example IANAZone.isValidSpecifier(\"Sport~~blorp\") //=> false\n   * @deprecated For backward compatibility, this forwards to isValidZone, better use `isValidZone()` directly instead.\n   * @return {boolean}\n   */\n  static isValidSpecifier(s) {\n    return this.isValidZone(s);\n  }\n\n  /**\n   * Returns whether the provided string identifies a real zone\n   * @param {string} zone - The string to check\n   * @example IANAZone.isValidZone(\"America/New_York\") //=> true\n   * @example IANAZone.isValidZone(\"Fantasia/Castle\") //=> false\n   * @example IANAZone.isValidZone(\"Sport~~blorp\") //=> false\n   * @return {boolean}\n   */\n  static isValidZone(zone) {\n    if (!zone) {\n      return false;\n    }\n    try {\n      new Intl.DateTimeFormat(\"en-US\", { timeZone: zone }).format();\n      return true;\n    } catch (e) {\n      return false;\n    }\n  }\n\n  constructor(name) {\n    super();\n    /** @private **/\n    this.zoneName = name;\n    /** @private **/\n    this.valid = IANAZone.isValidZone(name);\n  }\n\n  /**\n   * The type of zone. `iana` for all instances of `IANAZone`.\n   * @override\n   * @type {string}\n   */\n  get type() {\n    return \"iana\";\n  }\n\n  /**\n   * The name of this zone (i.e. the IANA zone name).\n   * @override\n   * @type {string}\n   */\n  get name() {\n    return this.zoneName;\n  }\n\n  /**\n   * Returns whether the offset is known to be fixed for the whole year:\n   * Always returns false for all IANA zones.\n   * @override\n   * @type {boolean}\n   */\n  get isUniversal() {\n    return false;\n  }\n\n  /**\n   * Returns the offset's common name (such as EST) at the specified timestamp\n   * @override\n   * @param {number} ts - Epoch milliseconds for which to get the name\n   * @param {Object} opts - Options to affect the format\n   * @param {string} opts.format - What style of offset to return. Accepts 'long' or 'short'.\n   * @param {string} opts.locale - What locale to return the offset name in.\n   * @return {string}\n   */\n  offsetName(ts, { format, locale }) {\n    return parseZoneInfo(ts, format, locale, this.name);\n  }\n\n  /**\n   * Returns the offset's value as a string\n   * @override\n   * @param {number} ts - Epoch milliseconds for which to get the offset\n   * @param {string} format - What style of offset to return.\n   *                          Accepts 'narrow', 'short', or 'techie'. Returning '+6', '+06:00', or '+0600' respectively\n   * @return {string}\n   */\n  formatOffset(ts, format) {\n    return formatOffset(this.offset(ts), format);\n  }\n\n  /**\n   * Return the offset in minutes for this zone at the specified timestamp.\n   * @override\n   * @param {number} ts - Epoch milliseconds for which to compute the offset\n   * @return {number}\n   */\n  offset(ts) {\n    if (!this.valid) return NaN;\n    const date = new Date(ts);\n\n    if (isNaN(date)) return NaN;\n\n    const dtf = makeDTF(this.name);\n    let [year, month, day, adOrBc, hour, minute, second] = dtf.formatToParts\n      ? partsOffset(dtf, date)\n      : hackyOffset(dtf, date);\n\n    if (adOrBc === \"BC\") {\n      year = -Math.abs(year) + 1;\n    }\n\n    // because we're using hour12 and https://bugs.chromium.org/p/chromium/issues/detail?id=1025564&can=2&q=%2224%3A00%22%20datetimeformat\n    const adjustedHour = hour === 24 ? 0 : hour;\n\n    const asUTC = objToLocalTS({\n      year,\n      month,\n      day,\n      hour: adjustedHour,\n      minute,\n      second,\n      millisecond: 0,\n    });\n\n    let asTS = +date;\n    const over = asTS % 1000;\n    asTS -= over >= 0 ? over : 1000 + over;\n    return (asUTC - asTS) / (60 * 1000);\n  }\n\n  /**\n   * Return whether this Zone is equal to another zone\n   * @override\n   * @param {Zone} otherZone - the zone to compare\n   * @return {boolean}\n   */\n  equals(otherZone) {\n    return otherZone.type === \"iana\" && otherZone.name === this.name;\n  }\n\n  /**\n   * Return whether this Zone is valid.\n   * @override\n   * @type {boolean}\n   */\n  get isValid() {\n    return this.valid;\n  }\n}\n", "import { hasLocaleWeekInfo, hasRelative, padStart, roundTo, validateWeekSettings } from \"./util.js\";\nimport * as English from \"./english.js\";\nimport Settings from \"../settings.js\";\nimport DateTime from \"../datetime.js\";\nimport IANAZone from \"../zones/IANAZone.js\";\n\n// todo - remap caching\n\nlet intlLFCache = {};\nfunction getCachedLF(locString, opts = {}) {\n  const key = JSON.stringify([locString, opts]);\n  let dtf = intlLFCache[key];\n  if (!dtf) {\n    dtf = new Intl.ListFormat(locString, opts);\n    intlLFCache[key] = dtf;\n  }\n  return dtf;\n}\n\nconst intlDTCache = new Map();\nfunction getCachedDTF(locString, opts = {}) {\n  const key = JSON.stringify([locString, opts]);\n  let dtf = intlDTCache.get(key);\n  if (dtf === undefined) {\n    dtf = new Intl.DateTimeFormat(locString, opts);\n    intlDTCache.set(key, dtf);\n  }\n  return dtf;\n}\n\nconst intlNumCache = new Map();\nfunction getCachedINF(locString, opts = {}) {\n  const key = JSON.stringify([locString, opts]);\n  let inf = intlNumCache.get(key);\n  if (inf === undefined) {\n    inf = new Intl.NumberFormat(locString, opts);\n    intlNumCache.set(key, inf);\n  }\n  return inf;\n}\n\nconst intlRelCache = new Map();\nfunction getCachedRTF(locString, opts = {}) {\n  const { base, ...cacheKeyOpts } = opts; // exclude `base` from the options\n  const key = JSON.stringify([locString, cacheKeyOpts]);\n  let inf = intlRelCache.get(key);\n  if (inf === undefined) {\n    inf = new Intl.RelativeTimeFormat(locString, opts);\n    intlRelCache.set(key, inf);\n  }\n  return inf;\n}\n\nlet sysLocaleCache = null;\nfunction systemLocale() {\n  if (sysLocaleCache) {\n    return sysLocaleCache;\n  } else {\n    sysLocaleCache = new Intl.DateTimeFormat().resolvedOptions().locale;\n    return sysLocaleCache;\n  }\n}\n\nconst intlResolvedOptionsCache = new Map();\nfunction getCachedIntResolvedOptions(locString) {\n  let opts = intlResolvedOptionsCache.get(locString);\n  if (opts === undefined) {\n    opts = new Intl.DateTimeFormat(locString).resolvedOptions();\n    intlResolvedOptionsCache.set(locString, opts);\n  }\n  return opts;\n}\n\nconst weekInfoCache = new Map();\nfunction getCachedWeekInfo(locString) {\n  let data = weekInfoCache.get(locString);\n  if (!data) {\n    const locale = new Intl.Locale(locString);\n    // browsers currently implement this as a property, but spec says it should be a getter function\n    data = \"getWeekInfo\" in locale ? locale.getWeekInfo() : locale.weekInfo;\n    // minimalDays was removed from WeekInfo: https://github.com/tc39/proposal-intl-locale-info/issues/86\n    if (!(\"minimalDays\" in data)) {\n      data = { ...fallbackWeekSettings, ...data };\n    }\n    weekInfoCache.set(locString, data);\n  }\n  return data;\n}\n\nfunction parseLocaleString(localeStr) {\n  // I really want to avoid writing a BCP 47 parser\n  // see, e.g. https://github.com/wooorm/bcp-47\n  // Instead, we'll do this:\n\n  // a) if the string has no -u extensions, just leave it alone\n  // b) if it does, use Intl to resolve everything\n  // c) if Intl fails, try again without the -u\n\n  // private subtags and unicode subtags have ordering requirements,\n  // and we're not properly parsing this, so just strip out the\n  // private ones if they exist.\n  const xIndex = localeStr.indexOf(\"-x-\");\n  if (xIndex !== -1) {\n    localeStr = localeStr.substring(0, xIndex);\n  }\n\n  const uIndex = localeStr.indexOf(\"-u-\");\n  if (uIndex === -1) {\n    return [localeStr];\n  } else {\n    let options;\n    let selectedStr;\n    try {\n      options = getCachedDTF(localeStr).resolvedOptions();\n      selectedStr = localeStr;\n    } catch (e) {\n      const smaller = localeStr.substring(0, uIndex);\n      options = getCachedDTF(smaller).resolvedOptions();\n      selectedStr = smaller;\n    }\n\n    const { numberingSystem, calendar } = options;\n    return [selectedStr, numberingSystem, calendar];\n  }\n}\n\nfunction intlConfigString(localeStr, numberingSystem, outputCalendar) {\n  if (outputCalendar || numberingSystem) {\n    if (!localeStr.includes(\"-u-\")) {\n      localeStr += \"-u\";\n    }\n\n    if (outputCalendar) {\n      localeStr += `-ca-${outputCalendar}`;\n    }\n\n    if (numberingSystem) {\n      localeStr += `-nu-${numberingSystem}`;\n    }\n    return localeStr;\n  } else {\n    return localeStr;\n  }\n}\n\nfunction mapMonths(f) {\n  const ms = [];\n  for (let i = 1; i <= 12; i++) {\n    const dt = DateTime.utc(2009, i, 1);\n    ms.push(f(dt));\n  }\n  return ms;\n}\n\nfunction mapWeekdays(f) {\n  const ms = [];\n  for (let i = 1; i <= 7; i++) {\n    const dt = DateTime.utc(2016, 11, 13 + i);\n    ms.push(f(dt));\n  }\n  return ms;\n}\n\nfunction listStuff(loc, length, englishFn, intlFn) {\n  const mode = loc.listingMode();\n\n  if (mode === \"error\") {\n    return null;\n  } else if (mode === \"en\") {\n    return englishFn(length);\n  } else {\n    return intlFn(length);\n  }\n}\n\nfunction supportsFastNumbers(loc) {\n  if (loc.numberingSystem && loc.numberingSystem !== \"latn\") {\n    return false;\n  } else {\n    return (\n      loc.numberingSystem === \"latn\" ||\n      !loc.locale ||\n      loc.locale.startsWith(\"en\") ||\n      getCachedIntResolvedOptions(loc.locale).numberingSystem === \"latn\"\n    );\n  }\n}\n\n/**\n * @private\n */\n\nclass PolyNumberFormatter {\n  constructor(intl, forceSimple, opts) {\n    this.padTo = opts.padTo || 0;\n    this.floor = opts.floor || false;\n\n    const { padTo, floor, ...otherOpts } = opts;\n\n    if (!forceSimple || Object.keys(otherOpts).length > 0) {\n      const intlOpts = { useGrouping: false, ...opts };\n      if (opts.padTo > 0) intlOpts.minimumIntegerDigits = opts.padTo;\n      this.inf = getCachedINF(intl, intlOpts);\n    }\n  }\n\n  format(i) {\n    if (this.inf) {\n      const fixed = this.floor ? Math.floor(i) : i;\n      return this.inf.format(fixed);\n    } else {\n      // to match the browser's numberformatter defaults\n      const fixed = this.floor ? Math.floor(i) : roundTo(i, 3);\n      return padStart(fixed, this.padTo);\n    }\n  }\n}\n\n/**\n * @private\n */\n\nclass PolyDateFormatter {\n  constructor(dt, intl, opts) {\n    this.opts = opts;\n    this.originalZone = undefined;\n\n    let z = undefined;\n    if (this.opts.timeZone) {\n      // Don't apply any workarounds if a timeZone is explicitly provided in opts\n      this.dt = dt;\n    } else if (dt.zone.type === \"fixed\") {\n      // UTC-8 or Etc/UTC-8 are not part of tzdata, only Etc/GMT+8 and the like.\n      // That is why fixed-offset TZ is set to that unless it is:\n      // 1. Representing offset 0 when UTC is used to maintain previous behavior and does not become GMT.\n      // 2. Unsupported by the browser:\n      //    - some do not support Etc/\n      //    - < Etc/GMT-14, > Etc/GMT+12, and 30-minute or 45-minute offsets are not part of tzdata\n      const gmtOffset = -1 * (dt.offset / 60);\n      const offsetZ = gmtOffset >= 0 ? `Etc/GMT+${gmtOffset}` : `Etc/GMT${gmtOffset}`;\n      if (dt.offset !== 0 && IANAZone.create(offsetZ).valid) {\n        z = offsetZ;\n        this.dt = dt;\n      } else {\n        // Not all fixed-offset zones like Etc/+4:30 are present in tzdata so\n        // we manually apply the offset and substitute the zone as needed.\n        z = \"UTC\";\n        this.dt = dt.offset === 0 ? dt : dt.setZone(\"UTC\").plus({ minutes: dt.offset });\n        this.originalZone = dt.zone;\n      }\n    } else if (dt.zone.type === \"system\") {\n      this.dt = dt;\n    } else if (dt.zone.type === \"iana\") {\n      this.dt = dt;\n      z = dt.zone.name;\n    } else {\n      // Custom zones can have any offset / offsetName so we just manually\n      // apply the offset and substitute the zone as needed.\n      z = \"UTC\";\n      this.dt = dt.setZone(\"UTC\").plus({ minutes: dt.offset });\n      this.originalZone = dt.zone;\n    }\n\n    const intlOpts = { ...this.opts };\n    intlOpts.timeZone = intlOpts.timeZone || z;\n    this.dtf = getCachedDTF(intl, intlOpts);\n  }\n\n  format() {\n    if (this.originalZone) {\n      // If we have to substitute in the actual zone name, we have to use\n      // formatToParts so that the timezone can be replaced.\n      return this.formatToParts()\n        .map(({ value }) => value)\n        .join(\"\");\n    }\n    return this.dtf.format(this.dt.toJSDate());\n  }\n\n  formatToParts() {\n    const parts = this.dtf.formatToParts(this.dt.toJSDate());\n    if (this.originalZone) {\n      return parts.map((part) => {\n        if (part.type === \"timeZoneName\") {\n          const offsetName = this.originalZone.offsetName(this.dt.ts, {\n            locale: this.dt.locale,\n            format: this.opts.timeZoneName,\n          });\n          return {\n            ...part,\n            value: offsetName,\n          };\n        } else {\n          return part;\n        }\n      });\n    }\n    return parts;\n  }\n\n  resolvedOptions() {\n    return this.dtf.resolvedOptions();\n  }\n}\n\n/**\n * @private\n */\nclass PolyRelFormatter {\n  constructor(intl, isEnglish, opts) {\n    this.opts = { style: \"long\", ...opts };\n    if (!isEnglish && hasRelative()) {\n      this.rtf = getCachedRTF(intl, opts);\n    }\n  }\n\n  format(count, unit) {\n    if (this.rtf) {\n      return this.rtf.format(count, unit);\n    } else {\n      return English.formatRelativeTime(unit, count, this.opts.numeric, this.opts.style !== \"long\");\n    }\n  }\n\n  formatToParts(count, unit) {\n    if (this.rtf) {\n      return this.rtf.formatToParts(count, unit);\n    } else {\n      return [];\n    }\n  }\n}\n\nconst fallbackWeekSettings = {\n  firstDay: 1,\n  minimalDays: 4,\n  weekend: [6, 7],\n};\n\n/**\n * @private\n */\nexport default class Locale {\n  static fromOpts(opts) {\n    return Locale.create(\n      opts.locale,\n      opts.numberingSystem,\n      opts.outputCalendar,\n      opts.weekSettings,\n      opts.defaultToEN\n    );\n  }\n\n  static create(locale, numberingSystem, outputCalendar, weekSettings, defaultToEN = false) {\n    const specifiedLocale = locale || Settings.defaultLocale;\n    // the system locale is useful for human-readable strings but annoying for parsing/formatting known formats\n    const localeR = specifiedLocale || (defaultToEN ? \"en-US\" : systemLocale());\n    const numberingSystemR = numberingSystem || Settings.defaultNumberingSystem;\n    const outputCalendarR = outputCalendar || Settings.defaultOutputCalendar;\n    const weekSettingsR = validateWeekSettings(weekSettings) || Settings.defaultWeekSettings;\n    return new Locale(localeR, numberingSystemR, outputCalendarR, weekSettingsR, specifiedLocale);\n  }\n\n  static resetCache() {\n    sysLocaleCache = null;\n    intlDTCache.clear();\n    intlNumCache.clear();\n    intlRelCache.clear();\n    intlResolvedOptionsCache.clear();\n    weekInfoCache.clear();\n  }\n\n  static fromObject({ locale, numberingSystem, outputCalendar, weekSettings } = {}) {\n    return Locale.create(locale, numberingSystem, outputCalendar, weekSettings);\n  }\n\n  constructor(locale, numbering, outputCalendar, weekSettings, specifiedLocale) {\n    const [parsedLocale, parsedNumberingSystem, parsedOutputCalendar] = parseLocaleString(locale);\n\n    this.locale = parsedLocale;\n    this.numberingSystem = numbering || parsedNumberingSystem || null;\n    this.outputCalendar = outputCalendar || parsedOutputCalendar || null;\n    this.weekSettings = weekSettings;\n    this.intl = intlConfigString(this.locale, this.numberingSystem, this.outputCalendar);\n\n    this.weekdaysCache = { format: {}, standalone: {} };\n    this.monthsCache = { format: {}, standalone: {} };\n    this.meridiemCache = null;\n    this.eraCache = {};\n\n    this.specifiedLocale = specifiedLocale;\n    this.fastNumbersCached = null;\n  }\n\n  get fastNumbers() {\n    if (this.fastNumbersCached == null) {\n      this.fastNumbersCached = supportsFastNumbers(this);\n    }\n\n    return this.fastNumbersCached;\n  }\n\n  listingMode() {\n    const isActuallyEn = this.isEnglish();\n    const hasNoWeirdness =\n      (this.numberingSystem === null || this.numberingSystem === \"latn\") &&\n      (this.outputCalendar === null || this.outputCalendar === \"gregory\");\n    return isActuallyEn && hasNoWeirdness ? \"en\" : \"intl\";\n  }\n\n  clone(alts) {\n    if (!alts || Object.getOwnPropertyNames(alts).length === 0) {\n      return this;\n    } else {\n      return Locale.create(\n        alts.locale || this.specifiedLocale,\n        alts.numberingSystem || this.numberingSystem,\n        alts.outputCalendar || this.outputCalendar,\n        validateWeekSettings(alts.weekSettings) || this.weekSettings,\n        alts.defaultToEN || false\n      );\n    }\n  }\n\n  redefaultToEN(alts = {}) {\n    return this.clone({ ...alts, defaultToEN: true });\n  }\n\n  redefaultToSystem(alts = {}) {\n    return this.clone({ ...alts, defaultToEN: false });\n  }\n\n  months(length, format = false) {\n    return listStuff(this, length, English.months, () => {\n      const intl = format ? { month: length, day: \"numeric\" } : { month: length },\n        formatStr = format ? \"format\" : \"standalone\";\n      if (!this.monthsCache[formatStr][length]) {\n        this.monthsCache[formatStr][length] = mapMonths((dt) => this.extract(dt, intl, \"month\"));\n      }\n      return this.monthsCache[formatStr][length];\n    });\n  }\n\n  weekdays(length, format = false) {\n    return listStuff(this, length, English.weekdays, () => {\n      const intl = format\n          ? { weekday: length, year: \"numeric\", month: \"long\", day: \"numeric\" }\n          : { weekday: length },\n        formatStr = format ? \"format\" : \"standalone\";\n      if (!this.weekdaysCache[formatStr][length]) {\n        this.weekdaysCache[formatStr][length] = mapWeekdays((dt) =>\n          this.extract(dt, intl, \"weekday\")\n        );\n      }\n      return this.weekdaysCache[formatStr][length];\n    });\n  }\n\n  meridiems() {\n    return listStuff(\n      this,\n      undefined,\n      () => English.meridiems,\n      () => {\n        // In theory there could be aribitrary day periods. We're gonna assume there are exactly two\n        // for AM and PM. This is probably wrong, but it's makes parsing way easier.\n        if (!this.meridiemCache) {\n          const intl = { hour: \"numeric\", hourCycle: \"h12\" };\n          this.meridiemCache = [DateTime.utc(2016, 11, 13, 9), DateTime.utc(2016, 11, 13, 19)].map(\n            (dt) => this.extract(dt, intl, \"dayperiod\")\n          );\n        }\n\n        return this.meridiemCache;\n      }\n    );\n  }\n\n  eras(length) {\n    return listStuff(this, length, English.eras, () => {\n      const intl = { era: length };\n\n      // This is problematic. Different calendars are going to define eras totally differently. What I need is the minimum set of dates\n      // to definitely enumerate them.\n      if (!this.eraCache[length]) {\n        this.eraCache[length] = [DateTime.utc(-40, 1, 1), DateTime.utc(2017, 1, 1)].map((dt) =>\n          this.extract(dt, intl, \"era\")\n        );\n      }\n\n      return this.eraCache[length];\n    });\n  }\n\n  extract(dt, intlOpts, field) {\n    const df = this.dtFormatter(dt, intlOpts),\n      results = df.formatToParts(),\n      matching = results.find((m) => m.type.toLowerCase() === field);\n    return matching ? matching.value : null;\n  }\n\n  numberFormatter(opts = {}) {\n    // this forcesimple option is never used (the only caller short-circuits on it, but it seems safer to leave)\n    // (in contrast, the rest of the condition is used heavily)\n    return new PolyNumberFormatter(this.intl, opts.forceSimple || this.fastNumbers, opts);\n  }\n\n  dtFormatter(dt, intlOpts = {}) {\n    return new PolyDateFormatter(dt, this.intl, intlOpts);\n  }\n\n  relFormatter(opts = {}) {\n    return new PolyRelFormatter(this.intl, this.isEnglish(), opts);\n  }\n\n  listFormatter(opts = {}) {\n    return getCachedLF(this.intl, opts);\n  }\n\n  isEnglish() {\n    return (\n      this.locale === \"en\" ||\n      this.locale.toLowerCase() === \"en-us\" ||\n      getCachedIntResolvedOptions(this.intl).locale.startsWith(\"en-us\")\n    );\n  }\n\n  getWeekSettings() {\n    if (this.weekSettings) {\n      return this.weekSettings;\n    } else if (!hasLocaleWeekInfo()) {\n      return fallbackWeekSettings;\n    } else {\n      return getCachedWeekInfo(this.locale);\n    }\n  }\n\n  getStartOfWeek() {\n    return this.getWeekSettings().firstDay;\n  }\n\n  getMinDaysInFirstWeek() {\n    return this.getWeekSettings().minimalDays;\n  }\n\n  getWeekendDays() {\n    return this.getWeekSettings().weekend;\n  }\n\n  equals(other) {\n    return (\n      this.locale === other.locale &&\n      this.numberingSystem === other.numberingSystem &&\n      this.outputCalendar === other.outputCalendar\n    );\n  }\n\n  toString() {\n    return `Locale(${this.locale}, ${this.numberingSystem}, ${this.outputCalendar})`;\n  }\n}\n", "import { formatOffset, signedOffset } from \"../impl/util.js\";\nimport Zone from \"../zone.js\";\n\nlet singleton = null;\n\n/**\n * A zone with a fixed offset (meaning no DST)\n * @implements {Zone}\n */\nexport default class FixedOffsetZone extends Zone {\n  /**\n   * Get a singleton instance of UTC\n   * @return {FixedOffsetZone}\n   */\n  static get utcInstance() {\n    if (singleton === null) {\n      singleton = new FixedOffsetZone(0);\n    }\n    return singleton;\n  }\n\n  /**\n   * Get an instance with a specified offset\n   * @param {number} offset - The offset in minutes\n   * @return {FixedOffsetZone}\n   */\n  static instance(offset) {\n    return offset === 0 ? FixedOffsetZone.utcInstance : new FixedOffsetZone(offset);\n  }\n\n  /**\n   * Get an instance of FixedOffsetZone from a UTC offset string, like \"UTC+6\"\n   * @param {string} s - The offset string to parse\n   * @example FixedOffsetZone.parseSpecifier(\"UTC+6\")\n   * @example FixedOffsetZone.parseSpecifier(\"UTC+06\")\n   * @example FixedOffsetZone.parseSpecifier(\"UTC-6:00\")\n   * @return {FixedOffsetZone}\n   */\n  static parseSpecifier(s) {\n    if (s) {\n      const r = s.match(/^utc(?:([+-]\\d{1,2})(?::(\\d{2}))?)?$/i);\n      if (r) {\n        return new FixedOffsetZone(signedOffset(r[1], r[2]));\n      }\n    }\n    return null;\n  }\n\n  constructor(offset) {\n    super();\n    /** @private **/\n    this.fixed = offset;\n  }\n\n  /**\n   * The type of zone. `fixed` for all instances of `FixedOffsetZone`.\n   * @override\n   * @type {string}\n   */\n  get type() {\n    return \"fixed\";\n  }\n\n  /**\n   * The name of this zone.\n   * All fixed zones' names always start with \"UTC\" (plus optional offset)\n   * @override\n   * @type {string}\n   */\n  get name() {\n    return this.fixed === 0 ? \"UTC\" : `UTC${formatOffset(this.fixed, \"narrow\")}`;\n  }\n\n  /**\n   * The IANA name of this zone, i.e. `Etc/UTC` or `Etc/GMT+/-nn`\n   *\n   * @override\n   * @type {string}\n   */\n  get ianaName() {\n    if (this.fixed === 0) {\n      return \"Etc/UTC\";\n    } else {\n      return `Etc/GMT${formatOffset(-this.fixed, \"narrow\")}`;\n    }\n  }\n\n  /**\n   * Returns the offset's common name at the specified timestamp.\n   *\n   * For fixed offset zones this equals to the zone name.\n   * @override\n   */\n  offsetName() {\n    return this.name;\n  }\n\n  /**\n   * Returns the offset's value as a string\n   * @override\n   * @param {number} ts - Epoch milliseconds for which to get the offset\n   * @param {string} format - What style of offset to return.\n   *                          Accepts 'narrow', 'short', or 'techie'. Returning '+6', '+06:00', or '+0600' respectively\n   * @return {string}\n   */\n  formatOffset(ts, format) {\n    return formatOffset(this.fixed, format);\n  }\n\n  /**\n   * Returns whether the offset is known to be fixed for the whole year:\n   * Always returns true for all fixed offset zones.\n   * @override\n   * @type {boolean}\n   */\n  get isUniversal() {\n    return true;\n  }\n\n  /**\n   * Return the offset in minutes for this zone at the specified timestamp.\n   *\n   * For fixed offset zones, this is constant and does not depend on a timestamp.\n   * @override\n   * @return {number}\n   */\n  offset() {\n    return this.fixed;\n  }\n\n  /**\n   * Return whether this Zone is equal to another zone (i.e. also fixed and same offset)\n   * @override\n   * @param {Zone} otherZone - the zone to compare\n   * @return {boolean}\n   */\n  equals(otherZone) {\n    return otherZone.type === \"fixed\" && otherZone.fixed === this.fixed;\n  }\n\n  /**\n   * Return whether this Zone is valid:\n   * All fixed offset zones are valid.\n   * @override\n   * @type {boolean}\n   */\n  get isValid() {\n    return true;\n  }\n}\n", "import Zone from \"../zone.js\";\n\n/**\n * A zone that failed to parse. You should never need to instantiate this.\n * @implements {Zone}\n */\nexport default class InvalidZone extends Zone {\n  constructor(zoneName) {\n    super();\n    /**  @private */\n    this.zoneName = zoneName;\n  }\n\n  /** @override **/\n  get type() {\n    return \"invalid\";\n  }\n\n  /** @override **/\n  get name() {\n    return this.zoneName;\n  }\n\n  /** @override **/\n  get isUniversal() {\n    return false;\n  }\n\n  /** @override **/\n  offsetName() {\n    return null;\n  }\n\n  /** @override **/\n  formatOffset() {\n    return \"\";\n  }\n\n  /** @override **/\n  offset() {\n    return NaN;\n  }\n\n  /** @override **/\n  equals() {\n    return false;\n  }\n\n  /** @override **/\n  get isValid() {\n    return false;\n  }\n}\n", "/**\n * @private\n */\n\nimport Zone from \"../zone.js\";\nimport IANAZone from \"../zones/IANAZone.js\";\nimport FixedOffsetZone from \"../zones/fixedOffsetZone.js\";\nimport InvalidZone from \"../zones/invalidZone.js\";\n\nimport { isUndefined, isString, isNumber } from \"./util.js\";\nimport SystemZone from \"../zones/systemZone.js\";\n\nexport function normalizeZone(input, defaultZone) {\n  let offset;\n  if (isUndefined(input) || input === null) {\n    return defaultZone;\n  } else if (input instanceof Zone) {\n    return input;\n  } else if (isString(input)) {\n    const lowered = input.toLowerCase();\n    if (lowered === \"default\") return defaultZone;\n    else if (lowered === \"local\" || lowered === \"system\") return SystemZone.instance;\n    else if (lowered === \"utc\" || lowered === \"gmt\") return FixedOffsetZone.utcInstance;\n    else return FixedOffsetZone.parseSpecifier(lowered) || IANAZone.create(input);\n  } else if (isNumber(input)) {\n    return FixedOffsetZone.instance(input);\n  } else if (typeof input === \"object\" && \"offset\" in input && typeof input.offset === \"function\") {\n    // This is dumb, but the instanceof check above doesn't seem to really work\n    // so we're duck checking it\n    return input;\n  } else {\n    return new InvalidZone(input);\n  }\n}\n", "const numberingSystems = {\n  arab: \"[\\u0660-\\u0669]\",\n  arabext: \"[\\u06F0-\\u06F9]\",\n  bali: \"[\\u1B50-\\u1B59]\",\n  beng: \"[\\u09E6-\\u09EF]\",\n  deva: \"[\\u0966-\\u096F]\",\n  fullwide: \"[\\uFF10-\\uFF19]\",\n  gujr: \"[\\u0AE6-\\u0AEF]\",\n  hanidec: \"[〇|一|二|三|四|五|六|七|八|九]\",\n  khmr: \"[\\u17E0-\\u17E9]\",\n  knda: \"[\\u0CE6-\\u0CEF]\",\n  laoo: \"[\\u0ED0-\\u0ED9]\",\n  limb: \"[\\u1946-\\u194F]\",\n  mlym: \"[\\u0D66-\\u0D6F]\",\n  mong: \"[\\u1810-\\u1819]\",\n  mymr: \"[\\u1040-\\u1049]\",\n  orya: \"[\\u0B66-\\u0B6F]\",\n  tamldec: \"[\\u0BE6-\\u0BEF]\",\n  telu: \"[\\u0C66-\\u0C6F]\",\n  thai: \"[\\u0E50-\\u0E59]\",\n  tibt: \"[\\u0F20-\\u0F29]\",\n  latn: \"\\\\d\",\n};\n\nconst numberingSystemsUTF16 = {\n  arab: [1632, 1641],\n  arabext: [1776, 1785],\n  bali: [6992, 7001],\n  beng: [2534, 2543],\n  deva: [2406, 2415],\n  fullwide: [65296, 65303],\n  gujr: [2790, 2799],\n  khmr: [6112, 6121],\n  knda: [3302, 3311],\n  laoo: [3792, 3801],\n  limb: [6470, 6479],\n  mlym: [3430, 3439],\n  mong: [6160, 6169],\n  mymr: [4160, 4169],\n  orya: [2918, 2927],\n  tamldec: [3046, 3055],\n  telu: [3174, 3183],\n  thai: [3664, 3673],\n  tibt: [3872, 3881],\n};\n\nconst hanidecChars = numberingSystems.hanidec.replace(/[\\[|\\]]/g, \"\").split(\"\");\n\nexport function parseDigits(str) {\n  let value = parseInt(str, 10);\n  if (isNaN(value)) {\n    value = \"\";\n    for (let i = 0; i < str.length; i++) {\n      const code = str.charCodeAt(i);\n\n      if (str[i].search(numberingSystems.hanidec) !== -1) {\n        value += hanidecChars.indexOf(str[i]);\n      } else {\n        for (const key in numberingSystemsUTF16) {\n          const [min, max] = numberingSystemsUTF16[key];\n          if (code >= min && code <= max) {\n            value += code - min;\n          }\n        }\n      }\n    }\n    return parseInt(value, 10);\n  } else {\n    return value;\n  }\n}\n\n// cache of {numberingSystem: {append: regex}}\nconst digitRegexCache = new Map();\nexport function resetDigitRegexCache() {\n  digitRegexCache.clear();\n}\n\nexport function digitRegex({ numberingSystem }, append = \"\") {\n  const ns = numberingSystem || \"latn\";\n\n  let appendCache = digitRegexCache.get(ns);\n  if (appendCache === undefined) {\n    appendCache = new Map();\n    digitRegexCache.set(ns, appendCache);\n  }\n  let regex = appendCache.get(append);\n  if (regex === undefined) {\n    regex = new RegExp(`${numberingSystems[ns]}${append}`);\n    appendCache.set(append, regex);\n  }\n\n  return regex;\n}\n", "import SystemZone from \"./zones/systemZone.js\";\nimport IANAZone from \"./zones/IANAZone.js\";\nimport Locale from \"./impl/locale.js\";\nimport DateTime from \"./datetime.js\";\n\nimport { normalizeZone } from \"./impl/zoneUtil.js\";\nimport { validateWeekSettings } from \"./impl/util.js\";\nimport { resetDigitRegexCache } from \"./impl/digits.js\";\n\nlet now = () => Date.now(),\n  defaultZone = \"system\",\n  defaultLocale = null,\n  defaultNumberingSystem = null,\n  defaultOutputCalendar = null,\n  twoDigitCutoffYear = 60,\n  throwOnInvalid,\n  defaultWeekSettings = null;\n\n/**\n * Settings contains static getters and setters that control <PERSON><PERSON>'s overall behavior. Luxon is a simple library with few options, but the ones it does have live here.\n */\nexport default class Settings {\n  /**\n   * Get the callback for returning the current timestamp.\n   * @type {function}\n   */\n  static get now() {\n    return now;\n  }\n\n  /**\n   * Set the callback for returning the current timestamp.\n   * The function should return a number, which will be interpreted as an Epoch millisecond count\n   * @type {function}\n   * @example Settings.now = () => Date.now() + 3000 // pretend it is 3 seconds in the future\n   * @example Settings.now = () => 0 // always pretend it's Jan 1, 1970 at midnight in UTC time\n   */\n  static set now(n) {\n    now = n;\n  }\n\n  /**\n   * Set the default time zone to create DateTimes in. Does not affect existing instances.\n   * Use the value \"system\" to reset this value to the system's time zone.\n   * @type {string}\n   */\n  static set defaultZone(zone) {\n    defaultZone = zone;\n  }\n\n  /**\n   * Get the default time zone object currently used to create DateTimes. Does not affect existing instances.\n   * The default value is the system's time zone (the one set on the machine that runs this code).\n   * @type {Zone}\n   */\n  static get defaultZone() {\n    return normalizeZone(defaultZone, SystemZone.instance);\n  }\n\n  /**\n   * Get the default locale to create DateTimes with. Does not affect existing instances.\n   * @type {string}\n   */\n  static get defaultLocale() {\n    return defaultLocale;\n  }\n\n  /**\n   * Set the default locale to create DateTimes with. Does not affect existing instances.\n   * @type {string}\n   */\n  static set defaultLocale(locale) {\n    defaultLocale = locale;\n  }\n\n  /**\n   * Get the default numbering system to create DateTimes with. Does not affect existing instances.\n   * @type {string}\n   */\n  static get defaultNumberingSystem() {\n    return defaultNumberingSystem;\n  }\n\n  /**\n   * Set the default numbering system to create DateTimes with. Does not affect existing instances.\n   * @type {string}\n   */\n  static set defaultNumberingSystem(numberingSystem) {\n    defaultNumberingSystem = numberingSystem;\n  }\n\n  /**\n   * Get the default output calendar to create DateTimes with. Does not affect existing instances.\n   * @type {string}\n   */\n  static get defaultOutputCalendar() {\n    return defaultOutputCalendar;\n  }\n\n  /**\n   * Set the default output calendar to create DateTimes with. Does not affect existing instances.\n   * @type {string}\n   */\n  static set defaultOutputCalendar(outputCalendar) {\n    defaultOutputCalendar = outputCalendar;\n  }\n\n  /**\n   * @typedef {Object} WeekSettings\n   * @property {number} firstDay\n   * @property {number} minimalDays\n   * @property {number[]} weekend\n   */\n\n  /**\n   * @return {WeekSettings|null}\n   */\n  static get defaultWeekSettings() {\n    return defaultWeekSettings;\n  }\n\n  /**\n   * Allows overriding the default locale week settings, i.e. the start of the week, the weekend and\n   * how many days are required in the first week of a year.\n   * Does not affect existing instances.\n   *\n   * @param {WeekSettings|null} weekSettings\n   */\n  static set defaultWeekSettings(weekSettings) {\n    defaultWeekSettings = validateWeekSettings(weekSettings);\n  }\n\n  /**\n   * Get the cutoff year for whether a 2-digit year string is interpreted in the current or previous century. Numbers higher than the cutoff will be considered to mean 19xx and numbers lower or equal to the cutoff will be considered 20xx.\n   * @type {number}\n   */\n  static get twoDigitCutoffYear() {\n    return twoDigitCutoffYear;\n  }\n\n  /**\n   * Set the cutoff year for whether a 2-digit year string is interpreted in the current or previous century. Numbers higher than the cutoff will be considered to mean 19xx and numbers lower or equal to the cutoff will be considered 20xx.\n   * @type {number}\n   * @example Settings.twoDigitCutoffYear = 0 // all 'yy' are interpreted as 20th century\n   * @example Settings.twoDigitCutoffYear = 99 // all 'yy' are interpreted as 21st century\n   * @example Settings.twoDigitCutoffYear = 50 // '49' -> 2049; '50' -> 1950\n   * @example Settings.twoDigitCutoffYear = 1950 // interpreted as 50\n   * @example Settings.twoDigitCutoffYear = 2050 // ALSO interpreted as 50\n   */\n  static set twoDigitCutoffYear(cutoffYear) {\n    twoDigitCutoffYear = cutoffYear % 100;\n  }\n\n  /**\n   * Get whether Luxon will throw when it encounters invalid DateTimes, Durations, or Intervals\n   * @type {boolean}\n   */\n  static get throwOnInvalid() {\n    return throwOnInvalid;\n  }\n\n  /**\n   * Set whether Luxon will throw when it encounters invalid DateTimes, Durations, or Intervals\n   * @type {boolean}\n   */\n  static set throwOnInvalid(t) {\n    throwOnInvalid = t;\n  }\n\n  /**\n   * Reset Luxon's global caches. Should only be necessary in testing scenarios.\n   * @return {void}\n   */\n  static resetCaches() {\n    Locale.resetCache();\n    IANAZone.resetCache();\n    DateTime.resetCache();\n    resetDigitRegexCache();\n  }\n}\n", "export default class Invalid {\n  constructor(reason, explanation) {\n    this.reason = reason;\n    this.explanation = explanation;\n  }\n\n  toMessage() {\n    if (this.explanation) {\n      return `${this.reason}: ${this.explanation}`;\n    } else {\n      return this.reason;\n    }\n  }\n}\n", "import {\n  integerBetween,\n  isLeapYear,\n  timeObject,\n  daysInYear,\n  daysInMonth,\n  weeksInWeekYear,\n  isInteger,\n  isUndefined,\n} from \"./util.js\";\nimport Invalid from \"./invalid.js\";\nimport { ConflictingSpecificationError } from \"../errors.js\";\n\nconst nonLeapLadder = [0, 31, 59, 90, 120, 151, 181, 212, 243, 273, 304, 334],\n  leapLadder = [0, 31, 60, 91, 121, 152, 182, 213, 244, 274, 305, 335];\n\nfunction unitOutOfRange(unit, value) {\n  return new Invalid(\n    \"unit out of range\",\n    `you specified ${value} (of type ${typeof value}) as a ${unit}, which is invalid`\n  );\n}\n\nexport function dayOfWeek(year, month, day) {\n  const d = new Date(Date.UTC(year, month - 1, day));\n\n  if (year < 100 && year >= 0) {\n    d.setUTCFullYear(d.getUTCFullYear() - 1900);\n  }\n\n  const js = d.getUTCDay();\n\n  return js === 0 ? 7 : js;\n}\n\nfunction computeOrdinal(year, month, day) {\n  return day + (isLeapYear(year) ? leapLadder : nonLeapLadder)[month - 1];\n}\n\nfunction uncomputeOrdinal(year, ordinal) {\n  const table = isLeapYear(year) ? leapLadder : nonLeapLadder,\n    month0 = table.findIndex((i) => i < ordinal),\n    day = ordinal - table[month0];\n  return { month: month0 + 1, day };\n}\n\nexport function isoWeekdayToLocal(isoWeekday, startOfWeek) {\n  return ((isoWeekday - startOfWeek + 7) % 7) + 1;\n}\n\n/**\n * @private\n */\n\nexport function gregorianToWeek(gregObj, minDaysInFirstWeek = 4, startOfWeek = 1) {\n  const { year, month, day } = gregObj,\n    ordinal = computeOrdinal(year, month, day),\n    weekday = isoWeekdayToLocal(dayOfWeek(year, month, day), startOfWeek);\n\n  let weekNumber = Math.floor((ordinal - weekday + 14 - minDaysInFirstWeek) / 7),\n    weekYear;\n\n  if (weekNumber < 1) {\n    weekYear = year - 1;\n    weekNumber = weeksInWeekYear(weekYear, minDaysInFirstWeek, startOfWeek);\n  } else if (weekNumber > weeksInWeekYear(year, minDaysInFirstWeek, startOfWeek)) {\n    weekYear = year + 1;\n    weekNumber = 1;\n  } else {\n    weekYear = year;\n  }\n\n  return { weekYear, weekNumber, weekday, ...timeObject(gregObj) };\n}\n\nexport function weekToGregorian(weekData, minDaysInFirstWeek = 4, startOfWeek = 1) {\n  const { weekYear, weekNumber, weekday } = weekData,\n    weekdayOfJan4 = isoWeekdayToLocal(dayOfWeek(weekYear, 1, minDaysInFirstWeek), startOfWeek),\n    yearInDays = daysInYear(weekYear);\n\n  let ordinal = weekNumber * 7 + weekday - weekdayOfJan4 - 7 + minDaysInFirstWeek,\n    year;\n\n  if (ordinal < 1) {\n    year = weekYear - 1;\n    ordinal += daysInYear(year);\n  } else if (ordinal > yearInDays) {\n    year = weekYear + 1;\n    ordinal -= daysInYear(weekYear);\n  } else {\n    year = weekYear;\n  }\n\n  const { month, day } = uncomputeOrdinal(year, ordinal);\n  return { year, month, day, ...timeObject(weekData) };\n}\n\nexport function gregorianToOrdinal(gregData) {\n  const { year, month, day } = gregData;\n  const ordinal = computeOrdinal(year, month, day);\n  return { year, ordinal, ...timeObject(gregData) };\n}\n\nexport function ordinalToGregorian(ordinalData) {\n  const { year, ordinal } = ordinalData;\n  const { month, day } = uncomputeOrdinal(year, ordinal);\n  return { year, month, day, ...timeObject(ordinalData) };\n}\n\n/**\n * Check if local week units like localWeekday are used in obj.\n * If so, validates that they are not mixed with ISO week units and then copies them to the normal week unit properties.\n * Modifies obj in-place!\n * @param obj the object values\n */\nexport function usesLocalWeekValues(obj, loc) {\n  const hasLocaleWeekData =\n    !isUndefined(obj.localWeekday) ||\n    !isUndefined(obj.localWeekNumber) ||\n    !isUndefined(obj.localWeekYear);\n  if (hasLocaleWeekData) {\n    const hasIsoWeekData =\n      !isUndefined(obj.weekday) || !isUndefined(obj.weekNumber) || !isUndefined(obj.weekYear);\n\n    if (hasIsoWeekData) {\n      throw new ConflictingSpecificationError(\n        \"Cannot mix locale-based week fields with ISO-based week fields\"\n      );\n    }\n    if (!isUndefined(obj.localWeekday)) obj.weekday = obj.localWeekday;\n    if (!isUndefined(obj.localWeekNumber)) obj.weekNumber = obj.localWeekNumber;\n    if (!isUndefined(obj.localWeekYear)) obj.weekYear = obj.localWeekYear;\n    delete obj.localWeekday;\n    delete obj.localWeekNumber;\n    delete obj.localWeekYear;\n    return {\n      minDaysInFirstWeek: loc.getMinDaysInFirstWeek(),\n      startOfWeek: loc.getStartOfWeek(),\n    };\n  } else {\n    return { minDaysInFirstWeek: 4, startOfWeek: 1 };\n  }\n}\n\nexport function hasInvalidWeekData(obj, minDaysInFirstWeek = 4, startOfWeek = 1) {\n  const validYear = isInteger(obj.weekYear),\n    validWeek = integerBetween(\n      obj.weekNumber,\n      1,\n      weeksInWeekYear(obj.weekYear, minDaysInFirstWeek, startOfWeek)\n    ),\n    validWeekday = integerBetween(obj.weekday, 1, 7);\n\n  if (!validYear) {\n    return unitOutOfRange(\"weekYear\", obj.weekYear);\n  } else if (!validWeek) {\n    return unitOutOfRange(\"week\", obj.weekNumber);\n  } else if (!validWeekday) {\n    return unitOutOfRange(\"weekday\", obj.weekday);\n  } else return false;\n}\n\nexport function hasInvalidOrdinalData(obj) {\n  const validYear = isInteger(obj.year),\n    validOrdinal = integerBetween(obj.ordinal, 1, daysInYear(obj.year));\n\n  if (!validYear) {\n    return unitOutOfRange(\"year\", obj.year);\n  } else if (!validOrdinal) {\n    return unitOutOfRange(\"ordinal\", obj.ordinal);\n  } else return false;\n}\n\nexport function hasInvalidGregorianData(obj) {\n  const validYear = isInteger(obj.year),\n    validMonth = integerBetween(obj.month, 1, 12),\n    validDay = integerBetween(obj.day, 1, daysInMonth(obj.year, obj.month));\n\n  if (!validYear) {\n    return unitOutOfRange(\"year\", obj.year);\n  } else if (!validMonth) {\n    return unitOutOfRange(\"month\", obj.month);\n  } else if (!validDay) {\n    return unitOutOfRange(\"day\", obj.day);\n  } else return false;\n}\n\nexport function hasInvalidTimeData(obj) {\n  const { hour, minute, second, millisecond } = obj;\n  const validHour =\n      integerBetween(hour, 0, 23) ||\n      (hour === 24 && minute === 0 && second === 0 && millisecond === 0),\n    validMinute = integerBetween(minute, 0, 59),\n    validSecond = integerBetween(second, 0, 59),\n    validMillisecond = integerBetween(millisecond, 0, 999);\n\n  if (!validHour) {\n    return unitOutOfRange(\"hour\", hour);\n  } else if (!validMinute) {\n    return unitOutOfRange(\"minute\", minute);\n  } else if (!validSecond) {\n    return unitOutOfRange(\"second\", second);\n  } else if (!validMillisecond) {\n    return unitOutOfRange(\"millisecond\", millisecond);\n  } else return false;\n}\n", "/*\n  This is just a junk drawer, containing anything used across multiple classes.\n  Because <PERSON>xon is small(ish), this should stay small and we won't worry about splitting\n  it up into, say, parsingUtil.js and basicUtil.js and so on. But they are divided up by feature area.\n*/\n\nimport { InvalidArgumentError } from \"../errors.js\";\nimport Settings from \"../settings.js\";\nimport { dayOfWeek, isoWeekdayToLocal } from \"./conversions.js\";\n\n/**\n * @private\n */\n\n// TYPES\n\nexport function isUndefined(o) {\n  return typeof o === \"undefined\";\n}\n\nexport function isNumber(o) {\n  return typeof o === \"number\";\n}\n\nexport function isInteger(o) {\n  return typeof o === \"number\" && o % 1 === 0;\n}\n\nexport function isString(o) {\n  return typeof o === \"string\";\n}\n\nexport function isDate(o) {\n  return Object.prototype.toString.call(o) === \"[object Date]\";\n}\n\n// CAPABILITIES\n\nexport function hasRelative() {\n  try {\n    return typeof Intl !== \"undefined\" && !!Intl.RelativeTimeFormat;\n  } catch (e) {\n    return false;\n  }\n}\n\nexport function hasLocaleWeekInfo() {\n  try {\n    return (\n      typeof Intl !== \"undefined\" &&\n      !!Intl.Locale &&\n      (\"weekInfo\" in Intl.Locale.prototype || \"getWeekInfo\" in Intl.Locale.prototype)\n    );\n  } catch (e) {\n    return false;\n  }\n}\n\n// OBJECTS AND ARRAYS\n\nexport function maybeArray(thing) {\n  return Array.isArray(thing) ? thing : [thing];\n}\n\nexport function bestBy(arr, by, compare) {\n  if (arr.length === 0) {\n    return undefined;\n  }\n  return arr.reduce((best, next) => {\n    const pair = [by(next), next];\n    if (!best) {\n      return pair;\n    } else if (compare(best[0], pair[0]) === best[0]) {\n      return best;\n    } else {\n      return pair;\n    }\n  }, null)[1];\n}\n\nexport function pick(obj, keys) {\n  return keys.reduce((a, k) => {\n    a[k] = obj[k];\n    return a;\n  }, {});\n}\n\nexport function hasOwnProperty(obj, prop) {\n  return Object.prototype.hasOwnProperty.call(obj, prop);\n}\n\nexport function validateWeekSettings(settings) {\n  if (settings == null) {\n    return null;\n  } else if (typeof settings !== \"object\") {\n    throw new InvalidArgumentError(\"Week settings must be an object\");\n  } else {\n    if (\n      !integerBetween(settings.firstDay, 1, 7) ||\n      !integerBetween(settings.minimalDays, 1, 7) ||\n      !Array.isArray(settings.weekend) ||\n      settings.weekend.some((v) => !integerBetween(v, 1, 7))\n    ) {\n      throw new InvalidArgumentError(\"Invalid week settings\");\n    }\n    return {\n      firstDay: settings.firstDay,\n      minimalDays: settings.minimalDays,\n      weekend: Array.from(settings.weekend),\n    };\n  }\n}\n\n// NUMBERS AND STRINGS\n\nexport function integerBetween(thing, bottom, top) {\n  return isInteger(thing) && thing >= bottom && thing <= top;\n}\n\n// x % n but takes the sign of n instead of x\nexport function floorMod(x, n) {\n  return x - n * Math.floor(x / n);\n}\n\nexport function padStart(input, n = 2) {\n  const isNeg = input < 0;\n  let padded;\n  if (isNeg) {\n    padded = \"-\" + (\"\" + -input).padStart(n, \"0\");\n  } else {\n    padded = (\"\" + input).padStart(n, \"0\");\n  }\n  return padded;\n}\n\nexport function parseInteger(string) {\n  if (isUndefined(string) || string === null || string === \"\") {\n    return undefined;\n  } else {\n    return parseInt(string, 10);\n  }\n}\n\nexport function parseFloating(string) {\n  if (isUndefined(string) || string === null || string === \"\") {\n    return undefined;\n  } else {\n    return parseFloat(string);\n  }\n}\n\nexport function parseMillis(fraction) {\n  // Return undefined (instead of 0) in these cases, where fraction is not set\n  if (isUndefined(fraction) || fraction === null || fraction === \"\") {\n    return undefined;\n  } else {\n    const f = parseFloat(\"0.\" + fraction) * 1000;\n    return Math.floor(f);\n  }\n}\n\nexport function roundTo(number, digits, towardZero = false) {\n  const factor = 10 ** digits,\n    rounder = towardZero ? Math.trunc : Math.round;\n  return rounder(number * factor) / factor;\n}\n\n// DATE BASICS\n\nexport function isLeapYear(year) {\n  return year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0);\n}\n\nexport function daysInYear(year) {\n  return isLeapYear(year) ? 366 : 365;\n}\n\nexport function daysInMonth(year, month) {\n  const modMonth = floorMod(month - 1, 12) + 1,\n    modYear = year + (month - modMonth) / 12;\n\n  if (modMonth === 2) {\n    return isLeapYear(modYear) ? 29 : 28;\n  } else {\n    return [31, null, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31][modMonth - 1];\n  }\n}\n\n// convert a calendar object to a local timestamp (epoch, but with the offset baked in)\nexport function objToLocalTS(obj) {\n  let d = Date.UTC(\n    obj.year,\n    obj.month - 1,\n    obj.day,\n    obj.hour,\n    obj.minute,\n    obj.second,\n    obj.millisecond\n  );\n\n  // for legacy reasons, years between 0 and 99 are interpreted as 19XX; revert that\n  if (obj.year < 100 && obj.year >= 0) {\n    d = new Date(d);\n    // set the month and day again, this is necessary because year 2000 is a leap year, but year 100 is not\n    // so if obj.year is in 99, but obj.day makes it roll over into year 100,\n    // the calculations done by Date.UTC are using year 2000 - which is incorrect\n    d.setUTCFullYear(obj.year, obj.month - 1, obj.day);\n  }\n  return +d;\n}\n\n// adapted from moment.js: https://github.com/moment/moment/blob/000ac1800e620f770f4eb31b5ae908f6167b0ab2/src/lib/units/week-calendar-utils.js\nfunction firstWeekOffset(year, minDaysInFirstWeek, startOfWeek) {\n  const fwdlw = isoWeekdayToLocal(dayOfWeek(year, 1, minDaysInFirstWeek), startOfWeek);\n  return -fwdlw + minDaysInFirstWeek - 1;\n}\n\nexport function weeksInWeekYear(weekYear, minDaysInFirstWeek = 4, startOfWeek = 1) {\n  const weekOffset = firstWeekOffset(weekYear, minDaysInFirstWeek, startOfWeek);\n  const weekOffsetNext = firstWeekOffset(weekYear + 1, minDaysInFirstWeek, startOfWeek);\n  return (daysInYear(weekYear) - weekOffset + weekOffsetNext) / 7;\n}\n\nexport function untruncateYear(year) {\n  if (year > 99) {\n    return year;\n  } else return year > Settings.twoDigitCutoffYear ? 1900 + year : 2000 + year;\n}\n\n// PARSING\n\nexport function parseZoneInfo(ts, offsetFormat, locale, timeZone = null) {\n  const date = new Date(ts),\n    intlOpts = {\n      hourCycle: \"h23\",\n      year: \"numeric\",\n      month: \"2-digit\",\n      day: \"2-digit\",\n      hour: \"2-digit\",\n      minute: \"2-digit\",\n    };\n\n  if (timeZone) {\n    intlOpts.timeZone = timeZone;\n  }\n\n  const modified = { timeZoneName: offsetFormat, ...intlOpts };\n\n  const parsed = new Intl.DateTimeFormat(locale, modified)\n    .formatToParts(date)\n    .find((m) => m.type.toLowerCase() === \"timezonename\");\n  return parsed ? parsed.value : null;\n}\n\n// signedOffset('-5', '30') -> -330\nexport function signedOffset(offHourStr, offMinuteStr) {\n  let offHour = parseInt(offHourStr, 10);\n\n  // don't || this because we want to preserve -0\n  if (Number.isNaN(offHour)) {\n    offHour = 0;\n  }\n\n  const offMin = parseInt(offMinuteStr, 10) || 0,\n    offMinSigned = offHour < 0 || Object.is(offHour, -0) ? -offMin : offMin;\n  return offHour * 60 + offMinSigned;\n}\n\n// COERCION\n\nexport function asNumber(value) {\n  const numericValue = Number(value);\n  if (typeof value === \"boolean\" || value === \"\" || Number.isNaN(numericValue))\n    throw new InvalidArgumentError(`Invalid unit value ${value}`);\n  return numericValue;\n}\n\nexport function normalizeObject(obj, normalizer) {\n  const normalized = {};\n  for (const u in obj) {\n    if (hasOwnProperty(obj, u)) {\n      const v = obj[u];\n      if (v === undefined || v === null) continue;\n      normalized[normalizer(u)] = asNumber(v);\n    }\n  }\n  return normalized;\n}\n\n/**\n * Returns the offset's value as a string\n * @param {number} ts - Epoch milliseconds for which to get the offset\n * @param {string} format - What style of offset to return.\n *                          Accepts 'narrow', 'short', or 'techie'. Returning '+6', '+06:00', or '+0600' respectively\n * @return {string}\n */\nexport function formatOffset(offset, format) {\n  const hours = Math.trunc(Math.abs(offset / 60)),\n    minutes = Math.trunc(Math.abs(offset % 60)),\n    sign = offset >= 0 ? \"+\" : \"-\";\n\n  switch (format) {\n    case \"short\":\n      return `${sign}${padStart(hours, 2)}:${padStart(minutes, 2)}`;\n    case \"narrow\":\n      return `${sign}${hours}${minutes > 0 ? `:${minutes}` : \"\"}`;\n    case \"techie\":\n      return `${sign}${padStart(hours, 2)}${padStart(minutes, 2)}`;\n    default:\n      throw new RangeError(`Value format ${format} is out of range for property format`);\n  }\n}\n\nexport function timeObject(obj) {\n  return pick(obj, [\"hour\", \"minute\", \"second\", \"millisecond\"]);\n}\n", "import * as Formats from \"./formats.js\";\nimport { pick } from \"./util.js\";\n\nfunction stringify(obj) {\n  return JSON.stringify(obj, Object.keys(obj).sort());\n}\n\n/**\n * @private\n */\n\nexport const monthsLong = [\n  \"January\",\n  \"February\",\n  \"March\",\n  \"April\",\n  \"May\",\n  \"June\",\n  \"July\",\n  \"August\",\n  \"September\",\n  \"October\",\n  \"November\",\n  \"December\",\n];\n\nexport const monthsShort = [\n  \"Jan\",\n  \"Feb\",\n  \"Mar\",\n  \"Apr\",\n  \"May\",\n  \"Jun\",\n  \"Jul\",\n  \"Aug\",\n  \"Sep\",\n  \"Oct\",\n  \"Nov\",\n  \"Dec\",\n];\n\nexport const monthsNarrow = [\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"];\n\nexport function months(length) {\n  switch (length) {\n    case \"narrow\":\n      return [...monthsNarrow];\n    case \"short\":\n      return [...monthsShort];\n    case \"long\":\n      return [...monthsLong];\n    case \"numeric\":\n      return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\"];\n    case \"2-digit\":\n      return [\"01\", \"02\", \"03\", \"04\", \"05\", \"06\", \"07\", \"08\", \"09\", \"10\", \"11\", \"12\"];\n    default:\n      return null;\n  }\n}\n\nexport const weekdaysLong = [\n  \"Monday\",\n  \"Tuesday\",\n  \"Wednesday\",\n  \"Thursday\",\n  \"Friday\",\n  \"Saturday\",\n  \"Sunday\",\n];\n\nexport const weekdaysShort = [\"Mon\", \"Tue\", \"Wed\", \"Thu\", \"Fri\", \"Sat\", \"Sun\"];\n\nexport const weekdaysNarrow = [\"M\", \"T\", \"W\", \"T\", \"F\", \"S\", \"S\"];\n\nexport function weekdays(length) {\n  switch (length) {\n    case \"narrow\":\n      return [...weekdaysNarrow];\n    case \"short\":\n      return [...weekdaysShort];\n    case \"long\":\n      return [...weekdaysLong];\n    case \"numeric\":\n      return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"];\n    default:\n      return null;\n  }\n}\n\nexport const meridiems = [\"AM\", \"PM\"];\n\nexport const erasLong = [\"Before Christ\", \"Anno Domini\"];\n\nexport const erasShort = [\"BC\", \"AD\"];\n\nexport const erasNarrow = [\"B\", \"A\"];\n\nexport function eras(length) {\n  switch (length) {\n    case \"narrow\":\n      return [...erasNarrow];\n    case \"short\":\n      return [...erasShort];\n    case \"long\":\n      return [...erasLong];\n    default:\n      return null;\n  }\n}\n\nexport function meridiemForDateTime(dt) {\n  return meridiems[dt.hour < 12 ? 0 : 1];\n}\n\nexport function weekdayForDateTime(dt, length) {\n  return weekdays(length)[dt.weekday - 1];\n}\n\nexport function monthForDateTime(dt, length) {\n  return months(length)[dt.month - 1];\n}\n\nexport function eraForDateTime(dt, length) {\n  return eras(length)[dt.year < 0 ? 0 : 1];\n}\n\nexport function formatRelativeTime(unit, count, numeric = \"always\", narrow = false) {\n  const units = {\n    years: [\"year\", \"yr.\"],\n    quarters: [\"quarter\", \"qtr.\"],\n    months: [\"month\", \"mo.\"],\n    weeks: [\"week\", \"wk.\"],\n    days: [\"day\", \"day\", \"days\"],\n    hours: [\"hour\", \"hr.\"],\n    minutes: [\"minute\", \"min.\"],\n    seconds: [\"second\", \"sec.\"],\n  };\n\n  const lastable = [\"hours\", \"minutes\", \"seconds\"].indexOf(unit) === -1;\n\n  if (numeric === \"auto\" && lastable) {\n    const isDay = unit === \"days\";\n    switch (count) {\n      case 1:\n        return isDay ? \"tomorrow\" : `next ${units[unit][0]}`;\n      case -1:\n        return isDay ? \"yesterday\" : `last ${units[unit][0]}`;\n      case 0:\n        return isDay ? \"today\" : `this ${units[unit][0]}`;\n      default: // fall through\n    }\n  }\n\n  const isInPast = Object.is(count, -0) || count < 0,\n    fmtValue = Math.abs(count),\n    singular = fmtValue === 1,\n    lilUnits = units[unit],\n    fmtUnit = narrow\n      ? singular\n        ? lilUnits[1]\n        : lilUnits[2] || lilUnits[1]\n      : singular\n      ? units[unit][0]\n      : unit;\n  return isInPast ? `${fmtValue} ${fmtUnit} ago` : `in ${fmtValue} ${fmtUnit}`;\n}\n\nexport function formatString(knownFormat) {\n  // these all have the offsets removed because we don't have access to them\n  // without all the intl stuff this is backfilling\n  const filtered = pick(knownFormat, [\n      \"weekday\",\n      \"era\",\n      \"year\",\n      \"month\",\n      \"day\",\n      \"hour\",\n      \"minute\",\n      \"second\",\n      \"timeZoneName\",\n      \"hourCycle\",\n    ]),\n    key = stringify(filtered),\n    dateTimeHuge = \"EEEE, LLLL d, yyyy, h:mm a\";\n  switch (key) {\n    case stringify(Formats.DATE_SHORT):\n      return \"M/d/yyyy\";\n    case stringify(Formats.DATE_MED):\n      return \"LLL d, yyyy\";\n    case stringify(Formats.DATE_MED_WITH_WEEKDAY):\n      return \"EEE, LLL d, yyyy\";\n    case stringify(Formats.DATE_FULL):\n      return \"LLLL d, yyyy\";\n    case stringify(Formats.DATE_HUGE):\n      return \"EEEE, LLLL d, yyyy\";\n    case stringify(Formats.TIME_SIMPLE):\n      return \"h:mm a\";\n    case stringify(Formats.TIME_WITH_SECONDS):\n      return \"h:mm:ss a\";\n    case stringify(Formats.TIME_WITH_SHORT_OFFSET):\n      return \"h:mm a\";\n    case stringify(Formats.TIME_WITH_LONG_OFFSET):\n      return \"h:mm a\";\n    case stringify(Formats.TIME_24_SIMPLE):\n      return \"HH:mm\";\n    case stringify(Formats.TIME_24_WITH_SECONDS):\n      return \"HH:mm:ss\";\n    case stringify(Formats.TIME_24_WITH_SHORT_OFFSET):\n      return \"HH:mm\";\n    case stringify(Formats.TIME_24_WITH_LONG_OFFSET):\n      return \"HH:mm\";\n    case stringify(Formats.DATETIME_SHORT):\n      return \"M/d/yyyy, h:mm a\";\n    case stringify(Formats.DATETIME_MED):\n      return \"LLL d, yyyy, h:mm a\";\n    case stringify(Formats.DATETIME_FULL):\n      return \"LLLL d, yyyy, h:mm a\";\n    case stringify(Formats.DATETIME_HUGE):\n      return dateTimeHuge;\n    case stringify(Formats.DATETIME_SHORT_WITH_SECONDS):\n      return \"M/d/yyyy, h:mm:ss a\";\n    case stringify(Formats.DATETIME_MED_WITH_SECONDS):\n      return \"LLL d, yyyy, h:mm:ss a\";\n    case stringify(Formats.DATETIME_MED_WITH_WEEKDAY):\n      return \"EEE, d LLL yyyy, h:mm a\";\n    case stringify(Formats.DATETIME_FULL_WITH_SECONDS):\n      return \"LLLL d, yyyy, h:mm:ss a\";\n    case stringify(Formats.DATETIME_HUGE_WITH_SECONDS):\n      return \"EEEE, LLLL d, yyyy, h:mm:ss a\";\n    default:\n      return dateTimeHuge;\n  }\n}\n", "import * as English from \"./english.js\";\nimport * as Formats from \"./formats.js\";\nimport { padStart } from \"./util.js\";\n\nfunction stringifyTokens(splits, tokenToString) {\n  let s = \"\";\n  for (const token of splits) {\n    if (token.literal) {\n      s += token.val;\n    } else {\n      s += tokenToString(token.val);\n    }\n  }\n  return s;\n}\n\nconst macroTokenToFormatOpts = {\n  D: Formats.DATE_SHORT,\n  DD: Formats.DATE_MED,\n  DDD: Formats.DATE_FULL,\n  DDDD: Formats.DATE_HUGE,\n  t: Formats.TIME_SIMPLE,\n  tt: Formats.TIME_WITH_SECONDS,\n  ttt: Formats.TIME_WITH_SHORT_OFFSET,\n  tttt: Formats.TIME_WITH_LONG_OFFSET,\n  T: Formats.TIME_24_SIMPLE,\n  TT: Formats.TIME_24_WITH_SECONDS,\n  TTT: Formats.TIME_24_WITH_SHORT_OFFSET,\n  TTTT: Formats.TIME_24_WITH_LONG_OFFSET,\n  f: Formats.DATETIME_SHORT,\n  ff: Formats.DATETIME_MED,\n  fff: Formats.DATETIME_FULL,\n  ffff: Formats.DATETIME_HUGE,\n  F: Formats.DATETIME_SHORT_WITH_SECONDS,\n  FF: Formats.DATETIME_MED_WITH_SECONDS,\n  FFF: Formats.DATETIME_FULL_WITH_SECONDS,\n  FFFF: Formats.DATETIME_HUGE_WITH_SECONDS,\n};\n\n/**\n * @private\n */\n\nexport default class Formatter {\n  static create(locale, opts = {}) {\n    return new Formatter(locale, opts);\n  }\n\n  static parseFormat(fmt) {\n    // white-space is always considered a literal in user-provided formats\n    // the \" \" token has a special meaning (see unitForToken)\n\n    let current = null,\n      currentFull = \"\",\n      bracketed = false;\n    const splits = [];\n    for (let i = 0; i < fmt.length; i++) {\n      const c = fmt.charAt(i);\n      if (c === \"'\") {\n        if (currentFull.length > 0) {\n          splits.push({ literal: bracketed || /^\\s+$/.test(currentFull), val: currentFull });\n        }\n        current = null;\n        currentFull = \"\";\n        bracketed = !bracketed;\n      } else if (bracketed) {\n        currentFull += c;\n      } else if (c === current) {\n        currentFull += c;\n      } else {\n        if (currentFull.length > 0) {\n          splits.push({ literal: /^\\s+$/.test(currentFull), val: currentFull });\n        }\n        currentFull = c;\n        current = c;\n      }\n    }\n\n    if (currentFull.length > 0) {\n      splits.push({ literal: bracketed || /^\\s+$/.test(currentFull), val: currentFull });\n    }\n\n    return splits;\n  }\n\n  static macroTokenToFormatOpts(token) {\n    return macroTokenToFormatOpts[token];\n  }\n\n  constructor(locale, formatOpts) {\n    this.opts = formatOpts;\n    this.loc = locale;\n    this.systemLoc = null;\n  }\n\n  formatWithSystemDefault(dt, opts) {\n    if (this.systemLoc === null) {\n      this.systemLoc = this.loc.redefaultToSystem();\n    }\n    const df = this.systemLoc.dtFormatter(dt, { ...this.opts, ...opts });\n    return df.format();\n  }\n\n  dtFormatter(dt, opts = {}) {\n    return this.loc.dtFormatter(dt, { ...this.opts, ...opts });\n  }\n\n  formatDateTime(dt, opts) {\n    return this.dtFormatter(dt, opts).format();\n  }\n\n  formatDateTimeParts(dt, opts) {\n    return this.dtFormatter(dt, opts).formatToParts();\n  }\n\n  formatInterval(interval, opts) {\n    const df = this.dtFormatter(interval.start, opts);\n    return df.dtf.formatRange(interval.start.toJSDate(), interval.end.toJSDate());\n  }\n\n  resolvedOptions(dt, opts) {\n    return this.dtFormatter(dt, opts).resolvedOptions();\n  }\n\n  num(n, p = 0) {\n    // we get some perf out of doing this here, annoyingly\n    if (this.opts.forceSimple) {\n      return padStart(n, p);\n    }\n\n    const opts = { ...this.opts };\n\n    if (p > 0) {\n      opts.padTo = p;\n    }\n\n    return this.loc.numberFormatter(opts).format(n);\n  }\n\n  formatDateTimeFromString(dt, fmt) {\n    const knownEnglish = this.loc.listingMode() === \"en\",\n      useDateTimeFormatter = this.loc.outputCalendar && this.loc.outputCalendar !== \"gregory\",\n      string = (opts, extract) => this.loc.extract(dt, opts, extract),\n      formatOffset = (opts) => {\n        if (dt.isOffsetFixed && dt.offset === 0 && opts.allowZ) {\n          return \"Z\";\n        }\n\n        return dt.isValid ? dt.zone.formatOffset(dt.ts, opts.format) : \"\";\n      },\n      meridiem = () =>\n        knownEnglish\n          ? English.meridiemForDateTime(dt)\n          : string({ hour: \"numeric\", hourCycle: \"h12\" }, \"dayperiod\"),\n      month = (length, standalone) =>\n        knownEnglish\n          ? English.monthForDateTime(dt, length)\n          : string(standalone ? { month: length } : { month: length, day: \"numeric\" }, \"month\"),\n      weekday = (length, standalone) =>\n        knownEnglish\n          ? English.weekdayForDateTime(dt, length)\n          : string(\n              standalone ? { weekday: length } : { weekday: length, month: \"long\", day: \"numeric\" },\n              \"weekday\"\n            ),\n      maybeMacro = (token) => {\n        const formatOpts = Formatter.macroTokenToFormatOpts(token);\n        if (formatOpts) {\n          return this.formatWithSystemDefault(dt, formatOpts);\n        } else {\n          return token;\n        }\n      },\n      era = (length) =>\n        knownEnglish ? English.eraForDateTime(dt, length) : string({ era: length }, \"era\"),\n      tokenToString = (token) => {\n        // Where possible: https://cldr.unicode.org/translation/date-time/date-time-symbols\n        switch (token) {\n          // ms\n          case \"S\":\n            return this.num(dt.millisecond);\n          case \"u\":\n          // falls through\n          case \"SSS\":\n            return this.num(dt.millisecond, 3);\n          // seconds\n          case \"s\":\n            return this.num(dt.second);\n          case \"ss\":\n            return this.num(dt.second, 2);\n          // fractional seconds\n          case \"uu\":\n            return this.num(Math.floor(dt.millisecond / 10), 2);\n          case \"uuu\":\n            return this.num(Math.floor(dt.millisecond / 100));\n          // minutes\n          case \"m\":\n            return this.num(dt.minute);\n          case \"mm\":\n            return this.num(dt.minute, 2);\n          // hours\n          case \"h\":\n            return this.num(dt.hour % 12 === 0 ? 12 : dt.hour % 12);\n          case \"hh\":\n            return this.num(dt.hour % 12 === 0 ? 12 : dt.hour % 12, 2);\n          case \"H\":\n            return this.num(dt.hour);\n          case \"HH\":\n            return this.num(dt.hour, 2);\n          // offset\n          case \"Z\":\n            // like +6\n            return formatOffset({ format: \"narrow\", allowZ: this.opts.allowZ });\n          case \"ZZ\":\n            // like +06:00\n            return formatOffset({ format: \"short\", allowZ: this.opts.allowZ });\n          case \"ZZZ\":\n            // like +0600\n            return formatOffset({ format: \"techie\", allowZ: this.opts.allowZ });\n          case \"ZZZZ\":\n            // like EST\n            return dt.zone.offsetName(dt.ts, { format: \"short\", locale: this.loc.locale });\n          case \"ZZZZZ\":\n            // like Eastern Standard Time\n            return dt.zone.offsetName(dt.ts, { format: \"long\", locale: this.loc.locale });\n          // zone\n          case \"z\":\n            // like America/New_York\n            return dt.zoneName;\n          // meridiems\n          case \"a\":\n            return meridiem();\n          // dates\n          case \"d\":\n            return useDateTimeFormatter ? string({ day: \"numeric\" }, \"day\") : this.num(dt.day);\n          case \"dd\":\n            return useDateTimeFormatter ? string({ day: \"2-digit\" }, \"day\") : this.num(dt.day, 2);\n          // weekdays - standalone\n          case \"c\":\n            // like 1\n            return this.num(dt.weekday);\n          case \"ccc\":\n            // like 'Tues'\n            return weekday(\"short\", true);\n          case \"cccc\":\n            // like 'Tuesday'\n            return weekday(\"long\", true);\n          case \"ccccc\":\n            // like 'T'\n            return weekday(\"narrow\", true);\n          // weekdays - format\n          case \"E\":\n            // like 1\n            return this.num(dt.weekday);\n          case \"EEE\":\n            // like 'Tues'\n            return weekday(\"short\", false);\n          case \"EEEE\":\n            // like 'Tuesday'\n            return weekday(\"long\", false);\n          case \"EEEEE\":\n            // like 'T'\n            return weekday(\"narrow\", false);\n          // months - standalone\n          case \"L\":\n            // like 1\n            return useDateTimeFormatter\n              ? string({ month: \"numeric\", day: \"numeric\" }, \"month\")\n              : this.num(dt.month);\n          case \"LL\":\n            // like 01, doesn't seem to work\n            return useDateTimeFormatter\n              ? string({ month: \"2-digit\", day: \"numeric\" }, \"month\")\n              : this.num(dt.month, 2);\n          case \"LLL\":\n            // like Jan\n            return month(\"short\", true);\n          case \"LLLL\":\n            // like January\n            return month(\"long\", true);\n          case \"LLLLL\":\n            // like J\n            return month(\"narrow\", true);\n          // months - format\n          case \"M\":\n            // like 1\n            return useDateTimeFormatter\n              ? string({ month: \"numeric\" }, \"month\")\n              : this.num(dt.month);\n          case \"MM\":\n            // like 01\n            return useDateTimeFormatter\n              ? string({ month: \"2-digit\" }, \"month\")\n              : this.num(dt.month, 2);\n          case \"MMM\":\n            // like Jan\n            return month(\"short\", false);\n          case \"MMMM\":\n            // like January\n            return month(\"long\", false);\n          case \"MMMMM\":\n            // like J\n            return month(\"narrow\", false);\n          // years\n          case \"y\":\n            // like 2014\n            return useDateTimeFormatter ? string({ year: \"numeric\" }, \"year\") : this.num(dt.year);\n          case \"yy\":\n            // like 14\n            return useDateTimeFormatter\n              ? string({ year: \"2-digit\" }, \"year\")\n              : this.num(dt.year.toString().slice(-2), 2);\n          case \"yyyy\":\n            // like 0012\n            return useDateTimeFormatter\n              ? string({ year: \"numeric\" }, \"year\")\n              : this.num(dt.year, 4);\n          case \"yyyyyy\":\n            // like 000012\n            return useDateTimeFormatter\n              ? string({ year: \"numeric\" }, \"year\")\n              : this.num(dt.year, 6);\n          // eras\n          case \"G\":\n            // like AD\n            return era(\"short\");\n          case \"GG\":\n            // like Anno Domini\n            return era(\"long\");\n          case \"GGGGG\":\n            return era(\"narrow\");\n          case \"kk\":\n            return this.num(dt.weekYear.toString().slice(-2), 2);\n          case \"kkkk\":\n            return this.num(dt.weekYear, 4);\n          case \"W\":\n            return this.num(dt.weekNumber);\n          case \"WW\":\n            return this.num(dt.weekNumber, 2);\n          case \"n\":\n            return this.num(dt.localWeekNumber);\n          case \"nn\":\n            return this.num(dt.localWeekNumber, 2);\n          case \"ii\":\n            return this.num(dt.localWeekYear.toString().slice(-2), 2);\n          case \"iiii\":\n            return this.num(dt.localWeekYear, 4);\n          case \"o\":\n            return this.num(dt.ordinal);\n          case \"ooo\":\n            return this.num(dt.ordinal, 3);\n          case \"q\":\n            // like 1\n            return this.num(dt.quarter);\n          case \"qq\":\n            // like 01\n            return this.num(dt.quarter, 2);\n          case \"X\":\n            return this.num(Math.floor(dt.ts / 1000));\n          case \"x\":\n            return this.num(dt.ts);\n          default:\n            return maybeMacro(token);\n        }\n      };\n\n    return stringifyTokens(Formatter.parseFormat(fmt), tokenToString);\n  }\n\n  formatDurationFromString(dur, fmt) {\n    const tokenToField = (token) => {\n        switch (token[0]) {\n          case \"S\":\n            return \"millisecond\";\n          case \"s\":\n            return \"second\";\n          case \"m\":\n            return \"minute\";\n          case \"h\":\n            return \"hour\";\n          case \"d\":\n            return \"day\";\n          case \"w\":\n            return \"week\";\n          case \"M\":\n            return \"month\";\n          case \"y\":\n            return \"year\";\n          default:\n            return null;\n        }\n      },\n      tokenToString = (lildur) => (token) => {\n        const mapped = tokenToField(token);\n        if (mapped) {\n          return this.num(lildur.get(mapped), token.length);\n        } else {\n          return token;\n        }\n      },\n      tokens = Formatter.parseFormat(fmt),\n      realTokens = tokens.reduce(\n        (found, { literal, val }) => (literal ? found : found.concat(val)),\n        []\n      ),\n      collapsed = dur.shiftTo(...realTokens.map(tokenToField).filter((t) => t));\n    return stringifyTokens(tokens, tokenToString(collapsed));\n  }\n}\n", "import {\n  untruncate<PERSON>ear,\n  signed<PERSON>ffset,\n  parseInteger,\n  parse<PERSON>illis,\n  isUndefined,\n  parseFloating,\n} from \"./util.js\";\nimport * as English from \"./english.js\";\nimport FixedOffsetZone from \"../zones/fixedOffsetZone.js\";\nimport IANAZone from \"../zones/IANAZone.js\";\n\n/*\n * This file handles parsing for well-specified formats. Here's how it works:\n * Two things go into parsing: a regex to match with and an extractor to take apart the groups in the match.\n * An extractor is just a function that takes a regex match array and returns a { year: ..., month: ... } object\n * parse() does the work of executing the regex and applying the extractor. It takes multiple regex/extractor pairs to try in sequence.\n * Extractors can take a \"cursor\" representing the offset in the match to look at. This makes it easy to combine extractors.\n * combineExtractors() does the work of combining them, keeping track of the cursor through multiple extractions.\n * Some extractions are super dumb and simpleParse and fromStrings help DRY them.\n */\n\nconst ianaRegex = /[A-Za-z_+-]{1,256}(?::?\\/[A-Za-z0-9_+-]{1,256}(?:\\/[A-Za-z0-9_+-]{1,256})?)?/;\n\nfunction combineRegexes(...regexes) {\n  const full = regexes.reduce((f, r) => f + r.source, \"\");\n  return RegExp(`^${full}$`);\n}\n\nfunction combineExtractors(...extractors) {\n  return (m) =>\n    extractors\n      .reduce(\n        ([mergedVals, mergedZone, cursor], ex) => {\n          const [val, zone, next] = ex(m, cursor);\n          return [{ ...mergedVals, ...val }, zone || mergedZone, next];\n        },\n        [{}, null, 1]\n      )\n      .slice(0, 2);\n}\n\nfunction parse(s, ...patterns) {\n  if (s == null) {\n    return [null, null];\n  }\n\n  for (const [regex, extractor] of patterns) {\n    const m = regex.exec(s);\n    if (m) {\n      return extractor(m);\n    }\n  }\n  return [null, null];\n}\n\nfunction simpleParse(...keys) {\n  return (match, cursor) => {\n    const ret = {};\n    let i;\n\n    for (i = 0; i < keys.length; i++) {\n      ret[keys[i]] = parseInteger(match[cursor + i]);\n    }\n    return [ret, null, cursor + i];\n  };\n}\n\n// ISO and SQL parsing\nconst offsetRegex = /(?:(Z)|([+-]\\d\\d)(?::?(\\d\\d))?)/;\nconst isoExtendedZone = `(?:${offsetRegex.source}?(?:\\\\[(${ianaRegex.source})\\\\])?)?`;\nconst isoTimeBaseRegex = /(\\d\\d)(?::?(\\d\\d)(?::?(\\d\\d)(?:[.,](\\d{1,30}))?)?)?/;\nconst isoTimeRegex = RegExp(`${isoTimeBaseRegex.source}${isoExtendedZone}`);\nconst isoTimeExtensionRegex = RegExp(`(?:T${isoTimeRegex.source})?`);\nconst isoYmdRegex = /([+-]\\d{6}|\\d{4})(?:-?(\\d\\d)(?:-?(\\d\\d))?)?/;\nconst isoWeekRegex = /(\\d{4})-?W(\\d\\d)(?:-?(\\d))?/;\nconst isoOrdinalRegex = /(\\d{4})-?(\\d{3})/;\nconst extractISOWeekData = simpleParse(\"weekYear\", \"weekNumber\", \"weekDay\");\nconst extractISOOrdinalData = simpleParse(\"year\", \"ordinal\");\nconst sqlYmdRegex = /(\\d{4})-(\\d\\d)-(\\d\\d)/; // dumbed-down version of the ISO one\nconst sqlTimeRegex = RegExp(\n  `${isoTimeBaseRegex.source} ?(?:${offsetRegex.source}|(${ianaRegex.source}))?`\n);\nconst sqlTimeExtensionRegex = RegExp(`(?: ${sqlTimeRegex.source})?`);\n\nfunction int(match, pos, fallback) {\n  const m = match[pos];\n  return isUndefined(m) ? fallback : parseInteger(m);\n}\n\nfunction extractISOYmd(match, cursor) {\n  const item = {\n    year: int(match, cursor),\n    month: int(match, cursor + 1, 1),\n    day: int(match, cursor + 2, 1),\n  };\n\n  return [item, null, cursor + 3];\n}\n\nfunction extractISOTime(match, cursor) {\n  const item = {\n    hours: int(match, cursor, 0),\n    minutes: int(match, cursor + 1, 0),\n    seconds: int(match, cursor + 2, 0),\n    milliseconds: parseMillis(match[cursor + 3]),\n  };\n\n  return [item, null, cursor + 4];\n}\n\nfunction extractISOOffset(match, cursor) {\n  const local = !match[cursor] && !match[cursor + 1],\n    fullOffset = signedOffset(match[cursor + 1], match[cursor + 2]),\n    zone = local ? null : FixedOffsetZone.instance(fullOffset);\n  return [{}, zone, cursor + 3];\n}\n\nfunction extractIANAZone(match, cursor) {\n  const zone = match[cursor] ? IANAZone.create(match[cursor]) : null;\n  return [{}, zone, cursor + 1];\n}\n\n// ISO time parsing\n\nconst isoTimeOnly = RegExp(`^T?${isoTimeBaseRegex.source}$`);\n\n// ISO duration parsing\n\nconst isoDuration =\n  /^-?P(?:(?:(-?\\d{1,20}(?:\\.\\d{1,20})?)Y)?(?:(-?\\d{1,20}(?:\\.\\d{1,20})?)M)?(?:(-?\\d{1,20}(?:\\.\\d{1,20})?)W)?(?:(-?\\d{1,20}(?:\\.\\d{1,20})?)D)?(?:T(?:(-?\\d{1,20}(?:\\.\\d{1,20})?)H)?(?:(-?\\d{1,20}(?:\\.\\d{1,20})?)M)?(?:(-?\\d{1,20})(?:[.,](-?\\d{1,20}))?S)?)?)$/;\n\nfunction extractISODuration(match) {\n  const [s, yearStr, monthStr, weekStr, dayStr, hourStr, minuteStr, secondStr, millisecondsStr] =\n    match;\n\n  const hasNegativePrefix = s[0] === \"-\";\n  const negativeSeconds = secondStr && secondStr[0] === \"-\";\n\n  const maybeNegate = (num, force = false) =>\n    num !== undefined && (force || (num && hasNegativePrefix)) ? -num : num;\n\n  return [\n    {\n      years: maybeNegate(parseFloating(yearStr)),\n      months: maybeNegate(parseFloating(monthStr)),\n      weeks: maybeNegate(parseFloating(weekStr)),\n      days: maybeNegate(parseFloating(dayStr)),\n      hours: maybeNegate(parseFloating(hourStr)),\n      minutes: maybeNegate(parseFloating(minuteStr)),\n      seconds: maybeNegate(parseFloating(secondStr), secondStr === \"-0\"),\n      milliseconds: maybeNegate(parseMillis(millisecondsStr), negativeSeconds),\n    },\n  ];\n}\n\n// These are a little braindead. EDT *should* tell us that we're in, say, America/New_York\n// and not just that we're in -240 *right now*. But since I don't think these are used that often\n// I'm just going to ignore that\nconst obsOffsets = {\n  GMT: 0,\n  EDT: -4 * 60,\n  EST: -5 * 60,\n  CDT: -5 * 60,\n  CST: -6 * 60,\n  MDT: -6 * 60,\n  MST: -7 * 60,\n  PDT: -7 * 60,\n  PST: -8 * 60,\n};\n\nfunction fromStrings(weekdayStr, yearStr, monthStr, dayStr, hourStr, minuteStr, secondStr) {\n  const result = {\n    year: yearStr.length === 2 ? untruncateYear(parseInteger(yearStr)) : parseInteger(yearStr),\n    month: English.monthsShort.indexOf(monthStr) + 1,\n    day: parseInteger(dayStr),\n    hour: parseInteger(hourStr),\n    minute: parseInteger(minuteStr),\n  };\n\n  if (secondStr) result.second = parseInteger(secondStr);\n  if (weekdayStr) {\n    result.weekday =\n      weekdayStr.length > 3\n        ? English.weekdaysLong.indexOf(weekdayStr) + 1\n        : English.weekdaysShort.indexOf(weekdayStr) + 1;\n  }\n\n  return result;\n}\n\n// RFC 2822/5322\nconst rfc2822 =\n  /^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),\\s)?(\\d{1,2})\\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\\s(\\d{2,4})\\s(\\d\\d):(\\d\\d)(?::(\\d\\d))?\\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|(?:([+-]\\d\\d)(\\d\\d)))$/;\n\nfunction extractRFC2822(match) {\n  const [\n      ,\n      weekdayStr,\n      dayStr,\n      monthStr,\n      yearStr,\n      hourStr,\n      minuteStr,\n      secondStr,\n      obsOffset,\n      milOffset,\n      offHourStr,\n      offMinuteStr,\n    ] = match,\n    result = fromStrings(weekdayStr, yearStr, monthStr, dayStr, hourStr, minuteStr, secondStr);\n\n  let offset;\n  if (obsOffset) {\n    offset = obsOffsets[obsOffset];\n  } else if (milOffset) {\n    offset = 0;\n  } else {\n    offset = signedOffset(offHourStr, offMinuteStr);\n  }\n\n  return [result, new FixedOffsetZone(offset)];\n}\n\nfunction preprocessRFC2822(s) {\n  // Remove comments and folding whitespace and replace multiple-spaces with a single space\n  return s\n    .replace(/\\([^()]*\\)|[\\n\\t]/g, \" \")\n    .replace(/(\\s\\s+)/g, \" \")\n    .trim();\n}\n\n// http date\n\nconst rfc1123 =\n    /^(Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\\d\\d) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\\d{4}) (\\d\\d):(\\d\\d):(\\d\\d) GMT$/,\n  rfc850 =\n    /^(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\\d\\d)-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\\d\\d) (\\d\\d):(\\d\\d):(\\d\\d) GMT$/,\n  ascii =\n    /^(Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( \\d|\\d\\d) (\\d\\d):(\\d\\d):(\\d\\d) (\\d{4})$/;\n\nfunction extractRFC1123Or850(match) {\n  const [, weekdayStr, dayStr, monthStr, yearStr, hourStr, minuteStr, secondStr] = match,\n    result = fromStrings(weekdayStr, yearStr, monthStr, dayStr, hourStr, minuteStr, secondStr);\n  return [result, FixedOffsetZone.utcInstance];\n}\n\nfunction extractASCII(match) {\n  const [, weekdayStr, monthStr, dayStr, hourStr, minuteStr, secondStr, yearStr] = match,\n    result = fromStrings(weekdayStr, yearStr, monthStr, dayStr, hourStr, minuteStr, secondStr);\n  return [result, FixedOffsetZone.utcInstance];\n}\n\nconst isoYmdWithTimeExtensionRegex = combineRegexes(isoYmdRegex, isoTimeExtensionRegex);\nconst isoWeekWithTimeExtensionRegex = combineRegexes(isoWeekRegex, isoTimeExtensionRegex);\nconst isoOrdinalWithTimeExtensionRegex = combineRegexes(isoOrdinalRegex, isoTimeExtensionRegex);\nconst isoTimeCombinedRegex = combineRegexes(isoTimeRegex);\n\nconst extractISOYmdTimeAndOffset = combineExtractors(\n  extractISOYmd,\n  extractISOTime,\n  extractISOOffset,\n  extractIANAZone\n);\nconst extractISOWeekTimeAndOffset = combineExtractors(\n  extractISOWeekData,\n  extractISOTime,\n  extractISOOffset,\n  extractIANAZone\n);\nconst extractISOOrdinalDateAndTime = combineExtractors(\n  extractISOOrdinalData,\n  extractISOTime,\n  extractISOOffset,\n  extractIANAZone\n);\nconst extractISOTimeAndOffset = combineExtractors(\n  extractISOTime,\n  extractISOOffset,\n  extractIANAZone\n);\n\n/*\n * @private\n */\n\nexport function parseISODate(s) {\n  return parse(\n    s,\n    [isoYmdWithTimeExtensionRegex, extractISOYmdTimeAndOffset],\n    [isoWeekWithTimeExtensionRegex, extractISOWeekTimeAndOffset],\n    [isoOrdinalWithTimeExtensionRegex, extractISOOrdinalDateAndTime],\n    [isoTimeCombinedRegex, extractISOTimeAndOffset]\n  );\n}\n\nexport function parseRFC2822Date(s) {\n  return parse(preprocessRFC2822(s), [rfc2822, extractRFC2822]);\n}\n\nexport function parseHTTPDate(s) {\n  return parse(\n    s,\n    [rfc1123, extractRFC1123Or850],\n    [rfc850, extractRFC1123Or850],\n    [ascii, extractASCII]\n  );\n}\n\nexport function parseISODuration(s) {\n  return parse(s, [isoDuration, extractISODuration]);\n}\n\nconst extractISOTimeOnly = combineExtractors(extractISOTime);\n\nexport function parseISOTimeOnly(s) {\n  return parse(s, [isoTimeOnly, extractISOTimeOnly]);\n}\n\nconst sqlYmdWithTimeExtensionRegex = combineRegexes(sqlYmdRegex, sqlTimeExtensionRegex);\nconst sqlTimeCombinedRegex = combineRegexes(sqlTimeRegex);\n\nconst extractISOTimeOffsetAndIANAZone = combineExtractors(\n  extractISOTime,\n  extractISOOffset,\n  extractIANAZone\n);\n\nexport function parseSQL(s) {\n  return parse(\n    s,\n    [sqlYmdWithTimeExtensionRegex, extractISOYmdTimeAndOffset],\n    [sqlTimeCombinedRegex, extractISOTimeOffsetAndIANAZone]\n  );\n}\n", "import { InvalidArgumentError, InvalidDurationError, InvalidUnitError } from \"./errors.js\";\nimport Formatter from \"./impl/formatter.js\";\nimport Invalid from \"./impl/invalid.js\";\nimport Locale from \"./impl/locale.js\";\nimport { parseISODuration, parseISOTimeOnly } from \"./impl/regexParser.js\";\nimport {\n  asNumber,\n  hasOwnProperty,\n  isNumber,\n  isUndefined,\n  normalizeObject,\n  roundTo,\n} from \"./impl/util.js\";\nimport Settings from \"./settings.js\";\nimport DateTime from \"./datetime.js\";\n\nconst INVALID = \"Invalid Duration\";\n\n// unit conversion constants\nexport const lowOrderMatrix = {\n    weeks: {\n      days: 7,\n      hours: 7 * 24,\n      minutes: 7 * 24 * 60,\n      seconds: 7 * 24 * 60 * 60,\n      milliseconds: 7 * 24 * 60 * 60 * 1000,\n    },\n    days: {\n      hours: 24,\n      minutes: 24 * 60,\n      seconds: 24 * 60 * 60,\n      milliseconds: 24 * 60 * 60 * 1000,\n    },\n    hours: { minutes: 60, seconds: 60 * 60, milliseconds: 60 * 60 * 1000 },\n    minutes: { seconds: 60, milliseconds: 60 * 1000 },\n    seconds: { milliseconds: 1000 },\n  },\n  casualMatrix = {\n    years: {\n      quarters: 4,\n      months: 12,\n      weeks: 52,\n      days: 365,\n      hours: 365 * 24,\n      minutes: 365 * 24 * 60,\n      seconds: 365 * 24 * 60 * 60,\n      milliseconds: 365 * 24 * 60 * 60 * 1000,\n    },\n    quarters: {\n      months: 3,\n      weeks: 13,\n      days: 91,\n      hours: 91 * 24,\n      minutes: 91 * 24 * 60,\n      seconds: 91 * 24 * 60 * 60,\n      milliseconds: 91 * 24 * 60 * 60 * 1000,\n    },\n    months: {\n      weeks: 4,\n      days: 30,\n      hours: 30 * 24,\n      minutes: 30 * 24 * 60,\n      seconds: 30 * 24 * 60 * 60,\n      milliseconds: 30 * 24 * 60 * 60 * 1000,\n    },\n\n    ...lowOrderMatrix,\n  },\n  daysInYearAccurate = 146097.0 / 400,\n  daysInMonthAccurate = 146097.0 / 4800,\n  accurateMatrix = {\n    years: {\n      quarters: 4,\n      months: 12,\n      weeks: daysInYearAccurate / 7,\n      days: daysInYearAccurate,\n      hours: daysInYearAccurate * 24,\n      minutes: daysInYearAccurate * 24 * 60,\n      seconds: daysInYearAccurate * 24 * 60 * 60,\n      milliseconds: daysInYearAccurate * 24 * 60 * 60 * 1000,\n    },\n    quarters: {\n      months: 3,\n      weeks: daysInYearAccurate / 28,\n      days: daysInYearAccurate / 4,\n      hours: (daysInYearAccurate * 24) / 4,\n      minutes: (daysInYearAccurate * 24 * 60) / 4,\n      seconds: (daysInYearAccurate * 24 * 60 * 60) / 4,\n      milliseconds: (daysInYearAccurate * 24 * 60 * 60 * 1000) / 4,\n    },\n    months: {\n      weeks: daysInMonthAccurate / 7,\n      days: daysInMonthAccurate,\n      hours: daysInMonthAccurate * 24,\n      minutes: daysInMonthAccurate * 24 * 60,\n      seconds: daysInMonthAccurate * 24 * 60 * 60,\n      milliseconds: daysInMonthAccurate * 24 * 60 * 60 * 1000,\n    },\n    ...lowOrderMatrix,\n  };\n\n// units ordered by size\nconst orderedUnits = [\n  \"years\",\n  \"quarters\",\n  \"months\",\n  \"weeks\",\n  \"days\",\n  \"hours\",\n  \"minutes\",\n  \"seconds\",\n  \"milliseconds\",\n];\n\nconst reverseUnits = orderedUnits.slice(0).reverse();\n\n// clone really means \"create another instance just like this one, but with these changes\"\nfunction clone(dur, alts, clear = false) {\n  // deep merge for vals\n  const conf = {\n    values: clear ? alts.values : { ...dur.values, ...(alts.values || {}) },\n    loc: dur.loc.clone(alts.loc),\n    conversionAccuracy: alts.conversionAccuracy || dur.conversionAccuracy,\n    matrix: alts.matrix || dur.matrix,\n  };\n  return new Duration(conf);\n}\n\nfunction durationToMillis(matrix, vals) {\n  let sum = vals.milliseconds ?? 0;\n  for (const unit of reverseUnits.slice(1)) {\n    if (vals[unit]) {\n      sum += vals[unit] * matrix[unit][\"milliseconds\"];\n    }\n  }\n  return sum;\n}\n\n// NB: mutates parameters\nfunction normalizeValues(matrix, vals) {\n  // the logic below assumes the overall value of the duration is positive\n  // if this is not the case, factor is used to make it so\n  const factor = durationToMillis(matrix, vals) < 0 ? -1 : 1;\n\n  orderedUnits.reduceRight((previous, current) => {\n    if (!isUndefined(vals[current])) {\n      if (previous) {\n        const previousVal = vals[previous] * factor;\n        const conv = matrix[current][previous];\n\n        // if (previousVal < 0):\n        // lower order unit is negative (e.g. { years: 2, days: -2 })\n        // normalize this by reducing the higher order unit by the appropriate amount\n        // and increasing the lower order unit\n        // this can never make the higher order unit negative, because this function only operates\n        // on positive durations, so the amount of time represented by the lower order unit cannot\n        // be larger than the higher order unit\n        // else:\n        // lower order unit is positive (e.g. { years: 2, days: 450 } or { years: -2, days: 450 })\n        // in this case we attempt to convert as much as possible from the lower order unit into\n        // the higher order one\n        //\n        // Math.floor takes care of both of these cases, rounding away from 0\n        // if previousVal < 0 it makes the absolute value larger\n        // if previousVal >= it makes the absolute value smaller\n        const rollUp = Math.floor(previousVal / conv);\n        vals[current] += rollUp * factor;\n        vals[previous] -= rollUp * conv * factor;\n      }\n      return current;\n    } else {\n      return previous;\n    }\n  }, null);\n\n  // try to convert any decimals into smaller units if possible\n  // for example for { years: 2.5, days: 0, seconds: 0 } we want to get { years: 2, days: 182, hours: 12 }\n  orderedUnits.reduce((previous, current) => {\n    if (!isUndefined(vals[current])) {\n      if (previous) {\n        const fraction = vals[previous] % 1;\n        vals[previous] -= fraction;\n        vals[current] += fraction * matrix[previous][current];\n      }\n      return current;\n    } else {\n      return previous;\n    }\n  }, null);\n}\n\n// Remove all properties with a value of 0 from an object\nfunction removeZeroes(vals) {\n  const newVals = {};\n  for (const [key, value] of Object.entries(vals)) {\n    if (value !== 0) {\n      newVals[key] = value;\n    }\n  }\n  return newVals;\n}\n\n/**\n * A Duration object represents a period of time, like \"2 months\" or \"1 day, 1 hour\". Conceptually, it's just a map of units to their quantities, accompanied by some additional configuration and methods for creating, parsing, interrogating, transforming, and formatting them. They can be used on their own or in conjunction with other Luxon types; for example, you can use {@link DateTime#plus} to add a Duration object to a DateTime, producing another DateTime.\n *\n * Here is a brief overview of commonly used methods and getters in Duration:\n *\n * * **Creation** To create a Duration, use {@link Duration.fromMillis}, {@link Duration.fromObject}, or {@link Duration.fromISO}.\n * * **Unit values** See the {@link Duration#years}, {@link Duration#months}, {@link Duration#weeks}, {@link Duration#days}, {@link Duration#hours}, {@link Duration#minutes}, {@link Duration#seconds}, {@link Duration#milliseconds} accessors.\n * * **Configuration** See  {@link Duration#locale} and {@link Duration#numberingSystem} accessors.\n * * **Transformation** To create new Durations out of old ones use {@link Duration#plus}, {@link Duration#minus}, {@link Duration#normalize}, {@link Duration#set}, {@link Duration#reconfigure}, {@link Duration#shiftTo}, and {@link Duration#negate}.\n * * **Output** To convert the Duration into other representations, see {@link Duration#as}, {@link Duration#toISO}, {@link Duration#toFormat}, and {@link Duration#toJSON}\n *\n * There's are more methods documented below. In addition, for more information on subtler topics like internationalization and validity, see the external documentation.\n */\nexport default class Duration {\n  /**\n   * @private\n   */\n  constructor(config) {\n    const accurate = config.conversionAccuracy === \"longterm\" || false;\n    let matrix = accurate ? accurateMatrix : casualMatrix;\n\n    if (config.matrix) {\n      matrix = config.matrix;\n    }\n\n    /**\n     * @access private\n     */\n    this.values = config.values;\n    /**\n     * @access private\n     */\n    this.loc = config.loc || Locale.create();\n    /**\n     * @access private\n     */\n    this.conversionAccuracy = accurate ? \"longterm\" : \"casual\";\n    /**\n     * @access private\n     */\n    this.invalid = config.invalid || null;\n    /**\n     * @access private\n     */\n    this.matrix = matrix;\n    /**\n     * @access private\n     */\n    this.isLuxonDuration = true;\n  }\n\n  /**\n   * Create Duration from a number of milliseconds.\n   * @param {number} count of milliseconds\n   * @param {Object} opts - options for parsing\n   * @param {string} [opts.locale='en-US'] - the locale to use\n   * @param {string} opts.numberingSystem - the numbering system to use\n   * @param {string} [opts.conversionAccuracy='casual'] - the conversion system to use\n   * @return {Duration}\n   */\n  static fromMillis(count, opts) {\n    return Duration.fromObject({ milliseconds: count }, opts);\n  }\n\n  /**\n   * Create a Duration from a JavaScript object with keys like 'years' and 'hours'.\n   * If this object is empty then a zero milliseconds duration is returned.\n   * @param {Object} obj - the object to create the DateTime from\n   * @param {number} obj.years\n   * @param {number} obj.quarters\n   * @param {number} obj.months\n   * @param {number} obj.weeks\n   * @param {number} obj.days\n   * @param {number} obj.hours\n   * @param {number} obj.minutes\n   * @param {number} obj.seconds\n   * @param {number} obj.milliseconds\n   * @param {Object} [opts=[]] - options for creating this Duration\n   * @param {string} [opts.locale='en-US'] - the locale to use\n   * @param {string} opts.numberingSystem - the numbering system to use\n   * @param {string} [opts.conversionAccuracy='casual'] - the preset conversion system to use\n   * @param {string} [opts.matrix=Object] - the custom conversion system to use\n   * @return {Duration}\n   */\n  static fromObject(obj, opts = {}) {\n    if (obj == null || typeof obj !== \"object\") {\n      throw new InvalidArgumentError(\n        `Duration.fromObject: argument expected to be an object, got ${\n          obj === null ? \"null\" : typeof obj\n        }`\n      );\n    }\n\n    return new Duration({\n      values: normalizeObject(obj, Duration.normalizeUnit),\n      loc: Locale.fromObject(opts),\n      conversionAccuracy: opts.conversionAccuracy,\n      matrix: opts.matrix,\n    });\n  }\n\n  /**\n   * Create a Duration from DurationLike.\n   *\n   * @param {Object | number | Duration} durationLike\n   * One of:\n   * - object with keys like 'years' and 'hours'.\n   * - number representing milliseconds\n   * - Duration instance\n   * @return {Duration}\n   */\n  static fromDurationLike(durationLike) {\n    if (isNumber(durationLike)) {\n      return Duration.fromMillis(durationLike);\n    } else if (Duration.isDuration(durationLike)) {\n      return durationLike;\n    } else if (typeof durationLike === \"object\") {\n      return Duration.fromObject(durationLike);\n    } else {\n      throw new InvalidArgumentError(\n        `Unknown duration argument ${durationLike} of type ${typeof durationLike}`\n      );\n    }\n  }\n\n  /**\n   * Create a Duration from an ISO 8601 duration string.\n   * @param {string} text - text to parse\n   * @param {Object} opts - options for parsing\n   * @param {string} [opts.locale='en-US'] - the locale to use\n   * @param {string} opts.numberingSystem - the numbering system to use\n   * @param {string} [opts.conversionAccuracy='casual'] - the preset conversion system to use\n   * @param {string} [opts.matrix=Object] - the preset conversion system to use\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Durations\n   * @example Duration.fromISO('P3Y6M1W4DT12H30M5S').toObject() //=> { years: 3, months: 6, weeks: 1, days: 4, hours: 12, minutes: 30, seconds: 5 }\n   * @example Duration.fromISO('PT23H').toObject() //=> { hours: 23 }\n   * @example Duration.fromISO('P5Y3M').toObject() //=> { years: 5, months: 3 }\n   * @return {Duration}\n   */\n  static fromISO(text, opts) {\n    const [parsed] = parseISODuration(text);\n    if (parsed) {\n      return Duration.fromObject(parsed, opts);\n    } else {\n      return Duration.invalid(\"unparsable\", `the input \"${text}\" can't be parsed as ISO 8601`);\n    }\n  }\n\n  /**\n   * Create a Duration from an ISO 8601 time string.\n   * @param {string} text - text to parse\n   * @param {Object} opts - options for parsing\n   * @param {string} [opts.locale='en-US'] - the locale to use\n   * @param {string} opts.numberingSystem - the numbering system to use\n   * @param {string} [opts.conversionAccuracy='casual'] - the preset conversion system to use\n   * @param {string} [opts.matrix=Object] - the conversion system to use\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Times\n   * @example Duration.fromISOTime('11:22:33.444').toObject() //=> { hours: 11, minutes: 22, seconds: 33, milliseconds: 444 }\n   * @example Duration.fromISOTime('11:00').toObject() //=> { hours: 11, minutes: 0, seconds: 0 }\n   * @example Duration.fromISOTime('T11:00').toObject() //=> { hours: 11, minutes: 0, seconds: 0 }\n   * @example Duration.fromISOTime('1100').toObject() //=> { hours: 11, minutes: 0, seconds: 0 }\n   * @example Duration.fromISOTime('T1100').toObject() //=> { hours: 11, minutes: 0, seconds: 0 }\n   * @return {Duration}\n   */\n  static fromISOTime(text, opts) {\n    const [parsed] = parseISOTimeOnly(text);\n    if (parsed) {\n      return Duration.fromObject(parsed, opts);\n    } else {\n      return Duration.invalid(\"unparsable\", `the input \"${text}\" can't be parsed as ISO 8601`);\n    }\n  }\n\n  /**\n   * Create an invalid Duration.\n   * @param {string} reason - simple string of why this datetime is invalid. Should not contain parameters or anything else data-dependent\n   * @param {string} [explanation=null] - longer explanation, may include parameters and other useful debugging information\n   * @return {Duration}\n   */\n  static invalid(reason, explanation = null) {\n    if (!reason) {\n      throw new InvalidArgumentError(\"need to specify a reason the Duration is invalid\");\n    }\n\n    const invalid = reason instanceof Invalid ? reason : new Invalid(reason, explanation);\n\n    if (Settings.throwOnInvalid) {\n      throw new InvalidDurationError(invalid);\n    } else {\n      return new Duration({ invalid });\n    }\n  }\n\n  /**\n   * @private\n   */\n  static normalizeUnit(unit) {\n    const normalized = {\n      year: \"years\",\n      years: \"years\",\n      quarter: \"quarters\",\n      quarters: \"quarters\",\n      month: \"months\",\n      months: \"months\",\n      week: \"weeks\",\n      weeks: \"weeks\",\n      day: \"days\",\n      days: \"days\",\n      hour: \"hours\",\n      hours: \"hours\",\n      minute: \"minutes\",\n      minutes: \"minutes\",\n      second: \"seconds\",\n      seconds: \"seconds\",\n      millisecond: \"milliseconds\",\n      milliseconds: \"milliseconds\",\n    }[unit ? unit.toLowerCase() : unit];\n\n    if (!normalized) throw new InvalidUnitError(unit);\n\n    return normalized;\n  }\n\n  /**\n   * Check if an object is a Duration. Works across context boundaries\n   * @param {object} o\n   * @return {boolean}\n   */\n  static isDuration(o) {\n    return (o && o.isLuxonDuration) || false;\n  }\n\n  /**\n   * Get  the locale of a Duration, such 'en-GB'\n   * @type {string}\n   */\n  get locale() {\n    return this.isValid ? this.loc.locale : null;\n  }\n\n  /**\n   * Get the numbering system of a Duration, such 'beng'. The numbering system is used when formatting the Duration\n   *\n   * @type {string}\n   */\n  get numberingSystem() {\n    return this.isValid ? this.loc.numberingSystem : null;\n  }\n\n  /**\n   * Returns a string representation of this Duration formatted according to the specified format string. You may use these tokens:\n   * * `S` for milliseconds\n   * * `s` for seconds\n   * * `m` for minutes\n   * * `h` for hours\n   * * `d` for days\n   * * `w` for weeks\n   * * `M` for months\n   * * `y` for years\n   * Notes:\n   * * Add padding by repeating the token, e.g. \"yy\" pads the years to two digits, \"hhhh\" pads the hours out to four digits\n   * * Tokens can be escaped by wrapping with single quotes.\n   * * The duration will be converted to the set of units in the format string using {@link Duration#shiftTo} and the Durations's conversion accuracy setting.\n   * @param {string} fmt - the format string\n   * @param {Object} opts - options\n   * @param {boolean} [opts.floor=true] - floor numerical values\n   * @example Duration.fromObject({ years: 1, days: 6, seconds: 2 }).toFormat(\"y d s\") //=> \"1 6 2\"\n   * @example Duration.fromObject({ years: 1, days: 6, seconds: 2 }).toFormat(\"yy dd sss\") //=> \"01 06 002\"\n   * @example Duration.fromObject({ years: 1, days: 6, seconds: 2 }).toFormat(\"M S\") //=> \"12 518402000\"\n   * @return {string}\n   */\n  toFormat(fmt, opts = {}) {\n    // reverse-compat since 1.2; we always round down now, never up, and we do it by default\n    const fmtOpts = {\n      ...opts,\n      floor: opts.round !== false && opts.floor !== false,\n    };\n    return this.isValid\n      ? Formatter.create(this.loc, fmtOpts).formatDurationFromString(this, fmt)\n      : INVALID;\n  }\n\n  /**\n   * Returns a string representation of a Duration with all units included.\n   * To modify its behavior, use `listStyle` and any Intl.NumberFormat option, though `unitDisplay` is especially relevant.\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/NumberFormat/NumberFormat#options\n   * @param {Object} opts - Formatting options. Accepts the same keys as the options parameter of the native `Intl.NumberFormat` constructor, as well as `listStyle`.\n   * @param {string} [opts.listStyle='narrow'] - How to format the merged list. Corresponds to the `style` property of the options parameter of the native `Intl.ListFormat` constructor.\n   * @example\n   * ```js\n   * var dur = Duration.fromObject({ days: 1, hours: 5, minutes: 6 })\n   * dur.toHuman() //=> '1 day, 5 hours, 6 minutes'\n   * dur.toHuman({ listStyle: \"long\" }) //=> '1 day, 5 hours, and 6 minutes'\n   * dur.toHuman({ unitDisplay: \"short\" }) //=> '1 day, 5 hr, 6 min'\n   * ```\n   */\n  toHuman(opts = {}) {\n    if (!this.isValid) return INVALID;\n\n    const l = orderedUnits\n      .map((unit) => {\n        const val = this.values[unit];\n        if (isUndefined(val)) {\n          return null;\n        }\n        return this.loc\n          .numberFormatter({ style: \"unit\", unitDisplay: \"long\", ...opts, unit: unit.slice(0, -1) })\n          .format(val);\n      })\n      .filter((n) => n);\n\n    return this.loc\n      .listFormatter({ type: \"conjunction\", style: opts.listStyle || \"narrow\", ...opts })\n      .format(l);\n  }\n\n  /**\n   * Returns a JavaScript object with this Duration's values.\n   * @example Duration.fromObject({ years: 1, days: 6, seconds: 2 }).toObject() //=> { years: 1, days: 6, seconds: 2 }\n   * @return {Object}\n   */\n  toObject() {\n    if (!this.isValid) return {};\n    return { ...this.values };\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this Duration.\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Durations\n   * @example Duration.fromObject({ years: 3, seconds: 45 }).toISO() //=> 'P3YT45S'\n   * @example Duration.fromObject({ months: 4, seconds: 45 }).toISO() //=> 'P4MT45S'\n   * @example Duration.fromObject({ months: 5 }).toISO() //=> 'P5M'\n   * @example Duration.fromObject({ minutes: 5 }).toISO() //=> 'PT5M'\n   * @example Duration.fromObject({ milliseconds: 6 }).toISO() //=> 'PT0.006S'\n   * @return {string}\n   */\n  toISO() {\n    // we could use the formatter, but this is an easier way to get the minimum string\n    if (!this.isValid) return null;\n\n    let s = \"P\";\n    if (this.years !== 0) s += this.years + \"Y\";\n    if (this.months !== 0 || this.quarters !== 0) s += this.months + this.quarters * 3 + \"M\";\n    if (this.weeks !== 0) s += this.weeks + \"W\";\n    if (this.days !== 0) s += this.days + \"D\";\n    if (this.hours !== 0 || this.minutes !== 0 || this.seconds !== 0 || this.milliseconds !== 0)\n      s += \"T\";\n    if (this.hours !== 0) s += this.hours + \"H\";\n    if (this.minutes !== 0) s += this.minutes + \"M\";\n    if (this.seconds !== 0 || this.milliseconds !== 0)\n      // this will handle \"floating point madness\" by removing extra decimal places\n      // https://stackoverflow.com/questions/588004/is-floating-point-math-broken\n      s += roundTo(this.seconds + this.milliseconds / 1000, 3) + \"S\";\n    if (s === \"P\") s += \"T0S\";\n    return s;\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this Duration, formatted as a time of day.\n   * Note that this will return null if the duration is invalid, negative, or equal to or greater than 24 hours.\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Times\n   * @param {Object} opts - options\n   * @param {boolean} [opts.suppressMilliseconds=false] - exclude milliseconds from the format if they're 0\n   * @param {boolean} [opts.suppressSeconds=false] - exclude seconds from the format if they're 0\n   * @param {boolean} [opts.includePrefix=false] - include the `T` prefix\n   * @param {string} [opts.format='extended'] - choose between the basic and extended format\n   * @example Duration.fromObject({ hours: 11 }).toISOTime() //=> '11:00:00.000'\n   * @example Duration.fromObject({ hours: 11 }).toISOTime({ suppressMilliseconds: true }) //=> '11:00:00'\n   * @example Duration.fromObject({ hours: 11 }).toISOTime({ suppressSeconds: true }) //=> '11:00'\n   * @example Duration.fromObject({ hours: 11 }).toISOTime({ includePrefix: true }) //=> 'T11:00:00.000'\n   * @example Duration.fromObject({ hours: 11 }).toISOTime({ format: 'basic' }) //=> '110000.000'\n   * @return {string}\n   */\n  toISOTime(opts = {}) {\n    if (!this.isValid) return null;\n\n    const millis = this.toMillis();\n    if (millis < 0 || millis >= 86400000) return null;\n\n    opts = {\n      suppressMilliseconds: false,\n      suppressSeconds: false,\n      includePrefix: false,\n      format: \"extended\",\n      ...opts,\n      includeOffset: false,\n    };\n\n    const dateTime = DateTime.fromMillis(millis, { zone: \"UTC\" });\n    return dateTime.toISOTime(opts);\n  }\n\n  /**\n   * Returns an ISO 8601 representation of this Duration appropriate for use in JSON.\n   * @return {string}\n   */\n  toJSON() {\n    return this.toISO();\n  }\n\n  /**\n   * Returns an ISO 8601 representation of this Duration appropriate for use in debugging.\n   * @return {string}\n   */\n  toString() {\n    return this.toISO();\n  }\n\n  /**\n   * Returns a string representation of this Duration appropriate for the REPL.\n   * @return {string}\n   */\n  [Symbol.for(\"nodejs.util.inspect.custom\")]() {\n    if (this.isValid) {\n      return `Duration { values: ${JSON.stringify(this.values)} }`;\n    } else {\n      return `Duration { Invalid, reason: ${this.invalidReason} }`;\n    }\n  }\n\n  /**\n   * Returns an milliseconds value of this Duration.\n   * @return {number}\n   */\n  toMillis() {\n    if (!this.isValid) return NaN;\n\n    return durationToMillis(this.matrix, this.values);\n  }\n\n  /**\n   * Returns an milliseconds value of this Duration. Alias of {@link toMillis}\n   * @return {number}\n   */\n  valueOf() {\n    return this.toMillis();\n  }\n\n  /**\n   * Make this Duration longer by the specified amount. Return a newly-constructed Duration.\n   * @param {Duration|Object|number} duration - The amount to add. Either a Luxon Duration, a number of milliseconds, the object argument to Duration.fromObject()\n   * @return {Duration}\n   */\n  plus(duration) {\n    if (!this.isValid) return this;\n\n    const dur = Duration.fromDurationLike(duration),\n      result = {};\n\n    for (const k of orderedUnits) {\n      if (hasOwnProperty(dur.values, k) || hasOwnProperty(this.values, k)) {\n        result[k] = dur.get(k) + this.get(k);\n      }\n    }\n\n    return clone(this, { values: result }, true);\n  }\n\n  /**\n   * Make this Duration shorter by the specified amount. Return a newly-constructed Duration.\n   * @param {Duration|Object|number} duration - The amount to subtract. Either a Luxon Duration, a number of milliseconds, the object argument to Duration.fromObject()\n   * @return {Duration}\n   */\n  minus(duration) {\n    if (!this.isValid) return this;\n\n    const dur = Duration.fromDurationLike(duration);\n    return this.plus(dur.negate());\n  }\n\n  /**\n   * Scale this Duration by the specified amount. Return a newly-constructed Duration.\n   * @param {function} fn - The function to apply to each unit. Arity is 1 or 2: the value of the unit and, optionally, the unit name. Must return a number.\n   * @example Duration.fromObject({ hours: 1, minutes: 30 }).mapUnits(x => x * 2) //=> { hours: 2, minutes: 60 }\n   * @example Duration.fromObject({ hours: 1, minutes: 30 }).mapUnits((x, u) => u === \"hours\" ? x * 2 : x) //=> { hours: 2, minutes: 30 }\n   * @return {Duration}\n   */\n  mapUnits(fn) {\n    if (!this.isValid) return this;\n    const result = {};\n    for (const k of Object.keys(this.values)) {\n      result[k] = asNumber(fn(this.values[k], k));\n    }\n    return clone(this, { values: result }, true);\n  }\n\n  /**\n   * Get the value of unit.\n   * @param {string} unit - a unit such as 'minute' or 'day'\n   * @example Duration.fromObject({years: 2, days: 3}).get('years') //=> 2\n   * @example Duration.fromObject({years: 2, days: 3}).get('months') //=> 0\n   * @example Duration.fromObject({years: 2, days: 3}).get('days') //=> 3\n   * @return {number}\n   */\n  get(unit) {\n    return this[Duration.normalizeUnit(unit)];\n  }\n\n  /**\n   * \"Set\" the values of specified units. Return a newly-constructed Duration.\n   * @param {Object} values - a mapping of units to numbers\n   * @example dur.set({ years: 2017 })\n   * @example dur.set({ hours: 8, minutes: 30 })\n   * @return {Duration}\n   */\n  set(values) {\n    if (!this.isValid) return this;\n\n    const mixed = { ...this.values, ...normalizeObject(values, Duration.normalizeUnit) };\n    return clone(this, { values: mixed });\n  }\n\n  /**\n   * \"Set\" the locale and/or numberingSystem.  Returns a newly-constructed Duration.\n   * @example dur.reconfigure({ locale: 'en-GB' })\n   * @return {Duration}\n   */\n  reconfigure({ locale, numberingSystem, conversionAccuracy, matrix } = {}) {\n    const loc = this.loc.clone({ locale, numberingSystem });\n    const opts = { loc, matrix, conversionAccuracy };\n    return clone(this, opts);\n  }\n\n  /**\n   * Return the length of the duration in the specified unit.\n   * @param {string} unit - a unit such as 'minutes' or 'days'\n   * @example Duration.fromObject({years: 1}).as('days') //=> 365\n   * @example Duration.fromObject({years: 1}).as('months') //=> 12\n   * @example Duration.fromObject({hours: 60}).as('days') //=> 2.5\n   * @return {number}\n   */\n  as(unit) {\n    return this.isValid ? this.shiftTo(unit).get(unit) : NaN;\n  }\n\n  /**\n   * Reduce this Duration to its canonical representation in its current units.\n   * Assuming the overall value of the Duration is positive, this means:\n   * - excessive values for lower-order units are converted to higher-order units (if possible, see first and second example)\n   * - negative lower-order units are converted to higher order units (there must be such a higher order unit, otherwise\n   *   the overall value would be negative, see third example)\n   * - fractional values for higher-order units are converted to lower-order units (if possible, see fourth example)\n   *\n   * If the overall value is negative, the result of this method is equivalent to `this.negate().normalize().negate()`.\n   * @example Duration.fromObject({ years: 2, days: 5000 }).normalize().toObject() //=> { years: 15, days: 255 }\n   * @example Duration.fromObject({ days: 5000 }).normalize().toObject() //=> { days: 5000 }\n   * @example Duration.fromObject({ hours: 12, minutes: -45 }).normalize().toObject() //=> { hours: 11, minutes: 15 }\n   * @example Duration.fromObject({ years: 2.5, days: 0, hours: 0 }).normalize().toObject() //=> { years: 2, days: 182, hours: 12 }\n   * @return {Duration}\n   */\n  normalize() {\n    if (!this.isValid) return this;\n    const vals = this.toObject();\n    normalizeValues(this.matrix, vals);\n    return clone(this, { values: vals }, true);\n  }\n\n  /**\n   * Rescale units to its largest representation\n   * @example Duration.fromObject({ milliseconds: 90000 }).rescale().toObject() //=> { minutes: 1, seconds: 30 }\n   * @return {Duration}\n   */\n  rescale() {\n    if (!this.isValid) return this;\n    const vals = removeZeroes(this.normalize().shiftToAll().toObject());\n    return clone(this, { values: vals }, true);\n  }\n\n  /**\n   * Convert this Duration into its representation in a different set of units.\n   * @example Duration.fromObject({ hours: 1, seconds: 30 }).shiftTo('minutes', 'milliseconds').toObject() //=> { minutes: 60, milliseconds: 30000 }\n   * @return {Duration}\n   */\n  shiftTo(...units) {\n    if (!this.isValid) return this;\n\n    if (units.length === 0) {\n      return this;\n    }\n\n    units = units.map((u) => Duration.normalizeUnit(u));\n\n    const built = {},\n      accumulated = {},\n      vals = this.toObject();\n    let lastUnit;\n\n    for (const k of orderedUnits) {\n      if (units.indexOf(k) >= 0) {\n        lastUnit = k;\n\n        let own = 0;\n\n        // anything we haven't boiled down yet should get boiled to this unit\n        for (const ak in accumulated) {\n          own += this.matrix[ak][k] * accumulated[ak];\n          accumulated[ak] = 0;\n        }\n\n        // plus anything that's already in this unit\n        if (isNumber(vals[k])) {\n          own += vals[k];\n        }\n\n        // only keep the integer part for now in the hopes of putting any decimal part\n        // into a smaller unit later\n        const i = Math.trunc(own);\n        built[k] = i;\n        accumulated[k] = (own * 1000 - i * 1000) / 1000;\n\n        // otherwise, keep it in the wings to boil it later\n      } else if (isNumber(vals[k])) {\n        accumulated[k] = vals[k];\n      }\n    }\n\n    // anything leftover becomes the decimal for the last unit\n    // lastUnit must be defined since units is not empty\n    for (const key in accumulated) {\n      if (accumulated[key] !== 0) {\n        built[lastUnit] +=\n          key === lastUnit ? accumulated[key] : accumulated[key] / this.matrix[lastUnit][key];\n      }\n    }\n\n    normalizeValues(this.matrix, built);\n    return clone(this, { values: built }, true);\n  }\n\n  /**\n   * Shift this Duration to all available units.\n   * Same as shiftTo(\"years\", \"months\", \"weeks\", \"days\", \"hours\", \"minutes\", \"seconds\", \"milliseconds\")\n   * @return {Duration}\n   */\n  shiftToAll() {\n    if (!this.isValid) return this;\n    return this.shiftTo(\n      \"years\",\n      \"months\",\n      \"weeks\",\n      \"days\",\n      \"hours\",\n      \"minutes\",\n      \"seconds\",\n      \"milliseconds\"\n    );\n  }\n\n  /**\n   * Return the negative of this Duration.\n   * @example Duration.fromObject({ hours: 1, seconds: 30 }).negate().toObject() //=> { hours: -1, seconds: -30 }\n   * @return {Duration}\n   */\n  negate() {\n    if (!this.isValid) return this;\n    const negated = {};\n    for (const k of Object.keys(this.values)) {\n      negated[k] = this.values[k] === 0 ? 0 : -this.values[k];\n    }\n    return clone(this, { values: negated }, true);\n  }\n\n  /**\n   * Get the years.\n   * @type {number}\n   */\n  get years() {\n    return this.isValid ? this.values.years || 0 : NaN;\n  }\n\n  /**\n   * Get the quarters.\n   * @type {number}\n   */\n  get quarters() {\n    return this.isValid ? this.values.quarters || 0 : NaN;\n  }\n\n  /**\n   * Get the months.\n   * @type {number}\n   */\n  get months() {\n    return this.isValid ? this.values.months || 0 : NaN;\n  }\n\n  /**\n   * Get the weeks\n   * @type {number}\n   */\n  get weeks() {\n    return this.isValid ? this.values.weeks || 0 : NaN;\n  }\n\n  /**\n   * Get the days.\n   * @type {number}\n   */\n  get days() {\n    return this.isValid ? this.values.days || 0 : NaN;\n  }\n\n  /**\n   * Get the hours.\n   * @type {number}\n   */\n  get hours() {\n    return this.isValid ? this.values.hours || 0 : NaN;\n  }\n\n  /**\n   * Get the minutes.\n   * @type {number}\n   */\n  get minutes() {\n    return this.isValid ? this.values.minutes || 0 : NaN;\n  }\n\n  /**\n   * Get the seconds.\n   * @return {number}\n   */\n  get seconds() {\n    return this.isValid ? this.values.seconds || 0 : NaN;\n  }\n\n  /**\n   * Get the milliseconds.\n   * @return {number}\n   */\n  get milliseconds() {\n    return this.isValid ? this.values.milliseconds || 0 : NaN;\n  }\n\n  /**\n   * Returns whether the Duration is invalid. Invalid durations are returned by diff operations\n   * on invalid DateTimes or Intervals.\n   * @return {boolean}\n   */\n  get isValid() {\n    return this.invalid === null;\n  }\n\n  /**\n   * Returns an error code if this Duration became invalid, or null if the Duration is valid\n   * @return {string}\n   */\n  get invalidReason() {\n    return this.invalid ? this.invalid.reason : null;\n  }\n\n  /**\n   * Returns an explanation of why this Duration became invalid, or null if the Duration is valid\n   * @type {string}\n   */\n  get invalidExplanation() {\n    return this.invalid ? this.invalid.explanation : null;\n  }\n\n  /**\n   * Equality check\n   * Two Durations are equal iff they have the same units and the same values for each unit.\n   * @param {Duration} other\n   * @return {boolean}\n   */\n  equals(other) {\n    if (!this.isValid || !other.isValid) {\n      return false;\n    }\n\n    if (!this.loc.equals(other.loc)) {\n      return false;\n    }\n\n    function eq(v1, v2) {\n      // Consider 0 and undefined as equal\n      if (v1 === undefined || v1 === 0) return v2 === undefined || v2 === 0;\n      return v1 === v2;\n    }\n\n    for (const u of orderedUnits) {\n      if (!eq(this.values[u], other.values[u])) {\n        return false;\n      }\n    }\n    return true;\n  }\n}\n", "import DateTime, { friendlyDateTime } from \"./datetime.js\";\nimport Duration from \"./duration.js\";\nimport Settings from \"./settings.js\";\nimport { InvalidArgumentError, InvalidIntervalError } from \"./errors.js\";\nimport Invalid from \"./impl/invalid.js\";\nimport Formatter from \"./impl/formatter.js\";\nimport * as Formats from \"./impl/formats.js\";\n\nconst INVALID = \"Invalid Interval\";\n\n// checks if the start is equal to or before the end\nfunction validateStartEnd(start, end) {\n  if (!start || !start.isValid) {\n    return Interval.invalid(\"missing or invalid start\");\n  } else if (!end || !end.isValid) {\n    return Interval.invalid(\"missing or invalid end\");\n  } else if (end < start) {\n    return Interval.invalid(\n      \"end before start\",\n      `The end of an interval must be after its start, but you had start=${start.toISO()} and end=${end.toISO()}`\n    );\n  } else {\n    return null;\n  }\n}\n\n/**\n * An Interval object represents a half-open interval of time, where each endpoint is a {@link DateTime}. Conceptually, it's a container for those two endpoints, accompanied by methods for creating, parsing, interrogating, comparing, transforming, and formatting them.\n *\n * Here is a brief overview of the most commonly used methods and getters in Interval:\n *\n * * **Creation** To create an Interval, use {@link Interval.fromDateTimes}, {@link Interval.after}, {@link Interval.before}, or {@link Interval.fromISO}.\n * * **Accessors** Use {@link Interval#start} and {@link Interval#end} to get the start and end.\n * * **Interrogation** To analyze the Interval, use {@link Interval#count}, {@link Interval#length}, {@link Interval#hasSame}, {@link Interval#contains}, {@link Interval#isAfter}, or {@link Interval#isBefore}.\n * * **Transformation** To create other Intervals out of this one, use {@link Interval#set}, {@link Interval#splitAt}, {@link Interval#splitBy}, {@link Interval#divideEqually}, {@link Interval.merge}, {@link Interval.xor}, {@link Interval#union}, {@link Interval#intersection}, or {@link Interval#difference}.\n * * **Comparison** To compare this Interval to another one, use {@link Interval#equals}, {@link Interval#overlaps}, {@link Interval#abutsStart}, {@link Interval#abutsEnd}, {@link Interval#engulfs}\n * * **Output** To convert the Interval into other representations, see {@link Interval#toString}, {@link Interval#toLocaleString}, {@link Interval#toISO}, {@link Interval#toISODate}, {@link Interval#toISOTime}, {@link Interval#toFormat}, and {@link Interval#toDuration}.\n */\nexport default class Interval {\n  /**\n   * @private\n   */\n  constructor(config) {\n    /**\n     * @access private\n     */\n    this.s = config.start;\n    /**\n     * @access private\n     */\n    this.e = config.end;\n    /**\n     * @access private\n     */\n    this.invalid = config.invalid || null;\n    /**\n     * @access private\n     */\n    this.isLuxonInterval = true;\n  }\n\n  /**\n   * Create an invalid Interval.\n   * @param {string} reason - simple string of why this Interval is invalid. Should not contain parameters or anything else data-dependent\n   * @param {string} [explanation=null] - longer explanation, may include parameters and other useful debugging information\n   * @return {Interval}\n   */\n  static invalid(reason, explanation = null) {\n    if (!reason) {\n      throw new InvalidArgumentError(\"need to specify a reason the Interval is invalid\");\n    }\n\n    const invalid = reason instanceof Invalid ? reason : new Invalid(reason, explanation);\n\n    if (Settings.throwOnInvalid) {\n      throw new InvalidIntervalError(invalid);\n    } else {\n      return new Interval({ invalid });\n    }\n  }\n\n  /**\n   * Create an Interval from a start DateTime and an end DateTime. Inclusive of the start but not the end.\n   * @param {DateTime|Date|Object} start\n   * @param {DateTime|Date|Object} end\n   * @return {Interval}\n   */\n  static fromDateTimes(start, end) {\n    const builtStart = friendlyDateTime(start),\n      builtEnd = friendlyDateTime(end);\n\n    const validateError = validateStartEnd(builtStart, builtEnd);\n\n    if (validateError == null) {\n      return new Interval({\n        start: builtStart,\n        end: builtEnd,\n      });\n    } else {\n      return validateError;\n    }\n  }\n\n  /**\n   * Create an Interval from a start DateTime and a Duration to extend to.\n   * @param {DateTime|Date|Object} start\n   * @param {Duration|Object|number} duration - the length of the Interval.\n   * @return {Interval}\n   */\n  static after(start, duration) {\n    const dur = Duration.fromDurationLike(duration),\n      dt = friendlyDateTime(start);\n    return Interval.fromDateTimes(dt, dt.plus(dur));\n  }\n\n  /**\n   * Create an Interval from an end DateTime and a Duration to extend backwards to.\n   * @param {DateTime|Date|Object} end\n   * @param {Duration|Object|number} duration - the length of the Interval.\n   * @return {Interval}\n   */\n  static before(end, duration) {\n    const dur = Duration.fromDurationLike(duration),\n      dt = friendlyDateTime(end);\n    return Interval.fromDateTimes(dt.minus(dur), dt);\n  }\n\n  /**\n   * Create an Interval from an ISO 8601 string.\n   * Accepts `<start>/<end>`, `<start>/<duration>`, and `<duration>/<end>` formats.\n   * @param {string} text - the ISO string to parse\n   * @param {Object} [opts] - options to pass {@link DateTime#fromISO} and optionally {@link Duration#fromISO}\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Time_intervals\n   * @return {Interval}\n   */\n  static fromISO(text, opts) {\n    const [s, e] = (text || \"\").split(\"/\", 2);\n    if (s && e) {\n      let start, startIsValid;\n      try {\n        start = DateTime.fromISO(s, opts);\n        startIsValid = start.isValid;\n      } catch (e) {\n        startIsValid = false;\n      }\n\n      let end, endIsValid;\n      try {\n        end = DateTime.fromISO(e, opts);\n        endIsValid = end.isValid;\n      } catch (e) {\n        endIsValid = false;\n      }\n\n      if (startIsValid && endIsValid) {\n        return Interval.fromDateTimes(start, end);\n      }\n\n      if (startIsValid) {\n        const dur = Duration.fromISO(e, opts);\n        if (dur.isValid) {\n          return Interval.after(start, dur);\n        }\n      } else if (endIsValid) {\n        const dur = Duration.fromISO(s, opts);\n        if (dur.isValid) {\n          return Interval.before(end, dur);\n        }\n      }\n    }\n    return Interval.invalid(\"unparsable\", `the input \"${text}\" can't be parsed as ISO 8601`);\n  }\n\n  /**\n   * Check if an object is an Interval. Works across context boundaries\n   * @param {object} o\n   * @return {boolean}\n   */\n  static isInterval(o) {\n    return (o && o.isLuxonInterval) || false;\n  }\n\n  /**\n   * Returns the start of the Interval\n   * @type {DateTime}\n   */\n  get start() {\n    return this.isValid ? this.s : null;\n  }\n\n  /**\n   * Returns the end of the Interval\n   * @type {DateTime}\n   */\n  get end() {\n    return this.isValid ? this.e : null;\n  }\n\n  /**\n   * Returns the last DateTime included in the interval (since end is not part of the interval)\n   * @type {DateTime}\n   */\n  get lastDateTime() {\n    return this.isValid ? (this.e ? this.e.minus(1) : null) : null;\n  }\n\n  /**\n   * Returns whether this Interval's end is at least its start, meaning that the Interval isn't 'backwards'.\n   * @type {boolean}\n   */\n  get isValid() {\n    return this.invalidReason === null;\n  }\n\n  /**\n   * Returns an error code if this Interval is invalid, or null if the Interval is valid\n   * @type {string}\n   */\n  get invalidReason() {\n    return this.invalid ? this.invalid.reason : null;\n  }\n\n  /**\n   * Returns an explanation of why this Interval became invalid, or null if the Interval is valid\n   * @type {string}\n   */\n  get invalidExplanation() {\n    return this.invalid ? this.invalid.explanation : null;\n  }\n\n  /**\n   * Returns the length of the Interval in the specified unit.\n   * @param {string} unit - the unit (such as 'hours' or 'days') to return the length in.\n   * @return {number}\n   */\n  length(unit = \"milliseconds\") {\n    return this.isValid ? this.toDuration(...[unit]).get(unit) : NaN;\n  }\n\n  /**\n   * Returns the count of minutes, hours, days, months, or years included in the Interval, even in part.\n   * Unlike {@link Interval#length} this counts sections of the calendar, not periods of time, e.g. specifying 'day'\n   * asks 'what dates are included in this interval?', not 'how many days long is this interval?'\n   * @param {string} [unit='milliseconds'] - the unit of time to count.\n   * @param {Object} opts - options\n   * @param {boolean} [opts.useLocaleWeeks=false] - If true, use weeks based on the locale, i.e. use the locale-dependent start of the week; this operation will always use the locale of the start DateTime\n   * @return {number}\n   */\n  count(unit = \"milliseconds\", opts) {\n    if (!this.isValid) return NaN;\n    const start = this.start.startOf(unit, opts);\n    let end;\n    if (opts?.useLocaleWeeks) {\n      end = this.end.reconfigure({ locale: start.locale });\n    } else {\n      end = this.end;\n    }\n    end = end.startOf(unit, opts);\n    return Math.floor(end.diff(start, unit).get(unit)) + (end.valueOf() !== this.end.valueOf());\n  }\n\n  /**\n   * Returns whether this Interval's start and end are both in the same unit of time\n   * @param {string} unit - the unit of time to check sameness on\n   * @return {boolean}\n   */\n  hasSame(unit) {\n    return this.isValid ? this.isEmpty() || this.e.minus(1).hasSame(this.s, unit) : false;\n  }\n\n  /**\n   * Return whether this Interval has the same start and end DateTimes.\n   * @return {boolean}\n   */\n  isEmpty() {\n    return this.s.valueOf() === this.e.valueOf();\n  }\n\n  /**\n   * Return whether this Interval's start is after the specified DateTime.\n   * @param {DateTime} dateTime\n   * @return {boolean}\n   */\n  isAfter(dateTime) {\n    if (!this.isValid) return false;\n    return this.s > dateTime;\n  }\n\n  /**\n   * Return whether this Interval's end is before the specified DateTime.\n   * @param {DateTime} dateTime\n   * @return {boolean}\n   */\n  isBefore(dateTime) {\n    if (!this.isValid) return false;\n    return this.e <= dateTime;\n  }\n\n  /**\n   * Return whether this Interval contains the specified DateTime.\n   * @param {DateTime} dateTime\n   * @return {boolean}\n   */\n  contains(dateTime) {\n    if (!this.isValid) return false;\n    return this.s <= dateTime && this.e > dateTime;\n  }\n\n  /**\n   * \"Sets\" the start and/or end dates. Returns a newly-constructed Interval.\n   * @param {Object} values - the values to set\n   * @param {DateTime} values.start - the starting DateTime\n   * @param {DateTime} values.end - the ending DateTime\n   * @return {Interval}\n   */\n  set({ start, end } = {}) {\n    if (!this.isValid) return this;\n    return Interval.fromDateTimes(start || this.s, end || this.e);\n  }\n\n  /**\n   * Split this Interval at each of the specified DateTimes\n   * @param {...DateTime} dateTimes - the unit of time to count.\n   * @return {Array}\n   */\n  splitAt(...dateTimes) {\n    if (!this.isValid) return [];\n    const sorted = dateTimes\n        .map(friendlyDateTime)\n        .filter((d) => this.contains(d))\n        .sort((a, b) => a.toMillis() - b.toMillis()),\n      results = [];\n    let { s } = this,\n      i = 0;\n\n    while (s < this.e) {\n      const added = sorted[i] || this.e,\n        next = +added > +this.e ? this.e : added;\n      results.push(Interval.fromDateTimes(s, next));\n      s = next;\n      i += 1;\n    }\n\n    return results;\n  }\n\n  /**\n   * Split this Interval into smaller Intervals, each of the specified length.\n   * Left over time is grouped into a smaller interval\n   * @param {Duration|Object|number} duration - The length of each resulting interval.\n   * @return {Array}\n   */\n  splitBy(duration) {\n    const dur = Duration.fromDurationLike(duration);\n\n    if (!this.isValid || !dur.isValid || dur.as(\"milliseconds\") === 0) {\n      return [];\n    }\n\n    let { s } = this,\n      idx = 1,\n      next;\n\n    const results = [];\n    while (s < this.e) {\n      const added = this.start.plus(dur.mapUnits((x) => x * idx));\n      next = +added > +this.e ? this.e : added;\n      results.push(Interval.fromDateTimes(s, next));\n      s = next;\n      idx += 1;\n    }\n\n    return results;\n  }\n\n  /**\n   * Split this Interval into the specified number of smaller intervals.\n   * @param {number} numberOfParts - The number of Intervals to divide the Interval into.\n   * @return {Array}\n   */\n  divideEqually(numberOfParts) {\n    if (!this.isValid) return [];\n    return this.splitBy(this.length() / numberOfParts).slice(0, numberOfParts);\n  }\n\n  /**\n   * Return whether this Interval overlaps with the specified Interval\n   * @param {Interval} other\n   * @return {boolean}\n   */\n  overlaps(other) {\n    return this.e > other.s && this.s < other.e;\n  }\n\n  /**\n   * Return whether this Interval's end is adjacent to the specified Interval's start.\n   * @param {Interval} other\n   * @return {boolean}\n   */\n  abutsStart(other) {\n    if (!this.isValid) return false;\n    return +this.e === +other.s;\n  }\n\n  /**\n   * Return whether this Interval's start is adjacent to the specified Interval's end.\n   * @param {Interval} other\n   * @return {boolean}\n   */\n  abutsEnd(other) {\n    if (!this.isValid) return false;\n    return +other.e === +this.s;\n  }\n\n  /**\n   * Returns true if this Interval fully contains the specified Interval, specifically if the intersect (of this Interval and the other Interval) is equal to the other Interval; false otherwise.\n   * @param {Interval} other\n   * @return {boolean}\n   */\n  engulfs(other) {\n    if (!this.isValid) return false;\n    return this.s <= other.s && this.e >= other.e;\n  }\n\n  /**\n   * Return whether this Interval has the same start and end as the specified Interval.\n   * @param {Interval} other\n   * @return {boolean}\n   */\n  equals(other) {\n    if (!this.isValid || !other.isValid) {\n      return false;\n    }\n\n    return this.s.equals(other.s) && this.e.equals(other.e);\n  }\n\n  /**\n   * Return an Interval representing the intersection of this Interval and the specified Interval.\n   * Specifically, the resulting Interval has the maximum start time and the minimum end time of the two Intervals.\n   * Returns null if the intersection is empty, meaning, the intervals don't intersect.\n   * @param {Interval} other\n   * @return {Interval}\n   */\n  intersection(other) {\n    if (!this.isValid) return this;\n    const s = this.s > other.s ? this.s : other.s,\n      e = this.e < other.e ? this.e : other.e;\n\n    if (s >= e) {\n      return null;\n    } else {\n      return Interval.fromDateTimes(s, e);\n    }\n  }\n\n  /**\n   * Return an Interval representing the union of this Interval and the specified Interval.\n   * Specifically, the resulting Interval has the minimum start time and the maximum end time of the two Intervals.\n   * @param {Interval} other\n   * @return {Interval}\n   */\n  union(other) {\n    if (!this.isValid) return this;\n    const s = this.s < other.s ? this.s : other.s,\n      e = this.e > other.e ? this.e : other.e;\n    return Interval.fromDateTimes(s, e);\n  }\n\n  /**\n   * Merge an array of Intervals into an equivalent minimal set of Intervals.\n   * Combines overlapping and adjacent Intervals.\n   * The resulting array will contain the Intervals in ascending order, that is, starting with the earliest Interval\n   * and ending with the latest.\n   *\n   * @param {Array} intervals\n   * @return {Array}\n   */\n  static merge(intervals) {\n    const [found, final] = intervals\n      .sort((a, b) => a.s - b.s)\n      .reduce(\n        ([sofar, current], item) => {\n          if (!current) {\n            return [sofar, item];\n          } else if (current.overlaps(item) || current.abutsStart(item)) {\n            return [sofar, current.union(item)];\n          } else {\n            return [sofar.concat([current]), item];\n          }\n        },\n        [[], null]\n      );\n    if (final) {\n      found.push(final);\n    }\n    return found;\n  }\n\n  /**\n   * Return an array of Intervals representing the spans of time that only appear in one of the specified Intervals.\n   * @param {Array} intervals\n   * @return {Array}\n   */\n  static xor(intervals) {\n    let start = null,\n      currentCount = 0;\n    const results = [],\n      ends = intervals.map((i) => [\n        { time: i.s, type: \"s\" },\n        { time: i.e, type: \"e\" },\n      ]),\n      flattened = Array.prototype.concat(...ends),\n      arr = flattened.sort((a, b) => a.time - b.time);\n\n    for (const i of arr) {\n      currentCount += i.type === \"s\" ? 1 : -1;\n\n      if (currentCount === 1) {\n        start = i.time;\n      } else {\n        if (start && +start !== +i.time) {\n          results.push(Interval.fromDateTimes(start, i.time));\n        }\n\n        start = null;\n      }\n    }\n\n    return Interval.merge(results);\n  }\n\n  /**\n   * Return an Interval representing the span of time in this Interval that doesn't overlap with any of the specified Intervals.\n   * @param {...Interval} intervals\n   * @return {Array}\n   */\n  difference(...intervals) {\n    return Interval.xor([this].concat(intervals))\n      .map((i) => this.intersection(i))\n      .filter((i) => i && !i.isEmpty());\n  }\n\n  /**\n   * Returns a string representation of this Interval appropriate for debugging.\n   * @return {string}\n   */\n  toString() {\n    if (!this.isValid) return INVALID;\n    return `[${this.s.toISO()} – ${this.e.toISO()})`;\n  }\n\n  /**\n   * Returns a string representation of this Interval appropriate for the REPL.\n   * @return {string}\n   */\n  [Symbol.for(\"nodejs.util.inspect.custom\")]() {\n    if (this.isValid) {\n      return `Interval { start: ${this.s.toISO()}, end: ${this.e.toISO()} }`;\n    } else {\n      return `Interval { Invalid, reason: ${this.invalidReason} }`;\n    }\n  }\n\n  /**\n   * Returns a localized string representing this Interval. Accepts the same options as the\n   * Intl.DateTimeFormat constructor and any presets defined by Luxon, such as\n   * {@link DateTime.DATE_FULL} or {@link DateTime.TIME_SIMPLE}. The exact behavior of this method\n   * is browser-specific, but in general it will return an appropriate representation of the\n   * Interval in the assigned locale. Defaults to the system's locale if no locale has been\n   * specified.\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat\n   * @param {Object} [formatOpts=DateTime.DATE_SHORT] - Either a DateTime preset or\n   * Intl.DateTimeFormat constructor options.\n   * @param {Object} opts - Options to override the configuration of the start DateTime.\n   * @example Interval.fromISO('2022-11-07T09:00Z/2022-11-08T09:00Z').toLocaleString(); //=> 11/7/2022 – 11/8/2022\n   * @example Interval.fromISO('2022-11-07T09:00Z/2022-11-08T09:00Z').toLocaleString(DateTime.DATE_FULL); //=> November 7 – 8, 2022\n   * @example Interval.fromISO('2022-11-07T09:00Z/2022-11-08T09:00Z').toLocaleString(DateTime.DATE_FULL, { locale: 'fr-FR' }); //=> 7–8 novembre 2022\n   * @example Interval.fromISO('2022-11-07T17:00Z/2022-11-07T19:00Z').toLocaleString(DateTime.TIME_SIMPLE); //=> 6:00 – 8:00 PM\n   * @example Interval.fromISO('2022-11-07T17:00Z/2022-11-07T19:00Z').toLocaleString({ weekday: 'short', month: 'short', day: '2-digit', hour: '2-digit', minute: '2-digit' }); //=> Mon, Nov 07, 6:00 – 8:00 p\n   * @return {string}\n   */\n  toLocaleString(formatOpts = Formats.DATE_SHORT, opts = {}) {\n    return this.isValid\n      ? Formatter.create(this.s.loc.clone(opts), formatOpts).formatInterval(this)\n      : INVALID;\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this Interval.\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Time_intervals\n   * @param {Object} opts - The same options as {@link DateTime#toISO}\n   * @return {string}\n   */\n  toISO(opts) {\n    if (!this.isValid) return INVALID;\n    return `${this.s.toISO(opts)}/${this.e.toISO(opts)}`;\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of date of this Interval.\n   * The time components are ignored.\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Time_intervals\n   * @return {string}\n   */\n  toISODate() {\n    if (!this.isValid) return INVALID;\n    return `${this.s.toISODate()}/${this.e.toISODate()}`;\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of time of this Interval.\n   * The date components are ignored.\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Time_intervals\n   * @param {Object} opts - The same options as {@link DateTime#toISO}\n   * @return {string}\n   */\n  toISOTime(opts) {\n    if (!this.isValid) return INVALID;\n    return `${this.s.toISOTime(opts)}/${this.e.toISOTime(opts)}`;\n  }\n\n  /**\n   * Returns a string representation of this Interval formatted according to the specified format\n   * string. **You may not want this.** See {@link Interval#toLocaleString} for a more flexible\n   * formatting tool.\n   * @param {string} dateFormat - The format string. This string formats the start and end time.\n   * See {@link DateTime#toFormat} for details.\n   * @param {Object} opts - Options.\n   * @param {string} [opts.separator =  ' – '] - A separator to place between the start and end\n   * representations.\n   * @return {string}\n   */\n  toFormat(dateFormat, { separator = \" – \" } = {}) {\n    if (!this.isValid) return INVALID;\n    return `${this.s.toFormat(dateFormat)}${separator}${this.e.toFormat(dateFormat)}`;\n  }\n\n  /**\n   * Return a Duration representing the time spanned by this interval.\n   * @param {string|string[]} [unit=['milliseconds']] - the unit or units (such as 'hours' or 'days') to include in the duration.\n   * @param {Object} opts - options that affect the creation of the Duration\n   * @param {string} [opts.conversionAccuracy='casual'] - the conversion system to use\n   * @example Interval.fromDateTimes(dt1, dt2).toDuration().toObject() //=> { milliseconds: 88489257 }\n   * @example Interval.fromDateTimes(dt1, dt2).toDuration('days').toObject() //=> { days: 1.0241812152777778 }\n   * @example Interval.fromDateTimes(dt1, dt2).toDuration(['hours', 'minutes']).toObject() //=> { hours: 24, minutes: 34.82095 }\n   * @example Interval.fromDateTimes(dt1, dt2).toDuration(['hours', 'minutes', 'seconds']).toObject() //=> { hours: 24, minutes: 34, seconds: 49.257 }\n   * @example Interval.fromDateTimes(dt1, dt2).toDuration('seconds').toObject() //=> { seconds: 88489.257 }\n   * @return {Duration}\n   */\n  toDuration(unit, opts) {\n    if (!this.isValid) {\n      return Duration.invalid(this.invalidReason);\n    }\n    return this.e.diff(this.s, unit, opts);\n  }\n\n  /**\n   * Run mapFn on the interval start and end, returning a new Interval from the resulting DateTimes\n   * @param {function} mapFn\n   * @return {Interval}\n   * @example Interval.fromDateTimes(dt1, dt2).mapEndpoints(endpoint => endpoint.toUTC())\n   * @example Interval.fromDateTimes(dt1, dt2).mapEndpoints(endpoint => endpoint.plus({ hours: 2 }))\n   */\n  mapEndpoints(mapFn) {\n    return Interval.fromDateTimes(mapFn(this.s), mapFn(this.e));\n  }\n}\n", "import DateTime from \"./datetime.js\";\nimport Settings from \"./settings.js\";\nimport Locale from \"./impl/locale.js\";\nimport IANAZone from \"./zones/IANAZone.js\";\nimport { normalizeZone } from \"./impl/zoneUtil.js\";\n\nimport { hasLocaleWeekInfo, hasRelative } from \"./impl/util.js\";\n\n/**\n * The Info class contains static methods for retrieving general time and date related data. For example, it has methods for finding out if a time zone has a DST, for listing the months in any supported locale, and for discovering which of Luxon features are available in the current environment.\n */\nexport default class Info {\n  /**\n   * Return whether the specified zone contains a DST.\n   * @param {string|Zone} [zone='local'] - Zone to check. Defaults to the environment's local zone.\n   * @return {boolean}\n   */\n  static hasDST(zone = Settings.defaultZone) {\n    const proto = DateTime.now().setZone(zone).set({ month: 12 });\n\n    return !zone.isUniversal && proto.offset !== proto.set({ month: 6 }).offset;\n  }\n\n  /**\n   * Return whether the specified zone is a valid IANA specifier.\n   * @param {string} zone - Zone to check\n   * @return {boolean}\n   */\n  static isValidIANAZone(zone) {\n    return IANAZone.isValidZone(zone);\n  }\n\n  /**\n   * Converts the input into a {@link Zone} instance.\n   *\n   * * If `input` is already a Zone instance, it is returned unchanged.\n   * * If `input` is a string containing a valid time zone name, a Zone instance\n   *   with that name is returned.\n   * * If `input` is a string that doesn't refer to a known time zone, a Zone\n   *   instance with {@link Zone#isValid} == false is returned.\n   * * If `input is a number, a Zone instance with the specified fixed offset\n   *   in minutes is returned.\n   * * If `input` is `null` or `undefined`, the default zone is returned.\n   * @param {string|Zone|number} [input] - the value to be converted\n   * @return {Zone}\n   */\n  static normalizeZone(input) {\n    return normalizeZone(input, Settings.defaultZone);\n  }\n\n  /**\n   * Get the weekday on which the week starts according to the given locale.\n   * @param {Object} opts - options\n   * @param {string} [opts.locale] - the locale code\n   * @param {string} [opts.locObj=null] - an existing locale object to use\n   * @returns {number} the start of the week, 1 for Monday through 7 for Sunday\n   */\n  static getStartOfWeek({ locale = null, locObj = null } = {}) {\n    return (locObj || Locale.create(locale)).getStartOfWeek();\n  }\n\n  /**\n   * Get the minimum number of days necessary in a week before it is considered part of the next year according\n   * to the given locale.\n   * @param {Object} opts - options\n   * @param {string} [opts.locale] - the locale code\n   * @param {string} [opts.locObj=null] - an existing locale object to use\n   * @returns {number}\n   */\n  static getMinimumDaysInFirstWeek({ locale = null, locObj = null } = {}) {\n    return (locObj || Locale.create(locale)).getMinDaysInFirstWeek();\n  }\n\n  /**\n   * Get the weekdays, which are considered the weekend according to the given locale\n   * @param {Object} opts - options\n   * @param {string} [opts.locale] - the locale code\n   * @param {string} [opts.locObj=null] - an existing locale object to use\n   * @returns {number[]} an array of weekdays, 1 for Monday through 7 for Sunday\n   */\n  static getWeekendWeekdays({ locale = null, locObj = null } = {}) {\n    // copy the array, because we cache it internally\n    return (locObj || Locale.create(locale)).getWeekendDays().slice();\n  }\n\n  /**\n   * Return an array of standalone month names.\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat\n   * @param {string} [length='long'] - the length of the month representation, such as \"numeric\", \"2-digit\", \"narrow\", \"short\", \"long\"\n   * @param {Object} opts - options\n   * @param {string} [opts.locale] - the locale code\n   * @param {string} [opts.numberingSystem=null] - the numbering system\n   * @param {string} [opts.locObj=null] - an existing locale object to use\n   * @param {string} [opts.outputCalendar='gregory'] - the calendar\n   * @example Info.months()[0] //=> 'January'\n   * @example Info.months('short')[0] //=> 'Jan'\n   * @example Info.months('numeric')[0] //=> '1'\n   * @example Info.months('short', { locale: 'fr-CA' } )[0] //=> 'janv.'\n   * @example Info.months('numeric', { locale: 'ar' })[0] //=> '١'\n   * @example Info.months('long', { outputCalendar: 'islamic' })[0] //=> 'Rabiʻ I'\n   * @return {Array}\n   */\n  static months(\n    length = \"long\",\n    { locale = null, numberingSystem = null, locObj = null, outputCalendar = \"gregory\" } = {}\n  ) {\n    return (locObj || Locale.create(locale, numberingSystem, outputCalendar)).months(length);\n  }\n\n  /**\n   * Return an array of format month names.\n   * Format months differ from standalone months in that they're meant to appear next to the day of the month. In some languages, that\n   * changes the string.\n   * See {@link Info#months}\n   * @param {string} [length='long'] - the length of the month representation, such as \"numeric\", \"2-digit\", \"narrow\", \"short\", \"long\"\n   * @param {Object} opts - options\n   * @param {string} [opts.locale] - the locale code\n   * @param {string} [opts.numberingSystem=null] - the numbering system\n   * @param {string} [opts.locObj=null] - an existing locale object to use\n   * @param {string} [opts.outputCalendar='gregory'] - the calendar\n   * @return {Array}\n   */\n  static monthsFormat(\n    length = \"long\",\n    { locale = null, numberingSystem = null, locObj = null, outputCalendar = \"gregory\" } = {}\n  ) {\n    return (locObj || Locale.create(locale, numberingSystem, outputCalendar)).months(length, true);\n  }\n\n  /**\n   * Return an array of standalone week names.\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat\n   * @param {string} [length='long'] - the length of the weekday representation, such as \"narrow\", \"short\", \"long\".\n   * @param {Object} opts - options\n   * @param {string} [opts.locale] - the locale code\n   * @param {string} [opts.numberingSystem=null] - the numbering system\n   * @param {string} [opts.locObj=null] - an existing locale object to use\n   * @example Info.weekdays()[0] //=> 'Monday'\n   * @example Info.weekdays('short')[0] //=> 'Mon'\n   * @example Info.weekdays('short', { locale: 'fr-CA' })[0] //=> 'lun.'\n   * @example Info.weekdays('short', { locale: 'ar' })[0] //=> 'الاثنين'\n   * @return {Array}\n   */\n  static weekdays(length = \"long\", { locale = null, numberingSystem = null, locObj = null } = {}) {\n    return (locObj || Locale.create(locale, numberingSystem, null)).weekdays(length);\n  }\n\n  /**\n   * Return an array of format week names.\n   * Format weekdays differ from standalone weekdays in that they're meant to appear next to more date information. In some languages, that\n   * changes the string.\n   * See {@link Info#weekdays}\n   * @param {string} [length='long'] - the length of the month representation, such as \"narrow\", \"short\", \"long\".\n   * @param {Object} opts - options\n   * @param {string} [opts.locale=null] - the locale code\n   * @param {string} [opts.numberingSystem=null] - the numbering system\n   * @param {string} [opts.locObj=null] - an existing locale object to use\n   * @return {Array}\n   */\n  static weekdaysFormat(\n    length = \"long\",\n    { locale = null, numberingSystem = null, locObj = null } = {}\n  ) {\n    return (locObj || Locale.create(locale, numberingSystem, null)).weekdays(length, true);\n  }\n\n  /**\n   * Return an array of meridiems.\n   * @param {Object} opts - options\n   * @param {string} [opts.locale] - the locale code\n   * @example Info.meridiems() //=> [ 'AM', 'PM' ]\n   * @example Info.meridiems({ locale: 'my' }) //=> [ 'နံနက်', 'ညနေ' ]\n   * @return {Array}\n   */\n  static meridiems({ locale = null } = {}) {\n    return Locale.create(locale).meridiems();\n  }\n\n  /**\n   * Return an array of eras, such as ['BC', 'AD']. The locale can be specified, but the calendar system is always Gregorian.\n   * @param {string} [length='short'] - the length of the era representation, such as \"short\" or \"long\".\n   * @param {Object} opts - options\n   * @param {string} [opts.locale] - the locale code\n   * @example Info.eras() //=> [ 'BC', 'AD' ]\n   * @example Info.eras('long') //=> [ 'Before Christ', 'Anno Domini' ]\n   * @example Info.eras('long', { locale: 'fr' }) //=> [ 'avant Jésus-Christ', 'après Jésus-Christ' ]\n   * @return {Array}\n   */\n  static eras(length = \"short\", { locale = null } = {}) {\n    return Locale.create(locale, null, \"gregory\").eras(length);\n  }\n\n  /**\n   * Return the set of available features in this environment.\n   * Some features of Luxon are not available in all environments. For example, on older browsers, relative time formatting support is not available. Use this function to figure out if that's the case.\n   * Keys:\n   * * `relative`: whether this environment supports relative time formatting\n   * * `localeWeek`: whether this environment supports different weekdays for the start of the week based on the locale\n   * @example Info.features() //=> { relative: false, localeWeek: true }\n   * @return {Object}\n   */\n  static features() {\n    return { relative: hasRelative(), localeWeek: hasLocaleWeekInfo() };\n  }\n}\n", "import Duration from \"../duration.js\";\n\nfunction dayDiff(earlier, later) {\n  const utcDayStart = (dt) => dt.toUTC(0, { keepLocalTime: true }).startOf(\"day\").valueOf(),\n    ms = utcDayStart(later) - utcDayStart(earlier);\n  return Math.floor(Duration.fromMillis(ms).as(\"days\"));\n}\n\nfunction highOrderDiffs(cursor, later, units) {\n  const differs = [\n    [\"years\", (a, b) => b.year - a.year],\n    [\"quarters\", (a, b) => b.quarter - a.quarter + (b.year - a.year) * 4],\n    [\"months\", (a, b) => b.month - a.month + (b.year - a.year) * 12],\n    [\n      \"weeks\",\n      (a, b) => {\n        const days = dayDiff(a, b);\n        return (days - (days % 7)) / 7;\n      },\n    ],\n    [\"days\", dayDiff],\n  ];\n\n  const results = {};\n  const earlier = cursor;\n  let lowestOrder, highWater;\n\n  /* This loop tries to diff using larger units first.\n     If we overshoot, we backtrack and try the next smaller unit.\n     \"cursor\" starts out at the earlier timestamp and moves closer and closer to \"later\"\n     as we use smaller and smaller units.\n     highWater keeps track of where we would be if we added one more of the smallest unit,\n     this is used later to potentially convert any difference smaller than the smallest higher order unit\n     into a fraction of that smallest higher order unit\n  */\n  for (const [unit, differ] of differs) {\n    if (units.indexOf(unit) >= 0) {\n      lowestOrder = unit;\n\n      results[unit] = differ(cursor, later);\n      highWater = earlier.plus(results);\n\n      if (highWater > later) {\n        // we overshot the end point, backtrack cursor by 1\n        results[unit]--;\n        cursor = earlier.plus(results);\n\n        // if we are still overshooting now, we need to backtrack again\n        // this happens in certain situations when diffing times in different zones,\n        // because this calculation ignores time zones\n        if (cursor > later) {\n          // keep the \"overshot by 1\" around as highWater\n          highWater = cursor;\n          // backtrack cursor by 1\n          results[unit]--;\n          cursor = earlier.plus(results);\n        }\n      } else {\n        cursor = highWater;\n      }\n    }\n  }\n\n  return [cursor, results, highWater, lowestOrder];\n}\n\nexport default function (earlier, later, units, opts) {\n  let [cursor, results, highWater, lowestOrder] = highOrderDiffs(earlier, later, units);\n\n  const remainingMillis = later - cursor;\n\n  const lowerOrderUnits = units.filter(\n    (u) => [\"hours\", \"minutes\", \"seconds\", \"milliseconds\"].indexOf(u) >= 0\n  );\n\n  if (lowerOrderUnits.length === 0) {\n    if (highWater < later) {\n      highWater = cursor.plus({ [lowestOrder]: 1 });\n    }\n\n    if (highWater !== cursor) {\n      results[lowestOrder] = (results[lowestOrder] || 0) + remainingMillis / (highWater - cursor);\n    }\n  }\n\n  const duration = Duration.fromObject(results, opts);\n\n  if (lowerOrderUnits.length > 0) {\n    return Duration.fromMillis(remainingMillis, opts)\n      .shiftTo(...lowerOrderUnits)\n      .plus(duration);\n  } else {\n    return duration;\n  }\n}\n", "import { parseM<PERSON>is, isUndefined, untruncate<PERSON>ear, signedOffset, hasOwnProperty } from \"./util.js\";\nimport Formatter from \"./formatter.js\";\nimport FixedOffsetZone from \"../zones/fixedOffsetZone.js\";\nimport IANAZone from \"../zones/IANAZone.js\";\nimport DateTime from \"../datetime.js\";\nimport { digitRegex, parseDigits } from \"./digits.js\";\nimport { ConflictingSpecificationError } from \"../errors.js\";\n\nconst MISSING_FTP = \"missing Intl.DateTimeFormat.formatToParts support\";\n\nfunction intUnit(regex, post = (i) => i) {\n  return { regex, deser: ([s]) => post(parseDigits(s)) };\n}\n\nconst NBSP = String.fromCharCode(160);\nconst spaceOrNBSP = `[ ${NBSP}]`;\nconst spaceOrNBSPRegExp = new RegExp(spaceOrNBSP, \"g\");\n\nfunction fixListRegex(s) {\n  // make dots optional and also make them literal\n  // make space and non breakable space characters interchangeable\n  return s.replace(/\\./g, \"\\\\.?\").replace(spaceOrNBSPRegExp, spaceOrNBSP);\n}\n\nfunction stripInsensitivities(s) {\n  return s\n    .replace(/\\./g, \"\") // ignore dots that were made optional\n    .replace(spaceOrNBSPRegExp, \" \") // interchange space and nbsp\n    .toLowerCase();\n}\n\nfunction oneOf(strings, startIndex) {\n  if (strings === null) {\n    return null;\n  } else {\n    return {\n      regex: RegExp(strings.map(fixListRegex).join(\"|\")),\n      deser: ([s]) =>\n        strings.findIndex((i) => stripInsensitivities(s) === stripInsensitivities(i)) + startIndex,\n    };\n  }\n}\n\nfunction offset(regex, groups) {\n  return { regex, deser: ([, h, m]) => signedOffset(h, m), groups };\n}\n\nfunction simple(regex) {\n  return { regex, deser: ([s]) => s };\n}\n\nfunction escapeToken(value) {\n  return value.replace(/[\\-\\[\\]{}()*+?.,\\\\\\^$|#\\s]/g, \"\\\\$&\");\n}\n\n/**\n * @param token\n * @param {Locale} loc\n */\nfunction unitForToken(token, loc) {\n  const one = digitRegex(loc),\n    two = digitRegex(loc, \"{2}\"),\n    three = digitRegex(loc, \"{3}\"),\n    four = digitRegex(loc, \"{4}\"),\n    six = digitRegex(loc, \"{6}\"),\n    oneOrTwo = digitRegex(loc, \"{1,2}\"),\n    oneToThree = digitRegex(loc, \"{1,3}\"),\n    oneToSix = digitRegex(loc, \"{1,6}\"),\n    oneToNine = digitRegex(loc, \"{1,9}\"),\n    twoToFour = digitRegex(loc, \"{2,4}\"),\n    fourToSix = digitRegex(loc, \"{4,6}\"),\n    literal = (t) => ({ regex: RegExp(escapeToken(t.val)), deser: ([s]) => s, literal: true }),\n    unitate = (t) => {\n      if (token.literal) {\n        return literal(t);\n      }\n      switch (t.val) {\n        // era\n        case \"G\":\n          return oneOf(loc.eras(\"short\"), 0);\n        case \"GG\":\n          return oneOf(loc.eras(\"long\"), 0);\n        // years\n        case \"y\":\n          return intUnit(oneToSix);\n        case \"yy\":\n          return intUnit(twoToFour, untruncateYear);\n        case \"yyyy\":\n          return intUnit(four);\n        case \"yyyyy\":\n          return intUnit(fourToSix);\n        case \"yyyyyy\":\n          return intUnit(six);\n        // months\n        case \"M\":\n          return intUnit(oneOrTwo);\n        case \"MM\":\n          return intUnit(two);\n        case \"MMM\":\n          return oneOf(loc.months(\"short\", true), 1);\n        case \"MMMM\":\n          return oneOf(loc.months(\"long\", true), 1);\n        case \"L\":\n          return intUnit(oneOrTwo);\n        case \"LL\":\n          return intUnit(two);\n        case \"LLL\":\n          return oneOf(loc.months(\"short\", false), 1);\n        case \"LLLL\":\n          return oneOf(loc.months(\"long\", false), 1);\n        // dates\n        case \"d\":\n          return intUnit(oneOrTwo);\n        case \"dd\":\n          return intUnit(two);\n        // ordinals\n        case \"o\":\n          return intUnit(oneToThree);\n        case \"ooo\":\n          return intUnit(three);\n        // time\n        case \"HH\":\n          return intUnit(two);\n        case \"H\":\n          return intUnit(oneOrTwo);\n        case \"hh\":\n          return intUnit(two);\n        case \"h\":\n          return intUnit(oneOrTwo);\n        case \"mm\":\n          return intUnit(two);\n        case \"m\":\n          return intUnit(oneOrTwo);\n        case \"q\":\n          return intUnit(oneOrTwo);\n        case \"qq\":\n          return intUnit(two);\n        case \"s\":\n          return intUnit(oneOrTwo);\n        case \"ss\":\n          return intUnit(two);\n        case \"S\":\n          return intUnit(oneToThree);\n        case \"SSS\":\n          return intUnit(three);\n        case \"u\":\n          return simple(oneToNine);\n        case \"uu\":\n          return simple(oneOrTwo);\n        case \"uuu\":\n          return intUnit(one);\n        // meridiem\n        case \"a\":\n          return oneOf(loc.meridiems(), 0);\n        // weekYear (k)\n        case \"kkkk\":\n          return intUnit(four);\n        case \"kk\":\n          return intUnit(twoToFour, untruncateYear);\n        // weekNumber (W)\n        case \"W\":\n          return intUnit(oneOrTwo);\n        case \"WW\":\n          return intUnit(two);\n        // weekdays\n        case \"E\":\n        case \"c\":\n          return intUnit(one);\n        case \"EEE\":\n          return oneOf(loc.weekdays(\"short\", false), 1);\n        case \"EEEE\":\n          return oneOf(loc.weekdays(\"long\", false), 1);\n        case \"ccc\":\n          return oneOf(loc.weekdays(\"short\", true), 1);\n        case \"cccc\":\n          return oneOf(loc.weekdays(\"long\", true), 1);\n        // offset/zone\n        case \"Z\":\n        case \"ZZ\":\n          return offset(new RegExp(`([+-]${oneOrTwo.source})(?::(${two.source}))?`), 2);\n        case \"ZZZ\":\n          return offset(new RegExp(`([+-]${oneOrTwo.source})(${two.source})?`), 2);\n        // we don't support ZZZZ (PST) or ZZZZZ (Pacific Standard Time) in parsing\n        // because we don't have any way to figure out what they are\n        case \"z\":\n          return simple(/[a-z_+-/]{1,256}?/i);\n        // this special-case \"token\" represents a place where a macro-token expanded into a white-space literal\n        // in this case we accept any non-newline white-space\n        case \" \":\n          return simple(/[^\\S\\n\\r]/);\n        default:\n          return literal(t);\n      }\n    };\n\n  const unit = unitate(token) || {\n    invalidReason: MISSING_FTP,\n  };\n\n  unit.token = token;\n\n  return unit;\n}\n\nconst partTypeStyleToTokenVal = {\n  year: {\n    \"2-digit\": \"yy\",\n    numeric: \"yyyyy\",\n  },\n  month: {\n    numeric: \"M\",\n    \"2-digit\": \"MM\",\n    short: \"MMM\",\n    long: \"MMMM\",\n  },\n  day: {\n    numeric: \"d\",\n    \"2-digit\": \"dd\",\n  },\n  weekday: {\n    short: \"EEE\",\n    long: \"EEEE\",\n  },\n  dayperiod: \"a\",\n  dayPeriod: \"a\",\n  hour12: {\n    numeric: \"h\",\n    \"2-digit\": \"hh\",\n  },\n  hour24: {\n    numeric: \"H\",\n    \"2-digit\": \"HH\",\n  },\n  minute: {\n    numeric: \"m\",\n    \"2-digit\": \"mm\",\n  },\n  second: {\n    numeric: \"s\",\n    \"2-digit\": \"ss\",\n  },\n  timeZoneName: {\n    long: \"ZZZZZ\",\n    short: \"ZZZ\",\n  },\n};\n\nfunction tokenForPart(part, formatOpts, resolvedOpts) {\n  const { type, value } = part;\n\n  if (type === \"literal\") {\n    const isSpace = /^\\s+$/.test(value);\n    return {\n      literal: !isSpace,\n      val: isSpace ? \" \" : value,\n    };\n  }\n\n  const style = formatOpts[type];\n\n  // The user might have explicitly specified hour12 or hourCycle\n  // if so, respect their decision\n  // if not, refer back to the resolvedOpts, which are based on the locale\n  let actualType = type;\n  if (type === \"hour\") {\n    if (formatOpts.hour12 != null) {\n      actualType = formatOpts.hour12 ? \"hour12\" : \"hour24\";\n    } else if (formatOpts.hourCycle != null) {\n      if (formatOpts.hourCycle === \"h11\" || formatOpts.hourCycle === \"h12\") {\n        actualType = \"hour12\";\n      } else {\n        actualType = \"hour24\";\n      }\n    } else {\n      // tokens only differentiate between 24 hours or not,\n      // so we do not need to check hourCycle here, which is less supported anyways\n      actualType = resolvedOpts.hour12 ? \"hour12\" : \"hour24\";\n    }\n  }\n  let val = partTypeStyleToTokenVal[actualType];\n  if (typeof val === \"object\") {\n    val = val[style];\n  }\n\n  if (val) {\n    return {\n      literal: false,\n      val,\n    };\n  }\n\n  return undefined;\n}\n\nfunction buildRegex(units) {\n  const re = units.map((u) => u.regex).reduce((f, r) => `${f}(${r.source})`, \"\");\n  return [`^${re}$`, units];\n}\n\nfunction match(input, regex, handlers) {\n  const matches = input.match(regex);\n\n  if (matches) {\n    const all = {};\n    let matchIndex = 1;\n    for (const i in handlers) {\n      if (hasOwnProperty(handlers, i)) {\n        const h = handlers[i],\n          groups = h.groups ? h.groups + 1 : 1;\n        if (!h.literal && h.token) {\n          all[h.token.val[0]] = h.deser(matches.slice(matchIndex, matchIndex + groups));\n        }\n        matchIndex += groups;\n      }\n    }\n    return [matches, all];\n  } else {\n    return [matches, {}];\n  }\n}\n\nfunction dateTimeFromMatches(matches) {\n  const toField = (token) => {\n    switch (token) {\n      case \"S\":\n        return \"millisecond\";\n      case \"s\":\n        return \"second\";\n      case \"m\":\n        return \"minute\";\n      case \"h\":\n      case \"H\":\n        return \"hour\";\n      case \"d\":\n        return \"day\";\n      case \"o\":\n        return \"ordinal\";\n      case \"L\":\n      case \"M\":\n        return \"month\";\n      case \"y\":\n        return \"year\";\n      case \"E\":\n      case \"c\":\n        return \"weekday\";\n      case \"W\":\n        return \"weekNumber\";\n      case \"k\":\n        return \"weekYear\";\n      case \"q\":\n        return \"quarter\";\n      default:\n        return null;\n    }\n  };\n\n  let zone = null;\n  let specificOffset;\n  if (!isUndefined(matches.z)) {\n    zone = IANAZone.create(matches.z);\n  }\n\n  if (!isUndefined(matches.Z)) {\n    if (!zone) {\n      zone = new FixedOffsetZone(matches.Z);\n    }\n    specificOffset = matches.Z;\n  }\n\n  if (!isUndefined(matches.q)) {\n    matches.M = (matches.q - 1) * 3 + 1;\n  }\n\n  if (!isUndefined(matches.h)) {\n    if (matches.h < 12 && matches.a === 1) {\n      matches.h += 12;\n    } else if (matches.h === 12 && matches.a === 0) {\n      matches.h = 0;\n    }\n  }\n\n  if (matches.G === 0 && matches.y) {\n    matches.y = -matches.y;\n  }\n\n  if (!isUndefined(matches.u)) {\n    matches.S = parseMillis(matches.u);\n  }\n\n  const vals = Object.keys(matches).reduce((r, k) => {\n    const f = toField(k);\n    if (f) {\n      r[f] = matches[k];\n    }\n\n    return r;\n  }, {});\n\n  return [vals, zone, specificOffset];\n}\n\nlet dummyDateTimeCache = null;\n\nfunction getDummyDateTime() {\n  if (!dummyDateTimeCache) {\n    dummyDateTimeCache = DateTime.fromMillis(1555555555555);\n  }\n\n  return dummyDateTimeCache;\n}\n\nfunction maybeExpandMacroToken(token, locale) {\n  if (token.literal) {\n    return token;\n  }\n\n  const formatOpts = Formatter.macroTokenToFormatOpts(token.val);\n  const tokens = formatOptsToTokens(formatOpts, locale);\n\n  if (tokens == null || tokens.includes(undefined)) {\n    return token;\n  }\n\n  return tokens;\n}\n\nexport function expandMacroTokens(tokens, locale) {\n  return Array.prototype.concat(...tokens.map((t) => maybeExpandMacroToken(t, locale)));\n}\n\n/**\n * @private\n */\n\nexport class TokenParser {\n  constructor(locale, format) {\n    this.locale = locale;\n    this.format = format;\n    this.tokens = expandMacroTokens(Formatter.parseFormat(format), locale);\n    this.units = this.tokens.map((t) => unitForToken(t, locale));\n    this.disqualifyingUnit = this.units.find((t) => t.invalidReason);\n\n    if (!this.disqualifyingUnit) {\n      const [regexString, handlers] = buildRegex(this.units);\n      this.regex = RegExp(regexString, \"i\");\n      this.handlers = handlers;\n    }\n  }\n\n  explainFromTokens(input) {\n    if (!this.isValid) {\n      return { input, tokens: this.tokens, invalidReason: this.invalidReason };\n    } else {\n      const [rawMatches, matches] = match(input, this.regex, this.handlers),\n        [result, zone, specificOffset] = matches\n          ? dateTimeFromMatches(matches)\n          : [null, null, undefined];\n      if (hasOwnProperty(matches, \"a\") && hasOwnProperty(matches, \"H\")) {\n        throw new ConflictingSpecificationError(\n          \"Can't include meridiem when specifying 24-hour format\"\n        );\n      }\n      return {\n        input,\n        tokens: this.tokens,\n        regex: this.regex,\n        rawMatches,\n        matches,\n        result,\n        zone,\n        specificOffset,\n      };\n    }\n  }\n\n  get isValid() {\n    return !this.disqualifyingUnit;\n  }\n\n  get invalidReason() {\n    return this.disqualifyingUnit ? this.disqualifyingUnit.invalidReason : null;\n  }\n}\n\nexport function explainFromTokens(locale, input, format) {\n  const parser = new TokenParser(locale, format);\n  return parser.explainFromTokens(input);\n}\n\nexport function parseFromTokens(locale, input, format) {\n  const { result, zone, specificOffset, invalidReason } = explainFromTokens(locale, input, format);\n  return [result, zone, specificOffset, invalidReason];\n}\n\nexport function formatOptsToTokens(formatOpts, locale) {\n  if (!formatOpts) {\n    return null;\n  }\n\n  const formatter = Formatter.create(locale, formatOpts);\n  const df = formatter.dtFormatter(getDummyDateTime());\n  const parts = df.formatToParts();\n  const resolvedOpts = df.resolvedOptions();\n  return parts.map((p) => tokenForPart(p, formatOpts, resolvedOpts));\n}\n", "import Duration from \"./duration.js\";\nimport Interval from \"./interval.js\";\nimport Settings from \"./settings.js\";\nimport Info from \"./info.js\";\nimport Formatter from \"./impl/formatter.js\";\nimport FixedOffsetZone from \"./zones/fixedOffsetZone.js\";\nimport Locale from \"./impl/locale.js\";\nimport {\n  isUndefined,\n  maybeArray,\n  isDate,\n  isNumber,\n  bestBy,\n  daysInMonth,\n  daysInYear,\n  isLeapYear,\n  weeksInWeekYear,\n  normalizeObject,\n  roundTo,\n  objToLocalTS,\n  padStart,\n} from \"./impl/util.js\";\nimport { normalizeZone } from \"./impl/zoneUtil.js\";\nimport diff from \"./impl/diff.js\";\nimport { parseRFC2822Date, parseISODate, parseHTTPDate, parseSQL } from \"./impl/regexParser.js\";\nimport {\n  parseFromTokens,\n  explainFromTokens,\n  formatOptsToTokens,\n  expandMacroTokens,\n  TokenParser,\n} from \"./impl/tokenParser.js\";\nimport {\n  gregorianToWeek,\n  weekToGregorian,\n  gregorianToOrdinal,\n  ordinalToGregorian,\n  hasInvalidGregorianData,\n  hasInvalidWeekData,\n  hasInvalidOrdinalData,\n  hasInvalidTimeData,\n  usesLocalWeekValues,\n  isoWeekdayToLocal,\n} from \"./impl/conversions.js\";\nimport * as Formats from \"./impl/formats.js\";\nimport {\n  InvalidArgumentError,\n  ConflictingSpecificationError,\n  InvalidUnitError,\n  InvalidDateTimeError,\n} from \"./errors.js\";\nimport Invalid from \"./impl/invalid.js\";\n\nconst INVALID = \"Invalid DateTime\";\nconst MAX_DATE = 8.64e15;\n\nfunction unsupportedZone(zone) {\n  return new Invalid(\"unsupported zone\", `the zone \"${zone.name}\" is not supported`);\n}\n\n// we cache week data on the DT object and this intermediates the cache\n/**\n * @param {DateTime} dt\n */\nfunction possiblyCachedWeekData(dt) {\n  if (dt.weekData === null) {\n    dt.weekData = gregorianToWeek(dt.c);\n  }\n  return dt.weekData;\n}\n\n/**\n * @param {DateTime} dt\n */\nfunction possiblyCachedLocalWeekData(dt) {\n  if (dt.localWeekData === null) {\n    dt.localWeekData = gregorianToWeek(\n      dt.c,\n      dt.loc.getMinDaysInFirstWeek(),\n      dt.loc.getStartOfWeek()\n    );\n  }\n  return dt.localWeekData;\n}\n\n// clone really means, \"make a new object with these modifications\". all \"setters\" really use this\n// to create a new object while only changing some of the properties\nfunction clone(inst, alts) {\n  const current = {\n    ts: inst.ts,\n    zone: inst.zone,\n    c: inst.c,\n    o: inst.o,\n    loc: inst.loc,\n    invalid: inst.invalid,\n  };\n  return new DateTime({ ...current, ...alts, old: current });\n}\n\n// find the right offset a given local time. The o input is our guess, which determines which\n// offset we'll pick in ambiguous cases (e.g. there are two 3 AMs b/c Fallback DST)\nfunction fixOffset(localTS, o, tz) {\n  // Our UTC time is just a guess because our offset is just a guess\n  let utcGuess = localTS - o * 60 * 1000;\n\n  // Test whether the zone matches the offset for this ts\n  const o2 = tz.offset(utcGuess);\n\n  // If so, offset didn't change and we're done\n  if (o === o2) {\n    return [utcGuess, o];\n  }\n\n  // If not, change the ts by the difference in the offset\n  utcGuess -= (o2 - o) * 60 * 1000;\n\n  // If that gives us the local time we want, we're done\n  const o3 = tz.offset(utcGuess);\n  if (o2 === o3) {\n    return [utcGuess, o2];\n  }\n\n  // If it's different, we're in a hole time. The offset has changed, but the we don't adjust the time\n  return [localTS - Math.min(o2, o3) * 60 * 1000, Math.max(o2, o3)];\n}\n\n// convert an epoch timestamp into a calendar object with the given offset\nfunction tsToObj(ts, offset) {\n  ts += offset * 60 * 1000;\n\n  const d = new Date(ts);\n\n  return {\n    year: d.getUTCFullYear(),\n    month: d.getUTCMonth() + 1,\n    day: d.getUTCDate(),\n    hour: d.getUTCHours(),\n    minute: d.getUTCMinutes(),\n    second: d.getUTCSeconds(),\n    millisecond: d.getUTCMilliseconds(),\n  };\n}\n\n// convert a calendar object to a epoch timestamp\nfunction objToTS(obj, offset, zone) {\n  return fixOffset(objToLocalTS(obj), offset, zone);\n}\n\n// create a new DT instance by adding a duration, adjusting for DSTs\nfunction adjustTime(inst, dur) {\n  const oPre = inst.o,\n    year = inst.c.year + Math.trunc(dur.years),\n    month = inst.c.month + Math.trunc(dur.months) + Math.trunc(dur.quarters) * 3,\n    c = {\n      ...inst.c,\n      year,\n      month,\n      day:\n        Math.min(inst.c.day, daysInMonth(year, month)) +\n        Math.trunc(dur.days) +\n        Math.trunc(dur.weeks) * 7,\n    },\n    millisToAdd = Duration.fromObject({\n      years: dur.years - Math.trunc(dur.years),\n      quarters: dur.quarters - Math.trunc(dur.quarters),\n      months: dur.months - Math.trunc(dur.months),\n      weeks: dur.weeks - Math.trunc(dur.weeks),\n      days: dur.days - Math.trunc(dur.days),\n      hours: dur.hours,\n      minutes: dur.minutes,\n      seconds: dur.seconds,\n      milliseconds: dur.milliseconds,\n    }).as(\"milliseconds\"),\n    localTS = objToLocalTS(c);\n\n  let [ts, o] = fixOffset(localTS, oPre, inst.zone);\n\n  if (millisToAdd !== 0) {\n    ts += millisToAdd;\n    // that could have changed the offset by going over a DST, but we want to keep the ts the same\n    o = inst.zone.offset(ts);\n  }\n\n  return { ts, o };\n}\n\n// helper useful in turning the results of parsing into real dates\n// by handling the zone options\nfunction parseDataToDateTime(parsed, parsedZone, opts, format, text, specificOffset) {\n  const { setZone, zone } = opts;\n  if ((parsed && Object.keys(parsed).length !== 0) || parsedZone) {\n    const interpretationZone = parsedZone || zone,\n      inst = DateTime.fromObject(parsed, {\n        ...opts,\n        zone: interpretationZone,\n        specificOffset,\n      });\n    return setZone ? inst : inst.setZone(zone);\n  } else {\n    return DateTime.invalid(\n      new Invalid(\"unparsable\", `the input \"${text}\" can't be parsed as ${format}`)\n    );\n  }\n}\n\n// if you want to output a technical format (e.g. RFC 2822), this helper\n// helps handle the details\nfunction toTechFormat(dt, format, allowZ = true) {\n  return dt.isValid\n    ? Formatter.create(Locale.create(\"en-US\"), {\n        allowZ,\n        forceSimple: true,\n      }).formatDateTimeFromString(dt, format)\n    : null;\n}\n\nfunction toISODate(o, extended) {\n  const longFormat = o.c.year > 9999 || o.c.year < 0;\n  let c = \"\";\n  if (longFormat && o.c.year >= 0) c += \"+\";\n  c += padStart(o.c.year, longFormat ? 6 : 4);\n\n  if (extended) {\n    c += \"-\";\n    c += padStart(o.c.month);\n    c += \"-\";\n    c += padStart(o.c.day);\n  } else {\n    c += padStart(o.c.month);\n    c += padStart(o.c.day);\n  }\n  return c;\n}\n\nfunction toISOTime(\n  o,\n  extended,\n  suppressSeconds,\n  suppressMilliseconds,\n  includeOffset,\n  extendedZone\n) {\n  let c = padStart(o.c.hour);\n  if (extended) {\n    c += \":\";\n    c += padStart(o.c.minute);\n    if (o.c.millisecond !== 0 || o.c.second !== 0 || !suppressSeconds) {\n      c += \":\";\n    }\n  } else {\n    c += padStart(o.c.minute);\n  }\n\n  if (o.c.millisecond !== 0 || o.c.second !== 0 || !suppressSeconds) {\n    c += padStart(o.c.second);\n\n    if (o.c.millisecond !== 0 || !suppressMilliseconds) {\n      c += \".\";\n      c += padStart(o.c.millisecond, 3);\n    }\n  }\n\n  if (includeOffset) {\n    if (o.isOffsetFixed && o.offset === 0 && !extendedZone) {\n      c += \"Z\";\n    } else if (o.o < 0) {\n      c += \"-\";\n      c += padStart(Math.trunc(-o.o / 60));\n      c += \":\";\n      c += padStart(Math.trunc(-o.o % 60));\n    } else {\n      c += \"+\";\n      c += padStart(Math.trunc(o.o / 60));\n      c += \":\";\n      c += padStart(Math.trunc(o.o % 60));\n    }\n  }\n\n  if (extendedZone) {\n    c += \"[\" + o.zone.ianaName + \"]\";\n  }\n  return c;\n}\n\n// defaults for unspecified units in the supported calendars\nconst defaultUnitValues = {\n    month: 1,\n    day: 1,\n    hour: 0,\n    minute: 0,\n    second: 0,\n    millisecond: 0,\n  },\n  defaultWeekUnitValues = {\n    weekNumber: 1,\n    weekday: 1,\n    hour: 0,\n    minute: 0,\n    second: 0,\n    millisecond: 0,\n  },\n  defaultOrdinalUnitValues = {\n    ordinal: 1,\n    hour: 0,\n    minute: 0,\n    second: 0,\n    millisecond: 0,\n  };\n\n// Units in the supported calendars, sorted by bigness\nconst orderedUnits = [\"year\", \"month\", \"day\", \"hour\", \"minute\", \"second\", \"millisecond\"],\n  orderedWeekUnits = [\n    \"weekYear\",\n    \"weekNumber\",\n    \"weekday\",\n    \"hour\",\n    \"minute\",\n    \"second\",\n    \"millisecond\",\n  ],\n  orderedOrdinalUnits = [\"year\", \"ordinal\", \"hour\", \"minute\", \"second\", \"millisecond\"];\n\n// standardize case and plurality in units\nfunction normalizeUnit(unit) {\n  const normalized = {\n    year: \"year\",\n    years: \"year\",\n    month: \"month\",\n    months: \"month\",\n    day: \"day\",\n    days: \"day\",\n    hour: \"hour\",\n    hours: \"hour\",\n    minute: \"minute\",\n    minutes: \"minute\",\n    quarter: \"quarter\",\n    quarters: \"quarter\",\n    second: \"second\",\n    seconds: \"second\",\n    millisecond: \"millisecond\",\n    milliseconds: \"millisecond\",\n    weekday: \"weekday\",\n    weekdays: \"weekday\",\n    weeknumber: \"weekNumber\",\n    weeksnumber: \"weekNumber\",\n    weeknumbers: \"weekNumber\",\n    weekyear: \"weekYear\",\n    weekyears: \"weekYear\",\n    ordinal: \"ordinal\",\n  }[unit.toLowerCase()];\n\n  if (!normalized) throw new InvalidUnitError(unit);\n\n  return normalized;\n}\n\nfunction normalizeUnitWithLocalWeeks(unit) {\n  switch (unit.toLowerCase()) {\n    case \"localweekday\":\n    case \"localweekdays\":\n      return \"localWeekday\";\n    case \"localweeknumber\":\n    case \"localweeknumbers\":\n      return \"localWeekNumber\";\n    case \"localweekyear\":\n    case \"localweekyears\":\n      return \"localWeekYear\";\n    default:\n      return normalizeUnit(unit);\n  }\n}\n\n// cache offsets for zones based on the current timestamp when this function is\n// first called. When we are handling a datetime from components like (year,\n// month, day, hour) in a time zone, we need a guess about what the timezone\n// offset is so that we can convert into a UTC timestamp. One way is to find the\n// offset of now in the zone. The actual date may have a different offset (for\n// example, if we handle a date in June while we're in December in a zone that\n// observes DST), but we can check and adjust that.\n//\n// When handling many dates, calculating the offset for now every time is\n// expensive. It's just a guess, so we can cache the offset to use even if we\n// are right on a time change boundary (we'll just correct in the other\n// direction). Using a timestamp from first read is a slight optimization for\n// handling dates close to the current date, since those dates will usually be\n// in the same offset (we could set the timestamp statically, instead). We use a\n// single timestamp for all zones to make things a bit more predictable.\n//\n// This is safe for quickDT (used by local() and utc()) because we don't fill in\n// higher-order units from tsNow (as we do in fromObject, this requires that\n// offset is calculated from tsNow).\n/**\n * @param {Zone} zone\n * @return {number}\n */\nfunction guessOffsetForZone(zone) {\n  if (zoneOffsetTs === undefined) {\n    zoneOffsetTs = Settings.now();\n  }\n\n  // Do not cache anything but IANA zones, because it is not safe to do so.\n  // Guessing an offset which is not present in the zone can cause wrong results from fixOffset\n  if (zone.type !== \"iana\") {\n    return zone.offset(zoneOffsetTs);\n  }\n  const zoneName = zone.name;\n  let offsetGuess = zoneOffsetGuessCache.get(zoneName);\n  if (offsetGuess === undefined) {\n    offsetGuess = zone.offset(zoneOffsetTs);\n    zoneOffsetGuessCache.set(zoneName, offsetGuess);\n  }\n  return offsetGuess;\n}\n\n// this is a dumbed down version of fromObject() that runs about 60% faster\n// but doesn't do any validation, makes a bunch of assumptions about what units\n// are present, and so on.\nfunction quickDT(obj, opts) {\n  const zone = normalizeZone(opts.zone, Settings.defaultZone);\n  if (!zone.isValid) {\n    return DateTime.invalid(unsupportedZone(zone));\n  }\n\n  const loc = Locale.fromObject(opts);\n\n  let ts, o;\n\n  // assume we have the higher-order units\n  if (!isUndefined(obj.year)) {\n    for (const u of orderedUnits) {\n      if (isUndefined(obj[u])) {\n        obj[u] = defaultUnitValues[u];\n      }\n    }\n\n    const invalid = hasInvalidGregorianData(obj) || hasInvalidTimeData(obj);\n    if (invalid) {\n      return DateTime.invalid(invalid);\n    }\n\n    const offsetProvis = guessOffsetForZone(zone);\n    [ts, o] = objToTS(obj, offsetProvis, zone);\n  } else {\n    ts = Settings.now();\n  }\n\n  return new DateTime({ ts, zone, loc, o });\n}\n\nfunction diffRelative(start, end, opts) {\n  const round = isUndefined(opts.round) ? true : opts.round,\n    format = (c, unit) => {\n      c = roundTo(c, round || opts.calendary ? 0 : 2, true);\n      const formatter = end.loc.clone(opts).relFormatter(opts);\n      return formatter.format(c, unit);\n    },\n    differ = (unit) => {\n      if (opts.calendary) {\n        if (!end.hasSame(start, unit)) {\n          return end.startOf(unit).diff(start.startOf(unit), unit).get(unit);\n        } else return 0;\n      } else {\n        return end.diff(start, unit).get(unit);\n      }\n    };\n\n  if (opts.unit) {\n    return format(differ(opts.unit), opts.unit);\n  }\n\n  for (const unit of opts.units) {\n    const count = differ(unit);\n    if (Math.abs(count) >= 1) {\n      return format(count, unit);\n    }\n  }\n  return format(start > end ? -0 : 0, opts.units[opts.units.length - 1]);\n}\n\nfunction lastOpts(argList) {\n  let opts = {},\n    args;\n  if (argList.length > 0 && typeof argList[argList.length - 1] === \"object\") {\n    opts = argList[argList.length - 1];\n    args = Array.from(argList).slice(0, argList.length - 1);\n  } else {\n    args = Array.from(argList);\n  }\n  return [opts, args];\n}\n\n/**\n * Timestamp to use for cached zone offset guesses (exposed for test)\n */\nlet zoneOffsetTs;\n/**\n * Cache for zone offset guesses (exposed for test).\n *\n * This optimizes quickDT via guessOffsetForZone to avoid repeated calls of\n * zone.offset().\n */\nconst zoneOffsetGuessCache = new Map();\n\n/**\n * A DateTime is an immutable data structure representing a specific date and time and accompanying methods. It contains class and instance methods for creating, parsing, interrogating, transforming, and formatting them.\n *\n * A DateTime comprises of:\n * * A timestamp. Each DateTime instance refers to a specific millisecond of the Unix epoch.\n * * A time zone. Each instance is considered in the context of a specific zone (by default the local system's zone).\n * * Configuration properties that effect how output strings are formatted, such as `locale`, `numberingSystem`, and `outputCalendar`.\n *\n * Here is a brief overview of the most commonly used functionality it provides:\n *\n * * **Creation**: To create a DateTime from its components, use one of its factory class methods: {@link DateTime.local}, {@link DateTime.utc}, and (most flexibly) {@link DateTime.fromObject}. To create one from a standard string format, use {@link DateTime.fromISO}, {@link DateTime.fromHTTP}, and {@link DateTime.fromRFC2822}. To create one from a custom string format, use {@link DateTime.fromFormat}. To create one from a native JS date, use {@link DateTime.fromJSDate}.\n * * **Gregorian calendar and time**: To examine the Gregorian properties of a DateTime individually (i.e as opposed to collectively through {@link DateTime#toObject}), use the {@link DateTime#year}, {@link DateTime#month},\n * {@link DateTime#day}, {@link DateTime#hour}, {@link DateTime#minute}, {@link DateTime#second}, {@link DateTime#millisecond} accessors.\n * * **Week calendar**: For ISO week calendar attributes, see the {@link DateTime#weekYear}, {@link DateTime#weekNumber}, and {@link DateTime#weekday} accessors.\n * * **Configuration** See the {@link DateTime#locale} and {@link DateTime#numberingSystem} accessors.\n * * **Transformation**: To transform the DateTime into other DateTimes, use {@link DateTime#set}, {@link DateTime#reconfigure}, {@link DateTime#setZone}, {@link DateTime#setLocale}, {@link DateTime.plus}, {@link DateTime#minus}, {@link DateTime#endOf}, {@link DateTime#startOf}, {@link DateTime#toUTC}, and {@link DateTime#toLocal}.\n * * **Output**: To convert the DateTime to other representations, use the {@link DateTime#toRelative}, {@link DateTime#toRelativeCalendar}, {@link DateTime#toJSON}, {@link DateTime#toISO}, {@link DateTime#toHTTP}, {@link DateTime#toObject}, {@link DateTime#toRFC2822}, {@link DateTime#toString}, {@link DateTime#toLocaleString}, {@link DateTime#toFormat}, {@link DateTime#toMillis} and {@link DateTime#toJSDate}.\n *\n * There's plenty others documented below. In addition, for more information on subtler topics like internationalization, time zones, alternative calendars, validity, and so on, see the external documentation.\n */\nexport default class DateTime {\n  /**\n   * @access private\n   */\n  constructor(config) {\n    const zone = config.zone || Settings.defaultZone;\n\n    let invalid =\n      config.invalid ||\n      (Number.isNaN(config.ts) ? new Invalid(\"invalid input\") : null) ||\n      (!zone.isValid ? unsupportedZone(zone) : null);\n    /**\n     * @access private\n     */\n    this.ts = isUndefined(config.ts) ? Settings.now() : config.ts;\n\n    let c = null,\n      o = null;\n    if (!invalid) {\n      const unchanged = config.old && config.old.ts === this.ts && config.old.zone.equals(zone);\n\n      if (unchanged) {\n        [c, o] = [config.old.c, config.old.o];\n      } else {\n        // If an offset has been passed and we have not been called from\n        // clone(), we can trust it and avoid the offset calculation.\n        const ot = isNumber(config.o) && !config.old ? config.o : zone.offset(this.ts);\n        c = tsToObj(this.ts, ot);\n        invalid = Number.isNaN(c.year) ? new Invalid(\"invalid input\") : null;\n        c = invalid ? null : c;\n        o = invalid ? null : ot;\n      }\n    }\n\n    /**\n     * @access private\n     */\n    this._zone = zone;\n    /**\n     * @access private\n     */\n    this.loc = config.loc || Locale.create();\n    /**\n     * @access private\n     */\n    this.invalid = invalid;\n    /**\n     * @access private\n     */\n    this.weekData = null;\n    /**\n     * @access private\n     */\n    this.localWeekData = null;\n    /**\n     * @access private\n     */\n    this.c = c;\n    /**\n     * @access private\n     */\n    this.o = o;\n    /**\n     * @access private\n     */\n    this.isLuxonDateTime = true;\n  }\n\n  // CONSTRUCT\n\n  /**\n   * Create a DateTime for the current instant, in the system's time zone.\n   *\n   * Use Settings to override these default values if needed.\n   * @example DateTime.now().toISO() //~> now in the ISO format\n   * @return {DateTime}\n   */\n  static now() {\n    return new DateTime({});\n  }\n\n  /**\n   * Create a local DateTime\n   * @param {number} [year] - The calendar year. If omitted (as in, call `local()` with no arguments), the current time will be used\n   * @param {number} [month=1] - The month, 1-indexed\n   * @param {number} [day=1] - The day of the month, 1-indexed\n   * @param {number} [hour=0] - The hour of the day, in 24-hour time\n   * @param {number} [minute=0] - The minute of the hour, meaning a number between 0 and 59\n   * @param {number} [second=0] - The second of the minute, meaning a number between 0 and 59\n   * @param {number} [millisecond=0] - The millisecond of the second, meaning a number between 0 and 999\n   * @example DateTime.local()                                  //~> now\n   * @example DateTime.local({ zone: \"America/New_York\" })      //~> now, in US east coast time\n   * @example DateTime.local(2017)                              //~> 2017-01-01T00:00:00\n   * @example DateTime.local(2017, 3)                           //~> 2017-03-01T00:00:00\n   * @example DateTime.local(2017, 3, 12, { locale: \"fr\" })     //~> 2017-03-12T00:00:00, with a French locale\n   * @example DateTime.local(2017, 3, 12, 5)                    //~> 2017-03-12T05:00:00\n   * @example DateTime.local(2017, 3, 12, 5, { zone: \"utc\" })   //~> 2017-03-12T05:00:00, in UTC\n   * @example DateTime.local(2017, 3, 12, 5, 45)                //~> 2017-03-12T05:45:00\n   * @example DateTime.local(2017, 3, 12, 5, 45, 10)            //~> 2017-03-12T05:45:10\n   * @example DateTime.local(2017, 3, 12, 5, 45, 10, 765)       //~> 2017-03-12T05:45:10.765\n   * @return {DateTime}\n   */\n  static local() {\n    const [opts, args] = lastOpts(arguments),\n      [year, month, day, hour, minute, second, millisecond] = args;\n    return quickDT({ year, month, day, hour, minute, second, millisecond }, opts);\n  }\n\n  /**\n   * Create a DateTime in UTC\n   * @param {number} [year] - The calendar year. If omitted (as in, call `utc()` with no arguments), the current time will be used\n   * @param {number} [month=1] - The month, 1-indexed\n   * @param {number} [day=1] - The day of the month\n   * @param {number} [hour=0] - The hour of the day, in 24-hour time\n   * @param {number} [minute=0] - The minute of the hour, meaning a number between 0 and 59\n   * @param {number} [second=0] - The second of the minute, meaning a number between 0 and 59\n   * @param {number} [millisecond=0] - The millisecond of the second, meaning a number between 0 and 999\n   * @param {Object} options - configuration options for the DateTime\n   * @param {string} [options.locale] - a locale to set on the resulting DateTime instance\n   * @param {string} [options.outputCalendar] - the output calendar to set on the resulting DateTime instance\n   * @param {string} [options.numberingSystem] - the numbering system to set on the resulting DateTime instance\n   * @param {string} [options.weekSettings] - the week settings to set on the resulting DateTime instance\n   * @example DateTime.utc()                                              //~> now\n   * @example DateTime.utc(2017)                                          //~> 2017-01-01T00:00:00Z\n   * @example DateTime.utc(2017, 3)                                       //~> 2017-03-01T00:00:00Z\n   * @example DateTime.utc(2017, 3, 12)                                   //~> 2017-03-12T00:00:00Z\n   * @example DateTime.utc(2017, 3, 12, 5)                                //~> 2017-03-12T05:00:00Z\n   * @example DateTime.utc(2017, 3, 12, 5, 45)                            //~> 2017-03-12T05:45:00Z\n   * @example DateTime.utc(2017, 3, 12, 5, 45, { locale: \"fr\" })          //~> 2017-03-12T05:45:00Z with a French locale\n   * @example DateTime.utc(2017, 3, 12, 5, 45, 10)                        //~> 2017-03-12T05:45:10Z\n   * @example DateTime.utc(2017, 3, 12, 5, 45, 10, 765, { locale: \"fr\" }) //~> 2017-03-12T05:45:10.765Z with a French locale\n   * @return {DateTime}\n   */\n  static utc() {\n    const [opts, args] = lastOpts(arguments),\n      [year, month, day, hour, minute, second, millisecond] = args;\n\n    opts.zone = FixedOffsetZone.utcInstance;\n    return quickDT({ year, month, day, hour, minute, second, millisecond }, opts);\n  }\n\n  /**\n   * Create a DateTime from a JavaScript Date object. Uses the default zone.\n   * @param {Date} date - a JavaScript Date object\n   * @param {Object} options - configuration options for the DateTime\n   * @param {string|Zone} [options.zone='local'] - the zone to place the DateTime into\n   * @return {DateTime}\n   */\n  static fromJSDate(date, options = {}) {\n    const ts = isDate(date) ? date.valueOf() : NaN;\n    if (Number.isNaN(ts)) {\n      return DateTime.invalid(\"invalid input\");\n    }\n\n    const zoneToUse = normalizeZone(options.zone, Settings.defaultZone);\n    if (!zoneToUse.isValid) {\n      return DateTime.invalid(unsupportedZone(zoneToUse));\n    }\n\n    return new DateTime({\n      ts: ts,\n      zone: zoneToUse,\n      loc: Locale.fromObject(options),\n    });\n  }\n\n  /**\n   * Create a DateTime from a number of milliseconds since the epoch (meaning since 1 January 1970 00:00:00 UTC). Uses the default zone.\n   * @param {number} milliseconds - a number of milliseconds since 1970 UTC\n   * @param {Object} options - configuration options for the DateTime\n   * @param {string|Zone} [options.zone='local'] - the zone to place the DateTime into\n   * @param {string} [options.locale] - a locale to set on the resulting DateTime instance\n   * @param {string} options.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} options.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} options.weekSettings - the week settings to set on the resulting DateTime instance\n   * @return {DateTime}\n   */\n  static fromMillis(milliseconds, options = {}) {\n    if (!isNumber(milliseconds)) {\n      throw new InvalidArgumentError(\n        `fromMillis requires a numerical input, but received a ${typeof milliseconds} with value ${milliseconds}`\n      );\n    } else if (milliseconds < -MAX_DATE || milliseconds > MAX_DATE) {\n      // this isn't perfect because we can still end up out of range because of additional shifting, but it's a start\n      return DateTime.invalid(\"Timestamp out of range\");\n    } else {\n      return new DateTime({\n        ts: milliseconds,\n        zone: normalizeZone(options.zone, Settings.defaultZone),\n        loc: Locale.fromObject(options),\n      });\n    }\n  }\n\n  /**\n   * Create a DateTime from a number of seconds since the epoch (meaning since 1 January 1970 00:00:00 UTC). Uses the default zone.\n   * @param {number} seconds - a number of seconds since 1970 UTC\n   * @param {Object} options - configuration options for the DateTime\n   * @param {string|Zone} [options.zone='local'] - the zone to place the DateTime into\n   * @param {string} [options.locale] - a locale to set on the resulting DateTime instance\n   * @param {string} options.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} options.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} options.weekSettings - the week settings to set on the resulting DateTime instance\n   * @return {DateTime}\n   */\n  static fromSeconds(seconds, options = {}) {\n    if (!isNumber(seconds)) {\n      throw new InvalidArgumentError(\"fromSeconds requires a numerical input\");\n    } else {\n      return new DateTime({\n        ts: seconds * 1000,\n        zone: normalizeZone(options.zone, Settings.defaultZone),\n        loc: Locale.fromObject(options),\n      });\n    }\n  }\n\n  /**\n   * Create a DateTime from a JavaScript object with keys like 'year' and 'hour' with reasonable defaults.\n   * @param {Object} obj - the object to create the DateTime from\n   * @param {number} obj.year - a year, such as 1987\n   * @param {number} obj.month - a month, 1-12\n   * @param {number} obj.day - a day of the month, 1-31, depending on the month\n   * @param {number} obj.ordinal - day of the year, 1-365 or 366\n   * @param {number} obj.weekYear - an ISO week year\n   * @param {number} obj.weekNumber - an ISO week number, between 1 and 52 or 53, depending on the year\n   * @param {number} obj.weekday - an ISO weekday, 1-7, where 1 is Monday and 7 is Sunday\n   * @param {number} obj.localWeekYear - a week year, according to the locale\n   * @param {number} obj.localWeekNumber - a week number, between 1 and 52 or 53, depending on the year, according to the locale\n   * @param {number} obj.localWeekday - a weekday, 1-7, where 1 is the first and 7 is the last day of the week, according to the locale\n   * @param {number} obj.hour - hour of the day, 0-23\n   * @param {number} obj.minute - minute of the hour, 0-59\n   * @param {number} obj.second - second of the minute, 0-59\n   * @param {number} obj.millisecond - millisecond of the second, 0-999\n   * @param {Object} opts - options for creating this DateTime\n   * @param {string|Zone} [opts.zone='local'] - interpret the numbers in the context of a particular zone. Can take any value taken as the first argument to setZone()\n   * @param {string} [opts.locale='system\\'s locale'] - a locale to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} opts.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @example DateTime.fromObject({ year: 1982, month: 5, day: 25}).toISODate() //=> '1982-05-25'\n   * @example DateTime.fromObject({ year: 1982 }).toISODate() //=> '1982-01-01'\n   * @example DateTime.fromObject({ hour: 10, minute: 26, second: 6 }) //~> today at 10:26:06\n   * @example DateTime.fromObject({ hour: 10, minute: 26, second: 6 }, { zone: 'utc' }),\n   * @example DateTime.fromObject({ hour: 10, minute: 26, second: 6 }, { zone: 'local' })\n   * @example DateTime.fromObject({ hour: 10, minute: 26, second: 6 }, { zone: 'America/New_York' })\n   * @example DateTime.fromObject({ weekYear: 2016, weekNumber: 2, weekday: 3 }).toISODate() //=> '2016-01-13'\n   * @example DateTime.fromObject({ localWeekYear: 2022, localWeekNumber: 1, localWeekday: 1 }, { locale: \"en-US\" }).toISODate() //=> '2021-12-26'\n   * @return {DateTime}\n   */\n  static fromObject(obj, opts = {}) {\n    obj = obj || {};\n    const zoneToUse = normalizeZone(opts.zone, Settings.defaultZone);\n    if (!zoneToUse.isValid) {\n      return DateTime.invalid(unsupportedZone(zoneToUse));\n    }\n\n    const loc = Locale.fromObject(opts);\n    const normalized = normalizeObject(obj, normalizeUnitWithLocalWeeks);\n    const { minDaysInFirstWeek, startOfWeek } = usesLocalWeekValues(normalized, loc);\n\n    const tsNow = Settings.now(),\n      offsetProvis = !isUndefined(opts.specificOffset)\n        ? opts.specificOffset\n        : zoneToUse.offset(tsNow),\n      containsOrdinal = !isUndefined(normalized.ordinal),\n      containsGregorYear = !isUndefined(normalized.year),\n      containsGregorMD = !isUndefined(normalized.month) || !isUndefined(normalized.day),\n      containsGregor = containsGregorYear || containsGregorMD,\n      definiteWeekDef = normalized.weekYear || normalized.weekNumber;\n\n    // cases:\n    // just a weekday -> this week's instance of that weekday, no worries\n    // (gregorian data or ordinal) + (weekYear or weekNumber) -> error\n    // (gregorian month or day) + ordinal -> error\n    // otherwise just use weeks or ordinals or gregorian, depending on what's specified\n\n    if ((containsGregor || containsOrdinal) && definiteWeekDef) {\n      throw new ConflictingSpecificationError(\n        \"Can't mix weekYear/weekNumber units with year/month/day or ordinals\"\n      );\n    }\n\n    if (containsGregorMD && containsOrdinal) {\n      throw new ConflictingSpecificationError(\"Can't mix ordinal dates with month/day\");\n    }\n\n    const useWeekData = definiteWeekDef || (normalized.weekday && !containsGregor);\n\n    // configure ourselves to deal with gregorian dates or week stuff\n    let units,\n      defaultValues,\n      objNow = tsToObj(tsNow, offsetProvis);\n    if (useWeekData) {\n      units = orderedWeekUnits;\n      defaultValues = defaultWeekUnitValues;\n      objNow = gregorianToWeek(objNow, minDaysInFirstWeek, startOfWeek);\n    } else if (containsOrdinal) {\n      units = orderedOrdinalUnits;\n      defaultValues = defaultOrdinalUnitValues;\n      objNow = gregorianToOrdinal(objNow);\n    } else {\n      units = orderedUnits;\n      defaultValues = defaultUnitValues;\n    }\n\n    // set default values for missing stuff\n    let foundFirst = false;\n    for (const u of units) {\n      const v = normalized[u];\n      if (!isUndefined(v)) {\n        foundFirst = true;\n      } else if (foundFirst) {\n        normalized[u] = defaultValues[u];\n      } else {\n        normalized[u] = objNow[u];\n      }\n    }\n\n    // make sure the values we have are in range\n    const higherOrderInvalid = useWeekData\n        ? hasInvalidWeekData(normalized, minDaysInFirstWeek, startOfWeek)\n        : containsOrdinal\n        ? hasInvalidOrdinalData(normalized)\n        : hasInvalidGregorianData(normalized),\n      invalid = higherOrderInvalid || hasInvalidTimeData(normalized);\n\n    if (invalid) {\n      return DateTime.invalid(invalid);\n    }\n\n    // compute the actual time\n    const gregorian = useWeekData\n        ? weekToGregorian(normalized, minDaysInFirstWeek, startOfWeek)\n        : containsOrdinal\n        ? ordinalToGregorian(normalized)\n        : normalized,\n      [tsFinal, offsetFinal] = objToTS(gregorian, offsetProvis, zoneToUse),\n      inst = new DateTime({\n        ts: tsFinal,\n        zone: zoneToUse,\n        o: offsetFinal,\n        loc,\n      });\n\n    // gregorian data + weekday serves only to validate\n    if (normalized.weekday && containsGregor && obj.weekday !== inst.weekday) {\n      return DateTime.invalid(\n        \"mismatched weekday\",\n        `you can't specify both a weekday of ${normalized.weekday} and a date of ${inst.toISO()}`\n      );\n    }\n\n    if (!inst.isValid) {\n      return DateTime.invalid(inst.invalid);\n    }\n\n    return inst;\n  }\n\n  /**\n   * Create a DateTime from an ISO 8601 string\n   * @param {string} text - the ISO string\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - use this zone if no offset is specified in the input string itself. Will also convert the time to this zone\n   * @param {boolean} [opts.setZone=false] - override the zone with a fixed-offset zone specified in the string itself, if it specifies one\n   * @param {string} [opts.locale='system's locale'] - a locale to set on the resulting DateTime instance\n   * @param {string} [opts.outputCalendar] - the output calendar to set on the resulting DateTime instance\n   * @param {string} [opts.numberingSystem] - the numbering system to set on the resulting DateTime instance\n   * @param {string} [opts.weekSettings] - the week settings to set on the resulting DateTime instance\n   * @example DateTime.fromISO('2016-05-25T09:08:34.123')\n   * @example DateTime.fromISO('2016-05-25T09:08:34.123+06:00')\n   * @example DateTime.fromISO('2016-05-25T09:08:34.123+06:00', {setZone: true})\n   * @example DateTime.fromISO('2016-05-25T09:08:34.123', {zone: 'utc'})\n   * @example DateTime.fromISO('2016-W05-4')\n   * @return {DateTime}\n   */\n  static fromISO(text, opts = {}) {\n    const [vals, parsedZone] = parseISODate(text);\n    return parseDataToDateTime(vals, parsedZone, opts, \"ISO 8601\", text);\n  }\n\n  /**\n   * Create a DateTime from an RFC 2822 string\n   * @param {string} text - the RFC 2822 string\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - convert the time to this zone. Since the offset is always specified in the string itself, this has no effect on the interpretation of string, merely the zone the resulting DateTime is expressed in.\n   * @param {boolean} [opts.setZone=false] - override the zone with a fixed-offset zone specified in the string itself, if it specifies one\n   * @param {string} [opts.locale='system's locale'] - a locale to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} opts.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @example DateTime.fromRFC2822('25 Nov 2016 13:23:12 GMT')\n   * @example DateTime.fromRFC2822('Fri, 25 Nov 2016 13:23:12 +0600')\n   * @example DateTime.fromRFC2822('25 Nov 2016 13:23 Z')\n   * @return {DateTime}\n   */\n  static fromRFC2822(text, opts = {}) {\n    const [vals, parsedZone] = parseRFC2822Date(text);\n    return parseDataToDateTime(vals, parsedZone, opts, \"RFC 2822\", text);\n  }\n\n  /**\n   * Create a DateTime from an HTTP header date\n   * @see https://www.w3.org/Protocols/rfc2616/rfc2616-sec3.html#sec3.3.1\n   * @param {string} text - the HTTP header date\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - convert the time to this zone. Since HTTP dates are always in UTC, this has no effect on the interpretation of string, merely the zone the resulting DateTime is expressed in.\n   * @param {boolean} [opts.setZone=false] - override the zone with the fixed-offset zone specified in the string. For HTTP dates, this is always UTC, so this option is equivalent to setting the `zone` option to 'utc', but this option is included for consistency with similar methods.\n   * @param {string} [opts.locale='system's locale'] - a locale to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} opts.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @example DateTime.fromHTTP('Sun, 06 Nov 1994 08:49:37 GMT')\n   * @example DateTime.fromHTTP('Sunday, 06-Nov-94 08:49:37 GMT')\n   * @example DateTime.fromHTTP('Sun Nov  6 08:49:37 1994')\n   * @return {DateTime}\n   */\n  static fromHTTP(text, opts = {}) {\n    const [vals, parsedZone] = parseHTTPDate(text);\n    return parseDataToDateTime(vals, parsedZone, opts, \"HTTP\", opts);\n  }\n\n  /**\n   * Create a DateTime from an input string and format string.\n   * Defaults to en-US if no locale has been specified, regardless of the system's locale. For a table of tokens and their interpretations, see [here](https://moment.github.io/luxon/#/parsing?id=table-of-tokens).\n   * @param {string} text - the string to parse\n   * @param {string} fmt - the format the string is expected to be in (see the link below for the formats)\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - use this zone if no offset is specified in the input string itself. Will also convert the DateTime to this zone\n   * @param {boolean} [opts.setZone=false] - override the zone with a zone specified in the string itself, if it specifies one\n   * @param {string} [opts.locale='en-US'] - a locale string to use when parsing. Will also set the DateTime to this locale\n   * @param {string} opts.numberingSystem - the numbering system to use when parsing. Will also set the resulting DateTime to this numbering system\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @return {DateTime}\n   */\n  static fromFormat(text, fmt, opts = {}) {\n    if (isUndefined(text) || isUndefined(fmt)) {\n      throw new InvalidArgumentError(\"fromFormat requires an input string and a format\");\n    }\n\n    const { locale = null, numberingSystem = null } = opts,\n      localeToUse = Locale.fromOpts({\n        locale,\n        numberingSystem,\n        defaultToEN: true,\n      }),\n      [vals, parsedZone, specificOffset, invalid] = parseFromTokens(localeToUse, text, fmt);\n    if (invalid) {\n      return DateTime.invalid(invalid);\n    } else {\n      return parseDataToDateTime(vals, parsedZone, opts, `format ${fmt}`, text, specificOffset);\n    }\n  }\n\n  /**\n   * @deprecated use fromFormat instead\n   */\n  static fromString(text, fmt, opts = {}) {\n    return DateTime.fromFormat(text, fmt, opts);\n  }\n\n  /**\n   * Create a DateTime from a SQL date, time, or datetime\n   * Defaults to en-US if no locale has been specified, regardless of the system's locale\n   * @param {string} text - the string to parse\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - use this zone if no offset is specified in the input string itself. Will also convert the DateTime to this zone\n   * @param {boolean} [opts.setZone=false] - override the zone with a zone specified in the string itself, if it specifies one\n   * @param {string} [opts.locale='en-US'] - a locale string to use when parsing. Will also set the DateTime to this locale\n   * @param {string} opts.numberingSystem - the numbering system to use when parsing. Will also set the resulting DateTime to this numbering system\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @example DateTime.fromSQL('2017-05-15')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342+06:00')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342 America/Los_Angeles')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342 America/Los_Angeles', { setZone: true })\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342', { zone: 'America/Los_Angeles' })\n   * @example DateTime.fromSQL('09:12:34.342')\n   * @return {DateTime}\n   */\n  static fromSQL(text, opts = {}) {\n    const [vals, parsedZone] = parseSQL(text);\n    return parseDataToDateTime(vals, parsedZone, opts, \"SQL\", text);\n  }\n\n  /**\n   * Create an invalid DateTime.\n   * @param {string} reason - simple string of why this DateTime is invalid. Should not contain parameters or anything else data-dependent.\n   * @param {string} [explanation=null] - longer explanation, may include parameters and other useful debugging information\n   * @return {DateTime}\n   */\n  static invalid(reason, explanation = null) {\n    if (!reason) {\n      throw new InvalidArgumentError(\"need to specify a reason the DateTime is invalid\");\n    }\n\n    const invalid = reason instanceof Invalid ? reason : new Invalid(reason, explanation);\n\n    if (Settings.throwOnInvalid) {\n      throw new InvalidDateTimeError(invalid);\n    } else {\n      return new DateTime({ invalid });\n    }\n  }\n\n  /**\n   * Check if an object is an instance of DateTime. Works across context boundaries\n   * @param {object} o\n   * @return {boolean}\n   */\n  static isDateTime(o) {\n    return (o && o.isLuxonDateTime) || false;\n  }\n\n  /**\n   * Produce the format string for a set of options\n   * @param formatOpts\n   * @param localeOpts\n   * @returns {string}\n   */\n  static parseFormatForOpts(formatOpts, localeOpts = {}) {\n    const tokenList = formatOptsToTokens(formatOpts, Locale.fromObject(localeOpts));\n    return !tokenList ? null : tokenList.map((t) => (t ? t.val : null)).join(\"\");\n  }\n\n  /**\n   * Produce the the fully expanded format token for the locale\n   * Does NOT quote characters, so quoted tokens will not round trip correctly\n   * @param fmt\n   * @param localeOpts\n   * @returns {string}\n   */\n  static expandFormat(fmt, localeOpts = {}) {\n    const expanded = expandMacroTokens(Formatter.parseFormat(fmt), Locale.fromObject(localeOpts));\n    return expanded.map((t) => t.val).join(\"\");\n  }\n\n  static resetCache() {\n    zoneOffsetTs = undefined;\n    zoneOffsetGuessCache.clear();\n  }\n\n  // INFO\n\n  /**\n   * Get the value of unit.\n   * @param {string} unit - a unit such as 'minute' or 'day'\n   * @example DateTime.local(2017, 7, 4).get('month'); //=> 7\n   * @example DateTime.local(2017, 7, 4).get('day'); //=> 4\n   * @return {number}\n   */\n  get(unit) {\n    return this[unit];\n  }\n\n  /**\n   * Returns whether the DateTime is valid. Invalid DateTimes occur when:\n   * * The DateTime was created from invalid calendar information, such as the 13th month or February 30\n   * * The DateTime was created by an operation on another invalid date\n   * @type {boolean}\n   */\n  get isValid() {\n    return this.invalid === null;\n  }\n\n  /**\n   * Returns an error code if this DateTime is invalid, or null if the DateTime is valid\n   * @type {string}\n   */\n  get invalidReason() {\n    return this.invalid ? this.invalid.reason : null;\n  }\n\n  /**\n   * Returns an explanation of why this DateTime became invalid, or null if the DateTime is valid\n   * @type {string}\n   */\n  get invalidExplanation() {\n    return this.invalid ? this.invalid.explanation : null;\n  }\n\n  /**\n   * Get the locale of a DateTime, such 'en-GB'. The locale is used when formatting the DateTime\n   *\n   * @type {string}\n   */\n  get locale() {\n    return this.isValid ? this.loc.locale : null;\n  }\n\n  /**\n   * Get the numbering system of a DateTime, such 'beng'. The numbering system is used when formatting the DateTime\n   *\n   * @type {string}\n   */\n  get numberingSystem() {\n    return this.isValid ? this.loc.numberingSystem : null;\n  }\n\n  /**\n   * Get the output calendar of a DateTime, such 'islamic'. The output calendar is used when formatting the DateTime\n   *\n   * @type {string}\n   */\n  get outputCalendar() {\n    return this.isValid ? this.loc.outputCalendar : null;\n  }\n\n  /**\n   * Get the time zone associated with this DateTime.\n   * @type {Zone}\n   */\n  get zone() {\n    return this._zone;\n  }\n\n  /**\n   * Get the name of the time zone.\n   * @type {string}\n   */\n  get zoneName() {\n    return this.isValid ? this.zone.name : null;\n  }\n\n  /**\n   * Get the year\n   * @example DateTime.local(2017, 5, 25).year //=> 2017\n   * @type {number}\n   */\n  get year() {\n    return this.isValid ? this.c.year : NaN;\n  }\n\n  /**\n   * Get the quarter\n   * @example DateTime.local(2017, 5, 25).quarter //=> 2\n   * @type {number}\n   */\n  get quarter() {\n    return this.isValid ? Math.ceil(this.c.month / 3) : NaN;\n  }\n\n  /**\n   * Get the month (1-12).\n   * @example DateTime.local(2017, 5, 25).month //=> 5\n   * @type {number}\n   */\n  get month() {\n    return this.isValid ? this.c.month : NaN;\n  }\n\n  /**\n   * Get the day of the month (1-30ish).\n   * @example DateTime.local(2017, 5, 25).day //=> 25\n   * @type {number}\n   */\n  get day() {\n    return this.isValid ? this.c.day : NaN;\n  }\n\n  /**\n   * Get the hour of the day (0-23).\n   * @example DateTime.local(2017, 5, 25, 9).hour //=> 9\n   * @type {number}\n   */\n  get hour() {\n    return this.isValid ? this.c.hour : NaN;\n  }\n\n  /**\n   * Get the minute of the hour (0-59).\n   * @example DateTime.local(2017, 5, 25, 9, 30).minute //=> 30\n   * @type {number}\n   */\n  get minute() {\n    return this.isValid ? this.c.minute : NaN;\n  }\n\n  /**\n   * Get the second of the minute (0-59).\n   * @example DateTime.local(2017, 5, 25, 9, 30, 52).second //=> 52\n   * @type {number}\n   */\n  get second() {\n    return this.isValid ? this.c.second : NaN;\n  }\n\n  /**\n   * Get the millisecond of the second (0-999).\n   * @example DateTime.local(2017, 5, 25, 9, 30, 52, 654).millisecond //=> 654\n   * @type {number}\n   */\n  get millisecond() {\n    return this.isValid ? this.c.millisecond : NaN;\n  }\n\n  /**\n   * Get the week year\n   * @see https://en.wikipedia.org/wiki/ISO_week_date\n   * @example DateTime.local(2014, 12, 31).weekYear //=> 2015\n   * @type {number}\n   */\n  get weekYear() {\n    return this.isValid ? possiblyCachedWeekData(this).weekYear : NaN;\n  }\n\n  /**\n   * Get the week number of the week year (1-52ish).\n   * @see https://en.wikipedia.org/wiki/ISO_week_date\n   * @example DateTime.local(2017, 5, 25).weekNumber //=> 21\n   * @type {number}\n   */\n  get weekNumber() {\n    return this.isValid ? possiblyCachedWeekData(this).weekNumber : NaN;\n  }\n\n  /**\n   * Get the day of the week.\n   * 1 is Monday and 7 is Sunday\n   * @see https://en.wikipedia.org/wiki/ISO_week_date\n   * @example DateTime.local(2014, 11, 31).weekday //=> 4\n   * @type {number}\n   */\n  get weekday() {\n    return this.isValid ? possiblyCachedWeekData(this).weekday : NaN;\n  }\n\n  /**\n   * Returns true if this date is on a weekend according to the locale, false otherwise\n   * @returns {boolean}\n   */\n  get isWeekend() {\n    return this.isValid && this.loc.getWeekendDays().includes(this.weekday);\n  }\n\n  /**\n   * Get the day of the week according to the locale.\n   * 1 is the first day of the week and 7 is the last day of the week.\n   * If the locale assigns Sunday as the first day of the week, then a date which is a Sunday will return 1,\n   * @returns {number}\n   */\n  get localWeekday() {\n    return this.isValid ? possiblyCachedLocalWeekData(this).weekday : NaN;\n  }\n\n  /**\n   * Get the week number of the week year according to the locale. Different locales assign week numbers differently,\n   * because the week can start on different days of the week (see localWeekday) and because a different number of days\n   * is required for a week to count as the first week of a year.\n   * @returns {number}\n   */\n  get localWeekNumber() {\n    return this.isValid ? possiblyCachedLocalWeekData(this).weekNumber : NaN;\n  }\n\n  /**\n   * Get the week year according to the locale. Different locales assign week numbers (and therefor week years)\n   * differently, see localWeekNumber.\n   * @returns {number}\n   */\n  get localWeekYear() {\n    return this.isValid ? possiblyCachedLocalWeekData(this).weekYear : NaN;\n  }\n\n  /**\n   * Get the ordinal (meaning the day of the year)\n   * @example DateTime.local(2017, 5, 25).ordinal //=> 145\n   * @type {number|DateTime}\n   */\n  get ordinal() {\n    return this.isValid ? gregorianToOrdinal(this.c).ordinal : NaN;\n  }\n\n  /**\n   * Get the human readable short month name, such as 'Oct'.\n   * Defaults to the system's locale if no locale has been specified\n   * @example DateTime.local(2017, 10, 30).monthShort //=> Oct\n   * @type {string}\n   */\n  get monthShort() {\n    return this.isValid ? Info.months(\"short\", { locObj: this.loc })[this.month - 1] : null;\n  }\n\n  /**\n   * Get the human readable long month name, such as 'October'.\n   * Defaults to the system's locale if no locale has been specified\n   * @example DateTime.local(2017, 10, 30).monthLong //=> October\n   * @type {string}\n   */\n  get monthLong() {\n    return this.isValid ? Info.months(\"long\", { locObj: this.loc })[this.month - 1] : null;\n  }\n\n  /**\n   * Get the human readable short weekday, such as 'Mon'.\n   * Defaults to the system's locale if no locale has been specified\n   * @example DateTime.local(2017, 10, 30).weekdayShort //=> Mon\n   * @type {string}\n   */\n  get weekdayShort() {\n    return this.isValid ? Info.weekdays(\"short\", { locObj: this.loc })[this.weekday - 1] : null;\n  }\n\n  /**\n   * Get the human readable long weekday, such as 'Monday'.\n   * Defaults to the system's locale if no locale has been specified\n   * @example DateTime.local(2017, 10, 30).weekdayLong //=> Monday\n   * @type {string}\n   */\n  get weekdayLong() {\n    return this.isValid ? Info.weekdays(\"long\", { locObj: this.loc })[this.weekday - 1] : null;\n  }\n\n  /**\n   * Get the UTC offset of this DateTime in minutes\n   * @example DateTime.now().offset //=> -240\n   * @example DateTime.utc().offset //=> 0\n   * @type {number}\n   */\n  get offset() {\n    return this.isValid ? +this.o : NaN;\n  }\n\n  /**\n   * Get the short human name for the zone's current offset, for example \"EST\" or \"EDT\".\n   * Defaults to the system's locale if no locale has been specified\n   * @type {string}\n   */\n  get offsetNameShort() {\n    if (this.isValid) {\n      return this.zone.offsetName(this.ts, {\n        format: \"short\",\n        locale: this.locale,\n      });\n    } else {\n      return null;\n    }\n  }\n\n  /**\n   * Get the long human name for the zone's current offset, for example \"Eastern Standard Time\" or \"Eastern Daylight Time\".\n   * Defaults to the system's locale if no locale has been specified\n   * @type {string}\n   */\n  get offsetNameLong() {\n    if (this.isValid) {\n      return this.zone.offsetName(this.ts, {\n        format: \"long\",\n        locale: this.locale,\n      });\n    } else {\n      return null;\n    }\n  }\n\n  /**\n   * Get whether this zone's offset ever changes, as in a DST.\n   * @type {boolean}\n   */\n  get isOffsetFixed() {\n    return this.isValid ? this.zone.isUniversal : null;\n  }\n\n  /**\n   * Get whether the DateTime is in a DST.\n   * @type {boolean}\n   */\n  get isInDST() {\n    if (this.isOffsetFixed) {\n      return false;\n    } else {\n      return (\n        this.offset > this.set({ month: 1, day: 1 }).offset ||\n        this.offset > this.set({ month: 5 }).offset\n      );\n    }\n  }\n\n  /**\n   * Get those DateTimes which have the same local time as this DateTime, but a different offset from UTC\n   * in this DateTime's zone. During DST changes local time can be ambiguous, for example\n   * `2023-10-29T02:30:00` in `Europe/Berlin` can have offset `+01:00` or `+02:00`.\n   * This method will return both possible DateTimes if this DateTime's local time is ambiguous.\n   * @returns {DateTime[]}\n   */\n  getPossibleOffsets() {\n    if (!this.isValid || this.isOffsetFixed) {\n      return [this];\n    }\n    const dayMs = 86400000;\n    const minuteMs = 60000;\n    const localTS = objToLocalTS(this.c);\n    const oEarlier = this.zone.offset(localTS - dayMs);\n    const oLater = this.zone.offset(localTS + dayMs);\n\n    const o1 = this.zone.offset(localTS - oEarlier * minuteMs);\n    const o2 = this.zone.offset(localTS - oLater * minuteMs);\n    if (o1 === o2) {\n      return [this];\n    }\n    const ts1 = localTS - o1 * minuteMs;\n    const ts2 = localTS - o2 * minuteMs;\n    const c1 = tsToObj(ts1, o1);\n    const c2 = tsToObj(ts2, o2);\n    if (\n      c1.hour === c2.hour &&\n      c1.minute === c2.minute &&\n      c1.second === c2.second &&\n      c1.millisecond === c2.millisecond\n    ) {\n      return [clone(this, { ts: ts1 }), clone(this, { ts: ts2 })];\n    }\n    return [this];\n  }\n\n  /**\n   * Returns true if this DateTime is in a leap year, false otherwise\n   * @example DateTime.local(2016).isInLeapYear //=> true\n   * @example DateTime.local(2013).isInLeapYear //=> false\n   * @type {boolean}\n   */\n  get isInLeapYear() {\n    return isLeapYear(this.year);\n  }\n\n  /**\n   * Returns the number of days in this DateTime's month\n   * @example DateTime.local(2016, 2).daysInMonth //=> 29\n   * @example DateTime.local(2016, 3).daysInMonth //=> 31\n   * @type {number}\n   */\n  get daysInMonth() {\n    return daysInMonth(this.year, this.month);\n  }\n\n  /**\n   * Returns the number of days in this DateTime's year\n   * @example DateTime.local(2016).daysInYear //=> 366\n   * @example DateTime.local(2013).daysInYear //=> 365\n   * @type {number}\n   */\n  get daysInYear() {\n    return this.isValid ? daysInYear(this.year) : NaN;\n  }\n\n  /**\n   * Returns the number of weeks in this DateTime's year\n   * @see https://en.wikipedia.org/wiki/ISO_week_date\n   * @example DateTime.local(2004).weeksInWeekYear //=> 53\n   * @example DateTime.local(2013).weeksInWeekYear //=> 52\n   * @type {number}\n   */\n  get weeksInWeekYear() {\n    return this.isValid ? weeksInWeekYear(this.weekYear) : NaN;\n  }\n\n  /**\n   * Returns the number of weeks in this DateTime's local week year\n   * @example DateTime.local(2020, 6, {locale: 'en-US'}).weeksInLocalWeekYear //=> 52\n   * @example DateTime.local(2020, 6, {locale: 'de-DE'}).weeksInLocalWeekYear //=> 53\n   * @type {number}\n   */\n  get weeksInLocalWeekYear() {\n    return this.isValid\n      ? weeksInWeekYear(\n          this.localWeekYear,\n          this.loc.getMinDaysInFirstWeek(),\n          this.loc.getStartOfWeek()\n        )\n      : NaN;\n  }\n\n  /**\n   * Returns the resolved Intl options for this DateTime.\n   * This is useful in understanding the behavior of formatting methods\n   * @param {Object} opts - the same options as toLocaleString\n   * @return {Object}\n   */\n  resolvedLocaleOptions(opts = {}) {\n    const { locale, numberingSystem, calendar } = Formatter.create(\n      this.loc.clone(opts),\n      opts\n    ).resolvedOptions(this);\n    return { locale, numberingSystem, outputCalendar: calendar };\n  }\n\n  // TRANSFORM\n\n  /**\n   * \"Set\" the DateTime's zone to UTC. Returns a newly-constructed DateTime.\n   *\n   * Equivalent to {@link DateTime#setZone}('utc')\n   * @param {number} [offset=0] - optionally, an offset from UTC in minutes\n   * @param {Object} [opts={}] - options to pass to `setZone()`\n   * @return {DateTime}\n   */\n  toUTC(offset = 0, opts = {}) {\n    return this.setZone(FixedOffsetZone.instance(offset), opts);\n  }\n\n  /**\n   * \"Set\" the DateTime's zone to the host's local zone. Returns a newly-constructed DateTime.\n   *\n   * Equivalent to `setZone('local')`\n   * @return {DateTime}\n   */\n  toLocal() {\n    return this.setZone(Settings.defaultZone);\n  }\n\n  /**\n   * \"Set\" the DateTime's zone to specified zone. Returns a newly-constructed DateTime.\n   *\n   * By default, the setter keeps the underlying time the same (as in, the same timestamp), but the new instance will report different local times and consider DSTs when making computations, as with {@link DateTime#plus}. You may wish to use {@link DateTime#toLocal} and {@link DateTime#toUTC} which provide simple convenience wrappers for commonly used zones.\n   * @param {string|Zone} [zone='local'] - a zone identifier. As a string, that can be any IANA zone supported by the host environment, or a fixed-offset name of the form 'UTC+3', or the strings 'local' or 'utc'. You may also supply an instance of a {@link DateTime#Zone} class.\n   * @param {Object} opts - options\n   * @param {boolean} [opts.keepLocalTime=false] - If true, adjust the underlying time so that the local time stays the same, but in the target zone. You should rarely need this.\n   * @return {DateTime}\n   */\n  setZone(zone, { keepLocalTime = false, keepCalendarTime = false } = {}) {\n    zone = normalizeZone(zone, Settings.defaultZone);\n    if (zone.equals(this.zone)) {\n      return this;\n    } else if (!zone.isValid) {\n      return DateTime.invalid(unsupportedZone(zone));\n    } else {\n      let newTS = this.ts;\n      if (keepLocalTime || keepCalendarTime) {\n        const offsetGuess = zone.offset(this.ts);\n        const asObj = this.toObject();\n        [newTS] = objToTS(asObj, offsetGuess, zone);\n      }\n      return clone(this, { ts: newTS, zone });\n    }\n  }\n\n  /**\n   * \"Set\" the locale, numberingSystem, or outputCalendar. Returns a newly-constructed DateTime.\n   * @param {Object} properties - the properties to set\n   * @example DateTime.local(2017, 5, 25).reconfigure({ locale: 'en-GB' })\n   * @return {DateTime}\n   */\n  reconfigure({ locale, numberingSystem, outputCalendar } = {}) {\n    const loc = this.loc.clone({ locale, numberingSystem, outputCalendar });\n    return clone(this, { loc });\n  }\n\n  /**\n   * \"Set\" the locale. Returns a newly-constructed DateTime.\n   * Just a convenient alias for reconfigure({ locale })\n   * @example DateTime.local(2017, 5, 25).setLocale('en-GB')\n   * @return {DateTime}\n   */\n  setLocale(locale) {\n    return this.reconfigure({ locale });\n  }\n\n  /**\n   * \"Set\" the values of specified units. Returns a newly-constructed DateTime.\n   * You can only set units with this method; for \"setting\" metadata, see {@link DateTime#reconfigure} and {@link DateTime#setZone}.\n   *\n   * This method also supports setting locale-based week units, i.e. `localWeekday`, `localWeekNumber` and `localWeekYear`.\n   * They cannot be mixed with ISO-week units like `weekday`.\n   * @param {Object} values - a mapping of units to numbers\n   * @example dt.set({ year: 2017 })\n   * @example dt.set({ hour: 8, minute: 30 })\n   * @example dt.set({ weekday: 5 })\n   * @example dt.set({ year: 2005, ordinal: 234 })\n   * @return {DateTime}\n   */\n  set(values) {\n    if (!this.isValid) return this;\n\n    const normalized = normalizeObject(values, normalizeUnitWithLocalWeeks);\n    const { minDaysInFirstWeek, startOfWeek } = usesLocalWeekValues(normalized, this.loc);\n\n    const settingWeekStuff =\n        !isUndefined(normalized.weekYear) ||\n        !isUndefined(normalized.weekNumber) ||\n        !isUndefined(normalized.weekday),\n      containsOrdinal = !isUndefined(normalized.ordinal),\n      containsGregorYear = !isUndefined(normalized.year),\n      containsGregorMD = !isUndefined(normalized.month) || !isUndefined(normalized.day),\n      containsGregor = containsGregorYear || containsGregorMD,\n      definiteWeekDef = normalized.weekYear || normalized.weekNumber;\n\n    if ((containsGregor || containsOrdinal) && definiteWeekDef) {\n      throw new ConflictingSpecificationError(\n        \"Can't mix weekYear/weekNumber units with year/month/day or ordinals\"\n      );\n    }\n\n    if (containsGregorMD && containsOrdinal) {\n      throw new ConflictingSpecificationError(\"Can't mix ordinal dates with month/day\");\n    }\n\n    let mixed;\n    if (settingWeekStuff) {\n      mixed = weekToGregorian(\n        { ...gregorianToWeek(this.c, minDaysInFirstWeek, startOfWeek), ...normalized },\n        minDaysInFirstWeek,\n        startOfWeek\n      );\n    } else if (!isUndefined(normalized.ordinal)) {\n      mixed = ordinalToGregorian({ ...gregorianToOrdinal(this.c), ...normalized });\n    } else {\n      mixed = { ...this.toObject(), ...normalized };\n\n      // if we didn't set the day but we ended up on an overflow date,\n      // use the last day of the right month\n      if (isUndefined(normalized.day)) {\n        mixed.day = Math.min(daysInMonth(mixed.year, mixed.month), mixed.day);\n      }\n    }\n\n    const [ts, o] = objToTS(mixed, this.o, this.zone);\n    return clone(this, { ts, o });\n  }\n\n  /**\n   * Add a period of time to this DateTime and return the resulting DateTime\n   *\n   * Adding hours, minutes, seconds, or milliseconds increases the timestamp by the right number of milliseconds. Adding days, months, or years shifts the calendar, accounting for DSTs and leap years along the way. Thus, `dt.plus({ hours: 24 })` may result in a different time than `dt.plus({ days: 1 })` if there's a DST shift in between.\n   * @param {Duration|Object|number} duration - The amount to add. Either a Luxon Duration, a number of milliseconds, the object argument to Duration.fromObject()\n   * @example DateTime.now().plus(123) //~> in 123 milliseconds\n   * @example DateTime.now().plus({ minutes: 15 }) //~> in 15 minutes\n   * @example DateTime.now().plus({ days: 1 }) //~> this time tomorrow\n   * @example DateTime.now().plus({ days: -1 }) //~> this time yesterday\n   * @example DateTime.now().plus({ hours: 3, minutes: 13 }) //~> in 3 hr, 13 min\n   * @example DateTime.now().plus(Duration.fromObject({ hours: 3, minutes: 13 })) //~> in 3 hr, 13 min\n   * @return {DateTime}\n   */\n  plus(duration) {\n    if (!this.isValid) return this;\n    const dur = Duration.fromDurationLike(duration);\n    return clone(this, adjustTime(this, dur));\n  }\n\n  /**\n   * Subtract a period of time to this DateTime and return the resulting DateTime\n   * See {@link DateTime#plus}\n   * @param {Duration|Object|number} duration - The amount to subtract. Either a Luxon Duration, a number of milliseconds, the object argument to Duration.fromObject()\n   @return {DateTime}\n   */\n  minus(duration) {\n    if (!this.isValid) return this;\n    const dur = Duration.fromDurationLike(duration).negate();\n    return clone(this, adjustTime(this, dur));\n  }\n\n  /**\n   * \"Set\" this DateTime to the beginning of a unit of time.\n   * @param {string} unit - The unit to go to the beginning of. Can be 'year', 'quarter', 'month', 'week', 'day', 'hour', 'minute', 'second', or 'millisecond'.\n   * @param {Object} opts - options\n   * @param {boolean} [opts.useLocaleWeeks=false] - If true, use weeks based on the locale, i.e. use the locale-dependent start of the week\n   * @example DateTime.local(2014, 3, 3).startOf('month').toISODate(); //=> '2014-03-01'\n   * @example DateTime.local(2014, 3, 3).startOf('year').toISODate(); //=> '2014-01-01'\n   * @example DateTime.local(2014, 3, 3).startOf('week').toISODate(); //=> '2014-03-03', weeks always start on Mondays\n   * @example DateTime.local(2014, 3, 3, 5, 30).startOf('day').toISOTime(); //=> '00:00.000-05:00'\n   * @example DateTime.local(2014, 3, 3, 5, 30).startOf('hour').toISOTime(); //=> '05:00:00.000-05:00'\n   * @return {DateTime}\n   */\n  startOf(unit, { useLocaleWeeks = false } = {}) {\n    if (!this.isValid) return this;\n\n    const o = {},\n      normalizedUnit = Duration.normalizeUnit(unit);\n    switch (normalizedUnit) {\n      case \"years\":\n        o.month = 1;\n      // falls through\n      case \"quarters\":\n      case \"months\":\n        o.day = 1;\n      // falls through\n      case \"weeks\":\n      case \"days\":\n        o.hour = 0;\n      // falls through\n      case \"hours\":\n        o.minute = 0;\n      // falls through\n      case \"minutes\":\n        o.second = 0;\n      // falls through\n      case \"seconds\":\n        o.millisecond = 0;\n        break;\n      case \"milliseconds\":\n        break;\n      // no default, invalid units throw in normalizeUnit()\n    }\n\n    if (normalizedUnit === \"weeks\") {\n      if (useLocaleWeeks) {\n        const startOfWeek = this.loc.getStartOfWeek();\n        const { weekday } = this;\n        if (weekday < startOfWeek) {\n          o.weekNumber = this.weekNumber - 1;\n        }\n        o.weekday = startOfWeek;\n      } else {\n        o.weekday = 1;\n      }\n    }\n\n    if (normalizedUnit === \"quarters\") {\n      const q = Math.ceil(this.month / 3);\n      o.month = (q - 1) * 3 + 1;\n    }\n\n    return this.set(o);\n  }\n\n  /**\n   * \"Set\" this DateTime to the end (meaning the last millisecond) of a unit of time\n   * @param {string} unit - The unit to go to the end of. Can be 'year', 'quarter', 'month', 'week', 'day', 'hour', 'minute', 'second', or 'millisecond'.\n   * @param {Object} opts - options\n   * @param {boolean} [opts.useLocaleWeeks=false] - If true, use weeks based on the locale, i.e. use the locale-dependent start of the week\n   * @example DateTime.local(2014, 3, 3).endOf('month').toISO(); //=> '2014-03-31T23:59:59.999-05:00'\n   * @example DateTime.local(2014, 3, 3).endOf('year').toISO(); //=> '2014-12-31T23:59:59.999-05:00'\n   * @example DateTime.local(2014, 3, 3).endOf('week').toISO(); // => '2014-03-09T23:59:59.999-05:00', weeks start on Mondays\n   * @example DateTime.local(2014, 3, 3, 5, 30).endOf('day').toISO(); //=> '2014-03-03T23:59:59.999-05:00'\n   * @example DateTime.local(2014, 3, 3, 5, 30).endOf('hour').toISO(); //=> '2014-03-03T05:59:59.999-05:00'\n   * @return {DateTime}\n   */\n  endOf(unit, opts) {\n    return this.isValid\n      ? this.plus({ [unit]: 1 })\n          .startOf(unit, opts)\n          .minus(1)\n      : this;\n  }\n\n  // OUTPUT\n\n  /**\n   * Returns a string representation of this DateTime formatted according to the specified format string.\n   * **You may not want this.** See {@link DateTime#toLocaleString} for a more flexible formatting tool. For a table of tokens and their interpretations, see [here](https://moment.github.io/luxon/#/formatting?id=table-of-tokens).\n   * Defaults to en-US if no locale has been specified, regardless of the system's locale.\n   * @param {string} fmt - the format string\n   * @param {Object} opts - opts to override the configuration options on this DateTime\n   * @example DateTime.now().toFormat('yyyy LLL dd') //=> '2017 Apr 22'\n   * @example DateTime.now().setLocale('fr').toFormat('yyyy LLL dd') //=> '2017 avr. 22'\n   * @example DateTime.now().toFormat('yyyy LLL dd', { locale: \"fr\" }) //=> '2017 avr. 22'\n   * @example DateTime.now().toFormat(\"HH 'hours and' mm 'minutes'\") //=> '20 hours and 55 minutes'\n   * @return {string}\n   */\n  toFormat(fmt, opts = {}) {\n    return this.isValid\n      ? Formatter.create(this.loc.redefaultToEN(opts)).formatDateTimeFromString(this, fmt)\n      : INVALID;\n  }\n\n  /**\n   * Returns a localized string representing this date. Accepts the same options as the Intl.DateTimeFormat constructor and any presets defined by Luxon, such as `DateTime.DATE_FULL` or `DateTime.TIME_SIMPLE`.\n   * The exact behavior of this method is browser-specific, but in general it will return an appropriate representation\n   * of the DateTime in the assigned locale.\n   * Defaults to the system's locale if no locale has been specified\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat\n   * @param formatOpts {Object} - Intl.DateTimeFormat constructor options and configuration options\n   * @param {Object} opts - opts to override the configuration options on this DateTime\n   * @example DateTime.now().toLocaleString(); //=> 4/20/2017\n   * @example DateTime.now().setLocale('en-gb').toLocaleString(); //=> '20/04/2017'\n   * @example DateTime.now().toLocaleString(DateTime.DATE_FULL); //=> 'April 20, 2017'\n   * @example DateTime.now().toLocaleString(DateTime.DATE_FULL, { locale: 'fr' }); //=> '28 août 2022'\n   * @example DateTime.now().toLocaleString(DateTime.TIME_SIMPLE); //=> '11:32 AM'\n   * @example DateTime.now().toLocaleString(DateTime.DATETIME_SHORT); //=> '4/20/2017, 11:32 AM'\n   * @example DateTime.now().toLocaleString({ weekday: 'long', month: 'long', day: '2-digit' }); //=> 'Thursday, April 20'\n   * @example DateTime.now().toLocaleString({ weekday: 'short', month: 'short', day: '2-digit', hour: '2-digit', minute: '2-digit' }); //=> 'Thu, Apr 20, 11:27 AM'\n   * @example DateTime.now().toLocaleString({ hour: '2-digit', minute: '2-digit', hourCycle: 'h23' }); //=> '11:32'\n   * @return {string}\n   */\n  toLocaleString(formatOpts = Formats.DATE_SHORT, opts = {}) {\n    return this.isValid\n      ? Formatter.create(this.loc.clone(opts), formatOpts).formatDateTime(this)\n      : INVALID;\n  }\n\n  /**\n   * Returns an array of format \"parts\", meaning individual tokens along with metadata. This is allows callers to post-process individual sections of the formatted output.\n   * Defaults to the system's locale if no locale has been specified\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat/formatToParts\n   * @param opts {Object} - Intl.DateTimeFormat constructor options, same as `toLocaleString`.\n   * @example DateTime.now().toLocaleParts(); //=> [\n   *                                   //=>   { type: 'day', value: '25' },\n   *                                   //=>   { type: 'literal', value: '/' },\n   *                                   //=>   { type: 'month', value: '05' },\n   *                                   //=>   { type: 'literal', value: '/' },\n   *                                   //=>   { type: 'year', value: '1982' }\n   *                                   //=> ]\n   */\n  toLocaleParts(opts = {}) {\n    return this.isValid\n      ? Formatter.create(this.loc.clone(opts), opts).formatDateTimeParts(this)\n      : [];\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this DateTime\n   * @param {Object} opts - options\n   * @param {boolean} [opts.suppressMilliseconds=false] - exclude milliseconds from the format if they're 0\n   * @param {boolean} [opts.suppressSeconds=false] - exclude seconds from the format if they're 0\n   * @param {boolean} [opts.includeOffset=true] - include the offset, such as 'Z' or '-04:00'\n   * @param {boolean} [opts.extendedZone=false] - add the time zone format extension\n   * @param {string} [opts.format='extended'] - choose between the basic and extended format\n   * @example DateTime.utc(1983, 5, 25).toISO() //=> '1982-05-25T00:00:00.000Z'\n   * @example DateTime.now().toISO() //=> '2017-04-22T20:47:05.335-04:00'\n   * @example DateTime.now().toISO({ includeOffset: false }) //=> '2017-04-22T20:47:05.335'\n   * @example DateTime.now().toISO({ format: 'basic' }) //=> '20170422T204705.335-0400'\n   * @return {string|null}\n   */\n  toISO({\n    format = \"extended\",\n    suppressSeconds = false,\n    suppressMilliseconds = false,\n    includeOffset = true,\n    extendedZone = false,\n  } = {}) {\n    if (!this.isValid) {\n      return null;\n    }\n\n    const ext = format === \"extended\";\n\n    let c = toISODate(this, ext);\n    c += \"T\";\n    c += toISOTime(this, ext, suppressSeconds, suppressMilliseconds, includeOffset, extendedZone);\n    return c;\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this DateTime's date component\n   * @param {Object} opts - options\n   * @param {string} [opts.format='extended'] - choose between the basic and extended format\n   * @example DateTime.utc(1982, 5, 25).toISODate() //=> '1982-05-25'\n   * @example DateTime.utc(1982, 5, 25).toISODate({ format: 'basic' }) //=> '19820525'\n   * @return {string|null}\n   */\n  toISODate({ format = \"extended\" } = {}) {\n    if (!this.isValid) {\n      return null;\n    }\n\n    return toISODate(this, format === \"extended\");\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this DateTime's week date\n   * @example DateTime.utc(1982, 5, 25).toISOWeekDate() //=> '1982-W21-2'\n   * @return {string}\n   */\n  toISOWeekDate() {\n    return toTechFormat(this, \"kkkk-'W'WW-c\");\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this DateTime's time component\n   * @param {Object} opts - options\n   * @param {boolean} [opts.suppressMilliseconds=false] - exclude milliseconds from the format if they're 0\n   * @param {boolean} [opts.suppressSeconds=false] - exclude seconds from the format if they're 0\n   * @param {boolean} [opts.includeOffset=true] - include the offset, such as 'Z' or '-04:00'\n   * @param {boolean} [opts.extendedZone=true] - add the time zone format extension\n   * @param {boolean} [opts.includePrefix=false] - include the `T` prefix\n   * @param {string} [opts.format='extended'] - choose between the basic and extended format\n   * @example DateTime.utc().set({ hour: 7, minute: 34 }).toISOTime() //=> '07:34:19.361Z'\n   * @example DateTime.utc().set({ hour: 7, minute: 34, seconds: 0, milliseconds: 0 }).toISOTime({ suppressSeconds: true }) //=> '07:34Z'\n   * @example DateTime.utc().set({ hour: 7, minute: 34 }).toISOTime({ format: 'basic' }) //=> '073419.361Z'\n   * @example DateTime.utc().set({ hour: 7, minute: 34 }).toISOTime({ includePrefix: true }) //=> 'T07:34:19.361Z'\n   * @return {string}\n   */\n  toISOTime({\n    suppressMilliseconds = false,\n    suppressSeconds = false,\n    includeOffset = true,\n    includePrefix = false,\n    extendedZone = false,\n    format = \"extended\",\n  } = {}) {\n    if (!this.isValid) {\n      return null;\n    }\n\n    let c = includePrefix ? \"T\" : \"\";\n    return (\n      c +\n      toISOTime(\n        this,\n        format === \"extended\",\n        suppressSeconds,\n        suppressMilliseconds,\n        includeOffset,\n        extendedZone\n      )\n    );\n  }\n\n  /**\n   * Returns an RFC 2822-compatible string representation of this DateTime\n   * @example DateTime.utc(2014, 7, 13).toRFC2822() //=> 'Sun, 13 Jul 2014 00:00:00 +0000'\n   * @example DateTime.local(2014, 7, 13).toRFC2822() //=> 'Sun, 13 Jul 2014 00:00:00 -0400'\n   * @return {string}\n   */\n  toRFC2822() {\n    return toTechFormat(this, \"EEE, dd LLL yyyy HH:mm:ss ZZZ\", false);\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for use in HTTP headers. The output is always expressed in GMT.\n   * Specifically, the string conforms to RFC 1123.\n   * @see https://www.w3.org/Protocols/rfc2616/rfc2616-sec3.html#sec3.3.1\n   * @example DateTime.utc(2014, 7, 13).toHTTP() //=> 'Sun, 13 Jul 2014 00:00:00 GMT'\n   * @example DateTime.utc(2014, 7, 13, 19).toHTTP() //=> 'Sun, 13 Jul 2014 19:00:00 GMT'\n   * @return {string}\n   */\n  toHTTP() {\n    return toTechFormat(this.toUTC(), \"EEE, dd LLL yyyy HH:mm:ss 'GMT'\");\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for use in SQL Date\n   * @example DateTime.utc(2014, 7, 13).toSQLDate() //=> '2014-07-13'\n   * @return {string|null}\n   */\n  toSQLDate() {\n    if (!this.isValid) {\n      return null;\n    }\n    return toISODate(this, true);\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for use in SQL Time\n   * @param {Object} opts - options\n   * @param {boolean} [opts.includeZone=false] - include the zone, such as 'America/New_York'. Overrides includeOffset.\n   * @param {boolean} [opts.includeOffset=true] - include the offset, such as 'Z' or '-04:00'\n   * @param {boolean} [opts.includeOffsetSpace=true] - include the space between the time and the offset, such as '05:15:16.345 -04:00'\n   * @example DateTime.utc().toSQL() //=> '05:15:16.345'\n   * @example DateTime.now().toSQL() //=> '05:15:16.345 -04:00'\n   * @example DateTime.now().toSQL({ includeOffset: false }) //=> '05:15:16.345'\n   * @example DateTime.now().toSQL({ includeZone: false }) //=> '05:15:16.345 America/New_York'\n   * @return {string}\n   */\n  toSQLTime({ includeOffset = true, includeZone = false, includeOffsetSpace = true } = {}) {\n    let fmt = \"HH:mm:ss.SSS\";\n\n    if (includeZone || includeOffset) {\n      if (includeOffsetSpace) {\n        fmt += \" \";\n      }\n      if (includeZone) {\n        fmt += \"z\";\n      } else if (includeOffset) {\n        fmt += \"ZZ\";\n      }\n    }\n\n    return toTechFormat(this, fmt, true);\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for use in SQL DateTime\n   * @param {Object} opts - options\n   * @param {boolean} [opts.includeZone=false] - include the zone, such as 'America/New_York'. Overrides includeOffset.\n   * @param {boolean} [opts.includeOffset=true] - include the offset, such as 'Z' or '-04:00'\n   * @param {boolean} [opts.includeOffsetSpace=true] - include the space between the time and the offset, such as '05:15:16.345 -04:00'\n   * @example DateTime.utc(2014, 7, 13).toSQL() //=> '2014-07-13 00:00:00.000 Z'\n   * @example DateTime.local(2014, 7, 13).toSQL() //=> '2014-07-13 00:00:00.000 -04:00'\n   * @example DateTime.local(2014, 7, 13).toSQL({ includeOffset: false }) //=> '2014-07-13 00:00:00.000'\n   * @example DateTime.local(2014, 7, 13).toSQL({ includeZone: true }) //=> '2014-07-13 00:00:00.000 America/New_York'\n   * @return {string}\n   */\n  toSQL(opts = {}) {\n    if (!this.isValid) {\n      return null;\n    }\n\n    return `${this.toSQLDate()} ${this.toSQLTime(opts)}`;\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for debugging\n   * @return {string}\n   */\n  toString() {\n    return this.isValid ? this.toISO() : INVALID;\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for the REPL.\n   * @return {string}\n   */\n  [Symbol.for(\"nodejs.util.inspect.custom\")]() {\n    if (this.isValid) {\n      return `DateTime { ts: ${this.toISO()}, zone: ${this.zone.name}, locale: ${this.locale} }`;\n    } else {\n      return `DateTime { Invalid, reason: ${this.invalidReason} }`;\n    }\n  }\n\n  /**\n   * Returns the epoch milliseconds of this DateTime. Alias of {@link DateTime#toMillis}\n   * @return {number}\n   */\n  valueOf() {\n    return this.toMillis();\n  }\n\n  /**\n   * Returns the epoch milliseconds of this DateTime.\n   * @return {number}\n   */\n  toMillis() {\n    return this.isValid ? this.ts : NaN;\n  }\n\n  /**\n   * Returns the epoch seconds (including milliseconds in the fractional part) of this DateTime.\n   * @return {number}\n   */\n  toSeconds() {\n    return this.isValid ? this.ts / 1000 : NaN;\n  }\n\n  /**\n   * Returns the epoch seconds (as a whole number) of this DateTime.\n   * @return {number}\n   */\n  toUnixInteger() {\n    return this.isValid ? Math.floor(this.ts / 1000) : NaN;\n  }\n\n  /**\n   * Returns an ISO 8601 representation of this DateTime appropriate for use in JSON.\n   * @return {string}\n   */\n  toJSON() {\n    return this.toISO();\n  }\n\n  /**\n   * Returns a BSON serializable equivalent to this DateTime.\n   * @return {Date}\n   */\n  toBSON() {\n    return this.toJSDate();\n  }\n\n  /**\n   * Returns a JavaScript object with this DateTime's year, month, day, and so on.\n   * @param opts - options for generating the object\n   * @param {boolean} [opts.includeConfig=false] - include configuration attributes in the output\n   * @example DateTime.now().toObject() //=> { year: 2017, month: 4, day: 22, hour: 20, minute: 49, second: 42, millisecond: 268 }\n   * @return {Object}\n   */\n  toObject(opts = {}) {\n    if (!this.isValid) return {};\n\n    const base = { ...this.c };\n\n    if (opts.includeConfig) {\n      base.outputCalendar = this.outputCalendar;\n      base.numberingSystem = this.loc.numberingSystem;\n      base.locale = this.loc.locale;\n    }\n    return base;\n  }\n\n  /**\n   * Returns a JavaScript Date equivalent to this DateTime.\n   * @return {Date}\n   */\n  toJSDate() {\n    return new Date(this.isValid ? this.ts : NaN);\n  }\n\n  // COMPARE\n\n  /**\n   * Return the difference between two DateTimes as a Duration.\n   * @param {DateTime} otherDateTime - the DateTime to compare this one to\n   * @param {string|string[]} [unit=['milliseconds']] - the unit or array of units (such as 'hours' or 'days') to include in the duration.\n   * @param {Object} opts - options that affect the creation of the Duration\n   * @param {string} [opts.conversionAccuracy='casual'] - the conversion system to use\n   * @example\n   * var i1 = DateTime.fromISO('1982-05-25T09:45'),\n   *     i2 = DateTime.fromISO('1983-10-14T10:30');\n   * i2.diff(i1).toObject() //=> { milliseconds: 43807500000 }\n   * i2.diff(i1, 'hours').toObject() //=> { hours: 12168.75 }\n   * i2.diff(i1, ['months', 'days']).toObject() //=> { months: 16, days: 19.03125 }\n   * i2.diff(i1, ['months', 'days', 'hours']).toObject() //=> { months: 16, days: 19, hours: 0.75 }\n   * @return {Duration}\n   */\n  diff(otherDateTime, unit = \"milliseconds\", opts = {}) {\n    if (!this.isValid || !otherDateTime.isValid) {\n      return Duration.invalid(\"created by diffing an invalid DateTime\");\n    }\n\n    const durOpts = { locale: this.locale, numberingSystem: this.numberingSystem, ...opts };\n\n    const units = maybeArray(unit).map(Duration.normalizeUnit),\n      otherIsLater = otherDateTime.valueOf() > this.valueOf(),\n      earlier = otherIsLater ? this : otherDateTime,\n      later = otherIsLater ? otherDateTime : this,\n      diffed = diff(earlier, later, units, durOpts);\n\n    return otherIsLater ? diffed.negate() : diffed;\n  }\n\n  /**\n   * Return the difference between this DateTime and right now.\n   * See {@link DateTime#diff}\n   * @param {string|string[]} [unit=['milliseconds']] - the unit or units units (such as 'hours' or 'days') to include in the duration\n   * @param {Object} opts - options that affect the creation of the Duration\n   * @param {string} [opts.conversionAccuracy='casual'] - the conversion system to use\n   * @return {Duration}\n   */\n  diffNow(unit = \"milliseconds\", opts = {}) {\n    return this.diff(DateTime.now(), unit, opts);\n  }\n\n  /**\n   * Return an Interval spanning between this DateTime and another DateTime\n   * @param {DateTime} otherDateTime - the other end point of the Interval\n   * @return {Interval|DateTime}\n   */\n  until(otherDateTime) {\n    return this.isValid ? Interval.fromDateTimes(this, otherDateTime) : this;\n  }\n\n  /**\n   * Return whether this DateTime is in the same unit of time as another DateTime.\n   * Higher-order units must also be identical for this function to return `true`.\n   * Note that time zones are **ignored** in this comparison, which compares the **local** calendar time. Use {@link DateTime#setZone} to convert one of the dates if needed.\n   * @param {DateTime} otherDateTime - the other DateTime\n   * @param {string} unit - the unit of time to check sameness on\n   * @param {Object} opts - options\n   * @param {boolean} [opts.useLocaleWeeks=false] - If true, use weeks based on the locale, i.e. use the locale-dependent start of the week; only the locale of this DateTime is used\n   * @example DateTime.now().hasSame(otherDT, 'day'); //~> true if otherDT is in the same current calendar day\n   * @return {boolean}\n   */\n  hasSame(otherDateTime, unit, opts) {\n    if (!this.isValid) return false;\n\n    const inputMs = otherDateTime.valueOf();\n    const adjustedToZone = this.setZone(otherDateTime.zone, { keepLocalTime: true });\n    return (\n      adjustedToZone.startOf(unit, opts) <= inputMs && inputMs <= adjustedToZone.endOf(unit, opts)\n    );\n  }\n\n  /**\n   * Equality check\n   * Two DateTimes are equal if and only if they represent the same millisecond, have the same zone and location, and are both valid.\n   * To compare just the millisecond values, use `+dt1 === +dt2`.\n   * @param {DateTime} other - the other DateTime\n   * @return {boolean}\n   */\n  equals(other) {\n    return (\n      this.isValid &&\n      other.isValid &&\n      this.valueOf() === other.valueOf() &&\n      this.zone.equals(other.zone) &&\n      this.loc.equals(other.loc)\n    );\n  }\n\n  /**\n   * Returns a string representation of a this time relative to now, such as \"in two days\". Can only internationalize if your\n   * platform supports Intl.RelativeTimeFormat. Rounds down by default.\n   * @param {Object} options - options that affect the output\n   * @param {DateTime} [options.base=DateTime.now()] - the DateTime to use as the basis to which this time is compared. Defaults to now.\n   * @param {string} [options.style=\"long\"] - the style of units, must be \"long\", \"short\", or \"narrow\"\n   * @param {string|string[]} options.unit - use a specific unit or array of units; if omitted, or an array, the method will pick the best unit. Use an array or one of \"years\", \"quarters\", \"months\", \"weeks\", \"days\", \"hours\", \"minutes\", or \"seconds\"\n   * @param {boolean} [options.round=true] - whether to round the numbers in the output.\n   * @param {number} [options.padding=0] - padding in milliseconds. This allows you to round up the result if it fits inside the threshold. Don't use in combination with {round: false} because the decimal output will include the padding.\n   * @param {string} options.locale - override the locale of this DateTime\n   * @param {string} options.numberingSystem - override the numberingSystem of this DateTime. The Intl system may choose not to honor this\n   * @example DateTime.now().plus({ days: 1 }).toRelative() //=> \"in 1 day\"\n   * @example DateTime.now().setLocale(\"es\").toRelative({ days: 1 }) //=> \"dentro de 1 día\"\n   * @example DateTime.now().plus({ days: 1 }).toRelative({ locale: \"fr\" }) //=> \"dans 23 heures\"\n   * @example DateTime.now().minus({ days: 2 }).toRelative() //=> \"2 days ago\"\n   * @example DateTime.now().minus({ days: 2 }).toRelative({ unit: \"hours\" }) //=> \"48 hours ago\"\n   * @example DateTime.now().minus({ hours: 36 }).toRelative({ round: false }) //=> \"1.5 days ago\"\n   */\n  toRelative(options = {}) {\n    if (!this.isValid) return null;\n    const base = options.base || DateTime.fromObject({}, { zone: this.zone }),\n      padding = options.padding ? (this < base ? -options.padding : options.padding) : 0;\n    let units = [\"years\", \"months\", \"days\", \"hours\", \"minutes\", \"seconds\"];\n    let unit = options.unit;\n    if (Array.isArray(options.unit)) {\n      units = options.unit;\n      unit = undefined;\n    }\n    return diffRelative(base, this.plus(padding), {\n      ...options,\n      numeric: \"always\",\n      units,\n      unit,\n    });\n  }\n\n  /**\n   * Returns a string representation of this date relative to today, such as \"yesterday\" or \"next month\".\n   * Only internationalizes on platforms that supports Intl.RelativeTimeFormat.\n   * @param {Object} options - options that affect the output\n   * @param {DateTime} [options.base=DateTime.now()] - the DateTime to use as the basis to which this time is compared. Defaults to now.\n   * @param {string} options.locale - override the locale of this DateTime\n   * @param {string} options.unit - use a specific unit; if omitted, the method will pick the unit. Use one of \"years\", \"quarters\", \"months\", \"weeks\", or \"days\"\n   * @param {string} options.numberingSystem - override the numberingSystem of this DateTime. The Intl system may choose not to honor this\n   * @example DateTime.now().plus({ days: 1 }).toRelativeCalendar() //=> \"tomorrow\"\n   * @example DateTime.now().setLocale(\"es\").plus({ days: 1 }).toRelative() //=> \"\"mañana\"\n   * @example DateTime.now().plus({ days: 1 }).toRelativeCalendar({ locale: \"fr\" }) //=> \"demain\"\n   * @example DateTime.now().minus({ days: 2 }).toRelativeCalendar() //=> \"2 days ago\"\n   */\n  toRelativeCalendar(options = {}) {\n    if (!this.isValid) return null;\n\n    return diffRelative(options.base || DateTime.fromObject({}, { zone: this.zone }), this, {\n      ...options,\n      numeric: \"auto\",\n      units: [\"years\", \"months\", \"days\"],\n      calendary: true,\n    });\n  }\n\n  /**\n   * Return the min of several date times\n   * @param {...DateTime} dateTimes - the DateTimes from which to choose the minimum\n   * @return {DateTime} the min DateTime, or undefined if called with no argument\n   */\n  static min(...dateTimes) {\n    if (!dateTimes.every(DateTime.isDateTime)) {\n      throw new InvalidArgumentError(\"min requires all arguments be DateTimes\");\n    }\n    return bestBy(dateTimes, (i) => i.valueOf(), Math.min);\n  }\n\n  /**\n   * Return the max of several date times\n   * @param {...DateTime} dateTimes - the DateTimes from which to choose the maximum\n   * @return {DateTime} the max DateTime, or undefined if called with no argument\n   */\n  static max(...dateTimes) {\n    if (!dateTimes.every(DateTime.isDateTime)) {\n      throw new InvalidArgumentError(\"max requires all arguments be DateTimes\");\n    }\n    return bestBy(dateTimes, (i) => i.valueOf(), Math.max);\n  }\n\n  // MISC\n\n  /**\n   * Explain how a string would be parsed by fromFormat()\n   * @param {string} text - the string to parse\n   * @param {string} fmt - the format the string is expected to be in (see description)\n   * @param {Object} options - options taken by fromFormat()\n   * @return {Object}\n   */\n  static fromFormatExplain(text, fmt, options = {}) {\n    const { locale = null, numberingSystem = null } = options,\n      localeToUse = Locale.fromOpts({\n        locale,\n        numberingSystem,\n        defaultToEN: true,\n      });\n    return explainFromTokens(localeToUse, text, fmt);\n  }\n\n  /**\n   * @deprecated use fromFormatExplain instead\n   */\n  static fromStringExplain(text, fmt, options = {}) {\n    return DateTime.fromFormatExplain(text, fmt, options);\n  }\n\n  /**\n   * Build a parser for `fmt` using the given locale. This parser can be passed\n   * to {@link DateTime.fromFormatParser} to a parse a date in this format. This\n   * can be used to optimize cases where many dates need to be parsed in a\n   * specific format.\n   *\n   * @param {String} fmt - the format the string is expected to be in (see\n   * description)\n   * @param {Object} options - options used to set locale and numberingSystem\n   * for parser\n   * @returns {TokenParser} - opaque object to be used\n   */\n  static buildFormatParser(fmt, options = {}) {\n    const { locale = null, numberingSystem = null } = options,\n      localeToUse = Locale.fromOpts({\n        locale,\n        numberingSystem,\n        defaultToEN: true,\n      });\n    return new TokenParser(localeToUse, fmt);\n  }\n\n  /**\n   * Create a DateTime from an input string and format parser.\n   *\n   * The format parser must have been created with the same locale as this call.\n   *\n   * @param {String} text - the string to parse\n   * @param {TokenParser} formatParser - parser from {@link DateTime.buildFormatParser}\n   * @param {Object} opts - options taken by fromFormat()\n   * @returns {DateTime}\n   */\n  static fromFormatParser(text, formatParser, opts = {}) {\n    if (isUndefined(text) || isUndefined(formatParser)) {\n      throw new InvalidArgumentError(\n        \"fromFormatParser requires an input string and a format parser\"\n      );\n    }\n    const { locale = null, numberingSystem = null } = opts,\n      localeToUse = Locale.fromOpts({\n        locale,\n        numberingSystem,\n        defaultToEN: true,\n      });\n\n    if (!localeToUse.equals(formatParser.locale)) {\n      throw new InvalidArgumentError(\n        `fromFormatParser called with a locale of ${localeToUse}, ` +\n          `but the format parser was created for ${formatParser.locale}`\n      );\n    }\n\n    const { result, zone, specificOffset, invalidReason } = formatParser.explainFromTokens(text);\n\n    if (invalidReason) {\n      return DateTime.invalid(invalidReason);\n    } else {\n      return parseDataToDateTime(\n        result,\n        zone,\n        opts,\n        `format ${formatParser.format}`,\n        text,\n        specificOffset\n      );\n    }\n  }\n\n  // FORMAT PRESETS\n\n  /**\n   * {@link DateTime#toLocaleString} format like 10/14/1983\n   * @type {Object}\n   */\n  static get DATE_SHORT() {\n    return Formats.DATE_SHORT;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Oct 14, 1983'\n   * @type {Object}\n   */\n  static get DATE_MED() {\n    return Formats.DATE_MED;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Fri, Oct 14, 1983'\n   * @type {Object}\n   */\n  static get DATE_MED_WITH_WEEKDAY() {\n    return Formats.DATE_MED_WITH_WEEKDAY;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'October 14, 1983'\n   * @type {Object}\n   */\n  static get DATE_FULL() {\n    return Formats.DATE_FULL;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Tuesday, October 14, 1983'\n   * @type {Object}\n   */\n  static get DATE_HUGE() {\n    return Formats.DATE_HUGE;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get TIME_SIMPLE() {\n    return Formats.TIME_SIMPLE;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get TIME_WITH_SECONDS() {\n    return Formats.TIME_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 AM EDT'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get TIME_WITH_SHORT_OFFSET() {\n    return Formats.TIME_WITH_SHORT_OFFSET;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 AM Eastern Daylight Time'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get TIME_WITH_LONG_OFFSET() {\n    return Formats.TIME_WITH_LONG_OFFSET;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30', always 24-hour.\n   * @type {Object}\n   */\n  static get TIME_24_SIMPLE() {\n    return Formats.TIME_24_SIMPLE;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23', always 24-hour.\n   * @type {Object}\n   */\n  static get TIME_24_WITH_SECONDS() {\n    return Formats.TIME_24_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 EDT', always 24-hour.\n   * @type {Object}\n   */\n  static get TIME_24_WITH_SHORT_OFFSET() {\n    return Formats.TIME_24_WITH_SHORT_OFFSET;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 Eastern Daylight Time', always 24-hour.\n   * @type {Object}\n   */\n  static get TIME_24_WITH_LONG_OFFSET() {\n    return Formats.TIME_24_WITH_LONG_OFFSET;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '10/14/1983, 9:30 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_SHORT() {\n    return Formats.DATETIME_SHORT;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '10/14/1983, 9:30:33 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_SHORT_WITH_SECONDS() {\n    return Formats.DATETIME_SHORT_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Oct 14, 1983, 9:30 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_MED() {\n    return Formats.DATETIME_MED;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Oct 14, 1983, 9:30:33 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_MED_WITH_SECONDS() {\n    return Formats.DATETIME_MED_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Fri, 14 Oct 1983, 9:30 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_MED_WITH_WEEKDAY() {\n    return Formats.DATETIME_MED_WITH_WEEKDAY;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'October 14, 1983, 9:30 AM EDT'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_FULL() {\n    return Formats.DATETIME_FULL;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'October 14, 1983, 9:30:33 AM EDT'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_FULL_WITH_SECONDS() {\n    return Formats.DATETIME_FULL_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Friday, October 14, 1983, 9:30 AM Eastern Daylight Time'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_HUGE() {\n    return Formats.DATETIME_HUGE;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Friday, October 14, 1983, 9:30:33 AM Eastern Daylight Time'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_HUGE_WITH_SECONDS() {\n    return Formats.DATETIME_HUGE_WITH_SECONDS;\n  }\n}\n\n/**\n * @private\n */\nexport function friendlyDateTime(dateTimeish) {\n  if (DateTime.isDateTime(dateTimeish)) {\n    return dateTimeish;\n  } else if (dateTimeish && dateTimeish.valueOf && isNumber(dateTimeish.valueOf())) {\n    return DateTime.fromJSDate(dateTimeish);\n  } else if (dateTimeish && typeof dateTimeish === \"object\") {\n    return DateTime.fromObject(dateTimeish);\n  } else {\n    throw new InvalidArgumentError(\n      `Unknown datetime argument: ${dateTimeish}, of type ${typeof dateTimeish}`\n    );\n  }\n}\n", "import DateTime from \"./datetime.js\";\nimport Duration from \"./duration.js\";\nimport Interval from \"./interval.js\";\nimport Info from \"./info.js\";\nimport Zone from \"./zone.js\";\nimport FixedOffsetZone from \"./zones/fixedOffsetZone.js\";\nimport IANAZone from \"./zones/IANAZone.js\";\nimport InvalidZone from \"./zones/invalidZone.js\";\nimport SystemZone from \"./zones/systemZone.js\";\nimport Settings from \"./settings.js\";\n\nconst VERSION = \"3.6.1\";\n\nexport {\n  VERSION,\n  DateTime,\n  Duration,\n  Interval,\n  Info,\n  Zone,\n  FixedOffsetZone,\n  IANAZone,\n  InvalidZone,\n  SystemZone,\n  Settings,\n};\n", "'use strict';\n\nvar luxon = require('luxon');\n\nCronDate.prototype.addYear = function() {\n  this._date = this._date.plus({ years: 1 });\n};\n\nCronDate.prototype.addMonth = function() {\n  this._date = this._date.plus({ months: 1 }).startOf('month');\n};\n\nCronDate.prototype.addDay = function() {\n  this._date = this._date.plus({ days: 1 }).startOf('day');\n};\n\nCronDate.prototype.addHour = function() {\n  var prev = this._date;\n  this._date = this._date.plus({ hours: 1 }).startOf('hour');\n  if (this._date <= prev) {\n    this._date = this._date.plus({ hours: 1 });\n  }\n};\n\nCronDate.prototype.addMinute = function() {\n  var prev = this._date;\n  this._date = this._date.plus({ minutes: 1 }).startOf('minute');\n  if (this._date < prev) {\n    this._date = this._date.plus({ hours: 1 });\n  }\n};\n\nCronDate.prototype.addSecond = function() {\n  var prev = this._date;\n  this._date = this._date.plus({ seconds: 1 }).startOf('second');\n  if (this._date < prev) {\n    this._date = this._date.plus({ hours: 1 });\n  }\n};\n\nCronDate.prototype.subtractYear = function() {\n  this._date = this._date.minus({ years: 1 });\n};\n\nCronDate.prototype.subtractMonth = function() {\n  this._date = this._date\n    .minus({ months: 1 })\n    .endOf('month')\n    .startOf('second');\n};\n\nCronDate.prototype.subtractDay = function() {\n  this._date = this._date\n    .minus({ days: 1 })\n    .endOf('day')\n    .startOf('second');\n};\n\nCronDate.prototype.subtractHour = function() {\n  var prev = this._date;\n  this._date = this._date\n    .minus({ hours: 1 })\n    .endOf('hour')\n    .startOf('second');\n  if (this._date >= prev) {\n    this._date = this._date.minus({ hours: 1 });\n  }\n};\n\nCronDate.prototype.subtractMinute = function() {\n  var prev = this._date;\n  this._date = this._date.minus({ minutes: 1 })\n    .endOf('minute')\n    .startOf('second');\n  if (this._date > prev) {\n    this._date = this._date.minus({ hours: 1 });\n  }\n};\n\nCronDate.prototype.subtractSecond = function() {\n  var prev = this._date;\n  this._date = this._date\n    .minus({ seconds: 1 })\n    .startOf('second');\n  if (this._date > prev) {\n    this._date = this._date.minus({ hours: 1 });\n  }\n};\n\nCronDate.prototype.getDate = function() {\n  return this._date.day;\n};\n\nCronDate.prototype.getFullYear = function() {\n  return this._date.year;\n};\n\nCronDate.prototype.getDay = function() {\n  var weekday = this._date.weekday;\n  return weekday == 7 ? 0 : weekday;\n};\n\nCronDate.prototype.getMonth = function() {\n  return this._date.month - 1;\n};\n\nCronDate.prototype.getHours = function() {\n  return this._date.hour;\n};\n\nCronDate.prototype.getMinutes = function() {\n  return this._date.minute;\n};\n\nCronDate.prototype.getSeconds = function() {\n  return this._date.second;\n};\n\nCronDate.prototype.getMilliseconds = function() {\n  return this._date.millisecond;\n};\n\nCronDate.prototype.getTime = function() {\n  return this._date.valueOf();\n};\n\nCronDate.prototype.getUTCDate = function() {\n  return this._getUTC().day;\n};\n\nCronDate.prototype.getUTCFullYear = function() {\n  return this._getUTC().year;\n};\n\nCronDate.prototype.getUTCDay = function() {\n  var weekday = this._getUTC().weekday;\n  return weekday == 7 ? 0 : weekday;\n};\n\nCronDate.prototype.getUTCMonth = function() {\n  return this._getUTC().month - 1;\n};\n\nCronDate.prototype.getUTCHours = function() {\n  return this._getUTC().hour;\n};\n\nCronDate.prototype.getUTCMinutes = function() {\n  return this._getUTC().minute;\n};\n\nCronDate.prototype.getUTCSeconds = function() {\n  return this._getUTC().second;\n};\n\nCronDate.prototype.toISOString = function() {\n  return this._date.toUTC().toISO();\n};\n\nCronDate.prototype.toJSON = function() {\n  return this._date.toJSON();\n};\n\nCronDate.prototype.setDate = function(d) {\n  this._date = this._date.set({ day: d });\n};\n\nCronDate.prototype.setFullYear = function(y) {\n  this._date = this._date.set({ year: y });\n};\n\nCronDate.prototype.setDay = function(d) {\n  this._date = this._date.set({ weekday: d });\n};\n\nCronDate.prototype.setMonth = function(m) {\n  this._date = this._date.set({ month: m + 1 });\n};\n\nCronDate.prototype.setHours = function(h) {\n  this._date = this._date.set({ hour: h });\n};\n\nCronDate.prototype.setMinutes = function(m) {\n  this._date = this._date.set({ minute: m });\n};\n\nCronDate.prototype.setSeconds = function(s) {\n  this._date = this._date.set({ second: s });\n};\n\nCronDate.prototype.setMilliseconds = function(s) {\n  this._date = this._date.set({ millisecond: s });\n};\n\nCronDate.prototype._getUTC = function() {\n  return this._date.toUTC();\n};\n\nCronDate.prototype.toString = function() {\n  return this.toDate().toString();\n};\n\nCronDate.prototype.toDate = function() {\n  return this._date.toJSDate();\n};\n\nCronDate.prototype.isLastDayOfMonth = function() {\n  //next day\n  var newDate = this._date.plus({ days: 1 }).startOf('day');\n  return this._date.month !== newDate.month;\n};\n\n/**\n * Returns true when the current weekday is the last occurrence of this weekday\n * for the present month.\n */\nCronDate.prototype.isLastWeekdayOfMonth = function() {\n  // Check this by adding 7 days to the current date and seeing if it's\n  // a different month\n  var newDate = this._date.plus({ days: 7 }).startOf('day');\n  return this._date.month !== newDate.month;\n};\n\nfunction CronDate (timestamp, tz) {\n  var dateOpts = { zone: tz };\n  if (!timestamp) {\n    this._date = luxon.DateTime.local();\n  } else if (timestamp instanceof CronDate) {\n    this._date = timestamp._date;\n  } else if (timestamp instanceof Date) {\n    this._date = luxon.DateTime.fromJSDate(timestamp, dateOpts);\n  } else if (typeof timestamp === 'number') {\n    this._date = luxon.DateTime.fromMillis(timestamp, dateOpts);\n  } else if (typeof timestamp === 'string') {\n    this._date = luxon.DateTime.fromISO(timestamp, dateOpts);\n    this._date.isValid || (this._date = luxon.DateTime.fromRFC2822(timestamp, dateOpts));\n    this._date.isValid || (this._date = luxon.DateTime.fromSQL(timestamp, dateOpts));\n    // RFC2822-like format without the required timezone offset (used in tests)\n    this._date.isValid || (this._date = luxon.DateTime.fromFormat(timestamp, 'EEE, d MMM yyyy HH:mm:ss', dateOpts));\n  }\n\n  if (!this._date || !this._date.isValid) {\n    throw new Error('CronDate: unhandled timestamp: ' + JSON.stringify(timestamp));\n  }\n  \n  if (tz && tz !== this._date.zoneName) {\n    this._date = this._date.setZone(tz);\n  }\n}\n\nmodule.exports = CronDate;\n", "'use strict';\n\nfunction buildRange(item) {\n  return {\n    start: item,\n    count: 1\n  };\n}\n\nfunction completeRangeWithItem(range, item) {\n  range.end = item;\n  range.step = item - range.start;\n  range.count = 2;\n}\n\nfunction finalizeCurrentRange(results, currentRange, currentItemRange) {\n  if (currentRange) {\n    // Two elements do not form a range so split them into 2 single elements\n    if (currentRange.count === 2) {\n      results.push(buildRange(currentRange.start));\n      results.push(buildRange(currentRange.end));\n    } else {\n      results.push(currentRange);\n    }\n  }\n  if (currentItemRange) {\n    results.push(currentItemRange);\n  }\n}\n\nfunction compactField(arr) {\n  var results = [];\n  var currentRange = undefined;\n\n  for (var i = 0; i < arr.length; i++) {\n    var currentItem = arr[i];\n    if (typeof currentItem !== 'number') {\n      // String elements can't form a range\n      finalizeCurrentRange(results, currentRange, buildRange(currentItem));\n      currentRange = undefined;\n    } else if (!currentRange) {\n      // Start a new range\n      currentRange = buildRange(currentItem);\n    } else if (currentRange.count === 1) {\n      // Guess that the current item starts a range\n      completeRangeWithItem(currentRange, currentItem);\n    } else {\n      if (currentRange.step === currentItem - currentRange.end) {\n        // We found another item that matches the current range\n        currentRange.count++;\n        currentRange.end = currentItem;\n      } else if (currentRange.count === 2) { // The current range can't be continued\n        // Break the first item of the current range into a single element, and try to start a new range with the second item\n        results.push(buildRange(currentRange.start));\n        currentRange = buildRange(currentRange.end);\n        completeRangeWithItem(currentRange, currentItem);\n      } else {\n        // Persist the current range and start a new one with current item\n        finalizeCurrentRange(results, currentRange);\n        currentRange = buildRange(currentItem);\n      }\n    }\n  }\n\n  finalizeCurrentRange(results, currentRange);\n\n  return results;\n}\n\nmodule.exports = compactField;\n", "'use strict';\n\nvar compactField = require('./field_compactor');\n\nfunction stringifyField(arr, min, max) {\n  var ranges = compactField(arr);\n  if (ranges.length === 1) {\n    var singleRange = ranges[0];\n    var step = singleRange.step;\n    if (step === 1 && singleRange.start === min && singleRange.end === max) {\n      return '*';\n    }\n    if (step !== 1 && singleRange.start === min && singleRange.end === max - step + 1) {\n      return '*/' + step;\n    }\n  }\n\n  var result = [];\n  for (var i = 0, l = ranges.length; i < l; ++i) {\n    var range = ranges[i];\n    if (range.count === 1) {\n      result.push(range.start);\n      continue;\n    }\n\n    var step = range.step;\n    if (range.step === 1) {\n      result.push(range.start + '-' + range.end);\n      continue;\n    }\n\n    var multiplier = range.start == 0 ? range.count - 1 : range.count;\n    if (range.step * multiplier > range.end) {\n      result = result.concat(\n         Array\n          .from({ length: range.end - range.start + 1 })\n          .map(function (_, index) {\n            var value = range.start + index;\n            if ((value - range.start) % range.step === 0) {\n              return value;\n            }\n            return null;\n          })\n          .filter(function (value) {\n            return value != null;\n          })\n      );\n    } else if (range.end === max - range.step + 1) {\n      result.push(range.start + '/' + range.step);\n    } else {\n      result.push(range.start + '-' + range.end + '/' + range.step);\n    }\n  }\n\n  return result.join(',');\n}\n\nmodule.exports = stringifyField;\n", "'use strict';\n\n// Load Date class extensions\nvar CronDate = require('./date');\n\nvar stringifyField = require('./field_stringify');\n\n/**\n * Cron iteration loop safety limit\n */\nvar LOOP_LIMIT = 10000;\n\n/**\n * Construct a new expression parser\n *\n * Options:\n *   currentDate: iterator start date\n *   endDate: iterator end date\n *\n * @constructor\n * @private\n * @param {Object} fields  Expression fields parsed values\n * @param {Object} options Parser options\n */\nfunction CronExpression (fields, options) {\n  this._options = options;\n  this._utc = options.utc || false;\n  this._tz = this._utc ? 'UTC' : options.tz;\n  this._currentDate = new CronDate(options.currentDate, this._tz);\n  this._startDate = options.startDate ? new CronDate(options.startDate, this._tz) : null;\n  this._endDate = options.endDate ? new CronDate(options.endDate, this._tz) : null;\n  this._isIterator = options.iterator || false;\n  this._hasIterated = false;\n  this._nthDayOfWeek = options.nthDayOfWeek || 0;\n  this.fields = CronExpression._freezeFields(fields);\n}\n\n/**\n * Field mappings\n * @type {Array}\n */\nCronExpression.map = [ 'second', 'minute', 'hour', 'dayOfMonth', 'month', 'dayOfWeek' ];\n\n/**\n * Prefined intervals\n * @type {Object}\n */\nCronExpression.predefined = {\n  '@yearly': '0 0 1 1 *',\n  '@monthly': '0 0 1 * *',\n  '@weekly': '0 0 * * 0',\n  '@daily': '0 0 * * *',\n  '@hourly': '0 * * * *'\n};\n\n/**\n * Fields constraints\n * @type {Array}\n */\nCronExpression.constraints = [\n  { min: 0, max: 59, chars: [] }, // Second\n  { min: 0, max: 59, chars: [] }, // Minute\n  { min: 0, max: 23, chars: [] }, // Hour\n  { min: 1, max: 31, chars: ['L'] }, // Day of month\n  { min: 1, max: 12, chars: [] }, // Month\n  { min: 0, max: 7, chars: ['L'] }, // Day of week\n];\n\n/**\n * Days in month\n * @type {number[]}\n */\nCronExpression.daysInMonth = [\n  31,\n  29,\n  31,\n  30,\n  31,\n  30,\n  31,\n  31,\n  30,\n  31,\n  30,\n  31\n];\n\n/**\n * Field aliases\n * @type {Object}\n */\nCronExpression.aliases = {\n  month: {\n    jan: 1,\n    feb: 2,\n    mar: 3,\n    apr: 4,\n    may: 5,\n    jun: 6,\n    jul: 7,\n    aug: 8,\n    sep: 9,\n    oct: 10,\n    nov: 11,\n    dec: 12\n  },\n\n  dayOfWeek: {\n    sun: 0,\n    mon: 1,\n    tue: 2,\n    wed: 3,\n    thu: 4,\n    fri: 5,\n    sat: 6\n  }\n};\n\n/**\n * Field defaults\n * @type {Array}\n */\nCronExpression.parseDefaults = [ '0', '*', '*', '*', '*', '*' ];\n\nCronExpression.standardValidCharacters = /^[,*\\d/-]+$/;\nCronExpression.dayOfWeekValidCharacters = /^[?,*\\dL#/-]+$/;\nCronExpression.dayOfMonthValidCharacters = /^[?,*\\dL/-]+$/;\nCronExpression.validCharacters = {\n  second: CronExpression.standardValidCharacters,\n  minute: CronExpression.standardValidCharacters,\n  hour: CronExpression.standardValidCharacters,\n  dayOfMonth: CronExpression.dayOfMonthValidCharacters,\n  month: CronExpression.standardValidCharacters,\n  dayOfWeek: CronExpression.dayOfWeekValidCharacters,\n};\n\nCronExpression._isValidConstraintChar = function _isValidConstraintChar(constraints, value) {\n  if (typeof value !== 'string') {\n    return false;\n  }\n\n  return constraints.chars.some(function(char) {\n    return value.indexOf(char) > -1;\n  });\n};\n\n/**\n * Parse input interval\n *\n * @param {String} field Field symbolic name\n * @param {String} value Field value\n * @param {Array} constraints Range upper and lower constraints\n * @return {Array} Sequence of sorted values\n * @private\n */\nCronExpression._parseField = function _parseField (field, value, constraints) {\n  // Replace aliases\n  switch (field) {\n    case 'month':\n    case 'dayOfWeek':\n      var aliases = CronExpression.aliases[field];\n\n      value = value.replace(/[a-z]{3}/gi, function(match) {\n        match = match.toLowerCase();\n\n        if (typeof aliases[match] !== 'undefined') {\n          return aliases[match];\n        } else {\n          throw new Error('Validation error, cannot resolve alias \"' + match + '\"');\n        }\n      });\n      break;\n  }\n\n  // Check for valid characters.\n  if (!(CronExpression.validCharacters[field].test(value))) {\n    throw new Error('Invalid characters, got value: ' + value);\n  }\n\n  // Replace '*' and '?'\n  if (value.indexOf('*') !== -1) {\n    value = value.replace(/\\*/g, constraints.min + '-' + constraints.max);\n  } else if (value.indexOf('?') !== -1) {\n    value = value.replace(/\\?/g, constraints.min + '-' + constraints.max);\n  }\n\n  //\n  // Inline parsing functions\n  //\n  // Parser path:\n  //  - parseSequence\n  //    - parseRepeat\n  //      - parseRange\n\n  /**\n   * Parse sequence\n   *\n   * @param {String} val\n   * @return {Array}\n   * @private\n   */\n  function parseSequence (val) {\n    var stack = [];\n\n    function handleResult (result) {\n      if (result instanceof Array) { // Make sequence linear\n        for (var i = 0, c = result.length; i < c; i++) {\n          var value = result[i];\n\n          if (CronExpression._isValidConstraintChar(constraints, value)) {\n            stack.push(value);\n            continue;\n          }\n          // Check constraints\n          if (typeof value !== 'number' || Number.isNaN(value) || value < constraints.min || value > constraints.max) {\n            throw new Error(\n                'Constraint error, got value ' + value + ' expected range ' +\n                constraints.min + '-' + constraints.max\n            );\n          }\n\n          stack.push(value);\n        }\n      } else { // Scalar value\n\n        if (CronExpression._isValidConstraintChar(constraints, result)) {\n          stack.push(result);\n          return;\n        }\n\n        var numResult = +result;\n\n        // Check constraints\n        if (Number.isNaN(numResult) || numResult < constraints.min || numResult > constraints.max) {\n          throw new Error(\n            'Constraint error, got value ' + result + ' expected range ' +\n            constraints.min + '-' + constraints.max\n          );\n        }\n\n        if (field === 'dayOfWeek') {\n          numResult = numResult % 7;\n        }\n\n        stack.push(numResult);\n      }\n    }\n\n    var atoms = val.split(',');\n    if (!atoms.every(function (atom) {\n      return atom.length > 0;\n    })) {\n      throw new Error('Invalid list value format');\n    }\n\n    if (atoms.length > 1) {\n      for (var i = 0, c = atoms.length; i < c; i++) {\n        handleResult(parseRepeat(atoms[i]));\n      }\n    } else {\n      handleResult(parseRepeat(val));\n    }\n\n    stack.sort(CronExpression._sortCompareFn);\n\n    return stack;\n  }\n\n  /**\n   * Parse repetition interval\n   *\n   * @param {String} val\n   * @return {Array}\n   */\n  function parseRepeat (val) {\n    var repeatInterval = 1;\n    var atoms = val.split('/');\n\n    if (atoms.length > 2) {\n      throw new Error('Invalid repeat: ' + val);\n    }\n\n    if (atoms.length > 1) {\n      if (atoms[0] == +atoms[0]) {\n        atoms = [atoms[0] + '-' + constraints.max, atoms[1]];\n      }\n      return parseRange(atoms[0], atoms[atoms.length - 1]);\n    }\n\n    return parseRange(val, repeatInterval);\n  }\n\n  /**\n   * Parse range\n   *\n   * @param {String} val\n   * @param {Number} repeatInterval Repetition interval\n   * @return {Array}\n   * @private\n   */\n  function parseRange (val, repeatInterval) {\n    var stack = [];\n    var atoms = val.split('-');\n\n    if (atoms.length > 1 ) {\n      // Invalid range, return value\n      if (atoms.length < 2) {\n        return +val;\n      }\n\n      if (!atoms[0].length) {\n        if (!atoms[1].length) {\n          throw new Error('Invalid range: ' + val);\n        }\n\n        return +val;\n      }\n\n      // Validate range\n      var min = +atoms[0];\n      var max = +atoms[1];\n\n      if (Number.isNaN(min) || Number.isNaN(max) ||\n          min < constraints.min || max > constraints.max) {\n        throw new Error(\n          'Constraint error, got range ' +\n          min + '-' + max +\n          ' expected range ' +\n          constraints.min + '-' + constraints.max\n        );\n      } else if (min > max) {\n        throw new Error('Invalid range: ' + val);\n      }\n\n      // Create range\n      var repeatIndex = +repeatInterval;\n\n      if (Number.isNaN(repeatIndex) || repeatIndex <= 0) {\n        throw new Error('Constraint error, cannot repeat at every ' + repeatIndex + ' time.');\n      }\n\n      // JS DOW is in range of 0-6 (SUN-SAT) but we also support 7 in the expression\n      // Handle case when range contains 7 instead of 0 and translate this value to 0\n      if (field === 'dayOfWeek' && max % 7 === 0) {\n        stack.push(0);\n      }\n\n      for (var index = min, count = max; index <= count; index++) {\n        var exists = stack.indexOf(index) !== -1;\n        if (!exists && repeatIndex > 0 && (repeatIndex % repeatInterval) === 0) {\n          repeatIndex = 1;\n          stack.push(index);\n        } else {\n          repeatIndex++;\n        }\n      }\n      return stack;\n    }\n\n    return Number.isNaN(+val) ? val : +val;\n  }\n\n  return parseSequence(value);\n};\n\nCronExpression._sortCompareFn = function(a, b) {\n  var aIsNumber = typeof a === 'number';\n  var bIsNumber = typeof b === 'number';\n\n  if (aIsNumber && bIsNumber) {\n    return a - b;\n  }\n\n  if (!aIsNumber && bIsNumber) {\n    return 1;\n  }\n\n  if (aIsNumber && !bIsNumber) {\n    return -1;\n  }\n\n  return a.localeCompare(b);\n};\n\nCronExpression._handleMaxDaysInMonth = function(mappedFields) {\n  // Filter out any day of month value that is larger than given month expects\n  if (mappedFields.month.length === 1) {\n    var daysInMonth = CronExpression.daysInMonth[mappedFields.month[0] - 1];\n\n    if (mappedFields.dayOfMonth[0] > daysInMonth) {\n      throw new Error('Invalid explicit day of month definition');\n    }\n\n    return mappedFields.dayOfMonth\n      .filter(function(dayOfMonth) {\n        return dayOfMonth === 'L' ? true : dayOfMonth <= daysInMonth;\n      })\n      .sort(CronExpression._sortCompareFn);\n  }\n};\n\nCronExpression._freezeFields = function(fields) {\n  for (var i = 0, c = CronExpression.map.length; i < c; ++i) {\n    var field = CronExpression.map[i]; // Field name\n    var value = fields[field];\n    fields[field] = Object.freeze(value);\n  }\n  return Object.freeze(fields);\n};\n\nCronExpression.prototype._applyTimezoneShift = function(currentDate, dateMathVerb, method) {\n  if ((method === 'Month') || (method === 'Day')) {\n    var prevTime = currentDate.getTime();\n    currentDate[dateMathVerb + method]();\n    var currTime = currentDate.getTime();\n    if (prevTime === currTime) {\n      // Jumped into a not existent date due to a DST transition\n      if ((currentDate.getMinutes() === 0) &&\n          (currentDate.getSeconds() === 0)) {\n        currentDate.addHour();\n      } else if ((currentDate.getMinutes() === 59) &&\n                 (currentDate.getSeconds() === 59)) {\n        currentDate.subtractHour();\n      }\n    }\n  } else {\n    var previousHour = currentDate.getHours();\n    currentDate[dateMathVerb + method]();\n    var currentHour = currentDate.getHours();\n    var diff = currentHour - previousHour;\n    if (diff === 2) {\n        // Starting DST\n        if (this.fields.hour.length !== 24) {\n          // Hour is specified\n          this._dstStart = currentHour;\n        }\n      } else if ((diff === 0) &&\n                 (currentDate.getMinutes() === 0) &&\n                 (currentDate.getSeconds() === 0)) {\n        // Ending DST\n        if (this.fields.hour.length !== 24) {\n          // Hour is specified\n          this._dstEnd = currentHour;\n        }\n      }\n  }\n};\n\n\n/**\n * Find next or previous matching schedule date\n *\n * @return {CronDate}\n * @private\n */\nCronExpression.prototype._findSchedule = function _findSchedule (reverse) {\n\n  /**\n   * Match field value\n   *\n   * @param {String} value\n   * @param {Array} sequence\n   * @return {Boolean}\n   * @private\n   */\n  function matchSchedule (value, sequence) {\n    for (var i = 0, c = sequence.length; i < c; i++) {\n      if (sequence[i] >= value) {\n        return sequence[i] === value;\n      }\n    }\n\n    return sequence[0] === value;\n  }\n\n  /**\n   * Helps determine if the provided date is the correct nth occurence of the\n   * desired day of week.\n   *\n   * @param {CronDate} date\n   * @param {Number} nthDayOfWeek\n   * @return {Boolean}\n   * @private\n   */\n  function isNthDayMatch(date, nthDayOfWeek) {\n    if (nthDayOfWeek < 6) {\n      if (\n        date.getDate() < 8 &&\n        nthDayOfWeek === 1 // First occurence has to happen in first 7 days of the month\n      ) {\n        return true;\n      }\n\n      var offset = date.getDate() % 7 ? 1 : 0; // Math is off by 1 when dayOfWeek isn't divisible by 7\n      var adjustedDate = date.getDate() - (date.getDate() % 7); // find the first occurance\n      var occurrence = Math.floor(adjustedDate / 7) + offset;\n\n      return occurrence === nthDayOfWeek;\n    }\n\n    return false;\n  }\n\n  /**\n   * Helper function that checks if 'L' is in the array\n   *\n   * @param {Array} expressions\n   */\n  function isLInExpressions(expressions) {\n    return expressions.length > 0 && expressions.some(function(expression) {\n      return typeof expression === 'string' && expression.indexOf('L') >= 0;\n    });\n  }\n\n\n  // Whether to use backwards directionality when searching\n  reverse = reverse || false;\n  var dateMathVerb = reverse ? 'subtract' : 'add';\n\n  var currentDate = new CronDate(this._currentDate, this._tz);\n  var startDate = this._startDate;\n  var endDate = this._endDate;\n\n  // Find matching schedule\n  var startTimestamp = currentDate.getTime();\n  var stepCount = 0;\n\n  function isLastWeekdayOfMonthMatch(expressions) {\n    return expressions.some(function(expression) {\n      // There might be multiple expressions and not all of them will contain\n      // the \"L\".\n      if (!isLInExpressions([expression])) {\n        return false;\n      }\n\n      // The first character represents the weekday\n      var weekday = Number.parseInt(expression[0]) % 7;\n\n      if (Number.isNaN(weekday)) {\n        throw new Error('Invalid last weekday of the month expression: ' + expression);\n      }\n\n      return currentDate.getDay() === weekday && currentDate.isLastWeekdayOfMonth();\n    });\n  }\n\n  while (stepCount < LOOP_LIMIT) {\n    stepCount++;\n\n    // Validate timespan\n    if (reverse) {\n      if (startDate && (currentDate.getTime() - startDate.getTime() < 0)) {\n        throw new Error('Out of the timespan range');\n      }\n    } else {\n      if (endDate && (endDate.getTime() - currentDate.getTime()) < 0) {\n        throw new Error('Out of the timespan range');\n      }\n    }\n\n    // Day of month and week matching:\n    //\n    // \"The day of a command's execution can be specified by two fields --\n    // day of month, and day of week.  If  both\t fields\t are  restricted  (ie,\n    // aren't  *),  the command will be run when either field matches the cur-\n    // rent time.  For example, \"30 4 1,15 * 5\" would cause a command to be\n    // run at 4:30 am on the  1st and 15th of each month, plus every Friday.\"\n    //\n    // http://unixhelp.ed.ac.uk/CGI/man-cgi?crontab+5\n    //\n\n    var dayOfMonthMatch = matchSchedule(currentDate.getDate(), this.fields.dayOfMonth);\n    if (isLInExpressions(this.fields.dayOfMonth)) {\n      dayOfMonthMatch = dayOfMonthMatch || currentDate.isLastDayOfMonth();\n    }\n    var dayOfWeekMatch = matchSchedule(currentDate.getDay(), this.fields.dayOfWeek);\n    if (isLInExpressions(this.fields.dayOfWeek)) {\n      dayOfWeekMatch = dayOfWeekMatch || isLastWeekdayOfMonthMatch(this.fields.dayOfWeek);\n    }\n    var isDayOfMonthWildcardMatch = this.fields.dayOfMonth.length >= CronExpression.daysInMonth[currentDate.getMonth()];\n    var isDayOfWeekWildcardMatch = this.fields.dayOfWeek.length === CronExpression.constraints[5].max - CronExpression.constraints[5].min + 1;\n    var currentHour = currentDate.getHours();\n\n    // Add or subtract day if select day not match with month (according to calendar)\n    if (!dayOfMonthMatch && (!dayOfWeekMatch || isDayOfWeekWildcardMatch)) {\n      this._applyTimezoneShift(currentDate, dateMathVerb, 'Day');\n      continue;\n    }\n\n    // Add or subtract day if not day of month is set (and no match) and day of week is wildcard\n    if (!isDayOfMonthWildcardMatch && isDayOfWeekWildcardMatch && !dayOfMonthMatch) {\n      this._applyTimezoneShift(currentDate, dateMathVerb, 'Day');\n      continue;\n    }\n\n    // Add or subtract day if not day of week is set (and no match) and day of month is wildcard\n    if (isDayOfMonthWildcardMatch && !isDayOfWeekWildcardMatch && !dayOfWeekMatch) {\n      this._applyTimezoneShift(currentDate, dateMathVerb, 'Day');\n      continue;\n    }\n\n    // Add or subtract day if day of week & nthDayOfWeek are set (and no match)\n    if (\n      this._nthDayOfWeek > 0 &&\n      !isNthDayMatch(currentDate, this._nthDayOfWeek)\n    ) {\n      this._applyTimezoneShift(currentDate, dateMathVerb, 'Day');\n      continue;\n    }\n\n    // Match month\n    if (!matchSchedule(currentDate.getMonth() + 1, this.fields.month)) {\n      this._applyTimezoneShift(currentDate, dateMathVerb, 'Month');\n      continue;\n    }\n\n    // Match hour\n    if (!matchSchedule(currentHour, this.fields.hour)) {\n      if (this._dstStart !== currentHour) {\n        this._dstStart = null;\n        this._applyTimezoneShift(currentDate, dateMathVerb, 'Hour');\n        continue;\n      } else if (!matchSchedule(currentHour - 1, this.fields.hour)) {\n        currentDate[dateMathVerb + 'Hour']();\n        continue;\n      }\n    } else if (this._dstEnd === currentHour) {\n      if (!reverse) {\n        this._dstEnd = null;\n        this._applyTimezoneShift(currentDate, 'add', 'Hour');\n        continue;\n      }\n    }\n\n    // Match minute\n    if (!matchSchedule(currentDate.getMinutes(), this.fields.minute)) {\n      this._applyTimezoneShift(currentDate, dateMathVerb, 'Minute');\n      continue;\n    }\n\n    // Match second\n    if (!matchSchedule(currentDate.getSeconds(), this.fields.second)) {\n      this._applyTimezoneShift(currentDate, dateMathVerb, 'Second');\n      continue;\n    }\n\n    // Increase a second in case in the first iteration the currentDate was not\n    // modified\n    if (startTimestamp === currentDate.getTime()) {\n      if ((dateMathVerb === 'add') || (currentDate.getMilliseconds() === 0)) {\n        this._applyTimezoneShift(currentDate, dateMathVerb, 'Second');\n      } else {\n        currentDate.setMilliseconds(0);\n      }\n\n      continue;\n    }\n\n    break;\n  }\n\n  if (stepCount >= LOOP_LIMIT) {\n    throw new Error('Invalid expression, loop limit exceeded');\n  }\n\n  this._currentDate = new CronDate(currentDate, this._tz);\n  this._hasIterated = true;\n\n  return currentDate;\n};\n\n/**\n * Find next suitable date\n *\n * @public\n * @return {CronDate|Object}\n */\nCronExpression.prototype.next = function next () {\n  var schedule = this._findSchedule();\n\n  // Try to return ES6 compatible iterator\n  if (this._isIterator) {\n    return {\n      value: schedule,\n      done: !this.hasNext()\n    };\n  }\n\n  return schedule;\n};\n\n/**\n * Find previous suitable date\n *\n * @public\n * @return {CronDate|Object}\n */\nCronExpression.prototype.prev = function prev () {\n  var schedule = this._findSchedule(true);\n\n  // Try to return ES6 compatible iterator\n  if (this._isIterator) {\n    return {\n      value: schedule,\n      done: !this.hasPrev()\n    };\n  }\n\n  return schedule;\n};\n\n/**\n * Check if next suitable date exists\n *\n * @public\n * @return {Boolean}\n */\nCronExpression.prototype.hasNext = function() {\n  var current = this._currentDate;\n  var hasIterated = this._hasIterated;\n\n  try {\n    this._findSchedule();\n    return true;\n  } catch (err) {\n    return false;\n  } finally {\n    this._currentDate = current;\n    this._hasIterated = hasIterated;\n  }\n};\n\n/**\n * Check if previous suitable date exists\n *\n * @public\n * @return {Boolean}\n */\nCronExpression.prototype.hasPrev = function() {\n  var current = this._currentDate;\n  var hasIterated = this._hasIterated;\n\n  try {\n    this._findSchedule(true);\n    return true;\n  } catch (err) {\n    return false;\n  } finally {\n    this._currentDate = current;\n    this._hasIterated = hasIterated;\n  }\n};\n\n/**\n * Iterate over expression iterator\n *\n * @public\n * @param {Number} steps Numbers of steps to iterate\n * @param {Function} callback Optional callback\n * @return {Array} Array of the iterated results\n */\nCronExpression.prototype.iterate = function iterate (steps, callback) {\n  var dates = [];\n\n  if (steps >= 0) {\n    for (var i = 0, c = steps; i < c; i++) {\n      try {\n        var item = this.next();\n        dates.push(item);\n\n        // Fire the callback\n        if (callback) {\n          callback(item, i);\n        }\n      } catch (err) {\n        break;\n      }\n    }\n  } else {\n    for (var i = 0, c = steps; i > c; i--) {\n      try {\n        var item = this.prev();\n        dates.push(item);\n\n        // Fire the callback\n        if (callback) {\n          callback(item, i);\n        }\n      } catch (err) {\n        break;\n      }\n    }\n  }\n\n  return dates;\n};\n\n/**\n * Reset expression iterator state\n *\n * @public\n */\nCronExpression.prototype.reset = function reset (newDate) {\n  this._currentDate = new CronDate(newDate || this._options.currentDate);\n};\n\n/**\n * Stringify the expression\n *\n * @public\n * @param {Boolean} [includeSeconds] Should stringify seconds\n * @return {String}\n */\nCronExpression.prototype.stringify = function stringify(includeSeconds) {\n  var resultArr = [];\n  for (var i = includeSeconds ? 0 : 1, c = CronExpression.map.length; i < c; ++i) {\n    var field = CronExpression.map[i];\n    var value = this.fields[field];\n    var constraint = CronExpression.constraints[i];\n\n    if (field === 'dayOfMonth' && this.fields.month.length === 1) {\n      constraint = { min: 1, max: CronExpression.daysInMonth[this.fields.month[0] - 1] };\n    } else if (field === 'dayOfWeek') {\n      // Prefer 0-6 range when serializing day of week field\n      constraint = { min: 0, max: 6 };\n      value = value[value.length - 1] === 7 ? value.slice(0, -1) : value;\n    }\n\n    resultArr.push(stringifyField(value, constraint.min, constraint.max));\n  }\n  return resultArr.join(' ');\n};\n\n/**\n * Parse input expression (async)\n *\n * @public\n * @param {String} expression Input expression\n * @param {Object} [options] Parsing options\n */\nCronExpression.parse = function parse(expression, options) {\n  var self = this;\n  if (typeof options === 'function') {\n    options = {};\n  }\n\n  function parse (expression, options) {\n    if (!options) {\n      options = {};\n    }\n\n    if (typeof options.currentDate === 'undefined') {\n      options.currentDate = new CronDate(undefined, self._tz);\n    }\n\n    // Is input expression predefined?\n    if (CronExpression.predefined[expression]) {\n      expression = CronExpression.predefined[expression];\n    }\n\n    // Split fields\n    var fields = [];\n    var atoms = (expression + '').trim().split(/\\s+/);\n\n    if (atoms.length > 6) {\n      throw new Error('Invalid cron expression');\n    }\n\n    // Resolve fields\n    var start = (CronExpression.map.length - atoms.length);\n    for (var i = 0, c = CronExpression.map.length; i < c; ++i) {\n      var field = CronExpression.map[i]; // Field name\n      var value = atoms[atoms.length > c ? i : i - start]; // Field value\n\n      if (i < start || !value) { // Use default value\n        fields.push(CronExpression._parseField(\n          field,\n          CronExpression.parseDefaults[i],\n          CronExpression.constraints[i]\n          )\n        );\n      } else {\n        var val = field === 'dayOfWeek' ? parseNthDay(value) : value;\n\n        fields.push(CronExpression._parseField(\n          field,\n          val,\n          CronExpression.constraints[i]\n          )\n        );\n      }\n    }\n\n    var mappedFields = {};\n    for (var i = 0, c = CronExpression.map.length; i < c; i++) {\n      var key = CronExpression.map[i];\n      mappedFields[key] = fields[i];\n    }\n\n    var dayOfMonth = CronExpression._handleMaxDaysInMonth(mappedFields);\n    mappedFields.dayOfMonth = dayOfMonth || mappedFields.dayOfMonth;\n    return new CronExpression(mappedFields, options);\n\n    /**\n     * Parses out the # special character for the dayOfWeek field & adds it to options.\n     *\n     * @param {String} val\n     * @return {String}\n     * @private\n     */\n    function parseNthDay(val) {\n      var atoms = val.split('#');\n      if (atoms.length > 1) {\n        var nthValue = +atoms[atoms.length - 1];\n        if(/,/.test(val)) {\n          throw new Error('Constraint error, invalid dayOfWeek `#` and `,` '\n            + 'special characters are incompatible');\n        }\n        if(/\\//.test(val)) {\n          throw new Error('Constraint error, invalid dayOfWeek `#` and `/` '\n            + 'special characters are incompatible');\n        }\n        if(/-/.test(val)) {\n          throw new Error('Constraint error, invalid dayOfWeek `#` and `-` '\n            + 'special characters are incompatible');\n        }\n        if (atoms.length > 2 || Number.isNaN(nthValue) || (nthValue < 1 || nthValue > 5)) {\n          throw new Error('Constraint error, invalid dayOfWeek occurrence number (#)');\n        }\n\n        options.nthDayOfWeek = nthValue;\n        return atoms[0];\n      }\n      return val;\n    }\n  }\n\n  return parse(expression, options);\n};\n\n/**\n * Convert cron fields back to Cron Expression\n *\n * @public\n * @param {Object} fields Input fields\n * @param {Object} [options] Parsing options\n * @return {Object}\n */\nCronExpression.fieldsToExpression = function fieldsToExpression(fields, options) {\n  function validateConstraints (field, values, constraints) {\n    if (!values) {\n      throw new Error('Validation error, Field ' + field + ' is missing');\n    }\n    if (values.length === 0) {\n      throw new Error('Validation error, Field ' + field + ' contains no values');\n    }\n    for (var i = 0, c = values.length; i < c; i++) {\n      var value = values[i];\n\n      if (CronExpression._isValidConstraintChar(constraints, value)) {\n        continue;\n      }\n\n      // Check constraints\n      if (typeof value !== 'number' || Number.isNaN(value) || value < constraints.min || value > constraints.max) {\n        throw new Error(\n          'Constraint error, got value ' + value + ' expected range ' +\n          constraints.min + '-' + constraints.max\n        );\n      }\n    }\n  }\n\n  var mappedFields = {};\n  for (var i = 0, c = CronExpression.map.length; i < c; ++i) {\n    var field = CronExpression.map[i]; // Field name\n    var values = fields[field];\n    validateConstraints(\n      field,\n      values,\n      CronExpression.constraints[i]\n    );\n    var copy = [];\n    var j = -1;\n    while (++j < values.length) {\n      copy[j] = values[j];\n    }\n    values = copy.sort(CronExpression._sortCompareFn)\n      .filter(function(item, pos, ary) {\n        return !pos || item !== ary[pos - 1];\n      });\n    if (values.length !== copy.length) {\n      throw new Error('Validation error, Field ' + field + ' contains duplicate values');\n    }\n    mappedFields[field] = values;\n  }\n  var dayOfMonth = CronExpression._handleMaxDaysInMonth(mappedFields);\n  mappedFields.dayOfMonth = dayOfMonth || mappedFields.dayOfMonth;\n  return new CronExpression(mappedFields, options || {});\n};\n\nmodule.exports = CronExpression;\n", "module.exports = Object.create(new Proxy({}, {\n  get(_, key) {\n    if (\n      key !== '__esModule' &&\n      key !== '__proto__' &&\n      key !== 'constructor' &&\n      key !== 'splice'\n    ) {\n      console.warn(`Module \"fs\" has been externalized for browser compatibility. Cannot access \"fs.${key}\" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`)\n    }\n  }\n}))", "'use strict';\n\nvar CronExpression = require('./expression');\n\nfunction CronParser() {}\n\n/**\n * Parse crontab entry\n *\n * @private\n * @param {String} entry Crontab file entry/line\n */\nCronParser._parseEntry = function _parseEntry (entry) {\n  var atoms = entry.split(' ');\n\n  if (atoms.length === 6) {\n    return {\n      interval: CronExpression.parse(entry)\n    };\n  } else if (atoms.length > 6) {\n    return {\n      interval: CronExpression.parse(\n        atoms.slice(0, 6).join(' ')\n      ),\n      command: atoms.slice(6, atoms.length)\n    };\n  } else {\n    throw new Error('Invalid entry: ' + entry);\n  }\n};\n\n/**\n * Wrapper for CronExpression.parser method\n *\n * @public\n * @param {String} expression Input expression\n * @param {Object} [options] Parsing options\n * @return {Object}\n */\nCronParser.parseExpression = function parseExpression (expression, options) {\n  return CronExpression.parse(expression, options);\n};\n\n/**\n * Wrapper for CronExpression.fieldsToExpression method\n *\n * @public\n * @param {Object} fields Input fields\n * @param {Object} [options] Parsing options\n * @return {Object}\n */\nCronParser.fieldsToExpression = function fieldsToExpression (fields, options) {\n  return CronExpression.fieldsToExpression(fields, options);\n};\n\n/**\n * Parse content string\n *\n * @public\n * @param {String} data Crontab content\n * @return {Object}\n */\nCronParser.parseString = function parseString (data) {\n  var blocks = data.split('\\n');\n\n  var response = {\n    variables: {},\n    expressions: [],\n    errors: {}\n  };\n\n  for (var i = 0, c = blocks.length; i < c; i++) {\n    var block = blocks[i];\n    var matches = null;\n    var entry = block.trim(); // Remove surrounding spaces\n\n    if (entry.length > 0) {\n      if (entry.match(/^#/)) { // Comment\n        continue;\n      } else if ((matches = entry.match(/^(.*)=(.*)$/))) { // Variable\n        response.variables[matches[1]] = matches[2];\n      } else { // Expression?\n        var result = null;\n\n        try {\n          result = CronParser._parseEntry('0 ' + entry);\n          response.expressions.push(result.interval);\n        } catch (err) {\n          response.errors[entry] = err;\n        }\n      }\n    }\n  }\n\n  return response;\n};\n\n/**\n * Parse crontab file\n *\n * @public\n * @param {String} filePath Path to file\n * @param {Function} callback\n */\nCronParser.parseFile = function parseFile (filePath, callback) {\n  require('fs').readFile(filePath, function(err, data) {\n    if (err) {\n      callback(err);\n      return;\n    }\n\n    return callback(null, CronParser.parseString(data.toString()));\n  });\n};\n\nmodule.exports = CronParser;\n"], "mappings": ";;;;;;;;;AAKA,QAAMA,aAAN,cAAyBC,MAAM;IAAA;AAKxB,QAAMC,uBAAN,cAAmCF,WAAW;MACnDG,YAAYC,QAAQ;AAClB,cAAO,qBAAoBA,OAAOC,UAAS,CAAG,EAAC;MACjD;IACF;AAKO,QAAMC,uBAAN,cAAmCN,WAAW;MACnDG,YAAYC,QAAQ;AAClB,cAAO,qBAAoBA,OAAOC,UAAS,CAAG,EAAC;MACjD;IACF;AAKO,QAAME,uBAAN,cAAmCP,WAAW;MACnDG,YAAYC,QAAQ;AAClB,cAAO,qBAAoBA,OAAOC,UAAS,CAAG,EAAC;MACjD;IACF;AAKO,QAAMG,gCAAN,cAA4CR,WAAW;IAAA;AAKvD,QAAMS,mBAAN,cAA+BT,WAAW;MAC/CG,YAAYO,MAAM;AAChB,cAAO,gBAAeA,IAAK,EAAC;MAC9B;IACF;AAKO,QAAMC,uBAAN,cAAmCX,WAAW;IAAA;AAK9C,QAAMY,sBAAN,cAAkCZ,WAAW;MAClDG,cAAc;AACZ,cAAM,2BAA2B;MACnC;IACF;ACxDA,QAAMU,IAAI;AAAV,QACEC,IAAI;AADN,QAEEC,IAAI;AAEC,QAAMC,aAAa;MACxBC,MAAMJ;MACNK,OAAOL;MACPM,KAAKN;IACP;AAEO,QAAMO,WAAW;MACtBH,MAAMJ;MACNK,OAAOJ;MACPK,KAAKN;IACP;AAEO,QAAMQ,wBAAwB;MACnCJ,MAAMJ;MACNK,OAAOJ;MACPK,KAAKN;MACLS,SAASR;IACX;AAEO,QAAMS,YAAY;MACvBN,MAAMJ;MACNK,OAAOH;MACPI,KAAKN;IACP;AAEO,QAAMW,YAAY;MACvBP,MAAMJ;MACNK,OAAOH;MACPI,KAAKN;MACLS,SAASP;IACX;AAEO,QAAMU,cAAc;MACzBC,MAAMb;MACNc,QAAQd;IACV;AAEO,QAAMe,oBAAoB;MAC/BF,MAAMb;MACNc,QAAQd;MACRgB,QAAQhB;IACV;AAEO,QAAMiB,yBAAyB;MACpCJ,MAAMb;MACNc,QAAQd;MACRgB,QAAQhB;MACRkB,cAAcjB;IAChB;AAEO,QAAMkB,wBAAwB;MACnCN,MAAMb;MACNc,QAAQd;MACRgB,QAAQhB;MACRkB,cAAchB;IAChB;AAEO,QAAMkB,iBAAiB;MAC5BP,MAAMb;MACNc,QAAQd;MACRqB,WAAW;IACb;AAEO,QAAMC,uBAAuB;MAClCT,MAAMb;MACNc,QAAQd;MACRgB,QAAQhB;MACRqB,WAAW;IACb;AAEO,QAAME,4BAA4B;MACvCV,MAAMb;MACNc,QAAQd;MACRgB,QAAQhB;MACRqB,WAAW;MACXH,cAAcjB;IAChB;AAEO,QAAMuB,2BAA2B;MACtCX,MAAMb;MACNc,QAAQd;MACRgB,QAAQhB;MACRqB,WAAW;MACXH,cAAchB;IAChB;AAEO,QAAMuB,iBAAiB;MAC5BrB,MAAMJ;MACNK,OAAOL;MACPM,KAAKN;MACLa,MAAMb;MACNc,QAAQd;IACV;AAEO,QAAM0B,8BAA8B;MACzCtB,MAAMJ;MACNK,OAAOL;MACPM,KAAKN;MACLa,MAAMb;MACNc,QAAQd;MACRgB,QAAQhB;IACV;AAEO,QAAM2B,eAAe;MAC1BvB,MAAMJ;MACNK,OAAOJ;MACPK,KAAKN;MACLa,MAAMb;MACNc,QAAQd;IACV;AAEO,QAAM4B,4BAA4B;MACvCxB,MAAMJ;MACNK,OAAOJ;MACPK,KAAKN;MACLa,MAAMb;MACNc,QAAQd;MACRgB,QAAQhB;IACV;AAEO,QAAM6B,4BAA4B;MACvCzB,MAAMJ;MACNK,OAAOJ;MACPK,KAAKN;MACLS,SAASR;MACTY,MAAMb;MACNc,QAAQd;IACV;AAEO,QAAM8B,gBAAgB;MAC3B1B,MAAMJ;MACNK,OAAOH;MACPI,KAAKN;MACLa,MAAMb;MACNc,QAAQd;MACRkB,cAAcjB;IAChB;AAEO,QAAM8B,6BAA6B;MACxC3B,MAAMJ;MACNK,OAAOH;MACPI,KAAKN;MACLa,MAAMb;MACNc,QAAQd;MACRgB,QAAQhB;MACRkB,cAAcjB;IAChB;AAEO,QAAM+B,gBAAgB;MAC3B5B,MAAMJ;MACNK,OAAOH;MACPI,KAAKN;MACLS,SAASP;MACTW,MAAMb;MACNc,QAAQd;MACRkB,cAAchB;IAChB;AAEO,QAAM+B,6BAA6B;MACxC7B,MAAMJ;MACNK,OAAOH;MACPI,KAAKN;MACLS,SAASP;MACTW,MAAMb;MACNc,QAAQd;MACRgB,QAAQhB;MACRkB,cAAchB;IAChB;AC1Ke,QAAMgC,OAAN,MAAW;;;;;;MAMxB,IAAIC,OAAO;AACT,cAAM,IAAIpC,oBAAmB;MAC/B;;;;;;MAOA,IAAIqC,OAAO;AACT,cAAM,IAAIrC,oBAAmB;MAC/B;;;;;;;MAQA,IAAIsC,WAAW;AACb,eAAO,KAAKD;MACd;;;;;;MAOA,IAAIE,cAAc;AAChB,cAAM,IAAIvC,oBAAmB;MAC/B;;;;;;;;;;MAWAwC,WAAWC,IAAIC,MAAM;AACnB,cAAM,IAAI1C,oBAAmB;MAC/B;;;;;;;;;MAUA2C,aAAaF,IAAIG,QAAQ;AACvB,cAAM,IAAI5C,oBAAmB;MAC/B;;;;;;;MAQA6C,OAAOJ,IAAI;AACT,cAAM,IAAIzC,oBAAmB;MAC/B;;;;;;;MAQA8C,OAAOC,WAAW;AAChB,cAAM,IAAI/C,oBAAmB;MAC/B;;;;;;MAOA,IAAIgD,UAAU;AACZ,cAAM,IAAIhD,oBAAmB;MAC/B;IACF;AC7FA,QAAIiD,cAAY;AAMD,QAAMC,aAAN,MAAMA,oBAAmBf,KAAK;;;;;MAK3C,WAAWgB,WAAW;AACpB,YAAIF,gBAAc,MAAM;AACtBA,wBAAY,IAAIC,YAAU;QAC5B;AACA,eAAOD;MACT;;MAGA,IAAIb,OAAO;AACT,eAAO;MACT;;MAGA,IAAIC,OAAO;AACT,eAAO,IAAIe,KAAKC,eAAc,EAAGC,gBAAe,EAAGC;MACrD;;MAGA,IAAIhB,cAAc;AAChB,eAAO;MACT;;MAGAC,WAAWC,IAAI;QAAEG;QAAQY;MAAO,GAAG;AACjC,eAAOC,cAAchB,IAAIG,QAAQY,MAAM;MACzC;;MAGAb,aAAaF,IAAIG,QAAQ;AACvB,eAAOD,aAAa,KAAKE,OAAOJ,EAAE,GAAGG,MAAM;MAC7C;;MAGAC,OAAOJ,IAAI;AACT,eAAO,CAAC,IAAIiB,KAAKjB,EAAE,EAAEkB,kBAAiB;MACxC;;MAGAb,OAAOC,WAAW;AAChB,eAAOA,UAAUX,SAAS;MAC5B;;MAGA,IAAIY,UAAU;AACZ,eAAO;MACT;IACF;ACzDA,QAAMY,WAAW,oBAAIC,IAAG;AACxB,aAASC,QAAQC,UAAU;AACzB,UAAIC,MAAMJ,SAASK,IAAIF,QAAQ;AAC/B,UAAIC,QAAQE,QAAW;AACrBF,cAAM,IAAIZ,KAAKC,eAAe,SAAS;UACrCc,QAAQ;UACRZ,UAAUQ;UACV1D,MAAM;UACNC,OAAO;UACPC,KAAK;UACLO,MAAM;UACNC,QAAQ;UACRE,QAAQ;UACRmD,KAAK;QACP,CAAC;AACDR,iBAASS,IAAIN,UAAUC,GAAG;MAC5B;AACA,aAAOA;IACT;AAEA,QAAMM,YAAY;MAChBjE,MAAM;MACNC,OAAO;MACPC,KAAK;MACL6D,KAAK;MACLtD,MAAM;MACNC,QAAQ;MACRE,QAAQ;IACV;AAEA,aAASsD,YAAYP,KAAKQ,MAAM;AAC9B,YAAMC,YAAYT,IAAIpB,OAAO4B,IAAI,EAAEE,QAAQ,WAAW,EAAE,GACtDC,SAAS,kDAAkDC,KAAKH,SAAS,GACzE,CAAA,EAAGI,QAAQC,MAAMC,OAAOC,SAASC,OAAOC,SAASC,OAAO,IAAIR;AAC9D,aAAO,CAACI,OAAOF,QAAQC,MAAME,SAASC,OAAOC,SAASC,OAAO;IAC/D;AAEA,aAASC,YAAYpB,KAAKQ,MAAM;AAC9B,YAAMC,YAAYT,IAAIqB,cAAcb,IAAI;AACxC,YAAMc,SAAS,CAAA;AACf,eAASC,IAAI,GAAGA,IAAId,UAAUe,QAAQD,KAAK;AACzC,cAAM;UAAEnD;UAAMqD;QAAM,IAAIhB,UAAUc,CAAC;AACnC,cAAMG,MAAMpB,UAAUlC,IAAI;AAE1B,YAAIA,SAAS,OAAO;AAClBkD,iBAAOI,GAAG,IAAID;QAChB,WAAW,CAACE,YAAYD,GAAG,GAAG;AAC5BJ,iBAAOI,GAAG,IAAIE,SAASH,OAAO,EAAE;QAClC;MACF;AACA,aAAOH;IACT;AAEA,QAAMO,gBAAgB,oBAAIhC,IAAG;AAKd,QAAMiC,WAAN,MAAMA,kBAAiB3D,KAAK;;;;;MAKzC,OAAO4D,OAAO1D,MAAM;AAClB,YAAI2D,OAAOH,cAAc5B,IAAI5B,IAAI;AACjC,YAAI2D,SAAS9B,QAAW;AACtB2B,wBAAcxB,IAAIhC,MAAO2D,OAAO,IAAIF,UAASzD,IAAI,CAAE;QACrD;AACA,eAAO2D;MACT;;;;;MAMA,OAAOC,aAAa;AAClBJ,sBAAcK,MAAK;AACnBtC,iBAASsC,MAAK;MAChB;;;;;;;;;MAUA,OAAOC,iBAAiBjG,IAAG;AACzB,eAAO,KAAKkG,YAAYlG,EAAC;MAC3B;;;;;;;;;MAUA,OAAOkG,YAAYJ,MAAM;AACvB,YAAI,CAACA,MAAM;AACT,iBAAO;QACT;AACA,YAAI;AACF,cAAI5C,KAAKC,eAAe,SAAS;YAAEE,UAAUyC;UAAK,CAAC,EAAEpD,OAAM;AAC3D,iBAAO;iBACAyD,GAAG;AACV,iBAAO;QACT;MACF;MAEA9G,YAAY8C,MAAM;AAChB,cAAK;AAEL,aAAK0B,WAAW1B;AAEhB,aAAKiE,QAAQR,UAASM,YAAY/D,IAAI;MACxC;;;;;;MAOA,IAAID,OAAO;AACT,eAAO;MACT;;;;;;MAOA,IAAIC,OAAO;AACT,eAAO,KAAK0B;MACd;;;;;;;MAQA,IAAIxB,cAAc;AAChB,eAAO;MACT;;;;;;;;;;MAWAC,WAAWC,IAAI;QAAEG;QAAQY;MAAO,GAAG;AACjC,eAAOC,cAAchB,IAAIG,QAAQY,QAAQ,KAAKnB,IAAI;MACpD;;;;;;;;;MAUAM,aAAaF,IAAIG,QAAQ;AACvB,eAAOD,aAAa,KAAKE,OAAOJ,EAAE,GAAGG,MAAM;MAC7C;;;;;;;MAQAC,OAAOJ,IAAI;AACT,YAAI,CAAC,KAAK6D,MAAO,QAAOC;AACxB,cAAM/B,OAAO,IAAId,KAAKjB,EAAE;AAExB,YAAI+D,MAAMhC,IAAI,EAAG,QAAO+B;AAExB,cAAMvC,MAAMF,QAAQ,KAAKzB,IAAI;AAC7B,YAAI,CAAChC,MAAMC,OAAOC,KAAKkG,QAAQ3F,MAAMC,QAAQE,MAAM,IAAI+C,IAAIqB,gBACvDD,YAAYpB,KAAKQ,IAAI,IACrBD,YAAYP,KAAKQ,IAAI;AAEzB,YAAIiC,WAAW,MAAM;AACnBpG,iBAAO,CAACqG,KAAKC,IAAItG,IAAI,IAAI;QAC3B;AAGA,cAAMuG,eAAe9F,SAAS,KAAK,IAAIA;AAEvC,cAAM+F,QAAQC,aAAa;UACzBzG;UACAC;UACAC;UACAO,MAAM8F;UACN7F;UACAE;UACA8F,aAAa;QACf,CAAC;AAED,YAAIC,OAAO,CAACxC;AACZ,cAAMyC,OAAOD,OAAO;AACpBA,gBAAQC,QAAQ,IAAIA,OAAO,MAAOA;AAClC,gBAAQJ,QAAQG,SAAS,KAAK;MAChC;;;;;;;MAQAlE,OAAOC,WAAW;AAChB,eAAOA,UAAUX,SAAS,UAAUW,UAAUV,SAAS,KAAKA;MAC9D;;;;;;MAOA,IAAIW,UAAU;AACZ,eAAO,KAAKsD;MACd;IACF;AClOA,QAAIY,cAAc,CAAA;AAClB,aAASC,YAAYC,WAAW1E,OAAO,CAAA,GAAI;AACzC,YAAM2E,MAAMC,KAAKC,UAAU,CAACH,WAAW1E,IAAI,CAAC;AAC5C,UAAIsB,MAAMkD,YAAYG,GAAG;AACzB,UAAI,CAACrD,KAAK;AACRA,cAAM,IAAIZ,KAAKoE,WAAWJ,WAAW1E,IAAI;AACzCwE,oBAAYG,GAAG,IAAIrD;MACrB;AACA,aAAOA;IACT;AAEA,QAAMyD,cAAc,oBAAI5D,IAAG;AAC3B,aAAS6D,aAAaN,WAAW1E,OAAO,CAAA,GAAI;AAC1C,YAAM2E,MAAMC,KAAKC,UAAU,CAACH,WAAW1E,IAAI,CAAC;AAC5C,UAAIsB,MAAMyD,YAAYxD,IAAIoD,GAAG;AAC7B,UAAIrD,QAAQE,QAAW;AACrBF,cAAM,IAAIZ,KAAKC,eAAe+D,WAAW1E,IAAI;AAC7C+E,oBAAYpD,IAAIgD,KAAKrD,GAAG;MAC1B;AACA,aAAOA;IACT;AAEA,QAAM2D,eAAe,oBAAI9D,IAAG;AAC5B,aAAS+D,aAAaR,WAAW1E,OAAO,CAAA,GAAI;AAC1C,YAAM2E,MAAMC,KAAKC,UAAU,CAACH,WAAW1E,IAAI,CAAC;AAC5C,UAAImF,MAAMF,aAAa1D,IAAIoD,GAAG;AAC9B,UAAIQ,QAAQ3D,QAAW;AACrB2D,cAAM,IAAIzE,KAAK0E,aAAaV,WAAW1E,IAAI;AAC3CiF,qBAAatD,IAAIgD,KAAKQ,GAAG;MAC3B;AACA,aAAOA;IACT;AAEA,QAAME,eAAe,oBAAIlE,IAAG;AAC5B,aAASmE,aAAaZ,WAAW1E,OAAO,CAAA,GAAI;AAC1C,YAAM;QAAEuF;QAAM,GAAGC;UAAiBxF;AAClC,YAAM2E,MAAMC,KAAKC,UAAU,CAACH,WAAWc,YAAY,CAAC;AACpD,UAAIL,MAAME,aAAa9D,IAAIoD,GAAG;AAC9B,UAAIQ,QAAQ3D,QAAW;AACrB2D,cAAM,IAAIzE,KAAK+E,mBAAmBf,WAAW1E,IAAI;AACjDqF,qBAAa1D,IAAIgD,KAAKQ,GAAG;MAC3B;AACA,aAAOA;IACT;AAEA,QAAIO,iBAAiB;AACrB,aAASC,eAAe;AACtB,UAAID,gBAAgB;AAClB,eAAOA;MACT,OAAO;AACLA,yBAAiB,IAAIhF,KAAKC,eAAc,EAAGC,gBAAe,EAAGE;AAC7D,eAAO4E;MACT;IACF;AAEA,QAAME,2BAA2B,oBAAIzE,IAAG;AACxC,aAAS0E,4BAA4BnB,WAAW;AAC9C,UAAI1E,OAAO4F,yBAAyBrE,IAAImD,SAAS;AACjD,UAAI1E,SAASwB,QAAW;AACtBxB,eAAO,IAAIU,KAAKC,eAAe+D,SAAS,EAAE9D,gBAAe;AACzDgF,iCAAyBjE,IAAI+C,WAAW1E,IAAI;MAC9C;AACA,aAAOA;IACT;AAEA,QAAM8F,gBAAgB,oBAAI3E,IAAG;AAC7B,aAAS4E,kBAAkBrB,WAAW;AACpC,UAAIsB,OAAOF,cAAcvE,IAAImD,SAAS;AACtC,UAAI,CAACsB,MAAM;AACT,cAAMlF,SAAS,IAAIJ,KAAKuF,OAAOvB,SAAS;AAExCsB,eAAO,iBAAiBlF,SAASA,OAAOoF,YAAW,IAAKpF,OAAOqF;AAE/D,YAAI,EAAE,iBAAiBH,OAAO;AAC5BA,iBAAO;YAAE,GAAGI;YAAsB,GAAGJ;;QACvC;AACAF,sBAAcnE,IAAI+C,WAAWsB,IAAI;MACnC;AACA,aAAOA;IACT;AAEA,aAASK,kBAAkBC,WAAW;AAYpC,YAAMC,SAASD,UAAUE,QAAQ,KAAK;AACtC,UAAID,WAAW,IAAI;AACjBD,oBAAYA,UAAUG,UAAU,GAAGF,MAAM;MAC3C;AAEA,YAAMG,SAASJ,UAAUE,QAAQ,KAAK;AACtC,UAAIE,WAAW,IAAI;AACjB,eAAO,CAACJ,SAAS;MACnB,OAAO;AACL,YAAIK;AACJ,YAAIC;AACJ,YAAI;AACFD,oBAAU3B,aAAasB,SAAS,EAAE1F,gBAAe;AACjDgG,wBAAcN;iBACP3C,GAAG;AACV,gBAAMkD,UAAUP,UAAUG,UAAU,GAAGC,MAAM;AAC7CC,oBAAU3B,aAAa6B,OAAO,EAAEjG,gBAAe;AAC/CgG,wBAAcC;QAChB;AAEA,cAAM;UAAEC;UAAiBC;QAAS,IAAIJ;AACtC,eAAO,CAACC,aAAaE,iBAAiBC,QAAQ;MAChD;IACF;AAEA,aAASC,iBAAiBV,WAAWQ,iBAAiBG,gBAAgB;AACpE,UAAIA,kBAAkBH,iBAAiB;AACrC,YAAI,CAACR,UAAUY,SAAS,KAAK,GAAG;AAC9BZ,uBAAa;QACf;AAEA,YAAIW,gBAAgB;AAClBX,uBAAc,OAAMW,cAAe;QACrC;AAEA,YAAIH,iBAAiB;AACnBR,uBAAc,OAAMQ,eAAgB;QACtC;AACA,eAAOR;MACT,OAAO;AACL,eAAOA;MACT;IACF;AAEA,aAASa,UAAUC,GAAG;AACpB,YAAMC,KAAK,CAAA;AACX,eAASxE,IAAI,GAAGA,KAAK,IAAIA,KAAK;AAC5B,cAAMyE,KAAKC,SAASC,IAAI,MAAM3E,GAAG,CAAC;AAClCwE,WAAGI,KAAKL,EAAEE,EAAE,CAAC;MACf;AACA,aAAOD;IACT;AAEA,aAASK,YAAYN,GAAG;AACtB,YAAMC,KAAK,CAAA;AACX,eAASxE,IAAI,GAAGA,KAAK,GAAGA,KAAK;AAC3B,cAAMyE,KAAKC,SAASC,IAAI,MAAM,IAAI,KAAK3E,CAAC;AACxCwE,WAAGI,KAAKL,EAAEE,EAAE,CAAC;MACf;AACA,aAAOD;IACT;AAEA,aAASM,UAAUC,KAAK9E,QAAQ+E,WAAWC,QAAQ;AACjD,YAAMC,OAAOH,IAAII,YAAW;AAE5B,UAAID,SAAS,SAAS;AACpB,eAAO;MACT,WAAWA,SAAS,MAAM;AACxB,eAAOF,UAAU/E,MAAM;MACzB,OAAO;AACL,eAAOgF,OAAOhF,MAAM;MACtB;IACF;AAEA,aAASmF,oBAAoBL,KAAK;AAChC,UAAIA,IAAId,mBAAmBc,IAAId,oBAAoB,QAAQ;AACzD,eAAO;MACT,OAAO;AACL,eACEc,IAAId,oBAAoB,UACxB,CAACc,IAAI9G,UACL8G,IAAI9G,OAAOoH,WAAW,IAAI,KAC1BrC,4BAA4B+B,IAAI9G,MAAM,EAAEgG,oBAAoB;MAEhE;IACF;AAMA,QAAMqB,sBAAN,MAA0B;MACxBtL,YAAYuL,MAAMC,aAAarI,MAAM;AACnC,aAAKsI,QAAQtI,KAAKsI,SAAS;AAC3B,aAAKC,QAAQvI,KAAKuI,SAAS;AAE3B,cAAM;UAAED;UAAOC;UAAO,GAAGC;QAAU,IAAIxI;AAEvC,YAAI,CAACqI,eAAeI,OAAOC,KAAKF,SAAS,EAAE1F,SAAS,GAAG;AACrD,gBAAM6F,WAAW;YAAEC,aAAa;YAAO,GAAG5I;;AAC1C,cAAIA,KAAKsI,QAAQ,EAAGK,UAASE,uBAAuB7I,KAAKsI;AACzD,eAAKnD,MAAMD,aAAakD,MAAMO,QAAQ;QACxC;MACF;MAEAzI,OAAO2C,GAAG;AACR,YAAI,KAAKsC,KAAK;AACZ,gBAAM2D,QAAQ,KAAKP,QAAQvE,KAAKuE,MAAM1F,CAAC,IAAIA;AAC3C,iBAAO,KAAKsC,IAAIjF,OAAO4I,KAAK;QAC9B,OAAO;AAEL,gBAAMA,QAAQ,KAAKP,QAAQvE,KAAKuE,MAAM1F,CAAC,IAAIkG,QAAQlG,GAAG,CAAC;AACvD,iBAAOmG,SAASF,OAAO,KAAKR,KAAK;QACnC;MACF;IACF;AAMA,QAAMW,oBAAN,MAAwB;MACtBpM,YAAYyK,IAAIc,MAAMpI,MAAM;AAC1B,aAAKA,OAAOA;AACZ,aAAKkJ,eAAe1H;AAEpB,YAAI2H,IAAI3H;AACR,YAAI,KAAKxB,KAAKa,UAAU;AAEtB,eAAKyG,KAAKA;mBACDA,GAAGhE,KAAK5D,SAAS,SAAS;AAOnC,gBAAM0J,YAAY,MAAM9B,GAAGnH,SAAS;AACpC,gBAAMkJ,UAAUD,aAAa,IAAK,WAAUA,SAAU,KAAK,UAASA,SAAU;AAC9E,cAAI9B,GAAGnH,WAAW,KAAKiD,SAASC,OAAOgG,OAAO,EAAEzF,OAAO;AACrDuF,gBAAIE;AACJ,iBAAK/B,KAAKA;UACZ,OAAO;AAGL6B,gBAAI;AACJ,iBAAK7B,KAAKA,GAAGnH,WAAW,IAAImH,KAAKA,GAAGgC,QAAQ,KAAK,EAAEC,KAAK;cAAEC,SAASlC,GAAGnH;YAAO,CAAC;AAC9E,iBAAK+I,eAAe5B,GAAGhE;UACzB;mBACSgE,GAAGhE,KAAK5D,SAAS,UAAU;AACpC,eAAK4H,KAAKA;mBACDA,GAAGhE,KAAK5D,SAAS,QAAQ;AAClC,eAAK4H,KAAKA;AACV6B,cAAI7B,GAAGhE,KAAK3D;QACd,OAAO;AAGLwJ,cAAI;AACJ,eAAK7B,KAAKA,GAAGgC,QAAQ,KAAK,EAAEC,KAAK;YAAEC,SAASlC,GAAGnH;UAAO,CAAC;AACvD,eAAK+I,eAAe5B,GAAGhE;QACzB;AAEA,cAAMqF,WAAW;UAAE,GAAG,KAAK3I;;AAC3B2I,iBAAS9H,WAAW8H,SAAS9H,YAAYsI;AACzC,aAAK7H,MAAM0D,aAAaoD,MAAMO,QAAQ;MACxC;MAEAzI,SAAS;AACP,YAAI,KAAKgJ,cAAc;AAGrB,iBAAO,KAAKvG,cAAa,EACtB8G,IAAI,CAAC;YAAE1G;UAAM,MAAMA,KAAK,EACxB2G,KAAK,EAAE;QACZ;AACA,eAAO,KAAKpI,IAAIpB,OAAO,KAAKoH,GAAGqC,SAAQ,CAAE;MAC3C;MAEAhH,gBAAgB;AACd,cAAMiH,QAAQ,KAAKtI,IAAIqB,cAAc,KAAK2E,GAAGqC,SAAQ,CAAE;AACvD,YAAI,KAAKT,cAAc;AACrB,iBAAOU,MAAMH,IAAKI,UAAS;AACzB,gBAAIA,KAAKnK,SAAS,gBAAgB;AAChC,oBAAMI,aAAa,KAAKoJ,aAAapJ,WAAW,KAAKwH,GAAGvH,IAAI;gBAC1De,QAAQ,KAAKwG,GAAGxG;gBAChBZ,QAAQ,KAAKF,KAAKvB;cACpB,CAAC;AACD,qBAAO;gBACL,GAAGoL;gBACH9G,OAAOjD;;YAEX,OAAO;AACL,qBAAO+J;YACT;UACF,CAAC;QACH;AACA,eAAOD;MACT;MAEAhJ,kBAAkB;AAChB,eAAO,KAAKU,IAAIV,gBAAe;MACjC;IACF;AAKA,QAAMkJ,mBAAN,MAAuB;MACrBjN,YAAYuL,MAAM2B,WAAW/J,MAAM;AACjC,aAAKA,OAAO;UAAEgK,OAAO;UAAQ,GAAGhK;;AAChC,YAAI,CAAC+J,aAAaE,YAAW,GAAI;AAC/B,eAAKC,MAAM5E,aAAa8C,MAAMpI,IAAI;QACpC;MACF;MAEAE,OAAOiK,OAAO/M,MAAM;AAClB,YAAI,KAAK8M,KAAK;AACZ,iBAAO,KAAKA,IAAIhK,OAAOiK,OAAO/M,IAAI;QACpC,OAAO;AACL,iBAAOgN,mBAA2BhN,MAAM+M,OAAO,KAAKnK,KAAKqK,SAAS,KAAKrK,KAAKgK,UAAU,MAAM;QAC9F;MACF;MAEArH,cAAcwH,OAAO/M,MAAM;AACzB,YAAI,KAAK8M,KAAK;AACZ,iBAAO,KAAKA,IAAIvH,cAAcwH,OAAO/M,IAAI;QAC3C,OAAO;AACL,iBAAO,CAAA;QACT;MACF;IACF;AAEA,QAAMgJ,uBAAuB;MAC3BkE,UAAU;MACVC,aAAa;MACbC,SAAS,CAAC,GAAG,CAAC;IAChB;AAKe,QAAMvE,SAAN,MAAMA,QAAO;MAC1B,OAAOwE,SAASzK,MAAM;AACpB,eAAOiG,QAAO5C,OACZrD,KAAKc,QACLd,KAAK8G,iBACL9G,KAAKiH,gBACLjH,KAAK0K,cACL1K,KAAK2K,WACP;MACF;MAEA,OAAOtH,OAAOvC,QAAQgG,iBAAiBG,gBAAgByD,cAAcC,cAAc,OAAO;AACxF,cAAMC,kBAAkB9J,UAAU+J,SAASC;AAE3C,cAAMC,UAAUH,oBAAoBD,cAAc,UAAUhF,aAAY;AACxE,cAAMqF,mBAAmBlE,mBAAmB+D,SAASI;AACrD,cAAMC,kBAAkBjE,kBAAkB4D,SAASM;AACnD,cAAMC,gBAAgBC,qBAAqBX,YAAY,KAAKG,SAASS;AACrE,eAAO,IAAIrF,QAAO8E,SAASC,kBAAkBE,iBAAiBE,eAAeR,eAAe;MAC9F;MAEA,OAAOrH,aAAa;AAClBmC,yBAAiB;AACjBX,oBAAYvB,MAAK;AACjByB,qBAAazB,MAAK;AAClB6B,qBAAa7B,MAAK;AAClBoC,iCAAyBpC,MAAK;AAC9BsC,sBAActC,MAAK;MACrB;MAEA,OAAO+H,WAAW;QAAEzK;QAAQgG;QAAiBG;QAAgByD;UAAiB,CAAA,GAAI;AAChF,eAAOzE,QAAO5C,OAAOvC,QAAQgG,iBAAiBG,gBAAgByD,YAAY;MAC5E;MAEA7N,YAAYiE,QAAQ0K,WAAWvE,gBAAgByD,cAAcE,iBAAiB;AAC5E,cAAM,CAACa,cAAcC,uBAAuBC,oBAAoB,IAAItF,kBAAkBvF,MAAM;AAE5F,aAAKA,SAAS2K;AACd,aAAK3E,kBAAkB0E,aAAaE,yBAAyB;AAC7D,aAAKzE,iBAAiBA,kBAAkB0E,wBAAwB;AAChE,aAAKjB,eAAeA;AACpB,aAAKtC,OAAOpB,iBAAiB,KAAKlG,QAAQ,KAAKgG,iBAAiB,KAAKG,cAAc;AAEnF,aAAK2E,gBAAgB;UAAE1L,QAAQ,CAAA;UAAI2L,YAAY,CAAA;;AAC/C,aAAKC,cAAc;UAAE5L,QAAQ,CAAA;UAAI2L,YAAY,CAAA;;AAC7C,aAAKE,gBAAgB;AACrB,aAAKC,WAAW,CAAA;AAEhB,aAAKpB,kBAAkBA;AACvB,aAAKqB,oBAAoB;MAC3B;MAEA,IAAIC,cAAc;AAChB,YAAI,KAAKD,qBAAqB,MAAM;AAClC,eAAKA,oBAAoBhE,oBAAoB,IAAI;QACnD;AAEA,eAAO,KAAKgE;MACd;MAEAjE,cAAc;AACZ,cAAMmE,eAAe,KAAKpC,UAAS;AACnC,cAAMqC,kBACH,KAAKtF,oBAAoB,QAAQ,KAAKA,oBAAoB,YAC1D,KAAKG,mBAAmB,QAAQ,KAAKA,mBAAmB;AAC3D,eAAOkF,gBAAgBC,iBAAiB,OAAO;MACjD;MAEAC,MAAMC,MAAM;AACV,YAAI,CAACA,QAAQ7D,OAAO8D,oBAAoBD,IAAI,EAAExJ,WAAW,GAAG;AAC1D,iBAAO;QACT,OAAO;AACL,iBAAOmD,QAAO5C,OACZiJ,KAAKxL,UAAU,KAAK8J,iBACpB0B,KAAKxF,mBAAmB,KAAKA,iBAC7BwF,KAAKrF,kBAAkB,KAAKA,gBAC5BoE,qBAAqBiB,KAAK5B,YAAY,KAAK,KAAKA,cAChD4B,KAAK3B,eAAe,KACtB;QACF;MACF;MAEA6B,cAAcF,OAAO,CAAA,GAAI;AACvB,eAAO,KAAKD,MAAM;UAAE,GAAGC;UAAM3B,aAAa;QAAK,CAAC;MAClD;MAEA8B,kBAAkBH,OAAO,CAAA,GAAI;AAC3B,eAAO,KAAKD,MAAM;UAAE,GAAGC;UAAM3B,aAAa;QAAM,CAAC;MACnD;MAEA+B,OAAO5J,QAAQ5C,SAAS,OAAO;AAC7B,eAAOyH,UAAU,MAAM7E,QAAQsH,QAAgB,MAAM;AACnD,gBAAMhC,OAAOlI,SAAS;YAAEtC,OAAOkF;YAAQjF,KAAK;UAAU,IAAI;YAAED,OAAOkF;aACjE6J,YAAYzM,SAAS,WAAW;AAClC,cAAI,CAAC,KAAK4L,YAAYa,SAAS,EAAE7J,MAAM,GAAG;AACxC,iBAAKgJ,YAAYa,SAAS,EAAE7J,MAAM,IAAIqE,UAAWG,QAAO,KAAKsF,QAAQtF,IAAIc,MAAM,OAAO,CAAC;UACzF;AACA,iBAAO,KAAK0D,YAAYa,SAAS,EAAE7J,MAAM;QAC3C,CAAC;MACH;MAEA+J,SAAS/J,QAAQ5C,SAAS,OAAO;AAC/B,eAAOyH,UAAU,MAAM7E,QAAQsH,UAAkB,MAAM;AACrD,gBAAMhC,OAAOlI,SACP;YAAElC,SAAS8E;YAAQnF,MAAM;YAAWC,OAAO;YAAQC,KAAK;UAAU,IAClE;YAAEG,SAAS8E;aACf6J,YAAYzM,SAAS,WAAW;AAClC,cAAI,CAAC,KAAK0L,cAAce,SAAS,EAAE7J,MAAM,GAAG;AAC1C,iBAAK8I,cAAce,SAAS,EAAE7J,MAAM,IAAI4E,YAAaJ,QACnD,KAAKsF,QAAQtF,IAAIc,MAAM,SAAS,CAClC;UACF;AACA,iBAAO,KAAKwD,cAAce,SAAS,EAAE7J,MAAM;QAC7C,CAAC;MACH;MAEAgK,YAAY;AACV,eAAOnF,UACL,MACAnG,QACA,MAAM4I,WACN,MAAM;AAGJ,cAAI,CAAC,KAAK2B,eAAe;AACvB,kBAAM3D,OAAO;cAAEhK,MAAM;cAAWQ,WAAW;;AAC3C,iBAAKmN,gBAAgB,CAACxE,SAASC,IAAI,MAAM,IAAI,IAAI,CAAC,GAAGD,SAASC,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC,EAAEiC,IAClFnC,QAAO,KAAKsF,QAAQtF,IAAIc,MAAM,WAAW,CAC5C;UACF;AAEA,iBAAO,KAAK2D;QACd,CACF;MACF;MAEAgB,KAAKjK,QAAQ;AACX,eAAO6E,UAAU,MAAM7E,QAAQsH,MAAc,MAAM;AACjD,gBAAMhC,OAAO;YAAE1G,KAAKoB;;AAIpB,cAAI,CAAC,KAAKkJ,SAASlJ,MAAM,GAAG;AAC1B,iBAAKkJ,SAASlJ,MAAM,IAAI,CAACyE,SAASC,IAAI,KAAK,GAAG,CAAC,GAAGD,SAASC,IAAI,MAAM,GAAG,CAAC,CAAC,EAAEiC,IAAKnC,QAC/E,KAAKsF,QAAQtF,IAAIc,MAAM,KAAK,CAC9B;UACF;AAEA,iBAAO,KAAK4D,SAASlJ,MAAM;QAC7B,CAAC;MACH;MAEA8J,QAAQtF,IAAIqB,UAAUqE,OAAO;AAC3B,cAAMC,KAAK,KAAKC,YAAY5F,IAAIqB,QAAQ,GACtCwE,UAAUF,GAAGtK,cAAa,GAC1ByK,WAAWD,QAAQE,KAAMC,OAAMA,EAAE5N,KAAK6N,YAAW,MAAOP,KAAK;AAC/D,eAAOI,WAAWA,SAASrK,QAAQ;MACrC;MAEAyK,gBAAgBxN,OAAO,CAAA,GAAI;AAGzB,eAAO,IAAImI,oBAAoB,KAAKC,MAAMpI,KAAKqI,eAAe,KAAK6D,aAAalM,IAAI;MACtF;MAEAkN,YAAY5F,IAAIqB,WAAW,CAAA,GAAI;AAC7B,eAAO,IAAIM,kBAAkB3B,IAAI,KAAKc,MAAMO,QAAQ;MACtD;MAEA8E,aAAazN,OAAO,CAAA,GAAI;AACtB,eAAO,IAAI8J,iBAAiB,KAAK1B,MAAM,KAAK2B,UAAS,GAAI/J,IAAI;MAC/D;MAEA0N,cAAc1N,OAAO,CAAA,GAAI;AACvB,eAAOyE,YAAY,KAAK2D,MAAMpI,IAAI;MACpC;MAEA+J,YAAY;AACV,eACE,KAAKjJ,WAAW,QAChB,KAAKA,OAAOyM,YAAW,MAAO,WAC9B1H,4BAA4B,KAAKuC,IAAI,EAAEtH,OAAOoH,WAAW,OAAO;MAEpE;MAEAyF,kBAAkB;AAChB,YAAI,KAAKjD,cAAc;AACrB,iBAAO,KAAKA;QACd,WAAW,CAACkD,kBAAiB,GAAI;AAC/B,iBAAOxH;QACT,OAAO;AACL,iBAAOL,kBAAkB,KAAKjF,MAAM;QACtC;MACF;MAEA+M,iBAAiB;AACf,eAAO,KAAKF,gBAAe,EAAGrD;MAChC;MAEAwD,wBAAwB;AACtB,eAAO,KAAKH,gBAAe,EAAGpD;MAChC;MAEAwD,iBAAiB;AACf,eAAO,KAAKJ,gBAAe,EAAGnD;MAChC;MAEApK,OAAO4N,OAAO;AACZ,eACE,KAAKlN,WAAWkN,MAAMlN,UACtB,KAAKgG,oBAAoBkH,MAAMlH,mBAC/B,KAAKG,mBAAmB+G,MAAM/G;MAElC;MAEAgH,WAAW;AACT,eAAQ,UAAS,KAAKnN,MAAO,KAAI,KAAKgG,eAAgB,KAAI,KAAKG,cAAe;MAChF;IACF;AC7iBA,QAAI1G,YAAY;AAMD,QAAM2N,kBAAN,MAAMA,yBAAwBzO,KAAK;;;;;MAKhD,WAAW0O,cAAc;AACvB,YAAI5N,cAAc,MAAM;AACtBA,sBAAY,IAAI2N,iBAAgB,CAAC;QACnC;AACA,eAAO3N;MACT;;;;;;MAOA,OAAOE,SAASN,SAAQ;AACtB,eAAOA,YAAW,IAAI+N,iBAAgBC,cAAc,IAAID,iBAAgB/N,OAAM;MAChF;;;;;;;;;MAUA,OAAOiO,eAAe5Q,IAAG;AACvB,YAAIA,IAAG;AACL,gBAAM6Q,IAAI7Q,GAAE8Q,MAAM,uCAAuC;AACzD,cAAID,GAAG;AACL,mBAAO,IAAIH,iBAAgBK,aAAaF,EAAE,CAAC,GAAGA,EAAE,CAAC,CAAC,CAAC;UACrD;QACF;AACA,eAAO;MACT;MAEAxR,YAAYsD,SAAQ;AAClB,cAAK;AAEL,aAAK2I,QAAQ3I;MACf;;;;;;MAOA,IAAIT,OAAO;AACT,eAAO;MACT;;;;;;;MAQA,IAAIC,OAAO;AACT,eAAO,KAAKmJ,UAAU,IAAI,QAAS,MAAK7I,aAAa,KAAK6I,OAAO,QAAQ,CAAE;MAC7E;;;;;;;MAQA,IAAIlJ,WAAW;AACb,YAAI,KAAKkJ,UAAU,GAAG;AACpB,iBAAO;QACT,OAAO;AACL,iBAAQ,UAAS7I,aAAa,CAAC,KAAK6I,OAAO,QAAQ,CAAE;QACvD;MACF;;;;;;;MAQAhJ,aAAa;AACX,eAAO,KAAKH;MACd;;;;;;;;;MAUAM,aAAaF,IAAIG,QAAQ;AACvB,eAAOD,aAAa,KAAK6I,OAAO5I,MAAM;MACxC;;;;;;;MAQA,IAAIL,cAAc;AAChB,eAAO;MACT;;;;;;;;MASAM,SAAS;AACP,eAAO,KAAK2I;MACd;;;;;;;MAQA1I,OAAOC,WAAW;AAChB,eAAOA,UAAUX,SAAS,WAAWW,UAAUyI,UAAU,KAAKA;MAChE;;;;;;;MAQA,IAAIxI,UAAU;AACZ,eAAO;MACT;IACF;AC/Ie,QAAMkO,cAAN,cAA0B/O,KAAK;MAC5C5C,YAAYwE,UAAU;AACpB,cAAK;AAEL,aAAKA,WAAWA;MAClB;;MAGA,IAAI3B,OAAO;AACT,eAAO;MACT;;MAGA,IAAIC,OAAO;AACT,eAAO,KAAK0B;MACd;;MAGA,IAAIxB,cAAc;AAChB,eAAO;MACT;;MAGAC,aAAa;AACX,eAAO;MACT;;MAGAG,eAAe;AACb,eAAO;MACT;;MAGAE,SAAS;AACP,eAAO0D;MACT;;MAGAzD,SAAS;AACP,eAAO;MACT;;MAGA,IAAIE,UAAU;AACZ,eAAO;MACT;IACF;ACxCO,aAASmO,cAAcC,OAAOC,cAAa;AAEhD,UAAI1L,YAAYyL,KAAK,KAAKA,UAAU,MAAM;AACxC,eAAOC;MACT,WAAWD,iBAAiBjP,MAAM;AAChC,eAAOiP;MACT,WAAWE,SAASF,KAAK,GAAG;AAC1B,cAAMG,UAAUH,MAAMnB,YAAW;AACjC,YAAIsB,YAAY,UAAW,QAAOF;iBACzBE,YAAY,WAAWA,YAAY,SAAU,QAAOrO,WAAWC;iBAC/DoO,YAAY,SAASA,YAAY,MAAO,QAAOX,gBAAgBC;YACnE,QAAOD,gBAAgBE,eAAeS,OAAO,KAAKzL,SAASC,OAAOqL,KAAK;MAC9E,WAAWI,SAASJ,KAAK,GAAG;AAC1B,eAAOR,gBAAgBzN,SAASiO,KAAK;MACvC,WAAW,OAAOA,UAAU,YAAY,YAAYA,SAAS,OAAOA,MAAMvO,WAAW,YAAY;AAG/F,eAAOuO;MACT,OAAO;AACL,eAAO,IAAIF,YAAYE,KAAK;MAC9B;IACF;ACjCA,QAAMK,mBAAmB;MACvBC,MAAM;MACNC,SAAS;MACTC,MAAM;MACNC,MAAM;MACNC,MAAM;MACNC,UAAU;MACVC,MAAM;MACNC,SAAS;MACTC,MAAM;MACNC,MAAM;MACNC,MAAM;MACNC,MAAM;MACNC,MAAM;MACNC,MAAM;MACNC,MAAM;MACNC,MAAM;MACNC,SAAS;MACTC,MAAM;MACNC,MAAM;MACNC,MAAM;MACNC,MAAM;IACR;AAEA,QAAMC,wBAAwB;MAC5BrB,MAAM,CAAC,MAAM,IAAI;MACjBC,SAAS,CAAC,MAAM,IAAI;MACpBC,MAAM,CAAC,MAAM,IAAI;MACjBC,MAAM,CAAC,MAAM,IAAI;MACjBC,MAAM,CAAC,MAAM,IAAI;MACjBC,UAAU,CAAC,OAAO,KAAK;MACvBC,MAAM,CAAC,MAAM,IAAI;MACjBE,MAAM,CAAC,MAAM,IAAI;MACjBC,MAAM,CAAC,MAAM,IAAI;MACjBC,MAAM,CAAC,MAAM,IAAI;MACjBC,MAAM,CAAC,MAAM,IAAI;MACjBC,MAAM,CAAC,MAAM,IAAI;MACjBC,MAAM,CAAC,MAAM,IAAI;MACjBC,MAAM,CAAC,MAAM,IAAI;MACjBC,MAAM,CAAC,MAAM,IAAI;MACjBC,SAAS,CAAC,MAAM,IAAI;MACpBC,MAAM,CAAC,MAAM,IAAI;MACjBC,MAAM,CAAC,MAAM,IAAI;MACjBC,MAAM,CAAC,MAAM,IAAI;IACnB;AAEA,QAAMG,eAAevB,iBAAiBQ,QAAQvN,QAAQ,YAAY,EAAE,EAAEuO,MAAM,EAAE;AAEvE,aAASC,YAAYC,KAAK;AAC/B,UAAI1N,QAAQG,SAASuN,KAAK,EAAE;AAC5B,UAAI3M,MAAMf,KAAK,GAAG;AAChBA,gBAAQ;AACR,iBAASF,IAAI,GAAGA,IAAI4N,IAAI3N,QAAQD,KAAK;AACnC,gBAAM6N,OAAOD,IAAIE,WAAW9N,CAAC;AAE7B,cAAI4N,IAAI5N,CAAC,EAAE+N,OAAO7B,iBAAiBQ,OAAO,MAAM,IAAI;AAClDxM,qBAASuN,aAAa9J,QAAQiK,IAAI5N,CAAC,CAAC;UACtC,OAAO;AACL,uBAAW8B,OAAO0L,uBAAuB;AACvC,oBAAM,CAACQ,KAAKC,GAAG,IAAIT,sBAAsB1L,GAAG;AAC5C,kBAAI+L,QAAQG,OAAOH,QAAQI,KAAK;AAC9B/N,yBAAS2N,OAAOG;cAClB;YACF;UACF;QACF;AACA,eAAO3N,SAASH,OAAO,EAAE;MAC3B,OAAO;AACL,eAAOA;MACT;IACF;AAGA,QAAMgO,kBAAkB,oBAAI5P,IAAG;AACxB,aAAS6P,uBAAuB;AACrCD,sBAAgBvN,MAAK;IACvB;AAEO,aAASyN,WAAW;MAAEnK;IAAgB,GAAGoK,SAAS,IAAI;AAC3D,YAAMC,KAAKrK,mBAAmB;AAE9B,UAAIsK,cAAcL,gBAAgBxP,IAAI4P,EAAE;AACxC,UAAIC,gBAAgB5P,QAAW;AAC7B4P,sBAAc,oBAAIjQ,IAAG;AACrB4P,wBAAgBpP,IAAIwP,IAAIC,WAAW;MACrC;AACA,UAAIC,QAAQD,YAAY7P,IAAI2P,MAAM;AAClC,UAAIG,UAAU7P,QAAW;AACvB6P,gBAAQ,IAAIC,OAAQ,GAAEvC,iBAAiBoC,EAAE,CAAE,GAAED,MAAO,EAAC;AACrDE,oBAAYzP,IAAIuP,QAAQG,KAAK;MAC/B;AAEA,aAAOA;IACT;ACpFA,QAAIE,MAAMA,MAAMvQ,KAAKuQ,IAAG;AAAxB,QACE5C,cAAc;AADhB,QAEE7D,gBAAgB;AAFlB,QAGEG,yBAAyB;AAH3B,QAIEE,wBAAwB;AAJ1B,QAKEqG,qBAAqB;AALvB,QAMEC;AANF,QAOEnG,sBAAsB;AAKT,QAAMT,WAAN,MAAe;;;;;MAK5B,WAAW0G,MAAM;AACf,eAAOA;MACT;;;;;;;;MASA,WAAWA,IAAIhU,IAAG;AAChBgU,cAAMhU;MACR;;;;;;MAOA,WAAWoR,YAAYrL,MAAM;AAC3BqL,sBAAcrL;MAChB;;;;;;MAOA,WAAWqL,cAAc;AACvB,eAAOF,cAAcE,aAAanO,WAAWC,QAAQ;MACvD;;;;;MAMA,WAAWqK,gBAAgB;AACzB,eAAOA;MACT;;;;;MAMA,WAAWA,cAAchK,QAAQ;AAC/BgK,wBAAgBhK;MAClB;;;;;MAMA,WAAWmK,yBAAyB;AAClC,eAAOA;MACT;;;;;MAMA,WAAWA,uBAAuBnE,iBAAiB;AACjDmE,iCAAyBnE;MAC3B;;;;;MAMA,WAAWqE,wBAAwB;AACjC,eAAOA;MACT;;;;;MAMA,WAAWA,sBAAsBlE,gBAAgB;AAC/CkE,gCAAwBlE;MAC1B;;;;;;;;;;MAYA,WAAWqE,sBAAsB;AAC/B,eAAOA;MACT;;;;;;;;MASA,WAAWA,oBAAoBZ,cAAc;AAC3CY,8BAAsBD,qBAAqBX,YAAY;MACzD;;;;;MAMA,WAAW8G,qBAAqB;AAC9B,eAAOA;MACT;;;;;;;;;;MAWA,WAAWA,mBAAmBE,YAAY;AACxCF,6BAAqBE,aAAa;MACpC;;;;;MAMA,WAAWD,iBAAiB;AAC1B,eAAOA;MACT;;;;;MAMA,WAAWA,eAAeE,GAAG;AAC3BF,yBAAiBE;MACnB;;;;;MAMA,OAAOC,cAAc;AACnB3L,eAAO1C,WAAU;AACjBH,iBAASG,WAAU;AACnBgE,iBAAShE,WAAU;AACnByN,6BAAoB;MACtB;IACF;ACnLe,QAAMa,UAAN,MAAc;MAC3BhV,YAAYC,QAAQgV,aAAa;AAC/B,aAAKhV,SAASA;AACd,aAAKgV,cAAcA;MACrB;MAEA/U,YAAY;AACV,YAAI,KAAK+U,aAAa;AACpB,iBAAQ,GAAE,KAAKhV,MAAO,KAAI,KAAKgV,WAAY;QAC7C,OAAO;AACL,iBAAO,KAAKhV;QACd;MACF;IACF;ACAA,QAAMiV,gBAAgB,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAA5E,QACEC,aAAa,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAErE,aAASC,eAAe7U,MAAM2F,OAAO;AACnC,aAAO,IAAI8O,QACT,qBACC,iBAAgB9O,KAAM,aAAY,OAAOA,KAAM,UAAS3F,IAAK,oBAChE;IACF;AAEO,aAAS8U,UAAUvU,MAAMC,OAAOC,KAAK;AAC1C,YAAMsU,IAAI,IAAInR,KAAKA,KAAKoR,IAAIzU,MAAMC,QAAQ,GAAGC,GAAG,CAAC;AAEjD,UAAIF,OAAO,OAAOA,QAAQ,GAAG;AAC3BwU,UAAEE,eAAeF,EAAEG,eAAc,IAAK,IAAI;MAC5C;AAEA,YAAMC,KAAKJ,EAAEK,UAAS;AAEtB,aAAOD,OAAO,IAAI,IAAIA;IACxB;AAEA,aAASE,eAAe9U,MAAMC,OAAOC,KAAK;AACxC,aAAOA,OAAO6U,WAAW/U,IAAI,IAAIqU,aAAaD,eAAenU,QAAQ,CAAC;IACxE;AAEA,aAAS+U,iBAAiBhV,MAAMiV,SAAS;AACvC,YAAMC,QAAQH,WAAW/U,IAAI,IAAIqU,aAAaD,eAC5Ce,SAASD,MAAME,UAAWlQ,OAAMA,IAAI+P,OAAO,GAC3C/U,MAAM+U,UAAUC,MAAMC,MAAM;AAC9B,aAAO;QAAElV,OAAOkV,SAAS;QAAGjV;;IAC9B;AAEO,aAASmV,kBAAkBC,YAAYC,aAAa;AACzD,cAASD,aAAaC,cAAc,KAAK,IAAK;IAChD;AAMO,aAASC,gBAAgBC,SAASC,qBAAqB,GAAGH,cAAc,GAAG;AAChF,YAAM;QAAEvV;QAAMC;QAAOC;MAAI,IAAIuV,SAC3BR,UAAUH,eAAe9U,MAAMC,OAAOC,GAAG,GACzCG,UAAUgV,kBAAkBd,UAAUvU,MAAMC,OAAOC,GAAG,GAAGqV,WAAW;AAEtE,UAAII,aAAatP,KAAKuE,OAAOqK,UAAU5U,UAAU,KAAKqV,sBAAsB,CAAC,GAC3EE;AAEF,UAAID,aAAa,GAAG;AAClBC,mBAAW5V,OAAO;AAClB2V,qBAAaE,gBAAgBD,UAAUF,oBAAoBH,WAAW;MACxE,WAAWI,aAAaE,gBAAgB7V,MAAM0V,oBAAoBH,WAAW,GAAG;AAC9EK,mBAAW5V,OAAO;AAClB2V,qBAAa;MACf,OAAO;AACLC,mBAAW5V;MACb;AAEA,aAAO;QAAE4V;QAAUD;QAAYtV;QAAS,GAAGyV,WAAWL,OAAO;;IAC/D;AAEO,aAASM,gBAAgBC,UAAUN,qBAAqB,GAAGH,cAAc,GAAG;AACjF,YAAM;QAAEK;QAAUD;QAAYtV;MAAQ,IAAI2V,UACxCC,gBAAgBZ,kBAAkBd,UAAUqB,UAAU,GAAGF,kBAAkB,GAAGH,WAAW,GACzFW,aAAaC,WAAWP,QAAQ;AAElC,UAAIX,UAAUU,aAAa,IAAItV,UAAU4V,gBAAgB,IAAIP,oBAC3D1V;AAEF,UAAIiV,UAAU,GAAG;AACfjV,eAAO4V,WAAW;AAClBX,mBAAWkB,WAAWnW,IAAI;MAC5B,WAAWiV,UAAUiB,YAAY;AAC/BlW,eAAO4V,WAAW;AAClBX,mBAAWkB,WAAWP,QAAQ;MAChC,OAAO;AACL5V,eAAO4V;MACT;AAEA,YAAM;QAAE3V;QAAOC;MAAI,IAAI8U,iBAAiBhV,MAAMiV,OAAO;AACrD,aAAO;QAAEjV;QAAMC;QAAOC;QAAK,GAAG4V,WAAWE,QAAQ;;IACnD;AAEO,aAASI,mBAAmBC,UAAU;AAC3C,YAAM;QAAErW;QAAMC;QAAOC;MAAI,IAAImW;AAC7B,YAAMpB,UAAUH,eAAe9U,MAAMC,OAAOC,GAAG;AAC/C,aAAO;QAAEF;QAAMiV;QAAS,GAAGa,WAAWO,QAAQ;;IAChD;AAEO,aAASC,mBAAmBC,aAAa;AAC9C,YAAM;QAAEvW;QAAMiV;MAAQ,IAAIsB;AAC1B,YAAM;QAAEtW;QAAOC;MAAI,IAAI8U,iBAAiBhV,MAAMiV,OAAO;AACrD,aAAO;QAAEjV;QAAMC;QAAOC;QAAK,GAAG4V,WAAWS,WAAW;;IACtD;AAQO,aAASC,oBAAoBC,KAAKxM,KAAK;AAC5C,YAAMyM,oBACJ,CAACpR,YAAYmR,IAAIE,YAAY,KAC7B,CAACrR,YAAYmR,IAAIG,eAAe,KAChC,CAACtR,YAAYmR,IAAII,aAAa;AAChC,UAAIH,mBAAmB;AACrB,cAAMI,iBACJ,CAACxR,YAAYmR,IAAIpW,OAAO,KAAK,CAACiF,YAAYmR,IAAId,UAAU,KAAK,CAACrQ,YAAYmR,IAAIb,QAAQ;AAExF,YAAIkB,gBAAgB;AAClB,gBAAM,IAAIvX,8BACR,gEACF;QACF;AACA,YAAI,CAAC+F,YAAYmR,IAAIE,YAAY,EAAGF,KAAIpW,UAAUoW,IAAIE;AACtD,YAAI,CAACrR,YAAYmR,IAAIG,eAAe,EAAGH,KAAId,aAAac,IAAIG;AAC5D,YAAI,CAACtR,YAAYmR,IAAII,aAAa,EAAGJ,KAAIb,WAAWa,IAAII;AACxD,eAAOJ,IAAIE;AACX,eAAOF,IAAIG;AACX,eAAOH,IAAII;AACX,eAAO;UACLnB,oBAAoBzL,IAAIkG,sBAAqB;UAC7CoF,aAAatL,IAAIiG,eAAc;;MAEnC,OAAO;AACL,eAAO;UAAEwF,oBAAoB;UAAGH,aAAa;;MAC/C;IACF;AAEO,aAASwB,mBAAmBN,KAAKf,qBAAqB,GAAGH,cAAc,GAAG;AAC/E,YAAMyB,YAAYC,UAAUR,IAAIb,QAAQ,GACtCsB,YAAYC,eACVV,IAAId,YACJ,GACAE,gBAAgBY,IAAIb,UAAUF,oBAAoBH,WAAW,CAC/D,GACA6B,eAAeD,eAAeV,IAAIpW,SAAS,GAAG,CAAC;AAEjD,UAAI,CAAC2W,WAAW;AACd,eAAO1C,eAAe,YAAYmC,IAAIb,QAAQ;MAChD,WAAW,CAACsB,WAAW;AACrB,eAAO5C,eAAe,QAAQmC,IAAId,UAAU;MAC9C,WAAW,CAACyB,cAAc;AACxB,eAAO9C,eAAe,WAAWmC,IAAIpW,OAAO;YACvC,QAAO;IAChB;AAEO,aAASgX,sBAAsBZ,KAAK;AACzC,YAAMO,YAAYC,UAAUR,IAAIzW,IAAI,GAClCsX,eAAeH,eAAeV,IAAIxB,SAAS,GAAGkB,WAAWM,IAAIzW,IAAI,CAAC;AAEpE,UAAI,CAACgX,WAAW;AACd,eAAO1C,eAAe,QAAQmC,IAAIzW,IAAI;MACxC,WAAW,CAACsX,cAAc;AACxB,eAAOhD,eAAe,WAAWmC,IAAIxB,OAAO;YACvC,QAAO;IAChB;AAEO,aAASsC,wBAAwBd,KAAK;AAC3C,YAAMO,YAAYC,UAAUR,IAAIzW,IAAI,GAClCwX,aAAaL,eAAeV,IAAIxW,OAAO,GAAG,EAAE,GAC5CwX,WAAWN,eAAeV,IAAIvW,KAAK,GAAGwX,YAAYjB,IAAIzW,MAAMyW,IAAIxW,KAAK,CAAC;AAExE,UAAI,CAAC+W,WAAW;AACd,eAAO1C,eAAe,QAAQmC,IAAIzW,IAAI;MACxC,WAAW,CAACwX,YAAY;AACtB,eAAOlD,eAAe,SAASmC,IAAIxW,KAAK;MAC1C,WAAW,CAACwX,UAAU;AACpB,eAAOnD,eAAe,OAAOmC,IAAIvW,GAAG;YAC/B,QAAO;IAChB;AAEO,aAASyX,mBAAmBlB,KAAK;AACtC,YAAM;QAAEhW;QAAMC;QAAQE;QAAQ8F;MAAY,IAAI+P;AAC9C,YAAMmB,YACFT,eAAe1W,MAAM,GAAG,EAAE,KACzBA,SAAS,MAAMC,WAAW,KAAKE,WAAW,KAAK8F,gBAAgB,GAClEmR,cAAcV,eAAezW,QAAQ,GAAG,EAAE,GAC1CoX,cAAcX,eAAevW,QAAQ,GAAG,EAAE,GAC1CmX,mBAAmBZ,eAAezQ,aAAa,GAAG,GAAG;AAEvD,UAAI,CAACkR,WAAW;AACd,eAAOtD,eAAe,QAAQ7T,IAAI;MACpC,WAAW,CAACoX,aAAa;AACvB,eAAOvD,eAAe,UAAU5T,MAAM;MACxC,WAAW,CAACoX,aAAa;AACvB,eAAOxD,eAAe,UAAU1T,MAAM;MACxC,WAAW,CAACmX,kBAAkB;AAC5B,eAAOzD,eAAe,eAAe5N,WAAW;YAC3C,QAAO;IAChB;AC7LO,aAASpB,YAAY0S,GAAG;AAC7B,aAAO,OAAOA,MAAM;IACtB;AAEO,aAAS7G,SAAS6G,GAAG;AAC1B,aAAO,OAAOA,MAAM;IACtB;AAEO,aAASf,UAAUe,GAAG;AAC3B,aAAO,OAAOA,MAAM,YAAYA,IAAI,MAAM;IAC5C;AAEO,aAAS/G,SAAS+G,GAAG;AAC1B,aAAO,OAAOA,MAAM;IACtB;AAEO,aAASC,OAAOD,GAAG;AACxB,aAAOlN,OAAOoN,UAAU5H,SAAS6H,KAAKH,CAAC,MAAM;IAC/C;AAIO,aAAS1L,cAAc;AAC5B,UAAI;AACF,eAAO,OAAOvJ,SAAS,eAAe,CAAC,CAACA,KAAK+E;eACtC9B,GAAG;AACV,eAAO;MACT;IACF;AAEO,aAASiK,oBAAoB;AAClC,UAAI;AACF,eACE,OAAOlN,SAAS,eAChB,CAAC,CAACA,KAAKuF,WACN,cAAcvF,KAAKuF,OAAO4P,aAAa,iBAAiBnV,KAAKuF,OAAO4P;eAEhElS,GAAG;AACV,eAAO;MACT;IACF;AAIO,aAASoS,WAAWC,OAAO;AAChC,aAAOC,MAAMC,QAAQF,KAAK,IAAIA,QAAQ,CAACA,KAAK;IAC9C;AAEO,aAASG,OAAOC,KAAKC,IAAIC,SAAS;AACvC,UAAIF,IAAItT,WAAW,GAAG;AACpB,eAAOtB;MACT;AACA,aAAO4U,IAAIG,OAAO,CAACC,MAAMC,SAAS;AAChC,cAAMC,OAAO,CAACL,GAAGI,IAAI,GAAGA,IAAI;AAC5B,YAAI,CAACD,MAAM;AACT,iBAAOE;QACT,WAAWJ,QAAQE,KAAK,CAAC,GAAGE,KAAK,CAAC,CAAC,MAAMF,KAAK,CAAC,GAAG;AAChD,iBAAOA;QACT,OAAO;AACL,iBAAOE;QACT;MACF,GAAG,IAAI,EAAE,CAAC;IACZ;AAEO,aAASC,KAAKvC,KAAK1L,MAAM;AAC9B,aAAOA,KAAK6N,OAAO,CAACK,GAAGC,MAAM;AAC3BD,UAAEC,CAAC,IAAIzC,IAAIyC,CAAC;AACZ,eAAOD;SACN,CAAA,CAAE;IACP;AAEO,aAASE,eAAe1C,KAAK2C,MAAM;AACxC,aAAOtO,OAAOoN,UAAUiB,eAAehB,KAAK1B,KAAK2C,IAAI;IACvD;AAEO,aAAS1L,qBAAqB2L,UAAU;AAC7C,UAAIA,YAAY,MAAM;AACpB,eAAO;MACT,WAAW,OAAOA,aAAa,UAAU;AACvC,cAAM,IAAI3Z,qBAAqB,iCAAiC;MAClE,OAAO;AACL,YACE,CAACyX,eAAekC,SAAS1M,UAAU,GAAG,CAAC,KACvC,CAACwK,eAAekC,SAASzM,aAAa,GAAG,CAAC,KAC1C,CAAC0L,MAAMC,QAAQc,SAASxM,OAAO,KAC/BwM,SAASxM,QAAQyM,KAAMC,OAAM,CAACpC,eAAeoC,GAAG,GAAG,CAAC,CAAC,GACrD;AACA,gBAAM,IAAI7Z,qBAAqB,uBAAuB;QACxD;AACA,eAAO;UACLiN,UAAU0M,SAAS1M;UACnBC,aAAayM,SAASzM;UACtBC,SAASyL,MAAMkB,KAAKH,SAASxM,OAAO;;MAExC;IACF;AAIO,aAASsK,eAAekB,OAAOoB,QAAQC,KAAK;AACjD,aAAOzC,UAAUoB,KAAK,KAAKA,SAASoB,UAAUpB,SAASqB;IACzD;AAGO,aAASC,SAASC,GAAGha,IAAG;AAC7B,aAAOga,IAAIha,KAAIyG,KAAKuE,MAAMgP,IAAIha,EAAC;IACjC;AAEO,aAASyL,SAAS0F,OAAOnR,KAAI,GAAG;AACrC,YAAMia,QAAQ9I,QAAQ;AACtB,UAAI+I;AACJ,UAAID,OAAO;AACTC,iBAAS,OAAO,KAAK,CAAC/I,OAAO1F,SAASzL,IAAG,GAAG;MAC9C,OAAO;AACLka,kBAAU,KAAK/I,OAAO1F,SAASzL,IAAG,GAAG;MACvC;AACA,aAAOka;IACT;AAEO,aAASC,aAAaC,QAAQ;AACnC,UAAI1U,YAAY0U,MAAM,KAAKA,WAAW,QAAQA,WAAW,IAAI;AAC3D,eAAOnW;MACT,OAAO;AACL,eAAO0B,SAASyU,QAAQ,EAAE;MAC5B;IACF;AAEO,aAASC,cAAcD,QAAQ;AACpC,UAAI1U,YAAY0U,MAAM,KAAKA,WAAW,QAAQA,WAAW,IAAI;AAC3D,eAAOnW;MACT,OAAO;AACL,eAAOqW,WAAWF,MAAM;MAC1B;IACF;AAEO,aAASG,YAAYC,UAAU;AAEpC,UAAI9U,YAAY8U,QAAQ,KAAKA,aAAa,QAAQA,aAAa,IAAI;AACjE,eAAOvW;MACT,OAAO;AACL,cAAM4F,IAAIyQ,WAAW,OAAOE,QAAQ,IAAI;AACxC,eAAO/T,KAAKuE,MAAMnB,CAAC;MACrB;IACF;AAEO,aAAS2B,QAAQiP,QAAQC,QAAQC,aAAa,OAAO;AAC1D,YAAMC,SAAS,MAAMF,QACnBG,UAAUF,aAAalU,KAAKqU,QAAQrU,KAAKsU;AAC3C,aAAOF,QAAQJ,SAASG,MAAM,IAAIA;IACpC;AAIO,aAASzF,WAAW/U,MAAM;AAC/B,aAAOA,OAAO,MAAM,MAAMA,OAAO,QAAQ,KAAKA,OAAO,QAAQ;IAC/D;AAEO,aAASmW,WAAWnW,MAAM;AAC/B,aAAO+U,WAAW/U,IAAI,IAAI,MAAM;IAClC;AAEO,aAAS0X,YAAY1X,MAAMC,OAAO;AACvC,YAAM2a,WAAWjB,SAAS1Z,QAAQ,GAAG,EAAE,IAAI,GACzC4a,UAAU7a,QAAQC,QAAQ2a,YAAY;AAExC,UAAIA,aAAa,GAAG;AAClB,eAAO7F,WAAW8F,OAAO,IAAI,KAAK;MACpC,OAAO;AACL,eAAO,CAAC,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,EAAED,WAAW,CAAC;MACxE;IACF;AAGO,aAASnU,aAAagQ,KAAK;AAChC,UAAIjC,IAAInR,KAAKoR,IACXgC,IAAIzW,MACJyW,IAAIxW,QAAQ,GACZwW,IAAIvW,KACJuW,IAAIhW,MACJgW,IAAI/V,QACJ+V,IAAI7V,QACJ6V,IAAI/P,WACN;AAGA,UAAI+P,IAAIzW,OAAO,OAAOyW,IAAIzW,QAAQ,GAAG;AACnCwU,YAAI,IAAInR,KAAKmR,CAAC;AAIdA,UAAEE,eAAe+B,IAAIzW,MAAMyW,IAAIxW,QAAQ,GAAGwW,IAAIvW,GAAG;MACnD;AACA,aAAO,CAACsU;IACV;AAGA,aAASsG,gBAAgB9a,MAAM0V,oBAAoBH,aAAa;AAC9D,YAAMwF,QAAQ1F,kBAAkBd,UAAUvU,MAAM,GAAG0V,kBAAkB,GAAGH,WAAW;AACnF,aAAO,CAACwF,QAAQrF,qBAAqB;IACvC;AAEO,aAASG,gBAAgBD,UAAUF,qBAAqB,GAAGH,cAAc,GAAG;AACjF,YAAMyF,aAAaF,gBAAgBlF,UAAUF,oBAAoBH,WAAW;AAC5E,YAAM0F,iBAAiBH,gBAAgBlF,WAAW,GAAGF,oBAAoBH,WAAW;AACpF,cAAQY,WAAWP,QAAQ,IAAIoF,aAAaC,kBAAkB;IAChE;AAEO,aAASC,eAAelb,MAAM;AACnC,UAAIA,OAAO,IAAI;AACb,eAAOA;MACT,MAAO,QAAOA,OAAOkN,SAAS2G,qBAAqB,OAAO7T,OAAO,MAAOA;IAC1E;AAIO,aAASoD,cAAchB,IAAI+Y,cAAchY,QAAQD,WAAW,MAAM;AACvE,YAAMiB,OAAO,IAAId,KAAKjB,EAAE,GACtB4I,WAAW;QACT/J,WAAW;QACXjB,MAAM;QACNC,OAAO;QACPC,KAAK;QACLO,MAAM;QACNC,QAAQ;;AAGZ,UAAIwC,UAAU;AACZ8H,iBAAS9H,WAAWA;MACtB;AAEA,YAAMkY,WAAW;QAAEta,cAAcqa;QAAc,GAAGnQ;;AAElD,YAAM1G,SAAS,IAAIvB,KAAKC,eAAeG,QAAQiY,QAAQ,EACpDpW,cAAcb,IAAI,EAClBuL,KAAMC,OAAMA,EAAE5N,KAAK6N,YAAW,MAAO,cAAc;AACtD,aAAOtL,SAASA,OAAOc,QAAQ;IACjC;AAGO,aAASwL,aAAayK,YAAYC,cAAc;AACrD,UAAIC,UAAUhW,SAAS8V,YAAY,EAAE;AAGrC,UAAIG,OAAOrV,MAAMoV,OAAO,GAAG;AACzBA,kBAAU;MACZ;AAEA,YAAME,SAASlW,SAAS+V,cAAc,EAAE,KAAK,GAC3CI,eAAeH,UAAU,KAAKzQ,OAAO6Q,GAAGJ,SAAS,EAAE,IAAI,CAACE,SAASA;AACnE,aAAOF,UAAU,KAAKG;IACxB;AAIO,aAASE,SAASxW,OAAO;AAC9B,YAAMyW,eAAeL,OAAOpW,KAAK;AACjC,UAAI,OAAOA,UAAU,aAAaA,UAAU,MAAMoW,OAAOrV,MAAM0V,YAAY,EACzE,OAAM,IAAInc,qBAAsB,sBAAqB0F,KAAM,EAAC;AAC9D,aAAOyW;IACT;AAEO,aAASC,gBAAgBrF,KAAKsF,YAAY;AAC/C,YAAMC,aAAa,CAAA;AACnB,iBAAWC,KAAKxF,KAAK;AACnB,YAAI0C,eAAe1C,KAAKwF,CAAC,GAAG;AAC1B,gBAAM1C,IAAI9C,IAAIwF,CAAC;AACf,cAAI1C,MAAM1V,UAAa0V,MAAM,KAAM;AACnCyC,qBAAWD,WAAWE,CAAC,CAAC,IAAIL,SAASrC,CAAC;QACxC;MACF;AACA,aAAOyC;IACT;AASO,aAAS1Z,aAAaE,SAAQD,QAAQ;AAC3C,YAAM2Z,QAAQ7V,KAAKqU,MAAMrU,KAAKC,IAAI9D,UAAS,EAAE,CAAC,GAC5CqJ,UAAUxF,KAAKqU,MAAMrU,KAAKC,IAAI9D,UAAS,EAAE,CAAC,GAC1C2Z,OAAO3Z,WAAU,IAAI,MAAM;AAE7B,cAAQD,QAAM;QACZ,KAAK;AACH,iBAAQ,GAAE4Z,IAAK,GAAE9Q,SAAS6Q,OAAO,CAAC,CAAE,IAAG7Q,SAASQ,SAAS,CAAC,CAAE;QAC9D,KAAK;AACH,iBAAQ,GAAEsQ,IAAK,GAAED,KAAM,GAAErQ,UAAU,IAAK,IAAGA,OAAQ,KAAI,EAAG;QAC5D,KAAK;AACH,iBAAQ,GAAEsQ,IAAK,GAAE9Q,SAAS6Q,OAAO,CAAC,CAAE,GAAE7Q,SAASQ,SAAS,CAAC,CAAE;QAC7D;AACE,gBAAM,IAAIuQ,WAAY,gBAAe7Z,MAAO,sCAAqC;MACrF;IACF;AAEO,aAASuT,WAAWW,KAAK;AAC9B,aAAOuC,KAAKvC,KAAK,CAAC,QAAQ,UAAU,UAAU,aAAa,CAAC;IAC9D;AChTO,QAAM4F,aAAa,CACxB,WACA,YACA,SACA,SACA,OACA,QACA,QACA,UACA,aACA,WACA,YACA,UAAU;AAGL,QAAMC,cAAc,CACzB,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,OACA,KAAK;AAGA,QAAMC,eAAe,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAEhF,aAASxN,OAAO5J,QAAQ;AAC7B,cAAQA,QAAM;QACZ,KAAK;AACH,iBAAO,CAAC,GAAGoX,YAAY;QACzB,KAAK;AACH,iBAAO,CAAC,GAAGD,WAAW;QACxB,KAAK;AACH,iBAAO,CAAC,GAAGD,UAAU;QACvB,KAAK;AACH,iBAAO,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,MAAM,IAAI;QACvE,KAAK;AACH,iBAAO,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;QAChF;AACE,iBAAO;MACX;IACF;AAEO,QAAMG,eAAe,CAC1B,UACA,WACA,aACA,YACA,UACA,YACA,QAAQ;AAGH,QAAMC,gBAAgB,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAEtE,QAAMC,iBAAiB,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAEzD,aAASxN,SAAS/J,QAAQ;AAC/B,cAAQA,QAAM;QACZ,KAAK;AACH,iBAAO,CAAC,GAAGuX,cAAc;QAC3B,KAAK;AACH,iBAAO,CAAC,GAAGD,aAAa;QAC1B,KAAK;AACH,iBAAO,CAAC,GAAGD,YAAY;QACzB,KAAK;AACH,iBAAO,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;QAC3C;AACE,iBAAO;MACX;IACF;AAEO,QAAMrN,YAAY,CAAC,MAAM,IAAI;AAE7B,QAAMwN,WAAW,CAAC,iBAAiB,aAAa;AAEhD,QAAMC,YAAY,CAAC,MAAM,IAAI;AAE7B,QAAMC,aAAa,CAAC,KAAK,GAAG;AAE5B,aAASzN,KAAKjK,QAAQ;AAC3B,cAAQA,QAAM;QACZ,KAAK;AACH,iBAAO,CAAC,GAAG0X,UAAU;QACvB,KAAK;AACH,iBAAO,CAAC,GAAGD,SAAS;QACtB,KAAK;AACH,iBAAO,CAAC,GAAGD,QAAQ;QACrB;AACE,iBAAO;MACX;IACF;AAEO,aAASG,oBAAoBnT,IAAI;AACtC,aAAOwF,UAAUxF,GAAGlJ,OAAO,KAAK,IAAI,CAAC;IACvC;AAEO,aAASsc,mBAAmBpT,IAAIxE,QAAQ;AAC7C,aAAO+J,SAAS/J,MAAM,EAAEwE,GAAGtJ,UAAU,CAAC;IACxC;AAEO,aAAS2c,iBAAiBrT,IAAIxE,QAAQ;AAC3C,aAAO4J,OAAO5J,MAAM,EAAEwE,GAAG1J,QAAQ,CAAC;IACpC;AAEO,aAASgd,eAAetT,IAAIxE,QAAQ;AACzC,aAAOiK,KAAKjK,MAAM,EAAEwE,GAAG3J,OAAO,IAAI,IAAI,CAAC;IACzC;AAEO,aAASkd,mBAAmBzd,MAAM+M,OAAOE,UAAU,UAAUyQ,SAAS,OAAO;AAClF,YAAMC,QAAQ;QACZC,OAAO,CAAC,QAAQ,KAAK;QACrBC,UAAU,CAAC,WAAW,MAAM;QAC5BvO,QAAQ,CAAC,SAAS,KAAK;QACvBwO,OAAO,CAAC,QAAQ,KAAK;QACrBC,MAAM,CAAC,OAAO,OAAO,MAAM;QAC3BtB,OAAO,CAAC,QAAQ,KAAK;QACrBrQ,SAAS,CAAC,UAAU,MAAM;QAC1B4R,SAAS,CAAC,UAAU,MAAM;;AAG5B,YAAMC,WAAW,CAAC,SAAS,WAAW,SAAS,EAAE7U,QAAQpJ,IAAI,MAAM;AAEnE,UAAIiN,YAAY,UAAUgR,UAAU;AAClC,cAAMC,QAAQle,SAAS;AACvB,gBAAQ+M,OAAK;UACX,KAAK;AACH,mBAAOmR,QAAQ,aAAc,QAAOP,MAAM3d,IAAI,EAAE,CAAC,CAAE;UACrD,KAAK;AACH,mBAAOke,QAAQ,cAAe,QAAOP,MAAM3d,IAAI,EAAE,CAAC,CAAE;UACtD,KAAK;AACH,mBAAOke,QAAQ,UAAW,QAAOP,MAAM3d,IAAI,EAAE,CAAC,CAAE;QAEpD;MACF;AAEA,YAAMme,WAAW9S,OAAO6Q,GAAGnP,OAAO,EAAE,KAAKA,QAAQ,GAC/CqR,WAAWxX,KAAKC,IAAIkG,KAAK,GACzBsR,WAAWD,aAAa,GACxBE,WAAWX,MAAM3d,IAAI,GACrBue,UAAUb,SACNW,WACEC,SAAS,CAAC,IACVA,SAAS,CAAC,KAAKA,SAAS,CAAC,IAC3BD,WACAV,MAAM3d,IAAI,EAAE,CAAC,IACbA;AACN,aAAOme,WAAY,GAAEC,QAAS,IAAGG,OAAQ,SAAS,MAAKH,QAAS,IAAGG,OAAQ;IAC7E;ACjKA,aAASC,gBAAgBC,QAAQC,eAAe;AAC9C,UAAIte,KAAI;AACR,iBAAWue,SAASF,QAAQ;AAC1B,YAAIE,MAAMC,SAAS;AACjBxe,UAAAA,MAAKue,MAAME;QACb,OAAO;AACLze,UAAAA,MAAKse,cAAcC,MAAME,GAAG;QAC9B;MACF;AACA,aAAOze;IACT;AAEA,QAAM0e,yBAAyB;MAC7BC,GAAGC;MACHC,IAAID;MACJE,KAAKF;MACLG,MAAMH;MACNzK,GAAGyK;MACHI,IAAIJ;MACJK,KAAKL;MACLM,MAAMN;MACNO,GAAGP;MACHQ,IAAIR;MACJS,KAAKT;MACLU,MAAMV;MACNhV,GAAGgV;MACHW,IAAIX;MACJY,KAAKZ;MACLa,MAAMb;MACNc,GAAGd;MACHe,IAAIf;MACJgB,KAAKhB;MACLiB,MAAMjB;IACR;AAMe,QAAMkB,YAAN,MAAMA,WAAU;MAC7B,OAAOja,OAAOvC,QAAQd,OAAO,CAAA,GAAI;AAC/B,eAAO,IAAIsd,WAAUxc,QAAQd,IAAI;MACnC;MAEA,OAAOud,YAAYC,KAAK;AAItB,YAAIC,UAAU,MACZC,cAAc,IACdC,YAAY;AACd,cAAM9B,SAAS,CAAA;AACf,iBAAShZ,IAAI,GAAGA,IAAI2a,IAAI1a,QAAQD,KAAK;AACnC,gBAAM+a,IAAIJ,IAAIK,OAAOhb,CAAC;AACtB,cAAI+a,MAAM,KAAK;AACb,gBAAIF,YAAY5a,SAAS,GAAG;AAC1B+Y,qBAAOpU,KAAK;gBAAEuU,SAAS2B,aAAa,QAAQG,KAAKJ,WAAW;gBAAGzB,KAAKyB;cAAY,CAAC;YACnF;AACAD,sBAAU;AACVC,0BAAc;AACdC,wBAAY,CAACA;qBACJA,WAAW;AACpBD,2BAAeE;UACjB,WAAWA,MAAMH,SAAS;AACxBC,2BAAeE;UACjB,OAAO;AACL,gBAAIF,YAAY5a,SAAS,GAAG;AAC1B+Y,qBAAOpU,KAAK;gBAAEuU,SAAS,QAAQ8B,KAAKJ,WAAW;gBAAGzB,KAAKyB;cAAY,CAAC;YACtE;AACAA,0BAAcE;AACdH,sBAAUG;UACZ;QACF;AAEA,YAAIF,YAAY5a,SAAS,GAAG;AAC1B+Y,iBAAOpU,KAAK;YAAEuU,SAAS2B,aAAa,QAAQG,KAAKJ,WAAW;YAAGzB,KAAKyB;UAAY,CAAC;QACnF;AAEA,eAAO7B;MACT;MAEA,OAAOK,uBAAuBH,OAAO;AACnC,eAAOG,uBAAuBH,KAAK;MACrC;MAEAlf,YAAYiE,QAAQid,YAAY;AAC9B,aAAK/d,OAAO+d;AACZ,aAAKnW,MAAM9G;AACX,aAAKkd,YAAY;MACnB;MAEAC,wBAAwB3W,IAAItH,MAAM;AAChC,YAAI,KAAKge,cAAc,MAAM;AAC3B,eAAKA,YAAY,KAAKpW,IAAI6E,kBAAiB;QAC7C;AACA,cAAMQ,KAAK,KAAK+Q,UAAU9Q,YAAY5F,IAAI;UAAE,GAAG,KAAKtH;UAAM,GAAGA;QAAK,CAAC;AACnE,eAAOiN,GAAG/M,OAAM;MAClB;MAEAgN,YAAY5F,IAAItH,OAAO,CAAA,GAAI;AACzB,eAAO,KAAK4H,IAAIsF,YAAY5F,IAAI;UAAE,GAAG,KAAKtH;UAAM,GAAGA;QAAK,CAAC;MAC3D;MAEAke,eAAe5W,IAAItH,MAAM;AACvB,eAAO,KAAKkN,YAAY5F,IAAItH,IAAI,EAAEE,OAAM;MAC1C;MAEAie,oBAAoB7W,IAAItH,MAAM;AAC5B,eAAO,KAAKkN,YAAY5F,IAAItH,IAAI,EAAE2C,cAAa;MACjD;MAEAyb,eAAeC,UAAUre,MAAM;AAC7B,cAAMiN,KAAK,KAAKC,YAAYmR,SAASC,OAAOte,IAAI;AAChD,eAAOiN,GAAG3L,IAAIid,YAAYF,SAASC,MAAM3U,SAAQ,GAAI0U,SAASG,IAAI7U,SAAQ,CAAE;MAC9E;MAEA/I,gBAAgB0G,IAAItH,MAAM;AACxB,eAAO,KAAKkN,YAAY5F,IAAItH,IAAI,EAAEY,gBAAe;MACnD;MAEA6d,IAAIlhB,IAAGmhB,IAAI,GAAG;AAEZ,YAAI,KAAK1e,KAAKqI,aAAa;AACzB,iBAAOW,SAASzL,IAAGmhB,CAAC;QACtB;AAEA,cAAM1e,OAAO;UAAE,GAAG,KAAKA;;AAEvB,YAAI0e,IAAI,GAAG;AACT1e,eAAKsI,QAAQoW;QACf;AAEA,eAAO,KAAK9W,IAAI4F,gBAAgBxN,IAAI,EAAEE,OAAO3C,EAAC;MAChD;MAEAohB,yBAAyBrX,IAAIkW,KAAK;AAChC,cAAMoB,eAAe,KAAKhX,IAAII,YAAW,MAAO,MAC9C6W,uBAAuB,KAAKjX,IAAIX,kBAAkB,KAAKW,IAAIX,mBAAmB,WAC9E0Q,SAASA,CAAC3X,MAAM4M,YAAY,KAAKhF,IAAIgF,QAAQtF,IAAItH,MAAM4M,OAAO,GAC9D3M,gBAAgBD,UAAS;AACvB,cAAIsH,GAAGwX,iBAAiBxX,GAAGnH,WAAW,KAAKH,KAAK+e,QAAQ;AACtD,mBAAO;UACT;AAEA,iBAAOzX,GAAGhH,UAAUgH,GAAGhE,KAAKrD,aAAaqH,GAAGvH,IAAIC,KAAKE,MAAM,IAAI;WAEjE8e,WAAWA,MACTJ,eACIxU,oBAA4B9C,EAAE,IAC9BqQ,OAAO;UAAEvZ,MAAM;UAAWQ,WAAW;WAAS,WAAW,GAC/DhB,QAAQA,CAACkF,QAAQ+I,eACf+S,eACIxU,iBAAyB9C,IAAIxE,MAAM,IACnC6U,OAAO9L,aAAa;UAAEjO,OAAOkF;QAAO,IAAI;UAAElF,OAAOkF;UAAQjF,KAAK;WAAa,OAAO,GACxFG,UAAUA,CAAC8E,QAAQ+I,eACjB+S,eACIxU,mBAA2B9C,IAAIxE,MAAM,IACrC6U,OACE9L,aAAa;UAAE7N,SAAS8E;QAAO,IAAI;UAAE9E,SAAS8E;UAAQlF,OAAO;UAAQC,KAAK;WAC1E,SACF,GACNohB,aAAclD,WAAU;AACtB,gBAAMgC,aAAaT,WAAUpB,uBAAuBH,KAAK;AACzD,cAAIgC,YAAY;AACd,mBAAO,KAAKE,wBAAwB3W,IAAIyW,UAAU;UACpD,OAAO;AACL,mBAAOhC;UACT;WAEFra,MAAOoB,YACL8b,eAAexU,eAAuB9C,IAAIxE,MAAM,IAAI6U,OAAO;UAAEjW,KAAKoB;WAAU,KAAK,GACnFgZ,gBAAiBC,WAAU;AAEzB,kBAAQA,OAAK;YAEX,KAAK;AACH,qBAAO,KAAK0C,IAAInX,GAAGjD,WAAW;YAChC,KAAK;YAEL,KAAK;AACH,qBAAO,KAAKoa,IAAInX,GAAGjD,aAAa,CAAC;YAEnC,KAAK;AACH,qBAAO,KAAKoa,IAAInX,GAAG/I,MAAM;YAC3B,KAAK;AACH,qBAAO,KAAKkgB,IAAInX,GAAG/I,QAAQ,CAAC;YAE9B,KAAK;AACH,qBAAO,KAAKkgB,IAAIza,KAAKuE,MAAMjB,GAAGjD,cAAc,EAAE,GAAG,CAAC;YACpD,KAAK;AACH,qBAAO,KAAKoa,IAAIza,KAAKuE,MAAMjB,GAAGjD,cAAc,GAAG,CAAC;YAElD,KAAK;AACH,qBAAO,KAAKoa,IAAInX,GAAGjJ,MAAM;YAC3B,KAAK;AACH,qBAAO,KAAKogB,IAAInX,GAAGjJ,QAAQ,CAAC;YAE9B,KAAK;AACH,qBAAO,KAAKogB,IAAInX,GAAGlJ,OAAO,OAAO,IAAI,KAAKkJ,GAAGlJ,OAAO,EAAE;YACxD,KAAK;AACH,qBAAO,KAAKqgB,IAAInX,GAAGlJ,OAAO,OAAO,IAAI,KAAKkJ,GAAGlJ,OAAO,IAAI,CAAC;YAC3D,KAAK;AACH,qBAAO,KAAKqgB,IAAInX,GAAGlJ,IAAI;YACzB,KAAK;AACH,qBAAO,KAAKqgB,IAAInX,GAAGlJ,MAAM,CAAC;YAE5B,KAAK;AAEH,qBAAO6B,cAAa;gBAAEC,QAAQ;gBAAU6e,QAAQ,KAAK/e,KAAK+e;cAAO,CAAC;YACpE,KAAK;AAEH,qBAAO9e,cAAa;gBAAEC,QAAQ;gBAAS6e,QAAQ,KAAK/e,KAAK+e;cAAO,CAAC;YACnE,KAAK;AAEH,qBAAO9e,cAAa;gBAAEC,QAAQ;gBAAU6e,QAAQ,KAAK/e,KAAK+e;cAAO,CAAC;YACpE,KAAK;AAEH,qBAAOzX,GAAGhE,KAAKxD,WAAWwH,GAAGvH,IAAI;gBAAEG,QAAQ;gBAASY,QAAQ,KAAK8G,IAAI9G;cAAO,CAAC;YAC/E,KAAK;AAEH,qBAAOwG,GAAGhE,KAAKxD,WAAWwH,GAAGvH,IAAI;gBAAEG,QAAQ;gBAAQY,QAAQ,KAAK8G,IAAI9G;cAAO,CAAC;YAE9E,KAAK;AAEH,qBAAOwG,GAAGjG;YAEZ,KAAK;AACH,qBAAO2d,SAAQ;YAEjB,KAAK;AACH,qBAAOH,uBAAuBlH,OAAO;gBAAE9Z,KAAK;iBAAa,KAAK,IAAI,KAAK4gB,IAAInX,GAAGzJ,GAAG;YACnF,KAAK;AACH,qBAAOghB,uBAAuBlH,OAAO;gBAAE9Z,KAAK;cAAU,GAAG,KAAK,IAAI,KAAK4gB,IAAInX,GAAGzJ,KAAK,CAAC;YAEtF,KAAK;AAEH,qBAAO,KAAK4gB,IAAInX,GAAGtJ,OAAO;YAC5B,KAAK;AAEH,qBAAOA,QAAQ,SAAS,IAAI;YAC9B,KAAK;AAEH,qBAAOA,QAAQ,QAAQ,IAAI;YAC7B,KAAK;AAEH,qBAAOA,QAAQ,UAAU,IAAI;YAE/B,KAAK;AAEH,qBAAO,KAAKygB,IAAInX,GAAGtJ,OAAO;YAC5B,KAAK;AAEH,qBAAOA,QAAQ,SAAS,KAAK;YAC/B,KAAK;AAEH,qBAAOA,QAAQ,QAAQ,KAAK;YAC9B,KAAK;AAEH,qBAAOA,QAAQ,UAAU,KAAK;YAEhC,KAAK;AAEH,qBAAO6gB,uBACHlH,OAAO;gBAAE/Z,OAAO;gBAAWC,KAAK;iBAAa,OAAO,IACpD,KAAK4gB,IAAInX,GAAG1J,KAAK;YACvB,KAAK;AAEH,qBAAOihB,uBACHlH,OAAO;gBAAE/Z,OAAO;gBAAWC,KAAK;cAAU,GAAG,OAAO,IACpD,KAAK4gB,IAAInX,GAAG1J,OAAO,CAAC;YAC1B,KAAK;AAEH,qBAAOA,MAAM,SAAS,IAAI;YAC5B,KAAK;AAEH,qBAAOA,MAAM,QAAQ,IAAI;YAC3B,KAAK;AAEH,qBAAOA,MAAM,UAAU,IAAI;YAE7B,KAAK;AAEH,qBAAOihB,uBACHlH,OAAO;gBAAE/Z,OAAO;iBAAa,OAAO,IACpC,KAAK6gB,IAAInX,GAAG1J,KAAK;YACvB,KAAK;AAEH,qBAAOihB,uBACHlH,OAAO;gBAAE/Z,OAAO;cAAU,GAAG,OAAO,IACpC,KAAK6gB,IAAInX,GAAG1J,OAAO,CAAC;YAC1B,KAAK;AAEH,qBAAOA,MAAM,SAAS,KAAK;YAC7B,KAAK;AAEH,qBAAOA,MAAM,QAAQ,KAAK;YAC5B,KAAK;AAEH,qBAAOA,MAAM,UAAU,KAAK;YAE9B,KAAK;AAEH,qBAAOihB,uBAAuBlH,OAAO;gBAAEha,MAAM;iBAAa,MAAM,IAAI,KAAK8gB,IAAInX,GAAG3J,IAAI;YACtF,KAAK;AAEH,qBAAOkhB,uBACHlH,OAAO;gBAAEha,MAAM;iBAAa,MAAM,IAClC,KAAK8gB,IAAInX,GAAG3J,KAAKsQ,SAAQ,EAAGiR,MAAM,EAAE,GAAG,CAAC;YAC9C,KAAK;AAEH,qBAAOL,uBACHlH,OAAO;gBAAEha,MAAM;cAAU,GAAG,MAAM,IAClC,KAAK8gB,IAAInX,GAAG3J,MAAM,CAAC;YACzB,KAAK;AAEH,qBAAOkhB,uBACHlH,OAAO;gBAAEha,MAAM;cAAU,GAAG,MAAM,IAClC,KAAK8gB,IAAInX,GAAG3J,MAAM,CAAC;YAEzB,KAAK;AAEH,qBAAO+D,IAAI,OAAO;YACpB,KAAK;AAEH,qBAAOA,IAAI,MAAM;YACnB,KAAK;AACH,qBAAOA,IAAI,QAAQ;YACrB,KAAK;AACH,qBAAO,KAAK+c,IAAInX,GAAGiM,SAAStF,SAAQ,EAAGiR,MAAM,EAAE,GAAG,CAAC;YACrD,KAAK;AACH,qBAAO,KAAKT,IAAInX,GAAGiM,UAAU,CAAC;YAChC,KAAK;AACH,qBAAO,KAAKkL,IAAInX,GAAGgM,UAAU;YAC/B,KAAK;AACH,qBAAO,KAAKmL,IAAInX,GAAGgM,YAAY,CAAC;YAClC,KAAK;AACH,qBAAO,KAAKmL,IAAInX,GAAGiN,eAAe;YACpC,KAAK;AACH,qBAAO,KAAKkK,IAAInX,GAAGiN,iBAAiB,CAAC;YACvC,KAAK;AACH,qBAAO,KAAKkK,IAAInX,GAAGkN,cAAcvG,SAAQ,EAAGiR,MAAM,EAAE,GAAG,CAAC;YAC1D,KAAK;AACH,qBAAO,KAAKT,IAAInX,GAAGkN,eAAe,CAAC;YACrC,KAAK;AACH,qBAAO,KAAKiK,IAAInX,GAAGsL,OAAO;YAC5B,KAAK;AACH,qBAAO,KAAK6L,IAAInX,GAAGsL,SAAS,CAAC;YAC/B,KAAK;AAEH,qBAAO,KAAK6L,IAAInX,GAAG6X,OAAO;YAC5B,KAAK;AAEH,qBAAO,KAAKV,IAAInX,GAAG6X,SAAS,CAAC;YAC/B,KAAK;AACH,qBAAO,KAAKV,IAAIza,KAAKuE,MAAMjB,GAAGvH,KAAK,GAAI,CAAC;YAC1C,KAAK;AACH,qBAAO,KAAK0e,IAAInX,GAAGvH,EAAE;YACvB;AACE,qBAAOkf,WAAWlD,KAAK;UAC3B;;AAGJ,eAAOH,gBAAgB0B,WAAUC,YAAYC,GAAG,GAAG1B,aAAa;MAClE;MAEAsD,yBAAyBC,KAAK7B,KAAK;AACjC,cAAM8B,eAAgBvD,WAAU;AAC5B,kBAAQA,MAAM,CAAC,GAAC;YACd,KAAK;AACH,qBAAO;YACT,KAAK;AACH,qBAAO;YACT,KAAK;AACH,qBAAO;YACT,KAAK;AACH,qBAAO;YACT,KAAK;AACH,qBAAO;YACT,KAAK;AACH,qBAAO;YACT,KAAK;AACH,qBAAO;YACT,KAAK;AACH,qBAAO;YACT;AACE,qBAAO;UACX;WAEFD,gBAAiByD,YAAYxD,WAAU;AACrC,gBAAMyD,SAASF,aAAavD,KAAK;AACjC,cAAIyD,QAAQ;AACV,mBAAO,KAAKf,IAAIc,OAAOhe,IAAIie,MAAM,GAAGzD,MAAMjZ,MAAM;UAClD,OAAO;AACL,mBAAOiZ;UACT;WAEF0D,SAASnC,WAAUC,YAAYC,GAAG,GAClCkC,aAAaD,OAAOlJ,OAClB,CAACoJ,OAAO;UAAE3D;UAASC;QAAI,MAAOD,UAAU2D,QAAQA,MAAMC,OAAO3D,GAAG,GAChE,CAAA,CACF,GACA4D,YAAYR,IAAIS,QAAQ,GAAGJ,WAAWjW,IAAI6V,YAAY,EAAES,OAAQpO,OAAMA,CAAC,CAAC;AAC1E,eAAOiK,gBAAgB6D,QAAQ3D,cAAc+D,SAAS,CAAC;MACzD;IACF;AClYA,QAAMG,YAAY;AAElB,aAASC,kBAAkBC,SAAS;AAClC,YAAMC,OAAOD,QAAQ3J,OAAO,CAACnP,GAAGiH,MAAMjH,IAAIiH,EAAE+R,QAAQ,EAAE;AACtD,aAAO9O,OAAQ,IAAG6O,IAAK,GAAE;IAC3B;AAEA,aAASE,qBAAqBC,YAAY;AACxC,aAAQhT,OACNgT,WACG/J,OACC,CAAC,CAACgK,YAAYC,YAAYC,MAAM,GAAGC,OAAO;AACxC,cAAM,CAACzE,KAAK3Y,MAAMmT,IAAI,IAAIiK,GAAGpT,GAAGmT,MAAM;AACtC,eAAO,CAAC;UAAE,GAAGF;UAAY,GAAGtE;QAAI,GAAG3Y,QAAQkd,YAAY/J,IAAI;MAC7D,GACA,CAAC,CAAA,GAAI,MAAM,CAAC,CACd,EACCyI,MAAM,GAAG,CAAC;IACjB;AAEA,aAASyB,MAAMnjB,OAAMojB,UAAU;AAC7B,UAAIpjB,MAAK,MAAM;AACb,eAAO,CAAC,MAAM,IAAI;MACpB;AAEA,iBAAW,CAAC6T,OAAOwP,SAAS,KAAKD,UAAU;AACzC,cAAMtT,IAAI+D,MAAMnP,KAAK1E,EAAC;AACtB,YAAI8P,GAAG;AACL,iBAAOuT,UAAUvT,CAAC;QACpB;MACF;AACA,aAAO,CAAC,MAAM,IAAI;IACpB;AAEA,aAASwT,eAAepY,MAAM;AAC5B,aAAO,CAAC4F,QAAOmS,WAAW;AACxB,cAAMM,MAAM,CAAA;AACZ,YAAIle;AAEJ,aAAKA,IAAI,GAAGA,IAAI6F,KAAK5F,QAAQD,KAAK;AAChCke,cAAIrY,KAAK7F,CAAC,CAAC,IAAI6U,aAAapJ,OAAMmS,SAAS5d,CAAC,CAAC;QAC/C;AACA,eAAO,CAACke,KAAK,MAAMN,SAAS5d,CAAC;;IAEjC;AAGA,QAAMme,cAAc;AACpB,QAAMC,kBAAmB,MAAKD,YAAYZ,MAAO,WAAUJ,UAAUI,MAAO;AAC5E,QAAMc,mBAAmB;AACzB,QAAMC,eAAe7P,OAAQ,GAAE4P,iBAAiBd,MAAO,GAAEa,eAAgB,EAAC;AAC1E,QAAMG,wBAAwB9P,OAAQ,OAAM6P,aAAaf,MAAO,IAAG;AACnE,QAAMiB,cAAc;AACpB,QAAMC,eAAe;AACrB,QAAMC,kBAAkB;AACxB,QAAMC,qBAAqBV,YAAY,YAAY,cAAc,SAAS;AAC1E,QAAMW,wBAAwBX,YAAY,QAAQ,SAAS;AAC3D,QAAMY,cAAc;AACpB,QAAMC,eAAerQ,OAClB,GAAE4P,iBAAiBd,MAAO,QAAOY,YAAYZ,MAAO,KAAIJ,UAAUI,MAAO,KAC5E;AACA,QAAMwB,wBAAwBtQ,OAAQ,OAAMqQ,aAAavB,MAAO,IAAG;AAEnE,aAASyB,IAAIvT,QAAOtL,KAAK8e,UAAU;AACjC,YAAMxU,IAAIgB,OAAMtL,GAAG;AACnB,aAAOC,YAAYqK,CAAC,IAAIwU,WAAWpK,aAAapK,CAAC;IACnD;AAEA,aAASyU,cAAczT,QAAOmS,QAAQ;AACpC,YAAMuB,OAAO;QACXrkB,MAAMkkB,IAAIvT,QAAOmS,MAAM;QACvB7iB,OAAOikB,IAAIvT,QAAOmS,SAAS,GAAG,CAAC;QAC/B5iB,KAAKgkB,IAAIvT,QAAOmS,SAAS,GAAG,CAAC;;AAG/B,aAAO,CAACuB,MAAM,MAAMvB,SAAS,CAAC;IAChC;AAEA,aAASwB,eAAe3T,QAAOmS,QAAQ;AACrC,YAAMuB,OAAO;QACXnI,OAAOgI,IAAIvT,QAAOmS,QAAQ,CAAC;QAC3BjX,SAASqY,IAAIvT,QAAOmS,SAAS,GAAG,CAAC;QACjCrF,SAASyG,IAAIvT,QAAOmS,SAAS,GAAG,CAAC;QACjCyB,cAAcpK,YAAYxJ,OAAMmS,SAAS,CAAC,CAAC;;AAG7C,aAAO,CAACuB,MAAM,MAAMvB,SAAS,CAAC;IAChC;AAEA,aAAS0B,iBAAiB7T,QAAOmS,QAAQ;AACvC,YAAM2B,QAAQ,CAAC9T,OAAMmS,MAAM,KAAK,CAACnS,OAAMmS,SAAS,CAAC,GAC/C4B,aAAa9T,aAAaD,OAAMmS,SAAS,CAAC,GAAGnS,OAAMmS,SAAS,CAAC,CAAC,GAC9Dnd,OAAO8e,QAAQ,OAAOlU,gBAAgBzN,SAAS4hB,UAAU;AAC3D,aAAO,CAAC,CAAA,GAAI/e,MAAMmd,SAAS,CAAC;IAC9B;AAEA,aAAS6B,gBAAgBhU,QAAOmS,QAAQ;AACtC,YAAMnd,OAAOgL,OAAMmS,MAAM,IAAIrd,SAASC,OAAOiL,OAAMmS,MAAM,CAAC,IAAI;AAC9D,aAAO,CAAC,CAAA,GAAInd,MAAMmd,SAAS,CAAC;IAC9B;AAIA,QAAM8B,cAAcjR,OAAQ,MAAK4P,iBAAiBd,MAAO,GAAE;AAI3D,QAAMoC,cACJ;AAEF,aAASC,mBAAmBnU,QAAO;AACjC,YAAM,CAAC9Q,IAAGklB,SAASC,UAAUC,SAASC,QAAQC,SAASC,WAAWC,WAAWC,eAAe,IAC1F3U;AAEF,YAAM4U,oBAAoB1lB,GAAE,CAAC,MAAM;AACnC,YAAM2lB,kBAAkBH,aAAaA,UAAU,CAAC,MAAM;AAEtD,YAAMI,cAAcA,CAAC3E,KAAK4E,QAAQ,UAChC5E,QAAQjd,WAAc6hB,SAAU5E,OAAOyE,qBAAsB,CAACzE,MAAMA;AAEtE,aAAO,CACL;QACEzD,OAAOoI,YAAYxL,cAAc8K,OAAO,CAAC;QACzChW,QAAQ0W,YAAYxL,cAAc+K,QAAQ,CAAC;QAC3CzH,OAAOkI,YAAYxL,cAAcgL,OAAO,CAAC;QACzCzH,MAAMiI,YAAYxL,cAAciL,MAAM,CAAC;QACvChJ,OAAOuJ,YAAYxL,cAAckL,OAAO,CAAC;QACzCtZ,SAAS4Z,YAAYxL,cAAcmL,SAAS,CAAC;QAC7C3H,SAASgI,YAAYxL,cAAcoL,SAAS,GAAGA,cAAc,IAAI;QACjEd,cAAckB,YAAYtL,YAAYmL,eAAe,GAAGE,eAAe;MACzE,CAAC;IAEL;AAKA,QAAMG,aAAa;MACjBC,KAAK;MACLC,KAAK,KAAK;MACVC,KAAK,KAAK;MACVC,KAAK,KAAK;MACVC,KAAK,KAAK;MACVC,KAAK,KAAK;MACVC,KAAK,KAAK;MACVC,KAAK,KAAK;MACVC,KAAK,KAAK;IACZ;AAEA,aAASC,YAAYC,YAAYvB,SAASC,UAAUE,QAAQC,SAASC,WAAWC,WAAW;AACzF,YAAMkB,SAAS;QACbvmB,MAAM+kB,QAAQ5f,WAAW,IAAI+V,eAAenB,aAAagL,OAAO,CAAC,IAAIhL,aAAagL,OAAO;QACzF9kB,OAAOwM,YAAoB5D,QAAQmc,QAAQ,IAAI;QAC/C9kB,KAAK6Z,aAAamL,MAAM;QACxBzkB,MAAMsZ,aAAaoL,OAAO;QAC1BzkB,QAAQqZ,aAAaqL,SAAS;;AAGhC,UAAIC,UAAWkB,QAAO3lB,SAASmZ,aAAasL,SAAS;AACrD,UAAIiB,YAAY;AACdC,eAAOlmB,UACLimB,WAAWnhB,SAAS,IAChBsH,aAAqB5D,QAAQyd,UAAU,IAAI,IAC3C7Z,cAAsB5D,QAAQyd,UAAU,IAAI;MACpD;AAEA,aAAOC;IACT;AAGA,QAAMC,UACJ;AAEF,aAASC,eAAe9V,QAAO;AAC7B,YAAM,CAAA,EAEF2V,YACApB,QACAF,UACAD,SACAI,SACAC,WACAC,WACAqB,WACAC,WACAtL,YACAC,YAAY,IACV3K,QACJ4V,SAASF,YAAYC,YAAYvB,SAASC,UAAUE,QAAQC,SAASC,WAAWC,SAAS;AAE3F,UAAI7iB;AACJ,UAAIkkB,WAAW;AACblkB,QAAAA,UAASmjB,WAAWe,SAAS;iBACpBC,WAAW;AACpBnkB,QAAAA,UAAS;MACX,OAAO;AACLA,QAAAA,UAASoO,aAAayK,YAAYC,YAAY;MAChD;AAEA,aAAO,CAACiL,QAAQ,IAAIhW,gBAAgB/N,OAAM,CAAC;IAC7C;AAEA,aAASokB,kBAAkB/mB,IAAG;AAE5B,aAAOA,GACJwE,QAAQ,sBAAsB,GAAG,EACjCA,QAAQ,YAAY,GAAG,EACvBwiB,KAAI;IACT;AAIA,QAAMC,UACF;AADJ,QAEEC,SACE;AAHJ,QAIEC,QACE;AAEJ,aAASC,oBAAoBtW,QAAO;AAClC,YAAM,CAAA,EAAG2V,YAAYpB,QAAQF,UAAUD,SAASI,SAASC,WAAWC,SAAS,IAAI1U,QAC/E4V,SAASF,YAAYC,YAAYvB,SAASC,UAAUE,QAAQC,SAASC,WAAWC,SAAS;AAC3F,aAAO,CAACkB,QAAQhW,gBAAgBC,WAAW;IAC7C;AAEA,aAAS0W,aAAavW,QAAO;AAC3B,YAAM,CAAA,EAAG2V,YAAYtB,UAAUE,QAAQC,SAASC,WAAWC,WAAWN,OAAO,IAAIpU,QAC/E4V,SAASF,YAAYC,YAAYvB,SAASC,UAAUE,QAAQC,SAASC,WAAWC,SAAS;AAC3F,aAAO,CAACkB,QAAQhW,gBAAgBC,WAAW;IAC7C;AAEA,QAAM2W,+BAA+B7E,eAAeoB,aAAaD,qBAAqB;AACtF,QAAM2D,gCAAgC9E,eAAeqB,cAAcF,qBAAqB;AACxF,QAAM4D,mCAAmC/E,eAAesB,iBAAiBH,qBAAqB;AAC9F,QAAM6D,uBAAuBhF,eAAekB,YAAY;AAExD,QAAM+D,6BAA6B7E,kBACjC0B,eACAE,gBACAE,kBACAG,eACF;AACA,QAAM6C,8BAA8B9E,kBAClCmB,oBACAS,gBACAE,kBACAG,eACF;AACA,QAAM8C,+BAA+B/E,kBACnCoB,uBACAQ,gBACAE,kBACAG,eACF;AACA,QAAM+C,0BAA0BhF,kBAC9B4B,gBACAE,kBACAG,eACF;AAMO,aAASgD,aAAa9nB,IAAG;AAC9B,aAAOmjB,MACLnjB,IACA,CAACsnB,8BAA8BI,0BAA0B,GACzD,CAACH,+BAA+BI,2BAA2B,GAC3D,CAACH,kCAAkCI,4BAA4B,GAC/D,CAACH,sBAAsBI,uBAAuB,CAChD;IACF;AAEO,aAASE,iBAAiB/nB,IAAG;AAClC,aAAOmjB,MAAM4D,kBAAkB/mB,EAAC,GAAG,CAAC2mB,SAASC,cAAc,CAAC;IAC9D;AAEO,aAASoB,cAAchoB,IAAG;AAC/B,aAAOmjB,MACLnjB,IACA,CAACinB,SAASG,mBAAmB,GAC7B,CAACF,QAAQE,mBAAmB,GAC5B,CAACD,OAAOE,YAAY,CACtB;IACF;AAEO,aAASY,iBAAiBjoB,IAAG;AAClC,aAAOmjB,MAAMnjB,IAAG,CAACglB,aAAaC,kBAAkB,CAAC;IACnD;AAEA,QAAMiD,qBAAqBrF,kBAAkB4B,cAAc;AAEpD,aAAS0D,iBAAiBnoB,IAAG;AAClC,aAAOmjB,MAAMnjB,IAAG,CAAC+kB,aAAamD,kBAAkB,CAAC;IACnD;AAEA,QAAME,+BAA+B3F,eAAeyB,aAAaE,qBAAqB;AACtF,QAAMiE,uBAAuB5F,eAAe0B,YAAY;AAExD,QAAMmE,kCAAkCzF,kBACtC4B,gBACAE,kBACAG,eACF;AAEO,aAASyD,SAASvoB,IAAG;AAC1B,aAAOmjB,MACLnjB,IACA,CAACooB,8BAA8BV,0BAA0B,GACzD,CAACW,sBAAsBC,+BAA+B,CACxD;IACF;AC9TA,QAAME,YAAU;AAGT,QAAMC,iBAAiB;MAC1B/K,OAAO;QACLC,MAAM;QACNtB,OAAO,IAAI;QACXrQ,SAAS,IAAI,KAAK;QAClB4R,SAAS,IAAI,KAAK,KAAK;QACvB8G,cAAc,IAAI,KAAK,KAAK,KAAK;;MAEnC/G,MAAM;QACJtB,OAAO;QACPrQ,SAAS,KAAK;QACd4R,SAAS,KAAK,KAAK;QACnB8G,cAAc,KAAK,KAAK,KAAK;;MAE/BrI,OAAO;QAAErQ,SAAS;QAAI4R,SAAS,KAAK;QAAI8G,cAAc,KAAK,KAAK;;MAChE1Y,SAAS;QAAE4R,SAAS;QAAI8G,cAAc,KAAK;;MAC3C9G,SAAS;QAAE8G,cAAc;MAAK;;AAhB3B,QAkBLgE,eAAe;MACblL,OAAO;QACLC,UAAU;QACVvO,QAAQ;QACRwO,OAAO;QACPC,MAAM;QACNtB,OAAO,MAAM;QACbrQ,SAAS,MAAM,KAAK;QACpB4R,SAAS,MAAM,KAAK,KAAK;QACzB8G,cAAc,MAAM,KAAK,KAAK,KAAK;;MAErCjH,UAAU;QACRvO,QAAQ;QACRwO,OAAO;QACPC,MAAM;QACNtB,OAAO,KAAK;QACZrQ,SAAS,KAAK,KAAK;QACnB4R,SAAS,KAAK,KAAK,KAAK;QACxB8G,cAAc,KAAK,KAAK,KAAK,KAAK;;MAEpCxV,QAAQ;QACNwO,OAAO;QACPC,MAAM;QACNtB,OAAO,KAAK;QACZrQ,SAAS,KAAK,KAAK;QACnB4R,SAAS,KAAK,KAAK,KAAK;QACxB8G,cAAc,KAAK,KAAK,KAAK,KAAK;;MAGpC,GAAG+D;;AA/CA,QAiDLE,qBAAqB,SAAW;AAjD3B,QAkDLC,sBAAsB,SAAW;AAlD5B,QAmDLC,iBAAiB;MACfrL,OAAO;QACLC,UAAU;QACVvO,QAAQ;QACRwO,OAAOiL,qBAAqB;QAC5BhL,MAAMgL;QACNtM,OAAOsM,qBAAqB;QAC5B3c,SAAS2c,qBAAqB,KAAK;QACnC/K,SAAS+K,qBAAqB,KAAK,KAAK;QACxCjE,cAAciE,qBAAqB,KAAK,KAAK,KAAK;;MAEpDlL,UAAU;QACRvO,QAAQ;QACRwO,OAAOiL,qBAAqB;QAC5BhL,MAAMgL,qBAAqB;QAC3BtM,OAAQsM,qBAAqB,KAAM;QACnC3c,SAAU2c,qBAAqB,KAAK,KAAM;QAC1C/K,SAAU+K,qBAAqB,KAAK,KAAK,KAAM;QAC/CjE,cAAeiE,qBAAqB,KAAK,KAAK,KAAK,MAAQ;;MAE7DzZ,QAAQ;QACNwO,OAAOkL,sBAAsB;QAC7BjL,MAAMiL;QACNvM,OAAOuM,sBAAsB;QAC7B5c,SAAS4c,sBAAsB,KAAK;QACpChL,SAASgL,sBAAsB,KAAK,KAAK;QACzClE,cAAckE,sBAAsB,KAAK,KAAK,KAAK;;MAErD,GAAGH;;AAIP,QAAMK,iBAAe,CACnB,SACA,YACA,UACA,SACA,QACA,SACA,WACA,WACA,cAAc;AAGhB,QAAMC,eAAeD,eAAapH,MAAM,CAAC,EAAEsH,QAAO;AAGlD,aAASna,QAAMgT,KAAK/S,MAAM9I,QAAQ,OAAO;AAEvC,YAAMijB,OAAO;QACXC,QAAQljB,QAAQ8I,KAAKoa,SAAS;UAAE,GAAGrH,IAAIqH;UAAQ,GAAIpa,KAAKoa,UAAU,CAAA;;QAClE9e,KAAKyX,IAAIzX,IAAIyE,MAAMC,KAAK1E,GAAG;QAC3B+e,oBAAoBra,KAAKqa,sBAAsBtH,IAAIsH;QACnDC,QAAQta,KAAKsa,UAAUvH,IAAIuH;;AAE7B,aAAO,IAAIC,SAASJ,IAAI;IAC1B;AAEA,aAASK,iBAAiBF,QAAQG,MAAM;AAAA,UAAAC;AACtC,UAAIC,OAAGD,qBAAGD,KAAK7E,iBAAY,OAAA8E,qBAAI;AAC/B,iBAAW5pB,QAAQmpB,aAAarH,MAAM,CAAC,GAAG;AACxC,YAAI6H,KAAK3pB,IAAI,GAAG;AACd6pB,iBAAOF,KAAK3pB,IAAI,IAAIwpB,OAAOxpB,IAAI,EAAE,cAAc;QACjD;MACF;AACA,aAAO6pB;IACT;AAGA,aAASC,gBAAgBN,QAAQG,MAAM;AAGrC,YAAM5O,SAAS2O,iBAAiBF,QAAQG,IAAI,IAAI,IAAI,KAAK;AAEzDT,qBAAaa,YAAY,CAACC,UAAU3J,YAAY;AAC9C,YAAI,CAACxa,YAAY8jB,KAAKtJ,OAAO,CAAC,GAAG;AAC/B,cAAI2J,UAAU;AACZ,kBAAMC,cAAcN,KAAKK,QAAQ,IAAIjP;AACrC,kBAAMmP,OAAOV,OAAOnJ,OAAO,EAAE2J,QAAQ;AAiBrC,kBAAMG,SAASvjB,KAAKuE,MAAM8e,cAAcC,IAAI;AAC5CP,iBAAKtJ,OAAO,KAAK8J,SAASpP;AAC1B4O,iBAAKK,QAAQ,KAAKG,SAASD,OAAOnP;UACpC;AACA,iBAAOsF;QACT,OAAO;AACL,iBAAO2J;QACT;SACC,IAAI;AAIPd,qBAAa/P,OAAO,CAAC6Q,UAAU3J,YAAY;AACzC,YAAI,CAACxa,YAAY8jB,KAAKtJ,OAAO,CAAC,GAAG;AAC/B,cAAI2J,UAAU;AACZ,kBAAMrP,WAAWgP,KAAKK,QAAQ,IAAI;AAClCL,iBAAKK,QAAQ,KAAKrP;AAClBgP,iBAAKtJ,OAAO,KAAK1F,WAAW6O,OAAOQ,QAAQ,EAAE3J,OAAO;UACtD;AACA,iBAAOA;QACT,OAAO;AACL,iBAAO2J;QACT;SACC,IAAI;IACT;AAGA,aAASI,aAAaT,MAAM;AAC1B,YAAMU,UAAU,CAAA;AAChB,iBAAW,CAAC9iB,KAAK5B,KAAK,KAAK0F,OAAOif,QAAQX,IAAI,GAAG;AAC/C,YAAIhkB,UAAU,GAAG;AACf0kB,kBAAQ9iB,GAAG,IAAI5B;QACjB;MACF;AACA,aAAO0kB;IACT;AAee,QAAMZ,WAAN,MAAMA,UAAS;;;;MAI5BhqB,YAAY8qB,QAAQ;AAClB,cAAMC,WAAWD,OAAOhB,uBAAuB,cAAc;AAC7D,YAAIC,SAASgB,WAAWvB,iBAAiBH;AAEzC,YAAIyB,OAAOf,QAAQ;AACjBA,mBAASe,OAAOf;QAClB;AAKA,aAAKF,SAASiB,OAAOjB;AAIrB,aAAK9e,MAAM+f,OAAO/f,OAAO3B,OAAO5C,OAAM;AAItC,aAAKsjB,qBAAqBiB,WAAW,aAAa;AAIlD,aAAKC,UAAUF,OAAOE,WAAW;AAIjC,aAAKjB,SAASA;AAId,aAAKkB,kBAAkB;MACzB;;;;;;;;;;MAWA,OAAOC,WAAW5d,OAAOnK,MAAM;AAC7B,eAAO6mB,UAAStb,WAAW;UAAE2W,cAAc/X;WAASnK,IAAI;MAC1D;;;;;;;;;;;;;;;;;;;;;MAsBA,OAAOuL,WAAW6I,KAAKpU,OAAO,CAAA,GAAI;AAChC,YAAIoU,OAAO,QAAQ,OAAOA,QAAQ,UAAU;AAC1C,gBAAM,IAAI/W,qBACP,+DACC+W,QAAQ,OAAO,SAAS,OAAOA,GAChC,EACH;QACF;AAEA,eAAO,IAAIyS,UAAS;UAClBH,QAAQjN,gBAAgBrF,KAAKyS,UAASmB,aAAa;UACnDpgB,KAAK3B,OAAOsF,WAAWvL,IAAI;UAC3B2mB,oBAAoB3mB,KAAK2mB;UACzBC,QAAQ5mB,KAAK4mB;QACf,CAAC;MACH;;;;;;;;;;;MAYA,OAAOqB,iBAAiBC,cAAc;AACpC,YAAIpZ,SAASoZ,YAAY,GAAG;AAC1B,iBAAOrB,UAASkB,WAAWG,YAAY;mBAC9BrB,UAASsB,WAAWD,YAAY,GAAG;AAC5C,iBAAOA;QACT,WAAW,OAAOA,iBAAiB,UAAU;AAC3C,iBAAOrB,UAAStb,WAAW2c,YAAY;QACzC,OAAO;AACL,gBAAM,IAAI7qB,qBACP,6BAA4B6qB,YAAa,YAAW,OAAOA,YAAa,EAC3E;QACF;MACF;;;;;;;;;;;;;;;MAgBA,OAAOE,QAAQC,MAAMroB,MAAM;AACzB,cAAM,CAACiC,MAAM,IAAIwjB,iBAAiB4C,IAAI;AACtC,YAAIpmB,QAAQ;AACV,iBAAO4kB,UAAStb,WAAWtJ,QAAQjC,IAAI;QACzC,OAAO;AACL,iBAAO6mB,UAASgB,QAAQ,cAAe,cAAaQ,IAAK,+BAA8B;QACzF;MACF;;;;;;;;;;;;;;;;;MAkBA,OAAOC,YAAYD,MAAMroB,MAAM;AAC7B,cAAM,CAACiC,MAAM,IAAI0jB,iBAAiB0C,IAAI;AACtC,YAAIpmB,QAAQ;AACV,iBAAO4kB,UAAStb,WAAWtJ,QAAQjC,IAAI;QACzC,OAAO;AACL,iBAAO6mB,UAASgB,QAAQ,cAAe,cAAaQ,IAAK,+BAA8B;QACzF;MACF;;;;;;;MAQA,OAAOR,QAAQ/qB,QAAQgV,cAAc,MAAM;AACzC,YAAI,CAAChV,QAAQ;AACX,gBAAM,IAAIO,qBAAqB,kDAAkD;QACnF;AAEA,cAAMwqB,UAAU/qB,kBAAkB+U,UAAU/U,SAAS,IAAI+U,QAAQ/U,QAAQgV,WAAW;AAEpF,YAAIjH,SAAS4G,gBAAgB;AAC3B,gBAAM,IAAIxU,qBAAqB4qB,OAAO;QACxC,OAAO;AACL,iBAAO,IAAIhB,UAAS;YAAEgB;UAAQ,CAAC;QACjC;MACF;;;;MAKA,OAAOG,cAAc5qB,MAAM;AACzB,cAAMuc,aAAa;UACjBhc,MAAM;UACNqd,OAAO;UACPmE,SAAS;UACTlE,UAAU;UACVrd,OAAO;UACP8O,QAAQ;UACR6b,MAAM;UACNrN,OAAO;UACPrd,KAAK;UACLsd,MAAM;UACN/c,MAAM;UACNyb,OAAO;UACPxb,QAAQ;UACRmL,SAAS;UACTjL,QAAQ;UACR6c,SAAS;UACT/W,aAAa;UACb6d,cAAc;UACd9kB,OAAOA,KAAKmQ,YAAW,IAAKnQ,IAAI;AAElC,YAAI,CAACuc,WAAY,OAAM,IAAIxc,iBAAiBC,IAAI;AAEhD,eAAOuc;MACT;;;;;;MAOA,OAAOwO,WAAWxS,GAAG;AACnB,eAAQA,KAAKA,EAAEmS,mBAAoB;MACrC;;;;;MAMA,IAAIhnB,SAAS;AACX,eAAO,KAAKR,UAAU,KAAKsH,IAAI9G,SAAS;MAC1C;;;;;;MAOA,IAAIgG,kBAAkB;AACpB,eAAO,KAAKxG,UAAU,KAAKsH,IAAId,kBAAkB;MACnD;;;;;;;;;;;;;;;;;;;;;;;MAwBA0hB,SAAShL,KAAKxd,OAAO,CAAA,GAAI;AAEvB,cAAMyoB,UAAU;UACd,GAAGzoB;UACHuI,OAAOvI,KAAKsY,UAAU,SAAStY,KAAKuI,UAAU;;AAEhD,eAAO,KAAKjI,UACRgd,UAAUja,OAAO,KAAKuE,KAAK6gB,OAAO,EAAErJ,yBAAyB,MAAM5B,GAAG,IACtEwI;MACN;;;;;;;;;;;;;;;MAgBA0C,QAAQ1oB,OAAO,CAAA,GAAI;AACjB,YAAI,CAAC,KAAKM,QAAS,QAAO0lB;AAE1B,cAAMvoB,KAAI6oB,eACP7c,IAAKrM,UAAS;AACb,gBAAM6e,MAAM,KAAKyK,OAAOtpB,IAAI;AAC5B,cAAI6F,YAAYgZ,GAAG,GAAG;AACpB,mBAAO;UACT;AACA,iBAAO,KAAKrU,IACT4F,gBAAgB;YAAExD,OAAO;YAAQ2e,aAAa;YAAQ,GAAG3oB;YAAM5C,MAAMA,KAAK8hB,MAAM,GAAG,EAAE;UAAE,CAAC,EACxFhf,OAAO+b,GAAG;QACf,CAAC,EACA8D,OAAQxiB,CAAAA,OAAMA,EAAC;AAElB,eAAO,KAAKqK,IACT8F,cAAc;UAAEhO,MAAM;UAAesK,OAAOhK,KAAK4oB,aAAa;UAAU,GAAG5oB;QAAK,CAAC,EACjFE,OAAOzC,EAAC;MACb;;;;;;MAOAorB,WAAW;AACT,YAAI,CAAC,KAAKvoB,QAAS,QAAO,CAAA;AAC1B,eAAO;UAAE,GAAG,KAAKomB;;MACnB;;;;;;;;;;;MAYAoC,QAAQ;AAEN,YAAI,CAAC,KAAKxoB,QAAS,QAAO;AAE1B,YAAI9C,KAAI;AACR,YAAI,KAAKwd,UAAU,EAAGxd,CAAAA,MAAK,KAAKwd,QAAQ;AACxC,YAAI,KAAKtO,WAAW,KAAK,KAAKuO,aAAa,EAAGzd,CAAAA,MAAK,KAAKkP,SAAS,KAAKuO,WAAW,IAAI;AACrF,YAAI,KAAKC,UAAU,EAAG1d,CAAAA,MAAK,KAAK0d,QAAQ;AACxC,YAAI,KAAKC,SAAS,EAAG3d,CAAAA,MAAK,KAAK2d,OAAO;AACtC,YAAI,KAAKtB,UAAU,KAAK,KAAKrQ,YAAY,KAAK,KAAK4R,YAAY,KAAK,KAAK8G,iBAAiB,EACxF1kB,CAAAA,MAAK;AACP,YAAI,KAAKqc,UAAU,EAAGrc,CAAAA,MAAK,KAAKqc,QAAQ;AACxC,YAAI,KAAKrQ,YAAY,EAAGhM,CAAAA,MAAK,KAAKgM,UAAU;AAC5C,YAAI,KAAK4R,YAAY,KAAK,KAAK8G,iBAAiB;AAG9C1kB,UAAAA,MAAKuL,QAAQ,KAAKqS,UAAU,KAAK8G,eAAe,KAAM,CAAC,IAAI;AAC7D,YAAI1kB,OAAM,IAAKA,CAAAA,MAAK;AACpB,eAAOA;MACT;;;;;;;;;;;;;;;;;MAkBAurB,UAAU/oB,OAAO,CAAA,GAAI;AACnB,YAAI,CAAC,KAAKM,QAAS,QAAO;AAE1B,cAAM0oB,SAAS,KAAKC,SAAQ;AAC5B,YAAID,SAAS,KAAKA,UAAU,MAAU,QAAO;AAE7ChpB,eAAO;UACLkpB,sBAAsB;UACtBC,iBAAiB;UACjBC,eAAe;UACflpB,QAAQ;UACR,GAAGF;UACHqpB,eAAe;;AAGjB,cAAMC,WAAW/hB,SAASwgB,WAAWiB,QAAQ;UAAE1lB,MAAM;QAAM,CAAC;AAC5D,eAAOgmB,SAASP,UAAU/oB,IAAI;MAChC;;;;;MAMAupB,SAAS;AACP,eAAO,KAAKT,MAAK;MACnB;;;;;MAMA7a,WAAW;AACT,eAAO,KAAK6a,MAAK;MACnB;;;;;MAMA,CAACU,OAAOC,IAAI,4BAA4B,CAAC,IAAI;AAC3C,YAAI,KAAKnpB,SAAS;AAChB,iBAAQ,sBAAqBsE,KAAKC,UAAU,KAAK6hB,MAAM,CAAE;QAC3D,OAAO;AACL,iBAAQ,+BAA8B,KAAKgD,aAAc;QAC3D;MACF;;;;;MAMAT,WAAW;AACT,YAAI,CAAC,KAAK3oB,QAAS,QAAOuD;AAE1B,eAAOijB,iBAAiB,KAAKF,QAAQ,KAAKF,MAAM;MAClD;;;;;MAMAiD,UAAU;AACR,eAAO,KAAKV,SAAQ;MACtB;;;;;;MAOA1f,KAAKqgB,UAAU;AACb,YAAI,CAAC,KAAKtpB,QAAS,QAAO;AAE1B,cAAM+e,MAAMwH,UAASoB,iBAAiB2B,QAAQ,GAC5C1F,SAAS,CAAA;AAEX,mBAAWrN,KAAKyP,gBAAc;AAC5B,cAAIxP,eAAeuI,IAAIqH,QAAQ7P,CAAC,KAAKC,eAAe,KAAK4P,QAAQ7P,CAAC,GAAG;AACnEqN,mBAAOrN,CAAC,IAAIwI,IAAI9d,IAAIsV,CAAC,IAAI,KAAKtV,IAAIsV,CAAC;UACrC;QACF;AAEA,eAAOxK,QAAM,MAAM;UAAEqa,QAAQxC;WAAU,IAAI;MAC7C;;;;;;MAOA2F,MAAMD,UAAU;AACd,YAAI,CAAC,KAAKtpB,QAAS,QAAO;AAE1B,cAAM+e,MAAMwH,UAASoB,iBAAiB2B,QAAQ;AAC9C,eAAO,KAAKrgB,KAAK8V,IAAIyK,OAAM,CAAE;MAC/B;;;;;;;;MASAC,SAASC,IAAI;AACX,YAAI,CAAC,KAAK1pB,QAAS,QAAO;AAC1B,cAAM4jB,SAAS,CAAA;AACf,mBAAWrN,KAAKpO,OAAOC,KAAK,KAAKge,MAAM,GAAG;AACxCxC,iBAAOrN,CAAC,IAAI0C,SAASyQ,GAAG,KAAKtD,OAAO7P,CAAC,GAAGA,CAAC,CAAC;QAC5C;AACA,eAAOxK,QAAM,MAAM;UAAEqa,QAAQxC;WAAU,IAAI;MAC7C;;;;;;;;;MAUA3iB,IAAInE,MAAM;AACR,eAAO,KAAKypB,UAASmB,cAAc5qB,IAAI,CAAC;MAC1C;;;;;;;;MASAuE,IAAI+kB,QAAQ;AACV,YAAI,CAAC,KAAKpmB,QAAS,QAAO;AAE1B,cAAM2pB,QAAQ;UAAE,GAAG,KAAKvD;UAAQ,GAAGjN,gBAAgBiN,QAAQG,UAASmB,aAAa;;AACjF,eAAO3b,QAAM,MAAM;UAAEqa,QAAQuD;QAAM,CAAC;MACtC;;;;;;MAOAC,YAAY;QAAEppB;QAAQgG;QAAiB6f;QAAoBC;UAAW,CAAA,GAAI;AACxE,cAAMhf,MAAM,KAAKA,IAAIyE,MAAM;UAAEvL;UAAQgG;QAAgB,CAAC;AACtD,cAAM9G,OAAO;UAAE4H;UAAKgf;UAAQD;;AAC5B,eAAOta,QAAM,MAAMrM,IAAI;MACzB;;;;;;;;;MAUAmqB,GAAG/sB,MAAM;AACP,eAAO,KAAKkD,UAAU,KAAKwf,QAAQ1iB,IAAI,EAAEmE,IAAInE,IAAI,IAAIyG;MACvD;;;;;;;;;;;;;;;;MAiBAumB,YAAY;AACV,YAAI,CAAC,KAAK9pB,QAAS,QAAO;AAC1B,cAAMymB,OAAO,KAAK8B,SAAQ;AAC1B3B,wBAAgB,KAAKN,QAAQG,IAAI;AACjC,eAAO1a,QAAM,MAAM;UAAEqa,QAAQK;WAAQ,IAAI;MAC3C;;;;;;MAOAsD,UAAU;AACR,YAAI,CAAC,KAAK/pB,QAAS,QAAO;AAC1B,cAAMymB,OAAOS,aAAa,KAAK4C,UAAS,EAAGE,WAAU,EAAGzB,SAAQ,CAAE;AAClE,eAAOxc,QAAM,MAAM;UAAEqa,QAAQK;WAAQ,IAAI;MAC3C;;;;;;MAOAjH,WAAW/E,OAAO;AAChB,YAAI,CAAC,KAAKza,QAAS,QAAO;AAE1B,YAAIya,MAAMjY,WAAW,GAAG;AACtB,iBAAO;QACT;AAEAiY,gBAAQA,MAAMtR,IAAKmQ,OAAMiN,UAASmB,cAAcpO,CAAC,CAAC;AAElD,cAAM2Q,QAAQ,CAAA,GACZC,cAAc,CAAA,GACdzD,OAAO,KAAK8B,SAAQ;AACtB,YAAI4B;AAEJ,mBAAW5T,KAAKyP,gBAAc;AAC5B,cAAIvL,MAAMvU,QAAQqQ,CAAC,KAAK,GAAG;AACzB4T,uBAAW5T;AAEX,gBAAI6T,MAAM;AAGV,uBAAWC,MAAMH,aAAa;AAC5BE,qBAAO,KAAK9D,OAAO+D,EAAE,EAAE9T,CAAC,IAAI2T,YAAYG,EAAE;AAC1CH,0BAAYG,EAAE,IAAI;YACpB;AAGA,gBAAI7b,SAASiY,KAAKlQ,CAAC,CAAC,GAAG;AACrB6T,qBAAO3D,KAAKlQ,CAAC;YACf;AAIA,kBAAMhU,IAAImB,KAAKqU,MAAMqS,GAAG;AACxBH,kBAAM1T,CAAC,IAAIhU;AACX2nB,wBAAY3T,CAAC,KAAK6T,MAAM,MAAO7nB,IAAI,OAAQ;qBAGlCiM,SAASiY,KAAKlQ,CAAC,CAAC,GAAG;AAC5B2T,wBAAY3T,CAAC,IAAIkQ,KAAKlQ,CAAC;UACzB;QACF;AAIA,mBAAWlS,OAAO6lB,aAAa;AAC7B,cAAIA,YAAY7lB,GAAG,MAAM,GAAG;AAC1B4lB,kBAAME,QAAQ,KACZ9lB,QAAQ8lB,WAAWD,YAAY7lB,GAAG,IAAI6lB,YAAY7lB,GAAG,IAAI,KAAKiiB,OAAO6D,QAAQ,EAAE9lB,GAAG;UACtF;QACF;AAEAuiB,wBAAgB,KAAKN,QAAQ2D,KAAK;AAClC,eAAOle,QAAM,MAAM;UAAEqa,QAAQ6D;WAAS,IAAI;MAC5C;;;;;;MAOAD,aAAa;AACX,YAAI,CAAC,KAAKhqB,QAAS,QAAO;AAC1B,eAAO,KAAKwf,QACV,SACA,UACA,SACA,QACA,SACA,WACA,WACA,cACF;MACF;;;;;;MAOAgK,SAAS;AACP,YAAI,CAAC,KAAKxpB,QAAS,QAAO;AAC1B,cAAMsqB,UAAU,CAAA;AAChB,mBAAW/T,KAAKpO,OAAOC,KAAK,KAAKge,MAAM,GAAG;AACxCkE,kBAAQ/T,CAAC,IAAI,KAAK6P,OAAO7P,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK6P,OAAO7P,CAAC;QACxD;AACA,eAAOxK,QAAM,MAAM;UAAEqa,QAAQkE;WAAW,IAAI;MAC9C;;;;;MAMA,IAAI5P,QAAQ;AACV,eAAO,KAAK1a,UAAU,KAAKomB,OAAO1L,SAAS,IAAInX;MACjD;;;;;MAMA,IAAIoX,WAAW;AACb,eAAO,KAAK3a,UAAU,KAAKomB,OAAOzL,YAAY,IAAIpX;MACpD;;;;;MAMA,IAAI6I,SAAS;AACX,eAAO,KAAKpM,UAAU,KAAKomB,OAAOha,UAAU,IAAI7I;MAClD;;;;;MAMA,IAAIqX,QAAQ;AACV,eAAO,KAAK5a,UAAU,KAAKomB,OAAOxL,SAAS,IAAIrX;MACjD;;;;;MAMA,IAAIsX,OAAO;AACT,eAAO,KAAK7a,UAAU,KAAKomB,OAAOvL,QAAQ,IAAItX;MAChD;;;;;MAMA,IAAIgW,QAAQ;AACV,eAAO,KAAKvZ,UAAU,KAAKomB,OAAO7M,SAAS,IAAIhW;MACjD;;;;;MAMA,IAAI2F,UAAU;AACZ,eAAO,KAAKlJ,UAAU,KAAKomB,OAAOld,WAAW,IAAI3F;MACnD;;;;;MAMA,IAAIuX,UAAU;AACZ,eAAO,KAAK9a,UAAU,KAAKomB,OAAOtL,WAAW,IAAIvX;MACnD;;;;;MAMA,IAAIqe,eAAe;AACjB,eAAO,KAAK5hB,UAAU,KAAKomB,OAAOxE,gBAAgB,IAAIre;MACxD;;;;;;MAOA,IAAIvD,UAAU;AACZ,eAAO,KAAKunB,YAAY;MAC1B;;;;;MAMA,IAAI6B,gBAAgB;AAClB,eAAO,KAAK7B,UAAU,KAAKA,QAAQ/qB,SAAS;MAC9C;;;;;MAMA,IAAI+tB,qBAAqB;AACvB,eAAO,KAAKhD,UAAU,KAAKA,QAAQ/V,cAAc;MACnD;;;;;;;MAQA1R,OAAO4N,OAAO;AACZ,YAAI,CAAC,KAAK1N,WAAW,CAAC0N,MAAM1N,SAAS;AACnC,iBAAO;QACT;AAEA,YAAI,CAAC,KAAKsH,IAAIxH,OAAO4N,MAAMpG,GAAG,GAAG;AAC/B,iBAAO;QACT;AAEA,iBAASkjB,GAAGC,IAAIC,IAAI;AAElB,cAAID,OAAOvpB,UAAaupB,OAAO,EAAG,QAAOC,OAAOxpB,UAAawpB,OAAO;AACpE,iBAAOD,OAAOC;QAChB;AAEA,mBAAWpR,KAAK0M,gBAAc;AAC5B,cAAI,CAACwE,GAAG,KAAKpE,OAAO9M,CAAC,GAAG5L,MAAM0Y,OAAO9M,CAAC,CAAC,GAAG;AACxC,mBAAO;UACT;QACF;AACA,eAAO;MACT;IACF;ACr9BA,QAAMoM,YAAU;AAGhB,aAASiF,iBAAiB3M,OAAOE,KAAK;AACpC,UAAI,CAACF,SAAS,CAACA,MAAMhe,SAAS;AAC5B,eAAO4qB,SAASrD,QAAQ,0BAA0B;iBACzC,CAACrJ,OAAO,CAACA,IAAIle,SAAS;AAC/B,eAAO4qB,SAASrD,QAAQ,wBAAwB;MAClD,WAAWrJ,MAAMF,OAAO;AACtB,eAAO4M,SAASrD,QACd,oBACC,qEAAoEvJ,MAAMwK,MAAK,CAAG,YAAWtK,IAAIsK,MAAK,CAAG,EAC5G;MACF,OAAO;AACL,eAAO;MACT;IACF;AAce,QAAMoC,WAAN,MAAMA,UAAS;;;;MAI5BruB,YAAY8qB,QAAQ;AAIlB,aAAKnqB,IAAImqB,OAAOrJ;AAIhB,aAAK3a,IAAIgkB,OAAOnJ;AAIhB,aAAKqJ,UAAUF,OAAOE,WAAW;AAIjC,aAAKsD,kBAAkB;MACzB;;;;;;;MAQA,OAAOtD,QAAQ/qB,QAAQgV,cAAc,MAAM;AACzC,YAAI,CAAChV,QAAQ;AACX,gBAAM,IAAIO,qBAAqB,kDAAkD;QACnF;AAEA,cAAMwqB,UAAU/qB,kBAAkB+U,UAAU/U,SAAS,IAAI+U,QAAQ/U,QAAQgV,WAAW;AAEpF,YAAIjH,SAAS4G,gBAAgB;AAC3B,gBAAM,IAAIzU,qBAAqB6qB,OAAO;QACxC,OAAO;AACL,iBAAO,IAAIqD,UAAS;YAAErD;UAAQ,CAAC;QACjC;MACF;;;;;;;MAQA,OAAOuD,cAAc9M,OAAOE,KAAK;AAC/B,cAAM6M,aAAaC,iBAAiBhN,KAAK,GACvCiN,WAAWD,iBAAiB9M,GAAG;AAEjC,cAAMgN,gBAAgBP,iBAAiBI,YAAYE,QAAQ;AAE3D,YAAIC,iBAAiB,MAAM;AACzB,iBAAO,IAAIN,UAAS;YAClB5M,OAAO+M;YACP7M,KAAK+M;UACP,CAAC;QACH,OAAO;AACL,iBAAOC;QACT;MACF;;;;;;;MAQA,OAAOC,MAAMnN,OAAOsL,UAAU;AAC5B,cAAMvK,MAAMwH,SAASoB,iBAAiB2B,QAAQ,GAC5CtiB,KAAKgkB,iBAAiBhN,KAAK;AAC7B,eAAO4M,UAASE,cAAc9jB,IAAIA,GAAGiC,KAAK8V,GAAG,CAAC;MAChD;;;;;;;MAQA,OAAOqM,OAAOlN,KAAKoL,UAAU;AAC3B,cAAMvK,MAAMwH,SAASoB,iBAAiB2B,QAAQ,GAC5CtiB,KAAKgkB,iBAAiB9M,GAAG;AAC3B,eAAO0M,UAASE,cAAc9jB,GAAGuiB,MAAMxK,GAAG,GAAG/X,EAAE;MACjD;;;;;;;;;MAUA,OAAO8gB,QAAQC,MAAMroB,MAAM;AACzB,cAAM,CAACxC,IAAGmG,CAAC,KAAK0kB,QAAQ,IAAI9X,MAAM,KAAK,CAAC;AACxC,YAAI/S,MAAKmG,GAAG;AACV,cAAI2a,OAAOqN;AACX,cAAI;AACFrN,oBAAQ/W,SAAS6gB,QAAQ5qB,IAAGwC,IAAI;AAChC2rB,2BAAerN,MAAMhe;mBACdqD,IAAG;AACVgoB,2BAAe;UACjB;AAEA,cAAInN,KAAKoN;AACT,cAAI;AACFpN,kBAAMjX,SAAS6gB,QAAQzkB,GAAG3D,IAAI;AAC9B4rB,yBAAapN,IAAIle;mBACVqD,IAAG;AACVioB,yBAAa;UACf;AAEA,cAAID,gBAAgBC,YAAY;AAC9B,mBAAOV,UAASE,cAAc9M,OAAOE,GAAG;UAC1C;AAEA,cAAImN,cAAc;AAChB,kBAAMtM,MAAMwH,SAASuB,QAAQzkB,GAAG3D,IAAI;AACpC,gBAAIqf,IAAI/e,SAAS;AACf,qBAAO4qB,UAASO,MAAMnN,OAAOe,GAAG;YAClC;qBACSuM,YAAY;AACrB,kBAAMvM,MAAMwH,SAASuB,QAAQ5qB,IAAGwC,IAAI;AACpC,gBAAIqf,IAAI/e,SAAS;AACf,qBAAO4qB,UAASQ,OAAOlN,KAAKa,GAAG;YACjC;UACF;QACF;AACA,eAAO6L,UAASrD,QAAQ,cAAe,cAAaQ,IAAK,+BAA8B;MACzF;;;;;;MAOA,OAAOwD,WAAWlW,GAAG;AACnB,eAAQA,KAAKA,EAAEwV,mBAAoB;MACrC;;;;;MAMA,IAAI7M,QAAQ;AACV,eAAO,KAAKhe,UAAU,KAAK9C,IAAI;MACjC;;;;;MAMA,IAAIghB,MAAM;AACR,eAAO,KAAKle,UAAU,KAAKqD,IAAI;MACjC;;;;;MAMA,IAAImoB,eAAe;AACjB,eAAO,KAAKxrB,UAAW,KAAKqD,IAAI,KAAKA,EAAEkmB,MAAM,CAAC,IAAI,OAAQ;MAC5D;;;;;MAMA,IAAIvpB,UAAU;AACZ,eAAO,KAAKopB,kBAAkB;MAChC;;;;;MAMA,IAAIA,gBAAgB;AAClB,eAAO,KAAK7B,UAAU,KAAKA,QAAQ/qB,SAAS;MAC9C;;;;;MAMA,IAAI+tB,qBAAqB;AACvB,eAAO,KAAKhD,UAAU,KAAKA,QAAQ/V,cAAc;MACnD;;;;;;MAOAhP,OAAO1F,OAAO,gBAAgB;AAC5B,eAAO,KAAKkD,UAAU,KAAKyrB,WAAW,GAAG,CAAC3uB,IAAI,CAAC,EAAEmE,IAAInE,IAAI,IAAIyG;MAC/D;;;;;;;;;;MAWAsG,MAAM/M,OAAO,gBAAgB4C,MAAM;AACjC,YAAI,CAAC,KAAKM,QAAS,QAAOuD;AAC1B,cAAMya,QAAQ,KAAKA,MAAM0N,QAAQ5uB,MAAM4C,IAAI;AAC3C,YAAIwe;AACJ,YAAIxe,QAAI,QAAJA,KAAMisB,gBAAgB;AACxBzN,gBAAM,KAAKA,IAAI0L,YAAY;YAAEppB,QAAQwd,MAAMxd;UAAO,CAAC;QACrD,OAAO;AACL0d,gBAAM,KAAKA;QACb;AACAA,cAAMA,IAAIwN,QAAQ5uB,MAAM4C,IAAI;AAC5B,eAAOgE,KAAKuE,MAAMiW,IAAI0N,KAAK5N,OAAOlhB,IAAI,EAAEmE,IAAInE,IAAI,CAAC,KAAKohB,IAAImL,QAAO,MAAO,KAAKnL,IAAImL,QAAO;MAC1F;;;;;;MAOAwC,QAAQ/uB,MAAM;AACZ,eAAO,KAAKkD,UAAU,KAAK8rB,QAAO,KAAM,KAAKzoB,EAAEkmB,MAAM,CAAC,EAAEsC,QAAQ,KAAK3uB,GAAGJ,IAAI,IAAI;MAClF;;;;;MAMAgvB,UAAU;AACR,eAAO,KAAK5uB,EAAEmsB,QAAO,MAAO,KAAKhmB,EAAEgmB,QAAO;MAC5C;;;;;;MAOA0C,QAAQ/C,UAAU;AAChB,YAAI,CAAC,KAAKhpB,QAAS,QAAO;AAC1B,eAAO,KAAK9C,IAAI8rB;MAClB;;;;;;MAOAgD,SAAShD,UAAU;AACjB,YAAI,CAAC,KAAKhpB,QAAS,QAAO;AAC1B,eAAO,KAAKqD,KAAK2lB;MACnB;;;;;;MAOAiD,SAASjD,UAAU;AACjB,YAAI,CAAC,KAAKhpB,QAAS,QAAO;AAC1B,eAAO,KAAK9C,KAAK8rB,YAAY,KAAK3lB,IAAI2lB;MACxC;;;;;;;;MASA3nB,IAAI;QAAE2c;QAAOE;UAAQ,CAAA,GAAI;AACvB,YAAI,CAAC,KAAKle,QAAS,QAAO;AAC1B,eAAO4qB,UAASE,cAAc9M,SAAS,KAAK9gB,GAAGghB,OAAO,KAAK7a,CAAC;MAC9D;;;;;;MAOA6oB,WAAWC,WAAW;AACpB,YAAI,CAAC,KAAKnsB,QAAS,QAAO,CAAA;AAC1B,cAAMosB,SAASD,UACVhjB,IAAI6hB,gBAAgB,EACpBvL,OAAQ5N,OAAM,KAAKoa,SAASpa,CAAC,CAAC,EAC9Bwa,KAAK,CAAC/V,GAAGgW,MAAMhW,EAAEqS,SAAQ,IAAK2D,EAAE3D,SAAQ,CAAE,GAC7C9b,UAAU,CAAA;AACZ,YAAI;UAAE3P,GAAAA;QAAE,IAAI,MACVqF,IAAI;AAEN,eAAOrF,KAAI,KAAKmG,GAAG;AACjB,gBAAMkpB,QAAQH,OAAO7pB,CAAC,KAAK,KAAKc,GAC9B8S,OAAO,CAACoW,QAAQ,CAAC,KAAKlpB,IAAI,KAAKA,IAAIkpB;AACrC1f,kBAAQ1F,KAAKyjB,UAASE,cAAc5tB,IAAGiZ,IAAI,CAAC;AAC5CjZ,UAAAA,KAAIiZ;AACJ5T,eAAK;QACP;AAEA,eAAOsK;MACT;;;;;;;MAQA2f,QAAQlD,UAAU;AAChB,cAAMvK,MAAMwH,SAASoB,iBAAiB2B,QAAQ;AAE9C,YAAI,CAAC,KAAKtpB,WAAW,CAAC+e,IAAI/e,WAAW+e,IAAI8K,GAAG,cAAc,MAAM,GAAG;AACjE,iBAAO,CAAA;QACT;AAEA,YAAI;UAAE3sB,GAAAA;QAAE,IAAI,MACVuvB,MAAM,GACNtW;AAEF,cAAMtJ,UAAU,CAAA;AAChB,eAAO3P,KAAI,KAAKmG,GAAG;AACjB,gBAAMkpB,QAAQ,KAAKvO,MAAM/U,KAAK8V,IAAI0K,SAAUxS,OAAMA,IAAIwV,GAAG,CAAC;AAC1DtW,iBAAO,CAACoW,QAAQ,CAAC,KAAKlpB,IAAI,KAAKA,IAAIkpB;AACnC1f,kBAAQ1F,KAAKyjB,UAASE,cAAc5tB,IAAGiZ,IAAI,CAAC;AAC5CjZ,UAAAA,KAAIiZ;AACJsW,iBAAO;QACT;AAEA,eAAO5f;MACT;;;;;;MAOA6f,cAAcC,eAAe;AAC3B,YAAI,CAAC,KAAK3sB,QAAS,QAAO,CAAA;AAC1B,eAAO,KAAKwsB,QAAQ,KAAKhqB,OAAM,IAAKmqB,aAAa,EAAE/N,MAAM,GAAG+N,aAAa;MAC3E;;;;;;MAOAC,SAASlf,OAAO;AACd,eAAO,KAAKrK,IAAIqK,MAAMxQ,KAAK,KAAKA,IAAIwQ,MAAMrK;MAC5C;;;;;;MAOAwpB,WAAWnf,OAAO;AAChB,YAAI,CAAC,KAAK1N,QAAS,QAAO;AAC1B,eAAO,CAAC,KAAKqD,MAAM,CAACqK,MAAMxQ;MAC5B;;;;;;MAOA4vB,SAASpf,OAAO;AACd,YAAI,CAAC,KAAK1N,QAAS,QAAO;AAC1B,eAAO,CAAC0N,MAAMrK,MAAM,CAAC,KAAKnG;MAC5B;;;;;;MAOA6vB,QAAQrf,OAAO;AACb,YAAI,CAAC,KAAK1N,QAAS,QAAO;AAC1B,eAAO,KAAK9C,KAAKwQ,MAAMxQ,KAAK,KAAKmG,KAAKqK,MAAMrK;MAC9C;;;;;;MAOAvD,OAAO4N,OAAO;AACZ,YAAI,CAAC,KAAK1N,WAAW,CAAC0N,MAAM1N,SAAS;AACnC,iBAAO;QACT;AAEA,eAAO,KAAK9C,EAAE4C,OAAO4N,MAAMxQ,CAAC,KAAK,KAAKmG,EAAEvD,OAAO4N,MAAMrK,CAAC;MACxD;;;;;;;;MASA2pB,aAAatf,OAAO;AAClB,YAAI,CAAC,KAAK1N,QAAS,QAAO;AAC1B,cAAM9C,KAAI,KAAKA,IAAIwQ,MAAMxQ,IAAI,KAAKA,IAAIwQ,MAAMxQ,GAC1CmG,IAAI,KAAKA,IAAIqK,MAAMrK,IAAI,KAAKA,IAAIqK,MAAMrK;AAExC,YAAInG,MAAKmG,GAAG;AACV,iBAAO;QACT,OAAO;AACL,iBAAOunB,UAASE,cAAc5tB,IAAGmG,CAAC;QACpC;MACF;;;;;;;MAQA4pB,MAAMvf,OAAO;AACX,YAAI,CAAC,KAAK1N,QAAS,QAAO;AAC1B,cAAM9C,KAAI,KAAKA,IAAIwQ,MAAMxQ,IAAI,KAAKA,IAAIwQ,MAAMxQ,GAC1CmG,IAAI,KAAKA,IAAIqK,MAAMrK,IAAI,KAAKA,IAAIqK,MAAMrK;AACxC,eAAOunB,UAASE,cAAc5tB,IAAGmG,CAAC;MACpC;;;;;;;;;;MAWA,OAAO6pB,MAAMC,WAAW;AACtB,cAAM,CAAC9N,OAAO+N,KAAK,IAAID,UACpBd,KAAK,CAAC/V,GAAGgW,MAAMhW,EAAEpZ,IAAIovB,EAAEpvB,CAAC,EACxB+Y,OACC,CAAC,CAACoX,OAAOlQ,OAAO,GAAGuE,SAAS;AAC1B,cAAI,CAACvE,SAAS;AACZ,mBAAO,CAACkQ,OAAO3L,IAAI;UACrB,WAAWvE,QAAQyP,SAASlL,IAAI,KAAKvE,QAAQ0P,WAAWnL,IAAI,GAAG;AAC7D,mBAAO,CAAC2L,OAAOlQ,QAAQ8P,MAAMvL,IAAI,CAAC;UACpC,OAAO;AACL,mBAAO,CAAC2L,MAAM/N,OAAO,CAACnC,OAAO,CAAC,GAAGuE,IAAI;UACvC;QACF,GACA,CAAC,CAAA,GAAI,IAAI,CACX;AACF,YAAI0L,OAAO;AACT/N,gBAAMlY,KAAKimB,KAAK;QAClB;AACA,eAAO/N;MACT;;;;;;MAOA,OAAOiO,IAAIH,WAAW;AACpB,YAAInP,QAAQ,MACVuP,eAAe;AACjB,cAAM1gB,UAAU,CAAA,GACd2gB,OAAOL,UAAUhkB,IAAK5G,OAAM,CAC1B;UAAEkrB,MAAMlrB,EAAErF;UAAGkC,MAAM;QAAI,GACvB;UAAEquB,MAAMlrB,EAAEc;UAAGjE,MAAM;QAAI,CAAC,CACzB,GACDsuB,YAAY/X,MAAMJ,UAAU+J,OAAO,GAAGkO,IAAI,GAC1C1X,MAAM4X,UAAUrB,KAAK,CAAC/V,GAAGgW,MAAMhW,EAAEmX,OAAOnB,EAAEmB,IAAI;AAEhD,mBAAWlrB,KAAKuT,KAAK;AACnByX,0BAAgBhrB,EAAEnD,SAAS,MAAM,IAAI;AAErC,cAAImuB,iBAAiB,GAAG;AACtBvP,oBAAQzb,EAAEkrB;UACZ,OAAO;AACL,gBAAIzP,SAAS,CAACA,UAAU,CAACzb,EAAEkrB,MAAM;AAC/B5gB,sBAAQ1F,KAAKyjB,UAASE,cAAc9M,OAAOzb,EAAEkrB,IAAI,CAAC;YACpD;AAEAzP,oBAAQ;UACV;QACF;AAEA,eAAO4M,UAASsC,MAAMrgB,OAAO;MAC/B;;;;;;MAOA8gB,cAAcR,WAAW;AACvB,eAAOvC,UAAS0C,IAAI,CAAC,IAAI,EAAEhO,OAAO6N,SAAS,CAAC,EACzChkB,IAAK5G,OAAM,KAAKyqB,aAAazqB,CAAC,CAAC,EAC/Bkd,OAAQld,OAAMA,KAAK,CAACA,EAAEupB,QAAO,CAAE;MACpC;;;;;MAMAne,WAAW;AACT,YAAI,CAAC,KAAK3N,QAAS,QAAO0lB;AAC1B,eAAQ,IAAG,KAAKxoB,EAAEsrB,MAAK,CAAG,MAAK,KAAKnlB,EAAEmlB,MAAK,CAAG;MAChD;;;;;MAMA,CAACU,OAAOC,IAAI,4BAA4B,CAAC,IAAI;AAC3C,YAAI,KAAKnpB,SAAS;AAChB,iBAAQ,qBAAoB,KAAK9C,EAAEsrB,MAAK,CAAG,UAAS,KAAKnlB,EAAEmlB,MAAK,CAAG;QACrE,OAAO;AACL,iBAAQ,+BAA8B,KAAKY,aAAc;QAC3D;MACF;;;;;;;;;;;;;;;;;;;MAoBAwE,eAAenQ,aAAa3B,YAAoBpc,OAAO,CAAA,GAAI;AACzD,eAAO,KAAKM,UACRgd,UAAUja,OAAO,KAAK7F,EAAEoK,IAAIyE,MAAMrM,IAAI,GAAG+d,UAAU,EAAEK,eAAe,IAAI,IACxE4H;MACN;;;;;;;MAQA8C,MAAM9oB,MAAM;AACV,YAAI,CAAC,KAAKM,QAAS,QAAO0lB;AAC1B,eAAQ,GAAE,KAAKxoB,EAAEsrB,MAAM9oB,IAAI,CAAE,IAAG,KAAK2D,EAAEmlB,MAAM9oB,IAAI,CAAE;MACrD;;;;;;;MAQAmuB,YAAY;AACV,YAAI,CAAC,KAAK7tB,QAAS,QAAO0lB;AAC1B,eAAQ,GAAE,KAAKxoB,EAAE2wB,UAAS,CAAG,IAAG,KAAKxqB,EAAEwqB,UAAS,CAAG;MACrD;;;;;;;;MASApF,UAAU/oB,MAAM;AACd,YAAI,CAAC,KAAKM,QAAS,QAAO0lB;AAC1B,eAAQ,GAAE,KAAKxoB,EAAEurB,UAAU/oB,IAAI,CAAE,IAAG,KAAK2D,EAAEolB,UAAU/oB,IAAI,CAAE;MAC7D;;;;;;;;;;;;MAaAwoB,SAAS4F,YAAY;QAAEC,YAAY;UAAU,CAAA,GAAI;AAC/C,YAAI,CAAC,KAAK/tB,QAAS,QAAO0lB;AAC1B,eAAQ,GAAE,KAAKxoB,EAAEgrB,SAAS4F,UAAU,CAAE,GAAEC,SAAU,GAAE,KAAK1qB,EAAE6kB,SAAS4F,UAAU,CAAE;MAClF;;;;;;;;;;;;;MAcArC,WAAW3uB,MAAM4C,MAAM;AACrB,YAAI,CAAC,KAAKM,SAAS;AACjB,iBAAOumB,SAASgB,QAAQ,KAAK6B,aAAa;QAC5C;AACA,eAAO,KAAK/lB,EAAEuoB,KAAK,KAAK1uB,GAAGJ,MAAM4C,IAAI;MACvC;;;;;;;;MASAsuB,aAAaC,OAAO;AAClB,eAAOrD,UAASE,cAAcmD,MAAM,KAAK/wB,CAAC,GAAG+wB,MAAM,KAAK5qB,CAAC,CAAC;MAC5D;IACF;AChpBe,QAAM6qB,OAAN,MAAW;;;;;;MAMxB,OAAOC,OAAOnrB,OAAOuH,SAAS8D,aAAa;AACzC,cAAM+f,QAAQnnB,SAASgK,IAAG,EAAGjI,QAAQhG,IAAI,EAAE3B,IAAI;UAAE/D,OAAO;QAAG,CAAC;AAE5D,eAAO,CAAC0F,KAAKzD,eAAe6uB,MAAMvuB,WAAWuuB,MAAM/sB,IAAI;UAAE/D,OAAO;SAAG,EAAEuC;MACvE;;;;;;MAOA,OAAOwuB,gBAAgBrrB,MAAM;AAC3B,eAAOF,SAASM,YAAYJ,IAAI;MAClC;;;;;;;;;;;;;;;MAgBA,OAAOmL,cAAcC,OAAO;AAC1B,eAAOD,cAAcC,OAAO7D,SAAS8D,WAAW;MAClD;;;;;;;;MASA,OAAOd,eAAe;QAAE/M,SAAS;QAAM8tB,SAAS;UAAS,CAAA,GAAI;AAC3D,gBAAQA,UAAU3oB,OAAO5C,OAAOvC,MAAM,GAAG+M,eAAc;MACzD;;;;;;;;;MAUA,OAAOghB,0BAA0B;QAAE/tB,SAAS;QAAM8tB,SAAS;UAAS,CAAA,GAAI;AACtE,gBAAQA,UAAU3oB,OAAO5C,OAAOvC,MAAM,GAAGgN,sBAAqB;MAChE;;;;;;;;MASA,OAAOghB,mBAAmB;QAAEhuB,SAAS;QAAM8tB,SAAS;UAAS,CAAA,GAAI;AAE/D,gBAAQA,UAAU3oB,OAAO5C,OAAOvC,MAAM,GAAGiN,eAAc,EAAGmR,MAAK;MACjE;;;;;;;;;;;;;;;;;;MAmBA,OAAOxS,OACL5J,SAAS,QACT;QAAEhC,SAAS;QAAMgG,kBAAkB;QAAM8nB,SAAS;QAAM3nB,iBAAiB;UAAc,CAAA,GACvF;AACA,gBAAQ2nB,UAAU3oB,OAAO5C,OAAOvC,QAAQgG,iBAAiBG,cAAc,GAAGyF,OAAO5J,MAAM;MACzF;;;;;;;;;;;;;;MAeA,OAAOisB,aACLjsB,SAAS,QACT;QAAEhC,SAAS;QAAMgG,kBAAkB;QAAM8nB,SAAS;QAAM3nB,iBAAiB;UAAc,CAAA,GACvF;AACA,gBAAQ2nB,UAAU3oB,OAAO5C,OAAOvC,QAAQgG,iBAAiBG,cAAc,GAAGyF,OAAO5J,QAAQ,IAAI;MAC/F;;;;;;;;;;;;;;;MAgBA,OAAO+J,SAAS/J,SAAS,QAAQ;QAAEhC,SAAS;QAAMgG,kBAAkB;QAAM8nB,SAAS;UAAS,CAAA,GAAI;AAC9F,gBAAQA,UAAU3oB,OAAO5C,OAAOvC,QAAQgG,iBAAiB,IAAI,GAAG+F,SAAS/J,MAAM;MACjF;;;;;;;;;;;;;MAcA,OAAOksB,eACLlsB,SAAS,QACT;QAAEhC,SAAS;QAAMgG,kBAAkB;QAAM8nB,SAAS;UAAS,CAAA,GAC3D;AACA,gBAAQA,UAAU3oB,OAAO5C,OAAOvC,QAAQgG,iBAAiB,IAAI,GAAG+F,SAAS/J,QAAQ,IAAI;MACvF;;;;;;;;;MAUA,OAAOgK,UAAU;QAAEhM,SAAS;UAAS,CAAA,GAAI;AACvC,eAAOmF,OAAO5C,OAAOvC,MAAM,EAAEgM,UAAS;MACxC;;;;;;;;;;;MAYA,OAAOC,KAAKjK,SAAS,SAAS;QAAEhC,SAAS;UAAS,CAAA,GAAI;AACpD,eAAOmF,OAAO5C,OAAOvC,QAAQ,MAAM,SAAS,EAAEiM,KAAKjK,MAAM;MAC3D;;;;;;;;;;MAWA,OAAOmsB,WAAW;AAChB,eAAO;UAAEC,UAAUjlB,YAAW;UAAIklB,YAAYvhB,kBAAiB;;MACjE;IACF;AC1MA,aAASwhB,QAAQC,SAASC,OAAO;AAC/B,YAAMC,cAAejoB,QAAOA,GAAGkoB,MAAM,GAAG;QAAEC,eAAe;OAAM,EAAEzD,QAAQ,KAAK,EAAErC,QAAO,GACrFtiB,KAAKkoB,YAAYD,KAAK,IAAIC,YAAYF,OAAO;AAC/C,aAAOrrB,KAAKuE,MAAMse,SAASkB,WAAW1gB,EAAE,EAAE8iB,GAAG,MAAM,CAAC;IACtD;AAEA,aAASuF,eAAejP,QAAQ6O,OAAOvU,OAAO;AAC5C,YAAM4U,UAAU,CACd,CAAC,SAAS,CAAC/Y,GAAGgW,MAAMA,EAAEjvB,OAAOiZ,EAAEjZ,IAAI,GACnC,CAAC,YAAY,CAACiZ,GAAGgW,MAAMA,EAAEzN,UAAUvI,EAAEuI,WAAWyN,EAAEjvB,OAAOiZ,EAAEjZ,QAAQ,CAAC,GACpE,CAAC,UAAU,CAACiZ,GAAGgW,MAAMA,EAAEhvB,QAAQgZ,EAAEhZ,SAASgvB,EAAEjvB,OAAOiZ,EAAEjZ,QAAQ,EAAE,GAC/D,CACE,SACA,CAACiZ,GAAGgW,MAAM;AACR,cAAMzR,OAAOiU,QAAQxY,GAAGgW,CAAC;AACzB,gBAAQzR,OAAQA,OAAO,KAAM;MAC/B,CAAC,GAEH,CAAC,QAAQiU,OAAO,CAAC;AAGnB,YAAMjiB,UAAU,CAAA;AAChB,YAAMkiB,UAAU5O;AAChB,UAAImP,aAAaC;AAUjB,iBAAW,CAACzyB,MAAM0yB,MAAM,KAAKH,SAAS;AACpC,YAAI5U,MAAMvU,QAAQpJ,IAAI,KAAK,GAAG;AAC5BwyB,wBAAcxyB;AAEd+P,kBAAQ/P,IAAI,IAAI0yB,OAAOrP,QAAQ6O,KAAK;AACpCO,sBAAYR,QAAQ9lB,KAAK4D,OAAO;AAEhC,cAAI0iB,YAAYP,OAAO;AAErBniB,oBAAQ/P,IAAI;AACZqjB,qBAAS4O,QAAQ9lB,KAAK4D,OAAO;AAK7B,gBAAIsT,SAAS6O,OAAO;AAElBO,0BAAYpP;AAEZtT,sBAAQ/P,IAAI;AACZqjB,uBAAS4O,QAAQ9lB,KAAK4D,OAAO;YAC/B;UACF,OAAO;AACLsT,qBAASoP;UACX;QACF;MACF;AAEA,aAAO,CAACpP,QAAQtT,SAAS0iB,WAAWD,WAAW;IACjD;AAEe,aAAA,KAAUP,SAASC,OAAOvU,OAAO/a,MAAM;AACpD,UAAI,CAACygB,QAAQtT,SAAS0iB,WAAWD,WAAW,IAAIF,eAAeL,SAASC,OAAOvU,KAAK;AAEpF,YAAMgV,kBAAkBT,QAAQ7O;AAEhC,YAAMuP,kBAAkBjV,MAAMgF,OAC3BnG,OAAM,CAAC,SAAS,WAAW,WAAW,cAAc,EAAEpT,QAAQoT,CAAC,KAAK,CACvE;AAEA,UAAIoW,gBAAgBltB,WAAW,GAAG;AAChC,YAAI+sB,YAAYP,OAAO;AACrBO,sBAAYpP,OAAOlX,KAAK;YAAE,CAACqmB,WAAW,GAAG;UAAE,CAAC;QAC9C;AAEA,YAAIC,cAAcpP,QAAQ;AACxBtT,kBAAQyiB,WAAW,KAAKziB,QAAQyiB,WAAW,KAAK,KAAKG,mBAAmBF,YAAYpP;QACtF;MACF;AAEA,YAAMmJ,WAAW/C,SAAStb,WAAW4B,SAASnN,IAAI;AAElD,UAAIgwB,gBAAgBltB,SAAS,GAAG;AAC9B,eAAO+jB,SAASkB,WAAWgI,iBAAiB/vB,IAAI,EAC7C8f,QAAQ,GAAGkQ,eAAe,EAC1BzmB,KAAKqgB,QAAQ;MAClB,OAAO;AACL,eAAOA;MACT;IACF;ACtFA,QAAMqG,cAAc;AAEpB,aAASC,QAAQ7e,OAAO8e,OAAQttB,OAAMA,GAAG;AACvC,aAAO;QAAEwO;QAAO+e,OAAOA,CAAC,CAAC5yB,EAAC,MAAM2yB,KAAK3f,YAAYhT,EAAC,CAAC;;IACrD;AAEA,QAAM6yB,OAAOC,OAAOC,aAAa,GAAG;AACpC,QAAMC,cAAe,KAAIH,IAAK;AAC9B,QAAMI,oBAAoB,IAAInf,OAAOkf,aAAa,GAAG;AAErD,aAASE,aAAalzB,IAAG;AAGvB,aAAOA,GAAEwE,QAAQ,OAAO,MAAM,EAAEA,QAAQyuB,mBAAmBD,WAAW;IACxE;AAEA,aAASG,qBAAqBnzB,IAAG;AAC/B,aAAOA,GACJwE,QAAQ,OAAO,EAAE,EACjBA,QAAQyuB,mBAAmB,GAAG,EAC9BljB,YAAW;IAChB;AAEA,aAASqjB,MAAMC,SAASC,YAAY;AAClC,UAAID,YAAY,MAAM;AACpB,eAAO;MACT,OAAO;AACL,eAAO;UACLxf,OAAOC,OAAOuf,QAAQpnB,IAAIinB,YAAY,EAAEhnB,KAAK,GAAG,CAAC;UACjD0mB,OAAOA,CAAC,CAAC5yB,EAAC,MACRqzB,QAAQ9d,UAAWlQ,OAAM8tB,qBAAqBnzB,EAAC,MAAMmzB,qBAAqB9tB,CAAC,CAAC,IAAIiuB;;MAEtF;IACF;AAEA,aAAS3wB,OAAOkR,OAAO0f,QAAQ;AAC7B,aAAO;QAAE1f;QAAO+e,OAAOA,CAAC,CAAA,EAAGY,GAAG1jB,CAAC,MAAMiB,aAAayiB,GAAG1jB,CAAC;QAAGyjB;;IAC3D;AAEA,aAASE,OAAO5f,OAAO;AACrB,aAAO;QAAEA;QAAO+e,OAAOA,CAAC,CAAC5yB,EAAC,MAAMA;;IAClC;AAEA,aAAS0zB,YAAYnuB,OAAO;AAC1B,aAAOA,MAAMf,QAAQ,+BAA+B,MAAM;IAC5D;AAMA,aAASmvB,aAAapV,OAAOnU,KAAK;AAChC,YAAMwpB,MAAMngB,WAAWrJ,GAAG,GACxBypB,MAAMpgB,WAAWrJ,KAAK,KAAK,GAC3B0pB,QAAQrgB,WAAWrJ,KAAK,KAAK,GAC7B2pB,OAAOtgB,WAAWrJ,KAAK,KAAK,GAC5B4pB,MAAMvgB,WAAWrJ,KAAK,KAAK,GAC3B6pB,WAAWxgB,WAAWrJ,KAAK,OAAO,GAClC8pB,aAAazgB,WAAWrJ,KAAK,OAAO,GACpC+pB,WAAW1gB,WAAWrJ,KAAK,OAAO,GAClCgqB,YAAY3gB,WAAWrJ,KAAK,OAAO,GACnCiqB,YAAY5gB,WAAWrJ,KAAK,OAAO,GACnCkqB,YAAY7gB,WAAWrJ,KAAK,OAAO,GACnCoU,UAAWrK,QAAO;QAAEN,OAAOC,OAAO4f,YAAYvf,EAAEsK,GAAG,CAAC;QAAGmU,OAAOA,CAAC,CAAC5yB,EAAC,MAAMA;QAAGwe,SAAS;MAAK,IACxF+V,UAAWpgB,OAAM;AACf,YAAIoK,MAAMC,SAAS;AACjB,iBAAOA,QAAQrK,CAAC;QAClB;AACA,gBAAQA,EAAEsK,KAAG;UAEX,KAAK;AACH,mBAAO2U,MAAMhpB,IAAImF,KAAK,OAAO,GAAG,CAAC;UACnC,KAAK;AACH,mBAAO6jB,MAAMhpB,IAAImF,KAAK,MAAM,GAAG,CAAC;UAElC,KAAK;AACH,mBAAOmjB,QAAQyB,QAAQ;UACzB,KAAK;AACH,mBAAOzB,QAAQ2B,WAAWhZ,cAAc;UAC1C,KAAK;AACH,mBAAOqX,QAAQqB,IAAI;UACrB,KAAK;AACH,mBAAOrB,QAAQ4B,SAAS;UAC1B,KAAK;AACH,mBAAO5B,QAAQsB,GAAG;UAEpB,KAAK;AACH,mBAAOtB,QAAQuB,QAAQ;UACzB,KAAK;AACH,mBAAOvB,QAAQmB,GAAG;UACpB,KAAK;AACH,mBAAOT,MAAMhpB,IAAI8E,OAAO,SAAS,IAAI,GAAG,CAAC;UAC3C,KAAK;AACH,mBAAOkkB,MAAMhpB,IAAI8E,OAAO,QAAQ,IAAI,GAAG,CAAC;UAC1C,KAAK;AACH,mBAAOwjB,QAAQuB,QAAQ;UACzB,KAAK;AACH,mBAAOvB,QAAQmB,GAAG;UACpB,KAAK;AACH,mBAAOT,MAAMhpB,IAAI8E,OAAO,SAAS,KAAK,GAAG,CAAC;UAC5C,KAAK;AACH,mBAAOkkB,MAAMhpB,IAAI8E,OAAO,QAAQ,KAAK,GAAG,CAAC;UAE3C,KAAK;AACH,mBAAOwjB,QAAQuB,QAAQ;UACzB,KAAK;AACH,mBAAOvB,QAAQmB,GAAG;UAEpB,KAAK;AACH,mBAAOnB,QAAQwB,UAAU;UAC3B,KAAK;AACH,mBAAOxB,QAAQoB,KAAK;UAEtB,KAAK;AACH,mBAAOpB,QAAQmB,GAAG;UACpB,KAAK;AACH,mBAAOnB,QAAQuB,QAAQ;UACzB,KAAK;AACH,mBAAOvB,QAAQmB,GAAG;UACpB,KAAK;AACH,mBAAOnB,QAAQuB,QAAQ;UACzB,KAAK;AACH,mBAAOvB,QAAQmB,GAAG;UACpB,KAAK;AACH,mBAAOnB,QAAQuB,QAAQ;UACzB,KAAK;AACH,mBAAOvB,QAAQuB,QAAQ;UACzB,KAAK;AACH,mBAAOvB,QAAQmB,GAAG;UACpB,KAAK;AACH,mBAAOnB,QAAQuB,QAAQ;UACzB,KAAK;AACH,mBAAOvB,QAAQmB,GAAG;UACpB,KAAK;AACH,mBAAOnB,QAAQwB,UAAU;UAC3B,KAAK;AACH,mBAAOxB,QAAQoB,KAAK;UACtB,KAAK;AACH,mBAAOL,OAAOW,SAAS;UACzB,KAAK;AACH,mBAAOX,OAAOQ,QAAQ;UACxB,KAAK;AACH,mBAAOvB,QAAQkB,GAAG;UAEpB,KAAK;AACH,mBAAOR,MAAMhpB,IAAIkF,UAAS,GAAI,CAAC;UAEjC,KAAK;AACH,mBAAOojB,QAAQqB,IAAI;UACrB,KAAK;AACH,mBAAOrB,QAAQ2B,WAAWhZ,cAAc;UAE1C,KAAK;AACH,mBAAOqX,QAAQuB,QAAQ;UACzB,KAAK;AACH,mBAAOvB,QAAQmB,GAAG;UAEpB,KAAK;UACL,KAAK;AACH,mBAAOnB,QAAQkB,GAAG;UACpB,KAAK;AACH,mBAAOR,MAAMhpB,IAAIiF,SAAS,SAAS,KAAK,GAAG,CAAC;UAC9C,KAAK;AACH,mBAAO+jB,MAAMhpB,IAAIiF,SAAS,QAAQ,KAAK,GAAG,CAAC;UAC7C,KAAK;AACH,mBAAO+jB,MAAMhpB,IAAIiF,SAAS,SAAS,IAAI,GAAG,CAAC;UAC7C,KAAK;AACH,mBAAO+jB,MAAMhpB,IAAIiF,SAAS,QAAQ,IAAI,GAAG,CAAC;UAE5C,KAAK;UACL,KAAK;AACH,mBAAO1M,OAAO,IAAImR,OAAQ,QAAOmgB,SAASrR,MAAO,SAAQiR,IAAIjR,MAAO,KAAI,GAAG,CAAC;UAC9E,KAAK;AACH,mBAAOjgB,OAAO,IAAImR,OAAQ,QAAOmgB,SAASrR,MAAO,KAAIiR,IAAIjR,MAAO,IAAG,GAAG,CAAC;UAGzE,KAAK;AACH,mBAAO6Q,OAAO,oBAAoB;UAGpC,KAAK;AACH,mBAAOA,OAAO,WAAW;UAC3B;AACE,mBAAOjV,QAAQrK,CAAC;QACpB;;AAGJ,YAAMvU,OAAO20B,QAAQhW,KAAK,KAAK;QAC7B2N,eAAeuG;;AAGjB7yB,WAAK2e,QAAQA;AAEb,aAAO3e;IACT;AAEA,QAAM40B,0BAA0B;MAC9Br0B,MAAM;QACJ,WAAW;QACX0M,SAAS;;MAEXzM,OAAO;QACLyM,SAAS;QACT,WAAW;QACX4nB,OAAO;QACPC,MAAM;;MAERr0B,KAAK;QACHwM,SAAS;QACT,WAAW;;MAEbrM,SAAS;QACPi0B,OAAO;QACPC,MAAM;;MAERC,WAAW;MACXC,WAAW;MACX3wB,QAAQ;QACN4I,SAAS;QACT,WAAW;;MAEbgoB,QAAQ;QACNhoB,SAAS;QACT,WAAW;;MAEbhM,QAAQ;QACNgM,SAAS;QACT,WAAW;;MAEb9L,QAAQ;QACN8L,SAAS;QACT,WAAW;;MAEb5L,cAAc;QACZyzB,MAAM;QACND,OAAO;MACT;IACF;AAEA,aAASK,aAAazoB,MAAMkU,YAAYwU,cAAc;AACpD,YAAM;QAAE7yB;QAAMqD;MAAM,IAAI8G;AAExB,UAAInK,SAAS,WAAW;AACtB,cAAM8yB,UAAU,QAAQ1U,KAAK/a,KAAK;AAClC,eAAO;UACLiZ,SAAS,CAACwW;UACVvW,KAAKuW,UAAU,MAAMzvB;;MAEzB;AAEA,YAAMiH,QAAQ+T,WAAWre,IAAI;AAK7B,UAAI+yB,aAAa/yB;AACjB,UAAIA,SAAS,QAAQ;AACnB,YAAIqe,WAAWtc,UAAU,MAAM;AAC7BgxB,uBAAa1U,WAAWtc,SAAS,WAAW;QAC9C,WAAWsc,WAAWnf,aAAa,MAAM;AACvC,cAAImf,WAAWnf,cAAc,SAASmf,WAAWnf,cAAc,OAAO;AACpE6zB,yBAAa;UACf,OAAO;AACLA,yBAAa;UACf;QACF,OAAO;AAGLA,uBAAaF,aAAa9wB,SAAS,WAAW;QAChD;MACF;AACA,UAAIwa,MAAM+V,wBAAwBS,UAAU;AAC5C,UAAI,OAAOxW,QAAQ,UAAU;AAC3BA,cAAMA,IAAIjS,KAAK;MACjB;AAEA,UAAIiS,KAAK;AACP,eAAO;UACLD,SAAS;UACTC;;MAEJ;AAEA,aAAOza;IACT;AAEA,aAASkxB,WAAW3X,OAAO;AACzB,YAAM4X,KAAK5X,MAAMtR,IAAKmQ,OAAMA,EAAEvI,KAAK,EAAEkF,OAAO,CAACnP,GAAGiH,MAAO,GAAEjH,CAAE,IAAGiH,EAAE+R,MAAO,KAAI,EAAE;AAC7E,aAAO,CAAE,IAAGuS,EAAG,KAAI5X,KAAK;IAC1B;AAEA,aAASzM,MAAMI,OAAO2C,OAAOuhB,UAAU;AACrC,YAAMC,UAAUnkB,MAAMJ,MAAM+C,KAAK;AAEjC,UAAIwhB,SAAS;AACX,cAAMC,MAAM,CAAA;AACZ,YAAIC,aAAa;AACjB,mBAAWlwB,KAAK+vB,UAAU;AACxB,cAAI9b,eAAe8b,UAAU/vB,CAAC,GAAG;AAC/B,kBAAMmuB,IAAI4B,SAAS/vB,CAAC,GAClBkuB,SAASC,EAAED,SAASC,EAAED,SAAS,IAAI;AACrC,gBAAI,CAACC,EAAEhV,WAAWgV,EAAEjV,OAAO;AACzB+W,kBAAI9B,EAAEjV,MAAME,IAAI,CAAC,CAAC,IAAI+U,EAAEZ,MAAMyC,QAAQ3T,MAAM6T,YAAYA,aAAahC,MAAM,CAAC;YAC9E;AACAgC,0BAAchC;UAChB;QACF;AACA,eAAO,CAAC8B,SAASC,GAAG;MACtB,OAAO;AACL,eAAO,CAACD,SAAS,CAAA,CAAE;MACrB;IACF;AAEA,aAASG,oBAAoBH,SAAS;AACpC,YAAMI,UAAWlX,WAAU;AACzB,gBAAQA,OAAK;UACX,KAAK;AACH,mBAAO;UACT,KAAK;AACH,mBAAO;UACT,KAAK;AACH,mBAAO;UACT,KAAK;UACL,KAAK;AACH,mBAAO;UACT,KAAK;AACH,mBAAO;UACT,KAAK;AACH,mBAAO;UACT,KAAK;UACL,KAAK;AACH,mBAAO;UACT,KAAK;AACH,mBAAO;UACT,KAAK;UACL,KAAK;AACH,mBAAO;UACT,KAAK;AACH,mBAAO;UACT,KAAK;AACH,mBAAO;UACT,KAAK;AACH,mBAAO;UACT;AACE,mBAAO;QACX;;AAGF,UAAIzY,OAAO;AACX,UAAI4vB;AACJ,UAAI,CAACjwB,YAAY4vB,QAAQ1pB,CAAC,GAAG;AAC3B7F,eAAOF,SAASC,OAAOwvB,QAAQ1pB,CAAC;MAClC;AAEA,UAAI,CAAClG,YAAY4vB,QAAQM,CAAC,GAAG;AAC3B,YAAI,CAAC7vB,MAAM;AACTA,iBAAO,IAAI4K,gBAAgB2kB,QAAQM,CAAC;QACtC;AACAD,yBAAiBL,QAAQM;MAC3B;AAEA,UAAI,CAAClwB,YAAY4vB,QAAQO,CAAC,GAAG;AAC3BP,gBAAQQ,KAAKR,QAAQO,IAAI,KAAK,IAAI;MACpC;AAEA,UAAI,CAACnwB,YAAY4vB,QAAQ7B,CAAC,GAAG;AAC3B,YAAI6B,QAAQ7B,IAAI,MAAM6B,QAAQjc,MAAM,GAAG;AACrCic,kBAAQ7B,KAAK;QACf,WAAW6B,QAAQ7B,MAAM,MAAM6B,QAAQjc,MAAM,GAAG;AAC9Cic,kBAAQ7B,IAAI;QACd;MACF;AAEA,UAAI6B,QAAQS,MAAM,KAAKT,QAAQU,GAAG;AAChCV,gBAAQU,IAAI,CAACV,QAAQU;MACvB;AAEA,UAAI,CAACtwB,YAAY4vB,QAAQjZ,CAAC,GAAG;AAC3BiZ,gBAAQW,IAAI1b,YAAY+a,QAAQjZ,CAAC;MACnC;AAEA,YAAMmN,OAAOte,OAAOC,KAAKmqB,OAAO,EAAEtc,OAAO,CAAClI,GAAGwI,MAAM;AACjD,cAAMzP,IAAI6rB,QAAQpc,CAAC;AACnB,YAAIzP,GAAG;AACLiH,YAAEjH,CAAC,IAAIyrB,QAAQhc,CAAC;QAClB;AAEA,eAAOxI;SACN,CAAA,CAAE;AAEL,aAAO,CAAC0Y,MAAMzjB,MAAM4vB,cAAc;IACpC;AAEA,QAAIO,qBAAqB;AAEzB,aAASC,mBAAmB;AAC1B,UAAI,CAACD,oBAAoB;AACvBA,6BAAqBlsB,SAASwgB,WAAW,aAAa;MACxD;AAEA,aAAO0L;IACT;AAEA,aAASE,sBAAsB5X,OAAOjb,QAAQ;AAC5C,UAAIib,MAAMC,SAAS;AACjB,eAAOD;MACT;AAEA,YAAMgC,aAAaT,UAAUpB,uBAAuBH,MAAME,GAAG;AAC7D,YAAMwD,SAASmU,mBAAmB7V,YAAYjd,MAAM;AAEpD,UAAI2e,UAAU,QAAQA,OAAOvY,SAAS1F,MAAS,GAAG;AAChD,eAAOua;MACT;AAEA,aAAO0D;IACT;AAEO,aAASoU,kBAAkBpU,QAAQ3e,QAAQ;AAChD,aAAOmV,MAAMJ,UAAU+J,OAAO,GAAGH,OAAOhW,IAAKkI,OAAMgiB,sBAAsBhiB,GAAG7Q,MAAM,CAAC,CAAC;IACtF;AAMO,QAAMgzB,cAAN,MAAkB;MACvBj3B,YAAYiE,QAAQZ,QAAQ;AAC1B,aAAKY,SAASA;AACd,aAAKZ,SAASA;AACd,aAAKuf,SAASoU,kBAAkBvW,UAAUC,YAAYrd,MAAM,GAAGY,MAAM;AACrE,aAAKia,QAAQ,KAAK0E,OAAOhW,IAAKkI,OAAMwf,aAAaxf,GAAG7Q,MAAM,CAAC;AAC3D,aAAKizB,oBAAoB,KAAKhZ,MAAM1N,KAAMsE,OAAMA,EAAE+X,aAAa;AAE/D,YAAI,CAAC,KAAKqK,mBAAmB;AAC3B,gBAAM,CAACC,aAAapB,QAAQ,IAAIF,WAAW,KAAK3X,KAAK;AACrD,eAAK1J,QAAQC,OAAO0iB,aAAa,GAAG;AACpC,eAAKpB,WAAWA;QAClB;MACF;MAEAqB,kBAAkBvlB,OAAO;AACvB,YAAI,CAAC,KAAKpO,SAAS;AACjB,iBAAO;YAAEoO;YAAO+Q,QAAQ,KAAKA;YAAQiK,eAAe,KAAKA;;QAC3D,OAAO;AACL,gBAAM,CAACwK,YAAYrB,OAAO,IAAIvkB,MAAMI,OAAO,KAAK2C,OAAO,KAAKuhB,QAAQ,GAClE,CAAC1O,QAAQ5gB,MAAM4vB,cAAc,IAAIL,UAC7BG,oBAAoBH,OAAO,IAC3B,CAAC,MAAM,MAAMrxB,MAAS;AAC5B,cAAIsV,eAAe+b,SAAS,GAAG,KAAK/b,eAAe+b,SAAS,GAAG,GAAG;AAChE,kBAAM,IAAI31B,8BACR,uDACF;UACF;AACA,iBAAO;YACLwR;YACA+Q,QAAQ,KAAKA;YACbpO,OAAO,KAAKA;YACZ6iB;YACArB;YACA3O;YACA5gB;YACA4vB;;QAEJ;MACF;MAEA,IAAI5yB,UAAU;AACZ,eAAO,CAAC,KAAKyzB;MACf;MAEA,IAAIrK,gBAAgB;AAClB,eAAO,KAAKqK,oBAAoB,KAAKA,kBAAkBrK,gBAAgB;MACzE;IACF;AAEO,aAASuK,kBAAkBnzB,QAAQ4N,OAAOxO,QAAQ;AACvD,YAAMi0B,SAAS,IAAIL,YAAYhzB,QAAQZ,MAAM;AAC7C,aAAOi0B,OAAOF,kBAAkBvlB,KAAK;IACvC;AAEO,aAAS0lB,gBAAgBtzB,QAAQ4N,OAAOxO,QAAQ;AACrD,YAAM;QAAEgkB;QAAQ5gB;QAAM4vB;QAAgBxJ;UAAkBuK,kBAAkBnzB,QAAQ4N,OAAOxO,MAAM;AAC/F,aAAO,CAACgkB,QAAQ5gB,MAAM4vB,gBAAgBxJ,aAAa;IACrD;AAEO,aAASkK,mBAAmB7V,YAAYjd,QAAQ;AACrD,UAAI,CAACid,YAAY;AACf,eAAO;MACT;AAEA,YAAMsW,YAAY/W,UAAUja,OAAOvC,QAAQid,UAAU;AACrD,YAAM9Q,KAAKonB,UAAUnnB,YAAYwmB,iBAAgB,CAAE;AACnD,YAAM9pB,QAAQqD,GAAGtK,cAAa;AAC9B,YAAM4vB,eAAetlB,GAAGrM,gBAAe;AACvC,aAAOgJ,MAAMH,IAAKiV,OAAM4T,aAAa5T,GAAGX,YAAYwU,YAAY,CAAC;IACnE;ACncA,QAAMvM,UAAU;AAChB,QAAMsO,WAAW;AAEjB,aAASC,gBAAgBjxB,MAAM;AAC7B,aAAO,IAAIuO,QAAQ,oBAAqB,aAAYvO,KAAK3D,IAAK,oBAAmB;IACnF;AAMA,aAAS60B,uBAAuBltB,IAAI;AAClC,UAAIA,GAAGqM,aAAa,MAAM;AACxBrM,WAAGqM,WAAWR,gBAAgB7L,GAAGsW,CAAC;MACpC;AACA,aAAOtW,GAAGqM;IACZ;AAKA,aAAS8gB,4BAA4BntB,IAAI;AACvC,UAAIA,GAAGotB,kBAAkB,MAAM;AAC7BptB,WAAGotB,gBAAgBvhB,gBACjB7L,GAAGsW,GACHtW,GAAGM,IAAIkG,sBAAqB,GAC5BxG,GAAGM,IAAIiG,eAAc,CACvB;MACF;AACA,aAAOvG,GAAGotB;IACZ;AAIA,aAASroB,MAAMsoB,MAAMroB,MAAM;AACzB,YAAMmR,UAAU;QACd1d,IAAI40B,KAAK50B;QACTuD,MAAMqxB,KAAKrxB;QACXsa,GAAG+W,KAAK/W;QACRjI,GAAGgf,KAAKhf;QACR/N,KAAK+sB,KAAK/sB;QACVigB,SAAS8M,KAAK9M;;AAEhB,aAAO,IAAItgB,SAAS;QAAE,GAAGkW;QAAS,GAAGnR;QAAMsoB,KAAKnX;MAAQ,CAAC;IAC3D;AAIA,aAASoX,UAAUC,SAASnf,GAAGof,IAAI;AAEjC,UAAIC,WAAWF,UAAUnf,IAAI,KAAK;AAGlC,YAAMsf,KAAKF,GAAG50B,OAAO60B,QAAQ;AAG7B,UAAIrf,MAAMsf,IAAI;AACZ,eAAO,CAACD,UAAUrf,CAAC;MACrB;AAGAqf,mBAAaC,KAAKtf,KAAK,KAAK;AAG5B,YAAMuf,KAAKH,GAAG50B,OAAO60B,QAAQ;AAC7B,UAAIC,OAAOC,IAAI;AACb,eAAO,CAACF,UAAUC,EAAE;MACtB;AAGA,aAAO,CAACH,UAAU9wB,KAAK6M,IAAIokB,IAAIC,EAAE,IAAI,KAAK,KAAMlxB,KAAK8M,IAAImkB,IAAIC,EAAE,CAAC;IAClE;AAGA,aAASC,QAAQp1B,IAAII,SAAQ;AAC3BJ,YAAMI,UAAS,KAAK;AAEpB,YAAMgS,IAAI,IAAInR,KAAKjB,EAAE;AAErB,aAAO;QACLpC,MAAMwU,EAAEG,eAAc;QACtB1U,OAAOuU,EAAEijB,YAAW,IAAK;QACzBv3B,KAAKsU,EAAEkjB,WAAU;QACjBj3B,MAAM+T,EAAEmjB,YAAW;QACnBj3B,QAAQ8T,EAAEojB,cAAa;QACvBh3B,QAAQ4T,EAAEqjB,cAAa;QACvBnxB,aAAa8N,EAAEsjB,mBAAkB;;IAErC;AAGA,aAASC,QAAQthB,KAAKjU,SAAQmD,MAAM;AAClC,aAAOuxB,UAAUzwB,aAAagQ,GAAG,GAAGjU,SAAQmD,IAAI;IAClD;AAGA,aAASqyB,WAAWhB,MAAMtV,KAAK;AAC7B,YAAMuW,OAAOjB,KAAKhf,GAChBhY,OAAOg3B,KAAK/W,EAAEjgB,OAAOqG,KAAKqU,MAAMgH,IAAIrE,KAAK,GACzCpd,QAAQ+2B,KAAK/W,EAAEhgB,QAAQoG,KAAKqU,MAAMgH,IAAI3S,MAAM,IAAI1I,KAAKqU,MAAMgH,IAAIpE,QAAQ,IAAI,GAC3E2C,IAAI;QACF,GAAG+W,KAAK/W;QACRjgB;QACAC;QACAC,KACEmG,KAAK6M,IAAI8jB,KAAK/W,EAAE/f,KAAKwX,YAAY1X,MAAMC,KAAK,CAAC,IAC7CoG,KAAKqU,MAAMgH,IAAIlE,IAAI,IACnBnX,KAAKqU,MAAMgH,IAAInE,KAAK,IAAI;SAE5B2a,cAAchP,SAAStb,WAAW;QAChCyP,OAAOqE,IAAIrE,QAAQhX,KAAKqU,MAAMgH,IAAIrE,KAAK;QACvCC,UAAUoE,IAAIpE,WAAWjX,KAAKqU,MAAMgH,IAAIpE,QAAQ;QAChDvO,QAAQ2S,IAAI3S,SAAS1I,KAAKqU,MAAMgH,IAAI3S,MAAM;QAC1CwO,OAAOmE,IAAInE,QAAQlX,KAAKqU,MAAMgH,IAAInE,KAAK;QACvCC,MAAMkE,IAAIlE,OAAOnX,KAAKqU,MAAMgH,IAAIlE,IAAI;QACpCtB,OAAOwF,IAAIxF;QACXrQ,SAAS6V,IAAI7V;QACb4R,SAASiE,IAAIjE;QACb8G,cAAc7C,IAAI6C;MACpB,CAAC,EAAEiI,GAAG,cAAc,GACpB2K,UAAU1wB,aAAawZ,CAAC;AAE1B,UAAI,CAAC7d,IAAI4V,CAAC,IAAIkf,UAAUC,SAASc,MAAMjB,KAAKrxB,IAAI;AAEhD,UAAIuyB,gBAAgB,GAAG;AACrB91B,cAAM81B;AAENlgB,YAAIgf,KAAKrxB,KAAKnD,OAAOJ,EAAE;MACzB;AAEA,aAAO;QAAEA;QAAI4V;;IACf;AAIA,aAASmgB,oBAAoB7zB,QAAQ8zB,YAAY/1B,MAAME,QAAQmoB,MAAM6K,gBAAgB;AACnF,YAAM;QAAE5pB;QAAShG;MAAK,IAAItD;AAC1B,UAAKiC,UAAUwG,OAAOC,KAAKzG,MAAM,EAAEa,WAAW,KAAMizB,YAAY;AAC9D,cAAMC,qBAAqBD,cAAczyB,MACvCqxB,OAAOptB,SAASgE,WAAWtJ,QAAQ;UACjC,GAAGjC;UACHsD,MAAM0yB;UACN9C;QACF,CAAC;AACH,eAAO5pB,UAAUqrB,OAAOA,KAAKrrB,QAAQhG,IAAI;MAC3C,OAAO;AACL,eAAOiE,SAASsgB,QACd,IAAIhW,QAAQ,cAAe,cAAawW,IAAK,wBAAuBnoB,MAAO,EAAC,CAC9E;MACF;IACF;AAIA,aAAS+1B,aAAa3uB,IAAIpH,QAAQ6e,SAAS,MAAM;AAC/C,aAAOzX,GAAGhH,UACNgd,UAAUja,OAAO4C,OAAO5C,OAAO,OAAO,GAAG;QACvC0b;QACA1W,aAAa;OACd,EAAEsW,yBAAyBrX,IAAIpH,MAAM,IACtC;IACN;AAEA,aAASiuB,UAAUxY,GAAGugB,UAAU;AAC9B,YAAMC,aAAaxgB,EAAEiI,EAAEjgB,OAAO,QAAQgY,EAAEiI,EAAEjgB,OAAO;AACjD,UAAIigB,IAAI;AACR,UAAIuY,cAAcxgB,EAAEiI,EAAEjgB,QAAQ,EAAGigB,MAAK;AACtCA,WAAK5U,SAAS2M,EAAEiI,EAAEjgB,MAAMw4B,aAAa,IAAI,CAAC;AAE1C,UAAID,UAAU;AACZtY,aAAK;AACLA,aAAK5U,SAAS2M,EAAEiI,EAAEhgB,KAAK;AACvBggB,aAAK;AACLA,aAAK5U,SAAS2M,EAAEiI,EAAE/f,GAAG;MACvB,OAAO;AACL+f,aAAK5U,SAAS2M,EAAEiI,EAAEhgB,KAAK;AACvBggB,aAAK5U,SAAS2M,EAAEiI,EAAE/f,GAAG;MACvB;AACA,aAAO+f;IACT;AAEA,aAASmL,UACPpT,GACAugB,UACA/M,iBACAD,sBACAG,eACA+M,cACA;AACA,UAAIxY,IAAI5U,SAAS2M,EAAEiI,EAAExf,IAAI;AACzB,UAAI83B,UAAU;AACZtY,aAAK;AACLA,aAAK5U,SAAS2M,EAAEiI,EAAEvf,MAAM;AACxB,YAAIsX,EAAEiI,EAAEvZ,gBAAgB,KAAKsR,EAAEiI,EAAErf,WAAW,KAAK,CAAC4qB,iBAAiB;AACjEvL,eAAK;QACP;MACF,OAAO;AACLA,aAAK5U,SAAS2M,EAAEiI,EAAEvf,MAAM;MAC1B;AAEA,UAAIsX,EAAEiI,EAAEvZ,gBAAgB,KAAKsR,EAAEiI,EAAErf,WAAW,KAAK,CAAC4qB,iBAAiB;AACjEvL,aAAK5U,SAAS2M,EAAEiI,EAAErf,MAAM;AAExB,YAAIoX,EAAEiI,EAAEvZ,gBAAgB,KAAK,CAAC6kB,sBAAsB;AAClDtL,eAAK;AACLA,eAAK5U,SAAS2M,EAAEiI,EAAEvZ,aAAa,CAAC;QAClC;MACF;AAEA,UAAIglB,eAAe;AACjB,YAAI1T,EAAEmJ,iBAAiBnJ,EAAExV,WAAW,KAAK,CAACi2B,cAAc;AACtDxY,eAAK;QACP,WAAWjI,EAAEA,IAAI,GAAG;AAClBiI,eAAK;AACLA,eAAK5U,SAAShF,KAAKqU,MAAM,CAAC1C,EAAEA,IAAI,EAAE,CAAC;AACnCiI,eAAK;AACLA,eAAK5U,SAAShF,KAAKqU,MAAM,CAAC1C,EAAEA,IAAI,EAAE,CAAC;QACrC,OAAO;AACLiI,eAAK;AACLA,eAAK5U,SAAShF,KAAKqU,MAAM1C,EAAEA,IAAI,EAAE,CAAC;AAClCiI,eAAK;AACLA,eAAK5U,SAAShF,KAAKqU,MAAM1C,EAAEA,IAAI,EAAE,CAAC;QACpC;MACF;AAEA,UAAIygB,cAAc;AAChBxY,aAAK,MAAMjI,EAAErS,KAAK1D,WAAW;MAC/B;AACA,aAAOge;IACT;AAGA,QAAMyY,oBAAoB;MACtBz4B,OAAO;MACPC,KAAK;MACLO,MAAM;MACNC,QAAQ;MACRE,QAAQ;MACR8F,aAAa;;AANjB,QAQEiyB,wBAAwB;MACtBhjB,YAAY;MACZtV,SAAS;MACTI,MAAM;MACNC,QAAQ;MACRE,QAAQ;MACR8F,aAAa;;AAdjB,QAgBEkyB,2BAA2B;MACzB3jB,SAAS;MACTxU,MAAM;MACNC,QAAQ;MACRE,QAAQ;MACR8F,aAAa;;AAIjB,QAAMiiB,eAAe,CAAC,QAAQ,SAAS,OAAO,QAAQ,UAAU,UAAU,aAAa;AAAvF,QACEkQ,mBAAmB,CACjB,YACA,cACA,WACA,QACA,UACA,UACA,aAAa;AARjB,QAUEC,sBAAsB,CAAC,QAAQ,WAAW,QAAQ,UAAU,UAAU,aAAa;AAGrF,aAASzO,cAAc5qB,MAAM;AAC3B,YAAMuc,aAAa;QACjBhc,MAAM;QACNqd,OAAO;QACPpd,OAAO;QACP8O,QAAQ;QACR7O,KAAK;QACLsd,MAAM;QACN/c,MAAM;QACNyb,OAAO;QACPxb,QAAQ;QACRmL,SAAS;QACT2V,SAAS;QACTlE,UAAU;QACV1c,QAAQ;QACR6c,SAAS;QACT/W,aAAa;QACb6d,cAAc;QACdlkB,SAAS;QACT6O,UAAU;QACV6pB,YAAY;QACZC,aAAa;QACbC,aAAa;QACbC,UAAU;QACVC,WAAW;QACXlkB,SAAS;MACX,EAAExV,KAAKmQ,YAAW,CAAE;AAEpB,UAAI,CAACoM,WAAY,OAAM,IAAIxc,iBAAiBC,IAAI;AAEhD,aAAOuc;IACT;AAEA,aAASod,4BAA4B35B,MAAM;AACzC,cAAQA,KAAKmQ,YAAW,GAAE;QACxB,KAAK;QACL,KAAK;AACH,iBAAO;QACT,KAAK;QACL,KAAK;AACH,iBAAO;QACT,KAAK;QACL,KAAK;AACH,iBAAO;QACT;AACE,iBAAOya,cAAc5qB,IAAI;MAC7B;IACF;AAyBA,aAAS45B,mBAAmB1zB,MAAM;AAChC,UAAI2zB,iBAAiBz1B,QAAW;AAC9By1B,uBAAepsB,SAAS0G,IAAG;MAC7B;AAIA,UAAIjO,KAAK5D,SAAS,QAAQ;AACxB,eAAO4D,KAAKnD,OAAO82B,YAAY;MACjC;AACA,YAAM51B,WAAWiC,KAAK3D;AACtB,UAAIu3B,cAAcC,qBAAqB51B,IAAIF,QAAQ;AACnD,UAAI61B,gBAAgB11B,QAAW;AAC7B01B,sBAAc5zB,KAAKnD,OAAO82B,YAAY;AACtCE,6BAAqBx1B,IAAIN,UAAU61B,WAAW;MAChD;AACA,aAAOA;IACT;AAKA,aAASE,QAAQhjB,KAAKpU,MAAM;AAC1B,YAAMsD,OAAOmL,cAAczO,KAAKsD,MAAMuH,SAAS8D,WAAW;AAC1D,UAAI,CAACrL,KAAKhD,SAAS;AACjB,eAAOiH,SAASsgB,QAAQ0M,gBAAgBjxB,IAAI,CAAC;MAC/C;AAEA,YAAMsE,MAAM3B,OAAOsF,WAAWvL,IAAI;AAElC,UAAID,IAAI4V;AAGR,UAAI,CAAC1S,YAAYmR,IAAIzW,IAAI,GAAG;AAC1B,mBAAWic,KAAK0M,cAAc;AAC5B,cAAIrjB,YAAYmR,IAAIwF,CAAC,CAAC,GAAG;AACvBxF,gBAAIwF,CAAC,IAAIyc,kBAAkBzc,CAAC;UAC9B;QACF;AAEA,cAAMiO,UAAU3S,wBAAwBd,GAAG,KAAKkB,mBAAmBlB,GAAG;AACtE,YAAIyT,SAAS;AACX,iBAAOtgB,SAASsgB,QAAQA,OAAO;QACjC;AAEA,cAAMwP,eAAeL,mBAAmB1zB,IAAI;AAC5C,SAACvD,IAAI4V,CAAC,IAAI+f,QAAQthB,KAAKijB,cAAc/zB,IAAI;MAC3C,OAAO;AACLvD,aAAK8K,SAAS0G,IAAG;MACnB;AAEA,aAAO,IAAIhK,SAAS;QAAExH;QAAIuD;QAAMsE;QAAK+N;MAAE,CAAC;IAC1C;AAEA,aAAS2hB,aAAahZ,OAAOE,KAAKxe,MAAM;AACtC,YAAMsY,QAAQrV,YAAYjD,KAAKsY,KAAK,IAAI,OAAOtY,KAAKsY,OAClDpY,SAASA,CAAC0d,GAAGxgB,SAAS;AACpBwgB,YAAI7U,QAAQ6U,GAAGtF,SAAStY,KAAKu3B,YAAY,IAAI,GAAG,IAAI;AACpD,cAAMlD,YAAY7V,IAAI5W,IAAIyE,MAAMrM,IAAI,EAAEyN,aAAazN,IAAI;AACvD,eAAOq0B,UAAUn0B,OAAO0d,GAAGxgB,IAAI;SAEjC0yB,SAAU1yB,UAAS;AACjB,YAAI4C,KAAKu3B,WAAW;AAClB,cAAI,CAAC/Y,IAAI2N,QAAQ7N,OAAOlhB,IAAI,GAAG;AAC7B,mBAAOohB,IAAIwN,QAAQ5uB,IAAI,EAAE8uB,KAAK5N,MAAM0N,QAAQ5uB,IAAI,GAAGA,IAAI,EAAEmE,IAAInE,IAAI;gBAC5D,QAAO;QAChB,OAAO;AACL,iBAAOohB,IAAI0N,KAAK5N,OAAOlhB,IAAI,EAAEmE,IAAInE,IAAI;QACvC;;AAGJ,UAAI4C,KAAK5C,MAAM;AACb,eAAO8C,OAAO4vB,OAAO9vB,KAAK5C,IAAI,GAAG4C,KAAK5C,IAAI;MAC5C;AAEA,iBAAWA,QAAQ4C,KAAK+a,OAAO;AAC7B,cAAM5Q,QAAQ2lB,OAAO1yB,IAAI;AACzB,YAAI4G,KAAKC,IAAIkG,KAAK,KAAK,GAAG;AACxB,iBAAOjK,OAAOiK,OAAO/M,IAAI;QAC3B;MACF;AACA,aAAO8C,OAAOoe,QAAQE,MAAM,KAAK,GAAGxe,KAAK+a,MAAM/a,KAAK+a,MAAMjY,SAAS,CAAC,CAAC;IACvE;AAEA,aAAS00B,SAASC,SAAS;AACzB,UAAIz3B,OAAO,CAAA,GACT03B;AACF,UAAID,QAAQ30B,SAAS,KAAK,OAAO20B,QAAQA,QAAQ30B,SAAS,CAAC,MAAM,UAAU;AACzE9C,eAAOy3B,QAAQA,QAAQ30B,SAAS,CAAC;AACjC40B,eAAOzhB,MAAMkB,KAAKsgB,OAAO,EAAEvY,MAAM,GAAGuY,QAAQ30B,SAAS,CAAC;MACxD,OAAO;AACL40B,eAAOzhB,MAAMkB,KAAKsgB,OAAO;MAC3B;AACA,aAAO,CAACz3B,MAAM03B,IAAI;IACpB;AAKA,QAAIT;AAOJ,QAAME,uBAAuB,oBAAIh2B,IAAG;AAsBrB,QAAMoG,WAAN,MAAMA,UAAS;;;;MAI5B1K,YAAY8qB,QAAQ;AAClB,cAAMrkB,OAAOqkB,OAAOrkB,QAAQuH,SAAS8D;AAErC,YAAIkZ,UACFF,OAAOE,YACN1O,OAAOrV,MAAM6jB,OAAO5nB,EAAE,IAAI,IAAI8R,QAAQ,eAAe,IAAI,UACzD,CAACvO,KAAKhD,UAAUi0B,gBAAgBjxB,IAAI,IAAI;AAI3C,aAAKvD,KAAKkD,YAAY0kB,OAAO5nB,EAAE,IAAI8K,SAAS0G,IAAG,IAAKoW,OAAO5nB;AAE3D,YAAI6d,IAAI,MACNjI,IAAI;AACN,YAAI,CAACkS,SAAS;AACZ,gBAAM8P,YAAYhQ,OAAOiN,OAAOjN,OAAOiN,IAAI70B,OAAO,KAAKA,MAAM4nB,OAAOiN,IAAItxB,KAAKlD,OAAOkD,IAAI;AAExF,cAAIq0B,WAAW;AACb,aAAC/Z,GAAGjI,CAAC,IAAI,CAACgS,OAAOiN,IAAIhX,GAAG+J,OAAOiN,IAAIjf,CAAC;UACtC,OAAO;AAGL,kBAAMiiB,KAAK9oB,SAAS6Y,OAAOhS,CAAC,KAAK,CAACgS,OAAOiN,MAAMjN,OAAOhS,IAAIrS,KAAKnD,OAAO,KAAKJ,EAAE;AAC7E6d,gBAAIuX,QAAQ,KAAKp1B,IAAI63B,EAAE;AACvB/P,sBAAU1O,OAAOrV,MAAM8Z,EAAEjgB,IAAI,IAAI,IAAIkU,QAAQ,eAAe,IAAI;AAChE+L,gBAAIiK,UAAU,OAAOjK;AACrBjI,gBAAIkS,UAAU,OAAO+P;UACvB;QACF;AAKA,aAAKC,QAAQv0B;AAIb,aAAKsE,MAAM+f,OAAO/f,OAAO3B,OAAO5C,OAAM;AAItC,aAAKwkB,UAAUA;AAIf,aAAKlU,WAAW;AAIhB,aAAK+gB,gBAAgB;AAIrB,aAAK9W,IAAIA;AAIT,aAAKjI,IAAIA;AAIT,aAAKmiB,kBAAkB;MACzB;;;;;;;;;MAWA,OAAOvmB,MAAM;AACX,eAAO,IAAIhK,UAAS,CAAA,CAAE;MACxB;;;;;;;;;;;;;;;;;;;;;;MAuBA,OAAO6a,QAAQ;AACb,cAAM,CAACpiB,MAAM03B,IAAI,IAAIF,SAASO,SAAS,GACrC,CAACp6B,MAAMC,OAAOC,KAAKO,MAAMC,QAAQE,QAAQ8F,WAAW,IAAIqzB;AAC1D,eAAON,QAAQ;UAAEz5B;UAAMC;UAAOC;UAAKO;UAAMC;UAAQE;UAAQ8F;WAAerE,IAAI;MAC9E;;;;;;;;;;;;;;;;;;;;;;;;;;MA2BA,OAAOwH,MAAM;AACX,cAAM,CAACxH,MAAM03B,IAAI,IAAIF,SAASO,SAAS,GACrC,CAACp6B,MAAMC,OAAOC,KAAKO,MAAMC,QAAQE,QAAQ8F,WAAW,IAAIqzB;AAE1D13B,aAAKsD,OAAO4K,gBAAgBC;AAC5B,eAAOipB,QAAQ;UAAEz5B;UAAMC;UAAOC;UAAKO;UAAMC;UAAQE;UAAQ8F;WAAerE,IAAI;MAC9E;;;;;;;;MASA,OAAOg4B,WAAWl2B,MAAM6E,UAAU,CAAA,GAAI;AACpC,cAAM5G,KAAK6V,OAAO9T,IAAI,IAAIA,KAAK6nB,QAAO,IAAK9lB;AAC3C,YAAIsV,OAAOrV,MAAM/D,EAAE,GAAG;AACpB,iBAAOwH,UAASsgB,QAAQ,eAAe;QACzC;AAEA,cAAMoQ,YAAYxpB,cAAc9H,QAAQrD,MAAMuH,SAAS8D,WAAW;AAClE,YAAI,CAACspB,UAAU33B,SAAS;AACtB,iBAAOiH,UAASsgB,QAAQ0M,gBAAgB0D,SAAS,CAAC;QACpD;AAEA,eAAO,IAAI1wB,UAAS;UAClBxH;UACAuD,MAAM20B;UACNrwB,KAAK3B,OAAOsF,WAAW5E,OAAO;QAChC,CAAC;MACH;;;;;;;;;;;;MAaA,OAAOohB,WAAW7F,cAAcvb,UAAU,CAAA,GAAI;AAC5C,YAAI,CAACmI,SAASoT,YAAY,GAAG;AAC3B,gBAAM,IAAI7kB,qBACP,yDAAwD,OAAO6kB,YAAa,eAAcA,YAAa,EAC1G;mBACSA,eAAe,CAACoS,YAAYpS,eAAeoS,UAAU;AAE9D,iBAAO/sB,UAASsgB,QAAQ,wBAAwB;QAClD,OAAO;AACL,iBAAO,IAAItgB,UAAS;YAClBxH,IAAImiB;YACJ5e,MAAMmL,cAAc9H,QAAQrD,MAAMuH,SAAS8D,WAAW;YACtD/G,KAAK3B,OAAOsF,WAAW5E,OAAO;UAChC,CAAC;QACH;MACF;;;;;;;;;;;;MAaA,OAAOuxB,YAAY9c,SAASzU,UAAU,CAAA,GAAI;AACxC,YAAI,CAACmI,SAASsM,OAAO,GAAG;AACtB,gBAAM,IAAI/d,qBAAqB,wCAAwC;QACzE,OAAO;AACL,iBAAO,IAAIkK,UAAS;YAClBxH,IAAIqb,UAAU;YACd9X,MAAMmL,cAAc9H,QAAQrD,MAAMuH,SAAS8D,WAAW;YACtD/G,KAAK3B,OAAOsF,WAAW5E,OAAO;UAChC,CAAC;QACH;MACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAmCA,OAAO4E,WAAW6I,KAAKpU,OAAO,CAAA,GAAI;AAChCoU,cAAMA,OAAO,CAAA;AACb,cAAM6jB,YAAYxpB,cAAczO,KAAKsD,MAAMuH,SAAS8D,WAAW;AAC/D,YAAI,CAACspB,UAAU33B,SAAS;AACtB,iBAAOiH,UAASsgB,QAAQ0M,gBAAgB0D,SAAS,CAAC;QACpD;AAEA,cAAMrwB,MAAM3B,OAAOsF,WAAWvL,IAAI;AAClC,cAAM2Z,aAAaF,gBAAgBrF,KAAK2iB,2BAA2B;AACnE,cAAM;UAAE1jB;UAAoBH;QAAY,IAAIiB,oBAAoBwF,YAAY/R,GAAG;AAE/E,cAAMuwB,QAAQttB,SAAS0G,IAAG,GACxB8lB,eAAe,CAACp0B,YAAYjD,KAAKkzB,cAAc,IAC3ClzB,KAAKkzB,iBACL+E,UAAU93B,OAAOg4B,KAAK,GAC1BC,kBAAkB,CAACn1B,YAAY0W,WAAW/G,OAAO,GACjDylB,qBAAqB,CAACp1B,YAAY0W,WAAWhc,IAAI,GACjD26B,mBAAmB,CAACr1B,YAAY0W,WAAW/b,KAAK,KAAK,CAACqF,YAAY0W,WAAW9b,GAAG,GAChF06B,iBAAiBF,sBAAsBC,kBACvCE,kBAAkB7e,WAAWpG,YAAYoG,WAAWrG;AAQtD,aAAKilB,kBAAkBH,oBAAoBI,iBAAiB;AAC1D,gBAAM,IAAIt7B,8BACR,qEACF;QACF;AAEA,YAAIo7B,oBAAoBF,iBAAiB;AACvC,gBAAM,IAAIl7B,8BAA8B,wCAAwC;QAClF;AAEA,cAAMu7B,cAAcD,mBAAoB7e,WAAW3b,WAAW,CAACu6B;AAG/D,YAAIxd,OACF2d,eACAC,SAASxD,QAAQgD,OAAOd,YAAY;AACtC,YAAIoB,aAAa;AACf1d,kBAAQyb;AACRkC,0BAAgBpC;AAChBqC,mBAASxlB,gBAAgBwlB,QAAQtlB,oBAAoBH,WAAW;mBACvDklB,iBAAiB;AAC1Brd,kBAAQ0b;AACRiC,0BAAgBnC;AAChBoC,mBAAS5kB,mBAAmB4kB,MAAM;QACpC,OAAO;AACL5d,kBAAQuL;AACRoS,0BAAgBrC;QAClB;AAGA,YAAIuC,aAAa;AACjB,mBAAWhf,KAAKmB,OAAO;AACrB,gBAAM7D,IAAIyC,WAAWC,CAAC;AACtB,cAAI,CAAC3W,YAAYiU,CAAC,GAAG;AACnB0hB,yBAAa;qBACJA,YAAY;AACrBjf,uBAAWC,CAAC,IAAI8e,cAAc9e,CAAC;UACjC,OAAO;AACLD,uBAAWC,CAAC,IAAI+e,OAAO/e,CAAC;UAC1B;QACF;AAGA,cAAMif,qBAAqBJ,cACrB/jB,mBAAmBiF,YAAYtG,oBAAoBH,WAAW,IAC9DklB,kBACApjB,sBAAsB2E,UAAU,IAChCzE,wBAAwByE,UAAU,GACtCkO,UAAUgR,sBAAsBvjB,mBAAmBqE,UAAU;AAE/D,YAAIkO,SAAS;AACX,iBAAOtgB,UAASsgB,QAAQA,OAAO;QACjC;AAGA,cAAMiR,YAAYL,cACZ/kB,gBAAgBiG,YAAYtG,oBAAoBH,WAAW,IAC3DklB,kBACAnkB,mBAAmB0F,UAAU,IAC7BA,YACJ,CAACof,SAASC,WAAW,IAAItD,QAAQoD,WAAWzB,cAAcY,SAAS,GACnEtD,OAAO,IAAIptB,UAAS;UAClBxH,IAAIg5B;UACJz1B,MAAM20B;UACNtiB,GAAGqjB;UACHpxB;QACF,CAAC;AAGH,YAAI+R,WAAW3b,WAAWu6B,kBAAkBnkB,IAAIpW,YAAY22B,KAAK32B,SAAS;AACxE,iBAAOuJ,UAASsgB,QACd,sBACC,uCAAsClO,WAAW3b,OAAQ,kBAAiB22B,KAAK7L,MAAK,CAAG,EAC1F;QACF;AAEA,YAAI,CAAC6L,KAAKr0B,SAAS;AACjB,iBAAOiH,UAASsgB,QAAQ8M,KAAK9M,OAAO;QACtC;AAEA,eAAO8M;MACT;;;;;;;;;;;;;;;;;;MAmBA,OAAOvM,QAAQC,MAAMroB,OAAO,CAAA,GAAI;AAC9B,cAAM,CAAC+mB,MAAMgP,UAAU,IAAIzQ,aAAa+C,IAAI;AAC5C,eAAOyN,oBAAoB/O,MAAMgP,YAAY/1B,MAAM,YAAYqoB,IAAI;MACrE;;;;;;;;;;;;;;;;MAiBA,OAAO4Q,YAAY5Q,MAAMroB,OAAO,CAAA,GAAI;AAClC,cAAM,CAAC+mB,MAAMgP,UAAU,IAAIxQ,iBAAiB8C,IAAI;AAChD,eAAOyN,oBAAoB/O,MAAMgP,YAAY/1B,MAAM,YAAYqoB,IAAI;MACrE;;;;;;;;;;;;;;;;;MAkBA,OAAO6Q,SAAS7Q,MAAMroB,OAAO,CAAA,GAAI;AAC/B,cAAM,CAAC+mB,MAAMgP,UAAU,IAAIvQ,cAAc6C,IAAI;AAC7C,eAAOyN,oBAAoB/O,MAAMgP,YAAY/1B,MAAM,QAAQA,IAAI;MACjE;;;;;;;;;;;;;;;MAgBA,OAAOm5B,WAAW9Q,MAAM7K,KAAKxd,OAAO,CAAA,GAAI;AACtC,YAAIiD,YAAYolB,IAAI,KAAKplB,YAAYua,GAAG,GAAG;AACzC,gBAAM,IAAIngB,qBAAqB,kDAAkD;QACnF;AAEA,cAAM;UAAEyD,SAAS;UAAMgG,kBAAkB;QAAK,IAAI9G,MAChDo5B,cAAcnzB,OAAOwE,SAAS;UAC5B3J;UACAgG;UACA6D,aAAa;QACf,CAAC,GACD,CAACoc,MAAMgP,YAAY7C,gBAAgBrL,OAAO,IAAIuM,gBAAgBgF,aAAa/Q,MAAM7K,GAAG;AACtF,YAAIqK,SAAS;AACX,iBAAOtgB,UAASsgB,QAAQA,OAAO;QACjC,OAAO;AACL,iBAAOiO,oBAAoB/O,MAAMgP,YAAY/1B,MAAO,UAASwd,GAAI,IAAG6K,MAAM6K,cAAc;QAC1F;MACF;;;;MAKA,OAAOmG,WAAWhR,MAAM7K,KAAKxd,OAAO,CAAA,GAAI;AACtC,eAAOuH,UAAS4xB,WAAW9Q,MAAM7K,KAAKxd,IAAI;MAC5C;;;;;;;;;;;;;;;;;;;;;;MAuBA,OAAOs5B,QAAQjR,MAAMroB,OAAO,CAAA,GAAI;AAC9B,cAAM,CAAC+mB,MAAMgP,UAAU,IAAIhQ,SAASsC,IAAI;AACxC,eAAOyN,oBAAoB/O,MAAMgP,YAAY/1B,MAAM,OAAOqoB,IAAI;MAChE;;;;;;;MAQA,OAAOR,QAAQ/qB,QAAQgV,cAAc,MAAM;AACzC,YAAI,CAAChV,QAAQ;AACX,gBAAM,IAAIO,qBAAqB,kDAAkD;QACnF;AAEA,cAAMwqB,UAAU/qB,kBAAkB+U,UAAU/U,SAAS,IAAI+U,QAAQ/U,QAAQgV,WAAW;AAEpF,YAAIjH,SAAS4G,gBAAgB;AAC3B,gBAAM,IAAI7U,qBAAqBirB,OAAO;QACxC,OAAO;AACL,iBAAO,IAAItgB,UAAS;YAAEsgB;UAAQ,CAAC;QACjC;MACF;;;;;;MAOA,OAAO0R,WAAW5jB,GAAG;AACnB,eAAQA,KAAKA,EAAEmiB,mBAAoB;MACrC;;;;;;;MAQA,OAAO0B,mBAAmBzb,YAAY0b,aAAa,CAAA,GAAI;AACrD,cAAMC,YAAY9F,mBAAmB7V,YAAY9X,OAAOsF,WAAWkuB,UAAU,CAAC;AAC9E,eAAO,CAACC,YAAY,OAAOA,UAAUjwB,IAAKkI,OAAOA,IAAIA,EAAEsK,MAAM,IAAK,EAAEvS,KAAK,EAAE;MAC7E;;;;;;;;MASA,OAAOiwB,aAAanc,KAAKic,aAAa,CAAA,GAAI;AACxC,cAAMG,WAAW/F,kBAAkBvW,UAAUC,YAAYC,GAAG,GAAGvX,OAAOsF,WAAWkuB,UAAU,CAAC;AAC5F,eAAOG,SAASnwB,IAAKkI,OAAMA,EAAEsK,GAAG,EAAEvS,KAAK,EAAE;MAC3C;MAEA,OAAOnG,aAAa;AAClB0zB,uBAAez1B;AACf21B,6BAAqB3zB,MAAK;MAC5B;;;;;;;;;MAWAjC,IAAInE,MAAM;AACR,eAAO,KAAKA,IAAI;MAClB;;;;;;;MAQA,IAAIkD,UAAU;AACZ,eAAO,KAAKunB,YAAY;MAC1B;;;;;MAMA,IAAI6B,gBAAgB;AAClB,eAAO,KAAK7B,UAAU,KAAKA,QAAQ/qB,SAAS;MAC9C;;;;;MAMA,IAAI+tB,qBAAqB;AACvB,eAAO,KAAKhD,UAAU,KAAKA,QAAQ/V,cAAc;MACnD;;;;;;MAOA,IAAIhR,SAAS;AACX,eAAO,KAAKR,UAAU,KAAKsH,IAAI9G,SAAS;MAC1C;;;;;;MAOA,IAAIgG,kBAAkB;AACpB,eAAO,KAAKxG,UAAU,KAAKsH,IAAId,kBAAkB;MACnD;;;;;;MAOA,IAAIG,iBAAiB;AACnB,eAAO,KAAK3G,UAAU,KAAKsH,IAAIX,iBAAiB;MAClD;;;;;MAMA,IAAI3D,OAAO;AACT,eAAO,KAAKu0B;MACd;;;;;MAMA,IAAIx2B,WAAW;AACb,eAAO,KAAKf,UAAU,KAAKgD,KAAK3D,OAAO;MACzC;;;;;;MAOA,IAAIhC,OAAO;AACT,eAAO,KAAK2C,UAAU,KAAKsd,EAAEjgB,OAAOkG;MACtC;;;;;;MAOA,IAAIsb,UAAU;AACZ,eAAO,KAAK7e,UAAU0D,KAAK61B,KAAK,KAAKjc,EAAEhgB,QAAQ,CAAC,IAAIiG;MACtD;;;;;;MAOA,IAAIjG,QAAQ;AACV,eAAO,KAAK0C,UAAU,KAAKsd,EAAEhgB,QAAQiG;MACvC;;;;;;MAOA,IAAIhG,MAAM;AACR,eAAO,KAAKyC,UAAU,KAAKsd,EAAE/f,MAAMgG;MACrC;;;;;;MAOA,IAAIzF,OAAO;AACT,eAAO,KAAKkC,UAAU,KAAKsd,EAAExf,OAAOyF;MACtC;;;;;;MAOA,IAAIxF,SAAS;AACX,eAAO,KAAKiC,UAAU,KAAKsd,EAAEvf,SAASwF;MACxC;;;;;;MAOA,IAAItF,SAAS;AACX,eAAO,KAAK+B,UAAU,KAAKsd,EAAErf,SAASsF;MACxC;;;;;;MAOA,IAAIQ,cAAc;AAChB,eAAO,KAAK/D,UAAU,KAAKsd,EAAEvZ,cAAcR;MAC7C;;;;;;;MAQA,IAAI0P,WAAW;AACb,eAAO,KAAKjT,UAAUk0B,uBAAuB,IAAI,EAAEjhB,WAAW1P;MAChE;;;;;;;MAQA,IAAIyP,aAAa;AACf,eAAO,KAAKhT,UAAUk0B,uBAAuB,IAAI,EAAElhB,aAAazP;MAClE;;;;;;;;MASA,IAAI7F,UAAU;AACZ,eAAO,KAAKsC,UAAUk0B,uBAAuB,IAAI,EAAEx2B,UAAU6F;MAC/D;;;;;MAMA,IAAIi2B,YAAY;AACd,eAAO,KAAKx5B,WAAW,KAAKsH,IAAImG,eAAc,EAAG7G,SAAS,KAAKlJ,OAAO;MACxE;;;;;;;MAQA,IAAIsW,eAAe;AACjB,eAAO,KAAKhU,UAAUm0B,4BAA4B,IAAI,EAAEz2B,UAAU6F;MACpE;;;;;;;MAQA,IAAI0Q,kBAAkB;AACpB,eAAO,KAAKjU,UAAUm0B,4BAA4B,IAAI,EAAEnhB,aAAazP;MACvE;;;;;;MAOA,IAAI2Q,gBAAgB;AAClB,eAAO,KAAKlU,UAAUm0B,4BAA4B,IAAI,EAAElhB,WAAW1P;MACrE;;;;;;MAOA,IAAI+O,UAAU;AACZ,eAAO,KAAKtS,UAAUyT,mBAAmB,KAAK6J,CAAC,EAAEhL,UAAU/O;MAC7D;;;;;;;MAQA,IAAIk2B,aAAa;AACf,eAAO,KAAKz5B,UAAUkuB,KAAK9hB,OAAO,SAAS;UAAEkiB,QAAQ,KAAKhnB;SAAK,EAAE,KAAKhK,QAAQ,CAAC,IAAI;MACrF;;;;;;;MAQA,IAAIo8B,YAAY;AACd,eAAO,KAAK15B,UAAUkuB,KAAK9hB,OAAO,QAAQ;UAAEkiB,QAAQ,KAAKhnB;SAAK,EAAE,KAAKhK,QAAQ,CAAC,IAAI;MACpF;;;;;;;MAQA,IAAIq8B,eAAe;AACjB,eAAO,KAAK35B,UAAUkuB,KAAK3hB,SAAS,SAAS;UAAE+hB,QAAQ,KAAKhnB;SAAK,EAAE,KAAK5J,UAAU,CAAC,IAAI;MACzF;;;;;;;MAQA,IAAIk8B,cAAc;AAChB,eAAO,KAAK55B,UAAUkuB,KAAK3hB,SAAS,QAAQ;UAAE+hB,QAAQ,KAAKhnB;SAAK,EAAE,KAAK5J,UAAU,CAAC,IAAI;MACxF;;;;;;;MAQA,IAAImC,SAAS;AACX,eAAO,KAAKG,UAAU,CAAC,KAAKqV,IAAI9R;MAClC;;;;;;MAOA,IAAIs2B,kBAAkB;AACpB,YAAI,KAAK75B,SAAS;AAChB,iBAAO,KAAKgD,KAAKxD,WAAW,KAAKC,IAAI;YACnCG,QAAQ;YACRY,QAAQ,KAAKA;UACf,CAAC;QACH,OAAO;AACL,iBAAO;QACT;MACF;;;;;;MAOA,IAAIs5B,iBAAiB;AACnB,YAAI,KAAK95B,SAAS;AAChB,iBAAO,KAAKgD,KAAKxD,WAAW,KAAKC,IAAI;YACnCG,QAAQ;YACRY,QAAQ,KAAKA;UACf,CAAC;QACH,OAAO;AACL,iBAAO;QACT;MACF;;;;;MAMA,IAAIge,gBAAgB;AAClB,eAAO,KAAKxe,UAAU,KAAKgD,KAAKzD,cAAc;MAChD;;;;;MAMA,IAAIw6B,UAAU;AACZ,YAAI,KAAKvb,eAAe;AACtB,iBAAO;QACT,OAAO;AACL,iBACE,KAAK3e,SAAS,KAAKwB,IAAI;YAAE/D,OAAO;YAAGC,KAAK;WAAG,EAAEsC,UAC7C,KAAKA,SAAS,KAAKwB,IAAI;YAAE/D,OAAO;WAAG,EAAEuC;QAEzC;MACF;;;;;;;;MASAm6B,qBAAqB;AACnB,YAAI,CAAC,KAAKh6B,WAAW,KAAKwe,eAAe;AACvC,iBAAO,CAAC,IAAI;QACd;AACA,cAAMyb,QAAQ;AACd,cAAMC,WAAW;AACjB,cAAM1F,UAAU1wB,aAAa,KAAKwZ,CAAC;AACnC,cAAM6c,WAAW,KAAKn3B,KAAKnD,OAAO20B,UAAUyF,KAAK;AACjD,cAAMG,SAAS,KAAKp3B,KAAKnD,OAAO20B,UAAUyF,KAAK;AAE/C,cAAMI,KAAK,KAAKr3B,KAAKnD,OAAO20B,UAAU2F,WAAWD,QAAQ;AACzD,cAAMvF,KAAK,KAAK3xB,KAAKnD,OAAO20B,UAAU4F,SAASF,QAAQ;AACvD,YAAIG,OAAO1F,IAAI;AACb,iBAAO,CAAC,IAAI;QACd;AACA,cAAM2F,MAAM9F,UAAU6F,KAAKH;AAC3B,cAAMK,MAAM/F,UAAUG,KAAKuF;AAC3B,cAAMM,KAAK3F,QAAQyF,KAAKD,EAAE;AAC1B,cAAMI,KAAK5F,QAAQ0F,KAAK5F,EAAE;AAC1B,YACE6F,GAAG18B,SAAS28B,GAAG38B,QACf08B,GAAGz8B,WAAW08B,GAAG18B,UACjBy8B,GAAGv8B,WAAWw8B,GAAGx8B,UACjBu8B,GAAGz2B,gBAAgB02B,GAAG12B,aACtB;AACA,iBAAO,CAACgI,MAAM,MAAM;YAAEtM,IAAI66B;UAAI,CAAC,GAAGvuB,MAAM,MAAM;YAAEtM,IAAI86B;UAAI,CAAC,CAAC;QAC5D;AACA,eAAO,CAAC,IAAI;MACd;;;;;;;MAQA,IAAIG,eAAe;AACjB,eAAOtoB,WAAW,KAAK/U,IAAI;MAC7B;;;;;;;MAQA,IAAI0X,cAAc;AAChB,eAAOA,YAAY,KAAK1X,MAAM,KAAKC,KAAK;MAC1C;;;;;;;MAQA,IAAIkW,aAAa;AACf,eAAO,KAAKxT,UAAUwT,WAAW,KAAKnW,IAAI,IAAIkG;MAChD;;;;;;;;MASA,IAAI2P,kBAAkB;AACpB,eAAO,KAAKlT,UAAUkT,gBAAgB,KAAKD,QAAQ,IAAI1P;MACzD;;;;;;;MAQA,IAAIo3B,uBAAuB;AACzB,eAAO,KAAK36B,UACRkT,gBACE,KAAKgB,eACL,KAAK5M,IAAIkG,sBAAqB,GAC9B,KAAKlG,IAAIiG,eAAc,CACzB,IACAhK;MACN;;;;;;;MAQAq3B,sBAAsBl7B,OAAO,CAAA,GAAI;AAC/B,cAAM;UAAEc;UAAQgG;UAAiBC;YAAauW,UAAUja,OACtD,KAAKuE,IAAIyE,MAAMrM,IAAI,GACnBA,IACF,EAAEY,gBAAgB,IAAI;AACtB,eAAO;UAAEE;UAAQgG;UAAiBG,gBAAgBF;;MACpD;;;;;;;;;;MAYAyoB,MAAMrvB,UAAS,GAAGH,OAAO,CAAA,GAAI;AAC3B,eAAO,KAAKsJ,QAAQ4E,gBAAgBzN,SAASN,OAAM,GAAGH,IAAI;MAC5D;;;;;;;MAQAm7B,UAAU;AACR,eAAO,KAAK7xB,QAAQuB,SAAS8D,WAAW;MAC1C;;;;;;;;;;MAWArF,QAAQhG,MAAM;QAAEmsB,gBAAgB;QAAO2L,mBAAmB;UAAU,CAAA,GAAI;AACtE93B,eAAOmL,cAAcnL,MAAMuH,SAAS8D,WAAW;AAC/C,YAAIrL,KAAKlD,OAAO,KAAKkD,IAAI,GAAG;AAC1B,iBAAO;QACT,WAAW,CAACA,KAAKhD,SAAS;AACxB,iBAAOiH,UAASsgB,QAAQ0M,gBAAgBjxB,IAAI,CAAC;QAC/C,OAAO;AACL,cAAI+3B,QAAQ,KAAKt7B;AACjB,cAAI0vB,iBAAiB2L,kBAAkB;AACrC,kBAAMlE,cAAc5zB,KAAKnD,OAAO,KAAKJ,EAAE;AACvC,kBAAMu7B,QAAQ,KAAKzS,SAAQ;AAC3B,aAACwS,KAAK,IAAI3F,QAAQ4F,OAAOpE,aAAa5zB,IAAI;UAC5C;AACA,iBAAO+I,MAAM,MAAM;YAAEtM,IAAIs7B;YAAO/3B;UAAK,CAAC;QACxC;MACF;;;;;;;MAQA4mB,YAAY;QAAEppB;QAAQgG;QAAiBG;UAAmB,CAAA,GAAI;AAC5D,cAAMW,MAAM,KAAKA,IAAIyE,MAAM;UAAEvL;UAAQgG;UAAiBG;QAAe,CAAC;AACtE,eAAOoF,MAAM,MAAM;UAAEzE;QAAI,CAAC;MAC5B;;;;;;;MAQA2zB,UAAUz6B,QAAQ;AAChB,eAAO,KAAKopB,YAAY;UAAEppB;QAAO,CAAC;MACpC;;;;;;;;;;;;;;MAeAa,IAAI+kB,QAAQ;AACV,YAAI,CAAC,KAAKpmB,QAAS,QAAO;AAE1B,cAAMqZ,aAAaF,gBAAgBiN,QAAQqQ,2BAA2B;AACtE,cAAM;UAAE1jB;UAAoBH;YAAgBiB,oBAAoBwF,YAAY,KAAK/R,GAAG;AAEpF,cAAM4zB,mBACF,CAACv4B,YAAY0W,WAAWpG,QAAQ,KAChC,CAACtQ,YAAY0W,WAAWrG,UAAU,KAClC,CAACrQ,YAAY0W,WAAW3b,OAAO,GACjCo6B,kBAAkB,CAACn1B,YAAY0W,WAAW/G,OAAO,GACjDylB,qBAAqB,CAACp1B,YAAY0W,WAAWhc,IAAI,GACjD26B,mBAAmB,CAACr1B,YAAY0W,WAAW/b,KAAK,KAAK,CAACqF,YAAY0W,WAAW9b,GAAG,GAChF06B,iBAAiBF,sBAAsBC,kBACvCE,kBAAkB7e,WAAWpG,YAAYoG,WAAWrG;AAEtD,aAAKilB,kBAAkBH,oBAAoBI,iBAAiB;AAC1D,gBAAM,IAAIt7B,8BACR,qEACF;QACF;AAEA,YAAIo7B,oBAAoBF,iBAAiB;AACvC,gBAAM,IAAIl7B,8BAA8B,wCAAwC;QAClF;AAEA,YAAI+sB;AACJ,YAAIuR,kBAAkB;AACpBvR,kBAAQvW,gBACN;YAAE,GAAGP,gBAAgB,KAAKyK,GAAGvK,oBAAoBH,WAAW;YAAG,GAAGyG;UAAW,GAC7EtG,oBACAH,WACF;mBACS,CAACjQ,YAAY0W,WAAW/G,OAAO,GAAG;AAC3CqX,kBAAQhW,mBAAmB;YAAE,GAAGF,mBAAmB,KAAK6J,CAAC;YAAG,GAAGjE;UAAW,CAAC;QAC7E,OAAO;AACLsQ,kBAAQ;YAAE,GAAG,KAAKpB,SAAQ;YAAI,GAAGlP;;AAIjC,cAAI1W,YAAY0W,WAAW9b,GAAG,GAAG;AAC/BosB,kBAAMpsB,MAAMmG,KAAK6M,IAAIwE,YAAY4U,MAAMtsB,MAAMssB,MAAMrsB,KAAK,GAAGqsB,MAAMpsB,GAAG;UACtE;QACF;AAEA,cAAM,CAACkC,IAAI4V,CAAC,IAAI+f,QAAQzL,OAAO,KAAKtU,GAAG,KAAKrS,IAAI;AAChD,eAAO+I,MAAM,MAAM;UAAEtM;UAAI4V;QAAE,CAAC;MAC9B;;;;;;;;;;;;;;MAeApM,KAAKqgB,UAAU;AACb,YAAI,CAAC,KAAKtpB,QAAS,QAAO;AAC1B,cAAM+e,MAAMwH,SAASoB,iBAAiB2B,QAAQ;AAC9C,eAAOvd,MAAM,MAAMspB,WAAW,MAAMtW,GAAG,CAAC;MAC1C;;;;;;;MAQAwK,MAAMD,UAAU;AACd,YAAI,CAAC,KAAKtpB,QAAS,QAAO;AAC1B,cAAM+e,MAAMwH,SAASoB,iBAAiB2B,QAAQ,EAAEE,OAAM;AACtD,eAAOzd,MAAM,MAAMspB,WAAW,MAAMtW,GAAG,CAAC;MAC1C;;;;;;;;;;;;;MAcA2M,QAAQ5uB,MAAM;QAAE6uB,iBAAiB;UAAU,CAAA,GAAI;AAC7C,YAAI,CAAC,KAAK3rB,QAAS,QAAO;AAE1B,cAAMqV,IAAI,CAAA,GACR8lB,iBAAiB5U,SAASmB,cAAc5qB,IAAI;AAC9C,gBAAQq+B,gBAAc;UACpB,KAAK;AACH9lB,cAAE/X,QAAQ;UAEZ,KAAK;UACL,KAAK;AACH+X,cAAE9X,MAAM;UAEV,KAAK;UACL,KAAK;AACH8X,cAAEvX,OAAO;UAEX,KAAK;AACHuX,cAAEtX,SAAS;UAEb,KAAK;AACHsX,cAAEpX,SAAS;UAEb,KAAK;AACHoX,cAAEtR,cAAc;AAChB;QAIJ;AAEA,YAAIo3B,mBAAmB,SAAS;AAC9B,cAAIxP,gBAAgB;AAClB,kBAAM/Y,cAAc,KAAKtL,IAAIiG,eAAc;AAC3C,kBAAM;cAAE7P;YAAQ,IAAI;AACpB,gBAAIA,UAAUkV,aAAa;AACzByC,gBAAErC,aAAa,KAAKA,aAAa;YACnC;AACAqC,cAAE3X,UAAUkV;UACd,OAAO;AACLyC,cAAE3X,UAAU;UACd;QACF;AAEA,YAAIy9B,mBAAmB,YAAY;AACjC,gBAAMrI,IAAIpvB,KAAK61B,KAAK,KAAKj8B,QAAQ,CAAC;AAClC+X,YAAE/X,SAASw1B,IAAI,KAAK,IAAI;QAC1B;AAEA,eAAO,KAAKzxB,IAAIgU,CAAC;MACnB;;;;;;;;;;;;;MAcA+lB,MAAMt+B,MAAM4C,MAAM;AAChB,eAAO,KAAKM,UACR,KAAKiJ,KAAK;UAAE,CAACnM,IAAI,GAAG;QAAE,CAAC,EACpB4uB,QAAQ5uB,MAAM4C,IAAI,EAClB6pB,MAAM,CAAC,IACV;MACN;;;;;;;;;;;;;;MAgBArB,SAAShL,KAAKxd,OAAO,CAAA,GAAI;AACvB,eAAO,KAAKM,UACRgd,UAAUja,OAAO,KAAKuE,IAAI4E,cAAcxM,IAAI,CAAC,EAAE2e,yBAAyB,MAAMnB,GAAG,IACjFwI;MACN;;;;;;;;;;;;;;;;;;;;MAqBAkI,eAAenQ,aAAa3B,YAAoBpc,OAAO,CAAA,GAAI;AACzD,eAAO,KAAKM,UACRgd,UAAUja,OAAO,KAAKuE,IAAIyE,MAAMrM,IAAI,GAAG+d,UAAU,EAAEG,eAAe,IAAI,IACtE8H;MACN;;;;;;;;;;;;;;MAeA2V,cAAc37B,OAAO,CAAA,GAAI;AACvB,eAAO,KAAKM,UACRgd,UAAUja,OAAO,KAAKuE,IAAIyE,MAAMrM,IAAI,GAAGA,IAAI,EAAEme,oBAAoB,IAAI,IACrE,CAAA;MACN;;;;;;;;;;;;;;;MAgBA2K,MAAM;QACJ5oB,SAAS;QACTipB,kBAAkB;QAClBD,uBAAuB;QACvBG,gBAAgB;QAChB+M,eAAe;UACb,CAAA,GAAI;AACN,YAAI,CAAC,KAAK91B,SAAS;AACjB,iBAAO;QACT;AAEA,cAAMs7B,MAAM17B,WAAW;AAEvB,YAAI0d,IAAIuQ,UAAU,MAAMyN,GAAG;AAC3Bhe,aAAK;AACLA,aAAKmL,UAAU,MAAM6S,KAAKzS,iBAAiBD,sBAAsBG,eAAe+M,YAAY;AAC5F,eAAOxY;MACT;;;;;;;;;MAUAuQ,UAAU;QAAEjuB,SAAS;UAAe,CAAA,GAAI;AACtC,YAAI,CAAC,KAAKI,SAAS;AACjB,iBAAO;QACT;AAEA,eAAO6tB,UAAU,MAAMjuB,WAAW,UAAU;MAC9C;;;;;;MAOA27B,gBAAgB;AACd,eAAO5F,aAAa,MAAM,cAAc;MAC1C;;;;;;;;;;;;;;;;MAiBAlN,UAAU;QACRG,uBAAuB;QACvBC,kBAAkB;QAClBE,gBAAgB;QAChBD,gBAAgB;QAChBgN,eAAe;QACfl2B,SAAS;UACP,CAAA,GAAI;AACN,YAAI,CAAC,KAAKI,SAAS;AACjB,iBAAO;QACT;AAEA,YAAIsd,IAAIwL,gBAAgB,MAAM;AAC9B,eACExL,IACAmL,UACE,MACA7oB,WAAW,YACXipB,iBACAD,sBACAG,eACA+M,YACF;MAEJ;;;;;;;MAQA0F,YAAY;AACV,eAAO7F,aAAa,MAAM,iCAAiC,KAAK;MAClE;;;;;;;;;MAUA8F,SAAS;AACP,eAAO9F,aAAa,KAAKzG,MAAK,GAAI,iCAAiC;MACrE;;;;;;MAOAwM,YAAY;AACV,YAAI,CAAC,KAAK17B,SAAS;AACjB,iBAAO;QACT;AACA,eAAO6tB,UAAU,MAAM,IAAI;MAC7B;;;;;;;;;;;;;MAcA8N,UAAU;QAAE5S,gBAAgB;QAAM6S,cAAc;QAAOC,qBAAqB;UAAS,CAAA,GAAI;AACvF,YAAI3e,MAAM;AAEV,YAAI0e,eAAe7S,eAAe;AAChC,cAAI8S,oBAAoB;AACtB3e,mBAAO;UACT;AACA,cAAI0e,aAAa;AACf1e,mBAAO;qBACE6L,eAAe;AACxB7L,mBAAO;UACT;QACF;AAEA,eAAOyY,aAAa,MAAMzY,KAAK,IAAI;MACrC;;;;;;;;;;;;;MAcA4e,MAAMp8B,OAAO,CAAA,GAAI;AACf,YAAI,CAAC,KAAKM,SAAS;AACjB,iBAAO;QACT;AAEA,eAAQ,GAAE,KAAK07B,UAAS,CAAG,IAAG,KAAKC,UAAUj8B,IAAI,CAAE;MACrD;;;;;MAMAiO,WAAW;AACT,eAAO,KAAK3N,UAAU,KAAKwoB,MAAK,IAAK9C;MACvC;;;;;MAMA,CAACwD,OAAOC,IAAI,4BAA4B,CAAC,IAAI;AAC3C,YAAI,KAAKnpB,SAAS;AAChB,iBAAQ,kBAAiB,KAAKwoB,MAAK,CAAG,WAAU,KAAKxlB,KAAK3D,IAAK,aAAY,KAAKmB,MAAO;QACzF,OAAO;AACL,iBAAQ,+BAA8B,KAAK4oB,aAAc;QAC3D;MACF;;;;;MAMAC,UAAU;AACR,eAAO,KAAKV,SAAQ;MACtB;;;;;MAMAA,WAAW;AACT,eAAO,KAAK3oB,UAAU,KAAKP,KAAK8D;MAClC;;;;;MAMAw4B,YAAY;AACV,eAAO,KAAK/7B,UAAU,KAAKP,KAAK,MAAO8D;MACzC;;;;;MAMAy4B,gBAAgB;AACd,eAAO,KAAKh8B,UAAU0D,KAAKuE,MAAM,KAAKxI,KAAK,GAAI,IAAI8D;MACrD;;;;;MAMA0lB,SAAS;AACP,eAAO,KAAKT,MAAK;MACnB;;;;;MAMAyT,SAAS;AACP,eAAO,KAAK5yB,SAAQ;MACtB;;;;;;;;MASAkf,SAAS7oB,OAAO,CAAA,GAAI;AAClB,YAAI,CAAC,KAAKM,QAAS,QAAO,CAAA;AAE1B,cAAMiF,OAAO;UAAE,GAAG,KAAKqY;;AAEvB,YAAI5d,KAAKw8B,eAAe;AACtBj3B,eAAK0B,iBAAiB,KAAKA;AAC3B1B,eAAKuB,kBAAkB,KAAKc,IAAId;AAChCvB,eAAKzE,SAAS,KAAK8G,IAAI9G;QACzB;AACA,eAAOyE;MACT;;;;;MAMAoE,WAAW;AACT,eAAO,IAAI3I,KAAK,KAAKV,UAAU,KAAKP,KAAK8D,GAAG;MAC9C;;;;;;;;;;;;;;;;;MAmBAqoB,KAAKuQ,eAAer/B,OAAO,gBAAgB4C,OAAO,CAAA,GAAI;AACpD,YAAI,CAAC,KAAKM,WAAW,CAACm8B,cAAcn8B,SAAS;AAC3C,iBAAOumB,SAASgB,QAAQ,wCAAwC;QAClE;AAEA,cAAM6U,UAAU;UAAE57B,QAAQ,KAAKA;UAAQgG,iBAAiB,KAAKA;UAAiB,GAAG9G;;AAEjF,cAAM+a,QAAQhF,WAAW3Y,IAAI,EAAEqM,IAAIod,SAASmB,aAAa,GACvD2U,eAAeF,cAAc9S,QAAO,IAAK,KAAKA,QAAO,GACrD0F,UAAUsN,eAAe,OAAOF,eAChCnN,QAAQqN,eAAeF,gBAAgB,MACvCG,SAAS1Q,KAAKmD,SAASC,OAAOvU,OAAO2hB,OAAO;AAE9C,eAAOC,eAAeC,OAAO9S,OAAM,IAAK8S;MAC1C;;;;;;;;;MAUAC,QAAQz/B,OAAO,gBAAgB4C,OAAO,CAAA,GAAI;AACxC,eAAO,KAAKksB,KAAK3kB,UAASgK,IAAG,GAAInU,MAAM4C,IAAI;MAC7C;;;;;;MAOA88B,MAAML,eAAe;AACnB,eAAO,KAAKn8B,UAAU4qB,SAASE,cAAc,MAAMqR,aAAa,IAAI;MACtE;;;;;;;;;;;;MAaAtQ,QAAQsQ,eAAer/B,MAAM4C,MAAM;AACjC,YAAI,CAAC,KAAKM,QAAS,QAAO;AAE1B,cAAMy8B,UAAUN,cAAc9S,QAAO;AACrC,cAAMqT,iBAAiB,KAAK1zB,QAAQmzB,cAAcn5B,MAAM;UAAEmsB,eAAe;QAAK,CAAC;AAC/E,eACEuN,eAAehR,QAAQ5uB,MAAM4C,IAAI,KAAK+8B,WAAWA,WAAWC,eAAetB,MAAMt+B,MAAM4C,IAAI;MAE/F;;;;;;;;MASAI,OAAO4N,OAAO;AACZ,eACE,KAAK1N,WACL0N,MAAM1N,WACN,KAAKqpB,QAAO,MAAO3b,MAAM2b,QAAO,KAChC,KAAKrmB,KAAKlD,OAAO4N,MAAM1K,IAAI,KAC3B,KAAKsE,IAAIxH,OAAO4N,MAAMpG,GAAG;MAE7B;;;;;;;;;;;;;;;;;;;MAoBAq1B,WAAWt2B,UAAU,CAAA,GAAI;AACvB,YAAI,CAAC,KAAKrG,QAAS,QAAO;AAC1B,cAAMiF,OAAOoB,QAAQpB,QAAQgC,UAASgE,WAAW,CAAA,GAAI;UAAEjI,MAAM,KAAKA;QAAK,CAAC,GACtE45B,UAAUv2B,QAAQu2B,UAAW,OAAO33B,OAAO,CAACoB,QAAQu2B,UAAUv2B,QAAQu2B,UAAW;AACnF,YAAIniB,QAAQ,CAAC,SAAS,UAAU,QAAQ,SAAS,WAAW,SAAS;AACrE,YAAI3d,OAAOuJ,QAAQvJ;AACnB,YAAI6Y,MAAMC,QAAQvP,QAAQvJ,IAAI,GAAG;AAC/B2d,kBAAQpU,QAAQvJ;AAChBA,iBAAOoE;QACT;AACA,eAAO81B,aAAa/xB,MAAM,KAAKgE,KAAK2zB,OAAO,GAAG;UAC5C,GAAGv2B;UACH0D,SAAS;UACT0Q;UACA3d;QACF,CAAC;MACH;;;;;;;;;;;;;;MAeA+/B,mBAAmBx2B,UAAU,CAAA,GAAI;AAC/B,YAAI,CAAC,KAAKrG,QAAS,QAAO;AAE1B,eAAOg3B,aAAa3wB,QAAQpB,QAAQgC,UAASgE,WAAW,CAAA,GAAI;UAAEjI,MAAM,KAAKA;SAAM,GAAG,MAAM;UACtF,GAAGqD;UACH0D,SAAS;UACT0Q,OAAO,CAAC,SAAS,UAAU,MAAM;UACjCwc,WAAW;QACb,CAAC;MACH;;;;;;MAOA,OAAO1mB,OAAO4b,WAAW;AACvB,YAAI,CAACA,UAAU2Q,MAAM71B,UAASgyB,UAAU,GAAG;AACzC,gBAAM,IAAIl8B,qBAAqB,yCAAyC;QAC1E;AACA,eAAO8Y,OAAOsW,WAAY5pB,OAAMA,EAAE8mB,QAAO,GAAI3lB,KAAK6M,GAAG;MACvD;;;;;;MAOA,OAAOC,OAAO2b,WAAW;AACvB,YAAI,CAACA,UAAU2Q,MAAM71B,UAASgyB,UAAU,GAAG;AACzC,gBAAM,IAAIl8B,qBAAqB,yCAAyC;QAC1E;AACA,eAAO8Y,OAAOsW,WAAY5pB,OAAMA,EAAE8mB,QAAO,GAAI3lB,KAAK8M,GAAG;MACvD;;;;;;;;;MAWA,OAAOusB,kBAAkBhV,MAAM7K,KAAK7W,UAAU,CAAA,GAAI;AAChD,cAAM;UAAE7F,SAAS;UAAMgG,kBAAkB;QAAK,IAAIH,SAChDyyB,cAAcnzB,OAAOwE,SAAS;UAC5B3J;UACAgG;UACA6D,aAAa;QACf,CAAC;AACH,eAAOspB,kBAAkBmF,aAAa/Q,MAAM7K,GAAG;MACjD;;;;MAKA,OAAO8f,kBAAkBjV,MAAM7K,KAAK7W,UAAU,CAAA,GAAI;AAChD,eAAOY,UAAS81B,kBAAkBhV,MAAM7K,KAAK7W,OAAO;MACtD;;;;;;;;;;;;;MAcA,OAAO42B,kBAAkB/f,KAAK7W,UAAU,CAAA,GAAI;AAC1C,cAAM;UAAE7F,SAAS;UAAMgG,kBAAkB;QAAK,IAAIH,SAChDyyB,cAAcnzB,OAAOwE,SAAS;UAC5B3J;UACAgG;UACA6D,aAAa;QACf,CAAC;AACH,eAAO,IAAImpB,YAAYsF,aAAa5b,GAAG;MACzC;;;;;;;;;;;MAYA,OAAOggB,iBAAiBnV,MAAMoV,cAAcz9B,OAAO,CAAA,GAAI;AACrD,YAAIiD,YAAYolB,IAAI,KAAKplB,YAAYw6B,YAAY,GAAG;AAClD,gBAAM,IAAIpgC,qBACR,+DACF;QACF;AACA,cAAM;UAAEyD,SAAS;UAAMgG,kBAAkB;QAAK,IAAI9G,MAChDo5B,cAAcnzB,OAAOwE,SAAS;UAC5B3J;UACAgG;UACA6D,aAAa;QACf,CAAC;AAEH,YAAI,CAACyuB,YAAYh5B,OAAOq9B,aAAa38B,MAAM,GAAG;AAC5C,gBAAM,IAAIzD,qBACP,4CAA2C+7B,WAAY,2CACbqE,aAAa38B,MAAO,EACjE;QACF;AAEA,cAAM;UAAEojB;UAAQ5gB;UAAM4vB;UAAgBxJ;QAAc,IAAI+T,aAAaxJ,kBAAkB5L,IAAI;AAE3F,YAAIqB,eAAe;AACjB,iBAAOniB,UAASsgB,QAAQ6B,aAAa;QACvC,OAAO;AACL,iBAAOoM,oBACL5R,QACA5gB,MACAtD,MACC,UAASy9B,aAAav9B,MAAO,IAC9BmoB,MACA6K,cACF;QACF;MACF;;;;;;MAQA,WAAWx1B,aAAa;AACtB,eAAO0e;MACT;;;;;MAMA,WAAWte,WAAW;AACpB,eAAOse;MACT;;;;;MAMA,WAAWre,wBAAwB;AACjC,eAAOqe;MACT;;;;;MAMA,WAAWne,YAAY;AACrB,eAAOme;MACT;;;;;MAMA,WAAWle,YAAY;AACrB,eAAOke;MACT;;;;;MAMA,WAAWje,cAAc;AACvB,eAAOie;MACT;;;;;MAMA,WAAW9d,oBAAoB;AAC7B,eAAO8d;MACT;;;;;MAMA,WAAW5d,yBAAyB;AAClC,eAAO4d;MACT;;;;;MAMA,WAAW1d,wBAAwB;AACjC,eAAO0d;MACT;;;;;MAMA,WAAWzd,iBAAiB;AAC1B,eAAOyd;MACT;;;;;MAMA,WAAWvd,uBAAuB;AAChC,eAAOud;MACT;;;;;MAMA,WAAWtd,4BAA4B;AACrC,eAAOsd;MACT;;;;;MAMA,WAAWrd,2BAA2B;AACpC,eAAOqd;MACT;;;;;MAMA,WAAWpd,iBAAiB;AAC1B,eAAOod;MACT;;;;;MAMA,WAAWnd,8BAA8B;AACvC,eAAOmd;MACT;;;;;MAMA,WAAWld,eAAe;AACxB,eAAOkd;MACT;;;;;MAMA,WAAWjd,4BAA4B;AACrC,eAAOid;MACT;;;;;MAMA,WAAWhd,4BAA4B;AACrC,eAAOgd;MACT;;;;;MAMA,WAAW/c,gBAAgB;AACzB,eAAO+c;MACT;;;;;MAMA,WAAW9c,6BAA6B;AACtC,eAAO8c;MACT;;;;;MAMA,WAAW7c,gBAAgB;AACzB,eAAO6c;MACT;;;;;MAMA,WAAW5c,6BAA6B;AACtC,eAAO4c;MACT;IACF;AAKO,aAASkP,iBAAiBoS,aAAa;AAC5C,UAAIn2B,SAASgyB,WAAWmE,WAAW,GAAG;AACpC,eAAOA;MACT,WAAWA,eAAeA,YAAY/T,WAAW7a,SAAS4uB,YAAY/T,QAAO,CAAE,GAAG;AAChF,eAAOpiB,SAASywB,WAAW0F,WAAW;iBAC7BA,eAAe,OAAOA,gBAAgB,UAAU;AACzD,eAAOn2B,SAASgE,WAAWmyB,WAAW;MACxC,OAAO;AACL,cAAM,IAAIrgC,qBACP,8BAA6BqgC,WAAY,aAAY,OAAOA,WAAY,EAC3E;MACF;IACF;AC5/EMC,QAAAA,UAAU;;;;;;;;;;;;;;;;ACXhB;AAAA;AAAA;AAEA,QAAI,QAAQ;AAEZ,aAAS,UAAU,UAAU,WAAW;AACtC,WAAK,QAAQ,KAAK,MAAM,KAAK,EAAE,OAAO,EAAE,CAAC;AAAA,IAC3C;AAEA,aAAS,UAAU,WAAW,WAAW;AACvC,WAAK,QAAQ,KAAK,MAAM,KAAK,EAAE,QAAQ,EAAE,CAAC,EAAE,QAAQ,OAAO;AAAA,IAC7D;AAEA,aAAS,UAAU,SAAS,WAAW;AACrC,WAAK,QAAQ,KAAK,MAAM,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE,QAAQ,KAAK;AAAA,IACzD;AAEA,aAAS,UAAU,UAAU,WAAW;AACtC,UAAI,OAAO,KAAK;AAChB,WAAK,QAAQ,KAAK,MAAM,KAAK,EAAE,OAAO,EAAE,CAAC,EAAE,QAAQ,MAAM;AACzD,UAAI,KAAK,SAAS,MAAM;AACtB,aAAK,QAAQ,KAAK,MAAM,KAAK,EAAE,OAAO,EAAE,CAAC;AAAA,MAC3C;AAAA,IACF;AAEA,aAAS,UAAU,YAAY,WAAW;AACxC,UAAI,OAAO,KAAK;AAChB,WAAK,QAAQ,KAAK,MAAM,KAAK,EAAE,SAAS,EAAE,CAAC,EAAE,QAAQ,QAAQ;AAC7D,UAAI,KAAK,QAAQ,MAAM;AACrB,aAAK,QAAQ,KAAK,MAAM,KAAK,EAAE,OAAO,EAAE,CAAC;AAAA,MAC3C;AAAA,IACF;AAEA,aAAS,UAAU,YAAY,WAAW;AACxC,UAAI,OAAO,KAAK;AAChB,WAAK,QAAQ,KAAK,MAAM,KAAK,EAAE,SAAS,EAAE,CAAC,EAAE,QAAQ,QAAQ;AAC7D,UAAI,KAAK,QAAQ,MAAM;AACrB,aAAK,QAAQ,KAAK,MAAM,KAAK,EAAE,OAAO,EAAE,CAAC;AAAA,MAC3C;AAAA,IACF;AAEA,aAAS,UAAU,eAAe,WAAW;AAC3C,WAAK,QAAQ,KAAK,MAAM,MAAM,EAAE,OAAO,EAAE,CAAC;AAAA,IAC5C;AAEA,aAAS,UAAU,gBAAgB,WAAW;AAC5C,WAAK,QAAQ,KAAK,MACf,MAAM,EAAE,QAAQ,EAAE,CAAC,EACnB,MAAM,OAAO,EACb,QAAQ,QAAQ;AAAA,IACrB;AAEA,aAAS,UAAU,cAAc,WAAW;AAC1C,WAAK,QAAQ,KAAK,MACf,MAAM,EAAE,MAAM,EAAE,CAAC,EACjB,MAAM,KAAK,EACX,QAAQ,QAAQ;AAAA,IACrB;AAEA,aAAS,UAAU,eAAe,WAAW;AAC3C,UAAI,OAAO,KAAK;AAChB,WAAK,QAAQ,KAAK,MACf,MAAM,EAAE,OAAO,EAAE,CAAC,EAClB,MAAM,MAAM,EACZ,QAAQ,QAAQ;AACnB,UAAI,KAAK,SAAS,MAAM;AACtB,aAAK,QAAQ,KAAK,MAAM,MAAM,EAAE,OAAO,EAAE,CAAC;AAAA,MAC5C;AAAA,IACF;AAEA,aAAS,UAAU,iBAAiB,WAAW;AAC7C,UAAI,OAAO,KAAK;AAChB,WAAK,QAAQ,KAAK,MAAM,MAAM,EAAE,SAAS,EAAE,CAAC,EACzC,MAAM,QAAQ,EACd,QAAQ,QAAQ;AACnB,UAAI,KAAK,QAAQ,MAAM;AACrB,aAAK,QAAQ,KAAK,MAAM,MAAM,EAAE,OAAO,EAAE,CAAC;AAAA,MAC5C;AAAA,IACF;AAEA,aAAS,UAAU,iBAAiB,WAAW;AAC7C,UAAI,OAAO,KAAK;AAChB,WAAK,QAAQ,KAAK,MACf,MAAM,EAAE,SAAS,EAAE,CAAC,EACpB,QAAQ,QAAQ;AACnB,UAAI,KAAK,QAAQ,MAAM;AACrB,aAAK,QAAQ,KAAK,MAAM,MAAM,EAAE,OAAO,EAAE,CAAC;AAAA,MAC5C;AAAA,IACF;AAEA,aAAS,UAAU,UAAU,WAAW;AACtC,aAAO,KAAK,MAAM;AAAA,IACpB;AAEA,aAAS,UAAU,cAAc,WAAW;AAC1C,aAAO,KAAK,MAAM;AAAA,IACpB;AAEA,aAAS,UAAU,SAAS,WAAW;AACrC,UAAI,UAAU,KAAK,MAAM;AACzB,aAAO,WAAW,IAAI,IAAI;AAAA,IAC5B;AAEA,aAAS,UAAU,WAAW,WAAW;AACvC,aAAO,KAAK,MAAM,QAAQ;AAAA,IAC5B;AAEA,aAAS,UAAU,WAAW,WAAW;AACvC,aAAO,KAAK,MAAM;AAAA,IACpB;AAEA,aAAS,UAAU,aAAa,WAAW;AACzC,aAAO,KAAK,MAAM;AAAA,IACpB;AAEA,aAAS,UAAU,aAAa,WAAW;AACzC,aAAO,KAAK,MAAM;AAAA,IACpB;AAEA,aAAS,UAAU,kBAAkB,WAAW;AAC9C,aAAO,KAAK,MAAM;AAAA,IACpB;AAEA,aAAS,UAAU,UAAU,WAAW;AACtC,aAAO,KAAK,MAAM,QAAQ;AAAA,IAC5B;AAEA,aAAS,UAAU,aAAa,WAAW;AACzC,aAAO,KAAK,QAAQ,EAAE;AAAA,IACxB;AAEA,aAAS,UAAU,iBAAiB,WAAW;AAC7C,aAAO,KAAK,QAAQ,EAAE;AAAA,IACxB;AAEA,aAAS,UAAU,YAAY,WAAW;AACxC,UAAI,UAAU,KAAK,QAAQ,EAAE;AAC7B,aAAO,WAAW,IAAI,IAAI;AAAA,IAC5B;AAEA,aAAS,UAAU,cAAc,WAAW;AAC1C,aAAO,KAAK,QAAQ,EAAE,QAAQ;AAAA,IAChC;AAEA,aAAS,UAAU,cAAc,WAAW;AAC1C,aAAO,KAAK,QAAQ,EAAE;AAAA,IACxB;AAEA,aAAS,UAAU,gBAAgB,WAAW;AAC5C,aAAO,KAAK,QAAQ,EAAE;AAAA,IACxB;AAEA,aAAS,UAAU,gBAAgB,WAAW;AAC5C,aAAO,KAAK,QAAQ,EAAE;AAAA,IACxB;AAEA,aAAS,UAAU,cAAc,WAAW;AAC1C,aAAO,KAAK,MAAM,MAAM,EAAE,MAAM;AAAA,IAClC;AAEA,aAAS,UAAU,SAAS,WAAW;AACrC,aAAO,KAAK,MAAM,OAAO;AAAA,IAC3B;AAEA,aAAS,UAAU,UAAU,SAAS,GAAG;AACvC,WAAK,QAAQ,KAAK,MAAM,IAAI,EAAE,KAAK,EAAE,CAAC;AAAA,IACxC;AAEA,aAAS,UAAU,cAAc,SAAS,GAAG;AAC3C,WAAK,QAAQ,KAAK,MAAM,IAAI,EAAE,MAAM,EAAE,CAAC;AAAA,IACzC;AAEA,aAAS,UAAU,SAAS,SAAS,GAAG;AACtC,WAAK,QAAQ,KAAK,MAAM,IAAI,EAAE,SAAS,EAAE,CAAC;AAAA,IAC5C;AAEA,aAAS,UAAU,WAAW,SAAS,GAAG;AACxC,WAAK,QAAQ,KAAK,MAAM,IAAI,EAAE,OAAO,IAAI,EAAE,CAAC;AAAA,IAC9C;AAEA,aAAS,UAAU,WAAW,SAAS,GAAG;AACxC,WAAK,QAAQ,KAAK,MAAM,IAAI,EAAE,MAAM,EAAE,CAAC;AAAA,IACzC;AAEA,aAAS,UAAU,aAAa,SAAS,GAAG;AAC1C,WAAK,QAAQ,KAAK,MAAM,IAAI,EAAE,QAAQ,EAAE,CAAC;AAAA,IAC3C;AAEA,aAAS,UAAU,aAAa,SAAS,GAAG;AAC1C,WAAK,QAAQ,KAAK,MAAM,IAAI,EAAE,QAAQ,EAAE,CAAC;AAAA,IAC3C;AAEA,aAAS,UAAU,kBAAkB,SAAS,GAAG;AAC/C,WAAK,QAAQ,KAAK,MAAM,IAAI,EAAE,aAAa,EAAE,CAAC;AAAA,IAChD;AAEA,aAAS,UAAU,UAAU,WAAW;AACtC,aAAO,KAAK,MAAM,MAAM;AAAA,IAC1B;AAEA,aAAS,UAAU,WAAW,WAAW;AACvC,aAAO,KAAK,OAAO,EAAE,SAAS;AAAA,IAChC;AAEA,aAAS,UAAU,SAAS,WAAW;AACrC,aAAO,KAAK,MAAM,SAAS;AAAA,IAC7B;AAEA,aAAS,UAAU,mBAAmB,WAAW;AAE/C,UAAI,UAAU,KAAK,MAAM,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE,QAAQ,KAAK;AACxD,aAAO,KAAK,MAAM,UAAU,QAAQ;AAAA,IACtC;AAMA,aAAS,UAAU,uBAAuB,WAAW;AAGnD,UAAI,UAAU,KAAK,MAAM,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE,QAAQ,KAAK;AACxD,aAAO,KAAK,MAAM,UAAU,QAAQ;AAAA,IACtC;AAEA,aAAS,SAAU,WAAW,IAAI;AAChC,UAAI,WAAW,EAAE,MAAM,GAAG;AAC1B,UAAI,CAAC,WAAW;AACd,aAAK,QAAQ,MAAM,SAAS,MAAM;AAAA,MACpC,WAAW,qBAAqB,UAAU;AACxC,aAAK,QAAQ,UAAU;AAAA,MACzB,WAAW,qBAAqB,MAAM;AACpC,aAAK,QAAQ,MAAM,SAAS,WAAW,WAAW,QAAQ;AAAA,MAC5D,WAAW,OAAO,cAAc,UAAU;AACxC,aAAK,QAAQ,MAAM,SAAS,WAAW,WAAW,QAAQ;AAAA,MAC5D,WAAW,OAAO,cAAc,UAAU;AACxC,aAAK,QAAQ,MAAM,SAAS,QAAQ,WAAW,QAAQ;AACvD,aAAK,MAAM,YAAY,KAAK,QAAQ,MAAM,SAAS,YAAY,WAAW,QAAQ;AAClF,aAAK,MAAM,YAAY,KAAK,QAAQ,MAAM,SAAS,QAAQ,WAAW,QAAQ;AAE9E,aAAK,MAAM,YAAY,KAAK,QAAQ,MAAM,SAAS,WAAW,WAAW,4BAA4B,QAAQ;AAAA,MAC/G;AAEA,UAAI,CAAC,KAAK,SAAS,CAAC,KAAK,MAAM,SAAS;AACtC,cAAM,IAAI,MAAM,oCAAoC,KAAK,UAAU,SAAS,CAAC;AAAA,MAC/E;AAEA,UAAI,MAAM,OAAO,KAAK,MAAM,UAAU;AACpC,aAAK,QAAQ,KAAK,MAAM,QAAQ,EAAE;AAAA,MACpC;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC3PjB;AAAA;AAAA;AAEA,aAAS,WAAW,MAAM;AACxB,aAAO;AAAA,QACL,OAAO;AAAA,QACP,OAAO;AAAA,MACT;AAAA,IACF;AAEA,aAAS,sBAAsB,OAAO,MAAM;AAC1C,YAAM,MAAM;AACZ,YAAM,OAAO,OAAO,MAAM;AAC1B,YAAM,QAAQ;AAAA,IAChB;AAEA,aAAS,qBAAqB,SAAS,cAAc,kBAAkB;AACrE,UAAI,cAAc;AAEhB,YAAI,aAAa,UAAU,GAAG;AAC5B,kBAAQ,KAAK,WAAW,aAAa,KAAK,CAAC;AAC3C,kBAAQ,KAAK,WAAW,aAAa,GAAG,CAAC;AAAA,QAC3C,OAAO;AACL,kBAAQ,KAAK,YAAY;AAAA,QAC3B;AAAA,MACF;AACA,UAAI,kBAAkB;AACpB,gBAAQ,KAAK,gBAAgB;AAAA,MAC/B;AAAA,IACF;AAEA,aAAS,aAAa,KAAK;AACzB,UAAI,UAAU,CAAC;AACf,UAAI,eAAe;AAEnB,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,YAAI,cAAc,IAAI,CAAC;AACvB,YAAI,OAAO,gBAAgB,UAAU;AAEnC,+BAAqB,SAAS,cAAc,WAAW,WAAW,CAAC;AACnE,yBAAe;AAAA,QACjB,WAAW,CAAC,cAAc;AAExB,yBAAe,WAAW,WAAW;AAAA,QACvC,WAAW,aAAa,UAAU,GAAG;AAEnC,gCAAsB,cAAc,WAAW;AAAA,QACjD,OAAO;AACL,cAAI,aAAa,SAAS,cAAc,aAAa,KAAK;AAExD,yBAAa;AACb,yBAAa,MAAM;AAAA,UACrB,WAAW,aAAa,UAAU,GAAG;AAEnC,oBAAQ,KAAK,WAAW,aAAa,KAAK,CAAC;AAC3C,2BAAe,WAAW,aAAa,GAAG;AAC1C,kCAAsB,cAAc,WAAW;AAAA,UACjD,OAAO;AAEL,iCAAqB,SAAS,YAAY;AAC1C,2BAAe,WAAW,WAAW;AAAA,UACvC;AAAA,QACF;AAAA,MACF;AAEA,2BAAqB,SAAS,YAAY;AAE1C,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACrEjB;AAAA;AAAA;AAEA,QAAI,eAAe;AAEnB,aAAS,eAAe,KAAK,KAAK,KAAK;AACrC,UAAI,SAAS,aAAa,GAAG;AAC7B,UAAI,OAAO,WAAW,GAAG;AACvB,YAAI,cAAc,OAAO,CAAC;AAC1B,YAAI,OAAO,YAAY;AACvB,YAAI,SAAS,KAAK,YAAY,UAAU,OAAO,YAAY,QAAQ,KAAK;AACtE,iBAAO;AAAA,QACT;AACA,YAAI,SAAS,KAAK,YAAY,UAAU,OAAO,YAAY,QAAQ,MAAM,OAAO,GAAG;AACjF,iBAAO,OAAO;AAAA,QAChB;AAAA,MACF;AAEA,UAAI,SAAS,CAAC;AACd,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC7C,YAAI,QAAQ,OAAO,CAAC;AACpB,YAAI,MAAM,UAAU,GAAG;AACrB,iBAAO,KAAK,MAAM,KAAK;AACvB;AAAA,QACF;AAEA,YAAI,OAAO,MAAM;AACjB,YAAI,MAAM,SAAS,GAAG;AACpB,iBAAO,KAAK,MAAM,QAAQ,MAAM,MAAM,GAAG;AACzC;AAAA,QACF;AAEA,YAAI,aAAa,MAAM,SAAS,IAAI,MAAM,QAAQ,IAAI,MAAM;AAC5D,YAAI,MAAM,OAAO,aAAa,MAAM,KAAK;AACvC,mBAAS,OAAO;AAAA,YACb,MACE,KAAK,EAAE,QAAQ,MAAM,MAAM,MAAM,QAAQ,EAAE,CAAC,EAC5C,IAAI,SAAU,GAAG,OAAO;AACvB,kBAAI,QAAQ,MAAM,QAAQ;AAC1B,mBAAK,QAAQ,MAAM,SAAS,MAAM,SAAS,GAAG;AAC5C,uBAAO;AAAA,cACT;AACA,qBAAO;AAAA,YACT,CAAC,EACA,OAAO,SAAU,OAAO;AACvB,qBAAO,SAAS;AAAA,YAClB,CAAC;AAAA,UACL;AAAA,QACF,WAAW,MAAM,QAAQ,MAAM,MAAM,OAAO,GAAG;AAC7C,iBAAO,KAAK,MAAM,QAAQ,MAAM,MAAM,IAAI;AAAA,QAC5C,OAAO;AACL,iBAAO,KAAK,MAAM,QAAQ,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,QAC9D;AAAA,MACF;AAEA,aAAO,OAAO,KAAK,GAAG;AAAA,IACxB;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACzDjB;AAAA;AAAA;AAGA,QAAI,WAAW;AAEf,QAAI,iBAAiB;AAKrB,QAAI,aAAa;AAcjB,aAAS,eAAgB,QAAQ,SAAS;AACxC,WAAK,WAAW;AAChB,WAAK,OAAO,QAAQ,OAAO;AAC3B,WAAK,MAAM,KAAK,OAAO,QAAQ,QAAQ;AACvC,WAAK,eAAe,IAAI,SAAS,QAAQ,aAAa,KAAK,GAAG;AAC9D,WAAK,aAAa,QAAQ,YAAY,IAAI,SAAS,QAAQ,WAAW,KAAK,GAAG,IAAI;AAClF,WAAK,WAAW,QAAQ,UAAU,IAAI,SAAS,QAAQ,SAAS,KAAK,GAAG,IAAI;AAC5E,WAAK,cAAc,QAAQ,YAAY;AACvC,WAAK,eAAe;AACpB,WAAK,gBAAgB,QAAQ,gBAAgB;AAC7C,WAAK,SAAS,eAAe,cAAc,MAAM;AAAA,IACnD;AAMA,mBAAe,MAAM,CAAE,UAAU,UAAU,QAAQ,cAAc,SAAS,WAAY;AAMtF,mBAAe,aAAa;AAAA,MAC1B,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,UAAU;AAAA,MACV,WAAW;AAAA,IACb;AAMA,mBAAe,cAAc;AAAA,MAC3B,EAAE,KAAK,GAAG,KAAK,IAAI,OAAO,CAAC,EAAE;AAAA;AAAA,MAC7B,EAAE,KAAK,GAAG,KAAK,IAAI,OAAO,CAAC,EAAE;AAAA;AAAA,MAC7B,EAAE,KAAK,GAAG,KAAK,IAAI,OAAO,CAAC,EAAE;AAAA;AAAA,MAC7B,EAAE,KAAK,GAAG,KAAK,IAAI,OAAO,CAAC,GAAG,EAAE;AAAA;AAAA,MAChC,EAAE,KAAK,GAAG,KAAK,IAAI,OAAO,CAAC,EAAE;AAAA;AAAA,MAC7B,EAAE,KAAK,GAAG,KAAK,GAAG,OAAO,CAAC,GAAG,EAAE;AAAA;AAAA,IACjC;AAMA,mBAAe,cAAc;AAAA,MAC3B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAMA,mBAAe,UAAU;AAAA,MACvB,OAAO;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACP;AAAA,MAEA,WAAW;AAAA,QACT,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACP;AAAA,IACF;AAMA,mBAAe,gBAAgB,CAAE,KAAK,KAAK,KAAK,KAAK,KAAK,GAAI;AAE9D,mBAAe,0BAA0B;AACzC,mBAAe,2BAA2B;AAC1C,mBAAe,4BAA4B;AAC3C,mBAAe,kBAAkB;AAAA,MAC/B,QAAQ,eAAe;AAAA,MACvB,QAAQ,eAAe;AAAA,MACvB,MAAM,eAAe;AAAA,MACrB,YAAY,eAAe;AAAA,MAC3B,OAAO,eAAe;AAAA,MACtB,WAAW,eAAe;AAAA,IAC5B;AAEA,mBAAe,yBAAyB,SAAS,uBAAuB,aAAa,OAAO;AAC1F,UAAI,OAAO,UAAU,UAAU;AAC7B,eAAO;AAAA,MACT;AAEA,aAAO,YAAY,MAAM,KAAK,SAAS,MAAM;AAC3C,eAAO,MAAM,QAAQ,IAAI,IAAI;AAAA,MAC/B,CAAC;AAAA,IACH;AAWA,mBAAe,cAAc,SAAS,YAAa,OAAO,OAAO,aAAa;AAE5E,cAAQ,OAAO;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AACH,cAAI,UAAU,eAAe,QAAQ,KAAK;AAE1C,kBAAQ,MAAM,QAAQ,cAAc,SAAS,OAAO;AAClD,oBAAQ,MAAM,YAAY;AAE1B,gBAAI,OAAO,QAAQ,KAAK,MAAM,aAAa;AACzC,qBAAO,QAAQ,KAAK;AAAA,YACtB,OAAO;AACL,oBAAM,IAAI,MAAM,6CAA6C,QAAQ,GAAG;AAAA,YAC1E;AAAA,UACF,CAAC;AACD;AAAA,MACJ;AAGA,UAAI,CAAE,eAAe,gBAAgB,KAAK,EAAE,KAAK,KAAK,GAAI;AACxD,cAAM,IAAI,MAAM,oCAAoC,KAAK;AAAA,MAC3D;AAGA,UAAI,MAAM,QAAQ,GAAG,MAAM,IAAI;AAC7B,gBAAQ,MAAM,QAAQ,OAAO,YAAY,MAAM,MAAM,YAAY,GAAG;AAAA,MACtE,WAAW,MAAM,QAAQ,GAAG,MAAM,IAAI;AACpC,gBAAQ,MAAM,QAAQ,OAAO,YAAY,MAAM,MAAM,YAAY,GAAG;AAAA,MACtE;AAiBA,eAAS,cAAe,KAAK;AAC3B,YAAI,QAAQ,CAAC;AAEb,iBAAS,aAAc,QAAQ;AAC7B,cAAI,kBAAkB,OAAO;AAC3B,qBAASC,KAAI,GAAGC,KAAI,OAAO,QAAQD,KAAIC,IAAGD,MAAK;AAC7C,kBAAIE,SAAQ,OAAOF,EAAC;AAEpB,kBAAI,eAAe,uBAAuB,aAAaE,MAAK,GAAG;AAC7D,sBAAM,KAAKA,MAAK;AAChB;AAAA,cACF;AAEA,kBAAI,OAAOA,WAAU,YAAY,OAAO,MAAMA,MAAK,KAAKA,SAAQ,YAAY,OAAOA,SAAQ,YAAY,KAAK;AAC1G,sBAAM,IAAI;AAAA,kBACN,iCAAiCA,SAAQ,qBACzC,YAAY,MAAM,MAAM,YAAY;AAAA,gBACxC;AAAA,cACF;AAEA,oBAAM,KAAKA,MAAK;AAAA,YAClB;AAAA,UACF,OAAO;AAEL,gBAAI,eAAe,uBAAuB,aAAa,MAAM,GAAG;AAC9D,oBAAM,KAAK,MAAM;AACjB;AAAA,YACF;AAEA,gBAAI,YAAY,CAAC;AAGjB,gBAAI,OAAO,MAAM,SAAS,KAAK,YAAY,YAAY,OAAO,YAAY,YAAY,KAAK;AACzF,oBAAM,IAAI;AAAA,gBACR,iCAAiC,SAAS,qBAC1C,YAAY,MAAM,MAAM,YAAY;AAAA,cACtC;AAAA,YACF;AAEA,gBAAI,UAAU,aAAa;AACzB,0BAAY,YAAY;AAAA,YAC1B;AAEA,kBAAM,KAAK,SAAS;AAAA,UACtB;AAAA,QACF;AAEA,YAAI,QAAQ,IAAI,MAAM,GAAG;AACzB,YAAI,CAAC,MAAM,MAAM,SAAU,MAAM;AAC/B,iBAAO,KAAK,SAAS;AAAA,QACvB,CAAC,GAAG;AACF,gBAAM,IAAI,MAAM,2BAA2B;AAAA,QAC7C;AAEA,YAAI,MAAM,SAAS,GAAG;AACpB,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK;AAC5C,yBAAa,YAAY,MAAM,CAAC,CAAC,CAAC;AAAA,UACpC;AAAA,QACF,OAAO;AACL,uBAAa,YAAY,GAAG,CAAC;AAAA,QAC/B;AAEA,cAAM,KAAK,eAAe,cAAc;AAExC,eAAO;AAAA,MACT;AAQA,eAAS,YAAa,KAAK;AACzB,YAAI,iBAAiB;AACrB,YAAI,QAAQ,IAAI,MAAM,GAAG;AAEzB,YAAI,MAAM,SAAS,GAAG;AACpB,gBAAM,IAAI,MAAM,qBAAqB,GAAG;AAAA,QAC1C;AAEA,YAAI,MAAM,SAAS,GAAG;AACpB,cAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG;AACzB,oBAAQ,CAAC,MAAM,CAAC,IAAI,MAAM,YAAY,KAAK,MAAM,CAAC,CAAC;AAAA,UACrD;AACA,iBAAO,WAAW,MAAM,CAAC,GAAG,MAAM,MAAM,SAAS,CAAC,CAAC;AAAA,QACrD;AAEA,eAAO,WAAW,KAAK,cAAc;AAAA,MACvC;AAUA,eAAS,WAAY,KAAK,gBAAgB;AACxC,YAAI,QAAQ,CAAC;AACb,YAAI,QAAQ,IAAI,MAAM,GAAG;AAEzB,YAAI,MAAM,SAAS,GAAI;AAErB,cAAI,MAAM,SAAS,GAAG;AACpB,mBAAO,CAAC;AAAA,UACV;AAEA,cAAI,CAAC,MAAM,CAAC,EAAE,QAAQ;AACpB,gBAAI,CAAC,MAAM,CAAC,EAAE,QAAQ;AACpB,oBAAM,IAAI,MAAM,oBAAoB,GAAG;AAAA,YACzC;AAEA,mBAAO,CAAC;AAAA,UACV;AAGA,cAAI,MAAM,CAAC,MAAM,CAAC;AAClB,cAAI,MAAM,CAAC,MAAM,CAAC;AAElB,cAAI,OAAO,MAAM,GAAG,KAAK,OAAO,MAAM,GAAG,KACrC,MAAM,YAAY,OAAO,MAAM,YAAY,KAAK;AAClD,kBAAM,IAAI;AAAA,cACR,iCACA,MAAM,MAAM,MACZ,qBACA,YAAY,MAAM,MAAM,YAAY;AAAA,YACtC;AAAA,UACF,WAAW,MAAM,KAAK;AACpB,kBAAM,IAAI,MAAM,oBAAoB,GAAG;AAAA,UACzC;AAGA,cAAI,cAAc,CAAC;AAEnB,cAAI,OAAO,MAAM,WAAW,KAAK,eAAe,GAAG;AACjD,kBAAM,IAAI,MAAM,8CAA8C,cAAc,QAAQ;AAAA,UACtF;AAIA,cAAI,UAAU,eAAe,MAAM,MAAM,GAAG;AAC1C,kBAAM,KAAK,CAAC;AAAA,UACd;AAEA,mBAAS,QAAQ,KAAK,QAAQ,KAAK,SAAS,OAAO,SAAS;AAC1D,gBAAI,SAAS,MAAM,QAAQ,KAAK,MAAM;AACtC,gBAAI,CAAC,UAAU,cAAc,KAAM,cAAc,mBAAoB,GAAG;AACtE,4BAAc;AACd,oBAAM,KAAK,KAAK;AAAA,YAClB,OAAO;AACL;AAAA,YACF;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAEA,eAAO,OAAO,MAAM,CAAC,GAAG,IAAI,MAAM,CAAC;AAAA,MACrC;AAEA,aAAO,cAAc,KAAK;AAAA,IAC5B;AAEA,mBAAe,iBAAiB,SAAS,GAAG,GAAG;AAC7C,UAAI,YAAY,OAAO,MAAM;AAC7B,UAAI,YAAY,OAAO,MAAM;AAE7B,UAAI,aAAa,WAAW;AAC1B,eAAO,IAAI;AAAA,MACb;AAEA,UAAI,CAAC,aAAa,WAAW;AAC3B,eAAO;AAAA,MACT;AAEA,UAAI,aAAa,CAAC,WAAW;AAC3B,eAAO;AAAA,MACT;AAEA,aAAO,EAAE,cAAc,CAAC;AAAA,IAC1B;AAEA,mBAAe,wBAAwB,SAAS,cAAc;AAE5D,UAAI,aAAa,MAAM,WAAW,GAAG;AACnC,YAAI,cAAc,eAAe,YAAY,aAAa,MAAM,CAAC,IAAI,CAAC;AAEtE,YAAI,aAAa,WAAW,CAAC,IAAI,aAAa;AAC5C,gBAAM,IAAI,MAAM,0CAA0C;AAAA,QAC5D;AAEA,eAAO,aAAa,WACjB,OAAO,SAAS,YAAY;AAC3B,iBAAO,eAAe,MAAM,OAAO,cAAc;AAAA,QACnD,CAAC,EACA,KAAK,eAAe,cAAc;AAAA,MACvC;AAAA,IACF;AAEA,mBAAe,gBAAgB,SAAS,QAAQ;AAC9C,eAAS,IAAI,GAAG,IAAI,eAAe,IAAI,QAAQ,IAAI,GAAG,EAAE,GAAG;AACzD,YAAI,QAAQ,eAAe,IAAI,CAAC;AAChC,YAAI,QAAQ,OAAO,KAAK;AACxB,eAAO,KAAK,IAAI,OAAO,OAAO,KAAK;AAAA,MACrC;AACA,aAAO,OAAO,OAAO,MAAM;AAAA,IAC7B;AAEA,mBAAe,UAAU,sBAAsB,SAAS,aAAa,cAAc,QAAQ;AACzF,UAAK,WAAW,WAAa,WAAW,OAAQ;AAC9C,YAAI,WAAW,YAAY,QAAQ;AACnC,oBAAY,eAAe,MAAM,EAAE;AACnC,YAAI,WAAW,YAAY,QAAQ;AACnC,YAAI,aAAa,UAAU;AAEzB,cAAK,YAAY,WAAW,MAAM,KAC7B,YAAY,WAAW,MAAM,GAAI;AACpC,wBAAY,QAAQ;AAAA,UACtB,WAAY,YAAY,WAAW,MAAM,MAC7B,YAAY,WAAW,MAAM,IAAK;AAC5C,wBAAY,aAAa;AAAA,UAC3B;AAAA,QACF;AAAA,MACF,OAAO;AACL,YAAI,eAAe,YAAY,SAAS;AACxC,oBAAY,eAAe,MAAM,EAAE;AACnC,YAAI,cAAc,YAAY,SAAS;AACvC,YAAI,OAAO,cAAc;AACzB,YAAI,SAAS,GAAG;AAEZ,cAAI,KAAK,OAAO,KAAK,WAAW,IAAI;AAElC,iBAAK,YAAY;AAAA,UACnB;AAAA,QACF,WAAY,SAAS,KACT,YAAY,WAAW,MAAM,KAC7B,YAAY,WAAW,MAAM,GAAI;AAE3C,cAAI,KAAK,OAAO,KAAK,WAAW,IAAI;AAElC,iBAAK,UAAU;AAAA,UACjB;AAAA,QACF;AAAA,MACJ;AAAA,IACF;AASA,mBAAe,UAAU,gBAAgB,SAAS,cAAe,SAAS;AAUxE,eAAS,cAAe,OAAO,UAAU;AACvC,iBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,IAAI,GAAG,KAAK;AAC/C,cAAI,SAAS,CAAC,KAAK,OAAO;AACxB,mBAAO,SAAS,CAAC,MAAM;AAAA,UACzB;AAAA,QACF;AAEA,eAAO,SAAS,CAAC,MAAM;AAAA,MACzB;AAWA,eAAS,cAAc,MAAM,cAAc;AACzC,YAAI,eAAe,GAAG;AACpB,cACE,KAAK,QAAQ,IAAI,KACjB,iBAAiB,GACjB;AACA,mBAAO;AAAA,UACT;AAEA,cAAI,SAAS,KAAK,QAAQ,IAAI,IAAI,IAAI;AACtC,cAAI,eAAe,KAAK,QAAQ,IAAK,KAAK,QAAQ,IAAI;AACtD,cAAI,aAAa,KAAK,MAAM,eAAe,CAAC,IAAI;AAEhD,iBAAO,eAAe;AAAA,QACxB;AAEA,eAAO;AAAA,MACT;AAOA,eAAS,iBAAiB,aAAa;AACrC,eAAO,YAAY,SAAS,KAAK,YAAY,KAAK,SAAS,YAAY;AACrE,iBAAO,OAAO,eAAe,YAAY,WAAW,QAAQ,GAAG,KAAK;AAAA,QACtE,CAAC;AAAA,MACH;AAIA,gBAAU,WAAW;AACrB,UAAI,eAAe,UAAU,aAAa;AAE1C,UAAI,cAAc,IAAI,SAAS,KAAK,cAAc,KAAK,GAAG;AAC1D,UAAI,YAAY,KAAK;AACrB,UAAI,UAAU,KAAK;AAGnB,UAAI,iBAAiB,YAAY,QAAQ;AACzC,UAAI,YAAY;AAEhB,eAAS,0BAA0B,aAAa;AAC9C,eAAO,YAAY,KAAK,SAAS,YAAY;AAG3C,cAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,GAAG;AACnC,mBAAO;AAAA,UACT;AAGA,cAAI,UAAU,OAAO,SAAS,WAAW,CAAC,CAAC,IAAI;AAE/C,cAAI,OAAO,MAAM,OAAO,GAAG;AACzB,kBAAM,IAAI,MAAM,mDAAmD,UAAU;AAAA,UAC/E;AAEA,iBAAO,YAAY,OAAO,MAAM,WAAW,YAAY,qBAAqB;AAAA,QAC9E,CAAC;AAAA,MACH;AAEA,aAAO,YAAY,YAAY;AAC7B;AAGA,YAAI,SAAS;AACX,cAAI,aAAc,YAAY,QAAQ,IAAI,UAAU,QAAQ,IAAI,GAAI;AAClE,kBAAM,IAAI,MAAM,2BAA2B;AAAA,UAC7C;AAAA,QACF,OAAO;AACL,cAAI,WAAY,QAAQ,QAAQ,IAAI,YAAY,QAAQ,IAAK,GAAG;AAC9D,kBAAM,IAAI,MAAM,2BAA2B;AAAA,UAC7C;AAAA,QACF;AAaA,YAAI,kBAAkB,cAAc,YAAY,QAAQ,GAAG,KAAK,OAAO,UAAU;AACjF,YAAI,iBAAiB,KAAK,OAAO,UAAU,GAAG;AAC5C,4BAAkB,mBAAmB,YAAY,iBAAiB;AAAA,QACpE;AACA,YAAI,iBAAiB,cAAc,YAAY,OAAO,GAAG,KAAK,OAAO,SAAS;AAC9E,YAAI,iBAAiB,KAAK,OAAO,SAAS,GAAG;AAC3C,2BAAiB,kBAAkB,0BAA0B,KAAK,OAAO,SAAS;AAAA,QACpF;AACA,YAAI,4BAA4B,KAAK,OAAO,WAAW,UAAU,eAAe,YAAY,YAAY,SAAS,CAAC;AAClH,YAAI,2BAA2B,KAAK,OAAO,UAAU,WAAW,eAAe,YAAY,CAAC,EAAE,MAAM,eAAe,YAAY,CAAC,EAAE,MAAM;AACxI,YAAI,cAAc,YAAY,SAAS;AAGvC,YAAI,CAAC,oBAAoB,CAAC,kBAAkB,2BAA2B;AACrE,eAAK,oBAAoB,aAAa,cAAc,KAAK;AACzD;AAAA,QACF;AAGA,YAAI,CAAC,6BAA6B,4BAA4B,CAAC,iBAAiB;AAC9E,eAAK,oBAAoB,aAAa,cAAc,KAAK;AACzD;AAAA,QACF;AAGA,YAAI,6BAA6B,CAAC,4BAA4B,CAAC,gBAAgB;AAC7E,eAAK,oBAAoB,aAAa,cAAc,KAAK;AACzD;AAAA,QACF;AAGA,YACE,KAAK,gBAAgB,KACrB,CAAC,cAAc,aAAa,KAAK,aAAa,GAC9C;AACA,eAAK,oBAAoB,aAAa,cAAc,KAAK;AACzD;AAAA,QACF;AAGA,YAAI,CAAC,cAAc,YAAY,SAAS,IAAI,GAAG,KAAK,OAAO,KAAK,GAAG;AACjE,eAAK,oBAAoB,aAAa,cAAc,OAAO;AAC3D;AAAA,QACF;AAGA,YAAI,CAAC,cAAc,aAAa,KAAK,OAAO,IAAI,GAAG;AACjD,cAAI,KAAK,cAAc,aAAa;AAClC,iBAAK,YAAY;AACjB,iBAAK,oBAAoB,aAAa,cAAc,MAAM;AAC1D;AAAA,UACF,WAAW,CAAC,cAAc,cAAc,GAAG,KAAK,OAAO,IAAI,GAAG;AAC5D,wBAAY,eAAe,MAAM,EAAE;AACnC;AAAA,UACF;AAAA,QACF,WAAW,KAAK,YAAY,aAAa;AACvC,cAAI,CAAC,SAAS;AACZ,iBAAK,UAAU;AACf,iBAAK,oBAAoB,aAAa,OAAO,MAAM;AACnD;AAAA,UACF;AAAA,QACF;AAGA,YAAI,CAAC,cAAc,YAAY,WAAW,GAAG,KAAK,OAAO,MAAM,GAAG;AAChE,eAAK,oBAAoB,aAAa,cAAc,QAAQ;AAC5D;AAAA,QACF;AAGA,YAAI,CAAC,cAAc,YAAY,WAAW,GAAG,KAAK,OAAO,MAAM,GAAG;AAChE,eAAK,oBAAoB,aAAa,cAAc,QAAQ;AAC5D;AAAA,QACF;AAIA,YAAI,mBAAmB,YAAY,QAAQ,GAAG;AAC5C,cAAK,iBAAiB,SAAW,YAAY,gBAAgB,MAAM,GAAI;AACrE,iBAAK,oBAAoB,aAAa,cAAc,QAAQ;AAAA,UAC9D,OAAO;AACL,wBAAY,gBAAgB,CAAC;AAAA,UAC/B;AAEA;AAAA,QACF;AAEA;AAAA,MACF;AAEA,UAAI,aAAa,YAAY;AAC3B,cAAM,IAAI,MAAM,yCAAyC;AAAA,MAC3D;AAEA,WAAK,eAAe,IAAI,SAAS,aAAa,KAAK,GAAG;AACtD,WAAK,eAAe;AAEpB,aAAO;AAAA,IACT;AAQA,mBAAe,UAAU,OAAO,SAAS,OAAQ;AAC/C,UAAI,WAAW,KAAK,cAAc;AAGlC,UAAI,KAAK,aAAa;AACpB,eAAO;AAAA,UACL,OAAO;AAAA,UACP,MAAM,CAAC,KAAK,QAAQ;AAAA,QACtB;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAQA,mBAAe,UAAU,OAAO,SAAS,OAAQ;AAC/C,UAAI,WAAW,KAAK,cAAc,IAAI;AAGtC,UAAI,KAAK,aAAa;AACpB,eAAO;AAAA,UACL,OAAO;AAAA,UACP,MAAM,CAAC,KAAK,QAAQ;AAAA,QACtB;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAQA,mBAAe,UAAU,UAAU,WAAW;AAC5C,UAAI,UAAU,KAAK;AACnB,UAAI,cAAc,KAAK;AAEvB,UAAI;AACF,aAAK,cAAc;AACnB,eAAO;AAAA,MACT,SAAS,KAAK;AACZ,eAAO;AAAA,MACT,UAAE;AACA,aAAK,eAAe;AACpB,aAAK,eAAe;AAAA,MACtB;AAAA,IACF;AAQA,mBAAe,UAAU,UAAU,WAAW;AAC5C,UAAI,UAAU,KAAK;AACnB,UAAI,cAAc,KAAK;AAEvB,UAAI;AACF,aAAK,cAAc,IAAI;AACvB,eAAO;AAAA,MACT,SAAS,KAAK;AACZ,eAAO;AAAA,MACT,UAAE;AACA,aAAK,eAAe;AACpB,aAAK,eAAe;AAAA,MACtB;AAAA,IACF;AAUA,mBAAe,UAAU,UAAU,SAAS,QAAS,OAAO,UAAU;AACpE,UAAI,QAAQ,CAAC;AAEb,UAAI,SAAS,GAAG;AACd,iBAAS,IAAI,GAAG,IAAI,OAAO,IAAI,GAAG,KAAK;AACrC,cAAI;AACF,gBAAI,OAAO,KAAK,KAAK;AACrB,kBAAM,KAAK,IAAI;AAGf,gBAAI,UAAU;AACZ,uBAAS,MAAM,CAAC;AAAA,YAClB;AAAA,UACF,SAAS,KAAK;AACZ;AAAA,UACF;AAAA,QACF;AAAA,MACF,OAAO;AACL,iBAAS,IAAI,GAAG,IAAI,OAAO,IAAI,GAAG,KAAK;AACrC,cAAI;AACF,gBAAI,OAAO,KAAK,KAAK;AACrB,kBAAM,KAAK,IAAI;AAGf,gBAAI,UAAU;AACZ,uBAAS,MAAM,CAAC;AAAA,YAClB;AAAA,UACF,SAAS,KAAK;AACZ;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAOA,mBAAe,UAAU,QAAQ,SAAS,MAAO,SAAS;AACxD,WAAK,eAAe,IAAI,SAAS,WAAW,KAAK,SAAS,WAAW;AAAA,IACvE;AASA,mBAAe,UAAU,YAAY,SAAS,UAAU,gBAAgB;AACtE,UAAI,YAAY,CAAC;AACjB,eAAS,IAAI,iBAAiB,IAAI,GAAG,IAAI,eAAe,IAAI,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC9E,YAAI,QAAQ,eAAe,IAAI,CAAC;AAChC,YAAI,QAAQ,KAAK,OAAO,KAAK;AAC7B,YAAI,aAAa,eAAe,YAAY,CAAC;AAE7C,YAAI,UAAU,gBAAgB,KAAK,OAAO,MAAM,WAAW,GAAG;AAC5D,uBAAa,EAAE,KAAK,GAAG,KAAK,eAAe,YAAY,KAAK,OAAO,MAAM,CAAC,IAAI,CAAC,EAAE;AAAA,QACnF,WAAW,UAAU,aAAa;AAEhC,uBAAa,EAAE,KAAK,GAAG,KAAK,EAAE;AAC9B,kBAAQ,MAAM,MAAM,SAAS,CAAC,MAAM,IAAI,MAAM,MAAM,GAAG,EAAE,IAAI;AAAA,QAC/D;AAEA,kBAAU,KAAK,eAAe,OAAO,WAAW,KAAK,WAAW,GAAG,CAAC;AAAA,MACtE;AACA,aAAO,UAAU,KAAK,GAAG;AAAA,IAC3B;AASA,mBAAe,QAAQ,SAAS,MAAM,YAAY,SAAS;AACzD,UAAI,OAAO;AACX,UAAI,OAAO,YAAY,YAAY;AACjC,kBAAU,CAAC;AAAA,MACb;AAEA,eAASC,OAAOC,aAAYC,UAAS;AACnC,YAAI,CAACA,UAAS;AACZ,UAAAA,WAAU,CAAC;AAAA,QACb;AAEA,YAAI,OAAOA,SAAQ,gBAAgB,aAAa;AAC9C,UAAAA,SAAQ,cAAc,IAAI,SAAS,QAAW,KAAK,GAAG;AAAA,QACxD;AAGA,YAAI,eAAe,WAAWD,WAAU,GAAG;AACzC,UAAAA,cAAa,eAAe,WAAWA,WAAU;AAAA,QACnD;AAGA,YAAI,SAAS,CAAC;AACd,YAAI,SAASA,cAAa,IAAI,KAAK,EAAE,MAAM,KAAK;AAEhD,YAAI,MAAM,SAAS,GAAG;AACpB,gBAAM,IAAI,MAAM,yBAAyB;AAAA,QAC3C;AAGA,YAAI,QAAS,eAAe,IAAI,SAAS,MAAM;AAC/C,iBAAS,IAAI,GAAG,IAAI,eAAe,IAAI,QAAQ,IAAI,GAAG,EAAE,GAAG;AACzD,cAAI,QAAQ,eAAe,IAAI,CAAC;AAChC,cAAI,QAAQ,MAAM,MAAM,SAAS,IAAI,IAAI,IAAI,KAAK;AAElD,cAAI,IAAI,SAAS,CAAC,OAAO;AACvB,mBAAO;AAAA,cAAK,eAAe;AAAA,gBACzB;AAAA,gBACA,eAAe,cAAc,CAAC;AAAA,gBAC9B,eAAe,YAAY,CAAC;AAAA,cAC5B;AAAA,YACF;AAAA,UACF,OAAO;AACL,gBAAI,MAAM,UAAU,cAAc,YAAY,KAAK,IAAI;AAEvD,mBAAO;AAAA,cAAK,eAAe;AAAA,gBACzB;AAAA,gBACA;AAAA,gBACA,eAAe,YAAY,CAAC;AAAA,cAC5B;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,YAAI,eAAe,CAAC;AACpB,iBAAS,IAAI,GAAG,IAAI,eAAe,IAAI,QAAQ,IAAI,GAAG,KAAK;AACzD,cAAI,MAAM,eAAe,IAAI,CAAC;AAC9B,uBAAa,GAAG,IAAI,OAAO,CAAC;AAAA,QAC9B;AAEA,YAAI,aAAa,eAAe,sBAAsB,YAAY;AAClE,qBAAa,aAAa,cAAc,aAAa;AACrD,eAAO,IAAI,eAAe,cAAcC,QAAO;AAS/C,iBAAS,YAAYC,MAAK;AACxB,cAAIC,SAAQD,KAAI,MAAM,GAAG;AACzB,cAAIC,OAAM,SAAS,GAAG;AACpB,gBAAI,WAAW,CAACA,OAAMA,OAAM,SAAS,CAAC;AACtC,gBAAG,IAAI,KAAKD,IAAG,GAAG;AAChB,oBAAM,IAAI,MAAM,qFACyB;AAAA,YAC3C;AACA,gBAAG,KAAK,KAAKA,IAAG,GAAG;AACjB,oBAAM,IAAI,MAAM,qFACyB;AAAA,YAC3C;AACA,gBAAG,IAAI,KAAKA,IAAG,GAAG;AAChB,oBAAM,IAAI,MAAM,qFACyB;AAAA,YAC3C;AACA,gBAAIC,OAAM,SAAS,KAAK,OAAO,MAAM,QAAQ,MAAM,WAAW,KAAK,WAAW,IAAI;AAChF,oBAAM,IAAI,MAAM,2DAA2D;AAAA,YAC7E;AAEA,YAAAF,SAAQ,eAAe;AACvB,mBAAOE,OAAM,CAAC;AAAA,UAChB;AACA,iBAAOD;AAAA,QACT;AAAA,MACF;AAEA,aAAOH,OAAM,YAAY,OAAO;AAAA,IAClC;AAUA,mBAAe,qBAAqB,SAAS,mBAAmB,QAAQ,SAAS;AAC/E,eAAS,oBAAqBK,QAAOC,SAAQ,aAAa;AACxD,YAAI,CAACA,SAAQ;AACX,gBAAM,IAAI,MAAM,6BAA6BD,SAAQ,aAAa;AAAA,QACpE;AACA,YAAIC,QAAO,WAAW,GAAG;AACvB,gBAAM,IAAI,MAAM,6BAA6BD,SAAQ,qBAAqB;AAAA,QAC5E;AACA,iBAASR,KAAI,GAAGC,KAAIQ,QAAO,QAAQT,KAAIC,IAAGD,MAAK;AAC7C,cAAI,QAAQS,QAAOT,EAAC;AAEpB,cAAI,eAAe,uBAAuB,aAAa,KAAK,GAAG;AAC7D;AAAA,UACF;AAGA,cAAI,OAAO,UAAU,YAAY,OAAO,MAAM,KAAK,KAAK,QAAQ,YAAY,OAAO,QAAQ,YAAY,KAAK;AAC1G,kBAAM,IAAI;AAAA,cACR,iCAAiC,QAAQ,qBACzC,YAAY,MAAM,MAAM,YAAY;AAAA,YACtC;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,UAAI,eAAe,CAAC;AACpB,eAAS,IAAI,GAAG,IAAI,eAAe,IAAI,QAAQ,IAAI,GAAG,EAAE,GAAG;AACzD,YAAI,QAAQ,eAAe,IAAI,CAAC;AAChC,YAAI,SAAS,OAAO,KAAK;AACzB;AAAA,UACE;AAAA,UACA;AAAA,UACA,eAAe,YAAY,CAAC;AAAA,QAC9B;AACA,YAAI,OAAO,CAAC;AACZ,YAAI,IAAI;AACR,eAAO,EAAE,IAAI,OAAO,QAAQ;AAC1B,eAAK,CAAC,IAAI,OAAO,CAAC;AAAA,QACpB;AACA,iBAAS,KAAK,KAAK,eAAe,cAAc,EAC7C,OAAO,SAAS,MAAM,KAAK,KAAK;AAC/B,iBAAO,CAAC,OAAO,SAAS,IAAI,MAAM,CAAC;AAAA,QACrC,CAAC;AACH,YAAI,OAAO,WAAW,KAAK,QAAQ;AACjC,gBAAM,IAAI,MAAM,6BAA6B,QAAQ,4BAA4B;AAAA,QACnF;AACA,qBAAa,KAAK,IAAI;AAAA,MACxB;AACA,UAAI,aAAa,eAAe,sBAAsB,YAAY;AAClE,mBAAa,aAAa,cAAc,aAAa;AACrD,aAAO,IAAI,eAAe,cAAc,WAAW,CAAC,CAAC;AAAA,IACvD;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACz+BjB;AAAA;AAAA,WAAO,UAAU,OAAO,OAAO,IAAI,MAAM,CAAC,GAAG;AAAA,MAC3C,IAAI,GAAG,KAAK;AACV,YACE,QAAQ,gBACR,QAAQ,eACR,QAAQ,iBACR,QAAQ,UACR;AACA,kBAAQ,KAAK,kFAAkF,GAAG,mIAAmI;AAAA,QACvO;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA;AAAA;;;ACXF;AAAA;AAEA,QAAI,iBAAiB;AAErB,aAAS,aAAa;AAAA,IAAC;AAQvB,eAAW,cAAc,SAAS,YAAa,OAAO;AACpD,UAAI,QAAQ,MAAM,MAAM,GAAG;AAE3B,UAAI,MAAM,WAAW,GAAG;AACtB,eAAO;AAAA,UACL,UAAU,eAAe,MAAM,KAAK;AAAA,QACtC;AAAA,MACF,WAAW,MAAM,SAAS,GAAG;AAC3B,eAAO;AAAA,UACL,UAAU,eAAe;AAAA,YACvB,MAAM,MAAM,GAAG,CAAC,EAAE,KAAK,GAAG;AAAA,UAC5B;AAAA,UACA,SAAS,MAAM,MAAM,GAAG,MAAM,MAAM;AAAA,QACtC;AAAA,MACF,OAAO;AACL,cAAM,IAAI,MAAM,oBAAoB,KAAK;AAAA,MAC3C;AAAA,IACF;AAUA,eAAW,kBAAkB,SAAS,gBAAiB,YAAY,SAAS;AAC1E,aAAO,eAAe,MAAM,YAAY,OAAO;AAAA,IACjD;AAUA,eAAW,qBAAqB,SAAS,mBAAoB,QAAQ,SAAS;AAC5E,aAAO,eAAe,mBAAmB,QAAQ,OAAO;AAAA,IAC1D;AASA,eAAW,cAAc,SAAS,YAAa,MAAM;AACnD,UAAI,SAAS,KAAK,MAAM,IAAI;AAE5B,UAAI,WAAW;AAAA,QACb,WAAW,CAAC;AAAA,QACZ,aAAa,CAAC;AAAA,QACd,QAAQ,CAAC;AAAA,MACX;AAEA,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,YAAI,QAAQ,OAAO,CAAC;AACpB,YAAI,UAAU;AACd,YAAI,QAAQ,MAAM,KAAK;AAEvB,YAAI,MAAM,SAAS,GAAG;AACpB,cAAI,MAAM,MAAM,IAAI,GAAG;AACrB;AAAA,UACF,WAAY,UAAU,MAAM,MAAM,aAAa,GAAI;AACjD,qBAAS,UAAU,QAAQ,CAAC,CAAC,IAAI,QAAQ,CAAC;AAAA,UAC5C,OAAO;AACL,gBAAI,SAAS;AAEb,gBAAI;AACF,uBAAS,WAAW,YAAY,OAAO,KAAK;AAC5C,uBAAS,YAAY,KAAK,OAAO,QAAQ;AAAA,YAC3C,SAAS,KAAK;AACZ,uBAAS,OAAO,KAAK,IAAI;AAAA,YAC3B;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AASA,eAAW,YAAY,SAAS,UAAW,UAAU,UAAU;AAC7D,mBAAc,SAAS,UAAU,SAAS,KAAK,MAAM;AACnD,YAAI,KAAK;AACP,mBAAS,GAAG;AACZ;AAAA,QACF;AAEA,eAAO,SAAS,MAAM,WAAW,YAAY,KAAK,SAAS,CAAC,CAAC;AAAA,MAC/D,CAAC;AAAA,IACH;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": ["LuxonError", "Error", "InvalidDateTimeError", "constructor", "reason", "toMessage", "InvalidIntervalError", "InvalidDurationError", "ConflictingSpecificationError", "InvalidUnitError", "unit", "InvalidArgumentError", "ZoneIsAbstractError", "n", "s", "l", "DATE_SHORT", "year", "month", "day", "DATE_MED", "DATE_MED_WITH_WEEKDAY", "weekday", "DATE_FULL", "DATE_HUGE", "TIME_SIMPLE", "hour", "minute", "TIME_WITH_SECONDS", "second", "TIME_WITH_SHORT_OFFSET", "timeZoneName", "TIME_WITH_LONG_OFFSET", "TIME_24_SIMPLE", "hourCycle", "TIME_24_WITH_SECONDS", "TIME_24_WITH_SHORT_OFFSET", "TIME_24_WITH_LONG_OFFSET", "DATETIME_SHORT", "DATETIME_SHORT_WITH_SECONDS", "DATETIME_MED", "DATETIME_MED_WITH_SECONDS", "DATETIME_MED_WITH_WEEKDAY", "DATETIME_FULL", "DATETIME_FULL_WITH_SECONDS", "DATETIME_HUGE", "DATETIME_HUGE_WITH_SECONDS", "Zone", "type", "name", "<PERSON><PERSON><PERSON><PERSON>", "isUniversal", "offsetName", "ts", "opts", "formatOffset", "format", "offset", "equals", "otherZone", "<PERSON><PERSON><PERSON><PERSON>", "singleton", "SystemZone", "instance", "Intl", "DateTimeFormat", "resolvedOptions", "timeZone", "locale", "parseZoneInfo", "Date", "getTimezoneOffset", "dtfCache", "Map", "makeDTF", "zoneName", "dtf", "get", "undefined", "hour12", "era", "set", "typeToPos", "hackyOffset", "date", "formatted", "replace", "parsed", "exec", "fMonth", "fDay", "fYear", "fadOrBc", "fHour", "fMinute", "fSecond", "partsOffset", "formatToParts", "filled", "i", "length", "value", "pos", "isUndefined", "parseInt", "ianaZone<PERSON>ache", "IANAZone", "create", "zone", "resetCache", "clear", "isValidSpecifier", "isValidZone", "e", "valid", "NaN", "isNaN", "adOrBc", "Math", "abs", "adjustedHour", "asUTC", "objToLocalTS", "millisecond", "asTS", "over", "intlLFCache", "getCachedLF", "locString", "key", "JSON", "stringify", "ListFormat", "intlDTCache", "getCachedDTF", "intlNumCache", "getCachedINF", "inf", "NumberFormat", "intlRelCache", "getCachedRTF", "base", "cacheKeyOpts", "RelativeTimeFormat", "sysLocaleCache", "systemLocale", "intlResolvedOptionsCache", "getCachedIntResolvedOptions", "weekInfoCache", "getCachedWeekInfo", "data", "Locale", "getWeekInfo", "weekInfo", "fallbackWeekSettings", "parseLocaleString", "localeStr", "xIndex", "indexOf", "substring", "uIndex", "options", "selectedStr", "smaller", "numberingSystem", "calendar", "intlConfigString", "outputCalendar", "includes", "mapMonths", "f", "ms", "dt", "DateTime", "utc", "push", "mapWeekdays", "listStuff", "loc", "englishFn", "intlFn", "mode", "listingMode", "supportsFastNumbers", "startsWith", "PolyNumberFormatter", "intl", "forceSimple", "padTo", "floor", "otherOpts", "Object", "keys", "intlOpts", "useGrouping", "minimumIntegerDigits", "fixed", "roundTo", "padStart", "PolyDateFormatter", "originalZone", "z", "gmtOffset", "offsetZ", "setZone", "plus", "minutes", "map", "join", "toJSDate", "parts", "part", "PolyRelFormatter", "isEnglish", "style", "hasRelative", "rtf", "count", "English", "numeric", "firstDay", "minimalDays", "weekend", "fromOpts", "weekSettings", "defaultToEN", "specifiedLocale", "Settings", "defaultLocale", "localeR", "numberingSystemR", "defaultNumberingSystem", "outputCalendarR", "defaultOutputCalendar", "weekSettingsR", "validateWeekSettings", "defaultWeekSettings", "fromObject", "numbering", "parsedLocale", "parsedNumberingSystem", "parsedOutputCalendar", "weekdaysCache", "standalone", "monthsCache", "meridiemCache", "eraCache", "fastNumbersCached", "fastNumbers", "isActuallyEn", "has<PERSON>o<PERSON><PERSON><PERSON><PERSON>", "clone", "alts", "getOwnPropertyNames", "redefaultToEN", "redefaultToSystem", "months", "formatStr", "extract", "weekdays", "meridiems", "eras", "field", "df", "dt<PERSON><PERSON><PERSON><PERSON>", "results", "matching", "find", "m", "toLowerCase", "numberF<PERSON>atter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getWeekSettings", "hasLocaleWeekInfo", "getStartOfWeek", "getMinDaysInFirstWeek", "getWeekendDays", "other", "toString", "FixedOffsetZone", "utcInstance", "parseSpecifier", "r", "match", "signedOffset", "InvalidZone", "normalizeZone", "input", "defaultZone", "isString", "lowered", "isNumber", "numberingSystems", "arab", "arabext", "bali", "beng", "deva", "fullwide", "gujr", "hanidec", "khmr", "knda", "laoo", "limb", "mlym", "mong", "mymr", "orya", "tamldec", "telu", "thai", "tibt", "latn", "numberingSystemsUTF16", "hanidecChars", "split", "parseDigits", "str", "code", "charCodeAt", "search", "min", "max", "digitRegexCache", "resetDigitRegexCache", "digitRegex", "append", "ns", "appendCache", "regex", "RegExp", "now", "twoDigitCutoffYear", "throwOnInvalid", "cutoffYear", "t", "resetCaches", "Invalid", "explanation", "nonLeapLadder", "<PERSON><PERSON><PERSON><PERSON>", "unitOutOfRange", "dayOfWeek", "d", "UTC", "setUTCFullYear", "getUTCFullYear", "js", "getUTCDay", "computeOrdinal", "isLeapYear", "uncomputeOrdinal", "ordinal", "table", "month0", "findIndex", "isoWeekdayToLocal", "isoWeekday", "startOfWeek", "gregorianToWeek", "greg<PERSON><PERSON><PERSON>", "minDaysInFirstWeek", "weekNumber", "weekYear", "weeksInWeekYear", "timeObject", "weekT<PERSON><PERSON><PERSON><PERSON><PERSON>", "weekData", "weekdayOfJan4", "yearInDays", "daysInYear", "gregorianToOrdinal", "gregData", "ordinalToGregorian", "ordinalData", "usesLocalWeekValues", "obj", "hasLocaleWeekData", "localWeekday", "localWeekNumber", "localWeekYear", "hasIsoWeekData", "hasInvalidWeekData", "validYear", "isInteger", "validWeek", "integerBetween", "validWeekday", "hasInvalidOrdinalData", "validOrdinal", "hasInvalidGregorianData", "valid<PERSON><PERSON><PERSON>", "validDay", "daysInMonth", "hasInvalidTimeData", "validHour", "validMinute", "validSecond", "validMillisecond", "o", "isDate", "prototype", "call", "maybeA<PERSON>y", "thing", "Array", "isArray", "bestBy", "arr", "by", "compare", "reduce", "best", "next", "pair", "pick", "a", "k", "hasOwnProperty", "prop", "settings", "some", "v", "from", "bottom", "top", "floorMod", "x", "isNeg", "padded", "parseInteger", "string", "parseFloating", "parseFloat", "parse<PERSON><PERSON><PERSON>", "fraction", "number", "digits", "towardZero", "factor", "rounder", "trunc", "round", "mod<PERSON>onth", "modYear", "firstWeekOffset", "fwdlw", "weekOffset", "weekOffsetNext", "untruncateYear", "offsetFormat", "modified", "offHourStr", "offMinuteStr", "offHour", "Number", "offMin", "offMinSigned", "is", "asNumber", "numericValue", "normalizeObject", "normalizer", "normalized", "u", "hours", "sign", "RangeError", "monthsLong", "monthsShort", "<PERSON><PERSON><PERSON><PERSON>", "weekdaysLong", "weekdaysShort", "weekdaysNarrow", "erasLong", "erasShort", "eras<PERSON><PERSON><PERSON>", "meridiemForDateTime", "weekdayForDateTime", "monthForDateTime", "eraForDateTime", "formatRelativeTime", "narrow", "units", "years", "quarters", "weeks", "days", "seconds", "lastable", "isDay", "isInPast", "fmtValue", "singular", "lilUnits", "fmtUnit", "stringifyTokens", "splits", "tokenToString", "token", "literal", "val", "macroTokenToFormatOpts", "D", "Formats", "DD", "DDD", "DDDD", "tt", "ttt", "tttt", "T", "TT", "TTT", "TTTT", "ff", "fff", "ffff", "F", "FF", "FFF", "FFFF", "<PERSON><PERSON><PERSON>", "parseFormat", "fmt", "current", "currentFull", "bracketed", "c", "char<PERSON>t", "test", "formatOpts", "systemLoc", "formatWithSystemDefault", "formatDateTime", "formatDateTimeParts", "formatInterval", "interval", "start", "formatRange", "end", "num", "p", "formatDateTimeFromString", "knownEnglish", "useDateTimeFormatter", "isOffsetFixed", "allowZ", "meridiem", "<PERSON><PERSON><PERSON><PERSON>", "slice", "quarter", "formatDurationFromString", "dur", "tokenToField", "lildur", "mapped", "tokens", "realTokens", "found", "concat", "collapsed", "shiftTo", "filter", "ianaRegex", "combineRegexes", "regexes", "full", "source", "combineExtractors", "extractors", "mergedVals", "mergedZone", "cursor", "ex", "parse", "patterns", "extractor", "simpleParse", "ret", "offsetRegex", "isoExtendedZone", "isoTimeBaseRegex", "isoTimeRegex", "isoTimeExtensionRegex", "isoYmdRegex", "isoWeekRegex", "isoOrdinalRegex", "extractISOWeekData", "extractISOOrdinalData", "sqlYmdRegex", "sqlTimeRegex", "sqlTimeExtensionRegex", "int", "fallback", "extractISOYmd", "item", "extractISOTime", "milliseconds", "extractISOOffset", "local", "fullOffset", "extractIANAZone", "isoTimeOnly", "isoDuration", "extractISODuration", "yearStr", "monthStr", "weekStr", "dayStr", "hourStr", "minuteStr", "secondStr", "millisecondsStr", "hasNegativePrefix", "negativeSeconds", "maybeNegate", "force", "obsOffsets", "GMT", "EDT", "EST", "CDT", "CST", "MDT", "MST", "PDT", "PST", "fromStrings", "weekdayStr", "result", "rfc2822", "extractRFC2822", "obsOffset", "milOffset", "preprocessRFC2822", "trim", "rfc1123", "rfc850", "ascii", "extractRFC1123Or850", "extractASCII", "isoYmdWithTimeExtensionRegex", "isoWeekWithTimeExtensionRegex", "isoOrdinalWithTimeExtensionRegex", "isoTimeCombinedRegex", "extractISOYmdTimeAndOffset", "extractISOWeekTimeAndOffset", "extractISOOrdinalDateAndTime", "extractISOTimeAndOffset", "parseISODate", "parseRFC2822Date", "parseHTTPDate", "parseISODuration", "extractISOTimeOnly", "parseISOTimeOnly", "sqlYmdWithTimeExtensionRegex", "sqlTimeCombinedRegex", "extractISOTimeOffsetAndIANAZone", "parseSQL", "INVALID", "lowOrderMatrix", "casualMatrix", "daysInYearAccurate", "daysInMonthAccurate", "accurateMatrix", "orderedUnits", "reverseUnits", "reverse", "conf", "values", "conversionAccuracy", "matrix", "Duration", "durationTo<PERSON>illis", "vals", "_vals$milliseconds", "sum", "normalizeValues", "reduceRight", "previous", "previousVal", "conv", "rollUp", "removeZeroes", "newVals", "entries", "config", "accurate", "invalid", "isLuxonDuration", "fromMillis", "normalizeUnit", "fromDurationLike", "durationLike", "isDuration", "fromISO", "text", "fromISOTime", "week", "toFormat", "fmtOpts", "toHuman", "unitDisplay", "listStyle", "toObject", "toISO", "toISOTime", "millis", "<PERSON><PERSON><PERSON><PERSON>", "suppressMilliseconds", "suppressSeconds", "includePrefix", "includeOffset", "dateTime", "toJSON", "Symbol", "for", "invalidReason", "valueOf", "duration", "minus", "negate", "mapUnits", "fn", "mixed", "reconfigure", "as", "normalize", "rescale", "shiftToAll", "built", "accumulated", "lastUnit", "own", "ak", "negated", "invalidExplanation", "eq", "v1", "v2", "validateStartEnd", "Interval", "isLuxonInterval", "fromDateTimes", "builtStart", "friendlyDateTime", "builtEnd", "validateError", "after", "before", "startIsValid", "endIsValid", "isInterval", "lastDateTime", "toDuration", "startOf", "useLocaleWeeks", "diff", "<PERSON><PERSON><PERSON>", "isEmpty", "isAfter", "isBefore", "contains", "splitAt", "dateTimes", "sorted", "sort", "b", "added", "splitBy", "idx", "divideEqually", "numberOfParts", "overlaps", "abutsStart", "abutsEnd", "engulfs", "intersection", "union", "merge", "intervals", "final", "sofar", "xor", "currentCount", "ends", "time", "flattened", "difference", "toLocaleString", "toISODate", "dateFormat", "separator", "mapEndpoints", "mapFn", "Info", "hasDST", "proto", "isValidIANAZone", "locObj", "getMinimumDaysInFirstWeek", "getWeekendWeekdays", "monthsFormat", "weekdaysFormat", "features", "relative", "localeWeek", "dayDiff", "earlier", "later", "utcDayStart", "toUTC", "keepLocalTime", "highOrderDiffs", "differs", "lowestOrder", "highWater", "differ", "remaining<PERSON>ill<PERSON>", "lowerOrderUnits", "MISSING_FTP", "intUnit", "post", "deser", "NBSP", "String", "fromCharCode", "spaceOrNBSP", "spaceOrNBSPRegExp", "fixListRegex", "stripInsensitivities", "oneOf", "strings", "startIndex", "groups", "h", "simple", "escapeToken", "unitForToken", "one", "two", "three", "four", "six", "oneOrTwo", "oneToThree", "oneToSix", "oneToNine", "twoToFour", "fourToSix", "unitate", "partTypeStyleToTokenVal", "short", "long", "dayperiod", "<PERSON><PERSON><PERSON><PERSON>", "hour24", "tokenForPart", "resolvedOpts", "isSpace", "actualType", "buildRegex", "re", "handlers", "matches", "all", "matchIndex", "dateTimeFromMatches", "to<PERSON>ield", "specificOffset", "Z", "q", "M", "G", "y", "S", "dummyDateTimeCache", "getDummyDateTime", "maybeExpandMacroToken", "formatOptsToTokens", "expandMacroTokens", "Token<PERSON><PERSON><PERSON>", "disqualifying<PERSON>nit", "regexString", "explainFromTokens", "rawMatches", "parser", "parseFromTokens", "formatter", "MAX_DATE", "unsupportedZone", "possiblyCachedWeekData", "possiblyCachedLocalWeekData", "localWeekData", "inst", "old", "fixOffset", "localTS", "tz", "ut<PERSON><PERSON><PERSON><PERSON>", "o2", "o3", "tsToObj", "getUTCMonth", "getUTCDate", "getUTCHours", "getUTCMinutes", "getUTCSeconds", "getUTCMilliseconds", "objToTS", "adjustTime", "oPre", "millisToAdd", "parseDataToDateTime", "parsedZone", "interpretationZone", "toTechFormat", "extended", "longFormat", "extendedZone", "defaultUnitValues", "defaultWeekUnitValues", "defaultOrdinalUnitValues", "orderedWeekUnits", "orderedOrdinalUnits", "weeknumber", "weeksnumber", "weeknumbers", "weekyear", "weekyears", "normalizeUnitWithLocalWeeks", "guessOffsetForZone", "zoneOffsetTs", "offsetGuess", "zoneOffsetGuessCache", "quickDT", "<PERSON><PERSON><PERSON><PERSON>", "diffRelative", "calendary", "lastOpts", "argList", "args", "unchanged", "ot", "_zone", "isLuxonDateTime", "arguments", "fromJSDate", "zoneToUse", "fromSeconds", "tsNow", "containsOrdinal", "containsGregorYear", "containsGregorMD", "<PERSON><PERSON><PERSON><PERSON>", "definiteWeekDef", "useWeekData", "defaultValues", "objNow", "<PERSON><PERSON><PERSON><PERSON>", "higherOrderInvalid", "gregorian", "tsFinal", "offsetFinal", "fromRFC2822", "fromHTTP", "fromFormat", "localeToUse", "fromString", "fromSQL", "isDateTime", "parseFormatForOpts", "localeOpts", "tokenList", "expandFormat", "expanded", "ceil", "isWeekend", "monthShort", "monthLong", "weekdayShort", "weekdayLong", "offsetNameShort", "offsetNameLong", "isInDST", "getPossibleOffsets", "dayMs", "minuteMs", "oEarlier", "oLater", "o1", "ts1", "ts2", "c1", "c2", "isInLeapYear", "weeksInLocalWeekYear", "resolvedLocaleOptions", "toLocal", "keepCalendarTime", "newTS", "as<PERSON>bj", "setLocale", "settingWeekStuff", "normalizedUnit", "endOf", "toLocaleParts", "ext", "toISOWeekDate", "toRFC2822", "toHTTP", "toSQLDate", "toSQLTime", "includeZone", "includeOffsetSpace", "toSQL", "to<PERSON><PERSON><PERSON><PERSON>", "toUnixInteger", "toBSON", "includeConfig", "otherDateTime", "durOpts", "otherIsLater", "diffed", "diffNow", "until", "inputMs", "adjustedToZone", "toRelative", "padding", "toRelativeCalendar", "every", "fromFormatExplain", "fromStringExplain", "buildFormatParser", "fromFormatParser", "format<PERSON><PERSON>er", "dateTimeish", "VERSION", "i", "c", "value", "parse", "expression", "options", "val", "atoms", "field", "values"]}