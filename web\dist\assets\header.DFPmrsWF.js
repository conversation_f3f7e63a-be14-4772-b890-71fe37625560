const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.DeYhgzkX.js","assets/index.BHZI5pdK.js","assets/vue.BNx9QYep.js","assets/index.Dg-OhEXY.css","assets/_plugin-vue_export-helper.DlAUqK2U.js","assets/index.DBopHGVf.css"])))=>i.map(i=>d[i]);
import{T as n,_}from"./index.BHZI5pdK.js";import{d as s,M as c,k as i,A as u,D as l,u as e,b as d,o as p,w as m,l as f,P as h}from"./vue.BNx9QYep.js";const w=s({name:"layoutHeader"}),R=s({...w,setup(v){const o=h(()=>_(()=>import("./index.DeYhgzkX.js"),__vite__mapDeps([0,1,2,3,4,5]))),a=n(),{isTagsViewCurrenFull:t}=c(a);return(T,C)=>{const r=i("el-header");return u((p(),d(r,{class:"layout-header"},{default:m(()=>[f(e(o))]),_:1},512)),[[l,!e(t)]])}}});export{R as default};
