import {
  Ct,
  Fe
} from "./chunk-KSP3GDI3.js";
import "./chunk-NXSV4UZF.js";
import "./chunk-NROEEZWH.js";
import "./chunk-TMUISJ4F.js";
import "./chunk-ISH6AKKV.js";
import "./chunk-WPKI25LT.js";
import "./chunk-UOYEQWWP.js";
import "./chunk-YNRHTVZR.js";
import "./chunk-YLGFFQGZ.js";
import {
  defineComponent
} from "./chunk-NHLVTWLD.js";
import "./chunk-LK32TJAX.js";

// node_modules/.pnpm/@fast-crud+fast-extends@1.2_48d83ac4990201ef9a8c3e5855a79123/node_modules/@fast-crud/fast-extends/dist/fs-uploader-c6671961.mjs
var l = defineComponent({
  name: "FsUploader",
  props: {
    type: {}
  },
  setup(e) {
    async function t() {
      const { getDefaultType: o } = Ct(), p = e.type || o();
      return await Fe(p);
    }
    return {
      getUploaderRef: t
    };
  }
});
export {
  l as default
};
//# sourceMappingURL=fs-uploader-c6671961-5BRVIHUG.js.map
