import{r as O,ai as G,x as H,w as K,p as X,Z as ee,q as te,e as m}from"./index.BHZI5pdK.js";import{d as q,h as M,g as P,c as ae,j as le,k as c,y as se,a as D,o as _,e as o,l as e,w as a,F as E,p as N,b as k,q as C,u as y,s as f,A as oe,m as R}from"./vue.BNx9QYep.js";import{_ as ne}from"./_plugin-vue_export-helper.DlAUqK2U.js";const B="/api/price-analysis/";function re(i){return O({url:B+"query/",method:"post",data:i})}function ie(i){return O({url:B+"export/",method:"post",data:i,responseType:"blob",timeout:6e4})}function de(i){return i?`¥${Number(i).toLocaleString("zh-CN",{minimumFractionDigits:2})}`:"¥0.00"}function ue(i){const g=parseFloat(i.replace("%","").replace("+",""));return Math.abs(g)>=50?"danger":Math.abs(g)>=30?"warning":Math.abs(g)>=20?"info":"success"}function ce(i){return i.includes("严重")||i.includes("高度")?"danger":i.includes("显著")?"warning":i.includes("轻微")?"info":"success"}const pe={class:"price-analysis-container"},me={class:"stats-cards"},_e={class:"stat-content"},fe={class:"stat-icon sales"},ve={class:"stat-info"},ge={class:"stat-value"},he={class:"stat-content"},be={class:"stat-icon customer"},ye={class:"stat-info"},we={class:"stat-value"},Ce={class:"stat-content"},xe={class:"stat-icon price"},ze={class:"stat-info"},ke={class:"stat-value"},Ve={class:"stat-content"},De={class:"stat-icon amount"},Se={class:"stat-info"},Ue={class:"stat-value"},Me={class:"card-header"},Pe={class:"header-actions"},Re={key:1,class:"text-success"},Ae={key:0,class:"pagination-container"},Fe=q({name:"PriceAnalysis"}),Ee=q({...Fe,setup(i){const g=M(!1),h=M([]),p=P({currentPage:1,pageSize:20}),r=P({year:new Date().getFullYear(),month:new Date().getMonth()+1,threshold:.1,company:"",materialCategory:""}),w=P({abnormalCustomers:0,highRiskCustomers:0,materialCount:0,totalAmount:0}),A=M([]),L=M([1,2,3,4,5,6,7,8,9,10,11,12]),T=ae(()=>{const n=(p.currentPage-1)*p.pageSize,t=n+p.pageSize;return h.value.slice(n,t)}),Y=()=>{const n=new Date().getFullYear();for(let t=n;t>=n-5;t--)A.value.push(t)},F=async()=>{g.value=!0;try{const n=await re(r);if(n.code===200){h.value=n.data.list||[],p.currentPage=1;const t=n.data.statistics||{};w.abnormalCustomers=t.abnormalCustomers||0,w.highRiskCustomers=t.highRiskCustomers||0,w.materialCount=t.materialCount||0,w.totalAmount=t.totalAmount||0,m.success(`查询完成，共${h.value.length}条记录`)}else m.error(n.message||"查询失败")}catch(n){console.error("查询失败:",n),m.error("查询失败："+(n.message||"网络错误"))}finally{g.value=!1}},$=async()=>{var n;if(!h.value.length){m.warning("没有数据可导出，请先查询数据");return}try{m.info("正在生成Excel文件，请稍候...");const t=await ie(r);if(t&&t.data instanceof Blob){const l=window.URL.createObjectURL(t.data),d=document.createElement("a");d.href=l;let u=`价格分析报告_${r.year}年${r.month}月_${new Date().toISOString().slice(0,10)}.xlsx`;const b=t.headers["content-disposition"];if(b){const x=b.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);if(x&&x[1]&&(u=x[1].replace(/['"]/g,""),u.startsWith("=?utf-8?b?")))try{const S=u.substring(10,u.length-2);u=decodeURIComponent(escape(atob(S)))}catch{console.warn("文件名解码失败，使用默认文件名")}}d.download=u,document.body.appendChild(d),d.click(),document.body.removeChild(d),window.URL.revokeObjectURL(l),m.success("Excel文件导出成功")}else m.error("导出失败：响应格式错误")}catch(t){if(console.error("导出失败:",t),t.response)if(t.response.data instanceof Blob)try{const l=await t.response.data.text(),d=JSON.parse(l);m.error(d.message||"导出失败")}catch{m.error("导出失败：服务器错误")}else m.error(((n=t.response.data)==null?void 0:n.message)||"导出失败");else m.error("导出失败："+(t.message||"网络错误"))}},j=n=>{p.pageSize=n,p.currentPage=1},I=n=>{p.currentPage=n};return le(()=>{Y();const n=new Date;n.setMonth(n.getMonth()-1),r.year=n.getFullYear(),r.month=n.getMonth()+1,F()}),(n,t)=>{const l=c("el-option"),d=c("el-select"),u=c("el-form-item"),b=c("el-icon"),x=c("el-button"),S=c("el-form"),z=c("el-card"),U=c("el-col"),Q=c("el-row"),V=c("el-tag"),v=c("el-table-column"),J=c("el-table"),W=c("el-pagination"),Z=se("loading");return _(),D("div",pe,[t[18]||(t[18]=o("div",{class:"page-header"},[o("h2",null,"价格分析中心"),o("p",null,"监控价格变化和市场趋势，及时发现价格异常客户")],-1)),e(z,{class:"query-card",shadow:"never"},{default:a(()=>[e(S,{model:r,inline:""},{default:a(()=>[e(u,{label:"查询年份"},{default:a(()=>[e(d,{modelValue:r.year,"onUpdate:modelValue":t[0]||(t[0]=s=>r.year=s),placeholder:"选择年份",style:{width:"120px"}},{default:a(()=>[(_(!0),D(E,null,N(A.value,s=>(_(),k(l,{key:s,label:s+"年",value:s},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(u,{label:"查询月份"},{default:a(()=>[e(d,{modelValue:r.month,"onUpdate:modelValue":t[1]||(t[1]=s=>r.month=s),placeholder:"选择月份",style:{width:"120px"}},{default:a(()=>[(_(!0),D(E,null,N(L.value,s=>(_(),k(l,{key:s,label:s+"月",value:s},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(u,{label:"偏离阈值"},{default:a(()=>[e(d,{modelValue:r.threshold,"onUpdate:modelValue":t[2]||(t[2]=s=>r.threshold=s),placeholder:"选择阈值",style:{width:"140px"}},{default:a(()=>[e(l,{label:"10%以上",value:.1}),e(l,{label:"20%以上",value:.2}),e(l,{label:"30%以上",value:.3}),e(l,{label:"50%以上",value:.5})]),_:1},8,["modelValue"])]),_:1}),e(u,{label:"公司筛选"},{default:a(()=>[e(d,{modelValue:r.company,"onUpdate:modelValue":t[3]||(t[3]=s=>r.company=s),placeholder:"选择公司",style:{width:"240px"},clearable:""},{default:a(()=>[e(l,{label:"全部公司",value:""}),e(l,{label:"江苏华绿生物科技集团股份有限公司",value:"江苏华绿生物科技集团股份有限公司"}),e(l,{label:"江苏华骏生物科技有限公司",value:"江苏华骏生物科技有限公司"}),e(l,{label:"江苏华蕈生物科技有限公司",value:"江苏华蕈生物科技有限公司"}),e(l,{label:"泗阳华盛生物科技有限公司",value:"泗阳华盛生物科技有限公司"}),e(l,{label:"泗阳华茂生物科技有限公司",value:"泗阳华茂生物科技有限公司"}),e(l,{label:"浙江华实生物科技有限公司",value:"浙江华实生物科技有限公司"})]),_:1},8,["modelValue"])]),_:1}),e(u,{label:"物料大类"},{default:a(()=>[e(d,{modelValue:r.materialCategory,"onUpdate:modelValue":t[4]||(t[4]=s=>r.materialCategory=s),placeholder:"选择物料大类",style:{width:"180px"},clearable:""},{default:a(()=>[e(l,{label:"全部大类",value:""}),e(l,{label:"7001(金针菇)",value:"7001"}),e(l,{label:"7002(白玉菇)",value:"7002"}),e(l,{label:"7003(蟹味菇)",value:"7003"}),e(l,{label:"7004(白玉蟹味双拼)",value:"7004"}),e(l,{label:"7005(舞茸)",value:"7005"}),e(l,{label:"7006(灰树花)",value:"7006"}),e(l,{label:"7007(鹿茸菇)",value:"7007"}),e(l,{label:"7008(华之珍)",value:"7008"}),e(l,{label:"7009(海鲜菇)",value:"7009"}),e(l,{label:"7010(虫草花)",value:"7010"}),e(l,{label:"7011(其他外购菇)",value:"7011"}),e(l,{label:"7012(有机肥)",value:"7012"}),e(l,{label:"7013(双孢菇)",value:"7013"})]),_:1},8,["modelValue"])]),_:1}),e(u,null,{default:a(()=>[e(x,{type:"primary",onClick:F,loading:g.value},{default:a(()=>[e(b,null,{default:a(()=>[e(y(G))]),_:1}),t[7]||(t[7]=C(" 查询分析 "))]),_:1,__:[7]},8,["loading"]),e(x,{onClick:$,disabled:!h.value.length},{default:a(()=>[e(b,null,{default:a(()=>[e(y(H))]),_:1}),t[8]||(t[8]=C(" 导出数据 "))]),_:1,__:[8]},8,["disabled"])]),_:1})]),_:1},8,["model"])]),_:1}),o("div",me,[e(Q,{gutter:20},{default:a(()=>[e(U,{span:6},{default:a(()=>[e(z,{class:"stat-card",shadow:"hover"},{default:a(()=>[o("div",_e,[o("div",fe,[e(b,{size:"32"},{default:a(()=>[e(y(K))]),_:1})]),o("div",ve,[t[9]||(t[9]=o("div",{class:"stat-title"},"异常客户数",-1)),o("div",ge,f(w.abnormalCustomers),1),t[10]||(t[10]=o("div",{class:"stat-desc"},"价格偏离客户总数",-1))])])]),_:1})]),_:1}),e(U,{span:6},{default:a(()=>[e(z,{class:"stat-card",shadow:"hover"},{default:a(()=>[o("div",he,[o("div",be,[e(b,{size:"32"},{default:a(()=>[e(y(X))]),_:1})]),o("div",ye,[t[11]||(t[11]=o("div",{class:"stat-title"},"高风险客户",-1)),o("div",we,f(w.highRiskCustomers),1),t[12]||(t[12]=o("div",{class:"stat-desc"},"严重价格偏离客户",-1))])])]),_:1})]),_:1}),e(U,{span:6},{default:a(()=>[e(z,{class:"stat-card",shadow:"hover"},{default:a(()=>[o("div",Ce,[o("div",xe,[e(b,{size:"32"},{default:a(()=>[e(y(ee))]),_:1})]),o("div",ze,[t[13]||(t[13]=o("div",{class:"stat-title"},"涉及物料数",-1)),o("div",ke,f(w.materialCount),1),t[14]||(t[14]=o("div",{class:"stat-desc"},"存在价格异常的物料",-1))])])]),_:1})]),_:1}),e(U,{span:6},{default:a(()=>[e(z,{class:"stat-card",shadow:"hover"},{default:a(()=>[o("div",Ve,[o("div",De,[e(b,{size:"32"},{default:a(()=>[e(y(te))]),_:1})]),o("div",Se,[t[15]||(t[15]=o("div",{class:"stat-title"},"影响金额",-1)),o("div",Ue,f(y(de)(w.totalAmount)),1),t[16]||(t[16]=o("div",{class:"stat-desc"},"异常客户购买总额",-1))])])]),_:1})]),_:1})]),_:1})]),e(z,{class:"table-card",shadow:"never"},{header:a(()=>[o("div",Me,[t[17]||(t[17]=o("span",null,"价格异常客户明细",-1)),o("div",Pe,[r.year&&r.month?(_(),k(V,{key:0,type:"info"},{default:a(()=>[C(f(r.year)+"年"+f(r.month)+"月数据 ",1)]),_:1})):R("",!0),h.value.length?(_(),k(V,{key:1,type:"success"},{default:a(()=>[C(" 共"+f(h.value.length)+"条异常记录 ",1)]),_:1})):R("",!0)])])]),default:a(()=>[oe((_(),k(J,{data:T.value,stripe:"",border:"",height:"500",style:{width:"100%"}},{default:a(()=>[e(v,{prop:"公司简称",label:"公司",width:"200",fixed:"left","show-overflow-tooltip":""}),e(v,{prop:"物料名称",label:"物料名称",width:"200","show-overflow-tooltip":""}),e(v,{prop:"客户名称",label:"客户名称",width:"200","show-overflow-tooltip":""}),e(v,{prop:"客户平均单价",label:"客户单价",width:"100",align:"right"}),e(v,{prop:"市场平均单价",label:"市场单价",width:"100",align:"right"}),e(v,{prop:"偏离百分比",label:"偏离幅度",width:"100",align:"center"},{default:a(s=>[e(V,{type:y(ue)(s.row.偏离百分比),size:"small"},{default:a(()=>[C(f(s.row.偏离百分比),1)]),_:2},1032,["type"])]),_:1}),e(v,{prop:"偏离等级",label:"风险等级",width:"120",align:"center"},{default:a(s=>[e(V,{type:y(ce)(s.row.偏离等级),size:"small"},{default:a(()=>[C(f(s.row.偏离等级),1)]),_:2},1032,["type"])]),_:1}),e(v,{prop:"偏离方向",label:"偏离方向",width:"100",align:"center"}),e(v,{prop:"购买总额",label:"购买金额",width:"120",align:"right"}),e(v,{prop:"风险提示",label:"风险提示",width:"150",align:"center"},{default:a(s=>[s.row.风险提示!=="正常"?(_(),k(V,{key:0,type:"warning",size:"small"},{default:a(()=>[C(f(s.row.风险提示),1)]),_:2},1024)):(_(),D("span",Re,f(s.row.风险提示),1))]),_:1})]),_:1},8,["data"])),[[Z,g.value]]),h.value.length>0?(_(),D("div",Ae,[e(W,{"current-page":p.currentPage,"onUpdate:currentPage":t[5]||(t[5]=s=>p.currentPage=s),"page-size":p.pageSize,"onUpdate:pageSize":t[6]||(t[6]=s=>p.pageSize=s),"page-sizes":[10,20,50,100],small:!1,disabled:g.value,background:!0,layout:"total, sizes, prev, pager, next, jumper",total:h.value.length,onSizeChange:j,onCurrentChange:I},null,8,["current-page","page-size","disabled","total"])])):R("",!0)]),_:1})])}}}),Be=ne(Ee,[["__scopeId","data-v-253e03bc"]]);export{Be as default};
