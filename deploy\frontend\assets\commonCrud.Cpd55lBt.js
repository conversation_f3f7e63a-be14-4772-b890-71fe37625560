import{G as l,v as c}from"./index.BHZI5pdK.js";import{d,h as p,v as h,a as f,o as w,s as u,J as _}from"./vue.BNx9QYep.js";const b=d({__name:"index",props:{modelValue:{type:Number||String}},setup(s){const e=s,t=p();return h(()=>e.modelValue,async o=>{const r=await l().getParentDeptById(o);if(r!=null&&r.nodes){let i="";console.log(r),r.nodes.forEach((n,m)=>{i+=m>0?`/${n.name}`:n.name}),t.value=i}},{immediate:!0}),(o,a)=>(w(),f("div",null,u(t.value),1))}}),g={create_datetime:{form:!1,table:!1,search:!1,width:160},update_datetime:{form:!1,table:!1,search:!1,width:160},creator_name:{form:!1,table:!1,search:!1,width:100},modifier_name:{form:!1,table:!1,search:!1,width:100},dept_belong_id:{form:!1,table:!1,search:!1,width:300},description:{form:!1,table:!1,search:!1,width:100}};function v(s,e={}){const t={...s};for(const o in e)if(Object.prototype.hasOwnProperty.call(e,o)){const a=t[o],r=e[o];a&&r&&(t[o]={...a,...r})}return t}const y=(s={})=>{const e=v(g,s);return{dept_belong_id:{title:"所属部门",type:"dict-tree",search:{show:e.dept_belong_id.search},dict:c({url:"/api/system/dept/all_dept/",isTree:!0,value:"id",label:"name",children:"children"}),column:{align:"center",width:e.dept_belong_id.width,show:e.dept_belong_id.table,component:{is:_(b),vModel:"modelValue"}},form:{show:e.dept_belong_id.form,component:{multiple:!1,clearable:!0,props:{checkStrictly:!0,props:{label:"name",value:"id"}}},helper:"默认不填则为当前创建用户的部门ID"}},description:{title:"备注",search:{show:e.description.search},type:"textarea",column:{width:e.description.width,show:e.description.table},form:{show:e.description.form,component:{placeholder:"请输入内容",showWordLimit:!0,maxlength:"200"}},viewForm:{show:!0}},modifier_name:{title:"修改人",search:{show:e.modifier_name.search},column:{width:e.modifier_name.width,show:e.modifier_name.table},form:{show:e.modifier_name.form},viewForm:{show:!0}},creator_name:{title:"创建人",search:{show:e.creator_name.search},column:{width:e.creator_name.width,show:e.creator_name.table},form:{show:e.creator_name.form},viewForm:{show:!0}},update_datetime:{title:"更新时间",type:"datetime",search:{show:e.update_datetime.search,col:{span:8},component:{type:"datetimerange",props:{"start-placeholder":"开始时间","end-placeholder":"结束时间","value-format":"YYYY-MM-DD HH:mm:ss","picker-options":{shortcuts:[{text:"最近一周",onClick(t){const o=new Date,a=new Date;a.setTime(a.getTime()-3600*1e3*24*7),t.$emit("pick",[a,o])}},{text:"最近一个月",onClick(t){const o=new Date,a=new Date;a.setTime(a.getTime()-3600*1e3*24*30),t.$emit("pick",[a,o])}},{text:"最近三个月",onClick(t){const o=new Date,a=new Date;a.setTime(a.getTime()-3600*1e3*24*90),t.$emit("pick",[a,o])}}]}}},valueResolve(t){const{value:o}=t;o&&(t.form.update_datetime_after=o[0],t.form.update_datetime_before=o[1],delete t.form.update_datetime)}},column:{width:e.update_datetime.width,show:e.update_datetime.table},form:{show:e.update_datetime.form},viewForm:{show:!0}},create_datetime:{title:"创建时间",type:"datetime",search:{show:e.create_datetime.search,col:{span:8},component:{type:"datetimerange",props:{"start-placeholder":"开始时间","end-placeholder":"结束时间","value-format":"YYYY-MM-DD HH:mm:ss","picker-options":{shortcuts:[{text:"最近一周",onClick(t){const o=new Date,a=new Date;a.setTime(a.getTime()-3600*1e3*24*7),t.$emit("pick",[a,o])}},{text:"最近一个月",onClick(t){const o=new Date,a=new Date;a.setTime(a.getTime()-3600*1e3*24*30),t.$emit("pick",[a,o])}},{text:"最近三个月",onClick(t){const o=new Date,a=new Date;a.setTime(a.getTime()-3600*1e3*24*90),t.$emit("pick",[a,o])}}]}}},valueResolve(t){const{value:o}=t;o&&(t.form.create_datetime_after=o[0],t.form.create_datetime_before=o[1],delete t.form.create_datetime)}},column:{width:e.create_datetime.width,show:e.create_datetime.table},form:{show:e.create_datetime.form},viewForm:{show:!0}}}};export{y as c};
