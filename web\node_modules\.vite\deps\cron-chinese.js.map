{"version": 3, "sources": ["../../.pnpm/cron-chinese@1.2.0/node_modules/cron-chinese/index.js"], "sourcesContent": ["export function humanizeCronInChinese(cron) {\n    const tokens = cron.trim().split(' ');\n    const cronStruct = {\n        time: {\n            minute: compileNode(tokens[0]),\n            hour: compileNode(tokens[1])\n        },\n        date: {\n            dayInMonth: compileNode(tokens[2]),\n            month: compileNode(tokens[3]),\n            dayInWeek: compileNode(tokens[4])\n        }\n    };\n\n    cronStruct.date = compileDatePart(cronStruct.date);\n    cronStruct.time = compileTimePart(cronStruct.time);\n\n    if (cronStruct.date.anyCount === 3 && cronStruct.time.text[0] === '每') {\n        // 避免 '每日每分钟' 出现\n        cronStruct.date.text = '';\n    }\n\n    return cronStruct.date.text + cronStruct.time.text;\n}\n\nfunction compileNode(raw) {\n    const compiled = { raw };\n    compiled.isAny = raw === '*';\n    compiled.hasStepping = raw.indexOf('/') >= 0;\n    compiled.hasList = raw.indexOf(',') >= 0;\n    compiled.hasRange = raw.indexOf('-') >= 0;\n    compiled.values = raw.split(',');\n    return compiled;\n}\n\nfunction compileDatePart(date) {\n    date.anyCount = date.month.isAny + date.dayInMonth.isAny + date.dayInWeek.isAny;\n    if (date.anyCount === 3) {\n        date.text = '每日';\n    } else if (date.anyCount === 2) {\n        if (date.month.isAny === false) {\n            // X 月每日\n            date.text = parseMonthToken(date.month.raw) + '每日';\n        } else if (date.dayInMonth.isAny === false) {\n            // 每月 X 日\n            date.text = '每月' + parseDayInMonthToken(date.dayInMonth.raw);\n        } else {\n            // 每周 X\n            date.text = '每' + parseDayInWeekToken(date.dayInWeek.raw);\n        }\n    } else if (date.anyCount === 1) {\n        if (date.month.isAny) {\n            // 每月 X 日或周 X\n            date.text = '每月' + parseDayInMonthToken(date.dayInMonth.raw) + '或' + parseDayInWeekToken(date.dayInWeek.raw);\n        } else if (date.dayInMonth.isAny) {\n            // X 月的每周 X\n            date.text = parseMonthToken(date.month.raw) + '的每' + parseDayInWeekToken(date.dayInWeek.raw);\n        } else {\n            // X 月 X 日\n            date.text = parseMonthToken(date.month.raw) + parseDayInMonthToken(date.dayInMonth.raw);\n        }\n    } else {\n        // X月X日或周X\n        date.text = parseMonthToken(date.month.raw) + parseDayInMonthToken(date.dayInMonth.raw) + '或' + parseDayInWeekToken(date.dayInWeek.raw);\n    }\n\n    return date;\n}\n\nfunction compileTimePart(time) {\n    time.anyCount = time.hour.isAny + time.minute.isAny;\n    if (time.anyCount === 2) {\n        time.text = '每分钟';\n    } else if (time.anyCount === 1) {\n        if (time.hour.isAny) {\n            if (time.minute.hasStepping) {\n                const parts = time.minute.raw.split('/');\n                if (time.minute.hasRange || time.minute.hasList) {\n                    time.text = '每小时的第' + parts[0] + '分钟(间隔' + parts[1] + '分钟)';\n                } else {\n                    time.text = '每隔' + time.minute.raw.split('/')[1] + '分钟';\n                }\n            } else {\n                time.text = '每小时的第' + time.minute.raw + '分钟';\n            }\n        } else {\n            if (time.hour.hasStepping) {\n                const parts = time.hour.raw.split('/');\n                if (time.hour.hasRange || time.hour.hasList) {\n                    time.text = parts[0] + '时的每一分钟(间隔' + parts[1] + '小时)';\n                } else {\n                    time.text = '每隔' + time.minute.raw.split('/')[1] + '分钟';\n                }\n            } else {\n                time.text = time.hour.raw + '时的每一分钟';\n            }\n        }\n    } else {\n        if (time.hour.hasStepping || time.minute.hasStepping) {\n            let hourString;\n            if (time.hour.hasStepping) {\n                const parts = time.hour.raw.split('/');\n                if (time.hour.hasList || time.hour.hasRange) {\n                    hourString = parts[0] + '时(间隔' + parts[1] + '小时)';\n                } else {\n                    hourString = '每' + parts[1] + '小时';\n                }\n            } else {\n                hourString = time.hour.raw + '时';\n            }\n\n            let minuteString;\n            if (time.minute.hasStepping) {\n                const parts = time.minute.raw.split('/');\n                if (time.minute.hasRange || time.minute.hasList) {\n                    minuteString = '第' + parts[0] + '分钟(间隔' + parts[1] + '分钟)';\n                } else {\n                    minuteString = '每' + parts[1] + '分钟';\n                }\n            } else {\n                minuteString = '第' + time.minute.raw + '分钟';\n            }\n\n            time.text = hourString + '的' + minuteString;\n            return time;\n        }\n\n        if (!time.hour.hasList && !time.hour.hasRange) {\n            if (!time.minute.hasList) {\n                if (time.minute.hasRange) {\n                    // XX:XX - XX:YY\n                    const minuteRange = time.minute.raw.split('-');\n                    time.text = time.hour.raw.padStart(2, '0') + ':' + minuteRange[0].padStart(2, '0') + '-' + time.hour.raw.padStart(2, '0') + ':' + minuteRange[1].padStart(2, '0');\n                } else {\n                    // XX:XX\n                    time.text = time.hour.raw.padStart(2, '0') + ':' + time.minute.raw.padStart(2, '0');\n                }\n            } else {\n                time.text = time.hour.raw + '时的第' + time.minute.raw + '分钟';\n            }\n        } else {\n            time.text = time.hour.raw + '时的第' + time.minute.raw + '分钟';\n        }\n    }\n\n    return time;\n}\n\nfunction parseMonthToken(month) {\n    // replace month name if any\n    monthMap.forEach((x, i) => {\n        month = month.replace(x, (i + 1).toString());\n    });\n    if (month.indexOf('/') >= 0) {\n        const parts = month.split('/');\n        return parts[0] === '*' ? ('每' + parts[1] + '月') : (parts[0] + '月(间隔' + parts[1] + '月)');\n    }\n\n    return month + '月';\n}\n\nfunction parseDayInMonthToken(dayInMonth) {\n    if (dayInMonth.indexOf('/') >= 0) {\n        const parts = dayInMonth.split('/');\n        return parts[0] === '*' ? ('每' + parts[1] + '日') : (parts[1] + '日(间隔' + parts[1] + '日)');\n    }\n    return dayInMonth + '日';\n}\n\nfunction parseDayInWeekToken(dayInWeek) {\n    dayInWeekMap.forEach((x, i) => {\n        dayInWeek = dayInWeek.replace(x, i.toString());\n    });\n    if (dayInWeek.indexOf(',') >= 0) {\n        if (dayInWeek.indexOf('-') < 0) {\n            // 周三、四、六\n            return '周' + dayInWeek.split(',').map(x => dayInWeekNameMap[Number(x)]).join('、');\n        }\n\n        return dayInWeek.split(',').map(x => parseDayInWeekToken(x)).join(',');\n    }\n\n    if (dayInWeek.indexOf('-') >= 0) {\n        return dayInWeek.split('-').map(x => parseDayInWeekToken(x)).join('~');\n    }\n\n    return '周' + dayInWeekNameMap[Number(dayInWeek)];\n}\n\nconst dayInWeekNameMap = ['日', '一', '二', '三', '四', '五', '六'];\nconst dayInWeekMap = ['SUN', 'MON', 'TUE', 'WEB', 'THU', 'FRI', 'SAT'].map(x => new RegExp(x, 'ig'));\nconst monthMap = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'].map(x => new RegExp(x, 'ig'));\n"], "mappings": ";;;AAAO,SAAS,sBAAsB,MAAM;AACxC,QAAM,SAAS,KAAK,KAAK,EAAE,MAAM,GAAG;AACpC,QAAM,aAAa;AAAA,IACf,MAAM;AAAA,MACF,QAAQ,YAAY,OAAO,CAAC,CAAC;AAAA,MAC7B,MAAM,YAAY,OAAO,CAAC,CAAC;AAAA,IAC/B;AAAA,IACA,MAAM;AAAA,MACF,YAAY,YAAY,OAAO,CAAC,CAAC;AAAA,MACjC,OAAO,YAAY,OAAO,CAAC,CAAC;AAAA,MAC5B,WAAW,YAAY,OAAO,CAAC,CAAC;AAAA,IACpC;AAAA,EACJ;AAEA,aAAW,OAAO,gBAAgB,WAAW,IAAI;AACjD,aAAW,OAAO,gBAAgB,WAAW,IAAI;AAEjD,MAAI,WAAW,KAAK,aAAa,KAAK,WAAW,KAAK,KAAK,CAAC,MAAM,KAAK;AAEnE,eAAW,KAAK,OAAO;AAAA,EAC3B;AAEA,SAAO,WAAW,KAAK,OAAO,WAAW,KAAK;AAClD;AAEA,SAAS,YAAY,KAAK;AACtB,QAAM,WAAW,EAAE,IAAI;AACvB,WAAS,QAAQ,QAAQ;AACzB,WAAS,cAAc,IAAI,QAAQ,GAAG,KAAK;AAC3C,WAAS,UAAU,IAAI,QAAQ,GAAG,KAAK;AACvC,WAAS,WAAW,IAAI,QAAQ,GAAG,KAAK;AACxC,WAAS,SAAS,IAAI,MAAM,GAAG;AAC/B,SAAO;AACX;AAEA,SAAS,gBAAgB,MAAM;AAC3B,OAAK,WAAW,KAAK,MAAM,QAAQ,KAAK,WAAW,QAAQ,KAAK,UAAU;AAC1E,MAAI,KAAK,aAAa,GAAG;AACrB,SAAK,OAAO;AAAA,EAChB,WAAW,KAAK,aAAa,GAAG;AAC5B,QAAI,KAAK,MAAM,UAAU,OAAO;AAE5B,WAAK,OAAO,gBAAgB,KAAK,MAAM,GAAG,IAAI;AAAA,IAClD,WAAW,KAAK,WAAW,UAAU,OAAO;AAExC,WAAK,OAAO,OAAO,qBAAqB,KAAK,WAAW,GAAG;AAAA,IAC/D,OAAO;AAEH,WAAK,OAAO,MAAM,oBAAoB,KAAK,UAAU,GAAG;AAAA,IAC5D;AAAA,EACJ,WAAW,KAAK,aAAa,GAAG;AAC5B,QAAI,KAAK,MAAM,OAAO;AAElB,WAAK,OAAO,OAAO,qBAAqB,KAAK,WAAW,GAAG,IAAI,MAAM,oBAAoB,KAAK,UAAU,GAAG;AAAA,IAC/G,WAAW,KAAK,WAAW,OAAO;AAE9B,WAAK,OAAO,gBAAgB,KAAK,MAAM,GAAG,IAAI,OAAO,oBAAoB,KAAK,UAAU,GAAG;AAAA,IAC/F,OAAO;AAEH,WAAK,OAAO,gBAAgB,KAAK,MAAM,GAAG,IAAI,qBAAqB,KAAK,WAAW,GAAG;AAAA,IAC1F;AAAA,EACJ,OAAO;AAEH,SAAK,OAAO,gBAAgB,KAAK,MAAM,GAAG,IAAI,qBAAqB,KAAK,WAAW,GAAG,IAAI,MAAM,oBAAoB,KAAK,UAAU,GAAG;AAAA,EAC1I;AAEA,SAAO;AACX;AAEA,SAAS,gBAAgB,MAAM;AAC3B,OAAK,WAAW,KAAK,KAAK,QAAQ,KAAK,OAAO;AAC9C,MAAI,KAAK,aAAa,GAAG;AACrB,SAAK,OAAO;AAAA,EAChB,WAAW,KAAK,aAAa,GAAG;AAC5B,QAAI,KAAK,KAAK,OAAO;AACjB,UAAI,KAAK,OAAO,aAAa;AACzB,cAAM,QAAQ,KAAK,OAAO,IAAI,MAAM,GAAG;AACvC,YAAI,KAAK,OAAO,YAAY,KAAK,OAAO,SAAS;AAC7C,eAAK,OAAO,UAAU,MAAM,CAAC,IAAI,UAAU,MAAM,CAAC,IAAI;AAAA,QAC1D,OAAO;AACH,eAAK,OAAO,OAAO,KAAK,OAAO,IAAI,MAAM,GAAG,EAAE,CAAC,IAAI;AAAA,QACvD;AAAA,MACJ,OAAO;AACH,aAAK,OAAO,UAAU,KAAK,OAAO,MAAM;AAAA,MAC5C;AAAA,IACJ,OAAO;AACH,UAAI,KAAK,KAAK,aAAa;AACvB,cAAM,QAAQ,KAAK,KAAK,IAAI,MAAM,GAAG;AACrC,YAAI,KAAK,KAAK,YAAY,KAAK,KAAK,SAAS;AACzC,eAAK,OAAO,MAAM,CAAC,IAAI,cAAc,MAAM,CAAC,IAAI;AAAA,QACpD,OAAO;AACH,eAAK,OAAO,OAAO,KAAK,OAAO,IAAI,MAAM,GAAG,EAAE,CAAC,IAAI;AAAA,QACvD;AAAA,MACJ,OAAO;AACH,aAAK,OAAO,KAAK,KAAK,MAAM;AAAA,MAChC;AAAA,IACJ;AAAA,EACJ,OAAO;AACH,QAAI,KAAK,KAAK,eAAe,KAAK,OAAO,aAAa;AAClD,UAAI;AACJ,UAAI,KAAK,KAAK,aAAa;AACvB,cAAM,QAAQ,KAAK,KAAK,IAAI,MAAM,GAAG;AACrC,YAAI,KAAK,KAAK,WAAW,KAAK,KAAK,UAAU;AACzC,uBAAa,MAAM,CAAC,IAAI,SAAS,MAAM,CAAC,IAAI;AAAA,QAChD,OAAO;AACH,uBAAa,MAAM,MAAM,CAAC,IAAI;AAAA,QAClC;AAAA,MACJ,OAAO;AACH,qBAAa,KAAK,KAAK,MAAM;AAAA,MACjC;AAEA,UAAI;AACJ,UAAI,KAAK,OAAO,aAAa;AACzB,cAAM,QAAQ,KAAK,OAAO,IAAI,MAAM,GAAG;AACvC,YAAI,KAAK,OAAO,YAAY,KAAK,OAAO,SAAS;AAC7C,yBAAe,MAAM,MAAM,CAAC,IAAI,UAAU,MAAM,CAAC,IAAI;AAAA,QACzD,OAAO;AACH,yBAAe,MAAM,MAAM,CAAC,IAAI;AAAA,QACpC;AAAA,MACJ,OAAO;AACH,uBAAe,MAAM,KAAK,OAAO,MAAM;AAAA,MAC3C;AAEA,WAAK,OAAO,aAAa,MAAM;AAC/B,aAAO;AAAA,IACX;AAEA,QAAI,CAAC,KAAK,KAAK,WAAW,CAAC,KAAK,KAAK,UAAU;AAC3C,UAAI,CAAC,KAAK,OAAO,SAAS;AACtB,YAAI,KAAK,OAAO,UAAU;AAEtB,gBAAM,cAAc,KAAK,OAAO,IAAI,MAAM,GAAG;AAC7C,eAAK,OAAO,KAAK,KAAK,IAAI,SAAS,GAAG,GAAG,IAAI,MAAM,YAAY,CAAC,EAAE,SAAS,GAAG,GAAG,IAAI,MAAM,KAAK,KAAK,IAAI,SAAS,GAAG,GAAG,IAAI,MAAM,YAAY,CAAC,EAAE,SAAS,GAAG,GAAG;AAAA,QACpK,OAAO;AAEH,eAAK,OAAO,KAAK,KAAK,IAAI,SAAS,GAAG,GAAG,IAAI,MAAM,KAAK,OAAO,IAAI,SAAS,GAAG,GAAG;AAAA,QACtF;AAAA,MACJ,OAAO;AACH,aAAK,OAAO,KAAK,KAAK,MAAM,QAAQ,KAAK,OAAO,MAAM;AAAA,MAC1D;AAAA,IACJ,OAAO;AACH,WAAK,OAAO,KAAK,KAAK,MAAM,QAAQ,KAAK,OAAO,MAAM;AAAA,IAC1D;AAAA,EACJ;AAEA,SAAO;AACX;AAEA,SAAS,gBAAgB,OAAO;AAE5B,WAAS,QAAQ,CAAC,GAAG,MAAM;AACvB,YAAQ,MAAM,QAAQ,IAAI,IAAI,GAAG,SAAS,CAAC;AAAA,EAC/C,CAAC;AACD,MAAI,MAAM,QAAQ,GAAG,KAAK,GAAG;AACzB,UAAM,QAAQ,MAAM,MAAM,GAAG;AAC7B,WAAO,MAAM,CAAC,MAAM,MAAO,MAAM,MAAM,CAAC,IAAI,MAAQ,MAAM,CAAC,IAAI,SAAS,MAAM,CAAC,IAAI;AAAA,EACvF;AAEA,SAAO,QAAQ;AACnB;AAEA,SAAS,qBAAqB,YAAY;AACtC,MAAI,WAAW,QAAQ,GAAG,KAAK,GAAG;AAC9B,UAAM,QAAQ,WAAW,MAAM,GAAG;AAClC,WAAO,MAAM,CAAC,MAAM,MAAO,MAAM,MAAM,CAAC,IAAI,MAAQ,MAAM,CAAC,IAAI,SAAS,MAAM,CAAC,IAAI;AAAA,EACvF;AACA,SAAO,aAAa;AACxB;AAEA,SAAS,oBAAoB,WAAW;AACpC,eAAa,QAAQ,CAAC,GAAG,MAAM;AAC3B,gBAAY,UAAU,QAAQ,GAAG,EAAE,SAAS,CAAC;AAAA,EACjD,CAAC;AACD,MAAI,UAAU,QAAQ,GAAG,KAAK,GAAG;AAC7B,QAAI,UAAU,QAAQ,GAAG,IAAI,GAAG;AAE5B,aAAO,MAAM,UAAU,MAAM,GAAG,EAAE,IAAI,OAAK,iBAAiB,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,GAAG;AAAA,IACpF;AAEA,WAAO,UAAU,MAAM,GAAG,EAAE,IAAI,OAAK,oBAAoB,CAAC,CAAC,EAAE,KAAK,GAAG;AAAA,EACzE;AAEA,MAAI,UAAU,QAAQ,GAAG,KAAK,GAAG;AAC7B,WAAO,UAAU,MAAM,GAAG,EAAE,IAAI,OAAK,oBAAoB,CAAC,CAAC,EAAE,KAAK,GAAG;AAAA,EACzE;AAEA,SAAO,MAAM,iBAAiB,OAAO,SAAS,CAAC;AACnD;AAEA,IAAM,mBAAmB,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAC3D,IAAM,eAAe,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK,EAAE,IAAI,OAAK,IAAI,OAAO,GAAG,IAAI,CAAC;AACnG,IAAM,WAAW,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK,EAAE,IAAI,OAAK,IAAI,OAAO,GAAG,IAAI,CAAC;", "names": []}