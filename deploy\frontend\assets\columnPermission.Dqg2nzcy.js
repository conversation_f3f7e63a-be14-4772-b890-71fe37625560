import{X as d}from"./index.BHZI5pdK.js";const r=async(n,e,s=[])=>{const t=await n();e.pagination==null&&(e.pagination={show:!0});const i=e.columns,c=["checked","_index","id","create_datetime","update_datetime"].concat(s);return d.eachTree(i,(o,a)=>{!c.includes(String(a))&&a in t.data&&(t.data[a].is_query||(o.column.show=!1,o.column.columnSetDisabled=!0),o.addForm={show:t.data[a].is_create},o.editForm={show:t.data[a].is_update})}),e};export{r as h};
