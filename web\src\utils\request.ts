import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Session } from '/@/utils/storage';
import qs from 'qs';

// 配置新建一个 axios 实例
const service: AxiosInstance = axios.create({
	baseURL: import.meta.env.VITE_API_URL,
	timeout: 120000, // 增加超时时间到2分钟
	headers: { 'Content-Type': 'application/json' },
	paramsSerializer: {
		serialize(params) {
			return qs.stringify(params, { allowDots: true });
		},
	},
});

// 添加请求拦截器
service.interceptors.request.use(
	(config: AxiosRequestConfig) => {
		// 在发送请求之前做些什么 token
		if (Session.get('token')) {
			config.headers!['Authorization'] = `JWT ${Session.get('token')}`;
		}
		console.log('请求配置:', config);
		return config;
	},
	(error) => {
		// 对请求错误做些什么
		console.error('请求拦截器错误:', error);
		return Promise.reject(error);
	}
);

// 添加重试机制
service.interceptors.response.use(undefined, async (err) => {
	const config = err.config;

	// 如果配置了重试，并且请求超时，则进行重试
	if (config && config.retry && (err.code === 'ECONNABORTED' || err.message.includes('timeout'))) {
		// 设置重试次数
		config.__retryCount = config.__retryCount || 0;

		// 检查是否超过重试次数
		if (config.__retryCount < config.retry) {
			config.__retryCount += 1;
			console.log(`请求超时，正在进行第 ${config.__retryCount} 次重试...`);

			// 延迟重试
			await new Promise(resolve => setTimeout(resolve, config.retryDelay || 1000));

			// 重新发送请求
			return service(config);
		}
	}

	return Promise.reject(err);
});

// 添加响应拦截器
service.interceptors.response.use(
	(response) => {
		// 对响应数据做点什么
		const res = response.data;
		if (res.code && res.code !== 0 && res.code !== 200 && res.code !== 2000) {
			// `token` 过期或者账号已在别处登录
			if (res.code === 401 || res.code === 4001) {
				Session.clear(); // 清除浏览器全部临时缓存
				window.location.href = '/'; // 去登录页
				ElMessageBox.alert('你已被登出，请重新登录', '提示', {})
					.then(() => {})
					.catch(() => {});
			}
			return Promise.reject(res);
		} else {
			return response.data;
		}
	},
	(error) => {
		// 对响应错误做点什么
		if (error.message.indexOf('timeout') != -1) {
			ElMessage.error('网络超时');
		} else if (error.message == 'Network Error') {
			ElMessage.error('网络连接错误');
		} else {
			if (error.response.data) ElMessage.error(error.response.statusText);
			else ElMessage.error('接口路径找不到');
		}
		return Promise.reject(error);
	}
);

// 导出 axios 实例
export default service;
export const request = service;
