import{d as K,h as m,c as Z,j as z,k as s,a as _,o as d,m as G,l as o,u as U,e as u,A as C,w as r,F as v,p as V,b as R,D as w,q as x,s as H,Z as Q}from"./vue.BNx9QYep.js";import{R as W,d as Y,e as ee,f as ae}from"./api.CRWbL0zW.js";import{R as le}from"./RoleMenuBtnStores.WLEOorzd.js";import{R as te}from"./RoleMenuTreeStores.BHTvmwPg.js";import{L as g,X as T,e as $}from"./index.BHZI5pdK.js";import{_ as ne}from"./_plugin-vue_export-helper.DlAUqK2U.js";const oe={key:0,class:"pccm-item"},se={class:"menu-form-alert"},de={style:{display:"flex","align-items":"center","white-space":"nowrap","margin-bottom":"10px"}},ue={class:"btn-item"},re=["onClick"],ie={class:"pc-dialog"},ce=K({__name:"RoleMenuBtn",setup(pe){const b=W(),I=te(),f=le(),p=m(!1),t=m({id:0,menu_btn_pre_id:0,isCheck:!1,name:"",data_range:g.get("role_default_data_range"),dept:g.get("role_default_custom_dept")}),n=m({id:0,menu_btn_pre_id:0,isCheck:!1,name:"",data_range:0,dept:[]}),h=m([{label:"仅本人数据权限",value:0},{label:"本部门及以下数据权限",value:1},{label:"本部门数据权限",value:2},{label:"全部数据权限",value:3},{label:"自定数据权限",value:4}]),B={children:"children",label:"name",value:"id"},N=async l=>{t.value.data_range=l,g.set("role_default_data_range",l)},P=async l=>{t.value.dept=l,g.set("role_default_custom_dept",l)},A=async l=>{l<4&&(n.value.dept=[])},E=Z(()=>function(l,a){const c=T.find(h.value,i=>{if(i.value===l)return i.label});return l===t.value.data_range&&(l!==4||JSON.stringify(a)===JSON.stringify(t.value.dept))?"默认接口权限":c.label}),L=async l=>{n.value=t.value;const a={isCheck:l.isCheck,roleId:b.roleId,menuId:I.id,btnId:l.id,data_range:t.value.data_range,dept:t.value.dept},{data:c,msg:i}=await ae(a);f.updateState(c),$({message:i,type:"success"})},O=async()=>{const{data:l,msg:a}=await ee(n.value);n.value=l,p.value=!1,$({message:a,type:"success"})},D=()=>{p.value=!1},F=async l=>{n.value=l,p.value=!0},k=m([]);return z(async()=>{const l=await Y({role:b.roleId,menu_button:n.value.id}),a=T.toArrayTree(l.data,{parentKey:"parent",strict:!1});k.value=a}),(l,a)=>{const c=s("el-option"),i=s("el-select"),M=s("el-tree-select"),J=s("Setting"),X=s("el-icon"),j=s("el-checkbox"),S=s("el-button"),q=s("el-dialog");return d(),_(v,null,[U(f).$state.length>0?(d(),_("div",oe,[u("div",se,[u("div",de,[a[6]||(a[6]=u("span",null,"默认接口权限:",-1)),o(i,{modelValue:t.value.data_range,"onUpdate:modelValue":a[0]||(a[0]=e=>t.value.data_range=e),onChange:N,placeholder:"请选择",style:{"margin-left":"5px",width:"250px","min-width":"250px"}},{default:r(()=>[(d(!0),_(v,null,V(h.value,e=>(d(),R(c,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),C(o(M,{"node-key":"id",modelValue:t.value.dept,"onUpdate:modelValue":a[1]||(a[1]=e=>t.value.dept=e),props:B,data:k.value,onChange:a[2]||(a[2]=e=>P(t.value.dept)),placeholder:"请选择自定义部门",multiple:"","check-strictly":"","render-after-expand":!1,"show-checkbox":"",class:"dialog-tree",style:{"margin-left":"15px",width:"AUTO","min-width":"250px","margin-top":"0"}},null,8,["modelValue","data"]),[[w,t.value.data_range===4]])]),a[7]||(a[7]=u("span",null,"配置操作功能接口权限，配置数据权限点击小齿轮",-1))]),(d(!0),_(v,null,V(U(f).$state,e=>(d(),R(j,{key:e.id,modelValue:e.isCheck,"onUpdate:modelValue":y=>e.isCheck=y,onChange:y=>L(e)},{default:r(()=>[u("div",ue,[x(H(e.data_range!==null?`${e.name}(${E.value(e.data_range,e.dept)})`:e.name)+" ",1),C(u("span",{onClick:Q(y=>F(e),["stop","prevent"])},[o(X,null,{default:r(()=>[o(J)]),_:1})],8,re),[[w,e.isCheck]])])]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]))),128))])):G("",!0),o(q,{modelValue:p.value,"onUpdate:modelValue":a[5]||(a[5]=e=>p.value=e),title:"数据权限配置",width:"400px","close-on-click-modal":!1,"before-close":D},{footer:r(()=>[u("div",null,[o(S,{type:"primary",onClick:O},{default:r(()=>a[8]||(a[8]=[x(" 确定")])),_:1,__:[8]}),o(S,{onClick:D},{default:r(()=>a[9]||(a[9]=[x(" 取消")])),_:1,__:[9]})])]),default:r(()=>[u("div",ie,[o(i,{modelValue:n.value.data_range,"onUpdate:modelValue":a[3]||(a[3]=e=>n.value.data_range=e),onChange:A,placeholder:"请选择"},{default:r(()=>[(d(!0),_(v,null,V(h.value,e=>(d(),R(c,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),C(o(M,{"node-key":"id",modelValue:n.value.dept,"onUpdate:modelValue":a[4]||(a[4]=e=>n.value.dept=e),props:B,data:k.value,multiple:"","check-strictly":"","render-after-expand":!1,"show-checkbox":"",class:"dialog-tree"},null,8,["modelValue","data"]),[[w,n.value.data_range===4]])])]),_:1},8,["modelValue"])],64)}}}),ke=ne(ce,[["__scopeId","data-v-7f9548ed"]]);export{ke as default};
