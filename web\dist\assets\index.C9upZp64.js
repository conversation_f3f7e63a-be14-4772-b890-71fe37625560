import{a as i}from"./index.BHZI5pdK.js";import{G as d,c as f}from"./crud.XirMbZKs.js";import{h as m}from"./columnPermission.Dqg2nzcy.js";import{d as s,j as _,k as o,b as l,o as g,w as C,l as h,x,u as k}from"./vue.BNx9QYep.js";import"./commonCrud.Cpd55lBt.js";const w=s({name:"loginLog"}),j=s({...w,setup(O){const{crudBinding:n,crudRef:r,crudExpose:t,crudOptions:a,resetCrudOptions:c}=i({createCrudOptions:f});return _(async()=>{const e=await m(d,a);c(e),t.doRefresh()}),(e,B)=>{const p=o("fs-crud"),u=o("fs-page");return g(),l(u,null,{default:C(()=>[h(p,x({ref_key:"crudRef",ref:r},k(n)),null,16)]),_:1})}}});export{j as default};
