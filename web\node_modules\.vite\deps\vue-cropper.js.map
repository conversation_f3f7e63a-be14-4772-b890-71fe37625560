{"version": 3, "sources": ["../../.pnpm/vue-cropper@1.1.4/node_modules/vue-cropper/dist/vue-cropper.es.js"], "sourcesContent": ["import { defineComponent as S, openBlock as y, createElementBlock as x, withDirectives as H, createElementVNode as w, normalizeStyle as b, vShow as W, createCommentVNode as O, normalizeClass as I, toDisplayString as X } from \"vue\";\nconst Y = {};\nY.getData = (t) => new Promise((e, i) => {\n  let s = {};\n  L(t).then((r) => {\n    s.arrayBuffer = r;\n    try {\n      s.orientation = N(r);\n    } catch {\n      s.orientation = -1;\n    }\n    e(s);\n  }).catch((r) => {\n    i(r);\n  });\n});\nfunction L(t) {\n  let e = null;\n  return new Promise((i, s) => {\n    if (t.src)\n      if (/^data\\:/i.test(t.src))\n        e = k(t.src), i(e);\n      else if (/^blob\\:/i.test(t.src)) {\n        var r = new FileReader();\n        r.onload = function(h) {\n          e = h.target.result, i(e);\n        }, E(t.src, function(h) {\n          r.readAsArrayBuffer(h);\n        });\n      } else {\n        var o = new XMLHttpRequest();\n        o.onload = function() {\n          if (this.status == 200 || this.status === 0)\n            e = o.response, i(e);\n          else\n            throw \"Could not load image\";\n          o = null;\n        }, o.open(\"GET\", t.src, !0), o.responseType = \"arraybuffer\", o.send(null);\n      }\n    else\n      s(\"img error\");\n  });\n}\nfunction E(t, e) {\n  var i = new XMLHttpRequest();\n  i.open(\"GET\", t, !0), i.responseType = \"blob\", i.onload = function(s) {\n    (this.status == 200 || this.status === 0) && e(this.response);\n  }, i.send();\n}\nfunction k(t, e) {\n  e = e || t.match(/^data\\:([^\\;]+)\\;base64,/mi)[1] || \"\", t = t.replace(/^data\\:([^\\;]+)\\;base64,/gmi, \"\");\n  for (var i = atob(t), s = i.length % 2 == 0 ? i.length : i.length + 1, r = new ArrayBuffer(s), o = new Uint16Array(r), h = 0; h < s; h++)\n    o[h] = i.charCodeAt(h);\n  return r;\n}\nfunction T(t, e, i) {\n  var s = \"\", r;\n  for (r = e, i += e; r < i; r++)\n    s += String.fromCharCode(t.getUint8(r));\n  return s;\n}\nfunction N(t) {\n  var e = new DataView(t), i = e.byteLength, s, r, o, h, a, n, c, l, f, p;\n  if (e.getUint8(0) === 255 && e.getUint8(1) === 216)\n    for (f = 2; f < i; ) {\n      if (e.getUint8(f) === 255 && e.getUint8(f + 1) === 225) {\n        c = f;\n        break;\n      }\n      f++;\n    }\n  if (c && (r = c + 4, o = c + 10, T(e, r, 4) === \"Exif\" && (n = e.getUint16(o), a = n === 18761, (a || n === 19789) && e.getUint16(o + 2, a) === 42 && (h = e.getUint32(o + 4, a), h >= 8 && (l = o + h)))), l) {\n    for (i = e.getUint16(l, a), p = 0; p < i; p++)\n      if (f = l + p * 12 + 2, e.getUint16(f, a) === 274) {\n        f += 8, s = e.getUint16(f, a);\n        break;\n      }\n  }\n  return s;\n}\nconst $ = (t, e) => {\n  const i = t.__vccOpts || t;\n  for (const [s, r] of e)\n    i[s] = r;\n  return i;\n}, z = S({\n  data: function() {\n    return {\n      // 容器高宽\n      w: 0,\n      h: 0,\n      // 图片缩放比例\n      scale: 1,\n      // 图片偏移x轴\n      x: 0,\n      // 图片偏移y轴\n      y: 0,\n      // 图片加载\n      loading: !0,\n      // 图片真实宽度\n      trueWidth: 0,\n      // 图片真实高度\n      trueHeight: 0,\n      move: !0,\n      // 移动的x\n      moveX: 0,\n      // 移动的y\n      moveY: 0,\n      // 开启截图\n      crop: !1,\n      // 正在截图\n      cropping: !1,\n      // 裁剪框大小\n      cropW: 0,\n      cropH: 0,\n      cropOldW: 0,\n      cropOldH: 0,\n      // 判断是否能够改变\n      canChangeX: !1,\n      canChangeY: !1,\n      // 改变的基准点\n      changeCropTypeX: 1,\n      changeCropTypeY: 1,\n      // 裁剪框的坐标轴\n      cropX: 0,\n      cropY: 0,\n      cropChangeX: 0,\n      cropChangeY: 0,\n      cropOffsertX: 0,\n      cropOffsertY: 0,\n      // 支持的滚动事件\n      support: \"\",\n      // 移动端手指缩放\n      touches: [],\n      touchNow: !1,\n      // 图片旋转\n      rotate: 0,\n      isIos: !1,\n      orientation: 0,\n      imgs: \"\",\n      // 图片缩放系数\n      coe: 0.2,\n      // 是否正在多次缩放\n      scaling: !1,\n      scalingSet: \"\",\n      coeStatus: \"\",\n      // 控制emit触发频率\n      isCanShow: !0,\n      // 图片是否等于截图大小\n      imgIsQqualCrop: !1\n    };\n  },\n  props: {\n    img: {\n      type: [String, Blob, null, File],\n      default: \"\"\n    },\n    // 输出图片压缩比\n    outputSize: {\n      type: Number,\n      default: 1\n    },\n    outputType: {\n      type: String,\n      default: \"jpeg\"\n    },\n    info: {\n      type: Boolean,\n      default: !0\n    },\n    // 是否开启滚轮放大缩小\n    canScale: {\n      type: Boolean,\n      default: !0\n    },\n    // 是否自成截图框\n    autoCrop: {\n      type: Boolean,\n      default: !1\n    },\n    autoCropWidth: {\n      type: [Number, String],\n      default: 0\n    },\n    autoCropHeight: {\n      type: [Number, String],\n      default: 0\n    },\n    // 是否开启固定宽高比\n    fixed: {\n      type: Boolean,\n      default: !1\n    },\n    // 宽高比 w/h\n    fixedNumber: {\n      type: Array,\n      default: () => [1, 1]\n    },\n    // 固定大小 禁止改变截图框大小\n    fixedBox: {\n      type: Boolean,\n      default: !1\n    },\n    // 输出截图是否缩放\n    full: {\n      type: Boolean,\n      default: !1\n    },\n    // 是否可以拖动图片\n    canMove: {\n      type: Boolean,\n      default: !0\n    },\n    // 是否可以拖动截图框\n    canMoveBox: {\n      type: Boolean,\n      default: !0\n    },\n    // 上传图片按照原始比例显示\n    original: {\n      type: Boolean,\n      default: !1\n    },\n    // 截图框能否超过图片\n    centerBox: {\n      type: Boolean,\n      default: !1\n    },\n    // 是否根据dpr输出高清图片\n    high: {\n      type: Boolean,\n      default: !0\n    },\n    // 截图框展示宽高类型\n    infoTrue: {\n      type: Boolean,\n      default: !1\n    },\n    // 可以压缩图片宽高  默认不超过200\n    maxImgSize: {\n      type: [Number, String],\n      default: 2e3\n    },\n    // 倍数  可渲染当前截图框的n倍 0 - 1000;\n    enlarge: {\n      type: [Number, String],\n      default: 1\n    },\n    // 自动预览的固定宽度\n    preW: {\n      type: [Number, String],\n      default: 0\n    },\n    /*\n      图片布局方式 mode 实现和css背景一样的效果\n      contain  居中布局 默认不会缩放 保证图片在容器里面 mode: 'contain'\n      cover    拉伸布局 填充整个容器  mode: 'cover'\n      如果仅有一个数值被给定，这个数值将作为宽度值大小，高度值将被设定为auto。 mode: '50px'\n      如果有两个数值被给定，第一个将作为宽度值大小，第二个作为高度值大小。 mode: '50px 60px'\n    */\n    mode: {\n      type: String,\n      default: \"contain\"\n    },\n    //限制最小区域,可传1以上的数字和字符串，限制长宽都是这么大\n    // 也可以传数组[90,90] \n    limitMinSize: {\n      type: [Number, Array, String],\n      default: () => 10,\n      validator: function(t) {\n        return Array.isArray(t) ? Number(t[0]) >= 0 && Number(t[1]) >= 0 : Number(t) >= 0;\n      }\n    },\n    // 导出时,填充背景颜色\n    fillColor: {\n      type: String,\n      default: \"\"\n    }\n  },\n  computed: {\n    cropInfo() {\n      let t = {};\n      if (t.top = this.cropOffsertY > 21 ? \"-21px\" : \"0px\", t.width = this.cropW > 0 ? this.cropW : 0, t.height = this.cropH > 0 ? this.cropH : 0, this.infoTrue) {\n        let e = 1;\n        this.high && !this.full && (e = window.devicePixelRatio), this.enlarge !== 1 & !this.full && (e = Math.abs(Number(this.enlarge))), t.width = t.width * e, t.height = t.height * e, this.full && (t.width = t.width / this.scale, t.height = t.height / this.scale);\n      }\n      return t.width = t.width.toFixed(0), t.height = t.height.toFixed(0), t;\n    },\n    isIE() {\n      return !!window.ActiveXObject || \"ActiveXObject\" in window;\n    },\n    passive() {\n      return this.isIE ? null : {\n        passive: !1\n      };\n    },\n    // 是否处于左右旋转\n    isRotateRightOrLeft() {\n      return [1, -1, 3, -3].includes(this.rotate);\n    }\n  },\n  watch: {\n    // 如果图片改变， 重新布局\n    img() {\n      this.checkedImg();\n    },\n    imgs(t) {\n      t !== \"\" && this.reload();\n    },\n    cropW() {\n      this.showPreview();\n    },\n    cropH() {\n      this.showPreview();\n    },\n    cropOffsertX() {\n      this.showPreview();\n    },\n    cropOffsertY() {\n      this.showPreview();\n    },\n    scale(t, e) {\n      this.showPreview();\n    },\n    x() {\n      this.showPreview();\n    },\n    y() {\n      this.showPreview();\n    },\n    autoCrop(t) {\n      t && this.goAutoCrop();\n    },\n    // 修改了自动截图框\n    autoCropWidth() {\n      this.autoCrop && this.goAutoCrop();\n    },\n    autoCropHeight() {\n      this.autoCrop && this.goAutoCrop();\n    },\n    mode() {\n      this.checkedImg();\n    },\n    rotate() {\n      this.showPreview(), this.autoCrop ? this.goAutoCrop(this.cropW, this.cropH) : (this.cropW > 0 || this.cropH > 0) && this.goAutoCrop(this.cropW, this.cropH);\n    }\n  },\n  methods: {\n    getVersion(t) {\n      var e = navigator.userAgent.split(\" \"), i = \"\";\n      let s = 0;\n      const r = new RegExp(t, \"i\");\n      for (var o = 0; o < e.length; o++)\n        r.test(e[o]) && (i = e[o]);\n      return i ? s = i.split(\"/\")[1].split(\".\") : s = [\"0\", \"0\", \"0\"], s;\n    },\n    checkOrientationImage(t, e, i, s) {\n      if (this.getVersion(\"chrome\")[0] >= 81)\n        e = -1;\n      else if (this.getVersion(\"safari\")[0] >= 605) {\n        const h = this.getVersion(\"version\");\n        h[0] > 13 && h[1] > 1 && (e = -1);\n      } else {\n        const h = navigator.userAgent.toLowerCase().match(/cpu iphone os (.*?) like mac os/);\n        if (h) {\n          let a = h[1];\n          a = a.split(\"_\"), (a[0] > 13 || a[0] >= 13 && a[1] >= 4) && (e = -1);\n        }\n      }\n      let r = document.createElement(\"canvas\"), o = r.getContext(\"2d\");\n      switch (o.save(), e) {\n        case 2:\n          r.width = i, r.height = s, o.translate(i, 0), o.scale(-1, 1);\n          break;\n        case 3:\n          r.width = i, r.height = s, o.translate(i / 2, s / 2), o.rotate(180 * Math.PI / 180), o.translate(-i / 2, -s / 2);\n          break;\n        case 4:\n          r.width = i, r.height = s, o.translate(0, s), o.scale(1, -1);\n          break;\n        case 5:\n          r.height = i, r.width = s, o.rotate(0.5 * Math.PI), o.scale(1, -1);\n          break;\n        case 6:\n          r.width = s, r.height = i, o.translate(s / 2, i / 2), o.rotate(90 * Math.PI / 180), o.translate(-i / 2, -s / 2);\n          break;\n        case 7:\n          r.height = i, r.width = s, o.rotate(0.5 * Math.PI), o.translate(i, -s), o.scale(-1, 1);\n          break;\n        case 8:\n          r.height = i, r.width = s, o.translate(s / 2, i / 2), o.rotate(-90 * Math.PI / 180), o.translate(-i / 2, -s / 2);\n          break;\n        default:\n          r.width = i, r.height = s;\n      }\n      o.drawImage(t, 0, 0, i, s), o.restore(), r.toBlob(\n        (h) => {\n          let a = URL.createObjectURL(h);\n          URL.revokeObjectURL(this.imgs), this.imgs = a;\n        },\n        \"image/\" + this.outputType,\n        1\n      );\n    },\n    // checkout img\n    checkedImg() {\n      if (this.img === null || this.img === \"\") {\n        this.imgs = \"\", this.clearCrop();\n        return;\n      }\n      this.loading = !0, this.scale = 1, this.rotate = 0, this.imgIsQqualCrop = !1, this.clearCrop();\n      let t = new Image();\n      if (t.onload = () => {\n        if (this.img === \"\")\n          return this.$emit(\"img-load\", new Error(\"图片不能为空\")), !1;\n        let i = t.width, s = t.height;\n        Y.getData(t).then((r) => {\n          this.orientation = r.orientation || 1;\n          let o = Number(this.maxImgSize);\n          if (!this.orientation && i < o & s < o) {\n            this.imgs = this.img;\n            return;\n          }\n          i > o && (s = s / i * o, i = o), s > o && (i = i / s * o, s = o), this.checkOrientationImage(t, this.orientation, i, s);\n        }).catch((r) => {\n          this.$emit(\"img-load\", \"error\"), this.$emit(\"img-load-error\", r);\n        });\n      }, t.onerror = (i) => {\n        this.$emit(\"img-load\", \"error\"), this.$emit(\"img-load-error\", i);\n      }, this.img.substr(0, 4) !== \"data\" && (t.crossOrigin = \"\"), this.isIE) {\n        var e = new XMLHttpRequest();\n        e.onload = function() {\n          var i = URL.createObjectURL(this.response);\n          t.src = i;\n        }, e.open(\"GET\", this.img, !0), e.responseType = \"blob\", e.send();\n      } else\n        t.src = this.img;\n    },\n    // 当按下鼠标键\n    startMove(t) {\n      if (t.preventDefault(), this.move && !this.crop) {\n        if (!this.canMove)\n          return !1;\n        this.moveX = (\"clientX\" in t ? t.clientX : t.touches[0].clientX) - this.x, this.moveY = (\"clientY\" in t ? t.clientY : t.touches[0].clientY) - this.y, t.touches ? (window.addEventListener(\"touchmove\", this.moveImg), window.addEventListener(\"touchend\", this.leaveImg), t.touches.length == 2 && (this.touches = t.touches, window.addEventListener(\"touchmove\", this.touchScale), window.addEventListener(\"touchend\", this.cancelTouchScale))) : (window.addEventListener(\"mousemove\", this.moveImg), window.addEventListener(\"mouseup\", this.leaveImg)), this.$emit(\"img-moving\", {\n          moving: !0,\n          axis: this.getImgAxis()\n        });\n      } else\n        this.cropping = !0, window.addEventListener(\"mousemove\", this.createCrop), window.addEventListener(\"mouseup\", this.endCrop), window.addEventListener(\"touchmove\", this.createCrop), window.addEventListener(\"touchend\", this.endCrop), this.cropOffsertX = t.offsetX ? t.offsetX : t.touches[0].pageX - this.$refs.cropper.offsetLeft, this.cropOffsertY = t.offsetY ? t.offsetY : t.touches[0].pageY - this.$refs.cropper.offsetTop, this.cropX = \"clientX\" in t ? t.clientX : t.touches[0].clientX, this.cropY = \"clientY\" in t ? t.clientY : t.touches[0].clientY, this.cropChangeX = this.cropOffsertX, this.cropChangeY = this.cropOffsertY, this.cropW = 0, this.cropH = 0;\n    },\n    // 移动端缩放\n    touchScale(t) {\n      t.preventDefault();\n      let e = this.scale;\n      var i = {\n        x: this.touches[0].clientX,\n        y: this.touches[0].clientY\n      }, s = {\n        x: t.touches[0].clientX,\n        y: t.touches[0].clientY\n      }, r = {\n        x: this.touches[1].clientX,\n        y: this.touches[1].clientY\n      }, o = {\n        x: t.touches[1].clientX,\n        y: t.touches[1].clientY\n      }, h = Math.sqrt(\n        Math.pow(i.x - r.x, 2) + Math.pow(i.y - r.y, 2)\n      ), a = Math.sqrt(\n        Math.pow(s.x - o.x, 2) + Math.pow(s.y - o.y, 2)\n      ), n = a - h, c = 1;\n      c = c / this.trueWidth > c / this.trueHeight ? c / this.trueHeight : c / this.trueWidth, c = c > 0.1 ? 0.1 : c;\n      var l = c * n;\n      if (!this.touchNow) {\n        if (this.touchNow = !0, n > 0 ? e += Math.abs(l) : n < 0 && e > Math.abs(l) && (e -= Math.abs(l)), this.touches = t.touches, setTimeout(() => {\n          this.touchNow = !1;\n        }, 8), !this.checkoutImgAxis(this.x, this.y, e))\n          return !1;\n        this.scale = e;\n      }\n    },\n    cancelTouchScale(t) {\n      window.removeEventListener(\"touchmove\", this.touchScale);\n    },\n    // 移动图片\n    moveImg(t) {\n      if (t.preventDefault(), t.touches && t.touches.length === 2)\n        return this.touches = t.touches, window.addEventListener(\"touchmove\", this.touchScale), window.addEventListener(\"touchend\", this.cancelTouchScale), window.removeEventListener(\"touchmove\", this.moveImg), !1;\n      let e = \"clientX\" in t ? t.clientX : t.touches[0].clientX, i = \"clientY\" in t ? t.clientY : t.touches[0].clientY, s, r;\n      s = e - this.moveX, r = i - this.moveY, this.$nextTick(() => {\n        if (this.centerBox) {\n          let o = this.getImgAxis(s, r, this.scale), h = this.getCropAxis(), a = this.trueHeight * this.scale, n = this.trueWidth * this.scale, c, l, f, p;\n          switch (this.rotate) {\n            case 1:\n            case -1:\n            case 3:\n            case -3:\n              c = this.cropOffsertX - this.trueWidth * (1 - this.scale) / 2 + (a - n) / 2, l = this.cropOffsertY - this.trueHeight * (1 - this.scale) / 2 + (n - a) / 2, f = c - a + this.cropW, p = l - n + this.cropH;\n              break;\n            default:\n              c = this.cropOffsertX - this.trueWidth * (1 - this.scale) / 2, l = this.cropOffsertY - this.trueHeight * (1 - this.scale) / 2, f = c - n + this.cropW, p = l - a + this.cropH;\n              break;\n          }\n          o.x1 >= h.x1 && (s = c), o.y1 >= h.y1 && (r = l), o.x2 <= h.x2 && (s = f), o.y2 <= h.y2 && (r = p);\n        }\n        this.x = s, this.y = r, this.$emit(\"img-moving\", {\n          moving: !0,\n          axis: this.getImgAxis()\n        });\n      });\n    },\n    // 移动图片结束\n    leaveImg(t) {\n      window.removeEventListener(\"mousemove\", this.moveImg), window.removeEventListener(\"touchmove\", this.moveImg), window.removeEventListener(\"mouseup\", this.leaveImg), window.removeEventListener(\"touchend\", this.leaveImg), this.$emit(\"img-moving\", {\n        moving: !1,\n        axis: this.getImgAxis()\n      });\n    },\n    // 缩放图片\n    scaleImg() {\n      this.canScale && window.addEventListener(this.support, this.changeSize, this.passive);\n    },\n    // 移出框\n    cancelScale() {\n      this.canScale && window.removeEventListener(this.support, this.changeSize);\n    },\n    // 改变大小函数\n    changeSize(t) {\n      t.preventDefault();\n      let e = this.scale;\n      var i = t.deltaY || t.wheelDelta, s = navigator.userAgent.indexOf(\"Firefox\");\n      i = s > 0 ? i * 30 : i, this.isIE && (i = -i);\n      var r = this.coe;\n      r = r / this.trueWidth > r / this.trueHeight ? r / this.trueHeight : r / this.trueWidth;\n      var o = r * i;\n      o < 0 ? e += Math.abs(o) : e > Math.abs(o) && (e -= Math.abs(o));\n      let h = o < 0 ? \"add\" : \"reduce\";\n      if (h !== this.coeStatus && (this.coeStatus = h, this.coe = 0.2), this.scaling || (this.scalingSet = setTimeout(() => {\n        this.scaling = !1, this.coe = this.coe += 0.01;\n      }, 50)), this.scaling = !0, !this.checkoutImgAxis(this.x, this.y, e))\n        return !1;\n      this.scale = e;\n    },\n    // 修改图片大小函数\n    changeScale(t) {\n      let e = this.scale;\n      t = t || 1;\n      var i = 20;\n      if (i = i / this.trueWidth > i / this.trueHeight ? i / this.trueHeight : i / this.trueWidth, t = t * i, t > 0 ? e += Math.abs(t) : e > Math.abs(t) && (e -= Math.abs(t)), !this.checkoutImgAxis(this.x, this.y, e))\n        return !1;\n      this.scale = e;\n    },\n    // 创建截图框\n    createCrop(t) {\n      t.preventDefault();\n      var e = \"clientX\" in t ? t.clientX : t.touches ? t.touches[0].clientX : 0, i = \"clientY\" in t ? t.clientY : t.touches ? t.touches[0].clientY : 0;\n      this.$nextTick(() => {\n        var s = e - this.cropX, r = i - this.cropY;\n        if (s > 0 ? (this.cropW = s + this.cropChangeX > this.w ? this.w - this.cropChangeX : s, this.cropOffsertX = this.cropChangeX) : (this.cropW = this.w - this.cropChangeX + Math.abs(s) > this.w ? this.cropChangeX : Math.abs(s), this.cropOffsertX = this.cropChangeX + s > 0 ? this.cropChangeX + s : 0), !this.fixed)\n          r > 0 ? (this.cropH = r + this.cropChangeY > this.h ? this.h - this.cropChangeY : r, this.cropOffsertY = this.cropChangeY) : (this.cropH = this.h - this.cropChangeY + Math.abs(r) > this.h ? this.cropChangeY : Math.abs(r), this.cropOffsertY = this.cropChangeY + r > 0 ? this.cropChangeY + r : 0);\n        else {\n          var o = this.cropW / this.fixedNumber[0] * this.fixedNumber[1];\n          o + this.cropOffsertY > this.h ? (this.cropH = this.h - this.cropOffsertY, this.cropW = this.cropH / this.fixedNumber[1] * this.fixedNumber[0], s > 0 ? this.cropOffsertX = this.cropChangeX : this.cropOffsertX = this.cropChangeX - this.cropW) : this.cropH = o, this.cropOffsertY = this.cropOffsertY;\n        }\n      });\n    },\n    // 改变截图框大小\n    changeCropSize(t, e, i, s, r) {\n      t.preventDefault(), window.addEventListener(\"mousemove\", this.changeCropNow), window.addEventListener(\"mouseup\", this.changeCropEnd), window.addEventListener(\"touchmove\", this.changeCropNow), window.addEventListener(\"touchend\", this.changeCropEnd), this.canChangeX = e, this.canChangeY = i, this.changeCropTypeX = s, this.changeCropTypeY = r, this.cropX = \"clientX\" in t ? t.clientX : t.touches[0].clientX, this.cropY = \"clientY\" in t ? t.clientY : t.touches[0].clientY, this.cropOldW = this.cropW, this.cropOldH = this.cropH, this.cropChangeX = this.cropOffsertX, this.cropChangeY = this.cropOffsertY, this.fixed && this.canChangeX && this.canChangeY && (this.canChangeY = 0), this.$emit(\"change-crop-size\", {\n        width: this.cropW,\n        height: this.cropH\n      });\n    },\n    // 正在改变\n    changeCropNow(t) {\n      t.preventDefault();\n      var e = \"clientX\" in t ? t.clientX : t.touches ? t.touches[0].clientX : 0, i = \"clientY\" in t ? t.clientY : t.touches ? t.touches[0].clientY : 0;\n      let s = this.w, r = this.h, o = 0, h = 0;\n      if (this.centerBox) {\n        let c = this.getImgAxis(), l = c.x2, f = c.y2;\n        o = c.x1 > 0 ? c.x1 : 0, h = c.y1 > 0 ? c.y1 : 0, s > l && (s = l), r > f && (r = f);\n      }\n      const [a, n] = this.checkCropLimitSize();\n      this.$nextTick(() => {\n        var c = e - this.cropX, l = i - this.cropY;\n        if (this.canChangeX && (this.changeCropTypeX === 1 ? this.cropOldW - c < a ? (this.cropW = a, this.cropOffsertX = this.cropOldW + this.cropChangeX - o - a) : this.cropOldW - c > 0 ? (this.cropW = s - this.cropChangeX - c <= s - o ? this.cropOldW - c : this.cropOldW + this.cropChangeX - o, this.cropOffsertX = s - this.cropChangeX - c <= s - o ? this.cropChangeX + c : o) : (this.cropW = Math.abs(c) + this.cropChangeX <= s ? Math.abs(c) - this.cropOldW : s - this.cropOldW - this.cropChangeX, this.cropOffsertX = this.cropChangeX + this.cropOldW) : this.changeCropTypeX === 2 && (this.cropOldW + c < a ? this.cropW = a : this.cropOldW + c > 0 ? (this.cropW = this.cropOldW + c + this.cropOffsertX <= s ? this.cropOldW + c : s - this.cropOffsertX, this.cropOffsertX = this.cropChangeX) : (this.cropW = s - this.cropChangeX + Math.abs(c + this.cropOldW) <= s - o ? Math.abs(c + this.cropOldW) : this.cropChangeX - o, this.cropOffsertX = s - this.cropChangeX + Math.abs(c + this.cropOldW) <= s - o ? this.cropChangeX - Math.abs(c + this.cropOldW) : o))), this.canChangeY && (this.changeCropTypeY === 1 ? this.cropOldH - l < n ? (this.cropH = n, this.cropOffsertY = this.cropOldH + this.cropChangeY - h - n) : this.cropOldH - l > 0 ? (this.cropH = r - this.cropChangeY - l <= r - h ? this.cropOldH - l : this.cropOldH + this.cropChangeY - h, this.cropOffsertY = r - this.cropChangeY - l <= r - h ? this.cropChangeY + l : h) : (this.cropH = Math.abs(l) + this.cropChangeY <= r ? Math.abs(l) - this.cropOldH : r - this.cropOldH - this.cropChangeY, this.cropOffsertY = this.cropChangeY + this.cropOldH) : this.changeCropTypeY === 2 && (this.cropOldH + l < n ? this.cropH = n : this.cropOldH + l > 0 ? (this.cropH = this.cropOldH + l + this.cropOffsertY <= r ? this.cropOldH + l : r - this.cropOffsertY, this.cropOffsertY = this.cropChangeY) : (this.cropH = r - this.cropChangeY + Math.abs(l + this.cropOldH) <= r - h ? Math.abs(l + this.cropOldH) : this.cropChangeY - h, this.cropOffsertY = r - this.cropChangeY + Math.abs(l + this.cropOldH) <= r - h ? this.cropChangeY - Math.abs(l + this.cropOldH) : h))), this.canChangeX && this.fixed) {\n          var f = this.cropW / this.fixedNumber[0] * this.fixedNumber[1];\n          f < n ? (this.cropH = n, this.cropW = this.fixedNumber[0] * n / this.fixedNumber[1], this.changeCropTypeX === 1 && (this.cropOffsertX = this.cropChangeX + (this.cropOldW - this.cropW))) : f + this.cropOffsertY > r ? (this.cropH = r - this.cropOffsertY, this.cropW = this.cropH / this.fixedNumber[1] * this.fixedNumber[0], this.changeCropTypeX === 1 && (this.cropOffsertX = this.cropChangeX + (this.cropOldW - this.cropW))) : this.cropH = f;\n        }\n        if (this.canChangeY && this.fixed) {\n          var p = this.cropH / this.fixedNumber[1] * this.fixedNumber[0];\n          p < a ? (this.cropW = a, this.cropH = this.fixedNumber[1] * a / this.fixedNumber[0], this.cropOffsertY = this.cropOldH + this.cropChangeY - this.cropH) : p + this.cropOffsertX > s ? (this.cropW = s - this.cropOffsertX, this.cropH = this.cropW / this.fixedNumber[0] * this.fixedNumber[1]) : this.cropW = p;\n        }\n      });\n    },\n    checkCropLimitSize() {\n      let { cropW: t, cropH: e, limitMinSize: i } = this, s = new Array();\n      return Array.isArray(i) ? s = i : s = [i, i], t = parseFloat(s[0]), e = parseFloat(s[1]), [t, e];\n    },\n    // 结束改变\n    changeCropEnd(t) {\n      window.removeEventListener(\"mousemove\", this.changeCropNow), window.removeEventListener(\"mouseup\", this.changeCropEnd), window.removeEventListener(\"touchmove\", this.changeCropNow), window.removeEventListener(\"touchend\", this.changeCropEnd);\n    },\n    // 根据比例x/y，最小宽度，最小高度，现有宽度，现有高度，得到应该有的宽度和高度\n    calculateSize(t, e, i, s, r, o) {\n      const h = t / e;\n      let a = r, n = o;\n      return a < i && (a = i, n = Math.ceil(a / h)), n < s && (n = s, a = Math.ceil(n * h), a < i && (a = i, n = Math.ceil(a / h))), a < r && (a = r, n = Math.ceil(a / h)), n < o && (n = o, a = Math.ceil(n * h)), { width: a, height: n };\n    },\n    // 创建完成\n    endCrop() {\n      this.cropW === 0 && this.cropH === 0 && (this.cropping = !1);\n      let [t, e] = this.checkCropLimitSize();\n      const { width: i, height: s } = this.fixed ? this.calculateSize(\n        this.fixedNumber[0],\n        this.fixedNumber[1],\n        t,\n        e,\n        this.cropW,\n        this.cropH\n      ) : { width: t, height: e };\n      i > this.cropW && (this.cropW = i, this.cropOffsertX + i > this.w && (this.cropOffsertX = this.w - i)), s > this.cropH && (this.cropH = s, this.cropOffsertY + s > this.h && (this.cropOffsertY = this.h - s)), window.removeEventListener(\"mousemove\", this.createCrop), window.removeEventListener(\"mouseup\", this.endCrop), window.removeEventListener(\"touchmove\", this.createCrop), window.removeEventListener(\"touchend\", this.endCrop);\n    },\n    // 开始截图\n    startCrop() {\n      this.crop = !0;\n    },\n    // 停止截图\n    stopCrop() {\n      this.crop = !1;\n    },\n    // 清除截图\n    clearCrop() {\n      this.cropping = !1, this.cropW = 0, this.cropH = 0;\n    },\n    // 截图移动\n    cropMove(t) {\n      if (t.preventDefault(), !this.canMoveBox)\n        return this.crop = !1, this.startMove(t), !1;\n      if (t.touches && t.touches.length === 2)\n        return this.crop = !1, this.startMove(t), this.leaveCrop(), !1;\n      window.addEventListener(\"mousemove\", this.moveCrop), window.addEventListener(\"mouseup\", this.leaveCrop), window.addEventListener(\"touchmove\", this.moveCrop), window.addEventListener(\"touchend\", this.leaveCrop);\n      let e = \"clientX\" in t ? t.clientX : t.touches[0].clientX, i = \"clientY\" in t ? t.clientY : t.touches[0].clientY, s, r;\n      s = e - this.cropOffsertX, r = i - this.cropOffsertY, this.cropX = s, this.cropY = r, this.$emit(\"crop-moving\", {\n        moving: !0,\n        axis: this.getCropAxis()\n      });\n    },\n    moveCrop(t, e) {\n      let i = 0, s = 0;\n      t && (t.preventDefault(), i = \"clientX\" in t ? t.clientX : t.touches[0].clientX, s = \"clientY\" in t ? t.clientY : t.touches[0].clientY), this.$nextTick(() => {\n        let r, o, h = i - this.cropX, a = s - this.cropY;\n        if (e && (h = this.cropOffsertX, a = this.cropOffsertY), h <= 0 ? r = 0 : h + this.cropW > this.w ? r = this.w - this.cropW : r = h, a <= 0 ? o = 0 : a + this.cropH > this.h ? o = this.h - this.cropH : o = a, this.centerBox) {\n          let n = this.getImgAxis();\n          r <= n.x1 && (r = n.x1), r + this.cropW > n.x2 && (r = n.x2 - this.cropW), o <= n.y1 && (o = n.y1), o + this.cropH > n.y2 && (o = n.y2 - this.cropH);\n        }\n        this.cropOffsertX = r, this.cropOffsertY = o, this.$emit(\"crop-moving\", {\n          moving: !0,\n          axis: this.getCropAxis()\n        });\n      });\n    },\n    // 算出不同场景下面 图片相对于外层容器的坐标轴\n    getImgAxis(t, e, i) {\n      t = t || this.x, e = e || this.y, i = i || this.scale;\n      let s = {\n        x1: 0,\n        x2: 0,\n        y1: 0,\n        y2: 0\n      }, r = this.trueWidth * i, o = this.trueHeight * i;\n      switch (this.rotate) {\n        case 0:\n          s.x1 = t + this.trueWidth * (1 - i) / 2, s.x2 = s.x1 + this.trueWidth * i, s.y1 = e + this.trueHeight * (1 - i) / 2, s.y2 = s.y1 + this.trueHeight * i;\n          break;\n        case 1:\n        case -1:\n        case 3:\n        case -3:\n          s.x1 = t + this.trueWidth * (1 - i) / 2 + (r - o) / 2, s.x2 = s.x1 + this.trueHeight * i, s.y1 = e + this.trueHeight * (1 - i) / 2 + (o - r) / 2, s.y2 = s.y1 + this.trueWidth * i;\n          break;\n        default:\n          s.x1 = t + this.trueWidth * (1 - i) / 2, s.x2 = s.x1 + this.trueWidth * i, s.y1 = e + this.trueHeight * (1 - i) / 2, s.y2 = s.y1 + this.trueHeight * i;\n          break;\n      }\n      return s;\n    },\n    // 获取截图框的坐标轴\n    getCropAxis() {\n      let t = {\n        x1: 0,\n        x2: 0,\n        y1: 0,\n        y2: 0\n      };\n      return t.x1 = this.cropOffsertX, t.x2 = t.x1 + this.cropW, t.y1 = this.cropOffsertY, t.y2 = t.y1 + this.cropH, t;\n    },\n    leaveCrop(t) {\n      window.removeEventListener(\"mousemove\", this.moveCrop), window.removeEventListener(\"mouseup\", this.leaveCrop), window.removeEventListener(\"touchmove\", this.moveCrop), window.removeEventListener(\"touchend\", this.leaveCrop), this.$emit(\"crop-moving\", {\n        moving: !1,\n        axis: this.getCropAxis()\n      });\n    },\n    getCropChecked(t) {\n      let e = document.createElement(\"canvas\"), i = e.getContext(\"2d\"), s = new Image(), r = this.rotate, o = this.trueWidth, h = this.trueHeight, a = this.cropOffsertX, n = this.cropOffsertY;\n      s.onload = () => {\n        if (this.cropW !== 0) {\n          let p = 1;\n          this.high & !this.full && (p = window.devicePixelRatio), this.enlarge !== 1 & !this.full && (p = Math.abs(Number(this.enlarge)));\n          let d = this.cropW * p, C = this.cropH * p, u = o * this.scale * p, g = h * this.scale * p, m = (this.x - a + this.trueWidth * (1 - this.scale) / 2) * p, v = (this.y - n + this.trueHeight * (1 - this.scale) / 2) * p;\n          switch (f(d, C), i.save(), r) {\n            case 0:\n              this.full ? (f(d / this.scale, C / this.scale), i.drawImage(\n                s,\n                m / this.scale,\n                v / this.scale,\n                u / this.scale,\n                g / this.scale\n              )) : i.drawImage(s, m, v, u, g);\n              break;\n            case 1:\n            case -3:\n              this.full ? (f(d / this.scale, C / this.scale), m = m / this.scale + (u / this.scale - g / this.scale) / 2, v = v / this.scale + (g / this.scale - u / this.scale) / 2, i.rotate(r * 90 * Math.PI / 180), i.drawImage(\n                s,\n                v,\n                -m - g / this.scale,\n                u / this.scale,\n                g / this.scale\n              )) : (m = m + (u - g) / 2, v = v + (g - u) / 2, i.rotate(r * 90 * Math.PI / 180), i.drawImage(s, v, -m - g, u, g));\n              break;\n            case 2:\n            case -2:\n              this.full ? (f(d / this.scale, C / this.scale), i.rotate(r * 90 * Math.PI / 180), m = m / this.scale, v = v / this.scale, i.drawImage(\n                s,\n                -m - u / this.scale,\n                -v - g / this.scale,\n                u / this.scale,\n                g / this.scale\n              )) : (i.rotate(r * 90 * Math.PI / 180), i.drawImage(s, -m - u, -v - g, u, g));\n              break;\n            case 3:\n            case -1:\n              this.full ? (f(d / this.scale, C / this.scale), m = m / this.scale + (u / this.scale - g / this.scale) / 2, v = v / this.scale + (g / this.scale - u / this.scale) / 2, i.rotate(r * 90 * Math.PI / 180), i.drawImage(\n                s,\n                -v - u / this.scale,\n                m,\n                u / this.scale,\n                g / this.scale\n              )) : (m = m + (u - g) / 2, v = v + (g - u) / 2, i.rotate(r * 90 * Math.PI / 180), i.drawImage(s, -v - u, m, u, g));\n              break;\n            default:\n              this.full ? (f(d / this.scale, C / this.scale), i.drawImage(\n                s,\n                m / this.scale,\n                v / this.scale,\n                u / this.scale,\n                g / this.scale\n              )) : i.drawImage(s, m, v, u, g);\n          }\n          i.restore();\n        } else {\n          let p = o * this.scale, d = h * this.scale;\n          switch (i.save(), r) {\n            case 0:\n              f(p, d), i.drawImage(s, 0, 0, p, d);\n              break;\n            case 1:\n            case -3:\n              f(d, p), i.rotate(r * 90 * Math.PI / 180), i.drawImage(s, 0, -d, p, d);\n              break;\n            case 2:\n            case -2:\n              f(p, d), i.rotate(r * 90 * Math.PI / 180), i.drawImage(s, -p, -d, p, d);\n              break;\n            case 3:\n            case -1:\n              f(d, p), i.rotate(r * 90 * Math.PI / 180), i.drawImage(s, -p, 0, p, d);\n              break;\n            default:\n              f(p, d), i.drawImage(s, 0, 0, p, d);\n          }\n          i.restore();\n        }\n        t(e);\n      };\n      var c = this.img.substr(0, 4);\n      c !== \"data\" && (s.crossOrigin = \"Anonymous\"), s.src = this.imgs;\n      const l = this.fillColor;\n      function f(p, d) {\n        e.width = Math.round(p), e.height = Math.round(d), l && (i.fillStyle = l, i.fillRect(0, 0, e.width, e.height));\n      }\n    },\n    // 获取转换成base64 的图片信息\n    getCropData(t) {\n      this.getCropChecked((e) => {\n        t(e.toDataURL(\"image/\" + this.outputType, this.outputSize));\n      });\n    },\n    //canvas获取为blob对象\n    getCropBlob(t) {\n      this.getCropChecked((e) => {\n        e.toBlob(\n          (i) => t(i),\n          \"image/\" + this.outputType,\n          this.outputSize\n        );\n      });\n    },\n    // 自动预览函数\n    showPreview() {\n      if (this.isCanShow)\n        this.isCanShow = !1, setTimeout(() => {\n          this.isCanShow = !0;\n        }, 16);\n      else\n        return !1;\n      let t = this.cropW, e = this.cropH, i = this.scale;\n      var s = {};\n      s.div = {\n        width: `${t}px`,\n        height: `${e}px`\n      };\n      let r = (this.x - this.cropOffsertX) / i, o = (this.y - this.cropOffsertY) / i, h = 0;\n      s.w = t, s.h = e, s.url = this.imgs, s.img = {\n        width: `${this.trueWidth}px`,\n        height: `${this.trueHeight}px`,\n        transform: `scale(${i})translate3d(${r}px, ${o}px, ${h}px)rotateZ(${this.rotate * 90}deg)`\n      }, s.html = `\n      <div class=\"show-preview\" style=\"width: ${s.w}px; height: ${s.h}px,; overflow: hidden\">\n        <div style=\"width: ${t}px; height: ${e}px\">\n          <img src=${s.url} style=\"width: ${this.trueWidth}px; height: ${this.trueHeight}px; transform:\n          scale(${i})translate3d(${r}px, ${o}px, ${h}px)rotateZ(${this.rotate * 90}deg)\">\n        </div>\n      </div>`, this.$emit(\"real-time\", s);\n    },\n    // reload 图片布局函数\n    reload() {\n      let t = new Image();\n      t.onload = () => {\n        this.w = parseFloat(window.getComputedStyle(this.$refs.cropper).width), this.h = parseFloat(window.getComputedStyle(this.$refs.cropper).height), this.trueWidth = t.width, this.trueHeight = t.height, this.original ? this.scale = 1 : this.scale = this.checkedMode(), this.$nextTick(() => {\n          this.x = -(this.trueWidth - this.trueWidth * this.scale) / 2 + (this.w - this.trueWidth * this.scale) / 2, this.y = -(this.trueHeight - this.trueHeight * this.scale) / 2 + (this.h - this.trueHeight * this.scale) / 2, this.loading = !1, this.autoCrop && this.goAutoCrop(), this.$emit(\"img-load\", \"success\"), setTimeout(() => {\n            this.showPreview();\n          }, 20);\n        });\n      }, t.onerror = () => {\n        this.$emit(\"img-load\", \"error\");\n      }, t.src = this.imgs;\n    },\n    // 背景布局的函数\n    checkedMode() {\n      let t = 1, e = this.trueWidth, i = this.trueHeight;\n      const s = this.mode.split(\" \");\n      switch (s[0]) {\n        case \"contain\":\n          this.trueWidth > this.w && (t = this.w / this.trueWidth), this.trueHeight * t > this.h && (t = this.h / this.trueHeight);\n          break;\n        case \"cover\":\n          e = this.w, t = e / this.trueWidth, i = i * t, i < this.h && (i = this.h, t = i / this.trueHeight);\n          break;\n        default:\n          try {\n            let r = s[0];\n            if (r.search(\"px\") !== -1) {\n              r = r.replace(\"px\", \"\"), e = parseFloat(r);\n              const o = e / this.trueWidth;\n              let h = 1, a = s[1];\n              a.search(\"px\") !== -1 && (a = a.replace(\"px\", \"\"), i = parseFloat(a), h = i / this.trueHeight), t = Math.min(o, h);\n            }\n            if (r.search(\"%\") !== -1 && (r = r.replace(\"%\", \"\"), e = parseFloat(r) / 100 * this.w, t = e / this.trueWidth), s.length === 2 && r === \"auto\") {\n              let o = s[1];\n              o.search(\"px\") !== -1 && (o = o.replace(\"px\", \"\"), i = parseFloat(o), t = i / this.trueHeight), o.search(\"%\") !== -1 && (o = o.replace(\"%\", \"\"), i = parseFloat(o) / 100 * this.h, t = i / this.trueHeight);\n            }\n          } catch {\n            t = 1;\n          }\n      }\n      return t;\n    },\n    // 自动截图函数\n    goAutoCrop(t, e) {\n      if (this.imgs === \"\" || this.imgs === null)\n        return;\n      this.clearCrop(), this.cropping = !0;\n      let i = this.w, s = this.h;\n      if (this.centerBox) {\n        const h = Math.abs(this.rotate) % 2 > 0;\n        let a = (h ? this.trueHeight : this.trueWidth) * this.scale, n = (h ? this.trueWidth : this.trueHeight) * this.scale;\n        i = a < i ? a : i, s = n < s ? n : s;\n      }\n      var r = t || parseFloat(this.autoCropWidth), o = e || parseFloat(this.autoCropHeight);\n      (r === 0 || o === 0) && (r = i * 0.8, o = s * 0.8), r = r > i ? i : r, o = o > s ? s : o, this.fixed && (o = r / this.fixedNumber[0] * this.fixedNumber[1]), o > this.h && (o = this.h, r = o / this.fixedNumber[1] * this.fixedNumber[0]), this.changeCrop(r, o);\n    },\n    // 手动改变截图框大小函数\n    changeCrop(t, e) {\n      if (this.centerBox) {\n        let i = this.getImgAxis();\n        t > i.x2 - i.x1 && (t = i.x2 - i.x1, e = t / this.fixedNumber[0] * this.fixedNumber[1]), e > i.y2 - i.y1 && (e = i.y2 - i.y1, t = e / this.fixedNumber[1] * this.fixedNumber[0]);\n      }\n      this.cropW = t, this.cropH = e, this.checkCropLimitSize(), this.$nextTick(() => {\n        this.cropOffsertX = (this.w - this.cropW) / 2, this.cropOffsertY = (this.h - this.cropH) / 2, this.centerBox && this.moveCrop(null, !0);\n      });\n    },\n    // 重置函数， 恢复组件置初始状态\n    refresh() {\n      this.img, this.imgs = \"\", this.scale = 1, this.crop = !1, this.rotate = 0, this.w = 0, this.h = 0, this.trueWidth = 0, this.trueHeight = 0, this.imgIsQqualCrop = !1, this.clearCrop(), this.$nextTick(() => {\n        this.checkedImg();\n      });\n    },\n    // 向左边旋转\n    rotateLeft() {\n      this.rotate = this.rotate <= -3 ? 0 : this.rotate - 1;\n    },\n    // 向右边旋转\n    rotateRight() {\n      this.rotate = this.rotate >= 3 ? 0 : this.rotate + 1;\n    },\n    // 清除旋转\n    rotateClear() {\n      this.rotate = 0;\n    },\n    // 图片坐标点校验\n    checkoutImgAxis(t, e, i) {\n      t = t || this.x, e = e || this.y, i = i || this.scale;\n      let s = !0;\n      if (this.centerBox) {\n        let r = this.getImgAxis(t, e, i), o = this.getCropAxis();\n        r.x1 >= o.x1 && (s = !1), r.x2 <= o.x2 && (s = !1), r.y1 >= o.y1 && (s = !1), r.y2 <= o.y2 && (s = !1), s || this.changeImgScale(r, o, i);\n      }\n      return s;\n    },\n    // 缩放图片，将图片坐标适配截图框坐标\n    changeImgScale(t, e, i) {\n      let s = this.trueWidth, r = this.trueHeight, o = s * i, h = r * i;\n      if (o >= this.cropW && h >= this.cropH)\n        this.scale = i;\n      else {\n        const a = this.cropW / s, n = this.cropH / r, c = this.cropH <= r * a ? a : n;\n        this.scale = c, o = s * c, h = r * c;\n      }\n      this.imgIsQqualCrop || (t.x1 >= e.x1 && (this.isRotateRightOrLeft ? this.x = e.x1 - (s - o) / 2 - (o - h) / 2 : this.x = e.x1 - (s - o) / 2), t.x2 <= e.x2 && (this.isRotateRightOrLeft ? this.x = e.x1 - (s - o) / 2 - (o - h) / 2 - h + this.cropW : this.x = e.x2 - (s - o) / 2 - o), t.y1 >= e.y1 && (this.isRotateRightOrLeft ? this.y = e.y1 - (r - h) / 2 - (h - o) / 2 : this.y = e.y1 - (r - h) / 2), t.y2 <= e.y2 && (this.isRotateRightOrLeft ? this.y = e.y2 - (r - h) / 2 - (h - o) / 2 - o : this.y = e.y2 - (r - h) / 2 - h)), (o < this.cropW || h < this.cropH) && (this.imgIsQqualCrop = !0);\n    }\n  },\n  mounted() {\n    this.support = \"onwheel\" in document.createElement(\"div\") ? \"wheel\" : document.onmousewheel !== void 0 ? \"mousewheel\" : \"DOMMouseScroll\";\n    let t = this;\n    var e = navigator.userAgent;\n    this.isIOS = !!e.match(/\\(i[^;]+;( U;)? CPU.+Mac OS X/), HTMLCanvasElement.prototype.toBlob || Object.defineProperty(HTMLCanvasElement.prototype, \"toBlob\", {\n      value: function(i, s, r) {\n        for (var o = atob(this.toDataURL(s, r).split(\",\")[1]), h = o.length, a = new Uint8Array(h), n = 0; n < h; n++)\n          a[n] = o.charCodeAt(n);\n        i(new Blob([a], { type: t.type || \"image/png\" }));\n      }\n    }), this.showPreview(), this.checkedImg();\n  },\n  unmounted() {\n    window.removeEventListener(\"mousemove\", this.moveCrop), window.removeEventListener(\"mouseup\", this.leaveCrop), window.removeEventListener(\"touchmove\", this.moveCrop), window.removeEventListener(\"touchend\", this.leaveCrop), this.cancelScale();\n  }\n}), A = {\n  key: 0,\n  class: \"cropper-box\"\n}, B = [\"src\"], P = { class: \"cropper-view-box\" }, R = [\"src\"], D = { key: 1 };\nfunction U(t, e, i, s, r, o) {\n  return y(), x(\"div\", {\n    class: \"vue-cropper\",\n    ref: \"cropper\",\n    onMouseover: e[28] || (e[28] = (...h) => t.scaleImg && t.scaleImg(...h)),\n    onMouseout: e[29] || (e[29] = (...h) => t.cancelScale && t.cancelScale(...h))\n  }, [\n    t.imgs ? (y(), x(\"div\", A, [\n      H(w(\"div\", {\n        class: \"cropper-box-canvas\",\n        style: b({\n          width: t.trueWidth + \"px\",\n          height: t.trueHeight + \"px\",\n          transform: \"scale(\" + t.scale + \",\" + t.scale + \") translate3d(\" + t.x / t.scale + \"px,\" + t.y / t.scale + \"px,0)rotateZ(\" + t.rotate * 90 + \"deg)\"\n        })\n      }, [\n        w(\"img\", {\n          src: t.imgs,\n          alt: \"cropper-img\",\n          ref: \"cropperImg\"\n        }, null, 8, B)\n      ], 4), [\n        [W, !t.loading]\n      ])\n    ])) : O(\"\", !0),\n    w(\"div\", {\n      class: I([\"cropper-drag-box\", { \"cropper-move\": t.move && !t.crop, \"cropper-crop\": t.crop, \"cropper-modal\": t.cropping }]),\n      onMousedown: e[0] || (e[0] = (...h) => t.startMove && t.startMove(...h)),\n      onTouchstart: e[1] || (e[1] = (...h) => t.startMove && t.startMove(...h))\n    }, null, 34),\n    H(w(\"div\", {\n      class: \"cropper-crop-box\",\n      style: b({\n        width: t.cropW + \"px\",\n        height: t.cropH + \"px\",\n        transform: \"translate3d(\" + t.cropOffsertX + \"px,\" + t.cropOffsertY + \"px,0)\"\n      })\n    }, [\n      w(\"span\", P, [\n        w(\"img\", {\n          style: b({\n            width: t.trueWidth + \"px\",\n            height: t.trueHeight + \"px\",\n            transform: \"scale(\" + t.scale + \",\" + t.scale + \") translate3d(\" + (t.x - t.cropOffsertX) / t.scale + \"px,\" + (t.y - t.cropOffsertY) / t.scale + \"px,0)rotateZ(\" + t.rotate * 90 + \"deg)\"\n          }),\n          src: t.imgs,\n          alt: \"cropper-img\"\n        }, null, 12, R)\n      ]),\n      w(\"span\", {\n        class: \"cropper-face cropper-move\",\n        onMousedown: e[2] || (e[2] = (...h) => t.cropMove && t.cropMove(...h)),\n        onTouchstart: e[3] || (e[3] = (...h) => t.cropMove && t.cropMove(...h))\n      }, null, 32),\n      t.info ? (y(), x(\"span\", {\n        key: 0,\n        class: \"crop-info\",\n        style: b({ top: t.cropInfo.top })\n      }, X(t.cropInfo.width) + \" × \" + X(t.cropInfo.height), 5)) : O(\"\", !0),\n      t.fixedBox ? O(\"\", !0) : (y(), x(\"span\", D, [\n        w(\"span\", {\n          class: \"crop-line line-w\",\n          onMousedown: e[4] || (e[4] = (h) => t.changeCropSize(h, !1, !0, 0, 1)),\n          onTouchstart: e[5] || (e[5] = (h) => t.changeCropSize(h, !1, !0, 0, 1))\n        }, null, 32),\n        w(\"span\", {\n          class: \"crop-line line-a\",\n          onMousedown: e[6] || (e[6] = (h) => t.changeCropSize(h, !0, !1, 1, 0)),\n          onTouchstart: e[7] || (e[7] = (h) => t.changeCropSize(h, !0, !1, 1, 0))\n        }, null, 32),\n        w(\"span\", {\n          class: \"crop-line line-s\",\n          onMousedown: e[8] || (e[8] = (h) => t.changeCropSize(h, !1, !0, 0, 2)),\n          onTouchstart: e[9] || (e[9] = (h) => t.changeCropSize(h, !1, !0, 0, 2))\n        }, null, 32),\n        w(\"span\", {\n          class: \"crop-line line-d\",\n          onMousedown: e[10] || (e[10] = (h) => t.changeCropSize(h, !0, !1, 2, 0)),\n          onTouchstart: e[11] || (e[11] = (h) => t.changeCropSize(h, !0, !1, 2, 0))\n        }, null, 32),\n        w(\"span\", {\n          class: \"crop-point point1\",\n          onMousedown: e[12] || (e[12] = (h) => t.changeCropSize(h, !0, !0, 1, 1)),\n          onTouchstart: e[13] || (e[13] = (h) => t.changeCropSize(h, !0, !0, 1, 1))\n        }, null, 32),\n        w(\"span\", {\n          class: \"crop-point point2\",\n          onMousedown: e[14] || (e[14] = (h) => t.changeCropSize(h, !1, !0, 0, 1)),\n          onTouchstart: e[15] || (e[15] = (h) => t.changeCropSize(h, !1, !0, 0, 1))\n        }, null, 32),\n        w(\"span\", {\n          class: \"crop-point point3\",\n          onMousedown: e[16] || (e[16] = (h) => t.changeCropSize(h, !0, !0, 2, 1)),\n          onTouchstart: e[17] || (e[17] = (h) => t.changeCropSize(h, !0, !0, 2, 1))\n        }, null, 32),\n        w(\"span\", {\n          class: \"crop-point point4\",\n          onMousedown: e[18] || (e[18] = (h) => t.changeCropSize(h, !0, !1, 1, 0)),\n          onTouchstart: e[19] || (e[19] = (h) => t.changeCropSize(h, !0, !1, 1, 0))\n        }, null, 32),\n        w(\"span\", {\n          class: \"crop-point point5\",\n          onMousedown: e[20] || (e[20] = (h) => t.changeCropSize(h, !0, !1, 2, 0)),\n          onTouchstart: e[21] || (e[21] = (h) => t.changeCropSize(h, !0, !1, 2, 0))\n        }, null, 32),\n        w(\"span\", {\n          class: \"crop-point point6\",\n          onMousedown: e[22] || (e[22] = (h) => t.changeCropSize(h, !0, !0, 1, 2)),\n          onTouchstart: e[23] || (e[23] = (h) => t.changeCropSize(h, !0, !0, 1, 2))\n        }, null, 32),\n        w(\"span\", {\n          class: \"crop-point point7\",\n          onMousedown: e[24] || (e[24] = (h) => t.changeCropSize(h, !1, !0, 0, 2)),\n          onTouchstart: e[25] || (e[25] = (h) => t.changeCropSize(h, !1, !0, 0, 2))\n        }, null, 32),\n        w(\"span\", {\n          class: \"crop-point point8\",\n          onMousedown: e[26] || (e[26] = (h) => t.changeCropSize(h, !0, !0, 2, 2)),\n          onTouchstart: e[27] || (e[27] = (h) => t.changeCropSize(h, !0, !0, 2, 2))\n        }, null, 32)\n      ]))\n    ], 4), [\n      [W, t.cropping]\n    ])\n  ], 544);\n}\nconst M = /* @__PURE__ */ $(z, [[\"render\", U], [\"__scopeId\", \"data-v-a742df44\"]]), F = function(t) {\n  t.component(\"VueCropper\", M);\n}, V = {\n  version: \"1.1.4\",\n  install: F,\n  VueCropper: M\n};\nexport {\n  M as VueCropper,\n  V as default,\n  V as globalCropper\n};\n"], "mappings": ";;;;;;;;;;;;;;;;AACA,IAAM,IAAI,CAAC;AACX,EAAE,UAAU,CAAC,MAAM,IAAI,QAAQ,CAAC,GAAG,MAAM;AACvC,MAAI,IAAI,CAAC;AACT,IAAE,CAAC,EAAE,KAAK,CAAC,MAAM;AACf,MAAE,cAAc;AAChB,QAAI;AACF,QAAE,cAAc,EAAE,CAAC;AAAA,IACrB,QAAQ;AACN,QAAE,cAAc;AAAA,IAClB;AACA,MAAE,CAAC;AAAA,EACL,CAAC,EAAE,MAAM,CAAC,MAAM;AACd,MAAE,CAAC;AAAA,EACL,CAAC;AACH,CAAC;AACD,SAAS,EAAE,GAAG;AACZ,MAAI,IAAI;AACR,SAAO,IAAI,QAAQ,CAAC,GAAG,MAAM;AAC3B,QAAI,EAAE;AACJ,UAAI,WAAW,KAAK,EAAE,GAAG;AACvB,YAAI,EAAE,EAAE,GAAG,GAAG,EAAE,CAAC;AAAA,eACV,WAAW,KAAK,EAAE,GAAG,GAAG;AAC/B,YAAI,IAAI,IAAI,WAAW;AACvB,UAAE,SAAS,SAAS,GAAG;AACrB,cAAI,EAAE,OAAO,QAAQ,EAAE,CAAC;AAAA,QAC1B,GAAG,EAAE,EAAE,KAAK,SAAS,GAAG;AACtB,YAAE,kBAAkB,CAAC;AAAA,QACvB,CAAC;AAAA,MACH,OAAO;AACL,YAAI,IAAI,IAAI,eAAe;AAC3B,UAAE,SAAS,WAAW;AACpB,cAAI,KAAK,UAAU,OAAO,KAAK,WAAW;AACxC,gBAAI,EAAE,UAAU,EAAE,CAAC;AAAA;AAEnB,kBAAM;AACR,cAAI;AAAA,QACN,GAAG,EAAE,KAAK,OAAO,EAAE,KAAK,IAAE,GAAG,EAAE,eAAe,eAAe,EAAE,KAAK,IAAI;AAAA,MAC1E;AAAA;AAEA,QAAE,WAAW;AAAA,EACjB,CAAC;AACH;AACA,SAAS,EAAE,GAAG,GAAG;AACf,MAAI,IAAI,IAAI,eAAe;AAC3B,IAAE,KAAK,OAAO,GAAG,IAAE,GAAG,EAAE,eAAe,QAAQ,EAAE,SAAS,SAAS,GAAG;AACpE,KAAC,KAAK,UAAU,OAAO,KAAK,WAAW,MAAM,EAAE,KAAK,QAAQ;AAAA,EAC9D,GAAG,EAAE,KAAK;AACZ;AACA,SAAS,EAAE,GAAG,GAAG;AACf,MAAI,KAAK,EAAE,MAAM,4BAA4B,EAAE,CAAC,KAAK,IAAI,IAAI,EAAE,QAAQ,+BAA+B,EAAE;AACxG,WAAS,IAAI,KAAK,CAAC,GAAG,IAAI,EAAE,SAAS,KAAK,IAAI,EAAE,SAAS,EAAE,SAAS,GAAG,IAAI,IAAI,YAAY,CAAC,GAAG,IAAI,IAAI,YAAY,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG;AACnI,MAAE,CAAC,IAAI,EAAE,WAAW,CAAC;AACvB,SAAO;AACT;AACA,SAAS,EAAE,GAAG,GAAG,GAAG;AAClB,MAAI,IAAI,IAAI;AACZ,OAAK,IAAI,GAAG,KAAK,GAAG,IAAI,GAAG;AACzB,SAAK,OAAO,aAAa,EAAE,SAAS,CAAC,CAAC;AACxC,SAAO;AACT;AACA,SAAS,EAAE,GAAG;AACZ,MAAI,IAAI,IAAI,SAAS,CAAC,GAAG,IAAI,EAAE,YAAY,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AACtE,MAAI,EAAE,SAAS,CAAC,MAAM,OAAO,EAAE,SAAS,CAAC,MAAM;AAC7C,SAAK,IAAI,GAAG,IAAI,KAAK;AACnB,UAAI,EAAE,SAAS,CAAC,MAAM,OAAO,EAAE,SAAS,IAAI,CAAC,MAAM,KAAK;AACtD,YAAI;AACJ;AAAA,MACF;AACA;AAAA,IACF;AACF,MAAI,MAAM,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,EAAE,GAAG,GAAG,CAAC,MAAM,WAAW,IAAI,EAAE,UAAU,CAAC,GAAG,IAAI,MAAM,QAAQ,KAAK,MAAM,UAAU,EAAE,UAAU,IAAI,GAAG,CAAC,MAAM,OAAO,IAAI,EAAE,UAAU,IAAI,GAAG,CAAC,GAAG,KAAK,MAAM,IAAI,IAAI,OAAO,GAAG;AAC7M,SAAK,IAAI,EAAE,UAAU,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG;AACxC,UAAI,IAAI,IAAI,IAAI,KAAK,GAAG,EAAE,UAAU,GAAG,CAAC,MAAM,KAAK;AACjD,aAAK,GAAG,IAAI,EAAE,UAAU,GAAG,CAAC;AAC5B;AAAA,MACF;AAAA,EACJ;AACA,SAAO;AACT;AACA,IAAM,IAAI,CAAC,GAAG,MAAM;AAClB,QAAM,IAAI,EAAE,aAAa;AACzB,aAAW,CAAC,GAAG,CAAC,KAAK;AACnB,MAAE,CAAC,IAAI;AACT,SAAO;AACT;AALA,IAKG,IAAI,gBAAE;AAAA,EACP,MAAM,WAAW;AACf,WAAO;AAAA;AAAA,MAEL,GAAG;AAAA,MACH,GAAG;AAAA;AAAA,MAEH,OAAO;AAAA;AAAA,MAEP,GAAG;AAAA;AAAA,MAEH,GAAG;AAAA;AAAA,MAEH,SAAS;AAAA;AAAA,MAET,WAAW;AAAA;AAAA,MAEX,YAAY;AAAA,MACZ,MAAM;AAAA;AAAA,MAEN,OAAO;AAAA;AAAA,MAEP,OAAO;AAAA;AAAA,MAEP,MAAM;AAAA;AAAA,MAEN,UAAU;AAAA;AAAA,MAEV,OAAO;AAAA,MACP,OAAO;AAAA,MACP,UAAU;AAAA,MACV,UAAU;AAAA;AAAA,MAEV,YAAY;AAAA,MACZ,YAAY;AAAA;AAAA,MAEZ,iBAAiB;AAAA,MACjB,iBAAiB;AAAA;AAAA,MAEjB,OAAO;AAAA,MACP,OAAO;AAAA,MACP,aAAa;AAAA,MACb,aAAa;AAAA,MACb,cAAc;AAAA,MACd,cAAc;AAAA;AAAA,MAEd,SAAS;AAAA;AAAA,MAET,SAAS,CAAC;AAAA,MACV,UAAU;AAAA;AAAA,MAEV,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,aAAa;AAAA,MACb,MAAM;AAAA;AAAA,MAEN,KAAK;AAAA;AAAA,MAEL,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,WAAW;AAAA;AAAA,MAEX,WAAW;AAAA;AAAA,MAEX,gBAAgB;AAAA,IAClB;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,KAAK;AAAA,MACH,MAAM,CAAC,QAAQ,MAAM,MAAM,IAAI;AAAA,MAC/B,SAAS;AAAA,IACX;AAAA;AAAA,IAEA,YAAY;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,YAAY;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA;AAAA,IAEA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA;AAAA,IAEA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,eAAe;AAAA,MACb,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS;AAAA,IACX;AAAA,IACA,gBAAgB;AAAA,MACd,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS;AAAA,IACX;AAAA;AAAA,IAEA,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA;AAAA,IAEA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,SAAS,MAAM,CAAC,GAAG,CAAC;AAAA,IACtB;AAAA;AAAA,IAEA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA;AAAA,IAEA,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA;AAAA,IAEA,SAAS;AAAA,MACP,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA;AAAA,IAEA,YAAY;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA;AAAA,IAEA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA;AAAA,IAEA,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA;AAAA,IAEA,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA;AAAA,IAEA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA;AAAA,IAEA,YAAY;AAAA,MACV,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS;AAAA,IACX;AAAA;AAAA,IAEA,SAAS;AAAA,MACP,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS;AAAA,IACX;AAAA;AAAA,IAEA,MAAM;AAAA,MACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS;AAAA,IACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQA,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA;AAAA;AAAA,IAGA,cAAc;AAAA,MACZ,MAAM,CAAC,QAAQ,OAAO,MAAM;AAAA,MAC5B,SAAS,MAAM;AAAA,MACf,WAAW,SAAS,GAAG;AACrB,eAAO,MAAM,QAAQ,CAAC,IAAI,OAAO,EAAE,CAAC,CAAC,KAAK,KAAK,OAAO,EAAE,CAAC,CAAC,KAAK,IAAI,OAAO,CAAC,KAAK;AAAA,MAClF;AAAA,IACF;AAAA;AAAA,IAEA,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,WAAW;AACT,UAAI,IAAI,CAAC;AACT,UAAI,EAAE,MAAM,KAAK,eAAe,KAAK,UAAU,OAAO,EAAE,QAAQ,KAAK,QAAQ,IAAI,KAAK,QAAQ,GAAG,EAAE,SAAS,KAAK,QAAQ,IAAI,KAAK,QAAQ,GAAG,KAAK,UAAU;AAC1J,YAAI,IAAI;AACR,aAAK,QAAQ,CAAC,KAAK,SAAS,IAAI,OAAO,mBAAmB,KAAK,YAAY,IAAI,CAAC,KAAK,SAAS,IAAI,KAAK,IAAI,OAAO,KAAK,OAAO,CAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,GAAG,EAAE,SAAS,EAAE,SAAS,GAAG,KAAK,SAAS,EAAE,QAAQ,EAAE,QAAQ,KAAK,OAAO,EAAE,SAAS,EAAE,SAAS,KAAK;AAAA,MAC9P;AACA,aAAO,EAAE,QAAQ,EAAE,MAAM,QAAQ,CAAC,GAAG,EAAE,SAAS,EAAE,OAAO,QAAQ,CAAC,GAAG;AAAA,IACvE;AAAA,IACA,OAAO;AACL,aAAO,CAAC,CAAC,OAAO,iBAAiB,mBAAmB;AAAA,IACtD;AAAA,IACA,UAAU;AACR,aAAO,KAAK,OAAO,OAAO;AAAA,QACxB,SAAS;AAAA,MACX;AAAA,IACF;AAAA;AAAA,IAEA,sBAAsB;AACpB,aAAO,CAAC,GAAG,IAAI,GAAG,EAAE,EAAE,SAAS,KAAK,MAAM;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,OAAO;AAAA;AAAA,IAEL,MAAM;AACJ,WAAK,WAAW;AAAA,IAClB;AAAA,IACA,KAAK,GAAG;AACN,YAAM,MAAM,KAAK,OAAO;AAAA,IAC1B;AAAA,IACA,QAAQ;AACN,WAAK,YAAY;AAAA,IACnB;AAAA,IACA,QAAQ;AACN,WAAK,YAAY;AAAA,IACnB;AAAA,IACA,eAAe;AACb,WAAK,YAAY;AAAA,IACnB;AAAA,IACA,eAAe;AACb,WAAK,YAAY;AAAA,IACnB;AAAA,IACA,MAAM,GAAG,GAAG;AACV,WAAK,YAAY;AAAA,IACnB;AAAA,IACA,IAAI;AACF,WAAK,YAAY;AAAA,IACnB;AAAA,IACA,IAAI;AACF,WAAK,YAAY;AAAA,IACnB;AAAA,IACA,SAAS,GAAG;AACV,WAAK,KAAK,WAAW;AAAA,IACvB;AAAA;AAAA,IAEA,gBAAgB;AACd,WAAK,YAAY,KAAK,WAAW;AAAA,IACnC;AAAA,IACA,iBAAiB;AACf,WAAK,YAAY,KAAK,WAAW;AAAA,IACnC;AAAA,IACA,OAAO;AACL,WAAK,WAAW;AAAA,IAClB;AAAA,IACA,SAAS;AACP,WAAK,YAAY,GAAG,KAAK,WAAW,KAAK,WAAW,KAAK,OAAO,KAAK,KAAK,KAAK,KAAK,QAAQ,KAAK,KAAK,QAAQ,MAAM,KAAK,WAAW,KAAK,OAAO,KAAK,KAAK;AAAA,IAC5J;AAAA,EACF;AAAA,EACA,SAAS;AAAA,IACP,WAAW,GAAG;AACZ,UAAI,IAAI,UAAU,UAAU,MAAM,GAAG,GAAG,IAAI;AAC5C,UAAI,IAAI;AACR,YAAM,IAAI,IAAI,OAAO,GAAG,GAAG;AAC3B,eAAS,IAAI,GAAG,IAAI,EAAE,QAAQ;AAC5B,UAAE,KAAK,EAAE,CAAC,CAAC,MAAM,IAAI,EAAE,CAAC;AAC1B,aAAO,IAAI,IAAI,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,IAAI,IAAI,CAAC,KAAK,KAAK,GAAG,GAAG;AAAA,IACnE;AAAA,IACA,sBAAsB,GAAG,GAAG,GAAG,GAAG;AAChC,UAAI,KAAK,WAAW,QAAQ,EAAE,CAAC,KAAK;AAClC,YAAI;AAAA,eACG,KAAK,WAAW,QAAQ,EAAE,CAAC,KAAK,KAAK;AAC5C,cAAM,IAAI,KAAK,WAAW,SAAS;AACnC,UAAE,CAAC,IAAI,MAAM,EAAE,CAAC,IAAI,MAAM,IAAI;AAAA,MAChC,OAAO;AACL,cAAM,IAAI,UAAU,UAAU,YAAY,EAAE,MAAM,iCAAiC;AACnF,YAAI,GAAG;AACL,cAAI,IAAI,EAAE,CAAC;AACX,cAAI,EAAE,MAAM,GAAG,IAAI,EAAE,CAAC,IAAI,MAAM,EAAE,CAAC,KAAK,MAAM,EAAE,CAAC,KAAK,OAAO,IAAI;AAAA,QACnE;AAAA,MACF;AACA,UAAI,IAAI,SAAS,cAAc,QAAQ,GAAG,IAAI,EAAE,WAAW,IAAI;AAC/D,cAAQ,EAAE,KAAK,GAAG,GAAG;AAAA,QACnB,KAAK;AACH,YAAE,QAAQ,GAAG,EAAE,SAAS,GAAG,EAAE,UAAU,GAAG,CAAC,GAAG,EAAE,MAAM,IAAI,CAAC;AAC3D;AAAA,QACF,KAAK;AACH,YAAE,QAAQ,GAAG,EAAE,SAAS,GAAG,EAAE,UAAU,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,OAAO,MAAM,KAAK,KAAK,GAAG,GAAG,EAAE,UAAU,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC;AAC/G;AAAA,QACF,KAAK;AACH,YAAE,QAAQ,GAAG,EAAE,SAAS,GAAG,EAAE,UAAU,GAAG,CAAC,GAAG,EAAE,MAAM,GAAG,EAAE;AAC3D;AAAA,QACF,KAAK;AACH,YAAE,SAAS,GAAG,EAAE,QAAQ,GAAG,EAAE,OAAO,MAAM,KAAK,EAAE,GAAG,EAAE,MAAM,GAAG,EAAE;AACjE;AAAA,QACF,KAAK;AACH,YAAE,QAAQ,GAAG,EAAE,SAAS,GAAG,EAAE,UAAU,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,OAAO,KAAK,KAAK,KAAK,GAAG,GAAG,EAAE,UAAU,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC;AAC9G;AAAA,QACF,KAAK;AACH,YAAE,SAAS,GAAG,EAAE,QAAQ,GAAG,EAAE,OAAO,MAAM,KAAK,EAAE,GAAG,EAAE,UAAU,GAAG,CAAC,CAAC,GAAG,EAAE,MAAM,IAAI,CAAC;AACrF;AAAA,QACF,KAAK;AACH,YAAE,SAAS,GAAG,EAAE,QAAQ,GAAG,EAAE,UAAU,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,OAAO,MAAM,KAAK,KAAK,GAAG,GAAG,EAAE,UAAU,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC;AAC/G;AAAA,QACF;AACE,YAAE,QAAQ,GAAG,EAAE,SAAS;AAAA,MAC5B;AACA,QAAE,UAAU,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,EAAE,QAAQ,GAAG,EAAE;AAAA,QACzC,CAAC,MAAM;AACL,cAAI,IAAI,IAAI,gBAAgB,CAAC;AAC7B,cAAI,gBAAgB,KAAK,IAAI,GAAG,KAAK,OAAO;AAAA,QAC9C;AAAA,QACA,WAAW,KAAK;AAAA,QAChB;AAAA,MACF;AAAA,IACF;AAAA;AAAA,IAEA,aAAa;AACX,UAAI,KAAK,QAAQ,QAAQ,KAAK,QAAQ,IAAI;AACxC,aAAK,OAAO,IAAI,KAAK,UAAU;AAC/B;AAAA,MACF;AACA,WAAK,UAAU,MAAI,KAAK,QAAQ,GAAG,KAAK,SAAS,GAAG,KAAK,iBAAiB,OAAI,KAAK,UAAU;AAC7F,UAAI,IAAI,IAAI,MAAM;AAClB,UAAI,EAAE,SAAS,MAAM;AACnB,YAAI,KAAK,QAAQ;AACf,iBAAO,KAAK,MAAM,YAAY,IAAI,MAAM,QAAQ,CAAC,GAAG;AACtD,YAAI,IAAI,EAAE,OAAO,IAAI,EAAE;AACvB,UAAE,QAAQ,CAAC,EAAE,KAAK,CAAC,MAAM;AACvB,eAAK,cAAc,EAAE,eAAe;AACpC,cAAI,IAAI,OAAO,KAAK,UAAU;AAC9B,cAAI,CAAC,KAAK,eAAe,IAAI,IAAI,IAAI,GAAG;AACtC,iBAAK,OAAO,KAAK;AACjB;AAAA,UACF;AACA,cAAI,MAAM,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,KAAK,sBAAsB,GAAG,KAAK,aAAa,GAAG,CAAC;AAAA,QACxH,CAAC,EAAE,MAAM,CAAC,MAAM;AACd,eAAK,MAAM,YAAY,OAAO,GAAG,KAAK,MAAM,kBAAkB,CAAC;AAAA,QACjE,CAAC;AAAA,MACH,GAAG,EAAE,UAAU,CAAC,MAAM;AACpB,aAAK,MAAM,YAAY,OAAO,GAAG,KAAK,MAAM,kBAAkB,CAAC;AAAA,MACjE,GAAG,KAAK,IAAI,OAAO,GAAG,CAAC,MAAM,WAAW,EAAE,cAAc,KAAK,KAAK,MAAM;AACtE,YAAI,IAAI,IAAI,eAAe;AAC3B,UAAE,SAAS,WAAW;AACpB,cAAI,IAAI,IAAI,gBAAgB,KAAK,QAAQ;AACzC,YAAE,MAAM;AAAA,QACV,GAAG,EAAE,KAAK,OAAO,KAAK,KAAK,IAAE,GAAG,EAAE,eAAe,QAAQ,EAAE,KAAK;AAAA,MAClE;AACE,UAAE,MAAM,KAAK;AAAA,IACjB;AAAA;AAAA,IAEA,UAAU,GAAG;AACX,UAAI,EAAE,eAAe,GAAG,KAAK,QAAQ,CAAC,KAAK,MAAM;AAC/C,YAAI,CAAC,KAAK;AACR,iBAAO;AACT,aAAK,SAAS,aAAa,IAAI,EAAE,UAAU,EAAE,QAAQ,CAAC,EAAE,WAAW,KAAK,GAAG,KAAK,SAAS,aAAa,IAAI,EAAE,UAAU,EAAE,QAAQ,CAAC,EAAE,WAAW,KAAK,GAAG,EAAE,WAAW,OAAO,iBAAiB,aAAa,KAAK,OAAO,GAAG,OAAO,iBAAiB,YAAY,KAAK,QAAQ,GAAG,EAAE,QAAQ,UAAU,MAAM,KAAK,UAAU,EAAE,SAAS,OAAO,iBAAiB,aAAa,KAAK,UAAU,GAAG,OAAO,iBAAiB,YAAY,KAAK,gBAAgB,OAAO,OAAO,iBAAiB,aAAa,KAAK,OAAO,GAAG,OAAO,iBAAiB,WAAW,KAAK,QAAQ,IAAI,KAAK,MAAM,cAAc;AAAA,UACrjB,QAAQ;AAAA,UACR,MAAM,KAAK,WAAW;AAAA,QACxB,CAAC;AAAA,MACH;AACE,aAAK,WAAW,MAAI,OAAO,iBAAiB,aAAa,KAAK,UAAU,GAAG,OAAO,iBAAiB,WAAW,KAAK,OAAO,GAAG,OAAO,iBAAiB,aAAa,KAAK,UAAU,GAAG,OAAO,iBAAiB,YAAY,KAAK,OAAO,GAAG,KAAK,eAAe,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC,EAAE,QAAQ,KAAK,MAAM,QAAQ,YAAY,KAAK,eAAe,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC,EAAE,QAAQ,KAAK,MAAM,QAAQ,WAAW,KAAK,QAAQ,aAAa,IAAI,EAAE,UAAU,EAAE,QAAQ,CAAC,EAAE,SAAS,KAAK,QAAQ,aAAa,IAAI,EAAE,UAAU,EAAE,QAAQ,CAAC,EAAE,SAAS,KAAK,cAAc,KAAK,cAAc,KAAK,cAAc,KAAK,cAAc,KAAK,QAAQ,GAAG,KAAK,QAAQ;AAAA,IACnpB;AAAA;AAAA,IAEA,WAAW,GAAG;AACZ,QAAE,eAAe;AACjB,UAAI,IAAI,KAAK;AACb,UAAI,IAAI;AAAA,QACN,GAAG,KAAK,QAAQ,CAAC,EAAE;AAAA,QACnB,GAAG,KAAK,QAAQ,CAAC,EAAE;AAAA,MACrB,GAAG,IAAI;AAAA,QACL,GAAG,EAAE,QAAQ,CAAC,EAAE;AAAA,QAChB,GAAG,EAAE,QAAQ,CAAC,EAAE;AAAA,MAClB,GAAG,IAAI;AAAA,QACL,GAAG,KAAK,QAAQ,CAAC,EAAE;AAAA,QACnB,GAAG,KAAK,QAAQ,CAAC,EAAE;AAAA,MACrB,GAAG,IAAI;AAAA,QACL,GAAG,EAAE,QAAQ,CAAC,EAAE;AAAA,QAChB,GAAG,EAAE,QAAQ,CAAC,EAAE;AAAA,MAClB,GAAG,IAAI,KAAK;AAAA,QACV,KAAK,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,KAAK,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC;AAAA,MAChD,GAAG,IAAI,KAAK;AAAA,QACV,KAAK,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,KAAK,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC;AAAA,MAChD,GAAG,IAAI,IAAI,GAAG,IAAI;AAClB,UAAI,IAAI,KAAK,YAAY,IAAI,KAAK,aAAa,IAAI,KAAK,aAAa,IAAI,KAAK,WAAW,IAAI,IAAI,MAAM,MAAM;AAC7G,UAAI,IAAI,IAAI;AACZ,UAAI,CAAC,KAAK,UAAU;AAClB,YAAI,KAAK,WAAW,MAAI,IAAI,IAAI,KAAK,KAAK,IAAI,CAAC,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,MAAM,KAAK,KAAK,IAAI,CAAC,IAAI,KAAK,UAAU,EAAE,SAAS,WAAW,MAAM;AAC5I,eAAK,WAAW;AAAA,QAClB,GAAG,CAAC,GAAG,CAAC,KAAK,gBAAgB,KAAK,GAAG,KAAK,GAAG,CAAC;AAC5C,iBAAO;AACT,aAAK,QAAQ;AAAA,MACf;AAAA,IACF;AAAA,IACA,iBAAiB,GAAG;AAClB,aAAO,oBAAoB,aAAa,KAAK,UAAU;AAAA,IACzD;AAAA;AAAA,IAEA,QAAQ,GAAG;AACT,UAAI,EAAE,eAAe,GAAG,EAAE,WAAW,EAAE,QAAQ,WAAW;AACxD,eAAO,KAAK,UAAU,EAAE,SAAS,OAAO,iBAAiB,aAAa,KAAK,UAAU,GAAG,OAAO,iBAAiB,YAAY,KAAK,gBAAgB,GAAG,OAAO,oBAAoB,aAAa,KAAK,OAAO,GAAG;AAC7M,UAAI,IAAI,aAAa,IAAI,EAAE,UAAU,EAAE,QAAQ,CAAC,EAAE,SAAS,IAAI,aAAa,IAAI,EAAE,UAAU,EAAE,QAAQ,CAAC,EAAE,SAAS,GAAG;AACrH,UAAI,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK,OAAO,KAAK,UAAU,MAAM;AAC3D,YAAI,KAAK,WAAW;AAClB,cAAI,IAAI,KAAK,WAAW,GAAG,GAAG,KAAK,KAAK,GAAG,IAAI,KAAK,YAAY,GAAG,IAAI,KAAK,aAAa,KAAK,OAAO,IAAI,KAAK,YAAY,KAAK,OAAO,GAAG,GAAG,GAAG;AAC/I,kBAAQ,KAAK,QAAQ;AAAA,YACnB,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AACH,kBAAI,KAAK,eAAe,KAAK,aAAa,IAAI,KAAK,SAAS,KAAK,IAAI,KAAK,GAAG,IAAI,KAAK,eAAe,KAAK,cAAc,IAAI,KAAK,SAAS,KAAK,IAAI,KAAK,GAAG,IAAI,IAAI,IAAI,KAAK,OAAO,IAAI,IAAI,IAAI,KAAK;AACpM;AAAA,YACF;AACE,kBAAI,KAAK,eAAe,KAAK,aAAa,IAAI,KAAK,SAAS,GAAG,IAAI,KAAK,eAAe,KAAK,cAAc,IAAI,KAAK,SAAS,GAAG,IAAI,IAAI,IAAI,KAAK,OAAO,IAAI,IAAI,IAAI,KAAK;AACxK;AAAA,UACJ;AACA,YAAE,MAAM,EAAE,OAAO,IAAI,IAAI,EAAE,MAAM,EAAE,OAAO,IAAI,IAAI,EAAE,MAAM,EAAE,OAAO,IAAI,IAAI,EAAE,MAAM,EAAE,OAAO,IAAI;AAAA,QAClG;AACA,aAAK,IAAI,GAAG,KAAK,IAAI,GAAG,KAAK,MAAM,cAAc;AAAA,UAC/C,QAAQ;AAAA,UACR,MAAM,KAAK,WAAW;AAAA,QACxB,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA;AAAA,IAEA,SAAS,GAAG;AACV,aAAO,oBAAoB,aAAa,KAAK,OAAO,GAAG,OAAO,oBAAoB,aAAa,KAAK,OAAO,GAAG,OAAO,oBAAoB,WAAW,KAAK,QAAQ,GAAG,OAAO,oBAAoB,YAAY,KAAK,QAAQ,GAAG,KAAK,MAAM,cAAc;AAAA,QAClP,QAAQ;AAAA,QACR,MAAM,KAAK,WAAW;AAAA,MACxB,CAAC;AAAA,IACH;AAAA;AAAA,IAEA,WAAW;AACT,WAAK,YAAY,OAAO,iBAAiB,KAAK,SAAS,KAAK,YAAY,KAAK,OAAO;AAAA,IACtF;AAAA;AAAA,IAEA,cAAc;AACZ,WAAK,YAAY,OAAO,oBAAoB,KAAK,SAAS,KAAK,UAAU;AAAA,IAC3E;AAAA;AAAA,IAEA,WAAW,GAAG;AACZ,QAAE,eAAe;AACjB,UAAI,IAAI,KAAK;AACb,UAAI,IAAI,EAAE,UAAU,EAAE,YAAY,IAAI,UAAU,UAAU,QAAQ,SAAS;AAC3E,UAAI,IAAI,IAAI,IAAI,KAAK,GAAG,KAAK,SAAS,IAAI,CAAC;AAC3C,UAAI,IAAI,KAAK;AACb,UAAI,IAAI,KAAK,YAAY,IAAI,KAAK,aAAa,IAAI,KAAK,aAAa,IAAI,KAAK;AAC9E,UAAI,IAAI,IAAI;AACZ,UAAI,IAAI,KAAK,KAAK,IAAI,CAAC,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,KAAK,KAAK,IAAI,CAAC;AAC9D,UAAI,IAAI,IAAI,IAAI,QAAQ;AACxB,UAAI,MAAM,KAAK,cAAc,KAAK,YAAY,GAAG,KAAK,MAAM,MAAM,KAAK,YAAY,KAAK,aAAa,WAAW,MAAM;AACpH,aAAK,UAAU,OAAI,KAAK,MAAM,KAAK,OAAO;AAAA,MAC5C,GAAG,EAAE,IAAI,KAAK,UAAU,MAAI,CAAC,KAAK,gBAAgB,KAAK,GAAG,KAAK,GAAG,CAAC;AACjE,eAAO;AACT,WAAK,QAAQ;AAAA,IACf;AAAA;AAAA,IAEA,YAAY,GAAG;AACb,UAAI,IAAI,KAAK;AACb,UAAI,KAAK;AACT,UAAI,IAAI;AACR,UAAI,IAAI,IAAI,KAAK,YAAY,IAAI,KAAK,aAAa,IAAI,KAAK,aAAa,IAAI,KAAK,WAAW,IAAI,IAAI,GAAG,IAAI,IAAI,KAAK,KAAK,IAAI,CAAC,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,KAAK,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,gBAAgB,KAAK,GAAG,KAAK,GAAG,CAAC;AAC/M,eAAO;AACT,WAAK,QAAQ;AAAA,IACf;AAAA;AAAA,IAEA,WAAW,GAAG;AACZ,QAAE,eAAe;AACjB,UAAI,IAAI,aAAa,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC,EAAE,UAAU,GAAG,IAAI,aAAa,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC,EAAE,UAAU;AAC/I,WAAK,UAAU,MAAM;AACnB,YAAI,IAAI,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK;AACrC,YAAI,IAAI,KAAK,KAAK,QAAQ,IAAI,KAAK,cAAc,KAAK,IAAI,KAAK,IAAI,KAAK,cAAc,GAAG,KAAK,eAAe,KAAK,gBAAgB,KAAK,QAAQ,KAAK,IAAI,KAAK,cAAc,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,KAAK,cAAc,KAAK,IAAI,CAAC,GAAG,KAAK,eAAe,KAAK,cAAc,IAAI,IAAI,KAAK,cAAc,IAAI,IAAI,CAAC,KAAK;AAChT,cAAI,KAAK,KAAK,QAAQ,IAAI,KAAK,cAAc,KAAK,IAAI,KAAK,IAAI,KAAK,cAAc,GAAG,KAAK,eAAe,KAAK,gBAAgB,KAAK,QAAQ,KAAK,IAAI,KAAK,cAAc,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,KAAK,cAAc,KAAK,IAAI,CAAC,GAAG,KAAK,eAAe,KAAK,cAAc,IAAI,IAAI,KAAK,cAAc,IAAI;AAAA,aACjS;AACH,cAAI,IAAI,KAAK,QAAQ,KAAK,YAAY,CAAC,IAAI,KAAK,YAAY,CAAC;AAC7D,cAAI,KAAK,eAAe,KAAK,KAAK,KAAK,QAAQ,KAAK,IAAI,KAAK,cAAc,KAAK,QAAQ,KAAK,QAAQ,KAAK,YAAY,CAAC,IAAI,KAAK,YAAY,CAAC,GAAG,IAAI,IAAI,KAAK,eAAe,KAAK,cAAc,KAAK,eAAe,KAAK,cAAc,KAAK,SAAS,KAAK,QAAQ,GAAG,KAAK,eAAe,KAAK;AAAA,QAC/R;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA,IAEA,eAAe,GAAG,GAAG,GAAG,GAAG,GAAG;AAC5B,QAAE,eAAe,GAAG,OAAO,iBAAiB,aAAa,KAAK,aAAa,GAAG,OAAO,iBAAiB,WAAW,KAAK,aAAa,GAAG,OAAO,iBAAiB,aAAa,KAAK,aAAa,GAAG,OAAO,iBAAiB,YAAY,KAAK,aAAa,GAAG,KAAK,aAAa,GAAG,KAAK,aAAa,GAAG,KAAK,kBAAkB,GAAG,KAAK,kBAAkB,GAAG,KAAK,QAAQ,aAAa,IAAI,EAAE,UAAU,EAAE,QAAQ,CAAC,EAAE,SAAS,KAAK,QAAQ,aAAa,IAAI,EAAE,UAAU,EAAE,QAAQ,CAAC,EAAE,SAAS,KAAK,WAAW,KAAK,OAAO,KAAK,WAAW,KAAK,OAAO,KAAK,cAAc,KAAK,cAAc,KAAK,cAAc,KAAK,cAAc,KAAK,SAAS,KAAK,cAAc,KAAK,eAAe,KAAK,aAAa,IAAI,KAAK,MAAM,oBAAoB;AAAA,QACnsB,OAAO,KAAK;AAAA,QACZ,QAAQ,KAAK;AAAA,MACf,CAAC;AAAA,IACH;AAAA;AAAA,IAEA,cAAc,GAAG;AACf,QAAE,eAAe;AACjB,UAAI,IAAI,aAAa,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC,EAAE,UAAU,GAAG,IAAI,aAAa,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC,EAAE,UAAU;AAC/I,UAAI,IAAI,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI,GAAG,IAAI;AACvC,UAAI,KAAK,WAAW;AAClB,YAAI,IAAI,KAAK,WAAW,GAAG,IAAI,EAAE,IAAI,IAAI,EAAE;AAC3C,YAAI,EAAE,KAAK,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,KAAK,IAAI,EAAE,KAAK,GAAG,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,IAAI;AAAA,MACpF;AACA,YAAM,CAAC,GAAG,CAAC,IAAI,KAAK,mBAAmB;AACvC,WAAK,UAAU,MAAM;AACnB,YAAI,IAAI,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK;AACrC,YAAI,KAAK,eAAe,KAAK,oBAAoB,IAAI,KAAK,WAAW,IAAI,KAAK,KAAK,QAAQ,GAAG,KAAK,eAAe,KAAK,WAAW,KAAK,cAAc,IAAI,KAAK,KAAK,WAAW,IAAI,KAAK,KAAK,QAAQ,IAAI,KAAK,cAAc,KAAK,IAAI,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW,KAAK,cAAc,GAAG,KAAK,eAAe,IAAI,KAAK,cAAc,KAAK,IAAI,IAAI,KAAK,cAAc,IAAI,MAAM,KAAK,QAAQ,KAAK,IAAI,CAAC,IAAI,KAAK,eAAe,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW,KAAK,aAAa,KAAK,eAAe,KAAK,cAAc,KAAK,YAAY,KAAK,oBAAoB,MAAM,KAAK,WAAW,IAAI,IAAI,KAAK,QAAQ,IAAI,KAAK,WAAW,IAAI,KAAK,KAAK,QAAQ,KAAK,WAAW,IAAI,KAAK,gBAAgB,IAAI,KAAK,WAAW,IAAI,IAAI,KAAK,cAAc,KAAK,eAAe,KAAK,gBAAgB,KAAK,QAAQ,IAAI,KAAK,cAAc,KAAK,IAAI,IAAI,KAAK,QAAQ,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,QAAQ,IAAI,KAAK,cAAc,GAAG,KAAK,eAAe,IAAI,KAAK,cAAc,KAAK,IAAI,IAAI,KAAK,QAAQ,KAAK,IAAI,IAAI,KAAK,cAAc,KAAK,IAAI,IAAI,KAAK,QAAQ,IAAI,MAAM,KAAK,eAAe,KAAK,oBAAoB,IAAI,KAAK,WAAW,IAAI,KAAK,KAAK,QAAQ,GAAG,KAAK,eAAe,KAAK,WAAW,KAAK,cAAc,IAAI,KAAK,KAAK,WAAW,IAAI,KAAK,KAAK,QAAQ,IAAI,KAAK,cAAc,KAAK,IAAI,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW,KAAK,cAAc,GAAG,KAAK,eAAe,IAAI,KAAK,cAAc,KAAK,IAAI,IAAI,KAAK,cAAc,IAAI,MAAM,KAAK,QAAQ,KAAK,IAAI,CAAC,IAAI,KAAK,eAAe,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW,KAAK,aAAa,KAAK,eAAe,KAAK,cAAc,KAAK,YAAY,KAAK,oBAAoB,MAAM,KAAK,WAAW,IAAI,IAAI,KAAK,QAAQ,IAAI,KAAK,WAAW,IAAI,KAAK,KAAK,QAAQ,KAAK,WAAW,IAAI,KAAK,gBAAgB,IAAI,KAAK,WAAW,IAAI,IAAI,KAAK,cAAc,KAAK,eAAe,KAAK,gBAAgB,KAAK,QAAQ,IAAI,KAAK,cAAc,KAAK,IAAI,IAAI,KAAK,QAAQ,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,QAAQ,IAAI,KAAK,cAAc,GAAG,KAAK,eAAe,IAAI,KAAK,cAAc,KAAK,IAAI,IAAI,KAAK,QAAQ,KAAK,IAAI,IAAI,KAAK,cAAc,KAAK,IAAI,IAAI,KAAK,QAAQ,IAAI,MAAM,KAAK,cAAc,KAAK,OAAO;AACnlE,cAAI,IAAI,KAAK,QAAQ,KAAK,YAAY,CAAC,IAAI,KAAK,YAAY,CAAC;AAC7D,cAAI,KAAK,KAAK,QAAQ,GAAG,KAAK,QAAQ,KAAK,YAAY,CAAC,IAAI,IAAI,KAAK,YAAY,CAAC,GAAG,KAAK,oBAAoB,MAAM,KAAK,eAAe,KAAK,eAAe,KAAK,WAAW,KAAK,WAAW,IAAI,KAAK,eAAe,KAAK,KAAK,QAAQ,IAAI,KAAK,cAAc,KAAK,QAAQ,KAAK,QAAQ,KAAK,YAAY,CAAC,IAAI,KAAK,YAAY,CAAC,GAAG,KAAK,oBAAoB,MAAM,KAAK,eAAe,KAAK,eAAe,KAAK,WAAW,KAAK,WAAW,KAAK,QAAQ;AAAA,QACxb;AACA,YAAI,KAAK,cAAc,KAAK,OAAO;AACjC,cAAI,IAAI,KAAK,QAAQ,KAAK,YAAY,CAAC,IAAI,KAAK,YAAY,CAAC;AAC7D,cAAI,KAAK,KAAK,QAAQ,GAAG,KAAK,QAAQ,KAAK,YAAY,CAAC,IAAI,IAAI,KAAK,YAAY,CAAC,GAAG,KAAK,eAAe,KAAK,WAAW,KAAK,cAAc,KAAK,SAAS,IAAI,KAAK,eAAe,KAAK,KAAK,QAAQ,IAAI,KAAK,cAAc,KAAK,QAAQ,KAAK,QAAQ,KAAK,YAAY,CAAC,IAAI,KAAK,YAAY,CAAC,KAAK,KAAK,QAAQ;AAAA,QACjT;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,qBAAqB;AACnB,UAAI,EAAE,OAAO,GAAG,OAAO,GAAG,cAAc,EAAE,IAAI,MAAM,IAAI,IAAI,MAAM;AAClE,aAAO,MAAM,QAAQ,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,WAAW,EAAE,CAAC,CAAC,GAAG,IAAI,WAAW,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;AAAA,IACjG;AAAA;AAAA,IAEA,cAAc,GAAG;AACf,aAAO,oBAAoB,aAAa,KAAK,aAAa,GAAG,OAAO,oBAAoB,WAAW,KAAK,aAAa,GAAG,OAAO,oBAAoB,aAAa,KAAK,aAAa,GAAG,OAAO,oBAAoB,YAAY,KAAK,aAAa;AAAA,IAChP;AAAA;AAAA,IAEA,cAAc,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAC9B,YAAM,IAAI,IAAI;AACd,UAAI,IAAI,GAAG,IAAI;AACf,aAAO,IAAI,MAAM,IAAI,GAAG,IAAI,KAAK,KAAK,IAAI,CAAC,IAAI,IAAI,MAAM,IAAI,GAAG,IAAI,KAAK,KAAK,IAAI,CAAC,GAAG,IAAI,MAAM,IAAI,GAAG,IAAI,KAAK,KAAK,IAAI,CAAC,KAAK,IAAI,MAAM,IAAI,GAAG,IAAI,KAAK,KAAK,IAAI,CAAC,IAAI,IAAI,MAAM,IAAI,GAAG,IAAI,KAAK,KAAK,IAAI,CAAC,IAAI,EAAE,OAAO,GAAG,QAAQ,EAAE;AAAA,IACvO;AAAA;AAAA,IAEA,UAAU;AACR,WAAK,UAAU,KAAK,KAAK,UAAU,MAAM,KAAK,WAAW;AACzD,UAAI,CAAC,GAAG,CAAC,IAAI,KAAK,mBAAmB;AACrC,YAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,IAAI,KAAK,QAAQ,KAAK;AAAA,QAChD,KAAK,YAAY,CAAC;AAAA,QAClB,KAAK,YAAY,CAAC;AAAA,QAClB;AAAA,QACA;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,MACP,IAAI,EAAE,OAAO,GAAG,QAAQ,EAAE;AAC1B,UAAI,KAAK,UAAU,KAAK,QAAQ,GAAG,KAAK,eAAe,IAAI,KAAK,MAAM,KAAK,eAAe,KAAK,IAAI,KAAK,IAAI,KAAK,UAAU,KAAK,QAAQ,GAAG,KAAK,eAAe,IAAI,KAAK,MAAM,KAAK,eAAe,KAAK,IAAI,KAAK,OAAO,oBAAoB,aAAa,KAAK,UAAU,GAAG,OAAO,oBAAoB,WAAW,KAAK,OAAO,GAAG,OAAO,oBAAoB,aAAa,KAAK,UAAU,GAAG,OAAO,oBAAoB,YAAY,KAAK,OAAO;AAAA,IAC9a;AAAA;AAAA,IAEA,YAAY;AACV,WAAK,OAAO;AAAA,IACd;AAAA;AAAA,IAEA,WAAW;AACT,WAAK,OAAO;AAAA,IACd;AAAA;AAAA,IAEA,YAAY;AACV,WAAK,WAAW,OAAI,KAAK,QAAQ,GAAG,KAAK,QAAQ;AAAA,IACnD;AAAA;AAAA,IAEA,SAAS,GAAG;AACV,UAAI,EAAE,eAAe,GAAG,CAAC,KAAK;AAC5B,eAAO,KAAK,OAAO,OAAI,KAAK,UAAU,CAAC,GAAG;AAC5C,UAAI,EAAE,WAAW,EAAE,QAAQ,WAAW;AACpC,eAAO,KAAK,OAAO,OAAI,KAAK,UAAU,CAAC,GAAG,KAAK,UAAU,GAAG;AAC9D,aAAO,iBAAiB,aAAa,KAAK,QAAQ,GAAG,OAAO,iBAAiB,WAAW,KAAK,SAAS,GAAG,OAAO,iBAAiB,aAAa,KAAK,QAAQ,GAAG,OAAO,iBAAiB,YAAY,KAAK,SAAS;AAChN,UAAI,IAAI,aAAa,IAAI,EAAE,UAAU,EAAE,QAAQ,CAAC,EAAE,SAAS,IAAI,aAAa,IAAI,EAAE,UAAU,EAAE,QAAQ,CAAC,EAAE,SAAS,GAAG;AACrH,UAAI,IAAI,KAAK,cAAc,IAAI,IAAI,KAAK,cAAc,KAAK,QAAQ,GAAG,KAAK,QAAQ,GAAG,KAAK,MAAM,eAAe;AAAA,QAC9G,QAAQ;AAAA,QACR,MAAM,KAAK,YAAY;AAAA,MACzB,CAAC;AAAA,IACH;AAAA,IACA,SAAS,GAAG,GAAG;AACb,UAAI,IAAI,GAAG,IAAI;AACf,YAAM,EAAE,eAAe,GAAG,IAAI,aAAa,IAAI,EAAE,UAAU,EAAE,QAAQ,CAAC,EAAE,SAAS,IAAI,aAAa,IAAI,EAAE,UAAU,EAAE,QAAQ,CAAC,EAAE,UAAU,KAAK,UAAU,MAAM;AAC5J,YAAI,GAAG,GAAG,IAAI,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK;AAC3C,YAAI,MAAM,IAAI,KAAK,cAAc,IAAI,KAAK,eAAe,KAAK,IAAI,IAAI,IAAI,IAAI,KAAK,QAAQ,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK,QAAQ,IAAI,GAAG,KAAK,IAAI,IAAI,IAAI,IAAI,KAAK,QAAQ,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK,QAAQ,IAAI,GAAG,KAAK,WAAW;AAC/N,cAAI,IAAI,KAAK,WAAW;AACxB,eAAK,EAAE,OAAO,IAAI,EAAE,KAAK,IAAI,KAAK,QAAQ,EAAE,OAAO,IAAI,EAAE,KAAK,KAAK,QAAQ,KAAK,EAAE,OAAO,IAAI,EAAE,KAAK,IAAI,KAAK,QAAQ,EAAE,OAAO,IAAI,EAAE,KAAK,KAAK;AAAA,QAChJ;AACA,aAAK,eAAe,GAAG,KAAK,eAAe,GAAG,KAAK,MAAM,eAAe;AAAA,UACtE,QAAQ;AAAA,UACR,MAAM,KAAK,YAAY;AAAA,QACzB,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA;AAAA,IAEA,WAAW,GAAG,GAAG,GAAG;AAClB,UAAI,KAAK,KAAK,GAAG,IAAI,KAAK,KAAK,GAAG,IAAI,KAAK,KAAK;AAChD,UAAI,IAAI;AAAA,QACN,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,MACN,GAAG,IAAI,KAAK,YAAY,GAAG,IAAI,KAAK,aAAa;AACjD,cAAQ,KAAK,QAAQ;AAAA,QACnB,KAAK;AACH,YAAE,KAAK,IAAI,KAAK,aAAa,IAAI,KAAK,GAAG,EAAE,KAAK,EAAE,KAAK,KAAK,YAAY,GAAG,EAAE,KAAK,IAAI,KAAK,cAAc,IAAI,KAAK,GAAG,EAAE,KAAK,EAAE,KAAK,KAAK,aAAa;AACrJ;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,YAAE,KAAK,IAAI,KAAK,aAAa,IAAI,KAAK,KAAK,IAAI,KAAK,GAAG,EAAE,KAAK,EAAE,KAAK,KAAK,aAAa,GAAG,EAAE,KAAK,IAAI,KAAK,cAAc,IAAI,KAAK,KAAK,IAAI,KAAK,GAAG,EAAE,KAAK,EAAE,KAAK,KAAK,YAAY;AACjL;AAAA,QACF;AACE,YAAE,KAAK,IAAI,KAAK,aAAa,IAAI,KAAK,GAAG,EAAE,KAAK,EAAE,KAAK,KAAK,YAAY,GAAG,EAAE,KAAK,IAAI,KAAK,cAAc,IAAI,KAAK,GAAG,EAAE,KAAK,EAAE,KAAK,KAAK,aAAa;AACrJ;AAAA,MACJ;AACA,aAAO;AAAA,IACT;AAAA;AAAA,IAEA,cAAc;AACZ,UAAI,IAAI;AAAA,QACN,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,MACN;AACA,aAAO,EAAE,KAAK,KAAK,cAAc,EAAE,KAAK,EAAE,KAAK,KAAK,OAAO,EAAE,KAAK,KAAK,cAAc,EAAE,KAAK,EAAE,KAAK,KAAK,OAAO;AAAA,IACjH;AAAA,IACA,UAAU,GAAG;AACX,aAAO,oBAAoB,aAAa,KAAK,QAAQ,GAAG,OAAO,oBAAoB,WAAW,KAAK,SAAS,GAAG,OAAO,oBAAoB,aAAa,KAAK,QAAQ,GAAG,OAAO,oBAAoB,YAAY,KAAK,SAAS,GAAG,KAAK,MAAM,eAAe;AAAA,QACvP,QAAQ;AAAA,QACR,MAAM,KAAK,YAAY;AAAA,MACzB,CAAC;AAAA,IACH;AAAA,IACA,eAAe,GAAG;AAChB,UAAI,IAAI,SAAS,cAAc,QAAQ,GAAG,IAAI,EAAE,WAAW,IAAI,GAAG,IAAI,IAAI,MAAM,GAAG,IAAI,KAAK,QAAQ,IAAI,KAAK,WAAW,IAAI,KAAK,YAAY,IAAI,KAAK,cAAc,IAAI,KAAK;AAC7K,QAAE,SAAS,MAAM;AACf,YAAI,KAAK,UAAU,GAAG;AACpB,cAAI,IAAI;AACR,eAAK,OAAO,CAAC,KAAK,SAAS,IAAI,OAAO,mBAAmB,KAAK,YAAY,IAAI,CAAC,KAAK,SAAS,IAAI,KAAK,IAAI,OAAO,KAAK,OAAO,CAAC;AAC9H,cAAI,IAAI,KAAK,QAAQ,GAAG,IAAI,KAAK,QAAQ,GAAG,IAAI,IAAI,KAAK,QAAQ,GAAG,IAAI,IAAI,KAAK,QAAQ,GAAG,KAAK,KAAK,IAAI,IAAI,KAAK,aAAa,IAAI,KAAK,SAAS,KAAK,GAAG,KAAK,KAAK,IAAI,IAAI,KAAK,cAAc,IAAI,KAAK,SAAS,KAAK;AACtN,kBAAQ,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,GAAG,GAAG;AAAA,YAC5B,KAAK;AACH,mBAAK,QAAQ,EAAE,IAAI,KAAK,OAAO,IAAI,KAAK,KAAK,GAAG,EAAE;AAAA,gBAChD;AAAA,gBACA,IAAI,KAAK;AAAA,gBACT,IAAI,KAAK;AAAA,gBACT,IAAI,KAAK;AAAA,gBACT,IAAI,KAAK;AAAA,cACX,KAAK,EAAE,UAAU,GAAG,GAAG,GAAG,GAAG,CAAC;AAC9B;AAAA,YACF,KAAK;AAAA,YACL,KAAK;AACH,mBAAK,QAAQ,EAAE,IAAI,KAAK,OAAO,IAAI,KAAK,KAAK,GAAG,IAAI,IAAI,KAAK,SAAS,IAAI,KAAK,QAAQ,IAAI,KAAK,SAAS,GAAG,IAAI,IAAI,KAAK,SAAS,IAAI,KAAK,QAAQ,IAAI,KAAK,SAAS,GAAG,EAAE,OAAO,IAAI,KAAK,KAAK,KAAK,GAAG,GAAG,EAAE;AAAA,gBAC1M;AAAA,gBACA;AAAA,gBACA,CAAC,IAAI,IAAI,KAAK;AAAA,gBACd,IAAI,KAAK;AAAA,gBACT,IAAI,KAAK;AAAA,cACX,MAAM,IAAI,KAAK,IAAI,KAAK,GAAG,IAAI,KAAK,IAAI,KAAK,GAAG,EAAE,OAAO,IAAI,KAAK,KAAK,KAAK,GAAG,GAAG,EAAE,UAAU,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC;AAChH;AAAA,YACF,KAAK;AAAA,YACL,KAAK;AACH,mBAAK,QAAQ,EAAE,IAAI,KAAK,OAAO,IAAI,KAAK,KAAK,GAAG,EAAE,OAAO,IAAI,KAAK,KAAK,KAAK,GAAG,GAAG,IAAI,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK,OAAO,EAAE;AAAA,gBAC1H;AAAA,gBACA,CAAC,IAAI,IAAI,KAAK;AAAA,gBACd,CAAC,IAAI,IAAI,KAAK;AAAA,gBACd,IAAI,KAAK;AAAA,gBACT,IAAI,KAAK;AAAA,cACX,MAAM,EAAE,OAAO,IAAI,KAAK,KAAK,KAAK,GAAG,GAAG,EAAE,UAAU,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC;AAC3E;AAAA,YACF,KAAK;AAAA,YACL,KAAK;AACH,mBAAK,QAAQ,EAAE,IAAI,KAAK,OAAO,IAAI,KAAK,KAAK,GAAG,IAAI,IAAI,KAAK,SAAS,IAAI,KAAK,QAAQ,IAAI,KAAK,SAAS,GAAG,IAAI,IAAI,KAAK,SAAS,IAAI,KAAK,QAAQ,IAAI,KAAK,SAAS,GAAG,EAAE,OAAO,IAAI,KAAK,KAAK,KAAK,GAAG,GAAG,EAAE;AAAA,gBAC1M;AAAA,gBACA,CAAC,IAAI,IAAI,KAAK;AAAA,gBACd;AAAA,gBACA,IAAI,KAAK;AAAA,gBACT,IAAI,KAAK;AAAA,cACX,MAAM,IAAI,KAAK,IAAI,KAAK,GAAG,IAAI,KAAK,IAAI,KAAK,GAAG,EAAE,OAAO,IAAI,KAAK,KAAK,KAAK,GAAG,GAAG,EAAE,UAAU,GAAG,CAAC,IAAI,GAAG,GAAG,GAAG,CAAC;AAChH;AAAA,YACF;AACE,mBAAK,QAAQ,EAAE,IAAI,KAAK,OAAO,IAAI,KAAK,KAAK,GAAG,EAAE;AAAA,gBAChD;AAAA,gBACA,IAAI,KAAK;AAAA,gBACT,IAAI,KAAK;AAAA,gBACT,IAAI,KAAK;AAAA,gBACT,IAAI,KAAK;AAAA,cACX,KAAK,EAAE,UAAU,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,UAClC;AACA,YAAE,QAAQ;AAAA,QACZ,OAAO;AACL,cAAI,IAAI,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK;AACrC,kBAAQ,EAAE,KAAK,GAAG,GAAG;AAAA,YACnB,KAAK;AACH,gBAAE,GAAG,CAAC,GAAG,EAAE,UAAU,GAAG,GAAG,GAAG,GAAG,CAAC;AAClC;AAAA,YACF,KAAK;AAAA,YACL,KAAK;AACH,gBAAE,GAAG,CAAC,GAAG,EAAE,OAAO,IAAI,KAAK,KAAK,KAAK,GAAG,GAAG,EAAE,UAAU,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;AACrE;AAAA,YACF,KAAK;AAAA,YACL,KAAK;AACH,gBAAE,GAAG,CAAC,GAAG,EAAE,OAAO,IAAI,KAAK,KAAK,KAAK,GAAG,GAAG,EAAE,UAAU,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;AACtE;AAAA,YACF,KAAK;AAAA,YACL,KAAK;AACH,gBAAE,GAAG,CAAC,GAAG,EAAE,OAAO,IAAI,KAAK,KAAK,KAAK,GAAG,GAAG,EAAE,UAAU,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC;AACrE;AAAA,YACF;AACE,gBAAE,GAAG,CAAC,GAAG,EAAE,UAAU,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,UACtC;AACA,YAAE,QAAQ;AAAA,QACZ;AACA,UAAE,CAAC;AAAA,MACL;AACA,UAAI,IAAI,KAAK,IAAI,OAAO,GAAG,CAAC;AAC5B,YAAM,WAAW,EAAE,cAAc,cAAc,EAAE,MAAM,KAAK;AAC5D,YAAM,IAAI,KAAK;AACf,eAAS,EAAE,GAAG,GAAG;AACf,UAAE,QAAQ,KAAK,MAAM,CAAC,GAAG,EAAE,SAAS,KAAK,MAAM,CAAC,GAAG,MAAM,EAAE,YAAY,GAAG,EAAE,SAAS,GAAG,GAAG,EAAE,OAAO,EAAE,MAAM;AAAA,MAC9G;AAAA,IACF;AAAA;AAAA,IAEA,YAAY,GAAG;AACb,WAAK,eAAe,CAAC,MAAM;AACzB,UAAE,EAAE,UAAU,WAAW,KAAK,YAAY,KAAK,UAAU,CAAC;AAAA,MAC5D,CAAC;AAAA,IACH;AAAA;AAAA,IAEA,YAAY,GAAG;AACb,WAAK,eAAe,CAAC,MAAM;AACzB,UAAE;AAAA,UACA,CAAC,MAAM,EAAE,CAAC;AAAA,UACV,WAAW,KAAK;AAAA,UAChB,KAAK;AAAA,QACP;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA,IAEA,cAAc;AACZ,UAAI,KAAK;AACP,aAAK,YAAY,OAAI,WAAW,MAAM;AACpC,eAAK,YAAY;AAAA,QACnB,GAAG,EAAE;AAAA;AAEL,eAAO;AACT,UAAI,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,IAAI,KAAK;AAC7C,UAAI,IAAI,CAAC;AACT,QAAE,MAAM;AAAA,QACN,OAAO,GAAG,CAAC;AAAA,QACX,QAAQ,GAAG,CAAC;AAAA,MACd;AACA,UAAI,KAAK,KAAK,IAAI,KAAK,gBAAgB,GAAG,KAAK,KAAK,IAAI,KAAK,gBAAgB,GAAG,IAAI;AACpF,QAAE,IAAI,GAAG,EAAE,IAAI,GAAG,EAAE,MAAM,KAAK,MAAM,EAAE,MAAM;AAAA,QAC3C,OAAO,GAAG,KAAK,SAAS;AAAA,QACxB,QAAQ,GAAG,KAAK,UAAU;AAAA,QAC1B,WAAW,SAAS,CAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,cAAc,KAAK,SAAS,EAAE;AAAA,MACtF,GAAG,EAAE,OAAO;AAAA,gDAC8B,EAAE,CAAC,eAAe,EAAE,CAAC;AAAA,6BACxC,CAAC,eAAe,CAAC;AAAA,qBACzB,EAAE,GAAG,kBAAkB,KAAK,SAAS,eAAe,KAAK,UAAU;AAAA,kBACtE,CAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,cAAc,KAAK,SAAS,EAAE;AAAA;AAAA,eAEnE,KAAK,MAAM,aAAa,CAAC;AAAA,IACpC;AAAA;AAAA,IAEA,SAAS;AACP,UAAI,IAAI,IAAI,MAAM;AAClB,QAAE,SAAS,MAAM;AACf,aAAK,IAAI,WAAW,OAAO,iBAAiB,KAAK,MAAM,OAAO,EAAE,KAAK,GAAG,KAAK,IAAI,WAAW,OAAO,iBAAiB,KAAK,MAAM,OAAO,EAAE,MAAM,GAAG,KAAK,YAAY,EAAE,OAAO,KAAK,aAAa,EAAE,QAAQ,KAAK,WAAW,KAAK,QAAQ,IAAI,KAAK,QAAQ,KAAK,YAAY,GAAG,KAAK,UAAU,MAAM;AAC5R,eAAK,IAAI,EAAE,KAAK,YAAY,KAAK,YAAY,KAAK,SAAS,KAAK,KAAK,IAAI,KAAK,YAAY,KAAK,SAAS,GAAG,KAAK,IAAI,EAAE,KAAK,aAAa,KAAK,aAAa,KAAK,SAAS,KAAK,KAAK,IAAI,KAAK,aAAa,KAAK,SAAS,GAAG,KAAK,UAAU,OAAI,KAAK,YAAY,KAAK,WAAW,GAAG,KAAK,MAAM,YAAY,SAAS,GAAG,WAAW,MAAM;AAClU,iBAAK,YAAY;AAAA,UACnB,GAAG,EAAE;AAAA,QACP,CAAC;AAAA,MACH,GAAG,EAAE,UAAU,MAAM;AACnB,aAAK,MAAM,YAAY,OAAO;AAAA,MAChC,GAAG,EAAE,MAAM,KAAK;AAAA,IAClB;AAAA;AAAA,IAEA,cAAc;AACZ,UAAI,IAAI,GAAG,IAAI,KAAK,WAAW,IAAI,KAAK;AACxC,YAAM,IAAI,KAAK,KAAK,MAAM,GAAG;AAC7B,cAAQ,EAAE,CAAC,GAAG;AAAA,QACZ,KAAK;AACH,eAAK,YAAY,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK,YAAY,KAAK,aAAa,IAAI,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK;AAC7G;AAAA,QACF,KAAK;AACH,cAAI,KAAK,GAAG,IAAI,IAAI,KAAK,WAAW,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK;AACvF;AAAA,QACF;AACE,cAAI;AACF,gBAAI,IAAI,EAAE,CAAC;AACX,gBAAI,EAAE,OAAO,IAAI,MAAM,IAAI;AACzB,kBAAI,EAAE,QAAQ,MAAM,EAAE,GAAG,IAAI,WAAW,CAAC;AACzC,oBAAM,IAAI,IAAI,KAAK;AACnB,kBAAI,IAAI,GAAG,IAAI,EAAE,CAAC;AAClB,gBAAE,OAAO,IAAI,MAAM,OAAO,IAAI,EAAE,QAAQ,MAAM,EAAE,GAAG,IAAI,WAAW,CAAC,GAAG,IAAI,IAAI,KAAK,aAAa,IAAI,KAAK,IAAI,GAAG,CAAC;AAAA,YACnH;AACA,gBAAI,EAAE,OAAO,GAAG,MAAM,OAAO,IAAI,EAAE,QAAQ,KAAK,EAAE,GAAG,IAAI,WAAW,CAAC,IAAI,MAAM,KAAK,GAAG,IAAI,IAAI,KAAK,YAAY,EAAE,WAAW,KAAK,MAAM,QAAQ;AAC9I,kBAAI,IAAI,EAAE,CAAC;AACX,gBAAE,OAAO,IAAI,MAAM,OAAO,IAAI,EAAE,QAAQ,MAAM,EAAE,GAAG,IAAI,WAAW,CAAC,GAAG,IAAI,IAAI,KAAK,aAAa,EAAE,OAAO,GAAG,MAAM,OAAO,IAAI,EAAE,QAAQ,KAAK,EAAE,GAAG,IAAI,WAAW,CAAC,IAAI,MAAM,KAAK,GAAG,IAAI,IAAI,KAAK;AAAA,YAClM;AAAA,UACF,QAAQ;AACN,gBAAI;AAAA,UACN;AAAA,MACJ;AACA,aAAO;AAAA,IACT;AAAA;AAAA,IAEA,WAAW,GAAG,GAAG;AACf,UAAI,KAAK,SAAS,MAAM,KAAK,SAAS;AACpC;AACF,WAAK,UAAU,GAAG,KAAK,WAAW;AAClC,UAAI,IAAI,KAAK,GAAG,IAAI,KAAK;AACzB,UAAI,KAAK,WAAW;AAClB,cAAM,IAAI,KAAK,IAAI,KAAK,MAAM,IAAI,IAAI;AACtC,YAAI,KAAK,IAAI,KAAK,aAAa,KAAK,aAAa,KAAK,OAAO,KAAK,IAAI,KAAK,YAAY,KAAK,cAAc,KAAK;AAC/G,YAAI,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,IAAI;AAAA,MACrC;AACA,UAAI,IAAI,KAAK,WAAW,KAAK,aAAa,GAAG,IAAI,KAAK,WAAW,KAAK,cAAc;AACpF,OAAC,MAAM,KAAK,MAAM,OAAO,IAAI,IAAI,KAAK,IAAI,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,IAAI,GAAG,KAAK,UAAU,IAAI,IAAI,KAAK,YAAY,CAAC,IAAI,KAAK,YAAY,CAAC,IAAI,IAAI,KAAK,MAAM,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,YAAY,CAAC,IAAI,KAAK,YAAY,CAAC,IAAI,KAAK,WAAW,GAAG,CAAC;AAAA,IAClQ;AAAA;AAAA,IAEA,WAAW,GAAG,GAAG;AACf,UAAI,KAAK,WAAW;AAClB,YAAI,IAAI,KAAK,WAAW;AACxB,YAAI,EAAE,KAAK,EAAE,OAAO,IAAI,EAAE,KAAK,EAAE,IAAI,IAAI,IAAI,KAAK,YAAY,CAAC,IAAI,KAAK,YAAY,CAAC,IAAI,IAAI,EAAE,KAAK,EAAE,OAAO,IAAI,EAAE,KAAK,EAAE,IAAI,IAAI,IAAI,KAAK,YAAY,CAAC,IAAI,KAAK,YAAY,CAAC;AAAA,MAChL;AACA,WAAK,QAAQ,GAAG,KAAK,QAAQ,GAAG,KAAK,mBAAmB,GAAG,KAAK,UAAU,MAAM;AAC9E,aAAK,gBAAgB,KAAK,IAAI,KAAK,SAAS,GAAG,KAAK,gBAAgB,KAAK,IAAI,KAAK,SAAS,GAAG,KAAK,aAAa,KAAK,SAAS,MAAM,IAAE;AAAA,MACxI,CAAC;AAAA,IACH;AAAA;AAAA,IAEA,UAAU;AACR,WAAK,KAAK,KAAK,OAAO,IAAI,KAAK,QAAQ,GAAG,KAAK,OAAO,OAAI,KAAK,SAAS,GAAG,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,KAAK,YAAY,GAAG,KAAK,aAAa,GAAG,KAAK,iBAAiB,OAAI,KAAK,UAAU,GAAG,KAAK,UAAU,MAAM;AAC3M,aAAK,WAAW;AAAA,MAClB,CAAC;AAAA,IACH;AAAA;AAAA,IAEA,aAAa;AACX,WAAK,SAAS,KAAK,UAAU,KAAK,IAAI,KAAK,SAAS;AAAA,IACtD;AAAA;AAAA,IAEA,cAAc;AACZ,WAAK,SAAS,KAAK,UAAU,IAAI,IAAI,KAAK,SAAS;AAAA,IACrD;AAAA;AAAA,IAEA,cAAc;AACZ,WAAK,SAAS;AAAA,IAChB;AAAA;AAAA,IAEA,gBAAgB,GAAG,GAAG,GAAG;AACvB,UAAI,KAAK,KAAK,GAAG,IAAI,KAAK,KAAK,GAAG,IAAI,KAAK,KAAK;AAChD,UAAI,IAAI;AACR,UAAI,KAAK,WAAW;AAClB,YAAI,IAAI,KAAK,WAAW,GAAG,GAAG,CAAC,GAAG,IAAI,KAAK,YAAY;AACvD,UAAE,MAAM,EAAE,OAAO,IAAI,QAAK,EAAE,MAAM,EAAE,OAAO,IAAI,QAAK,EAAE,MAAM,EAAE,OAAO,IAAI,QAAK,EAAE,MAAM,EAAE,OAAO,IAAI,QAAK,KAAK,KAAK,eAAe,GAAG,GAAG,CAAC;AAAA,MAC1I;AACA,aAAO;AAAA,IACT;AAAA;AAAA,IAEA,eAAe,GAAG,GAAG,GAAG;AACtB,UAAI,IAAI,KAAK,WAAW,IAAI,KAAK,YAAY,IAAI,IAAI,GAAG,IAAI,IAAI;AAChE,UAAI,KAAK,KAAK,SAAS,KAAK,KAAK;AAC/B,aAAK,QAAQ;AAAA,WACV;AACH,cAAM,IAAI,KAAK,QAAQ,GAAG,IAAI,KAAK,QAAQ,GAAG,IAAI,KAAK,SAAS,IAAI,IAAI,IAAI;AAC5E,aAAK,QAAQ,GAAG,IAAI,IAAI,GAAG,IAAI,IAAI;AAAA,MACrC;AACA,WAAK,mBAAmB,EAAE,MAAM,EAAE,OAAO,KAAK,sBAAsB,KAAK,IAAI,EAAE,MAAM,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,EAAE,MAAM,IAAI,KAAK,IAAI,EAAE,MAAM,EAAE,OAAO,KAAK,sBAAsB,KAAK,IAAI,EAAE,MAAM,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,QAAQ,KAAK,IAAI,EAAE,MAAM,IAAI,KAAK,IAAI,IAAI,EAAE,MAAM,EAAE,OAAO,KAAK,sBAAsB,KAAK,IAAI,EAAE,MAAM,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,EAAE,MAAM,IAAI,KAAK,IAAI,EAAE,MAAM,EAAE,OAAO,KAAK,sBAAsB,KAAK,IAAI,EAAE,MAAM,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,EAAE,MAAM,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,SAAS,IAAI,KAAK,WAAW,KAAK,iBAAiB;AAAA,IAC7kB;AAAA,EACF;AAAA,EACA,UAAU;AACR,SAAK,UAAU,aAAa,SAAS,cAAc,KAAK,IAAI,UAAU,SAAS,iBAAiB,SAAS,eAAe;AACxH,QAAI,IAAI;AACR,QAAI,IAAI,UAAU;AAClB,SAAK,QAAQ,CAAC,CAAC,EAAE,MAAM,+BAA+B,GAAG,kBAAkB,UAAU,UAAU,OAAO,eAAe,kBAAkB,WAAW,UAAU;AAAA,MAC1J,OAAO,SAAS,GAAG,GAAG,GAAG;AACvB,iBAAS,IAAI,KAAK,KAAK,UAAU,GAAG,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,CAAC,GAAG,IAAI,EAAE,QAAQ,IAAI,IAAI,WAAW,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG;AACxG,YAAE,CAAC,IAAI,EAAE,WAAW,CAAC;AACvB,UAAE,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,QAAQ,YAAY,CAAC,CAAC;AAAA,MAClD;AAAA,IACF,CAAC,GAAG,KAAK,YAAY,GAAG,KAAK,WAAW;AAAA,EAC1C;AAAA,EACA,YAAY;AACV,WAAO,oBAAoB,aAAa,KAAK,QAAQ,GAAG,OAAO,oBAAoB,WAAW,KAAK,SAAS,GAAG,OAAO,oBAAoB,aAAa,KAAK,QAAQ,GAAG,OAAO,oBAAoB,YAAY,KAAK,SAAS,GAAG,KAAK,YAAY;AAAA,EAClP;AACF,CAAC;AA72BD,IA62BI,IAAI;AAAA,EACN,KAAK;AAAA,EACL,OAAO;AACT;AAh3BA,IAg3BG,IAAI,CAAC,KAAK;AAh3Bb,IAg3BgB,IAAI,EAAE,OAAO,mBAAmB;AAh3BhD,IAg3BmD,IAAI,CAAC,KAAK;AAh3B7D,IAg3BgE,IAAI,EAAE,KAAK,EAAE;AAC7E,SAAS,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAC3B,SAAO,UAAE,GAAG,mBAAE,OAAO;AAAA,IACnB,OAAO;AAAA,IACP,KAAK;AAAA,IACL,aAAa,EAAE,EAAE,MAAM,EAAE,EAAE,IAAI,IAAI,MAAM,EAAE,YAAY,EAAE,SAAS,GAAG,CAAC;AAAA,IACtE,YAAY,EAAE,EAAE,MAAM,EAAE,EAAE,IAAI,IAAI,MAAM,EAAE,eAAe,EAAE,YAAY,GAAG,CAAC;AAAA,EAC7E,GAAG;AAAA,IACD,EAAE,QAAQ,UAAE,GAAG,mBAAE,OAAO,GAAG;AAAA,MACzB,eAAE,gBAAE,OAAO;AAAA,QACT,OAAO;AAAA,QACP,OAAO,eAAE;AAAA,UACP,OAAO,EAAE,YAAY;AAAA,UACrB,QAAQ,EAAE,aAAa;AAAA,UACvB,WAAW,WAAW,EAAE,QAAQ,MAAM,EAAE,QAAQ,mBAAmB,EAAE,IAAI,EAAE,QAAQ,QAAQ,EAAE,IAAI,EAAE,QAAQ,kBAAkB,EAAE,SAAS,KAAK;AAAA,QAC/I,CAAC;AAAA,MACH,GAAG;AAAA,QACD,gBAAE,OAAO;AAAA,UACP,KAAK,EAAE;AAAA,UACP,KAAK;AAAA,UACL,KAAK;AAAA,QACP,GAAG,MAAM,GAAG,CAAC;AAAA,MACf,GAAG,CAAC,GAAG;AAAA,QACL,CAAC,OAAG,CAAC,EAAE,OAAO;AAAA,MAChB,CAAC;AAAA,IACH,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,IACd,gBAAE,OAAO;AAAA,MACP,OAAO,eAAE,CAAC,oBAAoB,EAAE,gBAAgB,EAAE,QAAQ,CAAC,EAAE,MAAM,gBAAgB,EAAE,MAAM,iBAAiB,EAAE,SAAS,CAAC,CAAC;AAAA,MACzH,aAAa,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,IAAI,MAAM,EAAE,aAAa,EAAE,UAAU,GAAG,CAAC;AAAA,MACtE,cAAc,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,IAAI,MAAM,EAAE,aAAa,EAAE,UAAU,GAAG,CAAC;AAAA,IACzE,GAAG,MAAM,EAAE;AAAA,IACX,eAAE,gBAAE,OAAO;AAAA,MACT,OAAO;AAAA,MACP,OAAO,eAAE;AAAA,QACP,OAAO,EAAE,QAAQ;AAAA,QACjB,QAAQ,EAAE,QAAQ;AAAA,QAClB,WAAW,iBAAiB,EAAE,eAAe,QAAQ,EAAE,eAAe;AAAA,MACxE,CAAC;AAAA,IACH,GAAG;AAAA,MACD,gBAAE,QAAQ,GAAG;AAAA,QACX,gBAAE,OAAO;AAAA,UACP,OAAO,eAAE;AAAA,YACP,OAAO,EAAE,YAAY;AAAA,YACrB,QAAQ,EAAE,aAAa;AAAA,YACvB,WAAW,WAAW,EAAE,QAAQ,MAAM,EAAE,QAAQ,oBAAoB,EAAE,IAAI,EAAE,gBAAgB,EAAE,QAAQ,SAAS,EAAE,IAAI,EAAE,gBAAgB,EAAE,QAAQ,kBAAkB,EAAE,SAAS,KAAK;AAAA,UACrL,CAAC;AAAA,UACD,KAAK,EAAE;AAAA,UACP,KAAK;AAAA,QACP,GAAG,MAAM,IAAI,CAAC;AAAA,MAChB,CAAC;AAAA,MACD,gBAAE,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,aAAa,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,IAAI,MAAM,EAAE,YAAY,EAAE,SAAS,GAAG,CAAC;AAAA,QACpE,cAAc,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,IAAI,MAAM,EAAE,YAAY,EAAE,SAAS,GAAG,CAAC;AAAA,MACvE,GAAG,MAAM,EAAE;AAAA,MACX,EAAE,QAAQ,UAAE,GAAG,mBAAE,QAAQ;AAAA,QACvB,KAAK;AAAA,QACL,OAAO;AAAA,QACP,OAAO,eAAE,EAAE,KAAK,EAAE,SAAS,IAAI,CAAC;AAAA,MAClC,GAAG,gBAAE,EAAE,SAAS,KAAK,IAAI,QAAQ,gBAAE,EAAE,SAAS,MAAM,GAAG,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,MACrE,EAAE,WAAW,mBAAE,IAAI,IAAE,KAAK,UAAE,GAAG,mBAAE,QAAQ,GAAG;AAAA,QAC1C,gBAAE,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,aAAa,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,eAAe,GAAG,OAAI,MAAI,GAAG,CAAC;AAAA,UACpE,cAAc,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,eAAe,GAAG,OAAI,MAAI,GAAG,CAAC;AAAA,QACvE,GAAG,MAAM,EAAE;AAAA,QACX,gBAAE,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,aAAa,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,eAAe,GAAG,MAAI,OAAI,GAAG,CAAC;AAAA,UACpE,cAAc,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,eAAe,GAAG,MAAI,OAAI,GAAG,CAAC;AAAA,QACvE,GAAG,MAAM,EAAE;AAAA,QACX,gBAAE,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,aAAa,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,eAAe,GAAG,OAAI,MAAI,GAAG,CAAC;AAAA,UACpE,cAAc,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,eAAe,GAAG,OAAI,MAAI,GAAG,CAAC;AAAA,QACvE,GAAG,MAAM,EAAE;AAAA,QACX,gBAAE,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,aAAa,EAAE,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,eAAe,GAAG,MAAI,OAAI,GAAG,CAAC;AAAA,UACtE,cAAc,EAAE,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,eAAe,GAAG,MAAI,OAAI,GAAG,CAAC;AAAA,QACzE,GAAG,MAAM,EAAE;AAAA,QACX,gBAAE,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,aAAa,EAAE,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,eAAe,GAAG,MAAI,MAAI,GAAG,CAAC;AAAA,UACtE,cAAc,EAAE,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,eAAe,GAAG,MAAI,MAAI,GAAG,CAAC;AAAA,QACzE,GAAG,MAAM,EAAE;AAAA,QACX,gBAAE,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,aAAa,EAAE,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,eAAe,GAAG,OAAI,MAAI,GAAG,CAAC;AAAA,UACtE,cAAc,EAAE,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,eAAe,GAAG,OAAI,MAAI,GAAG,CAAC;AAAA,QACzE,GAAG,MAAM,EAAE;AAAA,QACX,gBAAE,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,aAAa,EAAE,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,eAAe,GAAG,MAAI,MAAI,GAAG,CAAC;AAAA,UACtE,cAAc,EAAE,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,eAAe,GAAG,MAAI,MAAI,GAAG,CAAC;AAAA,QACzE,GAAG,MAAM,EAAE;AAAA,QACX,gBAAE,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,aAAa,EAAE,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,eAAe,GAAG,MAAI,OAAI,GAAG,CAAC;AAAA,UACtE,cAAc,EAAE,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,eAAe,GAAG,MAAI,OAAI,GAAG,CAAC;AAAA,QACzE,GAAG,MAAM,EAAE;AAAA,QACX,gBAAE,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,aAAa,EAAE,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,eAAe,GAAG,MAAI,OAAI,GAAG,CAAC;AAAA,UACtE,cAAc,EAAE,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,eAAe,GAAG,MAAI,OAAI,GAAG,CAAC;AAAA,QACzE,GAAG,MAAM,EAAE;AAAA,QACX,gBAAE,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,aAAa,EAAE,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,eAAe,GAAG,MAAI,MAAI,GAAG,CAAC;AAAA,UACtE,cAAc,EAAE,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,eAAe,GAAG,MAAI,MAAI,GAAG,CAAC;AAAA,QACzE,GAAG,MAAM,EAAE;AAAA,QACX,gBAAE,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,aAAa,EAAE,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,eAAe,GAAG,OAAI,MAAI,GAAG,CAAC;AAAA,UACtE,cAAc,EAAE,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,eAAe,GAAG,OAAI,MAAI,GAAG,CAAC;AAAA,QACzE,GAAG,MAAM,EAAE;AAAA,QACX,gBAAE,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,aAAa,EAAE,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,eAAe,GAAG,MAAI,MAAI,GAAG,CAAC;AAAA,UACtE,cAAc,EAAE,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,eAAe,GAAG,MAAI,MAAI,GAAG,CAAC;AAAA,QACzE,GAAG,MAAM,EAAE;AAAA,MACb,CAAC;AAAA,IACH,GAAG,CAAC,GAAG;AAAA,MACL,CAAC,OAAG,EAAE,QAAQ;AAAA,IAChB,CAAC;AAAA,EACH,GAAG,GAAG;AACR;AACA,IAAM,IAAoB,EAAE,GAAG,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,aAAa,iBAAiB,CAAC,CAAC;AAAhF,IAAmF,IAAI,SAAS,GAAG;AACjG,IAAE,UAAU,cAAc,CAAC;AAC7B;AAFA,IAEG,IAAI;AAAA,EACL,SAAS;AAAA,EACT,SAAS;AAAA,EACT,YAAY;AACd;", "names": []}