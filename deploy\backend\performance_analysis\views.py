from performance_analysis.models import PerformanceAnalysis
from dvadmin.utils.serializers import CustomModelSerializer
from dvadmin.utils.viewset import CustomModelViewSet
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import AllowAny
from datetime import datetime
from django.db import connections
import logging

logger = logging.getLogger(__name__)


# 序列化器 - 用于查询操作
class PerformanceAnalysisSerializer(CustomModelSerializer):
    """
    绩效分析序列化器
    """
    class Meta:
        model = PerformanceAnalysis
        fields = "__all__"


# 序列化器 - 用于创建和更新操作
class PerformanceAnalysisCreateUpdateSerializer(CustomModelSerializer):
    """
    绩效分析创建/更新时的序列化器
    """
    class Meta:
        model = PerformanceAnalysis
        fields = '__all__'


# 视图集 - 处理CRUD操作
class PerformanceAnalysisViewSet(CustomModelViewSet):
    """
    绩效分析视图集
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    """
    queryset = PerformanceAnalysis.objects.all()
    serializer_class = PerformanceAnalysisSerializer
    create_serializer_class = PerformanceAnalysisCreateUpdateSerializer
    update_serializer_class = PerformanceAnalysisCreateUpdateSerializer
    permission_classes = [AllowAny]  # 允许所有用户访问

    # 可过滤的字段
    filter_fields = ['salesperson', 'region', 'query_type', 'query_year']
    # 可搜索的字段
    search_fields = ['salesperson', 'region']

    @action(methods=['POST'], detail=False, url_path='query')
    def performance_query(self, request):
        """
        业务员绩效分析查询接口
        支持按月度、季度、年度查询业务员销售绩效
        """
        try:
            # 获取查询参数
            data = request.data
            query_type = data.get('queryType', 'monthly')
            query_year = data.get('year', datetime.now().year)
            query_month = data.get('month', datetime.now().month)
            query_quarter = data.get('quarter', 1)
            salesperson_filter = data.get('salesperson', '')
            region_filter = data.get('region', '')
            
            # 根据查询类型构建不同的SQL
            if query_type == 'monthly':
                # 月度绩效查询
                sql_query = f"""
                SELECT 
                    ISNULL(t5.saler, '未维护') AS 业务员,
                    t3.地区2 AS 负责地区,
                    COUNT(DISTINCT t1.FID) AS 订单数量,
                    COUNT(DISTINCT t1.FCUSTID) AS 客户数量,
                    COUNT(DISTINCT t2.FMATERIALID) AS 产品种类,
                    ROUND(SUM(t2.FBASEUNITQTY), 2) AS 总销量,
                    ROUND(SUM(f.FALLAMOUNT), 2) AS 总销售金额,
                    ROUND(AVG(f.FALLAMOUNT / NULLIF(t2.FBASEUNITQTY, 0)), 4) AS 平均单价,
                    ROUND(SUM(f.FALLAMOUNT) / NULLIF(COUNT(DISTINCT t1.FCUSTID), 0), 2) AS 客户平均贡献,
                    ROUND(SUM(t2.FBASEUNITQTY) / NULLIF(COUNT(DISTINCT t1.FID), 0), 2) AS 单均销量,
                    CONVERT(VARCHAR(7), t1.FDATE, 120) AS 统计月份
                FROM 
                    T_SAL_ORDER t1 WITH (NOLOCK)
                JOIN
                    T_SAL_ORDERENTRY t2 WITH (NOLOCK) ON t1.FID = t2.FID
                JOIN
                    T_SAL_ORDERENTRY_F f WITH (NOLOCK) ON t2.FENTRYID = f.FENTRYID
                JOIN 
                    VIW_FI_COMM_DEPOSITORY t3 WITH (NOLOCK) ON t2.FSOSTOCKID = t3.FSTOCKID
                JOIN
                    VIW_FI_COMM_CUSTOMER4 t4 WITH (NOLOCK) ON t1.FCUSTID = t4.客户ID
                LEFT JOIN 
                    T_JSHL_CUSTSALER t5 WITH (NOLOCK) ON t4.客户名称 = t5.CUSTMER AND t5.FAREA = t3.地区2
                WHERE YEAR(t1.FDATE) = {query_year}
                    AND MONTH(t1.FDATE) = {query_month}
                    AND ISNULL(t2.F_JSHL_CHECKBOX_QTR, '0') != '1'
                    AND t2.FBASEUNITQTY > 0
                    AND f.FALLAMOUNT > 0
                    {f"AND t5.saler LIKE '%{salesperson_filter}%'" if salesperson_filter else ""}
                    {f"AND t3.地区2 LIKE '%{region_filter}%'" if region_filter else ""}
                GROUP BY ISNULL(t5.saler, '未维护'), t3.地区2, CONVERT(VARCHAR(7), t1.FDATE, 120)
                ORDER BY 总销售金额 DESC
                """
                
            elif query_type == 'quarterly':
                # 季度绩效查询
                quarter_months = {
                    1: "(1,2,3)",
                    2: "(4,5,6)", 
                    3: "(7,8,9)",
                    4: "(10,11,12)"
                }
                quarter_condition = quarter_months.get(query_quarter, "(1,2,3)")
                
                sql_query = f"""
                SELECT 
                    ISNULL(t5.saler, '未维护') AS 业务员,
                    t3.地区2 AS 负责地区,
                    'Q{query_quarter}' AS 季度,
                    COUNT(DISTINCT t1.FID) AS 季度订单数,
                    COUNT(DISTINCT t1.FCUSTID) AS 季度客户数,
                    ROUND(SUM(t2.FBASEUNITQTY), 2) AS 季度总销量,
                    ROUND(SUM(f.FALLAMOUNT), 2) AS 季度总销售金额,
                    ROUND(AVG(f.FALLAMOUNT / NULLIF(t2.FBASEUNITQTY, 0)), 4) AS 季度平均单价,
                    ROUND(SUM(f.FALLAMOUNT) / 3, 2) AS 月均销售金额,
                    {query_year} AS 统计年份
                FROM 
                    T_SAL_ORDER t1 WITH (NOLOCK)
                JOIN
                    T_SAL_ORDERENTRY t2 WITH (NOLOCK) ON t1.FID = t2.FID
                JOIN
                    T_SAL_ORDERENTRY_F f WITH (NOLOCK) ON t2.FENTRYID = f.FENTRYID
                JOIN 
                    VIW_FI_COMM_DEPOSITORY t3 WITH (NOLOCK) ON t2.FSOSTOCKID = t3.FSTOCKID
                JOIN
                    VIW_FI_COMM_CUSTOMER4 t4 WITH (NOLOCK) ON t1.FCUSTID = t4.客户ID
                LEFT JOIN 
                    T_JSHL_CUSTSALER t5 WITH (NOLOCK) ON t4.客户名称 = t5.CUSTMER AND t5.FAREA = t3.地区2
                WHERE YEAR(t1.FDATE) = {query_year}
                    AND MONTH(t1.FDATE) IN {quarter_condition}
                    AND ISNULL(t2.F_JSHL_CHECKBOX_QTR, '0') != '1'
                    AND t2.FBASEUNITQTY > 0
                    AND f.FALLAMOUNT > 0
                    {f"AND t5.saler LIKE '%{salesperson_filter}%'" if salesperson_filter else ""}
                    {f"AND t3.地区2 LIKE '%{region_filter}%'" if region_filter else ""}
                GROUP BY ISNULL(t5.saler, '未维护'), t3.地区2
                ORDER BY 季度总销售金额 DESC
                """
                
            else:  # yearly
                # 年度绩效查询
                sql_query = f"""
                SELECT 
                    ISNULL(t5.saler, '未维护') AS 业务员,
                    t3.地区2 AS 负责地区,
                    COUNT(DISTINCT t1.FID) AS 年度订单数,
                    COUNT(DISTINCT t1.FCUSTID) AS 年度客户数,
                    COUNT(DISTINCT t2.FMATERIALID) AS 年度产品种类,
                    ROUND(SUM(t2.FBASEUNITQTY), 2) AS 年度总销量,
                    ROUND(SUM(f.FALLAMOUNT), 2) AS 年度总销售金额,
                    ROUND(AVG(f.FALLAMOUNT / NULLIF(t2.FBASEUNITQTY, 0)), 4) AS 年度平均单价,
                    ROUND(SUM(f.FALLAMOUNT) / 12, 2) AS 月均销售金额,
                    ROUND(SUM(t2.FBASEUNITQTY) / 12, 2) AS 月均销量,
                    ROW_NUMBER() OVER (ORDER BY SUM(f.FALLAMOUNT) DESC) AS 销售金额排名,
                    ROW_NUMBER() OVER (ORDER BY SUM(t2.FBASEUNITQTY) DESC) AS 销量排名,
                    {query_year} AS 统计年份
                FROM 
                    T_SAL_ORDER t1 WITH (NOLOCK)
                JOIN
                    T_SAL_ORDERENTRY t2 WITH (NOLOCK) ON t1.FID = t2.FID
                JOIN
                    T_SAL_ORDERENTRY_F f WITH (NOLOCK) ON t2.FENTRYID = f.FENTRYID
                JOIN 
                    VIW_FI_COMM_DEPOSITORY t3 WITH (NOLOCK) ON t2.FSOSTOCKID = t3.FSTOCKID
                JOIN
                    VIW_FI_COMM_CUSTOMER4 t4 WITH (NOLOCK) ON t1.FCUSTID = t4.客户ID
                LEFT JOIN 
                    T_JSHL_CUSTSALER t5 WITH (NOLOCK) ON t4.客户名称 = t5.CUSTMER AND t5.FAREA = t3.地区2
                WHERE YEAR(t1.FDATE) = {query_year}
                    AND ISNULL(t2.F_JSHL_CHECKBOX_QTR, '0') != '1'
                    AND t2.FBASEUNITQTY > 0
                    AND f.FALLAMOUNT > 0
                    {f"AND t5.saler LIKE '%{salesperson_filter}%'" if salesperson_filter else ""}
                    {f"AND t3.地区2 LIKE '%{region_filter}%'" if region_filter else ""}
                GROUP BY ISNULL(t5.saler, '未维护'), t3.地区2
                ORDER BY 年度总销售金额 DESC
                """

            # 执行查询
            with connections['sqlserver'].cursor() as cursor:
                cursor.execute(sql_query)
                columns = [col[0] for col in cursor.description]
                results = cursor.fetchall()

                # 转换查询结果为字典列表
                performance_data = []
                for row in results:
                    row_dict = dict(zip(columns, row))
                    performance_data.append(row_dict)
            
            # 计算统计数据
            total_salespersons = len(set(item.get('业务员', '') for item in performance_data))

            # 根据查询类型使用不同的字段名
            if query_type == 'monthly':
                total_sales_amount = sum(float(item.get('总销售金额', 0) or 0) for item in performance_data)
                total_sales_quantity = sum(float(item.get('总销量', 0) or 0) for item in performance_data)
            elif query_type == 'quarterly':
                total_sales_amount = sum(float(item.get('季度总销售金额', 0) or 0) for item in performance_data)
                total_sales_quantity = sum(float(item.get('季度总销量', 0) or 0) for item in performance_data)
            else:  # yearly
                total_sales_amount = sum(float(item.get('年度总销售金额', 0) or 0) for item in performance_data)
                total_sales_quantity = sum(float(item.get('年度总销量', 0) or 0) for item in performance_data)
            
            # 构建响应数据
            response_data = {
                'code': 200,
                'message': '查询成功',
                'data': {
                    'queryType': query_type,
                    'queryParams': {
                        'year': query_year,
                        'month': query_month if query_type == 'monthly' else None,
                        'quarter': query_quarter if query_type == 'quarterly' else None,
                        'salesperson': salesperson_filter,
                        'region': region_filter
                    },
                    'statistics': {
                        'totalSalespersons': total_salespersons,
                        'totalSalesAmount': total_sales_amount,
                        'totalSalesQuantity': total_sales_quantity,
                        'averageSalesAmount': round(total_sales_amount / max(total_salespersons, 1), 2)
                    },
                    'list': performance_data
                }
            }
            
            return Response(response_data)
            
        except Exception as e:
            logger.error(f"绩效分析查询失败: {str(e)}")
            return Response({
                'code': 500,
                'message': f'查询失败: {str(e)}',
                'data': {
                    'statistics': {
                        'totalSalespersons': 0,
                        'totalSalesAmount': 0,
                        'totalSalesQuantity': 0,
                        'averageSalesAmount': 0
                    },
                    'list': []
                }
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['GET'], detail=False, url_path='salesperson-list')
    def get_salesperson_list(self, request):
        """获取所有业务员列表"""
        try:
            query = """
            SELECT DISTINCT
                ISNULL(t5.saler, '未维护') AS 业务员
            FROM
                T_SAL_ORDER t1 WITH (NOLOCK)
            JOIN
                T_SAL_ORDERENTRY t2 WITH (NOLOCK) ON t1.FID = t2.FID
            JOIN
                VIW_FI_COMM_DEPOSITORY t3 WITH (NOLOCK) ON t2.FSOSTOCKID = t3.FSTOCKID
            JOIN
                VIW_FI_COMM_CUSTOMER4 t4 WITH (NOLOCK) ON t1.FCUSTID = t4.客户ID
            LEFT JOIN
                T_JSHL_CUSTSALER t5 WITH (NOLOCK) ON t4.客户名称 = t5.CUSTMER AND t5.FAREA = t3.地区2
            WHERE t5.saler IS NOT NULL AND t5.saler != ''
            ORDER BY ISNULL(t5.saler, '未维护')
            """

            with connections['sqlserver'].cursor() as cursor:
                cursor.execute(query)
                results = cursor.fetchall()

                # 构建业务员列表
                salesperson_list = []
                for row in results:
                    salesperson_name = row[0]
                    if salesperson_name and salesperson_name != '未维护':
                        salesperson_list.append({
                            'label': salesperson_name,
                            'value': salesperson_name
                        })

            return Response({
                'code': 200,
                'message': '获取业务员列表成功',
                'data': salesperson_list
            })

        except Exception as e:
            logger.error(f"获取业务员列表失败: {e}")
            return Response({
                'code': 500,
                'message': f'获取业务员列表失败: {str(e)}',
                'data': []
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['GET'], detail=False, url_path='region-list')
    def get_region_list(self, request):
        """获取所有地区列表"""
        try:
            query = """
            SELECT DISTINCT t3.地区2 AS 地区
            FROM VIW_FI_COMM_DEPOSITORY t3 WITH (NOLOCK)
            WHERE t3.地区2 IS NOT NULL AND t3.地区2 != ''
            ORDER BY t3.地区2
            """

            with connections['sqlserver'].cursor() as cursor:
                cursor.execute(query)
                results = cursor.fetchall()

                # 构建地区列表
                region_list = []
                for row in results:
                    region_name = row[0]
                    if region_name:
                        region_list.append({
                            'label': region_name,
                            'value': region_name
                        })

            return Response({
                'code': 200,
                'message': '获取地区列表成功',
                'data': region_list
            })

        except Exception as e:
            logger.error(f"获取地区列表失败: {e}")
            return Response({
                'code': 500,
                'message': f'获取地区列表失败: {str(e)}',
                'data': []
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
