import{u as x,b as k}from"./api.BEBRHkAk.js";import{J as U}from"./index.BHZI5pdK.js";import{d as I,h as V,g as b,j,k as d,a as N,o as R,l,u as a,w as n,q as y}from"./vue.BNx9QYep.js";import{_ as B}from"./_plugin-vue_export-helper.DlAUqK2U.js";const M={class:"columns-form-com"},O=I({__name:"index",props:{currentInfo:{type:Object,required:!0,default:()=>{}},initFormData:{type:Object,default:()=>{}}},emits:["drawerClose"],setup(C,{emit:D}){const o=C,g=D,m=V(),F=b({field_name:[{required:!0,message:"请输入字段名！",trigger:"blur"}],title:[{required:!0,message:"请输入列名！",trigger:"blur"}]});let t=b({field_name:"",title:"",is_create:!0,is_update:!0,is_query:!0}),f=V(!1);const q=()=>{var u,e;(u=o.initFormData)!=null&&u.id&&(t.id=((e=o.initFormData)==null?void 0:e.id)||"",t.field_name=o.initFormData.field_name||"",t.title=o.initFormData.title||"",t.is_create=!!o.initFormData.is_create,t.is_update=!!o.initFormData.is_update,t.is_query=!!o.initFormData.is_query)},v=()=>{var u;(u=m.value)==null||u.validate(async e=>{if(e)try{f.value=!0;let i;t.id?i=await x({...t,...o.currentInfo}):i=await k({...t,...o.currentInfo}),(i==null?void 0:i.code)===2e3&&(U(i.msg),p("submit"))}finally{f.value=!1}})},p=(u="")=>{var e;g("drawerClose",u),(e=m.value)==null||e.resetFields()};return j(()=>{q()}),(u,e)=>{const i=d("el-input"),s=d("el-form-item"),_=d("el-switch"),c=d("el-button"),w=d("el-form");return R(),N("div",M,[l(w,{ref_key:"formRef",ref:m,model:a(t),rules:F,"label-width":"80px"},{default:n(()=>[l(s,{label:"字段名",prop:"field_name"},{default:n(()=>[l(i,{modelValue:a(t).field_name,"onUpdate:modelValue":e[0]||(e[0]=r=>a(t).field_name=r),placeholder:"请输入字段名"},null,8,["modelValue"])]),_:1}),l(s,{label:"列名",prop:"title"},{default:n(()=>[l(i,{modelValue:a(t).title,"onUpdate:modelValue":e[1]||(e[1]=r=>a(t).title=r),placeholder:"请输入列名"},null,8,["modelValue"])]),_:1}),l(s,{label:"创建显示"},{default:n(()=>[l(_,{modelValue:a(t).is_create,"onUpdate:modelValue":e[2]||(e[2]=r=>a(t).is_create=r)},null,8,["modelValue"])]),_:1}),l(s,{label:"编辑显示"},{default:n(()=>[l(_,{modelValue:a(t).is_update,"onUpdate:modelValue":e[3]||(e[3]=r=>a(t).is_update=r)},null,8,["modelValue"])]),_:1}),l(s,{label:"查询显示"},{default:n(()=>[l(_,{modelValue:a(t).is_query,"onUpdate:modelValue":e[4]||(e[4]=r=>a(t).is_query=r)},null,8,["modelValue"])]),_:1}),l(s,null,{default:n(()=>[l(c,{type:"primary",onClick:v,loading:a(f)},{default:n(()=>e[5]||(e[5]=[y(" 确定 ")])),_:1,__:[5]},8,["loading"]),l(c,{onClick:p},{default:n(()=>e[6]||(e[6]=[y("取消")])),_:1,__:[6]})]),_:1})]),_:1},8,["model","rules"])])}}}),T=B(O,[["__scopeId","data-v-7c3f6e9f"]]);export{T as default};
