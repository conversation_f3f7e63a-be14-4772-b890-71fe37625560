import{d as z,h as r,g as J,c as M,j as P,k as s,a as w,o as b,l,e as g,w as o,A as Q,D as G,q as p,s as x,F as H,p as K,u as h,x as W}from"./vue.BNx9QYep.js";import{a as Y,c as Z,b as ee,J as te,a1 as le,E as oe,X as D,e as ae}from"./index.BHZI5pdK.js";import{g as ne,B as se,c as ie}from"./crud.P6CIYdWQ.js";import{a as ce}from"./api.BEBRHkAk.js";import{_ as de}from"./_plugin-vue_export-helper.DlAUqK2U.js";import"./authFunction.D3Be3hRy.js";const ue={class:"model-card"},re=["value"],pe={class:"dialog-footer"},me={style:{height:"72vh"}},fe=z({__name:"index",setup(_e,{expose:V}){let m=r({name:null});const n=J({model:"",app:"",menu:""}),d=r(!1),f=r([]),R=r(null),T=(e,t)=>{R.value=t,n.model=e.key,n.app=e.app},_=r(""),E=M(()=>{if(!_.value)return f.value;const e=_.value.toLowerCase();return f.value.filter(t=>t.app.toLowerCase().includes(e)||t.title.toLowerCase().includes(e)||t.key.toLowerCase().includes(e))}),L=e=>{!e.is_catalog&&e.id?(m.value=e,c.doRefresh()):c.setTableData([])},N=async()=>{if(n.menu=m.value.id,d.value=!1,n.menu&&n.model){const e=await ce(n);(e==null?void 0:e.code)===2e3&&te("匹配成功"),c.doSearch({form:{menu:n.menu,model:n.model}})}else le("请选择角色和模型表！")},y=M(()=>i.value.length),S=async()=>{await oe.confirm(`确定要批量删除这${i.value.length}条记录吗`,"确认",{distinguishCancelAndClose:!0,confirmButtonText:"确定",cancelButtonText:"取消",closeOnClickModal:!1}),await se(D.pluck(i.value,"id")),ae.info("删除成功"),i.value=[],await c.doRefresh()},$=e=>{const t=c.getBaseTableRef(),C=c.getTableData();D.pluck(C,"id").includes(e.id)?t.toggleRowSelection(e,!1):i.value=D.remove(i.value,k=>k.id!==e.id)},{crudBinding:A,crudRef:F,crudExpose:c,selectedRows:i}=Y({createCrudOptions:ie,props:n,modelDialog:d,selectOptions:m,allModelData:f});return P(async()=>{const e=await ne();f.value=e.data}),V({selectOptions:m,handleRefreshTable:L}),(e,t)=>{const C=s("el-tag"),k=s("el-input"),O=s("el-text"),u=s("el-button"),U=s("el-dialog"),q=s("el-tooltip"),B=s("el-table-column"),I=s("el-table"),X=s("el-popover"),j=s("fs-crud");return b(),w("div",null,[l(U,{ref:"modelRef",modelValue:d.value,"onUpdate:modelValue":t[2]||(t[2]=a=>d.value=a),title:"选择model"},{footer:o(()=>[g("span",pe,[l(u,{onClick:t[1]||(t[1]=a=>d.value=!1)},{default:o(()=>t[3]||(t[3]=[p("取消")])),_:1,__:[3]}),l(u,{type:"primary",onClick:N},{default:o(()=>t[4]||(t[4]=[p(" 确定 ")])),_:1,__:[4]})])]),default:o(()=>[Q(g("div",null,[l(C,null,{default:o(()=>[p("已选择:"+x(n.model),1)]),_:1})],512),[[G,n.model]]),l(k,{modelValue:_.value,"onUpdate:modelValue":t[0]||(t[0]=a=>_.value=a),placeholder:"搜索模型...",style:{"margin-bottom":"10px"}},null,8,["modelValue"]),g("div",ue,[(b(!0),w(H,null,K(E.value,(a,v)=>(b(),w("div",{value:a.key,key:v},[l(O,{type:R.value===v?"primary":"",onClick:ve=>T(a,v)},{default:o(()=>[p(x(a.app+"--"+a.title+"("+a.key+")"),1)]),_:2},1032,["type","onClick"])],8,re))),128))])]),_:1},8,["modelValue"]),g("div",me,[l(j,W({ref_key:"crudRef",ref:F},h(A)),{"pagination-left":o(()=>[l(q,{content:"批量删除"},{default:o(()=>[l(u,{text:"",type:"danger",disabled:y.value===0,icon:h(ee),circle:"",onClick:S},null,8,["disabled","icon"])]),_:1})]),"pagination-right":o(()=>[l(X,{placement:"top",width:400,trigger:"click"},{reference:o(()=>[l(u,{text:"",type:y.value>0?"primary":""},{default:o(()=>[p("已选中"+x(y.value)+"条数据",1)]),_:1},8,["type"])]),default:o(()=>[l(I,{data:h(i),size:"small"},{default:o(()=>[l(B,{width:"150",property:"id",label:"id"}),l(B,{fixed:"right",label:"操作","min-width":"60"},{default:o(a=>[l(u,{text:"",type:"info",icon:h(Z),onClick:v=>$(a.row),circle:""},null,8,["icon","onClick"])]),_:1})]),_:1},8,["data"])]),_:1})]),_:1},16)])])}}}),be=de(fe,[["__scopeId","data-v-5d32cfca"]]);export{be as default};
