<template>
  <div class="price-analysis-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>价格分析中心</h2>
      <p>监控价格变化和市场趋势，及时发现价格异常客户</p>
    </div>

    <!-- 查询条件 -->
    <el-card class="query-card" shadow="never">
      <el-form :model="queryForm" inline>
        <el-form-item label="查询年份">
          <el-select v-model="queryForm.year" placeholder="选择年份" style="width: 120px">
            <el-option 
              v-for="year in yearOptions" 
              :key="year" 
              :label="year + '年'" 
              :value="year"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="查询月份">
          <el-select v-model="queryForm.month" placeholder="选择月份" style="width: 120px">
            <el-option 
              v-for="month in monthOptions" 
              :key="month" 
              :label="month + '月'" 
              :value="month"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="偏离阈值">
          <el-select v-model="queryForm.threshold" placeholder="选择阈值" style="width: 140px">
            <el-option label="10%以上" :value="0.1" />
            <el-option label="20%以上" :value="0.2" />
            <el-option label="30%以上" :value="0.3" />
            <el-option label="50%以上" :value="0.5" />
          </el-select>
        </el-form-item>

        <el-form-item label="公司筛选">
          <el-select v-model="queryForm.company" placeholder="选择公司" style="width: 240px" clearable>
            <el-option label="全部公司" value="" />
            <el-option label="江苏华绿生物科技集团股份有限公司" value="江苏华绿生物科技集团股份有限公司" />
            <el-option label="江苏华骏生物科技有限公司" value="江苏华骏生物科技有限公司" />
            <el-option label="江苏华蕈生物科技有限公司" value="江苏华蕈生物科技有限公司" />
            <el-option label="泗阳华盛生物科技有限公司" value="泗阳华盛生物科技有限公司" />
            <el-option label="泗阳华茂生物科技有限公司" value="泗阳华茂生物科技有限公司" />
            <el-option label="浙江华实生物科技有限公司" value="浙江华实生物科技有限公司" />
          </el-select>
        </el-form-item>

        <el-form-item label="物料大类">
          <el-select v-model="queryForm.materialCategory" placeholder="选择物料大类" style="width: 180px" clearable>
            <el-option label="全部大类" value="" />
            <el-option label="7001(金针菇)" value="7001" />
            <el-option label="7002(白玉菇)" value="7002" />
            <el-option label="7003(蟹味菇)" value="7003" />
            <el-option label="7004(白玉蟹味双拼)" value="7004" />
            <el-option label="7005(舞茸)" value="7005" />
            <el-option label="7006(灰树花)" value="7006" />
            <el-option label="7007(鹿茸菇)" value="7007" />
            <el-option label="7008(华之珍)" value="7008" />
            <el-option label="7009(海鲜菇)" value="7009" />
            <el-option label="7010(虫草花)" value="7010" />
            <el-option label="7011(其他外购菇)" value="7011" />
            <el-option label="7012(有机肥)" value="7012" />
            <el-option label="7013(双孢菇)" value="7013" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleQuery" :loading="loading">
            <el-icon><Search /></el-icon>
            查询分析
          </el-button>
          <el-button @click="handleExport" :disabled="!tableData.length">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon sales">
                <el-icon size="32"><TrendCharts /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-title">异常客户数</div>
                <div class="stat-value">{{ statistics.abnormalCustomers }}</div>
                <div class="stat-desc">价格偏离客户总数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon customer">
                <el-icon size="32"><User /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-title">高风险客户</div>
                <div class="stat-value">{{ statistics.highRiskCustomers }}</div>
                <div class="stat-desc">严重价格偏离客户</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon price">
                <el-icon size="32"><PriceTag /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-title">涉及物料数</div>
                <div class="stat-value">{{ statistics.materialCount }}</div>
                <div class="stat-desc">存在价格异常的物料</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon amount">
                <el-icon size="32"><Money /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-title">影响金额</div>
                <div class="stat-value">{{ formatCurrency(statistics.totalAmount) }}</div>
                <div class="stat-desc">异常客户购买总额</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>价格异常客户明细</span>
          <div class="header-actions">
            <el-tag v-if="queryForm.year && queryForm.month" type="info">
              {{ queryForm.year }}年{{ queryForm.month }}月数据
            </el-tag>
            <el-tag v-if="tableData.length" type="success">
              共{{ tableData.length }}条异常记录
            </el-tag>
          </div>
        </div>
      </template>
      
      <el-table
        :data="paginatedData"
        v-loading="loading"
        stripe
        border
        height="500"
        style="width: 100%"
      >
        <el-table-column prop="公司简称" label="公司" width="200" fixed="left" show-overflow-tooltip />
        <el-table-column prop="物料名称" label="物料名称" width="200" show-overflow-tooltip />
        <el-table-column prop="客户名称" label="客户名称" width="200" show-overflow-tooltip />
        <el-table-column prop="客户平均单价" label="客户单价" width="100" align="right" />
        <el-table-column prop="市场平均单价" label="市场单价" width="100" align="right" />
        <el-table-column prop="偏离百分比" label="偏离幅度" width="100" align="center">
          <template #default="scope">
            <el-tag 
              :type="getDeviationTagType(scope.row.偏离百分比)"
              size="small"
            >
              {{ scope.row.偏离百分比 }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="偏离等级" label="风险等级" width="120" align="center">
          <template #default="scope">
            <el-tag 
              :type="getRiskTagType(scope.row.偏离等级)"
              size="small"
            >
              {{ scope.row.偏离等级 }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="偏离方向" label="偏离方向" width="100" align="center" />
        <el-table-column prop="购买总额" label="购买金额" width="120" align="right" />
        <el-table-column prop="风险提示" label="风险提示" width="150" align="center">
          <template #default="scope">
            <el-tag
              v-if="scope.row.风险提示 !== '正常'"
              type="warning"
              size="small"
            >
              {{ scope.row.风险提示 }}
            </el-tag>
            <span v-else class="text-success">{{ scope.row.风险提示 }}</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <div class="pagination-container" v-if="tableData.length > 0">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :small="false"
          :disabled="loading"
          :background="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.length"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts" name="PriceAnalysis">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Download,
  TrendCharts,
  User,
  PriceTag,
  Money
} from '@element-plus/icons-vue'
import {
  priceAnalysisQuery,
  priceAnalysisExport,
  formatCurrency,
  getDeviationTagType,
  getRiskTagType
} from '/@/api/price-analysis/index'

// 响应式数据
const loading = ref(false)
const tableData = ref([])

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 20
})

// 查询表单
const queryForm = reactive({
  year: new Date().getFullYear(),
  month: new Date().getMonth() + 1,
  threshold: 0.1,
  company: '',
  materialCategory: ''
})

// 统计数据
const statistics = reactive({
  abnormalCustomers: 0,
  highRiskCustomers: 0,
  materialCount: 0,
  totalAmount: 0
})

// 年份选项
const yearOptions = ref([])
const monthOptions = ref([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12])

// 计算属性：分页数据
const paginatedData = computed(() => {
  const start = (pagination.currentPage - 1) * pagination.pageSize
  const end = start + pagination.pageSize
  return tableData.value.slice(start, end)
})

// 初始化年份选项
const initYearOptions = () => {
  const currentYear = new Date().getFullYear()
  for (let i = currentYear; i >= currentYear - 5; i--) {
    yearOptions.value.push(i)
  }
}

// 这些函数现在从API模块导入

// 查询数据
const handleQuery = async () => {
  loading.value = true
  try {
    const response = await priceAnalysisQuery(queryForm)

    if (response.code === 200) {
      tableData.value = response.data.list || []

      // 重置分页到第一页
      pagination.currentPage = 1

      // 更新统计数据
      const stats = response.data.statistics || {}
      statistics.abnormalCustomers = stats.abnormalCustomers || 0
      statistics.highRiskCustomers = stats.highRiskCustomers || 0
      statistics.materialCount = stats.materialCount || 0
      statistics.totalAmount = stats.totalAmount || 0

      ElMessage.success(`查询完成，共${tableData.value.length}条记录`)
    } else {
      ElMessage.error(response.message || '查询失败')
    }

  } catch (error) {
    console.error('查询失败:', error)
    ElMessage.error('查询失败：' + (error.message || '网络错误'))
  } finally {
    loading.value = false
  }
}

// 导出数据
const handleExport = async () => {
  if (!tableData.value.length) {
    ElMessage.warning('没有数据可导出，请先查询数据')
    return
  }

  try {
    ElMessage.info('正在生成Excel文件，请稍候...')

    const response = await priceAnalysisExport(queryForm)

    // 当responseType为blob时，axios拦截器会直接返回response对象
    if (response && response.data instanceof Blob) {
      const url = window.URL.createObjectURL(response.data)
      const link = document.createElement('a')
      link.href = url

      // 从响应头获取文件名，如果没有则生成默认文件名
      let filename = `价格分析报告_${queryForm.year}年${queryForm.month}月_${new Date().toISOString().slice(0, 10)}.xlsx`

      const contentDisposition = response.headers['content-disposition']
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
        if (filenameMatch && filenameMatch[1]) {
          filename = filenameMatch[1].replace(/['"]/g, '')
          // 处理UTF-8编码的文件名
          if (filename.startsWith('=?utf-8?b?')) {
            try {
              const base64 = filename.substring(10, filename.length - 2)
              filename = decodeURIComponent(escape(atob(base64)))
            } catch (e) {
              console.warn('文件名解码失败，使用默认文件名')
            }
          }
        }
      }

      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      window.URL.revokeObjectURL(url)
      ElMessage.success('Excel文件导出成功')
    } else {
      ElMessage.error('导出失败：响应格式错误')
    }
  } catch (error) {
    console.error('导出失败:', error)

    // 处理错误响应
    if (error.response) {
      if (error.response.data instanceof Blob) {
        // 如果错误响应是blob，尝试读取错误信息
        try {
          const text = await error.response.data.text()
          const errorData = JSON.parse(text)
          ElMessage.error(errorData.message || '导出失败')
        } catch {
          ElMessage.error('导出失败：服务器错误')
        }
      } else {
        ElMessage.error(error.response.data?.message || '导出失败')
      }
    } else {
      ElMessage.error('导出失败：' + (error.message || '网络错误'))
    }
  }
}

// 查看详情
const viewDetail = (row) => {
  ElMessageBox.alert(
    `客户：${row.客户名称}\n物料：${row.物料名称}\n偏离幅度：${row.偏离百分比}\n风险等级：${row.偏离等级}`,
    '客户价格详情',
    { confirmButtonText: '确定' }
  )
}

// 分页事件处理
const handleSizeChange = (newSize) => {
  pagination.pageSize = newSize
  pagination.currentPage = 1
}

const handleCurrentChange = (newPage) => {
  pagination.currentPage = newPage
}

// 组件挂载时初始化
onMounted(() => {
  initYearOptions()
  // 默认加载上个月数据
  const lastMonth = new Date()
  lastMonth.setMonth(lastMonth.getMonth() - 1)
  queryForm.year = lastMonth.getFullYear()
  queryForm.month = lastMonth.getMonth() + 1
  handleQuery()
})
</script>

<style scoped>
.price-analysis-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.query-card {
  margin-bottom: 20px;
}

.query-card :deep(.el-card__body) {
  padding: 20px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  height: 120px;
}

.stat-card :deep(.el-card__body) {
  padding: 20px;
  height: 100%;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
}

.stat-icon.sales {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.customer {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.price {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.amount {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
  line-height: 1;
}

.stat-desc {
  font-size: 12px;
  color: #C0C4CC;
}

.table-card {
  background: white;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.text-success {
  color: #67C23A;
  font-weight: 500;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px 0;
}

/* 表格样式优化 */
:deep(.el-table) {
  font-size: 13px;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table td) {
  padding: 8px 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-cards .el-col {
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .price-analysis-container {
    padding: 10px;
  }

  .query-card :deep(.el-form--inline .el-form-item) {
    display: block;
    margin-bottom: 10px;
  }

  .stat-content {
    flex-direction: column;
    text-align: center;
  }

  .stat-icon {
    margin-right: 0;
    margin-bottom: 10px;
  }
}
</style>
