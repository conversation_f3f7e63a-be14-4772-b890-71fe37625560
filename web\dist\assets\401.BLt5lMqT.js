import{d as m,M as r,c as d,k as f,a as g,o as p,e,s as i,l as u,w as _,q as h,n as v}from"./vue.BNx9QYep.js";import{u as w,T,S as C}from"./index.BHZI5pdK.js";import{_ as b}from"./_plugin-vue_export-helper.DlAUqK2U.js";const F=m({name:"401",setup(){const s=w(),t=T(),{themeConfig:a}=r(s),{isTagsViewCurrenFull:n}=r(t),l=()=>{C.clear(),window.location.reload()},c=d(()=>{let{isTagsview:o}=a.value;return n.value?"30px":o?"114px":"80px"});return{onSetAuth:l,initTagViewHeight:c}}}),V={class:"error-flex"},$={class:"left"},x={class:"left-item"},y={class:"left-item-animation left-item-title"},A={class:"left-item-animation left-item-msg"},k={class:"left-item-animation left-item-btn"};function S(s,t,a,n,l,c){const o=f("el-button");return p(),g("div",{class:"error layout-view-bg-white",style:v({height:`calc(100vh - ${s.initTagViewHeight}`})},[e("div",V,[e("div",$,[e("div",x,[t[0]||(t[0]=e("div",{class:"left-item-animation left-item-num"},"401",-1)),e("div",y,i(s.$t("message.noAccess.accessTitle")),1),e("div",A,i(s.$t("message.noAccess.accessMsg")),1),e("div",k,[u(o,{type:"primary",round:"",onClick:s.onSetAuth},{default:_(()=>[h(i(s.$t("message.noAccess.accessBtn")),1)]),_:1},8,["onClick"])])])]),t[1]||(t[1]=e("div",{class:"right"},[e("img",{src:"https://img-blog.csdnimg.cn/3333f265772a4fa89287993500ecbf96.png?x-oss-process=image/watermark,type_d3F5LXplbmhlaQ,shadow_50,text_Q1NETiBAbHl0LXRvcA==,size_16,color_FFFFFF,t_70,g_se,x_16"})],-1))])],4)}const H=b(F,[["render",S],["__scopeId","data-v-50910138"]]);export{H as default};
