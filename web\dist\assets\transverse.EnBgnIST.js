const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/header.DFPmrsWF.js","assets/index.BHZI5pdK.js","assets/vue.BNx9QYep.js","assets/index.Dg-OhEXY.css","assets/main.6V4YA0vm.js"])))=>i.map(i=>d[i]);
import{u as d,_ as a}from"./index.BHZI5pdK.js";import{d as l,h as y,M as h,Q as R,j as T,v as n,k as v,b as C,o as M,w as k,l as s,u as r,P as c,I as b}from"./vue.BNx9QYep.js";const x=l({name:"layoutTransverse"}),S=l({...x,setup(g){const u=c(()=>a(()=>import("./header.DFPmrsWF.js"),__vite__mapDeps([0,1,2,3]))),_=c(()=>a(()=>import("./main.6V4YA0vm.js"),__vite__mapDeps([4,1,2,3]))),e=y(),i=d(),{themeConfig:f}=h(i),p=R(),o=()=>{e.value.layoutMainScrollbarRef.update()},t=()=>{b(()=>{setTimeout(()=>{o(),e.value.layoutMainScrollbarRef.wrapRef.scrollTop=0},500)})};return T(()=>{t()}),n(()=>p.path,()=>{t()}),n(f,()=>{o()},{deep:!0}),(w,E)=>{const m=v("el-container");return M(),C(m,{class:"layout-container flex-center layout-backtop"},{default:k(()=>[s(r(u)),s(r(_),{ref_key:"layoutMainRef",ref:e},null,512)]),_:1})}}});export{S as default};
