const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.DlRqN6Za.js","assets/index.BHZI5pdK.js","assets/vue.BNx9QYep.js","assets/index.Dg-OhEXY.css","assets/crud.DMN86VyI.js"])))=>i.map(i=>d[i]);
import{a as T,b as z,c as q,_ as F,X as f,N as M,E as O,J as X}from"./index.BHZI5pdK.js";import{d as $,h as b,c as I,k as r,b as J,o as L,w as e,l as t,x as j,u as a,A as G,q as i,D as H,s as h,P as K,e as Q}from"./vue.BNx9QYep.js";import{a as W}from"./authFunction.D3Be3hRy.js";import{r as Y,c as Z}from"./crud.DBBATLVN.js";import{R as ee}from"./RoleUserStores.B27TR7Mx.js";const ce=$({__name:"index",setup(te,{expose:w}){const d=ee(),R=K(()=>F(()=>import("./index.DlRqN6Za.js"),__vite__mapDeps([0,1,2,3,4]))),m=b(),x=()=>{n.doRefresh()},k=b(!1),y=l=>{o.value=[],l()},u=I(()=>o.value.length),D=l=>{const s=n.getBaseTableRef(),_=n.getTableData();f.pluck(_,"id").includes(l.id)?s.toggleRowSelection(l,!1):o.value=f.remove(o.value,c=>c.id!==l.id)},C=async()=>{if(o.value.length<1){M("请先勾选用户");return}await O.confirm(`确定要删除这 “${o.value.length}” 位用户的权限吗`,"确认");const l=await Y(g.value.getSearchFormData().role_id,f.pluck(o.value,"id"));o.value=[],X(l.msg),n.doRefresh()},{crudBinding:V,crudRef:g,crudExpose:n,selectedRows:o}=T({createCrudOptions:Z,context:{subUserRef:m}}),{setSearchFormData:U,doRefresh:S}=n;return w({drawer:k,setSearchFormData:U,doRefresh:S}),(l,s)=>{const _=r("el-tag"),c=r("el-button"),v=r("el-table-column"),B=r("el-table"),E=r("el-popover"),N=r("el-tooltip"),A=r("fs-crud"),P=r("el-drawer");return L(),J(P,{size:"70%",modelValue:a(d).drawerVisible,"onUpdate:modelValue":s[0]||(s[0]=p=>a(d).drawerVisible=p),direction:"rtl","destroy-on-close":"","before-close":y},{header:e(()=>[Q("div",null,[s[1]||(s[1]=i(" 当前授权角色： ")),t(_,null,{default:e(()=>[i(h(a(d).role_name),1)]),_:1})])]),default:e(()=>[t(A,j({ref_key:"crudRef",ref:g},a(V)),{"pagination-right":e(()=>[t(E,{placement:"top",width:200,trigger:"click"},{reference:e(()=>[t(c,{text:"",type:u.value>0?"primary":""},{default:e(()=>[i("已选中"+h(u.value)+"条数据",1)]),_:1},8,["type"])]),default:e(()=>[t(B,{data:a(o),size:"small","max-height":500},{default:e(()=>[t(v,{width:"100",property:"name",label:"用户名"}),t(v,{fixed:"right",label:"操作","min-width":"60"},{default:e(p=>[t(c,{text:"",type:"info",icon:a(q),onClick:oe=>D(p.row),circle:""},null,8,["icon","onClick"])]),_:1})]),_:1},8,["data"])]),_:1})]),"pagination-left":e(()=>[t(N,{content:"批量删除所选择的用户权限"},{default:e(()=>[G(t(c,{type:"danger",onClick:C,icon:a(z)},{default:e(()=>s[2]||(s[2]=[i("批量删除")])),_:1,__:[2]},8,["icon"]),[[H,u.value>0&&a(W)("role:AuthorizedDel")]])]),_:1})]),_:1},16),t(a(R),{ref_key:"subUserRef",ref:m,refreshCallback:x},null,512)]),_:1},8,["modelValue"])}}});export{ce as default};
