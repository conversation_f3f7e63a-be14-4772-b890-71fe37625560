import{d as ee,h as x,g as T,j as te,k as u,y as se,a as D,o as z,l as e,e as s,w as a,s as _,q as b,u as f,F as ae,p as le,b as M,_ as oe,A as ne,I as ie}from"./vue.BNx9QYep.js";import{r as E,n as re,ai as de,aj as ce,x as ue,ak as pe,al as _e,am as fe,an as me,e as w,E as he}from"./index.BHZI5pdK.js";import{i as ge}from"./echarts.BiCAFTQd.js";import{_ as ve}from"./_plugin-vue_export-helper.DlAUqK2U.js";const N="/api/customer-analysis/";function we(i){return E({url:N+"query/",method:"post",data:i})}function ye(i){return E({url:N+"export/",method:"post",data:i,responseType:"blob",timeout:6e4})}function O(i,C=2){return i==null||isNaN(i)?"0.00":i.toLocaleString("zh-CN",{minimumFractionDigits:C,maximumFractionDigits:C})}function A(i){return{今天下单:"success",昨天下单:"success",活跃客户:"success",一般客户:"warning",沉睡客户:"danger",流失客户:"danger",从未下单:"info"}[i]||"info"}function q(i){return{低:"success",中:"warning",高:"danger"}[i]||"info"}function be(){return E({url:N+"companies/",method:"get"})}const Ce={class:"customer-analysis-container"},ke={class:"card-header"},xe={class:"header-info"},ze={class:"update-time"},Se={class:"filter-form"},De={class:"filter-item"},Le={class:"filter-item"},Re={class:"filter-item"},Ve={class:"filter-actions"},Te={class:"stats-section"},Ee={class:"stat-content"},Ne={class:"stat-icon"},Pe={class:"stat-info"},Ue={class:"stat-value"},Be={class:"stat-content"},Fe={class:"stat-icon"},je={class:"stat-info"},Me={class:"stat-value"},Oe={class:"stat-content"},Ae={class:"stat-icon"},qe={class:"stat-info"},Ie={class:"stat-value"},We={class:"stat-content"},Ke={class:"stat-icon"},Qe={class:"stat-info"},$e={class:"stat-value"},Ge={class:"card-header"},He={class:"table-info"},Je={key:0},Xe={key:1},Ye={class:"pagination-container"},Ze=ee({__name:"index",setup(i,{expose:C}){const k=x(!1),L=x(!1),P=x(""),R=x(),r=T({thresholdDays:7,company:"",customerName:""}),d=T({currentPage:1,pageSize:20,total:0}),y=T({totalCustomers:0,highRiskCustomers:0,mediumRiskCustomers:0,lowRiskCustomers:0,statusStats:{}}),V=x([]),U=x([]);let S=null;const p=async()=>{k.value=!0;try{const o={...r,page:d.currentPage,pageSize:d.pageSize},t=await we(o);if(t.code===200){const n=t.data;Object.assign(y,n.statistics),V.value=n.list||[],d.total=n.total||0,P.value=new Date().toLocaleString(),await ie(),G(),w.success("查询成功")}else w.error(t.message||"查询失败")}catch(o){console.error("查询失败:",o),w.error("查询失败，请稍后重试")}finally{k.value=!1}},I=()=>{r.thresholdDays=7,r.company="",r.customerName="",d.currentPage=1,p()},W=o=>{d.pageSize=o,d.currentPage=1,p()},K=o=>{d.currentPage=o,p()},Q=()=>{p()},$=async()=>{if(V.value.length===0){w.warning("没有数据可以导出");return}try{await he.confirm("确认导出客户分析数据？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}),L.value=!0;const o=await ye(r),t=new Blob([o],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),n=window.URL.createObjectURL(t),c=document.createElement("a");c.href=n;const m=new Date().toISOString().slice(0,19).replace(/:/g,"-");c.download=`客户分析报告_${r.thresholdDays}天阈值_${m}.xlsx`,document.body.appendChild(c),c.click(),document.body.removeChild(c),window.URL.revokeObjectURL(n),w.success("导出成功")}catch(o){o!=="cancel"&&(console.error("导出失败:",o),w.error("导出失败，请稍后重试"))}finally{L.value=!1}},G=()=>{if(!R.value)return;S||(S=ge(R.value));const o=Object.entries(y.statusStats).map(([n,c])=>({name:n,value:c})),t={title:{text:"客户状态分布",left:"center",textStyle:{fontSize:16,fontWeight:"bold"}},tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left",top:"middle"},series:[{name:"客户状态",type:"pie",radius:["40%","70%"],center:["60%","50%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:10,borderColor:"#fff",borderWidth:2},label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:20,fontWeight:"bold"}},labelLine:{show:!1},data:o}],color:["#67C23A","#E6A23C","#F56C6C","#909399","#409EFF","#F78989"]};S.setOption(t)},H=async()=>{try{const o=await be();o.code===200?U.value=o.data||[]:w.error(o.message||"获取公司列表失败")}catch(o){console.error("获取公司列表失败:",o),w.error("获取公司列表失败")}};return te(()=>{H(),p(),window.addEventListener("resize",()=>{S&&S.resize()})}),C({formatCurrency:O,getStatusTagType:A,getRiskTagType:q}),(o,t)=>{const n=u("el-icon"),c=u("el-button"),m=u("el-card"),h=u("el-option"),B=u("el-select"),g=u("el-col"),J=u("el-input"),F=u("el-row"),v=u("el-table-column"),j=u("el-tag"),X=u("el-table"),Y=u("el-pagination"),Z=se("loading");return z(),D("div",Ce,[e(m,{class:"header-card"},{header:a(()=>[s("div",ke,[s("div",xe,[s("span",ze,"更新时间: "+_(P.value),1),e(c,{type:"primary",size:"small",onClick:Q,loading:k.value},{default:a(()=>[e(n,null,{default:a(()=>[e(f(re))]),_:1}),t[5]||(t[5]=b(" 刷新数据 "))]),_:1,__:[5]},8,["loading"])])])]),default:a(()=>[t[6]||(t[6]=s("div",{class:"welcome-content"},[s("h2",null,"客户下单情况分析"),s("p",null,"分析客户最后下单时间，识别流失风险，优化客户关系管理")],-1))]),_:1,__:[6]}),e(m,{class:"filter-card"},{default:a(()=>[s("div",Se,[e(F,{gutter:16,style:{"align-items":"center"}},{default:a(()=>[e(g,{span:5},{default:a(()=>[s("div",De,[t[7]||(t[7]=s("label",null,"阈值天数：",-1)),e(B,{modelValue:r.thresholdDays,"onUpdate:modelValue":t[0]||(t[0]=l=>r.thresholdDays=l),placeholder:"选择阈值天数",onChange:p,style:{width:"100%"}},{default:a(()=>[e(h,{label:"3天",value:3}),e(h,{label:"5天",value:5}),e(h,{label:"7天",value:7}),e(h,{label:"10天",value:10}),e(h,{label:"15天",value:15}),e(h,{label:"30天",value:30})]),_:1},8,["modelValue"])])]),_:1}),e(g,{span:6},{default:a(()=>[s("div",Le,[t[8]||(t[8]=s("label",null,"公司选择：",-1)),e(B,{modelValue:r.company,"onUpdate:modelValue":t[1]||(t[1]=l=>r.company=l),placeholder:"选择公司名称",clearable:"",onChange:p,onClear:p,style:{width:"100%"}},{default:a(()=>[e(h,{label:"全部公司",value:""}),(z(!0),D(ae,null,le(U.value,l=>(z(),M(h,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])])]),_:1}),e(g,{span:5},{default:a(()=>[s("div",Re,[t[9]||(t[9]=s("label",null,"客户名称：",-1)),e(J,{modelValue:r.customerName,"onUpdate:modelValue":t[2]||(t[2]=l=>r.customerName=l),placeholder:"输入客户名称",clearable:"",onClear:p,onKeyup:oe(p,["enter"]),style:{width:"100%"}},null,8,["modelValue"])])]),_:1}),e(g,{span:8},{default:a(()=>[s("div",Ve,[e(c,{type:"primary",onClick:p,loading:k.value},{default:a(()=>[e(n,null,{default:a(()=>[e(f(de))]),_:1}),t[10]||(t[10]=b(" 查询 "))]),_:1,__:[10]},8,["loading"]),e(c,{onClick:I},{default:a(()=>[e(n,null,{default:a(()=>[e(f(ce))]),_:1}),t[11]||(t[11]=b(" 重置 "))]),_:1,__:[11]}),e(c,{type:"success",onClick:$,loading:L.value},{default:a(()=>[e(n,null,{default:a(()=>[e(f(ue))]),_:1}),t[12]||(t[12]=b(" 导出 "))]),_:1,__:[12]},8,["loading"])])]),_:1})]),_:1})])]),_:1}),s("div",Te,[e(F,{gutter:20},{default:a(()=>[e(g,{span:6},{default:a(()=>[e(m,{class:"stat-card total-customers",shadow:"hover"},{default:a(()=>[s("div",Ee,[s("div",Ne,[e(n,{size:"32"},{default:a(()=>[e(f(pe))]),_:1})]),s("div",Pe,[t[13]||(t[13]=s("div",{class:"stat-title"},"客户总数",-1)),s("div",Ue,_(y.totalCustomers),1)])])]),_:1})]),_:1}),e(g,{span:6},{default:a(()=>[e(m,{class:"stat-card high-risk",shadow:"hover"},{default:a(()=>[s("div",Be,[s("div",Fe,[e(n,{size:"32"},{default:a(()=>[e(f(_e))]),_:1})]),s("div",je,[t[14]||(t[14]=s("div",{class:"stat-title"},"高风险客户",-1)),s("div",Me,_(y.highRiskCustomers),1)])])]),_:1})]),_:1}),e(g,{span:6},{default:a(()=>[e(m,{class:"stat-card medium-risk",shadow:"hover"},{default:a(()=>[s("div",Oe,[s("div",Ae,[e(n,{size:"32"},{default:a(()=>[e(f(fe))]),_:1})]),s("div",qe,[t[15]||(t[15]=s("div",{class:"stat-title"},"中风险客户",-1)),s("div",Ie,_(y.mediumRiskCustomers),1)])])]),_:1})]),_:1}),e(g,{span:6},{default:a(()=>[e(m,{class:"stat-card low-risk",shadow:"hover"},{default:a(()=>[s("div",We,[s("div",Ke,[e(n,{size:"32"},{default:a(()=>[e(f(me))]),_:1})]),s("div",Qe,[t[16]||(t[16]=s("div",{class:"stat-title"},"低风险客户",-1)),s("div",$e,_(y.lowRiskCustomers),1)])])]),_:1})]),_:1})]),_:1})]),e(m,{class:"chart-card"},{header:a(()=>t[17]||(t[17]=[s("div",{class:"card-header"},[s("span",null,"客户状态分布")],-1)])),default:a(()=>[s("div",{ref_key:"statusChartRef",ref:R,class:"chart-container"},null,512)]),_:1}),e(m,{class:"table-card"},{header:a(()=>[s("div",Ge,[s("span",null,"客户分析详情 (阈值: "+_(r.thresholdDays)+"天)",1),s("div",He,[s("span",null,"共 "+_(y.totalCustomers)+" 条记录",1)])])]),default:a(()=>[ne((z(),M(X,{data:V.value,stripe:"",border:"",height:"600",style:{width:"100%"}},{default:a(()=>[e(v,{prop:"公司名称",label:"公司名称",width:"250","show-overflow-tooltip":""}),e(v,{prop:"客户名称",label:"客户名称",width:"200","show-overflow-tooltip":""}),e(v,{prop:"最后下单时间",label:"最后下单时间",width:"120",align:"center"}),e(v,{prop:"距离最后下单天数",label:"距离天数",width:"100",align:"center"},{default:a(l=>[l.row.距离最后下单天数===999999?(z(),D("span",Je,"从未下单")):(z(),D("span",Xe,_(l.row.距离最后下单天数)+"天",1))]),_:1}),e(v,{prop:"客户状态",label:"客户状态",width:"100",align:"center"},{default:a(l=>[e(j,{type:f(A)(l.row.客户状态),size:"small"},{default:a(()=>[b(_(l.row.客户状态),1)]),_:2},1032,["type"])]),_:1}),e(v,{prop:"流失风险",label:"流失风险",width:"100",align:"center"},{default:a(l=>[e(j,{type:f(q)(l.row.流失风险),size:"small"},{default:a(()=>[b(_(l.row.流失风险),1)]),_:2},1032,["type"])]),_:1}),e(v,{prop:"历史订单总数",label:"历史订单数",width:"120",align:"center"}),e(v,{prop:"历史订单总额",label:"历史订单总额",width:"150",align:"right"},{default:a(l=>[b(" ¥"+_(f(O)(l.row.历史订单总额)),1)]),_:1})]),_:1},8,["data"])),[[Z,k.value]]),s("div",Ye,[e(Y,{"current-page":d.currentPage,"onUpdate:currentPage":t[3]||(t[3]=l=>d.currentPage=l),"page-size":d.pageSize,"onUpdate:pageSize":t[4]||(t[4]=l=>d.pageSize=l),"page-sizes":[10,20,50,100],small:!1,disabled:k.value,background:!0,layout:"total, sizes, prev, pager, next, jumper",total:d.total,onSizeChange:W,onCurrentChange:K},null,8,["current-page","page-size","disabled","total"])])]),_:1})])}}}),lt=ve(Ze,[["__scopeId","data-v-5b9b2f14"]]);export{lt as default};
