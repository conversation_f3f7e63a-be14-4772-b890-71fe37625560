import { request } from '/@/utils/service';

/**
 * 数据分析API接口
 */

// API路径前缀
export const apiPrefix = '/api/data-analysis/';

/**
 * 获取数据分析首页概览数据
 */
export function getDashboardOverview() {
  return request({
    url: apiPrefix + 'dashboard/',
    method: 'get',
  });
}

/**
 * 格式化货币显示
 * @param value 数值
 * @param precision 小数位数，默认2位
 */
export function formatCurrency(value: number, precision: number = 2): string {
  if (value === null || value === undefined || isNaN(value)) {
    return '0.00';
  }
  return value.toLocaleString('zh-CN', {
    minimumFractionDigits: precision,
    maximumFractionDigits: precision
  });
}

/**
 * 格式化数量显示
 * @param value 数值
 * @param precision 小数位数，默认2位
 */
export function formatQuantity(value: number, precision: number = 2): string {
  if (value === null || value === undefined || isNaN(value)) {
    return '0.00';
  }
  return value.toLocaleString('zh-CN', {
    minimumFractionDigits: precision,
    maximumFractionDigits: precision
  });
}

/**
 * 格式化百分比显示
 * @param value 数值
 * @param precision 小数位数，默认2位
 */
export function formatPercentage(value: number, precision: number = 2): string {
  if (value === null || value === undefined || isNaN(value)) {
    return '0.00%';
  }
  return value.toFixed(precision) + '%';
}

/**
 * 获取增长率的颜色类型
 * @param growthRate 增长率
 */
export function getGrowthRateType(growthRate: number): string {
  if (growthRate > 0) {
    return 'success';
  } else if (growthRate < 0) {
    return 'danger';
  } else {
    return 'info';
  }
}

/**
 * 获取增长率的图标
 * @param growthRate 增长率
 */
export function getGrowthRateIcon(growthRate: number): string {
  if (growthRate > 0) {
    return 'ArrowUp';
  } else if (growthRate < 0) {
    return 'ArrowDown';
  } else {
    return 'Minus';
  }
}
