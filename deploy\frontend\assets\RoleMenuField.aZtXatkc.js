import{R as C,h as b}from"./api.CRWbL0zW.js";import{R as M,a as $}from"./RoleMenuFieldStores.CgsZ7drj.js";import{e as y}from"./index.BHZI5pdK.js";import{d as S,k as f,a as l,m as N,u as c,o as a,e as t,l as m,q as k,w as v,F as _,p,s as V}from"./vue.BNx9QYep.js";import{_ as U}from"./_plugin-vue_export-helper.DlAUqK2U.js";const B={key:0,class:"pccm-item"},D={class:"menu-form-alert"},E={class:"columns-list"},H={class:"columns-head"},I={class:"columns-content"},q={class:"width-txt"},z=S({__name:"RoleMenuField",setup(L){const d=M(),h=$(),R=C(),F=(r,s,u)=>{for(const n of d.$state)n[s]=n[u]?n[s]:r},g=async()=>{const r=await b(R.$state.roleId,d.$state);y({message:r.msg,type:"success"})};return(r,s)=>{const u=f("el-button"),n=f("el-checkbox");return c(d).$state.length>0?(a(),l("div",B,[t("div",D,[m(u,{size:"small",onClick:g},{default:v(()=>s[0]||(s[0]=[k("保存 ")])),_:1,__:[0]}),s[1]||(s[1]=k(" 配置数据列字段权限 "))]),t("ul",E,[t("li",H,[s[2]||(s[2]=t("div",{class:"width-txt"},[t("span",null,"字段")],-1)),(a(!0),l(_,null,p(c(h).$state,(e,i)=>(a(),l("div",{key:i,class:"width-check"},[m(n,{modelValue:e.checked,"onUpdate:modelValue":o=>e.checked=o,onChange:o=>F(o,e.value,e.disabled)},{default:v(()=>[t("span",null,V(e.label),1)]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]))),128))]),t("div",I,[(a(!0),l(_,null,p(c(d).$state,(e,i)=>(a(),l("li",{key:i,class:"columns-item"},[t("div",q,V(e.title),1),(a(!0),l(_,null,p(c(h).$state,(o,w)=>(a(),l("div",{key:w,class:"width-check"},[m(n,{modelValue:e[o.value],"onUpdate:modelValue":x=>e[o.value]=x,class:"ci-checkout",disabled:e[o.disabled]},null,8,["modelValue","onUpdate:modelValue","disabled"])]))),128))]))),128))])])])):N("",!0)}}}),O=U(z,[["__scopeId","data-v-a964fc36"]]);export{O as default};
