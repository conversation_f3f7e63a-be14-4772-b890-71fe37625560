import{d as me,h as p,v as Re,j as ze,k as u,a as f,o as s,m as r,b as y,f as ie,s as Z,G as Ce,H as Se,r as qe,l as o,Z as Ke,w as i,M as We,g as Pe,z as ae,Y as Ye,q as F,e as c,u as b,F as se,p as ce,n as h,I as xe,A as H,D as A,x as Ze}from"./vue.BNx9QYep.js";import{g as $,J as Le,a6 as Qe,k as Xe,az as he,r as fe,aA as ke,H as et,I as tt}from"./index.BHZI5pdK.js";import{createCrudOptions as lt}from"./crud.D3nDGXPd.js";import{_ as Ve}from"./_plugin-vue_export-helper.DlAUqK2U.js";const P={IMAGE:8,VIDEO:4,AUDIO:2,OTHER:1,ALL:15},ot=["title"],at=me({__name:"fileItem",props:{fileData:{type:Object,required:!0},api:{type:Object,required:!0},showTitle:{type:Boolean,default:!0},showClose:{type:Boolean,default:!0}},emits:["onDelFile"],setup(Q,{expose:J,emit:X}){const V=Q,j=me({template:"<el-icon><Files /></el-icon>"}),D=[{tag:"img",attr:{src:$(V.fileData.url),draggable:!1}},{tag:"video",attr:{src:$(V.fileData.url),controls:!1,autoplay:!0,muted:!0,loop:!0}},{tag:"audio",attr:{src:$(V.fileData.url),controls:!0,autoplay:!1,muted:!1,loop:!1,volume:0}},{tag:j,attr:{style:{fontSize:"2rem"}}}],w=p(!1),ee=p(),e=p(null),g=()=>V.api.DelObj(V.fileData.id).then(()=>{Le("删除成功"),d("onDelFile")});Re(V.fileData,T=>e.value=T,{immediate:!0,deep:!0});const d=X;return J({}),ze(()=>{}),(T,x)=>{const v=u("CircleClose"),O=u("el-icon");return s(),f("div",{ref_key:"itemRef",ref:ee,class:"file-item",title:e.value.name,onMouseenter:x[0]||(x[0]=q=>w.value=!0),onMouseleave:x[1]||(x[1]=q=>w.value=!1)},[Q.showTitle?(s(),f("div",{key:0,class:ie(["file-name",{show:w.value}])},Z(e.value.name),3)):r("",!0),(s(),y(qe(D[e.value.file_type].tag),Ce(Se(D[e.value.file_type].attr)),null,16)),V.showClose?(s(),f("div",{key:1,class:ie(["file-del",{show:w.value}])},[o(O,{size:24,color:"white",onClick:Ke(g,["stop"]),style:{cursor:"pointer"}},{default:i(()=>[o(v,{style:{"mix-blend-mode":"difference"}})]),_:1})],2)):r("",!0)],40,ot)}}}),st=Ve(at,[["__scopeId","data-v-bf7c8b2f"]]),it={key:1,style:{width:"100%",display:"flex",gap:"4px","flex-wrap":"wrap","margin-bottom":"4px"}},nt={style:{position:"absolute",left:"0",top:"0",width:"100%",height:"100%",display:"flex","justify-content":"center","align-items":"center"}},ut={style:{position:"absolute",left:"0",top:"0",width:"100%",height:"100%",display:"flex","justify-content":"center","align-items":"center"}},rt=["src"],dt={style:{position:"absolute",left:"0",top:"0",width:"100%",height:"100%",display:"flex","justify-content":"center","align-items":"center"}},pt=["src","controls"],ct={style:{position:"absolute",left:"0",top:"0",width:"100%",height:"100%",display:"flex","justify-content":"center","align-items":"center"}},ft={style:{padding:"4px"}},vt={style:{width:"100%",display:"flex","justify-content":"space-between",gap:"12px"}},mt={key:0},yt=["data-id"],_t={class:"listPaginator"},ve="/api/system/file/",wt=me({__name:"index",props:{modelValue:{},class:{type:Object,default:""},inputClass:{type:Object,default:""},style:{type:Object,default:{}},inputStyle:{type:Object,default:{}},disabled:{type:Boolean,default:!1},tabsType:{type:Object,default:""},itemSize:{type:Number,default:100},tabsShow:{type:Number,default:P.ALL},multiple:{type:Boolean,default:!1},selectable:{type:Boolean,default:!0},showInput:{type:Boolean,default:!0},inputType:{type:Object,default:"selector"},inputSize:{type:Number,default:100},valueKey:{type:String,default:"url"},showUploadButton:{type:Boolean,default:!0},showNetButton:{type:Boolean,default:!0}},emits:["update:modelValue","onSave","onClose","onClosed"],setup(Q,{expose:J,emit:X}){const V=We(Qe()).userInfos,j=!!(he&&he.length&&he.indexOf("dvadmin3-tenants-web")>=0),D=V.value.schema_name==="public",w=["图片","视频","音频","文件"],ee=["image/*","video/*","audio/*",""],e=Q,g=p(!1),d=p([3,2,1,0][e.tabsShow&e.tabsShow-1?3:Math.log2(e.tabsShow)]),T={GetList:l=>fe({url:ve,method:"get",params:l}),AddObj:l=>fe({url:ve,method:"post",data:l}),DelObj:l=>fe({url:ve+l+"/",method:"delete",data:{id:l}}),GetAll:()=>fe({url:ve+"get_all/"})},x=Pe({name:""}),v=Pe({page:1,limit:10,total:0}),O=p([]),q=p([]),z=async()=>{let l=await T.GetList({page:v.page,limit:v.limit,file_type:j?d.value%4:d.value,system:d.value>3,upload_method:1,...x});O.value=[],await xe(),O.value=l.data.map(t=>({...t,url:$(t.url)})),v.total=l.total,v.page=l.page,v.limit=l.limit,le()},k=l=>l.target.style.setProperty("--fileselector-close-display","block"),R=l=>l.target.style.setProperty("--fileselector-close-display","none"),K=async()=>{if(e.inputType!=="selector")return;let l=await T.GetAll();q.value=l.data},ne=l=>{v.page=1,z()},ye=(l,t)=>{v.page=l,v.limit=t,z()},te=p(),W=async l=>{var N;if(!e.selectable)return;let t=l.target,m=0;for(;!t.dataset.id;)t=t.parentElement;let S=t.dataset.id;if(e.multiple){if(n.value||(n.value=[]),t.classList.contains("active")?(t.classList.remove("active"),m=-1):(t.classList.add("active"),m=1),n.value.length){let C=JSON.parse(JSON.stringify(n.value));m===1?C.push(S):C.splice(C.indexOf(S),1),n.value=C}else n.value=[S];n.value=Array.from(new Set(n.value)).sort()}else{for(let C of(N=te.value)==null?void 0:N.children)C.classList.remove("active");t.classList.add("active"),n.value=S}},le=async()=>{var l,t;if(e.selectable){await xe();for(let m of((l=te.value)==null?void 0:l.children)||[]){m.classList.remove("active");let S=m.dataset.id;e.multiple?(t=n.value)!=null&&t.includes(S)&&m.classList.add("active"):S===n.value&&m.classList.add("active")}}},ue=p(),_e=()=>{pe(n.value),de("onSave",n.value),g.value=!1},re=()=>{n.value=e.modelValue,de("onClose"),g.value=!1},we=()=>{_(),de("onClosed")},_=()=>{x.name="",v.page=1,v.limit=10,v.total=0,O.value=[]},Y=()=>{n.value=null,pe(null)},He=l=>{let t=JSON.parse(JSON.stringify(n.value)).filter(m=>m!==l);n.value=t,pe(t)},L=p(!1),oe=p(!1),E=p(""),ge=p("HTTP://"),Ae=()=>{let l=E.value.trim();(l.toUpperCase().startsWith("HTTP://")||l.toUpperCase().startsWith("HTTPS://"))&&(l=l.split("://")[1]),l.startsWith("/")&&(l=l.substring(1)),E.value=l},je=()=>{if(!E.value)return;L.value=!0;let l=new AbortController,t=setTimeout(()=>{l.abort()},10*1e3);fetch(ge.value+E.value,{signal:l.signal}).then(async m=>{clearTimeout(t),m.ok||ke(`网络${w[d.value%4]}获取失败！`);const S=m.url.split("?")[0].split("/");let N=S[S.length-1],C=await m.blob(),I=new File([C],N,{type:C.type}),B=new FormData;B.append("file",I),B.append("upload_method","1"),fetch($()+"api/system/file/",{method:"post",body:B}).then(()=>Le("网络文件上传成功！")).then(()=>{oe.value=!1,z(),K()}).catch(()=>ke("网络文件上传失败！")).then(()=>L.value=!1)}).catch(m=>{console.log(m),clearTimeout(t),ke(`网络${w[d.value%4]}获取失败！`),L.value=!1})},n=p(null),de=X;Re(()=>e.modelValue,l=>n.value=e.multiple?JSON.parse(JSON.stringify(l)):l,{immediate:!0});const{ui:Be}=Xe(),Te=Be.formItem.injectFormItemContext(),pe=l=>{let t=null;if(l)if(typeof l=="string")t=l.replace(/\\/g,"/");else{t=[];for(let m of l)t.push(m.replace(/\\/g,"/"))}de("update:modelValue",t),Te.onChange(),Te.onBlur()};return J({data:n,onDataChange:pe,selectVisiable:g,clearState:_,clear:Y}),ze(()=>{if(e.multiple&&!["selector","image"].includes(e.inputType))throw new Error("FileSelector组件属性multiple为true时inputType必须为selector");K(),console.log("fileselector tenentmdoe",j),console.log("fileselector supertenent",D)}),(l,t)=>{const m=u("el-option"),S=u("el-select"),N=u("el-image"),C=u("Close"),I=u("el-icon"),B=u("Plus"),Ue=u("el-divider"),U=u("el-tab-pane"),Oe=u("el-tabs"),$e=u("el-input"),Fe=u("el-tag"),De=u("el-col"),M=u("el-button"),Ee=u("el-upload"),Ne=u("el-row"),Me=u("el-empty"),Ge=u("el-pagination"),Je=u("el-form-item"),Ie=u("el-dialog");return s(),f("div",{style:h([{width:"100%"},e.style]),class:ie(e.class)},[ae(l.$slots,"input",Ce(Se({})),()=>[e.showInput?(s(),f("div",{key:0,style:h([{width:"100%"},e.inputStyle]),class:ie(e.inputClass)},[e.inputType==="selector"?(s(),y(S,{key:0,modelValue:n.value,"onUpdate:modelValue":t[0]||(t[0]=a=>n.value=a),"suffix-icon":"arrow-down",clearable:"",multiple:e.multiple,placeholder:"请选择文件",onClick:t[1]||(t[1]=a=>g.value=!e.disabled),disabled:e.disabled,onClear:le,onRemoveTag:le},{default:i(()=>[(s(!0),f(se,null,ce(q.value,(a,G)=>(s(),y(m,{key:G,value:String(a[e.valueKey]),label:a.name},null,8,["value","label"]))),128))]),_:1},8,["modelValue","multiple","disabled"])):r("",!0),e.inputType==="image"&&e.multiple?(s(),f("div",it,[(s(!0),f(se,null,ce(n.value||[],(a,G)=>(s(),f("div",{style:h([{position:"relative"},{width:e.inputSize+"px",height:e.inputSize+"px"}])},[(s(),y(N,{src:a,key:G,fit:"scale-down",class:"itemList",style:h({width:e.inputSize+"px",aspectRatio:"1 / 1"})},null,8,["src","style"])),H(o(I,{class:"closeHover",size:16,onClick:be=>He(a)},{default:i(()=>[o(C)]),_:2},1032,["onClick"]),[[A,!!n.value&&!e.disabled]])],4))),256)),c("div",{style:h([{position:"relative"},{width:e.inputSize+"px",height:e.inputSize+"px"}])},[c("div",nt,[o(I,{size:24},{default:i(()=>[o(B)]),_:1})]),c("div",{onClick:t[2]||(t[2]=a=>g.value=!e.disabled),class:"addControllorHover",style:h({cursor:e.disabled?"not-allowed":"pointer"})},null,4)],4)])):r("",!0),e.inputType==="image"&&!e.multiple?(s(),f("div",{key:2,class:"form-display",style:h([{position:"relative"},{width:e.inputSize+"px",height:e.inputSize+"px"}]),onMouseenter:k,onMouseleave:R},[o(N,{src:n.value,fit:"scale-down",style:h({width:e.inputSize+"px",aspectRatio:"1 / 1"})},{error:i(()=>t[21]||(t[21]=[c("div",null,null,-1)])),_:1},8,["src","style"]),H(c("div",ut,[o(I,{size:24},{default:i(()=>[o(B)]),_:1})],512),[[A,!n.value]]),c("div",{onClick:t[3]||(t[3]=a=>g.value=!e.disabled),class:"addControllorHover",style:h({cursor:e.disabled?"not-allowed":"pointer"})},null,4),H(o(I,{class:"closeHover",size:16,onClick:Y},{default:i(()=>[o(C)]),_:1},512),[[A,!!n.value&&!e.disabled&&!e.multiple]])],36)):r("",!0),e.inputType==="video"?(s(),f("div",{key:3,class:"form-display",onMouseenter:k,onMouseleave:R,style:h([{position:"relative",display:"flex","align-items":"center","justify-items":"center"},{width:e.inputSize*2+"px",height:e.inputSize+"px"}])},[c("video",{src:n.value,controls:!1,autoplay:!0,muted:!0,loop:!0,style:h({maxWidth:e.inputSize*2+"px",maxHeight:e.inputSize+"px",margin:"0 auto"})},null,12,rt),H(c("div",dt,[o(I,{size:24},{default:i(()=>[o(B)]),_:1})],512),[[A,!n.value]]),c("div",{onClick:t[4]||(t[4]=a=>g.value=!e.disabled),class:"addControllorHover",style:h({cursor:e.disabled?"not-allowed":"pointer"})},null,4),H(o(I,{class:"closeHover",size:16,onClick:Y},{default:i(()=>[o(C)]),_:1},512),[[A,!!n.value&&!e.disabled]])],36)):r("",!0),e.inputType==="audio"?(s(),f("div",{key:4,class:"form-display",onMouseenter:k,onMouseleave:R,style:h([{position:"relative",display:"flex","align-items":"center","justify-items":"center"},{width:e.inputSize*2+"px",height:e.inputSize+"px"}])},[c("audio",{src:n.value,controls:!!n.value,autoplay:!1,muted:!0,loop:!0,style:{width:"100%","z-index":"1"}},null,8,pt),H(c("div",ct,[o(I,{size:24},{default:i(()=>[o(B)]),_:1})],512),[[A,!n.value]]),c("div",{onClick:t[5]||(t[5]=a=>g.value=!e.disabled),class:"addControllorHover",style:h({cursor:e.disabled?"not-allowed":"pointer"})},null,4),H(o(I,{class:"closeHover",size:16,onClick:Y},{default:i(()=>[o(C)]),_:1},512),[[A,!!n.value&&!e.disabled]])],36)):r("",!0)],6)):r("",!0)],!0),o(Ie,{modelValue:g.value,"onUpdate:modelValue":t[19]||(t[19]=a=>g.value=a),draggable:!0,width:"50%","align-center":!1,"append-to-body":!0,onOpen:t[20]||(t[20]=a=>{O.value.length===0&&z()}),onClose:re,onClosed:we,"modal-class":"_overlay"},Ye({header:i(()=>[t[22]||(t[22]=c("span",{class:"el-dialog__title"},"文件选择",-1)),o(Ue,{style:{margin:"0"}})]),default:i(()=>[c("div",ft,[c("div",vt,[D?r("",!0):(s(),y(Oe,{key:0,style:{width:"100%"},modelValue:d.value,"onUpdate:modelValue":t[6]||(t[6]=a=>d.value=a),type:e.tabsType,stretch:!0,onTabChange:ne},{default:i(()=>[e.tabsShow&b(P).IMAGE?(s(),y(U,{key:0,name:0,label:"图片"})):r("",!0),e.tabsShow&b(P).VIDEO?(s(),y(U,{key:1,name:1,label:"视频"})):r("",!0),e.tabsShow&b(P).AUDIO?(s(),y(U,{key:2,name:2,label:"音频"})):r("",!0),e.tabsShow&b(P).OTHER?(s(),y(U,{key:3,name:3,label:"其他"})):r("",!0)]),_:1},8,["modelValue","type"])),j?(s(),y(Oe,{key:1,style:{width:"100%"},modelValue:d.value,"onUpdate:modelValue":t[7]||(t[7]=a=>d.value=a),type:e.tabsType,stretch:!0,onTabChange:ne},{default:i(()=>[e.tabsShow&b(P).IMAGE?(s(),y(U,{key:0,name:4,label:"系统图片"})):r("",!0),e.tabsShow&b(P).VIDEO?(s(),y(U,{key:1,name:5,label:"系统视频"})):r("",!0),e.tabsShow&b(P).AUDIO?(s(),y(U,{key:2,name:6,label:"系统音频"})):r("",!0),e.tabsShow&b(P).OTHER?(s(),y(U,{key:3,name:7,label:"系统其他"})):r("",!0)]),_:1},8,["modelValue","type"])):r("",!0)]),o(Ne,{justify:"space-between",class:"headerBar"},{default:i(()=>[o(De,{span:12},{default:i(()=>[ae(l.$slots,"actionbar-left",{},()=>[o($e,{modelValue:x.name,"onUpdate:modelValue":t[8]||(t[8]=a=>x.name=a),placeholder:`请输入${w[d.value%4]}名`,"prefix-icon":"search",clearable:"",onChange:z},null,8,["modelValue","placeholder"]),c("div",null,[e.multiple?(s(),y(Fe,{key:0,type:"primary",effect:"light"},{default:i(()=>{var a;return[F(" 一共选中 "+Z(((a=n.value)==null?void 0:a.length)||0)+" 个文件 ",1)]}),_:1})):r("",!0)])],!0)]),_:3}),o(De,{span:12,style:{width:"100%",display:"flex",gap:"12px","justify-content":"flex-end"}},{default:i(()=>[ae(l.$slots,"actionbar-right",Ce(Se({})),()=>[o(M,{type:"default",circle:"",icon:"refresh",onClick:z}),!(d.value>3)||D?(s(),f(se,{key:0},[e.showUploadButton?(s(),y(Ee,{key:0,ref_key:"uploadRef",ref:ue,action:b($)()+"api/system/file/",multiple:!1,drag:!1,data:{upload_method:1},"show-file-list":!0,accept:ee[d.value%4],"on-success":()=>{z(),K(),ue.value.clearFiles()}},{default:i(()=>[o(M,{type:"primary",icon:"plus"},{default:i(()=>[F("上传"+Z(w[d.value%4]),1)]),_:1})]),_:1},8,["action","accept","on-success"])):r("",!0),e.showNetButton?(s(),y(M,{key:1,type:"info",icon:"link",onClick:t[9]||(t[9]=a=>oe.value=!0)},{default:i(()=>[F(" 网络"+Z(w[d.value%4]),1)]),_:1})):r("",!0)],64)):r("",!0)],!0)]),_:3})]),_:3}),O.value.length?(s(),f("div",{key:1,ref_key:"listContainerRef",ref:te,class:"listContainer"},[(s(!0),f(se,null,ce(O.value,(a,G)=>(s(),f("div",{key:G,onClick:t[11]||(t[11]=be=>W(be)),"data-id":a[e.valueKey],style:h({width:(e.itemSize||100)+"px",cursor:e.selectable?"pointer":"normal"})},[ae(l.$slots,"item",{data:a},()=>[o(st,{fileData:a,api:T,showClose:d.value<4||D,onOnDelFile:t[10]||(t[10]=be=>{z(),K()})},null,8,["fileData","showClose"])],!0)],12,yt))),128))],512)):(s(),f("div",mt,[ae(l.$slots,"empty",{},()=>[o(Me,{description:"无内容，请上传",style:{width:"100%",height:"calc(50vh)","margin-top":"24px",padding:"4px"}})],!0)])),c("div",_t,[o(Ge,{background:"",size:"small",layout:"total, sizes, prev, pager, next",total:v.total,"page-size":v.limit,"onUpdate:pageSize":t[12]||(t[12]=a=>v.limit=a),"page-sizes":[10,20,30,40,50],"current-page":v.page,"onUpdate:currentPage":t[13]||(t[13]=a=>v.page=a),"hide-on-single-page":!1,onChange:ye},null,8,["total","page-size","current-page"])])]),o(Ie,{modelValue:oe.value,"onUpdate:modelValue":t[17]||(t[17]=a=>oe.value=a),draggable:!1,width:"50%","align-center":!1,"append-to-body":!0,title:"网络"+w[d.value%4]+"上传",onClosed:t[18]||(t[18]=a=>E.value=""),"close-on-click-modal":!L.value,"close-on-press-escape":!L.value,"show-close":!L.value,"modal-class":"_overlay"},{footer:i(()=>[L.value?r("",!0):(s(),y(M,{key:0,type:"default",onClick:t[16]||(t[16]=a=>oe.value=!1)},{default:i(()=>t[23]||(t[23]=[F("取消")])),_:1,__:[23]})),o(M,{type:"primary",onClick:je,loading:L.value},{default:i(()=>[F(Z(L.value?"网络文件获取中...":"确定"),1)]),_:1},8,["loading"])]),default:i(()=>[o(Je,{label:w[d.value%4]+"链接"},{default:i(()=>[o($e,{modelValue:E.value,"onUpdate:modelValue":t[15]||(t[15]=a=>E.value=a),placeholder:"请输入网络连接",clearable:"",onInput:Ae},{prepend:i(()=>[o(S,{modelValue:ge.value,"onUpdate:modelValue":t[14]||(t[14]=a=>ge.value=a),style:{width:"110px"}},{default:i(()=>[(s(),f(se,null,ce(["HTTP://","HTTPS://"],(a,G)=>o(m,{key:G,label:a,value:a},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1},8,["modelValue"])]),_:1},8,["label"])]),_:1},8,["modelValue","title","close-on-click-modal","close-on-press-escape","show-close"])]),_:2},[e.showInput?{name:"footer",fn:i(()=>[o(M,{type:"default",onClick:re},{default:i(()=>t[24]||(t[24]=[F("取消")])),_:1,__:[24]}),o(M,{type:"primary",onClick:_e},{default:i(()=>t[25]||(t[25]=[F("确定")])),_:1,__:[25]})]),key:"0"}:void 0]),1032,["modelValue"])],6)}}}),gt=Ve(wt,[["__scopeId","data-v-e0057058"]]),bt={key:0},ht=["onClick"],kt=["onClick"],Ct={key:4},St=["src"],xt=["src"],zt={class:"closePreviewBtn"},Vt=me({__name:"index",setup(Q){const J=p(null),X=k=>k<1024?k+"b":k<1024*1024?(k/1024).toFixed(2)+"Kb":(k/(1024*1024)).toFixed(2)+"Mb",V=async()=>{J.value.selectVisiable=!0,await xe()},j=p(),D=p(),{crudExpose:w}=et({crudRef:j,crudBinding:D}),{crudOptions:ee}=lt({crudExpose:w,context:{openAddHandle:V}});tt({crudExpose:w,crudOptions:ee});const e=p([]),g=p(!1),d=p(""),T=p(""),x=p(),v=p(),O=(k,R)=>{g.value=!0,x.value.muted=!0,v.value.muted=!0,d.value=k,window.addEventListener("keydown",z)},q=()=>{g.value=!1,d.value="",T.value="",window.removeEventListener("keydown",z)},z=k=>{k.key==="Escape"&&(g.value=!1,d.value="",T.value="",window.removeEventListener("keydown",z))};return ze(()=>{w.doRefresh()}),(k,R)=>{const K=u("el-button"),ne=u("el-upload"),ye=u("el-image"),te=u("VideoCamera"),W=u("el-icon"),le=u("Headset"),ue=u("Document"),_e=u("fs-crud"),re=u("CircleClose"),we=u("fs-page");return s(),y(we,null,{default:i(()=>[o(gt,{modelValue:e.value,"onUpdate:modelValue":R[0]||(R[0]=_=>e.value=_),showInput:!1,ref_key:"fileSelectorRef",ref:J,tabsShow:b(P).ALL,itemSize:120,multiple:!1,selectable:!0,valueKey:"url",inputType:"image"},null,8,["modelValue","tabsShow"]),o(_e,Ze({ref_key:"crudRef",ref:j},D.value),{"actionbar-left":i(_=>[o(ne,{action:b($)()+"api/system/file/",multiple:!1,"on-success":()=>b(w).doRefresh(),drag:!1,"show-file-list":!1},{default:i(()=>[o(K,{type:"primary",icon:"plus"},{default:i(()=>R[1]||(R[1]=[F("上传")])),_:1,__:[1]})]),_:1},8,["action","on-success"])]),cell_size:i(_=>[c("span",null,Z(_.row.size?X(_.row.size):"0b"),1)]),cell_preview:i(_=>[_.row.file_type===0?(s(),f("div",bt,[o(ye,{style:{width:"100%","aspect-ratio":"1 /1"},src:b($)(_.row.url),"preview-src-list":[b($)(_.row.url)],"preview-teleported":!0},null,8,["src","preview-src-list"])])):r("",!0),_.row.file_type===1?(s(),f("div",{key:1,class:"_preview",onClick:Y=>O(b($)(_.row.url),"video")},[o(W,{size:60},{default:i(()=>[o(te)]),_:1})],8,ht)):r("",!0),_.row.file_type===2?(s(),f("div",{key:2,class:"_preview",onClick:Y=>O(b($)(_.row.url),"video")},[o(W,{size:60},{default:i(()=>[o(le)]),_:1})],8,kt)):r("",!0),_.row.file_type===3?(s(),y(W,{key:3,size:60},{default:i(()=>[o(ue)]),_:1})):r("",!0),_.row.file_type>3?(s(),f("div",Ct,"未知类型")):r("",!0)]),_:1},16),c("div",{class:ie(["preview",{show:g.value}])},[H(c("video",{src:d.value,class:"previewItem",controls:!0,autoplay:!0,muted:!0,loop:!1,ref_key:"videoPreviewRef",ref:x},null,8,St),[[A,d.value]]),H(c("audio",{src:T.value,class:"previewItem",controls:!0,autoplay:!1,muted:!0,loop:!1,ref_key:"audioPreviewRef",ref:v},null,8,xt),[[A,T.value]]),c("div",zt,[o(W,{size:48,color:"white",style:{cursor:"pointer"},onClick:q},{default:i(()=>[o(re)]),_:1})])],2)]),_:1})}}}),It=Ve(Vt,[["__scopeId","data-v-4ad6e209"]]);export{It as default};
