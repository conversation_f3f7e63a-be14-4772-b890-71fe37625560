import{d as u,g as k,c as y,j as v,O as L,v as b,k as S,b as I,o,w as V,A as M,a,n as d,e as i,F as m,p as E,m as $,l as B,s as D,D as O,S as W}from"./vue.BNx9QYep.js";import{_ as j}from"./_plugin-vue_export-helper.DlAUqK2U.js";const A={class:"el-dropdown-menu"},F=["onClick"],N=u({name:"layoutTagsViewContextmenu"}),T=u({...N,props:{dropdown:{type:Object,default:()=>({x:0,y:0})}},emits:["currentContextmenuClick"],setup(p,{expose:f,emit:x}){const n=p,w=x,e=k({isShow:!1,dropdownList:[{contextMenuClickId:0,txt:"message.tagsView.refresh",affix:!1,icon:"ele-RefreshRight"},{contextMenuClickId:1,txt:"message.tagsView.close",affix:!1,icon:"ele-Close"},{contextMenuClickId:2,txt:"message.tagsView.closeOther",affix:!1,icon:"ele-CircleClose"},{contextMenuClickId:3,txt:"message.tagsView.closeAll",affix:!1,icon:"ele-FolderDelete"},{contextMenuClickId:4,txt:"message.tagsView.fullscreen",affix:!1,icon:"iconfont icon-fullscreen"}],item:{},arrowLeft:10}),r=y(()=>n.dropdown.x+117>document.documentElement.clientWidth?{x:document.documentElement.clientWidth-117-5,y:n.dropdown.y}:n.dropdown),C=t=>{w("currentContextmenuClick",Object.assign({},{contextMenuClickId:t},e.item))},_=t=>{var c;e.item=t,(c=t.meta)!=null&&c.isAffix?e.dropdownList[1].affix=!0:e.dropdownList[1].affix=!1,l(),setTimeout(()=>{e.isShow=!0},10)},l=()=>{e.isShow=!1};return v(()=>{document.body.addEventListener("click",l)}),L(()=>{document.body.removeEventListener("click",l)}),b(()=>n.dropdown,({x:t})=>{t+117>document.documentElement.clientWidth?e.arrowLeft=117-(document.documentElement.clientWidth-t):e.arrowLeft=10},{deep:!0}),f({openContextmenu:_}),(t,c)=>{const h=S("SvgIcon");return o(),I(W,{name:"el-zoom-in-center"},{default:V(()=>[M((o(),a("div",{"aria-hidden":"true",class:"el-dropdown__popper el-popper is-light is-pure custom-contextmenu",role:"tooltip","data-popper-placement":"bottom",style:d(`top: ${r.value.y+5}px;left: ${r.value.x}px;`),key:Math.random()},[i("ul",A,[(o(!0),a(m,null,E(e.dropdownList,(s,g)=>(o(),a(m,null,[s.affix?$("",!0):(o(),a("li",{class:"el-dropdown-menu__item","aria-disabled":"false",tabindex:"-1",key:g,onClick:z=>C(s.contextMenuClickId)},[B(h,{name:s.icon},null,8,["name"]),i("span",null,D(t.$t(s.txt)),1)],8,F))],64))),256))]),i("div",{class:"el-popper__arrow",style:d({left:`${e.arrowLeft}px`})},null,4)],4)),[[O,e.isShow]])]),_:1})}}}),q=j(T,[["__scopeId","data-v-b988030b"]]);export{q as default};
