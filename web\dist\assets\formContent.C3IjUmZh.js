import{G as ue,s as pe,D as ie,U as _e}from"./api.FR9XnnSw.js";import{d as E}from"./dictionary.DNsEqk19.js";import{g as Y,S as ce,N as x,s as fe,a2 as me,a3 as ye,b as ke}from"./index.BHZI5pdK.js";import{_ as ve}from"./crudTable.vue_vue_type_script_setup_true_lang.D7IoA8Cq.js";import{d as be,h as v,i as ge,v as Ve,k as _,a as m,o as r,l as d,w as s,q as y,F as S,p as j,u as a,b as c,s as U,e as b,m as he}from"./vue.BNx9QYep.js";import{_ as Ue}from"./_plugin-vue_export-helper.DlAUqK2U.js";const we={key:1},xe={slot:"prepend"},Ce={style:{padding:"0px 5px"}},Oe={key:1},Ie=["src"],Se=["src"],je=be({__name:"formContent",props:["options","editableTabsItem"],setup(L){const R=L;let n=v({}),k=v([]);const T=v();let $=v(Y()+"api/system/file/"),z=v({Authorization:"JWT "+ce.get("token")}),N=v(""),D=v(!1);v(null);const Z=()=>{ue({parent:R.options.id,limit:999}).then(t=>{let o=t.data;k.value=o;const u={};for(const p of o){const i=p.key;p.value?u[i]=p.value:[5,12,11,14].indexOf(p.form_item_type)!==-1?u[i]=p.value||[]:u[i]=p.value}n.value=Object.assign({},u)})},K=t=>{const o=Object.keys(n.value),u=Object.values(n.value);for(const p in k.value){const i=k.value[p];o.forEach((g,w)=>{if(g===i.key&&(i.value=u[w],["img","imgs"].indexOf(i.form_item_type_label)>-1)){for(const h of i.rule)if(h.required&&i.value===null){x(i.title+"不能为空");return}}})}t&&t.validate(p=>{if(p)pe(k.value).then(i=>{fe("保存成功"),C&&C()});else return console.log("error submit!!"),!1})},J=t=>{N=t.url,D.value=!0},ee=t=>{if(typeof t!="string")return;const o=t.toLowerCase();return o.endsWith(".png")||o.endsWith(".jpeg")||o.endsWith(".jpg")||o.endsWith(".png")||o.endsWith(".bmp")},F=(t,o,u,p)=>{const{code:i,msg:g}=t;if(i===2e3){const{url:w}=t.data,{name:h}=o;if(!ee(h))x("只允许上传图片");else{const O=n[p];(!O||O==="")&&(n[p]=[]);const W={name:h,url:Y()+w};n[p].push(W)}}else x("上传失败,"+JSON.stringify(g))},q=()=>{x("上传失败")},G=()=>{x("超过文件上传数量")},M=(t,o,u)=>{var p=0;n[u].map((i,g)=>{i.uid===t.uid&&(p=g)}),n[u].splice(p,1)},le=t=>{ie(t.id).then(o=>{})},oe=t=>{k.value[t].edit=!0,k.value[t].new_key=k.value[t].key},C=ge("refreshView"),ae=t=>{t.key=JSON.parse(JSON.stringify(t.new_key)),_e(t).then(o=>{C&&C()})};return Ve(R.options,t=>{t&&t.id&&Z()},{immediate:!0}),(t,o)=>{const u=_("el-col"),p=_("el-row"),i=_("el-input"),g=_("el-input-number"),w=_("el-date-picker"),h=_("el-option"),P=_("el-select"),O=_("el-checkbox"),W=_("el-checkbox-group"),te=_("el-radio"),ne=_("el-radio-group"),A=_("el-switch"),H=_("el-upload"),Q=_("el-dialog"),se=_("table-selector"),I=_("el-button"),re=_("el-popconfirm"),X=_("el-form-item"),de=_("el-form");return r(),m("div",null,[d(p,{gutter:20},{default:s(()=>[d(u,{span:4},{default:s(()=>o[1]||(o[1]=[y("变量标题")])),_:1,__:[1]}),d(u,{span:4},{default:s(()=>o[2]||(o[2]=[y("变量名")])),_:1,__:[2]}),d(u,{span:10},{default:s(()=>o[3]||(o[3]=[y("变量值")])),_:1,__:[3]}),d(u,{span:2,offset:1},{default:s(()=>o[4]||(o[4]=[y("是否前端配置")])),_:1,__:[4]}),d(u,{span:3},{default:s(()=>o[5]||(o[5]=[y("操作")])),_:1,__:[5]})]),_:1}),d(de,{ref_key:"formRef",ref:T,model:a(n),"label-width":"0px","label-position":"left",style:{"margin-top":"20px"}},{default:s(()=>[(r(!0),m(S,null,j(a(k),(e,f)=>(r(),c(X,{prop:["array"].indexOf(e.form_item_type_label)>-1?"":e.key,key:f,rules:e.rule||[]},{default:s(()=>[d(u,{span:4},{default:s(()=>[e.edit?(r(),c(i,{key:0,modelValue:e.title,"onUpdate:modelValue":l=>e.title=l,style:{display:"inline-block",width:"200px"},placeholder:"请输入标题"},null,8,["modelValue","onUpdate:modelValue"])):(r(),m("span",we,U(e.title),1))]),_:2},1024),d(u,{span:4},{default:s(()=>[e.edit?(r(),c(i,{key:0,modelValue:e.new_key,"onUpdate:modelValue":l=>e.new_key=l,style:{width:"200px"},placeholder:"请输入变量key"},{default:s(()=>[b("template",xe,[b("span",Ce,U(L.editableTabsItem.key),1)])]),_:2},1032,["modelValue","onUpdate:modelValue"])):(r(),m("span",Oe,U(L.editableTabsItem.key)+"."+U(e.key),1))]),_:2},1024),d(u,{span:10},{default:s(()=>[["text","textarea"].indexOf(e.form_item_type_label)>-1?(r(),c(i,{key:f,type:e.form_item_type_label,modelValue:a(n)[e.key],"onUpdate:modelValue":l=>a(n)[e.key]=l,placeholder:e.placeholder,clearable:""},null,8,["type","modelValue","onUpdate:modelValue","placeholder"])):e.form_item_type_label==="number"?(r(),c(g,{key:f+1,modelValue:a(n)[e.key],"onUpdate:modelValue":l=>a(n)[e.key]=l,min:0},null,8,["modelValue","onUpdate:modelValue"])):["datetime","date","time"].indexOf(e.form_item_type_label)>-1?(r(),c(w,{modelValue:a(n)[e.key],"onUpdate:modelValue":l=>a(n)[e.key]=l,key:f+2,type:e.form_item_type_label,placeholder:e.placeholder},null,8,["modelValue","onUpdate:modelValue","type","placeholder"])):e.form_item_type_label==="select"?(r(),c(P,{key:f+3,modelValue:a(n)[e.key],"onUpdate:modelValue":l=>a(n)[e.key]=l,placeholder:e.placeholder,clearable:""},{default:s(()=>[(r(!0),m(S,null,j(a(E)(e.setting)||[],l=>(r(),c(h,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","placeholder"])):e.form_item_type_label==="checkbox"?(r(),c(W,{key:f+4,modelValue:a(n)[e.key],"onUpdate:modelValue":l=>a(n)[e.key]=l,placeholder:e.placeholder},{default:s(()=>[(r(!0),m(S,null,j(a(E)(e.setting)||[],l=>(r(),c(O,{key:l.value,label:l.value,value:l.value},{default:s(()=>[y(U(l.label),1)]),_:2},1032,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","placeholder"])):e.form_item_type_label==="radio"?(r(),c(ne,{key:f+5,modelValue:a(n)[e.key],"onUpdate:modelValue":l=>a(n)[e.key]=l,placeholder:e.placeholder,clearable:""},{default:s(()=>[(r(!0),m(S,null,j(a(E)(e.setting)||[],l=>(r(),c(te,{key:l.value,label:l.value,value:l.value},{default:s(()=>[y(U(l.label),1)]),_:2},1032,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","placeholder"])):e.form_item_type_label==="switch"?(r(),c(A,{key:f+6,modelValue:a(n)[e.key],"onUpdate:modelValue":l=>a(n)[e.key]=l,"inactive-value":!1,"active-color":"#13ce66","inactive-color":"#ff4949"},null,8,["modelValue","onUpdate:modelValue"])):["img","imgs"].indexOf(e.form_item_type_label)>-1?(r(),m("div",{key:f+7},[d(H,{action:a($),headers:a(z),name:"file",accept:"image/*","on-preview":J,"on-success":(l,V,B)=>{F(l,V,B,e.key)},"on-error":q,"on-exceed":G,"before-remove":(l,V)=>{M(l,V,e.key)},multiple:e.form_item_type_label!=="img",limit:e.form_item_type_label==="img"?1:5,ref_for:!0,ref:"imgUpload_"+e.key,"data-keyname":e.key,"file-list":e.value?e.value:[],"list-type":"picture-card"},{default:s(()=>o[6]||(o[6]=[b("i",{class:"el-icon-plus"},null,-1),b("div",{slot:"tip",class:"el-upload__tip"},"请选取图片,并且只能上传jpg/png文件",-1)])),_:2,__:[6]},1032,["action","headers","on-success","before-remove","multiple","limit","data-keyname","file-list"]),d(Q,{visible:a(D)},{default:s(()=>[b("img",{width:"100%",src:a(N),alt:""},null,8,Ie)]),_:1},8,["visible"])])):["file"].indexOf(e.form_item_type_label)>-1?(r(),m("div",{key:f+8},[d(H,{action:a($),headers:a(z),name:"file","on-preview":J,"on-success":(l,V,B)=>{F(l,V,B,e.key)},"on-error":q,"on-exceed":G,"before-remove":(l,V)=>{M(l,V,e.key)},limit:5,ref_for:!0,ref:"fileUpload_"+e.key,"data-keyname":e.key,"file-list":e.value,"list-type":"picture-card"},{default:s(()=>o[7]||(o[7]=[b("i",{class:"el-icon-plus"},null,-1),b("div",{slot:"tip",class:"el-upload__tip"},"请选取图片,并且只能上传jpg/png文件",-1)])),_:2,__:[7]},1032,["action","headers","on-success","before-remove","data-keyname","file-list"]),d(Q,{visible:a(D)},{default:s(()=>[b("img",{width:"100%",src:a(N),alt:""},null,8,Se)]),_:1},8,["visible"])])):["foreignkey","manytomany"].indexOf(e.form_item_type_label)>-1?(r(),m("div",{key:f+9},[d(se,{modelValue:a(n)[e.key],"onUpdate:modelValue":l=>a(n)[e.key]=l,"el-props":{pagination:!0,columns:e.setting.searchField},dict:{url:"/api/system/system_config/get_table_data/"+e.id+"/",value:e.setting.primarykey,label:e.setting.field},pagination:!0,multiple:e.form_item_type_label==="manytomany"},null,8,["modelValue","onUpdate:modelValue","el-props","dict","multiple"])])):e.form_item_type_label==="array"?(r(),m("div",{key:f+10},[d(ve,{modelValue:a(n)[e.key],"onUpdate:modelValue":l=>a(n)[e.key]=l},null,8,["modelValue","onUpdate:modelValue"])])):he("",!0)]),_:2},1024),d(u,{span:2,offset:1},{default:s(()=>[d(A,{modelValue:e.status,"onUpdate:modelValue":l=>e.status=l,"active-color":"#13ce66","inactive-color":"#ff4949"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),d(u,{span:3},{default:s(()=>[e.edit?(r(),c(I,{key:0,size:"mini",type:"primary",icon:a(me),onClick:l=>ae(e)},{default:s(()=>o[8]||(o[8]=[y("保存")])),_:2,__:[8]},1032,["icon","onClick"])):(r(),c(I,{key:1,size:"mini",type:"primary",icon:a(ye),onClick:l=>oe(f)},null,8,["icon","onClick"])),d(re,{title:"确定删除该条数据吗？",onConfirm:l=>le(e)},{reference:s(()=>[d(I,{size:"mini",type:"danger",icon:a(ke)},null,8,["icon"])]),_:2},1032,["onConfirm"])]),_:2},1024)]),_:2},1032,["prop","rules"]))),128)),d(X,null,{default:s(()=>[d(I,{type:"primary",onClick:o[0]||(o[0]=e=>K(T.value))},{default:s(()=>o[9]||(o[9]=[y("确定")])),_:1,__:[9]})]),_:1})]),_:1},8,["model"])])}}}),Re=Ue(je,[["__scopeId","data-v-0a04d3bc"]]);export{Re as default};
