import{d as p,M as C,h as R,R as T,g as k,k as n,a as I,o as B,l,w as u,e as Q,q as M,s as N,I as O}from"./vue.BNx9QYep.js";import{T as A,a7 as H}from"./index.BHZI5pdK.js";import{_ as U}from"./_plugin-vue_export-helper.DlAUqK2U.js";const $={class:"layout-search-dialog"},b=p({name:"layoutBreadcrumbSearch"}),j=p({...b,setup(D,{expose:f}){const _=A(),{tagsViewRoutes:h}=C(_),m=R(),{t:w}=H.useI18n(),d=T(),t=k({isShowSearch:!1,menuQuery:"",tagsViewList:[]}),S=()=>{t.menuQuery="",t.isShowSearch=!0,v(),O(()=>{setTimeout(()=>{m.value.focus()})})},V=()=>{t.isShowSearch=!1},g=(e,o)=>{let s=e?t.tagsViewList.filter(L(e)):t.tagsViewList;o(s)},L=e=>o=>o.path.toLowerCase().indexOf(e.toLowerCase())>-1||o.meta.title.toLowerCase().indexOf(e.toLowerCase())>-1||w(o.meta.title).indexOf(e.toLowerCase())>-1,v=()=>{if(t.tagsViewList.length>0)return!1;h.value.map(e=>{var o;(o=e.meta)!=null&&o.isHide||t.tagsViewList.push({...e})})},x=e=>{var c,r,i;let{path:o,redirect:s}=e;(c=e.meta)!=null&&c.isLink&&!((r=e.meta)!=null&&r.isIframe)?window.open((i=e.meta)==null?void 0:i.isLink):s?d.push(s):d.push(o),V()};return f({openSearch:S}),(e,o)=>{const s=n("ele-Search"),c=n("el-icon"),r=n("SvgIcon"),i=n("el-autocomplete"),y=n("el-dialog");return B(),I("div",$,[l(y,{modelValue:t.isShowSearch,"onUpdate:modelValue":o[1]||(o[1]=a=>t.isShowSearch=a),"destroy-on-close":"","show-close":!1},{footer:u(()=>[l(i,{modelValue:t.menuQuery,"onUpdate:modelValue":o[0]||(o[0]=a=>t.menuQuery=a),"fetch-suggestions":g,placeholder:e.$t("message.user.searchPlaceholder"),ref_key:"layoutMenuAutocompleteRef",ref:m,onSelect:x,"fit-input-width":!0},{prefix:u(()=>[l(c,{class:"el-input__icon"},{default:u(()=>[l(s)]),_:1})]),default:u(({item:a})=>[Q("div",null,[l(r,{name:a.meta.icon,class:"mr5"},null,8,["name"]),M(" "+N(e.$t(a.meta.title)),1)])]),_:1},8,["modelValue","placeholder"])]),_:1},8,["modelValue"])])}}}),z=U(j,[["__scopeId","data-v-a11c6061"]]);export{z as default};
