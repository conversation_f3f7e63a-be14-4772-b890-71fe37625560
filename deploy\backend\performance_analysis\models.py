from django.db import models
from dvadmin.utils.models import CoreModel


class PerformanceAnalysis(CoreModel):
    """
    绩效分析模型
    说明: 用于存储业务员绩效分析的查询结果
    """
    salesperson = models.CharField(max_length=100, verbose_name="业务员")
    region = models.CharField(max_length=100, verbose_name="负责地区", null=True, blank=True)
    query_type = models.CharField(max_length=20, verbose_name="查询类型")  # monthly, quarterly, yearly
    query_year = models.IntegerField(verbose_name="查询年份")
    query_month = models.IntegerField(verbose_name="查询月份", null=True, blank=True)
    query_quarter = models.IntegerField(verbose_name="查询季度", null=True, blank=True)

    order_count = models.IntegerField(verbose_name="订单数量", default=0)
    customer_count = models.IntegerField(verbose_name="客户数量", default=0)
    product_count = models.IntegerField(verbose_name="产品种类", default=0)
    total_quantity = models.DecimalField(max_digits=15, decimal_places=2, verbose_name="总销量", default=0)
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, verbose_name="总销售金额", default=0)
    avg_price = models.DecimalField(max_digits=10, decimal_places=4, verbose_name="平均单价", default=0)

    class Meta:
        db_table = "performance_analysis"
        verbose_name = '绩效分析'
        verbose_name_plural = verbose_name
        ordering = ('-create_datetime',)
