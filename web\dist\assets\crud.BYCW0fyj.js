import{r as s,v as m,e as E,J as D,X as u}from"./index.BHZI5pdK.js";import{a as d}from"./authFunction.D3Be3hRy.js";import{h as A,l as p,k as h,I as B}from"./vue.BNx9QYep.js";const i="/api/system/menu_button/";function F(e){return s({url:i,method:"get",params:e})}function O(e){return s({url:i,method:"post",data:e})}function R(e){return s({url:i+e.id+"/",method:"put",data:e})}function q(e){return s({url:i+e+"/",method:"delete",data:{id:e}})}function x(e){return s({url:i+"batch_create/",method:"post",data:e})}function j(e){return s({url:i+"multiple_delete/",method:"delete",data:{keys:e}})}const C=function({crudExpose:e,context:c}){const f=async()=>{if(c.selectOptions.value.id)return await F({menu:c.selectOptions.value.id})},w=async({form:t,row:n})=>await R({...t,menu:n.menu}),b=async({row:t})=>await q(t.id),g=async({form:t})=>await O({...t,menu:c.selectOptions.value.id}),r=A([]),y=t=>{const o=e.getTableData().filter(a=>!t.includes(a));u.arrayEach(t,a=>{u.pluck(r.value,"id").includes(a.id)||(r.value=u.union(r.value,[a]))}),u.arrayEach(o,a=>{r.value=u.remove(r.value,l=>l.id!==a.id)})},v=()=>{const t=e.getBaseTableRef(),n=e.getTableData(),o=u.filter(n,a=>u.pluck(r.value,"id").includes(a.id));B(()=>{u.arrayEach(o,a=>{t.toggleRowSelection(a,!0)})})};return{selectedRows:r,crudOptions:{pagination:{show:!1},search:{container:{action:{col:{span:8}}}},actionbar:{buttons:{add:{show:d("btn:Create")},batchAdd:{show:!0,type:"primary",text:"批量生成",click:async()=>{if(c.selectOptions.value.id==null){E.error("请选择菜单");return}const t=await x({menu:c.selectOptions.value.id});t.code==2e3&&(D(t.msg),e.doRefresh())}}}},rowHandle:{fixed:"right",width:200,buttons:{view:{show:!1},edit:{icon:"",type:"primary",show:d("btn:Update")},remove:{show:d("btn:Delete")}}},request:{pageRequest:f,addRequest:g,editRequest:w,delRequest:b},table:{rowKey:"id",onSelectionChange:y,onRefreshed:()=>v()},form:{col:{span:24},labelWidth:"100px",wrapper:{is:"el-dialog",width:"600px"}},columns:{$checked:{title:"选择",form:{show:!1},column:{type:"selection",align:"center",width:"70px",columnSetDisabled:!0}},_index:{title:"序号",form:{show:!1},column:{type:"index",align:"center",width:"70px",columnSetDisabled:!0}},search:{title:"关键词",column:{show:!1},type:"text",search:{show:!0},form:{show:!1,component:{placeholder:"输入关键词搜索"}}},id:{title:"ID",type:"text",column:{show:!1},search:{show:!1},form:{show:!1}},name:{title:"权限名称",type:"text",search:{show:!0},column:{minWidth:120,sortable:!0},form:{rules:[{required:!0,message:"权限名称必填"}],component:{placeholder:"输入权限名称搜索",props:{clearable:!0,allowCreate:!0,filterable:!0}},helper:{render(){return p(h("el-alert"),{title:"手动输入",type:"warning",description:"页面中按钮的名称或者自定义一个名称"},null)}}}},value:{title:"权限值",type:"text",search:{show:!1},column:{width:200,sortable:!0},form:{rules:[{required:!0,message:"权限标识必填"}],placeholder:"输入权限标识",helper:{render(){return p(h("el-alert"),{title:"唯一值",type:"warning",description:"用于判断前端按钮权限或接口权限"},null)}}}},method:{title:"请求方式",search:{show:!1},type:"dict-select",column:{width:120,sortable:!0},dict:m({data:[{label:"GET",value:0},{label:"POST",value:1,color:"success"},{label:"PUT",value:2,color:"warning"},{label:"DELETE",value:3,color:"danger"}]}),form:{rules:[{required:!0,message:"必填项"}]}},api:{title:"接口地址",search:{show:!1},type:"dict-select",dict:m({getData(){return s({url:"/swagger.json"}).then(t=>{const n=Object.keys(t.paths),o=[];for(const a of n){const l={};l.label=a,l.value=a,o.push(l)}return o})}}),column:{minWidth:250,sortable:!0},form:{rules:[{required:!0,message:"必填项"}],component:{props:{allowCreate:!0,filterable:!0,clearable:!0}},helper:{render(){return p(h("el-alert"),{title:"请正确填写，以免请求时被拦截。匹配单例使用正则,例如:/api/xx/.*?/",type:"warning"},null)}}}}}}}},k=Object.freeze(Object.defineProperty({__proto__:null,createCrudOptions:C},Symbol.toStringTag,{value:"Module"}));export{j as B,k as a,C as c};
