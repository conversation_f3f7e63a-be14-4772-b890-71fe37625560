server {
    listen 80;
    server_name your-domain.com;  # 修改为你的域名
    
    # 前端静态文件
    location / {
        root /path/to/deploy/frontend;  # 修改为实际路径
        try_files $uri $uri/ /index.html;
        index index.html;
    }
    
    # API代理到Django后端
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Django静态文件
    location /static/ {
        alias /path/to/deploy/backend/collected_static/;  # 修改为实际路径
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
    
    # Django媒体文件
    location /media/ {
        alias /path/to/deploy/backend/media/;  # 修改为实际路径
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
    
    # 安全配置
    location ~ .*\.(gif|jpg|jpeg|png|bmp|swf|js|css)$ {
        expires 30d;
        add_header Cache-Control "public, immutable";
        error_log off;
        access_log off;
    }
    
    # 压缩配置
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;
}
