const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/RoleMenuTree.CdXe7kzO.js","assets/api.CRWbL0zW.js","assets/vue.BNx9QYep.js","assets/index.BHZI5pdK.js","assets/index.Dg-OhEXY.css","assets/RoleMenuTreeStores.BHTvmwPg.js","assets/RoleMenuBtnStores.WLEOorzd.js","assets/RoleMenuFieldStores.CgsZ7drj.js","assets/RoleMenuBtn.gEelCGt0.js","assets/_plugin-vue_export-helper.DlAUqK2U.js","assets/RoleMenuBtn.DfDa5F6p.css","assets/RoleMenuField.aZtXatkc.js","assets/RoleMenuField.CyJupE3c.css","assets/RoleUsers.DVd5hrDj.js","assets/RoleUsersStores.CCf4JEgz.js"])))=>i.map(i=>d[i]);
import{ak as ge,_ as T}from"./index.BHZI5pdK.js";import{a2 as Pe,h as R,c as w,v as B,K as ee,j as ae,b as Se,V as k,o as K,r as we,i as N,C as Me,a as ne,z as Ee,n as Re,u as h,E as be,I as X,d as ye,k as O,l as z,w as x,e as Y,P as U,q as F,s as Z,F as ke}from"./vue.BNx9QYep.js";import{R as Ce}from"./api.CRWbL0zW.js";import{R as Ve}from"./RoleUsersStores.CCf4JEgz.js";import{_ as De}from"./_plugin-vue_export-helper.DlAUqK2U.js";const Ne={__name:"splitpanes",props:{horizontal:{type:Boolean},pushOtherPanes:{type:Boolean,default:!0},dblClickSplitter:{type:Boolean,default:!0},rtl:{type:Boolean,default:!1},firstSplitter:{type:Boolean}},emits:["ready","resize","resized","pane-click","pane-maximize","pane-add","pane-remove","splitter-click"],setup(A,{emit:C}){const m=C,u=A,V=Pe(),l=R([]),M=w(()=>l.value.reduce((e,n)=>(e[~~n.id]=n)&&e,{})),f=w(()=>l.value.length),v=R(null),g=R(!1),c=R({mouseDown:!1,dragging:!1,activeSplitter:null,cursorOffset:0}),d=R({splitter:null,timeoutId:null}),b=w(()=>({[`splitpanes splitpanes--${u.horizontal?"horizontal":"vertical"}`]:!0,"splitpanes--dragging":c.value.dragging})),y=()=>{document.addEventListener("mousemove",r,{passive:!1}),document.addEventListener("mouseup",P),"ontouchstart"in window&&(document.addEventListener("touchmove",r,{passive:!1}),document.addEventListener("touchend",P))},E=()=>{document.removeEventListener("mousemove",r,{passive:!1}),document.removeEventListener("mouseup",P),"ontouchstart"in window&&(document.removeEventListener("touchmove",r,{passive:!1}),document.removeEventListener("touchend",P))},D=(e,n)=>{const t=e.target.closest(".splitpanes__splitter");if(t){const{left:a,top:i}=t.getBoundingClientRect(),{clientX:s,clientY:o}="ontouchstart"in window&&e.touches?e.touches[0]:e;c.value.cursorOffset=u.horizontal?o-i:s-a}y(),c.value.mouseDown=!0,c.value.activeSplitter=n},r=e=>{c.value.mouseDown&&(e.preventDefault(),c.value.dragging=!0,requestAnimationFrame(()=>{se(ie(e)),m("resize",l.value.map(n=>({min:n.min,max:n.max,size:n.size})))}))},P=()=>{c.value.dragging&&m("resized",l.value.map(e=>({min:e.min,max:e.max,size:e.size}))),c.value.mouseDown=!1,setTimeout(()=>{c.value.dragging=!1,E()},100)},S=(e,n)=>{"ontouchstart"in window&&(e.preventDefault(),u.dblClickSplitter&&(d.value.splitter===n?(clearTimeout(d.value.timeoutId),d.value.timeoutId=null,$(e,n),d.value.splitter=null):(d.value.splitter=n,d.value.timeoutId=setTimeout(()=>d.value.splitter=null,500)))),c.value.dragging||m("splitter-click",l.value[n])},$=(e,n)=>{let t=0;l.value=l.value.map((a,i)=>(a.size=i===n?a.max:a.min,i!==n&&(t+=a.min),a)),l.value[n].size-=t,m("pane-maximize",l.value[n]),m("resized",l.value.map(a=>({min:a.min,max:a.max,size:a.size})))},te=(e,n)=>{m("pane-click",M.value[n])},ie=e=>{const n=v.value.getBoundingClientRect(),{clientX:t,clientY:a}="ontouchstart"in window&&e.touches?e.touches[0]:e;return{x:t-(u.horizontal?0:c.value.cursorOffset)-n.left,y:a-(u.horizontal?c.value.cursorOffset:0)-n.top}},le=e=>{e=e[u.horizontal?"y":"x"];const n=v.value[u.horizontal?"clientHeight":"clientWidth"];return u.rtl&&!u.horizontal&&(e=n-e),e*100/n},se=e=>{const n=c.value.activeSplitter;let t={prevPanesSize:W(n),nextPanesSize:L(n),prevReachedMinPanes:0,nextReachedMinPanes:0};const a=0+(u.pushOtherPanes?0:t.prevPanesSize),i=100-(u.pushOtherPanes?0:t.nextPanesSize),s=Math.max(Math.min(le(e),i),a);let o=[n,n+1],p=l.value[o[0]]||null,_=l.value[o[1]]||null;const J=p.max<100&&s>=p.max+t.prevPanesSize,xe=_.max<100&&s<=100-(_.max+L(n+1));if(J||xe){J?(p.size=p.max,_.size=Math.max(100-p.max-t.prevPanesSize-t.nextPanesSize,0)):(p.size=Math.max(100-_.max-t.prevPanesSize-L(n+1),0),_.size=_.max);return}if(u.pushOtherPanes){const Q=oe(t,s);if(!Q)return;({sums:t,panesToResize:o}=Q),p=l.value[o[0]]||null,_=l.value[o[1]]||null}p!==null&&(p.size=Math.min(Math.max(s-t.prevPanesSize-t.prevReachedMinPanes,p.min),p.max)),_!==null&&(_.size=Math.min(Math.max(100-s-t.nextPanesSize-t.nextReachedMinPanes,_.min),_.max))},oe=(e,n)=>{const t=c.value.activeSplitter,a=[t,t+1];return n<e.prevPanesSize+l.value[a[0]].min&&(a[0]=re(t).index,e.prevReachedMinPanes=0,a[0]<t&&l.value.forEach((i,s)=>{s>a[0]&&s<=t&&(i.size=i.min,e.prevReachedMinPanes+=i.min)}),e.prevPanesSize=W(a[0]),a[0]===void 0)?(e.prevReachedMinPanes=0,l.value[0].size=l.value[0].min,l.value.forEach((i,s)=>{s>0&&s<=t&&(i.size=i.min,e.prevReachedMinPanes+=i.min)}),l.value[a[1]].size=100-e.prevReachedMinPanes-l.value[0].min-e.prevPanesSize-e.nextPanesSize,null):n>100-e.nextPanesSize-l.value[a[1]].min&&(a[1]=ue(t).index,e.nextReachedMinPanes=0,a[1]>t+1&&l.value.forEach((i,s)=>{s>t&&s<a[1]&&(i.size=i.min,e.nextReachedMinPanes+=i.min)}),e.nextPanesSize=L(a[1]-1),a[1]===void 0)?(e.nextReachedMinPanes=0,l.value.forEach((i,s)=>{s<f.value-1&&s>=t+1&&(i.size=i.min,e.nextReachedMinPanes+=i.min)}),l.value[a[0]].size=100-e.prevPanesSize-L(a[0]-1),null):{sums:e,panesToResize:a}},W=e=>l.value.reduce((n,t,a)=>n+(a<e?t.size:0),0),L=e=>l.value.reduce((n,t,a)=>n+(a>e+1?t.size:0),0),re=e=>[...l.value].reverse().find(n=>n.index<e&&n.size>n.min)||{},ue=e=>l.value.find(n=>n.index>e+1&&n.size>n.min)||{},de=()=>{var e;Array.from(((e=v.value)==null?void 0:e.children)||[]).forEach(n=>{const t=n.classList.contains("splitpanes__pane"),a=n.classList.contains("splitpanes__splitter");!t&&!a&&(n.remove(),console.warn("Splitpanes: Only <pane> elements are allowed at the root of <splitpanes>. One of your DOM nodes was removed."))})},H=(e,n,t=!1)=>{const a=e-1,i=document.createElement("div");i.classList.add("splitpanes__splitter"),t||(i.onmousedown=s=>D(s,a),typeof window<"u"&&"ontouchstart"in window&&(i.ontouchstart=s=>D(s,a)),i.onclick=s=>S(s,a+1)),u.dblClickSplitter&&(i.ondblclick=s=>$(s,a+1)),n.parentNode.insertBefore(i,n)},me=e=>{e.onmousedown=void 0,e.onclick=void 0,e.ondblclick=void 0,e.remove()},I=()=>{var e;const n=Array.from(((e=v.value)==null?void 0:e.children)||[]);n.forEach(a=>{a.className.includes("splitpanes__splitter")&&me(a)});let t=0;n.forEach(a=>{a.className.includes("splitpanes__pane")&&(!t&&u.firstSplitter?H(t,a,!0):t&&H(t,a),t++)})},ve=({uid:e,...n})=>{const t=M.value[e];Object.entries(n).forEach(([a,i])=>t[a]=i)},ce=e=>{var n;let t=-1;Array.from(((n=v.value)==null?void 0:n.children)||[]).some(a=>(a.className.includes("splitpanes__pane")&&t++,a.isSameNode(e.el))),l.value.splice(t,0,{...e,index:t}),l.value.forEach((a,i)=>a.index=i),g.value&&X(()=>{I(),q({addedPane:l.value[t]}),m("pane-add",{index:t,panes:l.value.map(a=>({min:a.min,max:a.max,size:a.size}))})})},pe=e=>{const n=l.value.findIndex(a=>a.id===e),t=l.value.splice(n,1)[0];l.value.forEach((a,i)=>a.index=i),X(()=>{I(),q({removedPane:{...t}}),m("pane-remove",{removed:t,panes:l.value.map(a=>({min:a.min,max:a.max,size:a.size}))})})},q=(e={})=>{!e.addedPane&&!e.removedPane?he():l.value.some(n=>n.givenSize!==null||n.min||n.max<100)?fe(e):ze(),g.value&&m("resized",l.value.map(n=>({min:n.min,max:n.max,size:n.size})))},ze=()=>{const e=100/f.value;let n=0;const t=[],a=[];l.value.forEach(i=>{i.size=Math.max(Math.min(e,i.max),i.min),n-=i.size,i.size>=i.max&&t.push(i.id),i.size<=i.min&&a.push(i.id)}),n>.1&&j(n,t,a)},he=()=>{let e=100;const n=[],t=[];let a=0;l.value.forEach(s=>{e-=s.size,s.givenSize!==null&&a++,s.size>=s.max&&n.push(s.id),s.size<=s.min&&t.push(s.id)});let i=100;e>.1&&(l.value.forEach(s=>{s.givenSize===null&&(s.size=Math.max(Math.min(e/(f.value-a),s.max),s.min)),i-=s.size}),i>.1&&j(i,n,t))},fe=({addedPane:e,removedPane:n}={})=>{let t=100/f.value,a=0;const i=[],s=[];((e==null?void 0:e.givenSize)??null)!==null&&(t=(100-e.givenSize)/(f.value-1).value),l.value.forEach(o=>{a-=o.size,o.size>=o.max&&i.push(o.id),o.size<=o.min&&s.push(o.id)}),!(Math.abs(a)<.1)&&(l.value.forEach(o=>{(e==null?void 0:e.givenSize)!==null&&(e==null?void 0:e.id)===o.id||(o.size=Math.max(Math.min(t,o.max),o.min)),a-=o.size,o.size>=o.max&&i.push(o.id),o.size<=o.min&&s.push(o.id)}),a>.1&&j(a,i,s))},j=(e,n,t)=>{let a;e>0?a=e/(f.value-n.length):a=e/(f.value-t.length),l.value.forEach((i,s)=>{if(e>0&&!n.includes(i.id)){const o=Math.max(Math.min(i.size+a,i.max),i.min),p=o-i.size;e-=p,i.size=o}else if(!t.includes(i.id)){const o=Math.max(Math.min(i.size+a,i.max),i.min),p=o-i.size;e-=p,i.size=o}}),Math.abs(e)>.1&&X(()=>{g.value&&console.warn("Splitpanes: Could not resize panes correctly due to their constraints.")})};B(()=>u.firstSplitter,()=>I()),B(()=>u.dblClickSplitter,e=>{[...v.value.querySelectorAll(".splitpanes__splitter")].forEach((n,t)=>{n.ondblclick=e?a=>$(a,t):void 0})}),ee(()=>g.value=!1),ae(()=>{de(),I(),q(),m("ready"),g.value=!0});const _e=()=>{var e;return be("div",{ref:v,class:b.value},(e=V.default)==null?void 0:e.call(V))};return k("panes",l),k("indexedPanes",M),k("horizontal",w(()=>u.horizontal)),k("requestUpdate",ve),k("onPaneAdd",ce),k("onPaneRemove",pe),k("onPaneClick",te),(e,n)=>(K(),Se(we(_e)))}},G={__name:"pane",props:{size:{type:[Number,String]},minSize:{type:[Number,String],default:0},maxSize:{type:[Number,String],default:100}},setup(A){var C;const m=A,u=N("requestUpdate"),V=N("onPaneAdd"),l=N("horizontal"),M=N("onPaneRemove"),f=N("onPaneClick"),v=(C=Me())==null?void 0:C.uid,g=N("indexedPanes"),c=w(()=>g.value[v]),d=R(null),b=w(()=>{const r=isNaN(m.size)||m.size===void 0?0:parseFloat(m.size);return Math.max(Math.min(r,E.value),y.value)}),y=w(()=>{const r=parseFloat(m.minSize);return isNaN(r)?0:r}),E=w(()=>{const r=parseFloat(m.maxSize);return isNaN(r)?100:r}),D=w(()=>{var r;return`${l.value?"height":"width"}: ${(r=c.value)==null?void 0:r.size}%`});return ae(()=>{V({id:v,el:d.value,min:y.value,max:E.value,givenSize:m.size===void 0?null:b.value,size:b.value})}),B(()=>b.value,r=>u({uid:v,size:r})),B(()=>y.value,r=>u({uid:v,min:r})),B(()=>E.value,r=>u({uid:v,max:r})),ee(()=>M(v)),(r,P)=>(K(),ne("div",{ref_key:"paneEl",ref:d,class:"splitpanes__pane",onClick:P[0]||(P[0]=S=>h(f)(S,r._.uid)),style:Re(D.value)},[Ee(r.$slots,"default")],4))}},Oe={class:"pane-box"},Le={class:"pane-box"},Be=ye({__name:"RoleDrawer",setup(A){const C=U(()=>T(()=>import("./RoleMenuTree.CdXe7kzO.js"),__vite__mapDeps([0,1,2,3,4,5,6,7]))),m=U(()=>T(()=>import("./RoleMenuBtn.gEelCGt0.js"),__vite__mapDeps([8,2,1,3,4,6,5,9,10]))),u=U(()=>T(()=>import("./RoleMenuField.aZtXatkc.js"),__vite__mapDeps([11,1,2,3,4,7,9,12]))),V=U(()=>T(()=>import("./RoleUsers.DVd5hrDj.js"),__vite__mapDeps([13,1,2,3,4,14]))),l=Ce(),M=Ve(),f=R("first"),v=R(!1),g=()=>{v.value=!0,M.get_all_users(),M.set_right_users(l.$state.users)};return(c,d)=>{const b=O("el-tag"),y=O("el-button"),E=O("el-tab-pane"),D=O("el-tabs"),r=O("el-drawer"),P=O("el-dialog");return K(),ne(ke,null,[z(r,{modelValue:h(l).drawerVisible,"onUpdate:modelValue":d[1]||(d[1]=S=>h(l).drawerVisible=S),title:"权限配置",direction:"rtl",size:"80%","close-on-click-modal":!1,"before-close":h(l).handleDrawerClose,"destroy-on-close":!0},{header:x(()=>[Y("div",null,[d[3]||(d[3]=F(" 当前授权角色： ")),z(b,{style:{"margin-right":"20px"}},{default:x(()=>[F(Z(h(l).roleName),1)]),_:1}),d[4]||(d[4]=F(" 授权人员： ")),z(y,{size:"small",icon:h(ge),onClick:g},{default:x(()=>[F(Z(h(l).users.length),1)]),_:1},8,["icon"])])]),default:x(()=>[z(h(Ne),{class:"default-theme",style:{height:"100%"}},{default:x(()=>[z(h(G),{"min-size":"20",size:"22"},{default:x(()=>[Y("div",Oe,[z(h(C))])]),_:1}),z(h(G),{"min-size":"20"},{default:x(()=>[Y("div",Le,[z(D,{modelValue:f.value,"onUpdate:modelValue":d[0]||(d[0]=S=>f.value=S),class:"demo-tabs"},{default:x(()=>[z(E,{label:"接口权限",name:"first"},{default:x(()=>[z(h(m))]),_:1}),z(E,{label:"列字段权限",name:"second"},{default:x(()=>[z(h(u))]),_:1})]),_:1},8,["modelValue"])])]),_:1})]),_:1})]),_:1},8,["modelValue","before-close"]),z(P,{modelValue:v.value,"onUpdate:modelValue":d[2]||(d[2]=S=>v.value=S),title:"授权用户",width:"700px","close-on-click-modal":!1},{default:x(()=>[z(h(V))]),_:1},8,["modelValue"])],64)}}}),$e=De(Be,[["__scopeId","data-v-7db8ef4a"]]);export{$e as default};
