import{d as l,M as m,c as d,h as _,a as i,o as p,e,s as a}from"./vue.BNx9QYep.js";import{a6 as u}from"./index.BHZI5pdK.js";import{_ as f}from"./_plugin-vue_export-helper.DlAUqK2U.js";const h={class:"home-bg"},v={class:"welcome-card"},w={class:"welcome-content"},x={class:"welcome-username"},g={class:"welcome-quote"},B=l({__name:"index",setup(I){const{userInfos:c}=m(u()),n=d(()=>{var s;return((s=c.value)==null?void 0:s.name)||"用户"}),o=["今天的努力，是明天的基石。","成功不是偶然，而是日积月累的结果。","每一次尝试都是成长的机会。","坚持不懈，直到成功。","态度决定高度，细节决定成败。","没有口水与汗水，就没有成功的泪水。","机会总是留给有准备的人。","行动是成功的阶梯，行动越多，登得越高。","不经历风雨，怎能见彩虹。","志之所趋，无远勿届，穷山距海，不能限也。"],r=_(o[Math.floor(Math.random()*o.length)]);return(s,t)=>(p(),i("div",h,[e("div",v,[t[0]||(t[0]=e("div",{class:"welcome-header"},[e("h1",{class:"welcome-title"},"欢迎")],-1)),e("div",w,[e("p",x,a(n.value),1),e("p",g,a(r.value),1)])])]))}}),b=f(B,[["__scopeId","data-v-e65251c0"]]);export{b as default};
