import{a as c}from"./index.BHZI5pdK.js";import{createCrudOptions as p}from"./crud.PDViVUkv.js";import{d as o,j as f,k as e,b as u,o as _,w as d,l as m,x as i,u as l}from"./vue.BNx9QYep.js";import"./dictionary.DNsEqk19.js";import"./authFunction.D3Be3hRy.js";const x=o({name:"whiteList"}),j=o({...x,setup(g){const{crudBinding:r,crudRef:t,crudExpose:s}=c({createCrudOptions:p});return f(()=>{s.doRefresh()}),(h,k)=>{const n=e("fs-crud"),a=e("fs-page");return _(),u(a,null,{default:d(()=>[m(n,i({ref_key:"crudRef",ref:t},l(r)),null,16)]),_:1})}}});export{j as default};
