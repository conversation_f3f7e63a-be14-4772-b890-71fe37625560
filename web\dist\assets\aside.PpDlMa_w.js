const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.CFJPG_54.js","assets/vue.BNx9QYep.js","assets/index.BHZI5pdK.js","assets/index.Dg-OhEXY.css","assets/favicon.nG7g_iy4.js","assets/_plugin-vue_export-helper.DlAUqK2U.js","assets/index.B33z1et1.css","assets/vertical.BNazyepz.js"])))=>i.map(i=>d[i]);
import{R as O,u as q,T as z,l as u,U as H,_ as S}from"./index.BHZI5pdK.js";import{d as I,Q as $,h as F,M as p,g as j,c as x,N as Q,v as R,k as w,A as U,D as G,u as v,a as J,o as A,l as y,w as M,b as K,m as X,P as T,f as Y}from"./vue.BNx9QYep.js";const Z={class:"h100"},ee=I({name:"layoutAside"}),le=I({...ee,setup(te){const B=$(),E=T(()=>S(()=>import("./index.CFJPG_54.js"),__vite__mapDeps([0,1,2,3,4,5,6]))),V=T(()=>S(()=>import("./vertical.BNazyepz.js"),__vite__mapDeps([7,2,1,3]))),d=F(),f=F(0),_=O(),W=q(),k=z(),{routesList:r}=p(_),{themeConfig:l}=p(W),{isTagsViewCurrenFull:D}=p(k),n=j({menuList:[],clientWidth:0}),N=x(()=>{const{layout:e,isCollapse:t,menuBar:s}=l.value,o=["#FFFFFF","#FFF","#fff","#ffffff"].includes(s)?"layout-el-aside-br-color":"";if(n.clientWidth<=1e3)if(t){document.body.setAttribute("class","el-popup-parent--hidden");const g=document.querySelector(".layout-container"),a=document.createElement("div");return a.setAttribute("class","layout-aside-mobile-mode"),g.appendChild(a),a.addEventListener("click",m),[o,"layout-aside-mobile","layout-aside-mobile-open"]}else return m(),[o,"layout-aside-mobile","layout-aside-mobile-close"];else return e==="columns"?t?[o,"layout-aside-pc-1"]:[o,"layout-aside-pc-220"]:t?[o,"layout-aside-pc-64"]:[o,"layout-aside-pc-220"]}),P=x(()=>{let{layout:e,isShowLogo:t}=l.value;return t&&e==="defaults"||t&&e==="columns"}),m=()=>{const e=document.querySelector(".layout-aside-mobile-mode");e==null||e.setAttribute("style","animation: error-img-two 0.3s"),setTimeout(()=>{var s;(s=e==null?void 0:e.parentNode)==null||s.removeChild(e)},300),document.body.clientWidth<1e3&&(l.value.isCollapse=!1),document.body.setAttribute("class","")},C=(e,t)=>{for(let s=0;s<e.length;s++){const i=e[s];if(i.children&&i.children.length>0&&(i.children.findIndex(a=>a.path===t)!==-1||C(i.children,t)!==null))return s}return null},c=(e="")=>{if(l.value.layout==="columns")return!1;let{layout:t,isClassicSplitMenu:s}=l.value;t==="classic"&&s?(f.value=C(r.value,e||B.path)||0,n.menuList=h(r.value[f.value].children||[r.value[f.value]])):n.menuList=h(r.value)},h=e=>e.filter(t=>{var s;return!((s=t.meta)!=null&&s.isHide)}).map(t=>(t=Object.assign({},t),t.children&&(t.children=h(t.children)),t)),b=e=>{n.clientWidth=e},L=e=>{let{layout:t}=l.value;if(t!=="columns")return!1;e||u.emit("restoreDefault"),_.setColumnsMenuHover(e)};return Q(()=>{b(document.body.clientWidth),c(),u.on("setSendColumnsChildren",e=>{n.menuList=e.children}),u.on("setSendClassicChildren",e=>{let{layout:t,isClassicSplitMenu:s}=l.value;t==="classic"&&s&&(n.menuList=[],c(e.path))}),u.on("getBreadcrumbIndexSetFilterRoutes",()=>{c()}),u.on("layoutMobileResize",e=>{b(e.clientWidth),m()})}),R(l.value,e=>{e.isShowLogoChange!==e.isShowLogo&&d.value&&d.value.update()}),R(H.state,e=>{let{layout:t,isClassicSplitMenu:s}=e.themeConfig.themeConfig;if(t==="classic"&&s)return!1;c()},{deep:!0}),(e,t)=>{const s=w("el-scrollbar"),i=w("el-aside");return U((A(),J("div",Z,[y(i,{class:Y(["layout-aside",N.value])},{default:M(()=>[P.value?(A(),K(v(E),{key:0})):X("",!0),y(s,{class:"flex-auto",ref_key:"layoutAsideScrollbarRef",ref:d,onMouseenter:t[0]||(t[0]=o=>L(!0)),onMouseleave:t[1]||(t[1]=o=>L(!1))},{default:M(()=>[y(v(V),{menuList:n.menuList},null,8,["menuList"])]),_:1},512)]),_:1},8,["class"])],512)),[[G,!v(D)]])}}});export{le as default};
