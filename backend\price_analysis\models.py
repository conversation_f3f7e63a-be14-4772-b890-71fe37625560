from django.db import models
from dvadmin.utils.models import CoreModel

class PriceAnalysisResult(CoreModel):
    """
    价格分析结果模型 - 用于缓存分析结果
    """
    query_year = models.IntegerField(verbose_name="查询年份", help_text="查询年份")
    query_month = models.IntegerField(verbose_name="查询月份", help_text="查询月份")
    company_name = models.CharField(max_length=100, verbose_name="公司名称", help_text="公司名称")
    material_code = models.CharField(max_length=50, verbose_name="物料编码", help_text="物料编码")
    material_name = models.CharField(max_length=200, verbose_name="物料名称", help_text="物料名称")
    customer_name = models.CharField(max_length=200, verbose_name="客户名称", help_text="客户名称")
    
    # 价格数据
    customer_avg_price = models.DecimalField(max_digits=10, decimal_places=4, verbose_name="客户平均单价")
    market_avg_price = models.DecimalField(max_digits=10, decimal_places=4, verbose_name="市场平均单价")
    price_deviation = models.DecimalField(max_digits=5, decimal_places=2, verbose_name="价格偏离百分比")
    
    # 统计数据
    transaction_count = models.IntegerField(verbose_name="交易次数", default=0)
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, verbose_name="购买总额", default=0)
    total_quantity = models.DecimalField(max_digits=15, decimal_places=4, verbose_name="购买总量", default=0)
    
    # 分析结果
    deviation_level = models.CharField(max_length=50, verbose_name="偏离等级", help_text="偏离等级")
    deviation_direction = models.CharField(max_length=20, verbose_name="偏离方向", help_text="偏离方向")
    customer_value_level = models.CharField(max_length=20, verbose_name="客户价值等级", help_text="客户价值等级")
    risk_warning = models.CharField(max_length=50, verbose_name="风险提示", help_text="风险提示")
    
    class Meta:
        db_table = "price_analysis_result"
        verbose_name = "价格分析结果"
        verbose_name_plural = verbose_name
        ordering = ["-create_datetime"]
        indexes = [
            models.Index(fields=['query_year', 'query_month']),
            models.Index(fields=['company_name']),
            models.Index(fields=['material_code']),
            models.Index(fields=['customer_name']),
        ]
