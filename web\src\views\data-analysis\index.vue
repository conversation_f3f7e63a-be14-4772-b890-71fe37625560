<template>
  <div class="data-analysis-container">
    <!-- 页面标题 -->
    <el-card class="header-card">
      <template #header>
        <div class="card-header">
          <div class="header-info">
            <span class="update-time">更新时间: {{ dashboardData.updateTime }}</span>
            <el-button type="primary" size="small" @click="refreshData" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新数据
            </el-button>
          </div>
        </div>
      </template>

      <div class="welcome-content">
        <h2>{{ dashboardData.currentMonth.year }}年{{ dashboardData.currentMonth.month }}月销售数据概览</h2>
        <p>实时展示当月销售业绩、各公司销售情况和趋势分析</p>
      </div>
    </el-card>

    <!-- 当月销售统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card sales-amount" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon size="32"><Money /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-title">当月销售总额</div>
                <div class="stat-value">{{ formatCurrency(dashboardData.currentMonth.statistics.销售总额) }}</div>
                <div class="stat-desc">
                  <span :class="getGrowthClass(dashboardData.currentMonth.growthRate)">
                    <el-icon><component :is="getGrowthIcon(dashboardData.currentMonth.growthRate)" /></el-icon>
                    {{ formatPercentage(Math.abs(dashboardData.currentMonth.growthRate)) }}
                  </span>
                  环比上月
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stat-card orders" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon size="32"><Document /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-title">订单数量</div>
                <div class="stat-value">{{ dashboardData.currentMonth.statistics.订单数量 }}</div>
                <div class="stat-desc">当月成交订单总数</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stat-card customers" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon size="32"><UserFilled /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-title">客户数量</div>
                <div class="stat-value">{{ dashboardData.currentMonth.statistics.客户数量 }}</div>
                <div class="stat-desc">当月活跃客户总数</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stat-card products" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon size="32"><Box /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-title">产品种类</div>
                <div class="stat-value">{{ dashboardData.currentMonth.statistics.产品种类 }}</div>
                <div class="stat-desc">当月销售产品种类</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表和表格区域 -->
    <el-row :gutter="20" class="charts-section">
      <!-- 各公司销售金额汇总 -->
      <el-col :span="12">
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="chart-header">
              <el-icon size="20" color="#67C23A"><TrendCharts /></el-icon>
              <span>各公司销售金额汇总</span>
            </div>
          </template>

          <div class="company-sales-table">
            <el-table :data="dashboardData.companySales" stripe style="width: 100%" max-height="400">
              <el-table-column prop="公司名称" label="公司名称" width="300" show-overflow-tooltip>
                <template #default="{ row }">
                  <span class="company-name">{{ getCompanyName(row.公司名称) }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="销售金额" label="销售金额" align="right" sortable>
                <template #default="{ row }">
                  <span class="amount-text">{{ formatCurrency(row.销售金额) }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="订单数量" label="订单数" align="right" sortable>
                <template #default="{ row }">
                  <span>{{ row.订单数量 }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="客户数量" label="客户数" align="right" sortable>
                <template #default="{ row }">
                  <span>{{ row.客户数量 }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>

      <!-- 销售趋势图表 -->
      <el-col :span="12">
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="chart-header">
              <el-icon size="20" color="#E6A23C"><TrendCharts /></el-icon>
              <span>近6个月销售趋势</span>
            </div>
          </template>

          <div class="trend-chart" ref="trendChartRef" style="height: 400px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快捷功能区域 -->
    <el-card class="feature-card-container" shadow="hover">
      <template #header>
        <div class="chart-header">
          <el-icon size="20" color="#409EFF"><Grid /></el-icon>
          <span>快捷功能</span>
        </div>
      </template>

      <div class="feature-grid">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card class="feature-card" shadow="hover" @click="goToPage('/price-analysis')">
              <el-icon size="48" color="#F56C6C">
                <PriceTag />
              </el-icon>
              <h3>价格分析</h3>
              <p>监控价格变化和市场趋势</p>
            </el-card>
          </el-col>

          <el-col :span="8">
            <el-card class="feature-card" shadow="hover">
              <el-icon size="48" color="#67C23A">
                <TrendCharts />
              </el-icon>
              <h3>销售分析</h3>
              <p>分析销售数据趋势和业绩</p>
            </el-card>
          </el-col>

          <el-col :span="8">
            <el-card class="feature-card" shadow="hover">
              <el-icon size="48" color="#E6A23C">
                <User />
              </el-icon>
              <h3>客户分析</h3>
              <p>深入了解客户行为和偏好</p>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts" name="DataAnalysisIndex">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  DataAnalysis,
  TrendCharts,
  User,
  PriceTag,
  Money,
  UserFilled,
  Document,
  Box,
  Refresh,
  Grid,
  ArrowUp,
  ArrowDown,
  Minus
} from '@element-plus/icons-vue'
import {
  getDashboardOverview,
  formatCurrency,
  formatPercentage,
  getGrowthRateType,
  getGrowthRateIcon
} from '/@/api/data-analysis/index'
import * as echarts from 'echarts'

const router = useRouter()
const loading = ref(false)
const trendChartRef = ref()
let trendChart: echarts.ECharts | null = null

// 仪表板数据
const dashboardData = reactive({
  currentMonth: {
    year: new Date().getFullYear(),
    month: new Date().getMonth() + 1,
    statistics: {
      销售总额: 0,
      订单数量: 0,
      客户数量: 0,
      产品种类: 0,
      销售数量: 0
    },
    growthRate: 0,
    lastMonthAmount: 0
  },
  companySales: [],
  trendData: [],
  updateTime: ''
})

// 获取公司名称（显示完整名称）
const getCompanyName = (fullName: string): string => {
  return fullName || '未知公司'
}

// 获取增长率样式类
const getGrowthClass = (growthRate: number): string => {
  if (growthRate > 0) {
    return 'growth-positive'
  } else if (growthRate < 0) {
    return 'growth-negative'
  } else {
    return 'growth-neutral'
  }
}

// 获取增长率图标
const getGrowthIcon = (growthRate: number): string => {
  if (growthRate > 0) {
    return 'ArrowUp'
  } else if (growthRate < 0) {
    return 'ArrowDown'
  } else {
    return 'Minus'
  }
}

// 初始化趋势图表
const initTrendChart = () => {
  if (!trendChartRef.value) return

  trendChart = echarts.init(trendChartRef.value)

  const option = {
    title: {
      text: '销售金额趋势',
      left: 'center',
      textStyle: {
        fontSize: 14,
        color: '#303133'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const data = params[0]
        return `${data.name}<br/>销售金额: ${formatCurrency(data.value)}元`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dashboardData.trendData.map((item: any) => item.月份),
      axisLabel: {
        color: '#606266'
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        color: '#606266',
        formatter: (value: number) => {
          if (value >= 10000) {
            return (value / 10000).toFixed(1) + '万'
          }
          return value.toString()
        }
      }
    },
    series: [
      {
        name: '销售金额',
        type: 'line',
        smooth: true,
        data: dashboardData.trendData.map((item: any) => item.销售金额),
        lineStyle: {
          color: '#409EFF',
          width: 3
        },
        itemStyle: {
          color: '#409EFF'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
              { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
            ]
          }
        }
      }
    ]
  }

  trendChart.setOption(option)
}

// 更新趋势图表
const updateTrendChart = () => {
  if (!trendChart) return

  const option = {
    xAxis: {
      data: dashboardData.trendData.map((item: any) => item.月份)
    },
    series: [
      {
        data: dashboardData.trendData.map((item: any) => item.销售金额)
      }
    ]
  }

  trendChart.setOption(option)
}

// 刷新数据
const refreshData = async () => {
  loading.value = true
  try {
    const response = await getDashboardOverview()

    if (response.code === 200) {
      Object.assign(dashboardData, response.data)

      // 更新图表
      await nextTick()
      updateTrendChart()

      ElMessage.success('数据刷新成功')
    } else {
      ElMessage.error(response.message || '数据获取失败')
    }
  } catch (error) {
    console.error('数据获取失败:', error)
    ElMessage.error('数据获取失败：' + (error.message || '网络错误'))
  } finally {
    loading.value = false
  }
}

// 跳转到指定页面
const goToPage = (path: string) => {
  router.push(path)
}

// 组件挂载时初始化
onMounted(async () => {
  await refreshData()
  await nextTick()
  initTrendChart()

  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    if (trendChart) {
      trendChart.resize()
    }
  })
})
</script>

<style scoped>
.data-analysis-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: calc(100vh - 84px);
}

/* 页面标题区域 */
.header-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.card-header .title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
  margin-left: 10px;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.update-time {
  color: #909399;
  font-size: 14px;
}

.welcome-content {
  text-align: center;
}

.welcome-content h2 {
  color: #303133;
  margin-bottom: 10px;
  font-size: 24px;
}

.welcome-content p {
  color: #606266;
  margin-bottom: 0;
  font-size: 16px;
}

/* 统计卡片区域 */
.stats-section {
  margin-bottom: 20px;
}

.stat-card {
  border: none;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 20px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
}

.sales-amount .stat-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.orders .stat-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.customers .stat-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.products .stat-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
}

.stat-info {
  flex: 1;
}

.stat-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stat-desc {
  font-size: 12px;
  color: #606266;
  display: flex;
  align-items: center;
  gap: 5px;
}

.growth-positive {
  color: #67c23a;
}

.growth-negative {
  color: #f56c6c;
}

.growth-neutral {
  color: #909399;
}

/* 图表区域 */
.charts-section {
  margin-bottom: 20px;
}

.chart-card {
  border: none;
  border-radius: 12px;
  height: 500px;
}

.chart-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.company-sales-table {
  height: 400px;
  overflow-y: auto;
}

.company-name {
  font-weight: 500;
  color: #303133;
}

.amount-text {
  font-weight: bold;
  color: #409eff;
}

.trend-chart {
  width: 100%;
  height: 400px;
}

/* 功能卡片区域 */
.feature-card-container {
  border: none;
  border-radius: 12px;
}

.feature-grid {
  margin-top: 20px;
}

.feature-card {
  text-align: center;
  padding: 30px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #EBEEF5;
  border-radius: 12px;
  background: white;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #409eff;
}

.feature-card h3 {
  color: #303133;
  margin: 15px 0 10px 0;
  font-size: 18px;
  font-weight: bold;
}

.feature-card p {
  color: #909399;
  font-size: 14px;
  margin: 0;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
  color: #303133;
  font-weight: bold;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: #fafbfc;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stat-content {
    flex-direction: column;
    text-align: center;
  }

  .stat-icon {
    margin-right: 0;
    margin-bottom: 15px;
  }
}

@media (max-width: 768px) {
  .data-analysis-container {
    padding: 10px;
  }

  .card-header {
    flex-direction: column;
    gap: 15px;
  }

  .header-info {
    width: 100%;
    justify-content: center;
  }

  .welcome-content h2 {
    font-size: 20px;
  }

  .stat-value {
    font-size: 24px;
  }
}
</style>
