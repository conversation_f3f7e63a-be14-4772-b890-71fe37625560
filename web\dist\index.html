<!DOCTYPE html>
<html lang="zh-CN">
	<head>
		<meta charset="utf-8" />
		<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<meta
			name="keywords"
			content="django-vue3-admin"
		/>
		<meta
			name="description"
			content="django-vue-admin 基于RBAC模型的权限控制的一整套基础开发平台，权限粒度达到列级别，前后端分离，后端采用django + django-rest-framework，前端采用基于 vue3 + CompositionAPI + typescript + vite + element plus"
		/>
		<link rel="icon" href="/favicon.ico" />
		<title>django-vue-admin</title>
		<script type="module" crossorigin src="/assets/index.BHZI5pdK.js"></script>
		<link rel="modulepreload" crossorigin href="/assets/vue.BNx9QYep.js">
		<link rel="stylesheet" crossorigin href="/assets/index.Dg-OhEXY.css">
	</head>
	<body>
    <div id="app"></div>
    <script>
        var _hmt = _hmt || [];
        (function() {
          var hm = document.createElement("script");
          hm.src = "https://hm.baidu.com/hm.js?9ba8fc809b5584167a2fb9b31bb3970c";
          var s = document.getElementsByTagName("script")[0];
          s.parentNode.insertBefore(hm, s);
        })();
    </script>
	</body>
</html>
