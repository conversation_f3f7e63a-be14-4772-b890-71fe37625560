import{d as n,h as c,a as e,o as l,e as a,F as p,p as _,q as x,s}from"./vue.BNx9QYep.js";const u={class:"popup-result maxh"},i={class:"popup-result-scroll"},d={class:"ftcolor"},h=n({__name:"normal",setup(m){const b=c([{ex:"0/2 * * * ?",label:"表示每2分钟 执行任务"},{ex:"0 2 1 * ?",label:"表示在每月的1日的凌晨2点调整任务"},{ex:"15 10 ? * 1-5",label:"表示周一到周五每天上午10:15执行作业"},{ex:"15 10 ? 6L 2002-2006",label:"表示2002-2006年的每个月的最后一个星期五上午10:15执行作"},{ex:"0 10,14,16 * * ?",label:"每天上午10点，下午2点，4点"},{ex:"0/30 9-17 * * ?",label:"朝九晚五工作时间内每半小时"},{ex:"0 12 ? * 3",label:"表示每个星期三中午12点"},{ex:"0 12 * * ?",label:"每天中午12点触发"},{ex:"15 10 ? * *",label:"每天上午10:15触发"},{ex:"15 10 * * ?",label:"每天上午10:15触发"},{ex:"15 10 * * ? 2005",label:"2005年的每天上午10:15触发"},{ex:"0/5 14 * * ?",label:"在每天下午2点到下午2:00期间的每5分钟触发"},{ex:"0/5 14,18 * * ?",label:"在每天下午2点到2:55期间和下午6点到6:55期间的每5分钟触发"},{ex:"0-5 14 * * ?",label:"在每天下午2点到下午2:05期间的每1分钟触发"},{ex:"10,44 14 ? 3 3",label:"每年三月的星期三的下午2:10和2:44触发"},{ex:"15 10 ? * 1-5",label:"周一至周五的上午10:15触发"},{ex:"15 10 15 * ?",label:"每月15日上午10:15触发"},{ex:"15 10 L * ?",label:"每月最后一日的上午10:15触发"},{ex:"15 10 ? * 6L",label:"每月的最后一个星期五上午10:15触发"},{ex:"15 10 ? * 6L 2002-2005",label:"2002年至2005年的每月的最后一个星期五上午10:15触发"},{ex:"15 10 ? * 6#3",label:"每月的第三个星期五上午10:15触发"}]);return(L,t)=>(l(),e("div",u,[t[0]||(t[0]=a("p",{class:"title"},"常用cron表达式例子",-1)),a("ul",i,[(l(!0),e(p,null,_(b.value,(o,r)=>(l(),e("li",{key:r},[x(" ("+s(r+1)+") ",1),a("span",d,s(o.ex),1),x(" "+s(o.label),1)]))),128))])]))}});export{h as _};
