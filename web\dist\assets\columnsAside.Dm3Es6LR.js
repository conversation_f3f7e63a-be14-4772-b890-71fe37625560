import{d as O,h as M,M as H,Q as B,R as j,g as U,j as V,O as z,X as E,v as Q,k as $,a as c,o as d,l as v,w as X,e as m,F as q,p as G,f as C,u as f,s as R,I as J}from"./vue.BNx9QYep.js";import{R as K,u as W,l as i,U as Y}from"./index.BHZI5pdK.js";import{_ as Z}from"./_plugin-vue_export-helper.DlAUqK2U.js";const ee={class:"layout-columns-aside"},te=["onClick","onMouseenter","title"],se={class:"columns-vertical-title font12"},ne=["href"],oe={class:"columns-vertical-title font12"},le=O({name:"layoutColumnsAside"}),ie=O({...le,setup(ae){const A=M([]),_=M(),h=K(),k=W(),{routesList:x,isColumnsMenuHover:T,isColumnsNavHover:b}=H(h),{themeConfig:a}=H(k),y=B(),g=j(),t=U({columnsAsideList:[],liIndex:0,liOldIndex:null,liHoverIndex:null,liOldPath:null,difference:0,routeSplit:[]}),I=e=>{t.liIndex=e,_.value.style.top=`${A.value[e].offsetTop+t.difference}px`},D=(e,s)=>{I(s);let{path:o,redirect:l}=e;l?g.push(l):g.push(o)},P=(e,s)=>{if(!a.value.isColumnsMenuHoverPreload)return!1;let{path:o}=e;t.liOldPath=o,t.liOldIndex=s,t.liHoverIndex=s,i.emit("setSendColumnsChildren",r(o)),h.setColumnsMenuHover(!1),h.setColumnsNavHover(!0)},N=async()=>{await h.setColumnsNavHover(!1),setTimeout(()=>{!T&&!b&&i.emit("restoreDefault")},100)},L=e=>{J(()=>{I(e)})},w=()=>{var s;t.columnsAsideList=S(x.value);const e=r(y.path);if(Object.keys(e).length<=0)return!1;L((s=e.item)==null?void 0:s.k),i.emit("setSendColumnsChildren",e)},r=e=>{const s=e.split("/");let o={children:[]};return t.columnsAsideList.map((l,n)=>{l.path===`/${s[1]}`&&(l.k=n,o.item={...l},o.children=[{...l}],l.children&&(o.children=l.children))}),o},S=e=>e.filter(s=>{var o;return!((o=s.meta)!=null&&o.isHide)}).map(s=>(s=Object.assign({},s),s.children&&(s.children=S(s.children)),s)),F=e=>{t.routeSplit=e.split("/"),t.routeSplit.shift();const s=`/${t.routeSplit[0]}`,o=t.columnsAsideList.find(l=>l.path===s);if(!o)return!1;setTimeout(()=>{L(o.k)},0)};return V(()=>{w(),i.on("restoreDefault",()=>{t.liOldIndex=null,t.liOldPath=null})}),z(()=>{i.off("restoreDefault",()=>{})}),E(e=>{F(e.path),i.emit("setSendColumnsChildren",r(e.path))}),Q(Y.state,e=>{if(e.themeConfig.themeConfig.columnsAsideStyle==="columnsRound"?t.difference=3:t.difference=0,!e.routesList.isColumnsMenuHover&&!e.routesList.isColumnsNavHover)t.liHoverIndex=null,i.emit("setSendColumnsChildren",r(y.path));else{if(t.liHoverIndex=t.liOldIndex,!t.liOldPath)return!1;i.emit("setSendColumnsChildren",r(t.liOldPath))}},{deep:!0}),(e,s)=>{const o=$("SvgIcon"),l=$("el-scrollbar");return d(),c("div",ee,[v(l,null,{default:X(()=>[m("ul",{onMouseleave:s[0]||(s[0]=n=>N())},[(d(!0),c(q,null,G(t.columnsAsideList,(n,u)=>(d(),c("li",{key:u,onClick:p=>D(n,u),onMouseenter:p=>P(n,u),ref_for:!0,ref:p=>{p&&(A.value[u]=p)},class:C({"layout-columns-active":t.liIndex===u,"layout-columns-hover":t.liHoverIndex===u}),title:e.$t(n.meta.title)},[!n.meta.isLink||n.meta.isLink&&n.meta.isIframe?(d(),c("div",{key:0,class:C(f(a).columnsAsideLayout)},[v(o,{name:n.meta.icon},null,8,["name"]),m("div",se,R(e.$t(n.meta.title)&&e.$t(n.meta.title).length>=4?e.$t(n.meta.title).substr(0,f(a).columnsAsideLayout==="columns-vertical"?4:3):e.$t(n.meta.title)),1)],2)):(d(),c("div",{key:1,class:C(f(a).columnsAsideLayout)},[m("a",{href:n.meta.isLink,target:"_blank"},[v(o,{name:n.meta.icon},null,8,["name"]),m("div",oe,R(e.$t(n.meta.title)&&e.$t(n.meta.title).length>=4?e.$t(n.meta.title).substr(0,f(a).columnsAsideLayout==="columns-vertical"?4:3):e.$t(n.meta.title)),1)],8,ne)],2))],42,te))),128)),m("div",{ref_key:"columnsAsideActiveRef",ref:_,class:C(f(a).columnsAsideStyle)},null,2)],32)]),_:1})])}}}),de=Z(ie,[["__scopeId","data-v-e9a1a406"]]);export{de as default};
