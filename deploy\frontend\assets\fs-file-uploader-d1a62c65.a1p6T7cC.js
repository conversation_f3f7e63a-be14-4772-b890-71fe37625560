import{j as pe,k as de,o as me,m as C,f as ye,C as we}from"./index.BHZI5pdK.js";import{d as ve,a as ge,o as w,b as P,m as he,w as G,r as z,G as be,H as xe,x as H,e as Le,f as Ue,h as y,c as v,v as Te,I as Fe}from"./vue.BNx9QYep.js";const Re=ve({name:"FsFileUploader",inheritAttrs:!1,props:{modelValue:{},limit:{type:Number},sizeLimit:{type:[Number,Object]},pixelLimit:{type:Object,required:!1},showLimitTip:{type:Boolean,default:!0},buildUrl:{default(){return l=>l}},buildUrls:{},button:{type:Object},listType:{type:String},beforeUpload:{type:Function},beforeUploadRequest:{type:Function},uploader:{type:Object},preview:{type:Object},valueType:{type:String,default:"url"},getFileName:{}},emits:["change","update:modelValue","success","exceed","remove"],setup(l,r){const{ui:s}=de(),{t:c}=me(),n=y([]),g=y(),p=y(),K=v(()=>l.getFileName||(async e=>{if(typeof e!="string")return console.warn("获取文件名失败，请配置getFileName"),e;const t=e.substring(e.lastIndexOf("/")+1),i=t.indexOf("?");return i>=0?t.substring(0,i):t}));function h(e){return l.valueType==="object"?e:e[l.valueType]}function $(e){const t=[];for(let i of e)t.push(h(i));return t}async function J(e){const t=[];for(let i of e){let o;typeof i=="string"||typeof i=="number"||typeof i=="boolean"||i instanceof Date?(o={url:void 0,key:i,value:i},l.valueType!=="object"&&(o[l.valueType]=i)):o=i,o[s.upload.id]||(o[s.upload.id]=Math.random()+""),o.status||(o.status=s.upload.status.success),t.push(o)}await I(t);for(const i of t)if(!i.name){const o=i.url||i.value;i.name=await K.value(o,i)}return t}async function B(e){const t=[];for(let i of e){const o=i.response||i.fsRes,a={size:i.size,name:i.name,uid:i.uid,...o??i};t.push(a)}return await I(t),$(t)}async function b(e){const t=[];if(e==null||e.length===0){n.value=t;return}if(e instanceof Array)for(let o of e)t.push(o);else t.push(e);const i=await J(t);N(i)}async function Q(){await O.onChange(),await O.onBlur()}async function j(e){let t=await W(e);k(t),Fe(async()=>{await Q()})}async function W(e){if(e==null||e.length===0)return l.limit===1?null:[];if(l.limit===1)return(await B(e))[0];const t=[];for(let i of e)s.upload.isSuccess(i)&&t.push(i);return await B(t)}async function I(e){let t=e.filter(i=>i.url==null);if(l.buildUrls){const i=t.map(a=>h(a)),o=await l.buildUrls(i);for(let a=0;a<t.length;a++)t[a].url=o[a]}else if(l.buildUrl)for(let i of t)i.url=await l.buildUrl(h(i));else for(let i of t)i.url=i.value||i.key}function V(e){r.emit("change",e)}function k(e){g.value=e,r.emit("update:modelValue",e)}const O=s.formItem.injectFormItemContext();Te(()=>l.modelValue,async e=>{V(e),e!==g.value&&await b(e)}),b(l.modelValue);function X(){return n.value.filter(e=>e.status===s.upload.status.uploading).length>0}function d(e,t){N(t),j(t)}function x(e,t,i){r.emit("success",{res:e,file:t,fileList:i}),d(t,i)}function S(e){let t;return e>1024*1024*1024?t=(e/(1024*1024*1024)).toFixed(2)+"G":e>1024*1024?t=(e/(1024*1024)).toFixed(2)+"M":t=Math.round(e/1024)+"K",t}const E=(e=!1)=>{const t=e?s.upload.limitAdd:0;return l.limit>0&&n.value.length>=l.limit+t};function Y(){l.showLimitTip&&s.message.warn(c("fs.extends.fileUploader.limitTip",[l.limit]))}function Z(){if(E(!0))throw Y(),r.emit("exceed",{fileList:n.value,limit:l.limit}),new Error("文件数量超限")}function ee(e){if(l.sizeLimit!=null){let t=l.sizeLimit,i=null;if(typeof l.sizeLimit=="number"?i=(o,a)=>{const f=S(a),u=S(e.size);s.message.warn(c("fs.extends.fileUploader.sizeLimitTip",[f,u]))}:(t=l.sizeLimit.limit,i=l.sizeLimit.tip),e.size>t){let o="文件大小超过限制，当前文件大小："+e.size/1024+"k";throw i(e.size,t),new Error(o)}}}const te=e=>{let t=0,i=0,o="";if(l.pixelLimit)Array.isArray(l.pixelLimit)?(t=l.pixelLimit[0],i=l.pixelLimit[1]||l.pixelLimit[0]||0,o=l.pixelLimit[2]||""):typeof l.pixelLimit=="object"&&(t=l.pixelLimit.width||0,i=l.pixelLimit.height||0,o=l.pixelLimit.tip||"");else return Promise.resolve(!0);let a=o||c("fs.extends.fileUploader.pixelLimitTip",[t,i]);return new Promise((f,u)=>{let D=new FileReader;D.onload=fe=>{var M;let _=(M=fe.target)==null?void 0:M.result,m=new Image;m.onload=function(){t&&m.width>t||i&&m.height>i?(s.message.warn(a),u(a)):f(!0)},m.onerror=function(ze){s.message.warn(c("fs.extends.fileUploader.loadError")),u(c("fs.extends.fileUploader.loadError"))},_&&(m.src=_)},D.readAsDataURL(e)})},L=async(e,t=n.value)=>{if(l.beforeUpload&&await l.beforeUpload({file:e,fileList:n.value})===!1)return!1;try{Z(),ee(e),T()&&await te(e)}catch{return!1}};function N(e){n.value=e}async function ie(e){e.options=l.uploader||{};const{getUploaderImpl:t}=we();let i=await t(e.options.type);if(i==null)throw s.message.warn("Sorry，The uploader component is not ready yet"),new Error("Sorry，The component is not ready yet");return await(i==null?void 0:i.upload(e))}async function U(e){l.beforeUploadRequest&&await l.beforeUploadRequest(e);const{file:t,onProgress:i,onSuccess:o,onError:a}=e,f={file:t,fileName:t.name,onProgress:i};try{const u=await ie(f);o(u)}catch(u){console.error("上传失败",u),a(u)}}const le=v(()=>ae()?{is:"FsIcon",icon:s.icons.plus}:{is:"FsButton",icon:s.icons.upload,text:c("fs.extends.fileUploader.text"),...l.button}),q=y(!1),A=y(),oe=v(()=>({...s.dialog.footer(),...l.preview}));function se(e){return new Promise((t,i)=>{const o=new FileReader;o.readAsDataURL(e),o.onload=()=>t(o.result),o.onerror=a=>i(a)})}function T(){return l.listType===s.upload.typeImageCard||l.listType===s.upload.typeImage}function ae(){return l.listType===s.upload.typeImageCard}const F=async e=>{var t,i;if(!T()){let o;e.url?o=e.url:s.type==="antdv"?o=(t=e.response)==null?void 0:t.url:s.type==="element"?o=(i=e.fsRes)==null?void 0:i.url:o=e.url,window.open(o,"_blank")}!e.url&&!e.preview&&e.originFileObj&&(e.preview=await se(e.originFileObj)),A.value=e.url||e.preview,q.value=!0};function re(){const e={customRequest:U,beforeUpload:L,limit:l.limit,listType:l.listType,onChange:t=>{const{file:i,fileList:o}=t;d(i,o),i.status==="done"&&x(i.response,i,o)},onPreview:F};return l.limit!=null&&r.attrs.maxCount==null&&(e.maxCount=l.limit),e}function ne(){return{action:"",listType:l.listType,limit:l.limit,beforeUpload:L,httpRequest:U,onExceed:e=>{r.emit("exceed",e,n)},onRemove:(e,t)=>{d(e,t),r.emit("remove",e,t)},onChange:(e,t)=>{d(e,t)},onSuccess:(e,t,i)=>{e!=null&&(t.response=e,t.fsRes=e,x(e,t,i))},onPreview:F}}const R={};function ue(){function e(t){let i=t.value||t;i=ye(i);for(let o of i){const a=R[o.id];a&&C(o,a)}return i}return{action:"",limit:l.limit,listType:l.listType,onBeforeUpload:async({file:t,fileList:i})=>L(t,i),customRequest:t=>{const i=t.file;U({...t,file:i.file,onSuccess:async o=>{const a=l.valueType==="object"?o:o[l.valueType];o.url=await l.buildUrl(a),C(i,o),R[i.id]={...o,fsRes:o},t.onFinish(i)},onProgress:o=>{t.onProgress(o)}})},onExceed:t=>{r.emit("exceed",t)},onRemove:t=>{r.emit("remove",t)},onChange:t=>{const{event:i,file:o,fileList:a}=t,f=e(a);d(o,[...f])},onFinish:t=>{const i=R[t.id];i&&C(t,i);const o=e(n);x(i,t,o)},onPreview:F}}const ce=v(()=>{let e=null;return s.type==="antdv"?e=re():s.type==="element"?e=ne():e=ue(),{...e,...r.attrs}});return{ui:s,fileList:n,fileUploaderRef:p,initValue:b,onChange:V,onInput:k,hasUploading:X,isPicture:T,computedFileSelectBtn:le,previewVisible:q,previewImage:A,computedPreview:oe,computedOnLimit:E,computedBinding:ce,emitValue:j}}}),Ce=["src"];function Pe(l,r,s,c,n,g){return w(),ge("div",{class:Ue(["fs-file-uploader",{"fs-file-uploader-limit":l.computedOnLimit()}])},[(w(),P(z(l.ui.upload.name),H({ref:"fileUploaderRef","file-list":l.fileList,"onUpdate:fileList":r[0]||(r[0]=p=>l.fileList=p)},l.computedBinding),{default:G(()=>[(w(),P(z(l.computedFileSelectBtn.is),be(xe(l.computedFileSelectBtn)),null,16))]),_:1},16,["file-list"])),l.isPicture()?(w(),P(z(l.ui.dialog.name),H({key:0,[l.ui.dialog.visible]:l.previewVisible,["onUpdate:"+l.ui.dialog.visible]:r[1]||(r[1]=p=>l.previewVisible=p)},l.computedPreview),{default:G(()=>[Le("img",{style:{"max-width":"100%","max-height":"100%"},src:l.previewImage},null,8,Ce)]),_:1},16)):he("",!0)],2)}const Ie=pe(Re,[["render",Pe]]);export{Ie as default};
