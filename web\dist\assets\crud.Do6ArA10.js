import{r,v as f,A as l,s as w}from"./index.BHZI5pdK.js";import{d as y}from"./dictionary.DNsEqk19.js";import{a as n}from"./authFunction.D3Be3hRy.js";import{t as b}from"./index.tdFpgju_.js";import{J as g}from"./vue.BNx9QYep.js";const o="/api/system/area/";function x(t){return r({url:o,method:"get",params:t})}function v(t){return r({url:o,method:"post",data:t})}function d(t){return r({url:o+t.id+"/",method:"put",data:t})}function C(t){return r({url:o+t+"/",method:"delete",data:{id:t}})}function j(){return r({url:o+"field_permission/",method:"get"})}const _=function({crudExpose:t}){const i=async e=>await x(e),u=async({form:e,row:a})=>(e.id=a.id,await d(e)),c=async({row:e})=>await C(e.id),h=async({form:e})=>await v(e),s=(e,a,m)=>{i({pcode:e.code}).then(p=>{m(p.data)})};return{crudOptions:{request:{pageRequest:i,addRequest:h,editRequest:u,delRequest:c},actionbar:{buttons:{add:{show:n("area:Create")}}},rowHandle:{fixed:"right",width:200,buttons:{view:{show:!1},edit:{iconRight:"Edit",type:"text",show:n("area:Update")},remove:{iconRight:"Delete",type:"text",show:n("area:Delete")}}},pagination:{show:!1},table:{rowKey:"id",lazy:!0,load:s,treeProps:{children:"children",hasChildren:"hasChild"}},columns:{_index:{title:"序号",form:{show:!1},column:{type:"index",align:"center",width:"70px",columnSetDisabled:!0}},name:{title:"名称",search:{show:!0},treeNode:!0,type:"input",column:{minWidth:120},form:{rules:[{required:!0,message:"名称必填项"}],component:{placeholder:"请输入名称"}}},pcode:{title:"父级地区",search:{disabled:!0},width:130,type:"table-selector",form:{component:{name:g(b),vModel:"modelValue",displayLabel:l(({row:e})=>e?e.pcode_info:null),tableConfig:{url:"/api/system/area/",label:"name",value:"id",isTree:!0,isMultiple:!1,lazy:!0,load:s,treeProps:{children:"children",hasChildren:"hasChild"},columns:[{prop:"name",label:"地区",width:150},{prop:"code",label:"地区编码"}]}}},column:{show:!1}},code:{title:"地区编码",search:{show:!0},type:"input",column:{minWidth:90},form:{rules:[{required:!0,message:"地区编码必填项"}],component:{placeholder:"请输入地区编码"}}},enable:{title:"是否启用",search:{show:!0},type:"dict-radio",column:{minWidth:90,component:{name:"fs-dict-switch",activeText:"",inactiveText:"",style:"--el-switch-on-color: var(--el-color-primary); --el-switch-off-color: #dcdfe6",onChange:l(e=>()=>{d(e.row).then(a=>{w(a.msg)})})}},dict:f({data:y("button_status_bool")})}}}}},D=Object.freeze(Object.defineProperty({__proto__:null,createCrudOptions:_},Symbol.toStringTag,{value:"Module"}));export{j as G,D as a,_ as c};
