import{d as f,g,a0 as b,k as t,b as V,o as $,w as l,l as e,e as m,q as h,s as r}from"./vue.BNx9QYep.js";import{_ as y}from"./_plugin-vue_export-helper.DlAUqK2U.js";const F={username:"",code:""},v=f({name:"loginMobile",setup(){const o=g({ruleForm:F});return{...b(o)}}}),x={class:"font12 mt30 login-animation4 login-msg"};function T(o,n,k,B,C,N){const c=t("el-input"),a=t("el-form-item"),d=t("ele-Position"),u=t("el-icon"),s=t("el-col"),p=t("el-button"),_=t("el-form");return $(),V(_,{size:"large",class:"login-content-form"},{default:l(()=>[e(a,{class:"login-animation1"},{default:l(()=>[e(c,{type:"text",placeholder:o.$t("message.mobile.placeholder1"),modelValue:o.ruleForm.username,"onUpdate:modelValue":n[0]||(n[0]=i=>o.ruleForm.username=i),clearable:"",autocomplete:"off"},{prefix:l(()=>n[2]||(n[2]=[m("i",{class:"iconfont icon-dianhua el-input__icon"},null,-1)])),_:1},8,["placeholder","modelValue"])]),_:1}),e(a,{class:"login-animation2"},{default:l(()=>[e(s,{span:15},{default:l(()=>[e(c,{type:"text",maxlength:"4",placeholder:o.$t("message.mobile.placeholder2"),modelValue:o.ruleForm.code,"onUpdate:modelValue":n[1]||(n[1]=i=>o.ruleForm.code=i),clearable:"",autocomplete:"off"},{prefix:l(()=>[e(u,{class:"el-input__icon"},{default:l(()=>[e(d)]),_:1})]),_:1},8,["placeholder","modelValue"])]),_:1}),e(s,{span:1}),e(s,{span:8},{default:l(()=>[e(p,{class:"login-content-code"},{default:l(()=>[h(r(o.$t("message.mobile.codeText")),1)]),_:1})]),_:1})]),_:1}),e(a,{class:"login-animation3"},{default:l(()=>[e(p,{round:"",type:"primary",class:"login-content-submit"},{default:l(()=>[m("span",null,r(o.$t("message.mobile.btnText")),1)]),_:1})]),_:1}),m("div",x,r(o.$t("message.mobile.msgText")),1)]),_:1})}const U=y(v,[["render",T],["__scopeId","data-v-d22bc9b1"]]);export{U as default};
