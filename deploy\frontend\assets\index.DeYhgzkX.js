const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.DRKV2SoC.js","assets/index.BHZI5pdK.js","assets/vue.BNx9QYep.js","assets/index.Dg-OhEXY.css","assets/_plugin-vue_export-helper.DlAUqK2U.js","assets/index.fr345Oul.css","assets/tagsView.B9jqfD_7.js","assets/tagsView.B_TLRSQC.css"])))=>i.map(i=>d[i]);
import{u as d,_ as t}from"./index.BHZI5pdK.js";import{d as r,M as l,c as f,a as p,o as s,l as v,b as T,m as g,u as a,P as n}from"./vue.BNx9QYep.js";import{_ as h}from"./_plugin-vue_export-helper.DlAUqK2U.js";const C={class:"layout-navbars-container"},V=r({name:"layoutNavBars"}),x=r({...V,setup(y){const c=n(()=>t(()=>import("./index.DRKV2SoC.js"),__vite__mapDeps([0,1,2,3,4,5]))),_=n(()=>t(()=>import("./tagsView.B9jqfD_7.js"),__vite__mapDeps([6,1,2,3,4,7]))),i=d(),{themeConfig:m}=l(i),u=f(()=>{let{layout:e,isTagsview:o}=m.value;return e!=="classic"&&o});return(e,o)=>(s(),p("div",C,[v(a(c)),u.value?(s(),T(a(_),{key:0})):g("",!0)]))}}),w=h(x,[["__scopeId","data-v-1f92533c"]]);export{w as default};
