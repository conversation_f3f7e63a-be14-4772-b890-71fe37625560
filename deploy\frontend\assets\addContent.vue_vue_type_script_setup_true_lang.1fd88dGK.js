import{G as B,A as N}from"./api.FR9XnnSw.js";import{_ as $}from"./associationTable.vue_vue_type_script_setup_true_lang.sUPUexxV.js";import{s as F}from"./index.BHZI5pdK.js";import{d as G}from"./dictionary.DNsEqk19.js";import{d as M,g as U,h as V,i as P,j as S,k as m,a as f,o as u,l as o,w as r,b as y,m as x,u as t,F as c,p as g,q as z}from"./vue.BNx9QYep.js";const Z={style:{padding:"20px"}},D={key:1},W=M({__name:"addContent",setup(E){let a=U({parent:null,title:null,key:null,form_item_type:"",rule:null,placeholder:null});const k=V(),_=V(),q=U({parent:[{required:!0,message:"请选择"}],title:[{required:!0,message:"请输入"}],key:[{required:!0,message:"请输入"},{pattern:/^[A-Za-z0-9_]+$/,message:"请输入数字、字母或下划线"}],form_item_type:[{required:!0,message:"请输入"}]});let v=V([]),w=V([{label:"必填项",value:'{"required": true, "message": "必填项不能为空"}'},{label:"邮箱",value:'{ "type": "email", "message": "请输入正确的邮箱地址"}'},{label:"URL地址",value:'{ "type": "url", "message": "请输入正确的URL地址"}'}]);const C=()=>{B({parent__isnull:!0,limit:999}).then(s=>{v.value=s.data})},O=P("refreshView"),R=async s=>{s&&await s.validate((l,d)=>{l?N(a).then(i=>{i.code==2e3&&(F("新增成功"),O())}):console.log("error submit!",d)})},j=()=>new Promise(function(s,l){if(_){if(!_.onSubmit())return l(!1);const{formObj:d}=_;return a.setting=d,s(!0)}else return s(!0)});return S(()=>{C()}),(s,l)=>{const d=m("el-option"),i=m("el-select"),n=m("el-form-item"),b=m("el-input"),L=m("el-input-number"),T=m("el-button"),A=m("el-form");return u(),f("div",Z,[o(A,{ref_key:"formRef",ref:k,model:t(a),rules:q,"label-width":"80px"},{default:r(()=>[o(n,{label:"所属分组",prop:"parent"},{default:r(()=>[o(i,{modelValue:t(a).parent,"onUpdate:modelValue":l[0]||(l[0]=e=>t(a).parent=e),placeholder:"请选择分组",clearable:""},{default:r(()=>[(u(!0),f(c,null,g(t(v),(e,p)=>(u(),y(d,{label:e.title,value:e.id,key:p},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(n,{label:"标题",prop:"title"},{default:r(()=>[o(b,{modelValue:t(a).title,"onUpdate:modelValue":l[1]||(l[1]=e=>t(a).title=e),placeholder:"请输入",clearable:""},null,8,["modelValue"])]),_:1}),o(n,{label:"key值",prop:"key"},{default:r(()=>[o(b,{modelValue:t(a).key,"onUpdate:modelValue":l[2]||(l[2]=e=>t(a).key=e),placeholder:"请输入",clearable:""},null,8,["modelValue"])]),_:1}),o(n,{label:"表单类型",prop:"form_item_type"},{default:r(()=>[o(i,{modelValue:t(a).form_item_type,"onUpdate:modelValue":l[3]||(l[3]=e=>t(a).form_item_type=e),placeholder:"请选择",clearable:""},{default:r(()=>[(u(!0),f(c,null,g(t(G)("config_form_type"),(e,p)=>(u(),y(d,{label:e.label,value:e.value,key:p},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),[4,5,6].indexOf(t(a).form_item_type)>-1?(u(),y(n,{key:0,label:"字典key",prop:"setting",rules:[{required:!0,message:"不能为空"}]},{default:r(()=>[o(b,{modelValue:t(a).setting,"onUpdate:modelValue":l[4]||(l[4]=e=>t(a).setting=e),placeholder:"请输入dictionary中key值",clearable:""},null,8,["modelValue"])]),_:1})):x("",!0),[13,14].indexOf(t(a).form_item_type)>-1?(u(),f("div",D,[o($,{ref_key:"associationTableRef",ref:_,modelValue:t(a).setting,"onUpdate:modelValue":l[5]||(l[5]=e=>t(a).setting=e),onUpdateVal:j},null,8,["modelValue"])])):x("",!0),o(n,{label:"校验规则"},{default:r(()=>[o(i,{modelValue:t(a).rule,"onUpdate:modelValue":l[6]||(l[6]=e=>t(a).rule=e),multiple:"",placeholder:"请选择(可多选)",clearable:""},{default:r(()=>[(u(!0),f(c,null,g(t(w),(e,p)=>(u(),y(d,{label:e.label,value:e.value,key:p},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(n,{label:"提示信息",prop:"placeholder"},{default:r(()=>[o(b,{modelValue:t(a).placeholder,"onUpdate:modelValue":l[7]||(l[7]=e=>t(a).placeholder=e),placeholder:"请输入",clearable:""},null,8,["modelValue"])]),_:1}),o(n,{label:"排序",prop:"sort"},{default:r(()=>[o(L,{modelValue:t(a).sort,"onUpdate:modelValue":l[8]||(l[8]=e=>t(a).sort=e),min:0,max:99},null,8,["modelValue"])]),_:1}),o(n,null,{default:r(()=>[o(T,{type:"primary",onClick:l[9]||(l[9]=e=>R(k.value))},{default:r(()=>l[10]||(l[10]=[z("立即创建")])),_:1,__:[10]})]),_:1})]),_:1},8,["model","rules"])])}}});export{W as _};
