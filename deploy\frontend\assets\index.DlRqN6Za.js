import{a as N,c as T,X as f,J as U}from"./index.BHZI5pdK.js";import{a as q,c as E}from"./crud.DMN86VyI.js";import{d as X,h as z,c as J,k as s,b as O,o as P,w as e,e as h,l as t,x as $,u as m,q as _,s as j}from"./vue.BNx9QYep.js";const A={style:{height:"500px"}},L=X({__name:"index",props:{refreshCallback:{type:Function,required:!0}},setup(b,{expose:k}){const x=b,r=z(!1),i=x.refreshCallback,d=()=>{r.value=!1,a.value=[]},C=async()=>{a.value.length!==0&&(await q(g.value.getSearchFormData().role_id,f.pluck(a.value,"id")).then(l=>{U(l.msg)}),i&&i(),d())},{crudBinding:y,crudRef:g,crudExpose:u,selectedRows:a}=N({createCrudOptions:E,context:{}}),{setSearchFormData:R,doRefresh:w}=u,v=J(()=>a.value.length),D=l=>{const o=u.getBaseTableRef(),n=u.getTableData();f.pluck(n,"id").includes(l.id)?o.toggleRowSelection(l,!1):a.value=f.remove(a.value,c=>c.id!==l.id)};return k({dialog:r,setSearchFormData:R,doRefresh:w,parentRefreshCallbackFunc:i}),(l,o)=>{const n=s("el-button"),c=s("el-table-column"),V=s("el-table"),B=s("el-popover"),S=s("fs-crud"),F=s("el-dialog");return P(),O(F,{modelValue:r.value,"onUpdate:modelValue":o[0]||(o[0]=p=>r.value=p),title:"添加授权用户",direction:"rtl","destroy-on-close":"","before-close":d},{footer:e(()=>[h("div",null,[t(n,{type:"primary",onClick:C},{default:e(()=>o[1]||(o[1]=[_(" 确定")])),_:1,__:[1]}),t(n,{onClick:d},{default:e(()=>o[2]||(o[2]=[_(" 取消")])),_:1,__:[2]})])]),default:e(()=>[h("div",A,[t(S,$({ref_key:"crudRef",ref:g},m(y)),{"pagination-right":e(()=>[t(B,{placement:"top",width:200,trigger:"click"},{reference:e(()=>[t(n,{text:"",type:v.value>0?"primary":""},{default:e(()=>[_("已选中"+j(v.value)+"条数据",1)]),_:1},8,["type"])]),default:e(()=>[t(V,{data:m(a),size:"small","max-height":500},{default:e(()=>[t(c,{width:"100",property:"name",label:"用户名"}),t(c,{fixed:"right",label:"操作","min-width":"50"},{default:e(p=>[t(n,{text:"",type:"info",icon:m(T),onClick:G=>D(p.row),circle:""},null,8,["icon","onClick"])]),_:1})]),_:1},8,["data"])]),_:1})]),_:1},16)])]),_:1},8,["modelValue"])}}});export{L as default};
