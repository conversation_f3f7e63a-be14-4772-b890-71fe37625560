import{d as a,h as f,j as _,k as s,y as h,b as y,o as g,w as r,l as x,x as v,u as w,A as E,e as z,D as B}from"./vue.BNx9QYep.js";import{a as C}from"./index.BHZI5pdK.js";import{createCrudOptions as b}from"./crud.C4abRoLA.js";import{e as k}from"./echarts.BiCAFTQd.js";import"./commonCrud.Cpd55lBt.js";import"./_plugin-vue_export-helper.DlAUqK2U.js";const R={id:"myEcharts",style:{width:"100%",height:"300px"}},A=a({name:"loginLog"}),M=a({...A,setup(D){const t=f(!0),{crudBinding:i,crudRef:n,crudExpose:c}=C({createCrudOptions:b,isEcharts:t,initChart:o}),p=k;function o(){let e=p.init(document.getElementById("myEcharts"),"purple-passion");e.setOption({title:{text:"2021年各月份销售量（单位：件）",left:"center"},xAxis:{type:"category",data:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"]},tooltip:{trigger:"axis"},yAxis:{type:"value"},series:[{data:[606,542,985,687,501,787,339,706,383,684,669,737],type:"line",smooth:!0,itemStyle:{normal:{label:{show:!0,position:"top",formatter:"{c}"}}}}]}),window.onresize=function(){e.resize()}}function l(e){console.log(e)}return _(()=>{c.doRefresh(),o()}),(e,N)=>{const d=s("fs-crud"),u=s("fs-page"),m=h("resize-ob");return g(),y(u,null,{default:r(()=>[x(d,v({ref_key:"crudRef",ref:n},w(i)),{"header-top":r(()=>[E(z("div",R,null,512),[[B,t.value],[m,l]])]),_:1},16)]),_:1})}}});export{M as default};
