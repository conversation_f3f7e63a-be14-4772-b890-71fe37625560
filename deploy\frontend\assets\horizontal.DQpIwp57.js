const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/subItem.BsoOjfGx.js","assets/vue.BNx9QYep.js","assets/index.BHZI5pdK.js","assets/index.Dg-OhEXY.css"])))=>i.map(i=>d[i]);
import{R as N,u as O,_ as j,V as q,l as Q}from"./index.BHZI5pdK.js";import{d as $,R as W,g as Y,h as I,M as R,Q as Z,c as G,N as J,j as K,k as u,a as d,o as a,l as r,w as c,F as S,p as U,b as v,u as X,P as ee,e as M,s as f,Y as te,q as T,Z as ne,I as se}from"./vue.BNx9QYep.js";import{_ as ie}from"./_plugin-vue_export-helper.DlAUqK2U.js";const oe={class:"el-menu-horizontal-warp"},le=["onClick"],ae=$({name:"navMenuHorizontal"}),re=$({...ae,props:{menuList:{type:Array,default:()=>[]}},setup(b){const h=W(),w=ee(()=>j(()=>import("./subItem.BsoOjfGx.js"),__vite__mapDeps([0,1,2,3])));Y({menuList:[],clientWidth:0});const m=b,z=I(),A=N(),B=O(),{routesList:p}=R(A),{themeConfig:_}=R(B),k=Z(),o=I(""),C=G(()=>(m.menuList.shift(),m.menuList)),g=(t,i)=>{for(let e=0;e<t.length;e++){const n=t[e];if(n.children&&n.children.length>0&&(n.children.findIndex(s=>s.path===i)!==-1||g(n.children,i)!==null))return e}return null},D=()=>{se(()=>{let t=document.querySelector(".el-menu.el-menu--horizontal li.is-active");if(!t)return!1;z.value.$refs.wrapRef.scrollLeft=t.offsetLeft})},L=t=>t.filter(i=>{var e;return!((e=i.meta)!=null&&e.isHide)}).map(i=>(i=Object.assign({},i),i.children&&(i.children=L(i.children)),i)),H=t=>{const i=t.split("/");let e={children:[]};return L(p.value).map((n,l)=>{n.path===`/${i[1]}`&&(n.k=l,e.item={...n},e.children=[{...n}],n.children&&(e.children=n.children))}),e},P=t=>{const{path:i,meta:e}=t;if(_.value.layout==="classic"){let n=(g(p.value,k.path)||0)-1;o.value=n<0?o.value:C.value[n].path}else{const n=e!=null&&e.isDynamic?e.isDynamicPath.split("/"):i.split("/");n.length>=4&&(e!=null&&e.isHide)?o.value=n.splice(0,3).join("/"):o.value=i}},V=t=>{q.handleOpenLink(t)},x=(t,i)=>{let e=t.children;if(e===void 0&&(o.value=t.path,e=H(t.path).children),e.length>=1){if(e[0].is_catalog){x(e[0]);return}h.push(e[0].path);let{layout:n,isClassicSplitMenu:l}=_.value;n==="classic"&&l&&Q.emit("setSendClassicChildren",e[0])}else h.push("/home")};return J(()=>{P(k)}),K(()=>{D()}),(t,i)=>{const e=u("SvgIcon"),n=u("el-sub-menu"),l=u("el-menu-item"),y=u("el-menu");return a(),d("div",oe,[r(y,{"default-active":o.value,"background-color":"transparent",mode:"horizontal"},{default:c(()=>[(a(!0),d(S,null,U(C.value,(s,E)=>(a(),d(S,null,[s.children&&s.children.length>0?(a(),v(n,{index:s.path,key:s.path},{title:c(()=>[r(e,{name:s.meta.icon},null,8,["name"]),M("span",null,f(t.$t(s.meta.title)),1)]),default:c(()=>[r(X(w),{chil:s.children},null,8,["chil"])]),_:2},1032,["index"])):(a(),v(l,{index:s.path,key:s.path,style:{"--el-menu-active-color":"#fff"},onClick:F=>x(s,E)},te({_:2},[!s.meta.isLink||s.meta.isLink&&s.meta.isIframe?{name:"title",fn:c(()=>[r(e,{name:s.meta.icon},null,8,["name"]),T(" "+f(t.$t(s.meta.title)),1)]),key:"0"}:{name:"title",fn:c(()=>[M("a",{class:"w100",onClick:ne(F=>V(s),["prevent"])},[r(e,{name:s.meta.icon},null,8,["name"]),T(" "+f(t.$t(s.meta.title)),1)],8,le)]),key:"1"}]),1032,["index","onClick"]))],64))),256))]),_:1},8,["default-active"])])}}}),fe=ie(re,[["__scopeId","data-v-57fff618"]]);export{fe as default};
