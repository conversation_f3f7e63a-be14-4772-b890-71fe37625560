import{m as l}from"./editor.api.AfgADwdP.js";/*!-----------------------------------------------------------------------------
* Copyright (c) Microsoft Corporation. All rights reserved.
* Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)
* Released under the MIT license
* https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
*-----------------------------------------------------------------------------*/var c=Object.defineProperty,_=Object.getOwnPropertyDescriptor,d=Object.getOwnPropertyNames,u=Object.prototype.hasOwnProperty,y=(a,e,r,o)=>{if(e&&typeof e=="object"||typeof e=="function")for(let t of d(e))!u.call(a,t)&&t!==r&&c(a,t,{get:()=>e[t],enumerable:!(o=_(e,t))||o.enumerable});return a},p=(a,e,r)=>(y(a,e,"default"),r),n={};p(n,l);var i={},s={},L=class g{static getOrCreate(e){return s[e]||(s[e]=new g(e)),s[e]}constructor(e){this._languageId=e,this._loadingTriggered=!1,this._lazyLoadPromise=new Promise((r,o)=>{this._lazyLoadPromiseResolve=r,this._lazyLoadPromiseReject=o})}load(){return this._loadingTriggered||(this._loadingTriggered=!0,i[this._languageId].loader().then(e=>this._lazyLoadPromiseResolve(e),e=>this._lazyLoadPromiseReject(e))),this._lazyLoadPromise}};function h(a){const e=a.id;i[e]=a,n.languages.register(a);const r=L.getOrCreate(e);n.languages.registerTokensProviderFactory(e,{create:async()=>(await r.load()).language}),n.languages.onLanguageEncountered(e,async()=>{const o=await r.load();n.languages.setLanguageConfiguration(e,o.conf)})}export{h as r};
