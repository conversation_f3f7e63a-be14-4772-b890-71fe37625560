import{l as U,U as q,A as L}from"./api.B8LmnnXE.js";import{a5 as j,J as B}from"./index.BHZI5pdK.js";import{d as N,h as c,g as V,j as T,k as u,b as z,o as A,u as t,w as i,l as o,q as b,s as M}from"./vue.BNx9QYep.js";import{_ as O}from"./_plugin-vue_export-helper.DlAUqK2U.js";const R=N({__name:"index",props:{initFormData:{default:()=>null},treeData:{default:()=>[]},cacheData:{default:()=>[]}},emits:["drawerClose"],setup(w,{emit:g}){const k={children:"children",label:"name",value:"id",isLeaf:(l,e)=>!(e!=null&&e.data.hasChild)},p=c(),F=V({name:[{required:!0,message:"部门名称必填",trigger:"blur"}],key:[{required:!0,message:"部门标识必填",trigger:"blur"}]}),n=w,h=g;let _=c([]),a=V({key:"",parent:"",name:"",owner:"",description:""}),f=c(!1);const v=()=>{var l,e;(l=n.initFormData)!=null&&l.id&&(a.id=(e=n.initFormData)==null?void 0:e.id,a.key=n.initFormData.key||"",a.parent=n.initFormData.parent||"",a.name=n.initFormData.name||"",a.owner=n.initFormData.owner||"",a.description=n.initFormData.description||"")},C=(l,e)=>{l.level!==0&&U({parent:l.data.id}).then(r=>{e(r.data)})},x=()=>{var l;(l=p.value)==null||l.validate(async e=>{if(e)try{let r;f.value=!0,a.id?r=await q(a):r=await L(a),(r==null?void 0:r.code)===2e3&&(B(r.msg),y("submit"))}finally{f.value=!1}})},y=(l="")=>{var e;h("drawerClose",l),(e=p.value)==null||e.resetFields()};return T(async()=>{n.treeData.map(l=>{_.value.push(l)}),v()}),(l,e)=>{const r=u("el-tree-select"),s=u("el-form-item"),m=u("el-input"),D=u("el-button");return A(),z(t(j),{ref_key:"formRef",ref:p,rules:F,model:t(a),"label-width":"100px","label-position":"right",class:"dept-form-com"},{default:i(()=>[o(s,{label:"父级部门",prop:"parent"},{default:i(()=>[o(r,{modelValue:t(a).parent,"onUpdate:modelValue":e[0]||(e[0]=d=>t(a).parent=d),props:k,data:t(_),"cache-data":n.cacheData,lazy:"","check-strictly":"",load:C,style:{width:"100%"}},null,8,["modelValue","data","cache-data"])]),_:1}),o(s,{required:"",label:"部门名称",prop:"name"},{default:i(()=>[o(m,{modelValue:t(a).name,"onUpdate:modelValue":e[1]||(e[1]=d=>t(a).name=d)},null,8,["modelValue"])]),_:1}),o(s,{required:"",label:"部门标识",prop:"key"},{default:i(()=>[o(m,{modelValue:t(a).key,"onUpdate:modelValue":e[2]||(e[2]=d=>t(a).key=d)},null,8,["modelValue"])]),_:1}),o(s,{label:"负责人"},{default:i(()=>[o(m,{modelValue:t(a).owner,"onUpdate:modelValue":e[3]||(e[3]=d=>t(a).owner=d),placeholder:"请输入"},null,8,["modelValue"])]),_:1}),o(s,{label:"备注"},{default:i(()=>[o(m,{modelValue:t(a).description,"onUpdate:modelValue":e[4]||(e[4]=d=>t(a).description=d),maxlength:"200","show-word-limit":"",type:"textarea"},null,8,["modelValue"])]),_:1}),o(s,null,{default:i(()=>[o(D,{onClick:x,type:"primary",loading:t(f)},{default:i(()=>[b(M(t(a).id?"保存":"新增"),1)]),_:1},8,["loading"]),o(D,{onClick:y},{default:i(()=>e[5]||(e[5]=[b("取消 ")])),_:1,__:[5]})]),_:1})]),_:1},8,["rules","model"])}}}),S=O(R,[["__scopeId","data-v-94e5ddac"]]);export{S as default};
