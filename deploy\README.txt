华绿系统 - 生产环境部署包
==================================

构建时间: 2025-08-07 11:03

目录结构:
  deploy/
  ├── backend/          # Django后端代码和静态文件
  ├── frontend/         # Vue3前端构建文件
  ├── README.txt        # 本说明文件
  ├── start_server.bat  # Windows启动脚本
  └── nginx.conf        # Nginx配置示例

部署步骤:

1. 环境准备
   - Python 3.8+ 
   - pip包管理器
   - 数据库 (SQL Server/MySQL/PostgreSQL)
   - Web服务器 (Nginx推荐)

2. 后端部署
   a) 上传backend文件夹到服务器
   b) 安装Python依赖:
      cd backend
      pip install -r requirements.txt
   c) 配置数据库连接:
      编辑 application/settings.py 中的 DATABASES 配置
   d) 执行数据库迁移:
      python manage.py migrate
   e) 创建超级用户:
      python manage.py createsuperuser

3. 前端部署
   将frontend文件夹中的所有文件部署到Web服务器根目录

4. Web服务器配置
   参考nginx.conf配置文件，配置反向代理

5. 启动服务
   Windows: 双击 start_server.bat
   Linux: python manage.py runserver 0.0.0.0:8000

重要配置说明:

1. 数据库配置 (backend/application/settings.py):
   - 修改DATABASES配置
   - 设置正确的数据库连接信息

2. 生产环境设置:
   - DEBUG = False
   - ALLOWED_HOSTS = ['你的域名', '服务器IP']
   - 配置STATIC_ROOT和MEDIA_ROOT

3. 前端API地址:
   前端已构建为生产版本，API地址指向 /api/

默认管理员账号:
  用户名: superadmin
  密码: admin123

注意事项:
1. 首次部署后请立即修改管理员密码
2. 确保服务器防火墙开放相应端口
3. 建议使用HTTPS协议
4. 定期备份数据库和文件

技术支持:
如有问题请查看日志文件或联系技术支持
