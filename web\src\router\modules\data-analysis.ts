import { RouteRecordRaw } from 'vue-router';

/**
 * 数据分析相关路由
 */
const dataAnalysisRoutes: Array<RouteRecordRaw> = [
  {
    path: '/data-analysis',
    name: 'dataAnalysis',
    component: () => import('/@/views/data-analysis/index.vue'),
    meta: {
      title: '数据分析',
      isLink: '',
      isHide: false,
      isKeepAlive: true,
      isAffix: false,
      isIframe: false,
      icon: 'iconfont icon-shuju',
      roles: ['admin', 'common'],
    },
  },
];

export default dataAnalysisRoutes;
