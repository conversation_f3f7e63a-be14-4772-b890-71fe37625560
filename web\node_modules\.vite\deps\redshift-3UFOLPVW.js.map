{"version": 3, "sources": ["../../.pnpm/monaco-editor@0.52.2/node_modules/monaco-editor/esm/vs/basic-languages/redshift/redshift.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/redshift/redshift.ts\nvar conf = {\n  comments: {\n    lineComment: \"--\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".sql\",\n  ignoreCase: true,\n  brackets: [\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  keywords: [\n    \"AES128\",\n    \"AES256\",\n    \"ALL\",\n    \"ALLOWOVERWRITE\",\n    \"ANALYSE\",\n    \"ANALYZE\",\n    \"AND\",\n    \"ANY\",\n    \"ARRAY\",\n    \"AS\",\n    \"ASC\",\n    \"AUTHORIZATION\",\n    \"AZ64\",\n    \"BACKUP\",\n    \"BETWEEN\",\n    \"BINARY\",\n    \"BLANKSASNULL\",\n    \"BOTH\",\n    \"BYTEDICT\",\n    \"BZIP2\",\n    \"CASE\",\n    \"CAST\",\n    \"CHECK\",\n    \"COLLATE\",\n    \"COLUMN\",\n    \"CONSTRAINT\",\n    \"CREATE\",\n    \"CREDENTIALS\",\n    \"CROSS\",\n    \"CURRENT_DATE\",\n    \"CURRENT_TIME\",\n    \"CURRENT_TIMESTAMP\",\n    \"CURRENT_USER\",\n    \"CURRENT_USER_ID\",\n    \"DEFAULT\",\n    \"DEFERRABLE\",\n    \"DEFLATE\",\n    \"DEFRAG\",\n    \"DELTA\",\n    \"DELTA32K\",\n    \"DESC\",\n    \"DISABLE\",\n    \"DISTINCT\",\n    \"DO\",\n    \"ELSE\",\n    \"EMPTYASNULL\",\n    \"ENABLE\",\n    \"ENCODE\",\n    \"ENCRYPT\",\n    \"ENCRYPTION\",\n    \"END\",\n    \"EXCEPT\",\n    \"EXPLICIT\",\n    \"FALSE\",\n    \"FOR\",\n    \"FOREIGN\",\n    \"FREEZE\",\n    \"FROM\",\n    \"FULL\",\n    \"GLOBALDICT256\",\n    \"GLOBALDICT64K\",\n    \"GRANT\",\n    \"GROUP\",\n    \"GZIP\",\n    \"HAVING\",\n    \"IDENTITY\",\n    \"IGNORE\",\n    \"ILIKE\",\n    \"IN\",\n    \"INITIALLY\",\n    \"INNER\",\n    \"INTERSECT\",\n    \"INTO\",\n    \"IS\",\n    \"ISNULL\",\n    \"JOIN\",\n    \"LANGUAGE\",\n    \"LEADING\",\n    \"LEFT\",\n    \"LIKE\",\n    \"LIMIT\",\n    \"LOCALTIME\",\n    \"LOCALTIMESTAMP\",\n    \"LUN\",\n    \"LUNS\",\n    \"LZO\",\n    \"LZOP\",\n    \"MINUS\",\n    \"MOSTLY16\",\n    \"MOSTLY32\",\n    \"MOSTLY8\",\n    \"NATURAL\",\n    \"NEW\",\n    \"NOT\",\n    \"NOTNULL\",\n    \"NULL\",\n    \"NULLS\",\n    \"OFF\",\n    \"OFFLINE\",\n    \"OFFSET\",\n    \"OID\",\n    \"OLD\",\n    \"ON\",\n    \"ONLY\",\n    \"OPEN\",\n    \"OR\",\n    \"ORDER\",\n    \"OUTER\",\n    \"OVERLAPS\",\n    \"PARALLEL\",\n    \"PARTITION\",\n    \"PERCENT\",\n    \"PERMISSIONS\",\n    \"PLACING\",\n    \"PRIMARY\",\n    \"RAW\",\n    \"READRATIO\",\n    \"RECOVER\",\n    \"REFERENCES\",\n    \"RESPECT\",\n    \"REJECTLOG\",\n    \"RESORT\",\n    \"RESTORE\",\n    \"RIGHT\",\n    \"SELECT\",\n    \"SESSION_USER\",\n    \"SIMILAR\",\n    \"SNAPSHOT\",\n    \"SOME\",\n    \"SYSDATE\",\n    \"SYSTEM\",\n    \"TABLE\",\n    \"TAG\",\n    \"TDES\",\n    \"TEXT255\",\n    \"TEXT32K\",\n    \"THEN\",\n    \"TIMESTAMP\",\n    \"TO\",\n    \"TOP\",\n    \"TRAILING\",\n    \"TRUE\",\n    \"TRUNCATECOLUMNS\",\n    \"UNION\",\n    \"UNIQUE\",\n    \"USER\",\n    \"USING\",\n    \"VERBOSE\",\n    \"WALLET\",\n    \"WHEN\",\n    \"WHERE\",\n    \"WITH\",\n    \"WITHOUT\"\n  ],\n  operators: [\n    \"AND\",\n    \"BETWEEN\",\n    \"IN\",\n    \"LIKE\",\n    \"NOT\",\n    \"OR\",\n    \"IS\",\n    \"NULL\",\n    \"INTERSECT\",\n    \"UNION\",\n    \"INNER\",\n    \"JOIN\",\n    \"LEFT\",\n    \"OUTER\",\n    \"RIGHT\"\n  ],\n  builtinFunctions: [\n    \"current_schema\",\n    \"current_schemas\",\n    \"has_database_privilege\",\n    \"has_schema_privilege\",\n    \"has_table_privilege\",\n    \"age\",\n    \"current_time\",\n    \"current_timestamp\",\n    \"localtime\",\n    \"isfinite\",\n    \"now\",\n    \"ascii\",\n    \"get_bit\",\n    \"get_byte\",\n    \"set_bit\",\n    \"set_byte\",\n    \"to_ascii\",\n    \"approximate percentile_disc\",\n    \"avg\",\n    \"count\",\n    \"listagg\",\n    \"max\",\n    \"median\",\n    \"min\",\n    \"percentile_cont\",\n    \"stddev_samp\",\n    \"stddev_pop\",\n    \"sum\",\n    \"var_samp\",\n    \"var_pop\",\n    \"bit_and\",\n    \"bit_or\",\n    \"bool_and\",\n    \"bool_or\",\n    \"cume_dist\",\n    \"first_value\",\n    \"lag\",\n    \"last_value\",\n    \"lead\",\n    \"nth_value\",\n    \"ratio_to_report\",\n    \"dense_rank\",\n    \"ntile\",\n    \"percent_rank\",\n    \"rank\",\n    \"row_number\",\n    \"case\",\n    \"coalesce\",\n    \"decode\",\n    \"greatest\",\n    \"least\",\n    \"nvl\",\n    \"nvl2\",\n    \"nullif\",\n    \"add_months\",\n    \"at time zone\",\n    \"convert_timezone\",\n    \"current_date\",\n    \"date_cmp\",\n    \"date_cmp_timestamp\",\n    \"date_cmp_timestamptz\",\n    \"date_part_year\",\n    \"dateadd\",\n    \"datediff\",\n    \"date_part\",\n    \"date_trunc\",\n    \"extract\",\n    \"getdate\",\n    \"interval_cmp\",\n    \"last_day\",\n    \"months_between\",\n    \"next_day\",\n    \"sysdate\",\n    \"timeofday\",\n    \"timestamp_cmp\",\n    \"timestamp_cmp_date\",\n    \"timestamp_cmp_timestamptz\",\n    \"timestamptz_cmp\",\n    \"timestamptz_cmp_date\",\n    \"timestamptz_cmp_timestamp\",\n    \"timezone\",\n    \"to_timestamp\",\n    \"trunc\",\n    \"abs\",\n    \"acos\",\n    \"asin\",\n    \"atan\",\n    \"atan2\",\n    \"cbrt\",\n    \"ceil\",\n    \"ceiling\",\n    \"checksum\",\n    \"cos\",\n    \"cot\",\n    \"degrees\",\n    \"dexp\",\n    \"dlog1\",\n    \"dlog10\",\n    \"exp\",\n    \"floor\",\n    \"ln\",\n    \"log\",\n    \"mod\",\n    \"pi\",\n    \"power\",\n    \"radians\",\n    \"random\",\n    \"round\",\n    \"sin\",\n    \"sign\",\n    \"sqrt\",\n    \"tan\",\n    \"to_hex\",\n    \"bpcharcmp\",\n    \"btrim\",\n    \"bttext_pattern_cmp\",\n    \"char_length\",\n    \"character_length\",\n    \"charindex\",\n    \"chr\",\n    \"concat\",\n    \"crc32\",\n    \"func_sha1\",\n    \"initcap\",\n    \"left and rights\",\n    \"len\",\n    \"length\",\n    \"lower\",\n    \"lpad and rpads\",\n    \"ltrim\",\n    \"md5\",\n    \"octet_length\",\n    \"position\",\n    \"quote_ident\",\n    \"quote_literal\",\n    \"regexp_count\",\n    \"regexp_instr\",\n    \"regexp_replace\",\n    \"regexp_substr\",\n    \"repeat\",\n    \"replace\",\n    \"replicate\",\n    \"reverse\",\n    \"rtrim\",\n    \"split_part\",\n    \"strpos\",\n    \"strtol\",\n    \"substring\",\n    \"textlen\",\n    \"translate\",\n    \"trim\",\n    \"upper\",\n    \"cast\",\n    \"convert\",\n    \"to_char\",\n    \"to_date\",\n    \"to_number\",\n    \"json_array_length\",\n    \"json_extract_array_element_text\",\n    \"json_extract_path_text\",\n    \"current_setting\",\n    \"pg_cancel_backend\",\n    \"pg_terminate_backend\",\n    \"set_config\",\n    \"current_database\",\n    \"current_user\",\n    \"current_user_id\",\n    \"pg_backend_pid\",\n    \"pg_last_copy_count\",\n    \"pg_last_copy_id\",\n    \"pg_last_query_id\",\n    \"pg_last_unload_count\",\n    \"session_user\",\n    \"slice_num\",\n    \"user\",\n    \"version\",\n    \"abbrev\",\n    \"acosd\",\n    \"any\",\n    \"area\",\n    \"array_agg\",\n    \"array_append\",\n    \"array_cat\",\n    \"array_dims\",\n    \"array_fill\",\n    \"array_length\",\n    \"array_lower\",\n    \"array_ndims\",\n    \"array_position\",\n    \"array_positions\",\n    \"array_prepend\",\n    \"array_remove\",\n    \"array_replace\",\n    \"array_to_json\",\n    \"array_to_string\",\n    \"array_to_tsvector\",\n    \"array_upper\",\n    \"asind\",\n    \"atan2d\",\n    \"atand\",\n    \"bit\",\n    \"bit_length\",\n    \"bound_box\",\n    \"box\",\n    \"brin_summarize_new_values\",\n    \"broadcast\",\n    \"cardinality\",\n    \"center\",\n    \"circle\",\n    \"clock_timestamp\",\n    \"col_description\",\n    \"concat_ws\",\n    \"convert_from\",\n    \"convert_to\",\n    \"corr\",\n    \"cosd\",\n    \"cotd\",\n    \"covar_pop\",\n    \"covar_samp\",\n    \"current_catalog\",\n    \"current_query\",\n    \"current_role\",\n    \"currval\",\n    \"cursor_to_xml\",\n    \"diameter\",\n    \"div\",\n    \"encode\",\n    \"enum_first\",\n    \"enum_last\",\n    \"enum_range\",\n    \"every\",\n    \"family\",\n    \"format\",\n    \"format_type\",\n    \"generate_series\",\n    \"generate_subscripts\",\n    \"get_current_ts_config\",\n    \"gin_clean_pending_list\",\n    \"grouping\",\n    \"has_any_column_privilege\",\n    \"has_column_privilege\",\n    \"has_foreign_data_wrapper_privilege\",\n    \"has_function_privilege\",\n    \"has_language_privilege\",\n    \"has_sequence_privilege\",\n    \"has_server_privilege\",\n    \"has_tablespace_privilege\",\n    \"has_type_privilege\",\n    \"height\",\n    \"host\",\n    \"hostmask\",\n    \"inet_client_addr\",\n    \"inet_client_port\",\n    \"inet_merge\",\n    \"inet_same_family\",\n    \"inet_server_addr\",\n    \"inet_server_port\",\n    \"isclosed\",\n    \"isempty\",\n    \"isopen\",\n    \"json_agg\",\n    \"json_object\",\n    \"json_object_agg\",\n    \"json_populate_record\",\n    \"json_populate_recordset\",\n    \"json_to_record\",\n    \"json_to_recordset\",\n    \"jsonb_agg\",\n    \"jsonb_object_agg\",\n    \"justify_days\",\n    \"justify_hours\",\n    \"justify_interval\",\n    \"lastval\",\n    \"left\",\n    \"line\",\n    \"localtimestamp\",\n    \"lower_inc\",\n    \"lower_inf\",\n    \"lpad\",\n    \"lseg\",\n    \"make_date\",\n    \"make_interval\",\n    \"make_time\",\n    \"make_timestamp\",\n    \"make_timestamptz\",\n    \"masklen\",\n    \"mode\",\n    \"netmask\",\n    \"network\",\n    \"nextval\",\n    \"npoints\",\n    \"num_nonnulls\",\n    \"num_nulls\",\n    \"numnode\",\n    \"obj_description\",\n    \"overlay\",\n    \"parse_ident\",\n    \"path\",\n    \"pclose\",\n    \"percentile_disc\",\n    \"pg_advisory_lock\",\n    \"pg_advisory_lock_shared\",\n    \"pg_advisory_unlock\",\n    \"pg_advisory_unlock_all\",\n    \"pg_advisory_unlock_shared\",\n    \"pg_advisory_xact_lock\",\n    \"pg_advisory_xact_lock_shared\",\n    \"pg_backup_start_time\",\n    \"pg_blocking_pids\",\n    \"pg_client_encoding\",\n    \"pg_collation_is_visible\",\n    \"pg_column_size\",\n    \"pg_conf_load_time\",\n    \"pg_control_checkpoint\",\n    \"pg_control_init\",\n    \"pg_control_recovery\",\n    \"pg_control_system\",\n    \"pg_conversion_is_visible\",\n    \"pg_create_logical_replication_slot\",\n    \"pg_create_physical_replication_slot\",\n    \"pg_create_restore_point\",\n    \"pg_current_xlog_flush_location\",\n    \"pg_current_xlog_insert_location\",\n    \"pg_current_xlog_location\",\n    \"pg_database_size\",\n    \"pg_describe_object\",\n    \"pg_drop_replication_slot\",\n    \"pg_export_snapshot\",\n    \"pg_filenode_relation\",\n    \"pg_function_is_visible\",\n    \"pg_get_constraintdef\",\n    \"pg_get_expr\",\n    \"pg_get_function_arguments\",\n    \"pg_get_function_identity_arguments\",\n    \"pg_get_function_result\",\n    \"pg_get_functiondef\",\n    \"pg_get_indexdef\",\n    \"pg_get_keywords\",\n    \"pg_get_object_address\",\n    \"pg_get_owned_sequence\",\n    \"pg_get_ruledef\",\n    \"pg_get_serial_sequence\",\n    \"pg_get_triggerdef\",\n    \"pg_get_userbyid\",\n    \"pg_get_viewdef\",\n    \"pg_has_role\",\n    \"pg_identify_object\",\n    \"pg_identify_object_as_address\",\n    \"pg_index_column_has_property\",\n    \"pg_index_has_property\",\n    \"pg_indexam_has_property\",\n    \"pg_indexes_size\",\n    \"pg_is_in_backup\",\n    \"pg_is_in_recovery\",\n    \"pg_is_other_temp_schema\",\n    \"pg_is_xlog_replay_paused\",\n    \"pg_last_committed_xact\",\n    \"pg_last_xact_replay_timestamp\",\n    \"pg_last_xlog_receive_location\",\n    \"pg_last_xlog_replay_location\",\n    \"pg_listening_channels\",\n    \"pg_logical_emit_message\",\n    \"pg_logical_slot_get_binary_changes\",\n    \"pg_logical_slot_get_changes\",\n    \"pg_logical_slot_peek_binary_changes\",\n    \"pg_logical_slot_peek_changes\",\n    \"pg_ls_dir\",\n    \"pg_my_temp_schema\",\n    \"pg_notification_queue_usage\",\n    \"pg_opclass_is_visible\",\n    \"pg_operator_is_visible\",\n    \"pg_opfamily_is_visible\",\n    \"pg_options_to_table\",\n    \"pg_postmaster_start_time\",\n    \"pg_read_binary_file\",\n    \"pg_read_file\",\n    \"pg_relation_filenode\",\n    \"pg_relation_filepath\",\n    \"pg_relation_size\",\n    \"pg_reload_conf\",\n    \"pg_replication_origin_create\",\n    \"pg_replication_origin_drop\",\n    \"pg_replication_origin_oid\",\n    \"pg_replication_origin_progress\",\n    \"pg_replication_origin_session_is_setup\",\n    \"pg_replication_origin_session_progress\",\n    \"pg_replication_origin_session_reset\",\n    \"pg_replication_origin_session_setup\",\n    \"pg_replication_origin_xact_reset\",\n    \"pg_replication_origin_xact_setup\",\n    \"pg_rotate_logfile\",\n    \"pg_size_bytes\",\n    \"pg_size_pretty\",\n    \"pg_sleep\",\n    \"pg_sleep_for\",\n    \"pg_sleep_until\",\n    \"pg_start_backup\",\n    \"pg_stat_file\",\n    \"pg_stop_backup\",\n    \"pg_switch_xlog\",\n    \"pg_table_is_visible\",\n    \"pg_table_size\",\n    \"pg_tablespace_databases\",\n    \"pg_tablespace_location\",\n    \"pg_tablespace_size\",\n    \"pg_total_relation_size\",\n    \"pg_trigger_depth\",\n    \"pg_try_advisory_lock\",\n    \"pg_try_advisory_lock_shared\",\n    \"pg_try_advisory_xact_lock\",\n    \"pg_try_advisory_xact_lock_shared\",\n    \"pg_ts_config_is_visible\",\n    \"pg_ts_dict_is_visible\",\n    \"pg_ts_parser_is_visible\",\n    \"pg_ts_template_is_visible\",\n    \"pg_type_is_visible\",\n    \"pg_typeof\",\n    \"pg_xact_commit_timestamp\",\n    \"pg_xlog_location_diff\",\n    \"pg_xlog_replay_pause\",\n    \"pg_xlog_replay_resume\",\n    \"pg_xlogfile_name\",\n    \"pg_xlogfile_name_offset\",\n    \"phraseto_tsquery\",\n    \"plainto_tsquery\",\n    \"point\",\n    \"polygon\",\n    \"popen\",\n    \"pqserverversion\",\n    \"query_to_xml\",\n    \"querytree\",\n    \"quote_nullable\",\n    \"radius\",\n    \"range_merge\",\n    \"regexp_matches\",\n    \"regexp_split_to_array\",\n    \"regexp_split_to_table\",\n    \"regr_avgx\",\n    \"regr_avgy\",\n    \"regr_count\",\n    \"regr_intercept\",\n    \"regr_r2\",\n    \"regr_slope\",\n    \"regr_sxx\",\n    \"regr_sxy\",\n    \"regr_syy\",\n    \"right\",\n    \"row_security_active\",\n    \"row_to_json\",\n    \"rpad\",\n    \"scale\",\n    \"set_masklen\",\n    \"setseed\",\n    \"setval\",\n    \"setweight\",\n    \"shobj_description\",\n    \"sind\",\n    \"sprintf\",\n    \"statement_timestamp\",\n    \"stddev\",\n    \"string_agg\",\n    \"string_to_array\",\n    \"strip\",\n    \"substr\",\n    \"table_to_xml\",\n    \"table_to_xml_and_xmlschema\",\n    \"tand\",\n    \"text\",\n    \"to_json\",\n    \"to_regclass\",\n    \"to_regnamespace\",\n    \"to_regoper\",\n    \"to_regoperator\",\n    \"to_regproc\",\n    \"to_regprocedure\",\n    \"to_regrole\",\n    \"to_regtype\",\n    \"to_tsquery\",\n    \"to_tsvector\",\n    \"transaction_timestamp\",\n    \"ts_debug\",\n    \"ts_delete\",\n    \"ts_filter\",\n    \"ts_headline\",\n    \"ts_lexize\",\n    \"ts_parse\",\n    \"ts_rank\",\n    \"ts_rank_cd\",\n    \"ts_rewrite\",\n    \"ts_stat\",\n    \"ts_token_type\",\n    \"tsquery_phrase\",\n    \"tsvector_to_array\",\n    \"tsvector_update_trigger\",\n    \"tsvector_update_trigger_column\",\n    \"txid_current\",\n    \"txid_current_snapshot\",\n    \"txid_snapshot_xip\",\n    \"txid_snapshot_xmax\",\n    \"txid_snapshot_xmin\",\n    \"txid_visible_in_snapshot\",\n    \"unnest\",\n    \"upper_inc\",\n    \"upper_inf\",\n    \"variance\",\n    \"width\",\n    \"width_bucket\",\n    \"xml_is_well_formed\",\n    \"xml_is_well_formed_content\",\n    \"xml_is_well_formed_document\",\n    \"xmlagg\",\n    \"xmlcomment\",\n    \"xmlconcat\",\n    \"xmlelement\",\n    \"xmlexists\",\n    \"xmlforest\",\n    \"xmlparse\",\n    \"xmlpi\",\n    \"xmlroot\",\n    \"xmlserialize\",\n    \"xpath\",\n    \"xpath_exists\"\n  ],\n  builtinVariables: [\n    // NOT SUPPORTED\n  ],\n  pseudoColumns: [\n    // NOT SUPPORTED\n  ],\n  tokenizer: {\n    root: [\n      { include: \"@comments\" },\n      { include: \"@whitespace\" },\n      { include: \"@pseudoColumns\" },\n      { include: \"@numbers\" },\n      { include: \"@strings\" },\n      { include: \"@complexIdentifiers\" },\n      { include: \"@scopes\" },\n      [/[;,.]/, \"delimiter\"],\n      [/[()]/, \"@brackets\"],\n      [\n        /[\\w@#$]+/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@operators\": \"operator\",\n            \"@builtinVariables\": \"predefined\",\n            \"@builtinFunctions\": \"predefined\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/[<>=!%&+\\-*/|~^]/, \"operator\"]\n    ],\n    whitespace: [[/\\s+/, \"white\"]],\n    comments: [\n      [/--+.*/, \"comment\"],\n      [/\\/\\*/, { token: \"comment.quote\", next: \"@comment\" }]\n    ],\n    comment: [\n      [/[^*/]+/, \"comment\"],\n      // Not supporting nested comments, as nested comments seem to not be standard?\n      // i.e. http://stackoverflow.com/questions/728172/are-there-multiline-comment-delimiters-in-sql-that-are-vendor-agnostic\n      // [/\\/\\*/, { token: 'comment.quote', next: '@push' }],    // nested comment not allowed :-(\n      [/\\*\\//, { token: \"comment.quote\", next: \"@pop\" }],\n      [/./, \"comment\"]\n    ],\n    pseudoColumns: [\n      [\n        /[$][A-Za-z_][\\w@#$]*/,\n        {\n          cases: {\n            \"@pseudoColumns\": \"predefined\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ]\n    ],\n    numbers: [\n      [/0[xX][0-9a-fA-F]*/, \"number\"],\n      [/[$][+-]*\\d*(\\.\\d*)?/, \"number\"],\n      [/((\\d+(\\.\\d*)?)|(\\.\\d+))([eE][\\-+]?\\d+)?/, \"number\"]\n    ],\n    strings: [[/'/, { token: \"string\", next: \"@string\" }]],\n    string: [\n      [/[^']+/, \"string\"],\n      [/''/, \"string\"],\n      [/'/, { token: \"string\", next: \"@pop\" }]\n    ],\n    complexIdentifiers: [[/\"/, { token: \"identifier.quote\", next: \"@quotedIdentifier\" }]],\n    quotedIdentifier: [\n      [/[^\"]+/, \"identifier\"],\n      [/\"\"/, \"identifier\"],\n      [/\"/, { token: \"identifier.quote\", next: \"@pop\" }]\n    ],\n    scopes: [\n      // NOT SUPPORTED\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AASA,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC3B;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,IACnD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,EAC1D;AAAA,EACA,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,WAAW;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,kBAAkB;AAAA,IAChB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,kBAAkB;AAAA;AAAA,EAElB;AAAA,EACA,eAAe;AAAA;AAAA,EAEf;AAAA,EACA,WAAW;AAAA,IACT,MAAM;AAAA,MACJ,EAAE,SAAS,YAAY;AAAA,MACvB,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,iBAAiB;AAAA,MAC5B,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,sBAAsB;AAAA,MACjC,EAAE,SAAS,UAAU;AAAA,MACrB,CAAC,SAAS,WAAW;AAAA,MACrB,CAAC,QAAQ,WAAW;AAAA,MACpB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,aAAa;AAAA,YACb,cAAc;AAAA,YACd,qBAAqB;AAAA,YACrB,qBAAqB;AAAA,YACrB,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,oBAAoB,UAAU;AAAA,IACjC;AAAA,IACA,YAAY,CAAC,CAAC,OAAO,OAAO,CAAC;AAAA,IAC7B,UAAU;AAAA,MACR,CAAC,SAAS,SAAS;AAAA,MACnB,CAAC,QAAQ,EAAE,OAAO,iBAAiB,MAAM,WAAW,CAAC;AAAA,IACvD;AAAA,IACA,SAAS;AAAA,MACP,CAAC,UAAU,SAAS;AAAA;AAAA;AAAA;AAAA,MAIpB,CAAC,QAAQ,EAAE,OAAO,iBAAiB,MAAM,OAAO,CAAC;AAAA,MACjD,CAAC,KAAK,SAAS;AAAA,IACjB;AAAA,IACA,eAAe;AAAA,MACb;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,kBAAkB;AAAA,YAClB,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,SAAS;AAAA,MACP,CAAC,qBAAqB,QAAQ;AAAA,MAC9B,CAAC,uBAAuB,QAAQ;AAAA,MAChC,CAAC,2CAA2C,QAAQ;AAAA,IACtD;AAAA,IACA,SAAS,CAAC,CAAC,KAAK,EAAE,OAAO,UAAU,MAAM,UAAU,CAAC,CAAC;AAAA,IACrD,QAAQ;AAAA,MACN,CAAC,SAAS,QAAQ;AAAA,MAClB,CAAC,MAAM,QAAQ;AAAA,MACf,CAAC,KAAK,EAAE,OAAO,UAAU,MAAM,OAAO,CAAC;AAAA,IACzC;AAAA,IACA,oBAAoB,CAAC,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,oBAAoB,CAAC,CAAC;AAAA,IACpF,kBAAkB;AAAA,MAChB,CAAC,SAAS,YAAY;AAAA,MACtB,CAAC,MAAM,YAAY;AAAA,MACnB,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,OAAO,CAAC;AAAA,IACnD;AAAA,IACA,QAAQ;AAAA;AAAA,IAER;AAAA,EACF;AACF;", "names": []}