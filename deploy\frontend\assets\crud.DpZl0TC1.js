import{r as l,v as u}from"./index.BHZI5pdK.js";import{d as n}from"./dictionary.DNsEqk19.js";import"./vue.BNx9QYep.js";const o="/api/system/dictionary/";function i(e){return l({url:o,method:"get",params:e})}function s(e){return l({url:o,method:"post",data:e})}function c(e){return l({url:o+e.id+"/",method:"put",data:e})}function d(e){return l({url:o+e+"/",method:"delete",data:{}})}const y=function({crudExpose:e,context:p}){return{crudOptions:{request:{pageRequest:async t=>await i(t),addRequest:async({form:t})=>{const a=e.getSearchFormData().parent;if(t.parent=a,a)return await s(t)},editRequest:async({form:t,row:r})=>(t.id=r.id,await c(t)),delRequest:async({row:t})=>await d(t.id)},rowHandle:{fixed:"right",width:200,buttons:{view:{show:!1},edit:{iconRight:"Edit",type:"text"},remove:{iconRight:"Delete",type:"text"}}},columns:{_index:{title:"序号",form:{show:!1},column:{align:"center",width:"70px",columnSetDisabled:!0,formatter:t=>{let r=t.index??1,a=e.crudBinding.value.pagination;return((a.currentPage??1)-1)*a.pageSize+r+1}}},label:{title:"名称",search:{show:!0,component:{props:{clearable:!0}}},type:"input",form:{rules:[{required:!0,message:"名称必填项"}],component:{props:{clearable:!0},placeholder:"请输入名称"}}},type:{title:"数据值类型",type:"dict-select",search:{disabled:!0,component:{props:{clearable:!0}}},show:!1,dict:u({data:[{label:"text",value:0},{label:"number",value:1},{label:"date",value:2},{label:"datetime",value:3},{label:"time",value:4},{label:"file",value:5},{label:"boolean",value:6},{label:"images",value:7}]}),form:{rules:[{required:!0,message:"数据值类型必填项"}],value:0,component:{props:{clearable:!0},placeholder:"请选择数据值类型"}}},value:{title:"数据值",search:{show:!0,component:{props:{clearable:!0}}},view:{component:{props:{height:100,width:100}}},type:"input",form:{rules:[{required:!0,message:"数据值必填项"}],component:{props:{clearable:!0},placeholder:"请输入数据值"}}},status:{title:"状态",width:80,search:{show:!0},type:"dict-radio",dict:u({data:n("button_status_bool")}),form:{value:!0,rules:[{required:!0,message:"状态必填项"}]}},sort:{title:"排序",width:70,type:"number",form:{value:1,component:{},rules:[{required:!0,message:"排序必填项"}]}},color:{title:"标签颜色",width:90,search:{disabled:!0},type:"dict-select",dict:u({data:[{label:"success",value:"success",color:"success"},{label:"primary",value:"primary",color:"primary"},{label:"info",value:"info",color:"info"},{label:"danger",value:"danger",color:"danger"},{label:"warning",value:"warning",color:"warning"}]}),form:{component:{props:{clearable:!0}}}},is_value:{title:"是否值",column:{show:!1},form:{show:!1,value:!0}}}}}};export{y as createCrudOptions};
