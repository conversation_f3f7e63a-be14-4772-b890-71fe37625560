import{r as o,aw as V,v}from"./index.BHZI5pdK.js";import{t as b}from"./index.tdFpgju_.js";import{d as q,h as C,v as M,k as x,a as w,o as h,F as R,p as L,b as k,w as O,q as S,s as T,c as _,J as n}from"./vue.BNx9QYep.js";import{_ as A}from"./_plugin-vue_export-helper.DlAUqK2U.js";import{a as g}from"./authFunction.D3Be3hRy.js";const s="/api/system/message_center/";function D(t){return o({url:s,method:"get",params:t})}function j(t){return o({url:s+t+"/",method:"get"})}function B(t){return o({url:s+"get_self_receive/",method:"get",params:t})}function F(t){return o({url:s,method:"post",data:t})}function G(t){return o({url:s+t.id+"/",method:"put",data:t})}function N(t){return o({url:s+t+"/",method:"delete",data:{id:t}})}const I=q({__name:"index",props:{modelValue:Array,bindValue:Array,displayLabel:{type:String,default:""}},setup(t){const c=t,r=C();M(()=>c.bindValue,u=>{const{displayLabel:p}=c,i=u?u.map(d=>d[p]):null;r.value=i},{immediate:!0});const m=["success","info","warning","danger"],f=()=>m[Math.floor(Math.random()*m.length)];return(u,p)=>{const i=x("el-tag");return h(),w("div",null,[(h(!0),w(R,null,L(r.value,(d,e)=>(h(),k(i,{class:"many-to-many-tag",type:f,key:e},{default:O(()=>[S(T(d),1)]),_:2},1024))),128))])}}}),y=A(I,[["__scopeId","data-v-4dbdd8c1"]]),{compute:a}=V();function z({crudExpose:t,context:c}){const{tabActivted:r}=c,m=async e=>r.value==="receive"?await B(e):await D(e),f=async({form:e,row:l})=>(e.id=l.id,await G(e)),u=async({row:e})=>await N(e.id),p=async({form:e})=>await F(e),i=async({row:e})=>await j(e.id),d=_(()=>r.value==="receive");return{crudOptions:{request:{pageRequest:m,addRequest:p,editRequest:f,delRequest:u},actionbar:{buttons:{add:{show:_(()=>r.value!=="receive"&&g("messageCenter:Create"))}}},rowHandle:{fixed:"right",width:150,buttons:{edit:{show:!1},view:{text:"查看",type:"text",iconRight:"View",show:g("messageCenter:Search"),click({index:e,row:l}){t.openView({index:e,row:l}),r.value==="receive"&&(i({row:l}),t.doRefresh())}},remove:{iconRight:"Delete",type:"text",show:g("messageCenter:Delete")}}},columns:{id:{title:"id",form:{show:!1}},title:{title:"标题",search:{show:!0},type:["text","colspan"],column:{minWidth:120},form:{rules:[{required:!0,message:"必填项"}],component:{span:24,placeholder:"请输入标题"}}},is_read:{title:"是否已读",type:"dict-select",column:{show:d.value},dict:v({data:[{label:"已读",value:!0,color:"success"},{label:"未读",value:!1,color:"danger"}]}),form:{show:!1}},target_type:{title:"目标类型",type:["dict-radio","colspan"],column:{minWidth:120},dict:v({data:[{value:0,label:"按用户"},{value:1,label:"按角色"},{value:2,label:"按部门"},{value:3,label:"通知公告"}]}),form:{component:{optionName:"el-radio-button"},rules:[{required:!0,message:"必选项",trigger:["blur","change"]}]}},target_user:{title:"目标用户",search:{disabled:!0},form:{component:{name:n(b),vModel:"modelValue",displayLabel:a(({row:e})=>e?e.user_info:null),tableConfig:{url:"/api/system/user/",label:"name",value:"id",isMultiple:!0,columns:[{prop:"name",label:"用户名称",width:120},{prop:"phone",label:"用户电话",width:120}]}},show:a(({form:e})=>e.target_type===0),rules:[{required:!0,message:"必填项"}]},column:{show:!1,component:{name:n(y),vModel:"modelValue",bindValue:a(({row:e})=>e.user_info),displayLabel:"name"}}},target_role:{title:"目标角色",search:{disabled:!0},width:130,form:{component:{name:n(b),vModel:"modelValue",displayLabel:a(({row:e})=>e?e.role_info:null),tableConfig:{url:"/api/system/role/",label:"name",value:"id",isMultiple:!0,columns:[{prop:"name",label:"角色名称"},{prop:"key",label:"权限标识"}]}},show:a(({form:e})=>e.target_type===1),rules:[{required:!0,message:"必填项"}]},column:{show:!1,component:{name:n(y),vModel:"modelValue",bindValue:a(({row:e})=>e.role_info),displayLabel:"name"}}},target_dept:{title:"目标部门",search:{disabled:!0},width:130,type:"table-selector",form:{component:{name:n(b),vModel:"modelValue",displayLabel:a(({form:e})=>e.dept_info),tableConfig:{url:"/api/system/dept/all_dept/",label:"name",value:"id",isTree:!0,isMultiple:!0,columns:[{prop:"name",label:"部门名称",width:150},{prop:"status_label",label:"状态"},{prop:"parent_name",label:"父级部门"}]}},show:a(({form:e})=>e.target_type===2),rules:[{required:!0,message:"必填项"}]},column:{show:!1,component:{name:n(y),vModel:"modelValue",bindValue:a(({row:e})=>e.dept_info),displayLabel:"name"}}},content:{title:"内容",column:{width:300,show:!1},type:["editor-wang5","colspan"],form:{rules:[{required:!0,message:"必填项"}],component:{disabled:!1,id:"1",editorConfig:{readOnly:a(e=>{const{mode:l}=e;return l!=="add"})},uploader:{type:"form",buildUrl(e){return e.url}}}}}}}}}export{z as default};
