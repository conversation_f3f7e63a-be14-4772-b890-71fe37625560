import{d as A,R as I,h as D,g as O,j as q,I as M,k as f,a as P,o as F,l as s,e,w as a,s as i,q as y,u as l,f as V,b as $,r as j}from"./vue.BNx9QYep.js";import{r as G,n as U,q as Y,ao as Z,ak as H,t as J,w as b,Z as K,p as Q,ap as W,e as x}from"./index.BHZI5pdK.js";import{i as X}from"./echarts.BiCAFTQd.js";import{_ as tt}from"./_plugin-vue_export-helper.DlAUqK2U.js";const st="/api/data-analysis/";function et(){return G({url:st+"dashboard/",method:"get"})}function C(c,p=2){return c==null||isNaN(c)?"0.00":c.toLocaleString("zh-CN",{minimumFractionDigits:p,maximumFractionDigits:p})}function at(c,p=2){return c==null||isNaN(c)?"0.00%":c.toFixed(p)+"%"}const ot={class:"data-analysis-container"},nt={class:"card-header"},rt={class:"header-info"},lt={class:"update-time"},it={class:"welcome-content"},dt={class:"stats-section"},ct={class:"stat-content"},ut={class:"stat-icon"},_t={class:"stat-info"},ft={class:"stat-value"},pt={class:"stat-desc"},ht={class:"stat-content"},mt={class:"stat-icon"},vt={class:"stat-info"},gt={class:"stat-value"},wt={class:"stat-content"},yt={class:"stat-icon"},bt={class:"stat-info"},xt={class:"stat-value"},Ct={class:"stat-content"},zt={class:"stat-icon"},Dt={class:"stat-info"},Mt={class:"stat-value"},Ft={class:"chart-header"},At={class:"company-sales-table"},St={class:"company-name"},kt={class:"amount-text"},Nt={class:"chart-header"},Et={class:"chart-header"},Tt={class:"feature-grid"},Rt=A({name:"DataAnalysisIndex"}),Lt=A({...Rt,setup(c){const p=I(),v=D(!1),g=D();let h=null;const n=O({currentMonth:{year:new Date().getFullYear(),month:new Date().getMonth()+1,statistics:{销售总额:0,订单数量:0,客户数量:0,产品种类:0,销售数量:0},growthRate:0,lastMonthAmount:0},companySales:[],trendData:[],updateTime:""}),S=o=>o||"未知公司",k=o=>o>0?"growth-positive":o<0?"growth-negative":"growth-neutral",N=o=>o>0?"ArrowUp":o<0?"ArrowDown":"Minus",E=()=>{if(!g.value)return;h=X(g.value);const o={title:{text:"销售金额趋势",left:"center",textStyle:{fontSize:14,color:"#303133"}},tooltip:{trigger:"axis",formatter:t=>{const r=t[0];return`${r.name}<br/>销售金额: ${C(r.value)}元`}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:n.trendData.map(t=>t.月份),axisLabel:{color:"#606266"}},yAxis:{type:"value",axisLabel:{color:"#606266",formatter:t=>t>=1e4?(t/1e4).toFixed(1)+"万":t.toString()}},series:[{name:"销售金额",type:"line",smooth:!0,data:n.trendData.map(t=>t.销售金额),lineStyle:{color:"#409EFF",width:3},itemStyle:{color:"#409EFF"},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(64, 158, 255, 0.3)"},{offset:1,color:"rgba(64, 158, 255, 0.1)"}]}}}]};h.setOption(o)},T=()=>{if(!h)return;const o={xAxis:{data:n.trendData.map(t=>t.月份)},series:[{data:n.trendData.map(t=>t.销售金额)}]};h.setOption(o)},z=async()=>{v.value=!0;try{const o=await et();o.code===200?(Object.assign(n,o.data),await M(),T(),x.success("数据刷新成功")):x.error(o.message||"数据获取失败")}catch(o){console.error("数据获取失败:",o),x.error("数据获取失败："+(o.message||"网络错误"))}finally{v.value=!1}},R=o=>{p.push(o)};return q(async()=>{await z(),await M(),E(),window.addEventListener("resize",()=>{h&&h.resize()})}),(o,t)=>{const r=f("el-icon"),L=f("el-button"),d=f("el-card"),u=f("el-col"),w=f("el-row"),m=f("el-table-column"),B=f("el-table");return F(),P("div",ot,[s(d,{class:"header-card"},{header:a(()=>[e("div",nt,[e("div",rt,[e("span",lt,"更新时间: "+i(n.updateTime),1),s(L,{type:"primary",size:"small",onClick:z,loading:v.value},{default:a(()=>[s(r,null,{default:a(()=>[s(l(U))]),_:1}),t[1]||(t[1]=y(" 刷新数据 "))]),_:1,__:[1]},8,["loading"])])])]),default:a(()=>[e("div",it,[e("h2",null,i(n.currentMonth.year)+"年"+i(n.currentMonth.month)+"月销售数据概览",1),t[2]||(t[2]=e("p",null,"实时展示当月销售业绩、各公司销售情况和趋势分析",-1))])]),_:1}),e("div",dt,[s(w,{gutter:20},{default:a(()=>[s(u,{span:6},{default:a(()=>[s(d,{class:"stat-card sales-amount",shadow:"hover"},{default:a(()=>[e("div",ct,[e("div",ut,[s(r,{size:"32"},{default:a(()=>[s(l(Y))]),_:1})]),e("div",_t,[t[4]||(t[4]=e("div",{class:"stat-title"},"当月销售总额",-1)),e("div",ft,i(l(C)(n.currentMonth.statistics.销售总额)),1),e("div",pt,[e("span",{class:V(k(n.currentMonth.growthRate))},[s(r,null,{default:a(()=>[(F(),$(j(N(n.currentMonth.growthRate))))]),_:1}),y(" "+i(l(at)(Math.abs(n.currentMonth.growthRate))),1)],2),t[3]||(t[3]=y(" 环比上月 "))])])])]),_:1})]),_:1}),s(u,{span:6},{default:a(()=>[s(d,{class:"stat-card orders",shadow:"hover"},{default:a(()=>[e("div",ht,[e("div",mt,[s(r,{size:"32"},{default:a(()=>[s(l(Z))]),_:1})]),e("div",vt,[t[5]||(t[5]=e("div",{class:"stat-title"},"订单数量",-1)),e("div",gt,i(n.currentMonth.statistics.订单数量),1),t[6]||(t[6]=e("div",{class:"stat-desc"},"当月成交订单总数",-1))])])]),_:1})]),_:1}),s(u,{span:6},{default:a(()=>[s(d,{class:"stat-card customers",shadow:"hover"},{default:a(()=>[e("div",wt,[e("div",yt,[s(r,{size:"32"},{default:a(()=>[s(l(H))]),_:1})]),e("div",bt,[t[7]||(t[7]=e("div",{class:"stat-title"},"客户数量",-1)),e("div",xt,i(n.currentMonth.statistics.客户数量),1),t[8]||(t[8]=e("div",{class:"stat-desc"},"当月活跃客户总数",-1))])])]),_:1})]),_:1}),s(u,{span:6},{default:a(()=>[s(d,{class:"stat-card products",shadow:"hover"},{default:a(()=>[e("div",Ct,[e("div",zt,[s(r,{size:"32"},{default:a(()=>[s(l(J))]),_:1})]),e("div",Dt,[t[9]||(t[9]=e("div",{class:"stat-title"},"产品种类",-1)),e("div",Mt,i(n.currentMonth.statistics.产品种类),1),t[10]||(t[10]=e("div",{class:"stat-desc"},"当月销售产品种类",-1))])])]),_:1})]),_:1})]),_:1})]),s(w,{gutter:20,class:"charts-section"},{default:a(()=>[s(u,{span:12},{default:a(()=>[s(d,{class:"chart-card",shadow:"hover"},{header:a(()=>[e("div",Ft,[s(r,{size:"20",color:"#67C23A"},{default:a(()=>[s(l(b))]),_:1}),t[11]||(t[11]=e("span",null,"各公司销售金额汇总",-1))])]),default:a(()=>[e("div",At,[s(B,{data:n.companySales,stripe:"",style:{width:"100%"},"max-height":"400"},{default:a(()=>[s(m,{prop:"公司名称",label:"公司名称",width:"300","show-overflow-tooltip":""},{default:a(({row:_})=>[e("span",St,i(S(_.公司名称)),1)]),_:1}),s(m,{prop:"销售金额",label:"销售金额",align:"right",sortable:""},{default:a(({row:_})=>[e("span",kt,i(l(C)(_.销售金额)),1)]),_:1}),s(m,{prop:"订单数量",label:"订单数",align:"right",sortable:""},{default:a(({row:_})=>[e("span",null,i(_.订单数量),1)]),_:1}),s(m,{prop:"客户数量",label:"客户数",align:"right",sortable:""},{default:a(({row:_})=>[e("span",null,i(_.客户数量),1)]),_:1})]),_:1},8,["data"])])]),_:1})]),_:1}),s(u,{span:12},{default:a(()=>[s(d,{class:"chart-card",shadow:"hover"},{header:a(()=>[e("div",Nt,[s(r,{size:"20",color:"#E6A23C"},{default:a(()=>[s(l(b))]),_:1}),t[12]||(t[12]=e("span",null,"近6个月销售趋势",-1))])]),default:a(()=>[e("div",{class:"trend-chart",ref_key:"trendChartRef",ref:g,style:{height:"400px"}},null,512)]),_:1})]),_:1})]),_:1}),s(d,{class:"feature-card-container",shadow:"hover"},{header:a(()=>[e("div",Et,[s(r,{size:"20",color:"#409EFF"},{default:a(()=>[s(l(W))]),_:1}),t[13]||(t[13]=e("span",null,"快捷功能",-1))])]),default:a(()=>[e("div",Tt,[s(w,{gutter:20},{default:a(()=>[s(u,{span:8},{default:a(()=>[s(d,{class:"feature-card",shadow:"hover",onClick:t[0]||(t[0]=_=>R("/price-analysis"))},{default:a(()=>[s(r,{size:"48",color:"#F56C6C"},{default:a(()=>[s(l(K))]),_:1}),t[14]||(t[14]=e("h3",null,"价格分析",-1)),t[15]||(t[15]=e("p",null,"监控价格变化和市场趋势",-1))]),_:1,__:[14,15]})]),_:1}),s(u,{span:8},{default:a(()=>[s(d,{class:"feature-card",shadow:"hover"},{default:a(()=>[s(r,{size:"48",color:"#67C23A"},{default:a(()=>[s(l(b))]),_:1}),t[16]||(t[16]=e("h3",null,"销售分析",-1)),t[17]||(t[17]=e("p",null,"分析销售数据趋势和业绩",-1))]),_:1,__:[16,17]})]),_:1}),s(u,{span:8},{default:a(()=>[s(d,{class:"feature-card",shadow:"hover"},{default:a(()=>[s(r,{size:"48",color:"#E6A23C"},{default:a(()=>[s(l(Q))]),_:1}),t[18]||(t[18]=e("h3",null,"客户分析",-1)),t[19]||(t[19]=e("p",null,"深入了解客户行为和偏好",-1))]),_:1,__:[18,19]})]),_:1})]),_:1})])]),_:1})])}}}),Pt=tt(Lt,[["__scopeId","data-v-60ba9390"]]);export{Pt as default};
