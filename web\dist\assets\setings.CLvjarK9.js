import{e as P,ae as he,af as Ce,a7 as ce,u as xe,L as z,l as q,V as _e,ay as we}from"./index.BHZI5pdK.js";import{d as me,M as Ve,g as Te,c as Se,j as $e,I as Be,O as Ee,k as M,a as Me,o as ke,l as u,w as V,e as a,q as L,s as m,n as A,f as D}from"./vue.BNx9QYep.js";import{f as ne}from"./formatTime.in1fXasu.js";import{_ as Ie}from"./_plugin-vue_export-helper.DlAUqK2U.js";function J(){return{hexToRgb:g=>{let f="";if(!/^\#?[0-9A-Fa-f]{6}$/.test(g))return P.warning("输入错误的hex"),"";g=g.replace("#",""),f=g.match(/../g);for(let o=0;o<3;o++)f[o]=parseInt(f[o],16);return f},rgbToHex:(g,f,b)=>{let o=/^\d{1,3}$/;if(!o.test(g)||!o.test(f)||!o.test(b))return P.warning("输入错误的rgb颜色值"),"";let d=[g.toString(16),f.toString(16),b.toString(16)];for(let e=0;e<3;e++)d[e].length==1&&(d[e]=`0${d[e]}`);return`#${d.join("")}`},getDarkColor:(g,f)=>{if(!/^\#?[0-9A-Fa-f]{6}$/.test(g))return P.warning("输入错误的hex颜色值"),"";let o=J().hexToRgb(g);for(let d=0;d<3;d++)o[d]=Math.floor(o[d]*(1-f));return J().rgbToHex(o[0],o[1],o[2])},getLightColor:(g,f)=>{if(!/^\#?[0-9A-Fa-f]{6}$/.test(g))return P.warning("输入错误的hex颜色值"),"";let o=J().hexToRgb(g);for(let d=0;d<3;d++)o[d]=Math.floor((255-o[d])*f+o[d]);return J().rgbToHex(o[0],o[1],o[2])}}}const de=$=>{const B="1.23452384164.123412416";document.getElementById(B)!==null&&document.body.removeChild(document.getElementById(B));const T=document.createElement("canvas");T.width=200,T.height=130;const S=T.getContext("2d");S.rotate(-20*Math.PI/180),S.font="12px Vedana",S.fillStyle="rgba(200, 200, 200, 0.30)",S.textBaseline="middle",S.fillText($,T.width/10,T.height/2);const g=document.createElement("div");return g.id=B,g.style.pointerEvents="none",g.style.top="0px",g.style.left="0px",g.style.position="fixed",g.style.zIndex="10000000",g.style.width=`${document.documentElement.clientWidth}px`,g.style.height=`${document.documentElement.clientHeight}px`,g.style.background=`url(${T.toDataURL("image/png")}) left top repeat`,document.body.appendChild(g),B},re={set:$=>{let B=de($);document.getElementById(B)===null&&(B=de($))},del:()=>{let $="1.23452384164.123412416";document.getElementById($)!==null&&document.body.removeChild(document.getElementById($))}};var fe={exports:{}};/*!
* clipboard.js v2.0.11
* https://clipboardjs.com/
*
* Licensed MIT © Zeno Rocha
*/(function($,B){(function(S,g){$.exports=g()})(Ce,function(){return function(){var T={686:function(f,b,o){o.d(b,{default:function(){return se}});var d=o(279),e=o.n(d),c=o(370),y=o.n(c),v=o(817),_=o.n(v);function h(l){try{return document.execCommand(l)}catch{return!1}}var w=function(t){var n=_()(t);return h("cut"),n},C=w;function I(l){var t=document.documentElement.getAttribute("dir")==="rtl",n=document.createElement("textarea");n.style.fontSize="12pt",n.style.border="0",n.style.padding="0",n.style.margin="0",n.style.position="absolute",n.style[t?"right":"left"]="-9999px";var i=window.pageYOffset||document.documentElement.scrollTop;return n.style.top="".concat(i,"px"),n.setAttribute("readonly",""),n.value=l,n}var X=function(t,n){var i=I(t);n.container.appendChild(i);var r=_()(i);return h("copy"),i.remove(),r},Q=function(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{container:document.body},i="";return typeof t=="string"?i=X(t,n):t instanceof HTMLInputElement&&!["text","search","url","tel","password"].includes(t==null?void 0:t.type)?i=X(t.value,n):(i=_()(t),h("copy")),i},N=Q;function O(l){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?O=function(n){return typeof n}:O=function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},O(l)}var Z=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=t.action,i=n===void 0?"copy":n,r=t.container,p=t.target,k=t.text;if(i!=="copy"&&i!=="cut")throw new Error('Invalid "action" value, use either "copy" or "cut"');if(p!==void 0)if(p&&O(p)==="object"&&p.nodeType===1){if(i==="copy"&&p.hasAttribute("disabled"))throw new Error('Invalid "target" attribute. Please use "readonly" instead of "disabled" attribute');if(i==="cut"&&(p.hasAttribute("readonly")||p.hasAttribute("disabled")))throw new Error(`Invalid "target" attribute. You can't cut text from elements with "readonly" or "disabled" attributes`)}else throw new Error('Invalid "target" value, use a valid Element');if(k)return N(k,{container:r});if(p)return i==="cut"?C(p):N(p,{container:r})},ee=Z;function U(l){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?U=function(n){return typeof n}:U=function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},U(l)}function K(l,t){if(!(l instanceof t))throw new TypeError("Cannot call a class as a function")}function G(l,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(l,i.key,i)}}function te(l,t,n){return t&&G(l.prototype,t),n&&G(l,n),l}function R(l,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");l.prototype=Object.create(t&&t.prototype,{constructor:{value:l,writable:!0,configurable:!0}}),t&&F(l,t)}function F(l,t){return F=Object.setPrototypeOf||function(i,r){return i.__proto__=r,i},F(l,t)}function ae(l){var t=x();return function(){var i=H(l),r;if(t){var p=H(this).constructor;r=Reflect.construct(i,arguments,p)}else r=i.apply(this,arguments);return le(this,r)}}function le(l,t){return t&&(U(t)==="object"||typeof t=="function")?t:j(l)}function j(l){if(l===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return l}function x(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch{return!1}}function H(l){return H=Object.setPrototypeOf?Object.getPrototypeOf:function(n){return n.__proto__||Object.getPrototypeOf(n)},H(l)}function Y(l,t){var n="data-clipboard-".concat(l);if(t.hasAttribute(n))return t.getAttribute(n)}var oe=function(l){R(n,l);var t=ae(n);function n(i,r){var p;return K(this,n),p=t.call(this),p.resolveOptions(r),p.listenClick(i),p}return te(n,[{key:"resolveOptions",value:function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.action=typeof r.action=="function"?r.action:this.defaultAction,this.target=typeof r.target=="function"?r.target:this.defaultTarget,this.text=typeof r.text=="function"?r.text:this.defaultText,this.container=U(r.container)==="object"?r.container:document.body}},{key:"listenClick",value:function(r){var p=this;this.listener=y()(r,"click",function(k){return p.onClick(k)})}},{key:"onClick",value:function(r){var p=r.delegateTarget||r.currentTarget,k=this.action(p)||"copy",E=ee({action:k,container:this.container,target:this.target(p),text:this.text(p)});this.emit(E?"success":"error",{action:k,text:E,trigger:p,clearSelection:function(){p&&p.focus(),window.getSelection().removeAllRanges()}})}},{key:"defaultAction",value:function(r){return Y("action",r)}},{key:"defaultTarget",value:function(r){var p=Y("target",r);if(p)return document.querySelector(p)}},{key:"defaultText",value:function(r){return Y("text",r)}},{key:"destroy",value:function(){this.listener.destroy()}}],[{key:"copy",value:function(r){var p=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{container:document.body};return N(r,p)}},{key:"cut",value:function(r){return C(r)}},{key:"isSupported",value:function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:["copy","cut"],p=typeof r=="string"?[r]:r,k=!!document.queryCommandSupported;return p.forEach(function(E){k=k&&!!document.queryCommandSupported(E)}),k}}]),n}(e()),se=oe},828:function(f){var b=9;if(typeof Element<"u"&&!Element.prototype.matches){var o=Element.prototype;o.matches=o.matchesSelector||o.mozMatchesSelector||o.msMatchesSelector||o.oMatchesSelector||o.webkitMatchesSelector}function d(e,c){for(;e&&e.nodeType!==b;){if(typeof e.matches=="function"&&e.matches(c))return e;e=e.parentNode}}f.exports=d},438:function(f,b,o){var d=o(828);function e(v,_,h,w,C){var I=y.apply(this,arguments);return v.addEventListener(h,I,C),{destroy:function(){v.removeEventListener(h,I,C)}}}function c(v,_,h,w,C){return typeof v.addEventListener=="function"?e.apply(null,arguments):typeof h=="function"?e.bind(null,document).apply(null,arguments):(typeof v=="string"&&(v=document.querySelectorAll(v)),Array.prototype.map.call(v,function(I){return e(I,_,h,w,C)}))}function y(v,_,h,w){return function(C){C.delegateTarget=d(C.target,_),C.delegateTarget&&w.call(v,C)}}f.exports=c},879:function(f,b){b.node=function(o){return o!==void 0&&o instanceof HTMLElement&&o.nodeType===1},b.nodeList=function(o){var d=Object.prototype.toString.call(o);return o!==void 0&&(d==="[object NodeList]"||d==="[object HTMLCollection]")&&"length"in o&&(o.length===0||b.node(o[0]))},b.string=function(o){return typeof o=="string"||o instanceof String},b.fn=function(o){var d=Object.prototype.toString.call(o);return d==="[object Function]"}},370:function(f,b,o){var d=o(879),e=o(438);function c(h,w,C){if(!h&&!w&&!C)throw new Error("Missing required arguments");if(!d.string(w))throw new TypeError("Second argument must be a String");if(!d.fn(C))throw new TypeError("Third argument must be a Function");if(d.node(h))return y(h,w,C);if(d.nodeList(h))return v(h,w,C);if(d.string(h))return _(h,w,C);throw new TypeError("First argument must be a String, HTMLElement, HTMLCollection, or NodeList")}function y(h,w,C){return h.addEventListener(w,C),{destroy:function(){h.removeEventListener(w,C)}}}function v(h,w,C){return Array.prototype.forEach.call(h,function(I){I.addEventListener(w,C)}),{destroy:function(){Array.prototype.forEach.call(h,function(I){I.removeEventListener(w,C)})}}}function _(h,w,C){return e(document.body,h,w,C)}f.exports=c},817:function(f){function b(o){var d;if(o.nodeName==="SELECT")o.focus(),d=o.value;else if(o.nodeName==="INPUT"||o.nodeName==="TEXTAREA"){var e=o.hasAttribute("readonly");e||o.setAttribute("readonly",""),o.select(),o.setSelectionRange(0,o.value.length),e||o.removeAttribute("readonly"),d=o.value}else{o.hasAttribute("contenteditable")&&o.focus();var c=window.getSelection(),y=document.createRange();y.selectNodeContents(o),c.removeAllRanges(),c.addRange(y),d=c.toString()}return d}f.exports=b},279:function(f){function b(){}b.prototype={on:function(o,d,e){var c=this.e||(this.e={});return(c[o]||(c[o]=[])).push({fn:d,ctx:e}),this},once:function(o,d,e){var c=this;function y(){c.off(o,y),d.apply(e,arguments)}return y._=d,this.on(o,y,e)},emit:function(o){var d=[].slice.call(arguments,1),e=((this.e||(this.e={}))[o]||[]).slice(),c=0,y=e.length;for(c;c<y;c++)e[c].fn.apply(e[c].ctx,d);return this},off:function(o,d){var e=this.e||(this.e={}),c=e[o],y=[];if(c&&d)for(var v=0,_=c.length;v<_;v++)c[v].fn!==d&&c[v].fn._!==d&&y.push(c[v]);return y.length?e[o]=y:delete e[o],this}},f.exports=b,f.exports.TinyEmitter=b}},S={};function g(f){if(S[f])return S[f].exports;var b=S[f]={exports:{}};return T[f](b,b.exports,g),b.exports}return function(){g.n=function(f){var b=f&&f.__esModule?function(){return f.default}:function(){return f};return g.d(b,{a:b}),b}}(),function(){g.d=function(f,b){for(var o in b)g.o(b,o)&&!g.o(f,o)&&Object.defineProperty(f,o,{enumerable:!0,get:b[o]})}}(),function(){g.o=function(f,b){return Object.prototype.hasOwnProperty.call(f,b)}}(),g(686)}().default})})(fe);var Ae=fe.exports;const ze=he(Ae),Ue=$=>({toClipboard(B,T){return new Promise((S,g)=>{const f=document.createElement("button"),b=new ze(f,{text:()=>B,action:()=>"copy",container:T!==void 0?T:document.body});b.on("success",o=>{b.destroy(),S(o)}),b.on("error",o=>{b.destroy(),g(o)}),document.body.appendChild(f),f.click(),document.body.removeChild(f)})}});function Le(){const{t:$}=ce.useI18n(),{toClipboard:B}=Ue();return{percentFormat:(c,y,v)=>v?`${v}%`:"-",dateFormatYMD:(c,y,v)=>v?ne(new Date(v),"YYYY-mm-dd"):"-",dateFormatYMDHMS:(c,y,v)=>v?ne(new Date(v),"YYYY-mm-dd HH:MM:SS"):"-",dateFormatHMS:(c,y,v)=>{if(!v)return"-";let _=0;return typeof c=="number"&&(_=c),typeof v=="number"&&(_=v),ne(new Date(_*1e3),"HH:MM:SS")},scaleFormat:(c="0",y=4)=>c==null?"0.0000":Number(c).toFixed(y),scale2Format:(c="0")=>c==null?"0.00":Number(c).toFixed(2),safeNumberFormat:(c,y=2,v="0.00")=>{if(c==null||c==="")return v;const _=Number(c);return isNaN(_)?v:_.toFixed(y)},copyText:c=>new Promise((y,v)=>{try{B(c),P.success($("message.layout.copyTextSuccess")),y(c)}catch(_){P.error($("message.layout.copyTextError")),v(_)}})}}const De={class:"layout-breadcrumb-seting"},Fe={class:"layout-breadcrumb-seting-bar-flex"},Pe={class:"layout-breadcrumb-seting-bar-flex-value"},Oe={class:"layout-breadcrumb-seting-bar-flex mt15"},Re={class:"layout-breadcrumb-seting-bar-flex-label"},He={class:"layout-breadcrumb-seting-bar-flex-value"},Ne={class:"layout-breadcrumb-seting-bar-flex"},Ge={class:"layout-breadcrumb-seting-bar-flex-label"},je={class:"layout-breadcrumb-seting-bar-flex-value"},Ye={class:"layout-breadcrumb-seting-bar-flex"},We={class:"layout-breadcrumb-seting-bar-flex-label"},qe={class:"layout-breadcrumb-seting-bar-flex-value"},Je={class:"layout-breadcrumb-seting-bar-flex mt10"},Xe={class:"layout-breadcrumb-seting-bar-flex-label"},Ke={class:"layout-breadcrumb-seting-bar-flex-value"},Qe={class:"layout-breadcrumb-seting-bar-flex"},Ze={class:"layout-breadcrumb-seting-bar-flex-label"},et={class:"layout-breadcrumb-seting-bar-flex-value"},tt={class:"layout-breadcrumb-seting-bar-flex"},at={class:"layout-breadcrumb-seting-bar-flex-label"},lt={class:"layout-breadcrumb-seting-bar-flex-value"},ot={class:"layout-breadcrumb-seting-bar-flex"},st={class:"layout-breadcrumb-seting-bar-flex-label"},nt={class:"layout-breadcrumb-seting-bar-flex-value"},rt={class:"layout-breadcrumb-seting-bar-flex mt14"},ut={class:"layout-breadcrumb-seting-bar-flex-label"},it={class:"layout-breadcrumb-seting-bar-flex-value"},dt={class:"layout-breadcrumb-seting-bar-flex-label"},ct={class:"layout-breadcrumb-seting-bar-flex-value"},mt={class:"layout-breadcrumb-seting-bar-flex-label"},ft={class:"layout-breadcrumb-seting-bar-flex-value"},bt={class:"layout-breadcrumb-seting-bar-flex-label"},yt={class:"layout-breadcrumb-seting-bar-flex-value"},vt={class:"layout-breadcrumb-seting-bar-flex-label"},gt={class:"layout-breadcrumb-seting-bar-flex-value"},pt={class:"layout-breadcrumb-seting-bar-flex-label"},ht={class:"layout-breadcrumb-seting-bar-flex-value"},Ct={class:"layout-breadcrumb-seting-bar-flex-label"},xt={class:"layout-breadcrumb-seting-bar-flex-value"},_t={class:"layout-breadcrumb-seting-bar-flex mt15"},wt={class:"layout-breadcrumb-seting-bar-flex-label"},Vt={class:"layout-breadcrumb-seting-bar-flex-value"},Tt={class:"layout-breadcrumb-seting-bar-flex-label"},St={class:"layout-breadcrumb-seting-bar-flex-value"},$t={class:"layout-breadcrumb-seting-bar-flex mt15"},Bt={class:"layout-breadcrumb-seting-bar-flex-label"},Et={class:"layout-breadcrumb-seting-bar-flex-value"},Mt={class:"layout-breadcrumb-seting-bar-flex mt11"},kt={class:"layout-breadcrumb-seting-bar-flex-label"},It={class:"layout-breadcrumb-seting-bar-flex-value"},At={class:"layout-breadcrumb-seting-bar-flex mt15"},zt={class:"layout-breadcrumb-seting-bar-flex-label"},Ut={class:"layout-breadcrumb-seting-bar-flex-value"},Lt={class:"layout-breadcrumb-seting-bar-flex-label"},Dt={class:"layout-breadcrumb-seting-bar-flex-value"},Ft={class:"layout-breadcrumb-seting-bar-flex mt15"},Pt={class:"layout-breadcrumb-seting-bar-flex-label"},Ot={class:"layout-breadcrumb-seting-bar-flex-value"},Rt={class:"layout-breadcrumb-seting-bar-flex mt15"},Ht={class:"layout-breadcrumb-seting-bar-flex-label"},Nt={class:"layout-breadcrumb-seting-bar-flex-value"},Gt={class:"layout-breadcrumb-seting-bar-flex mt15"},jt={class:"layout-breadcrumb-seting-bar-flex-label"},Yt={class:"layout-breadcrumb-seting-bar-flex-value"},Wt={class:"layout-breadcrumb-seting-bar-flex mt15"},qt={class:"layout-breadcrumb-seting-bar-flex-label"},Jt={class:"layout-breadcrumb-seting-bar-flex-value"},Xt={class:"layout-breadcrumb-seting-bar-flex-label"},Kt={class:"layout-breadcrumb-seting-bar-flex-value"},Qt={class:"layout-breadcrumb-seting-bar-flex mt15"},Zt={class:"layout-breadcrumb-seting-bar-flex-label"},ea={class:"layout-breadcrumb-seting-bar-flex-value"},ta={class:"layout-breadcrumb-seting-bar-flex mt15"},aa={class:"layout-breadcrumb-seting-bar-flex-label"},la={class:"layout-breadcrumb-seting-bar-flex-value"},oa={class:"layout-breadcrumb-seting-bar-flex mt15"},sa={class:"layout-breadcrumb-seting-bar-flex-label"},na={class:"layout-breadcrumb-seting-bar-flex-value"},ra={class:"layout-breadcrumb-seting-bar-flex mt15"},ua={class:"layout-breadcrumb-seting-bar-flex-label"},ia={class:"layout-breadcrumb-seting-bar-flex-value"},da={class:"layout-breadcrumb-seting-bar-flex mt15"},ca={class:"layout-breadcrumb-seting-bar-flex-label"},ma={class:"layout-breadcrumb-seting-bar-flex-value"},fa={class:"layout-breadcrumb-seting-bar-flex mt14"},ba={class:"layout-breadcrumb-seting-bar-flex-label"},ya={class:"layout-breadcrumb-seting-bar-flex-value"},va={class:"layout-breadcrumb-seting-bar-flex mt15"},ga={class:"layout-breadcrumb-seting-bar-flex-label"},pa={class:"layout-breadcrumb-seting-bar-flex-value"},ha={class:"layout-breadcrumb-seting-bar-flex mt15"},Ca={class:"layout-breadcrumb-seting-bar-flex-label"},xa={class:"layout-breadcrumb-seting-bar-flex-value"},_a={class:"layout-breadcrumb-seting-bar-flex-label"},wa={class:"layout-breadcrumb-seting-bar-flex-value"},Va={class:"layout-breadcrumb-seting-bar-flex-label"},Ta={class:"layout-breadcrumb-seting-bar-flex-value"},Sa={class:"layout-drawer-content-flex"},$a={class:"layout-tips-box"},Ba={class:"layout-tips-txt"},Ea={class:"layout-tips-box"},Ma={class:"layout-tips-txt"},ka={class:"layout-tips-box"},Ia={class:"layout-tips-txt"},Aa={class:"layout-tips-box"},za={class:"layout-tips-txt"},Ua={class:"copy-config"},La=me({name:"layoutBreadcrumbSeting"}),Da=me({...La,setup($,{expose:B}){const{locale:T}=ce.useI18n(),S=xe(),{themeConfig:g}=Ve(S),{copyText:f}=Le(),{getLightColor:b,getDarkColor:o}=J(),d=Te({isMobile:!1}),e=Se(()=>g.value),c=()=>{if(!e.value.primary)return P.warning("全局主题 primary 颜色值不能为空");document.documentElement.style.setProperty("--el-color-primary-dark-2",`${o(e.value.primary,.1)}`),document.documentElement.style.setProperty("--el-color-primary",e.value.primary);for(let l=1;l<=9;l++)document.documentElement.style.setProperty(`--el-color-primary-light-${l}`,`${b(e.value.primary,l/10)}`);j()},y=l=>{document.documentElement.style.setProperty(`--next-bg-${l}`,g.value[l]),l==="menuBar"&&document.documentElement.style.setProperty("--next-bg-menuBar-light-1",b(e.value.menuBar,.05)),v(),_(),h(),j()},v=()=>{w(".layout-navbars-breadcrumb-index",e.value.isTopBarColorGradual,e.value.topBar)},_=()=>{w(".layout-container .el-aside",e.value.isMenuBarColorGradual,e.value.menuBar)},h=()=>{w(".layout-container .layout-columns-aside",e.value.isColumnsMenuBarColorGradual,e.value.columnsMenuBar)},w=(l,t,n)=>{setTimeout(()=>{let i=document.querySelector(l);if(!i)return!1;document.documentElement.style.setProperty("--el-menu-bg-color",document.documentElement.style.getPropertyValue("--next-bg-menuBar")),t?i.setAttribute("style",`background:linear-gradient(to bottom left , ${n}, ${b(n,.6)}) !important;`):i.setAttribute("style",""),x()},200)},C=()=>{x()},I=()=>{j()},X=()=>{e.value.isFixedHeaderChange=!e.value.isFixedHeader,x()},Q=()=>{e.value.isBreadcrumb=!1,x(),q.emit("getBreadcrumbIndexSetFilterRoutes")},N=()=>{e.value.isShowLogoChange=!e.value.isShowLogo,x()},O=()=>{e.value.layout==="classic"&&(e.value.isClassicSplitMenu=!1),x()},Z=()=>{q.emit("openOrCloseSortable"),x()},ee=()=>{q.emit("openShareTagsView"),x()},U=l=>{l==="grayscale"?e.value.isGrayscale&&(e.value.isInvert=!1):e.value.isInvert&&(e.value.isGrayscale=!1);const t=l==="grayscale"?`grayscale(${e.value.isGrayscale?1:0})`:`invert(${e.value.isInvert?"80%":"0%"})`;document.body.setAttribute("style",`filter: ${t}`),x()},K=()=>{const l=document.documentElement;e.value.isIsDark?l.setAttribute("data-theme","dark"):l.setAttribute("data-theme","")},G=()=>{e.value.isWartermark?re.set(e.value.wartermarkText):re.del(),x()},te=l=>{if(e.value.wartermarkText=we(l),e.value.wartermarkText==="")return!1;e.value.isWartermark&&re.set(e.value.wartermarkText),x()},R=l=>{if(z.set("oldLayout",l),e.value.layout===l)return!1;l==="transverse"&&(e.value.isCollapse=!1),e.value.layout=l,e.value.isDrawer=!1,F()},F=()=>{y("menuBar"),y("menuBarColor"),y("menuBarActiveColor"),y("topBar"),y("topBarColor"),y("columnsMenuBar"),y("columnsMenuBarColor")},ae=()=>{e.value.isFixedHeaderChange=!1,e.value.isShowLogoChange=!1,e.value.isDrawer=!1,x()},le=()=>{e.value.isDrawer=!0},j=()=>{x(),H()},x=()=>{z.remove("themeConfig"),z.set("themeConfig",e.value)},H=()=>{z.set("themeConfigStyle",document.documentElement.style.cssText)},Y=()=>{let l=z.get("themeConfig");l.isDrawer=!1,f(JSON.stringify(l)).then(()=>{e.value.isDrawer=!1})},oe=()=>{z.clear(),window.location.reload(),z.set("version","3.1.0")},se=()=>{v(),_(),h()};return $e(()=>{Be(()=>{z.get("frequency")||F(),z.set("frequency",1),q.on("layoutMobileResize",l=>{e.value.layout=l.layout,e.value.isDrawer=!1,F(),d.isMobile=_e.isMobile()}),setTimeout(()=>{c(),e.value.isGrayscale&&U("grayscale"),e.value.isInvert&&U("invert"),e.value.isIsDark&&K(),G(),z.get("themeConfig")&&(T.value=z.get("themeConfig").globalI18n),se()},100)})}),Ee(()=>{q.off("layoutMobileResize",()=>{})}),B({openDrawer:le}),(l,t)=>{const n=M("el-divider"),i=M("el-color-picker"),r=M("el-switch"),p=M("el-input-number"),k=M("el-input"),E=M("el-option"),W=M("el-select"),be=M("el-alert"),ye=M("ele-CopyDocument"),ue=M("el-icon"),ie=M("el-button"),ve=M("ele-RefreshRight"),ge=M("el-scrollbar"),pe=M("el-drawer");return ke(),Me("div",De,[u(pe,{title:l.$t("message.layout.configTitle"),modelValue:e.value.isDrawer,"onUpdate:modelValue":t[49]||(t[49]=s=>e.value.isDrawer=s),direction:"rtl","destroy-on-close":"",size:"260px",onClose:ae},{default:V(()=>[u(ge,{class:"layout-breadcrumb-seting-bar"},{default:V(()=>[u(n,{"content-position":"left"},{default:V(()=>[L(m(l.$t("message.layout.oneTitle")),1)]),_:1}),a("div",Fe,[t[50]||(t[50]=a("div",{class:"layout-breadcrumb-seting-bar-flex-label"},"primary",-1)),a("div",Pe,[u(i,{modelValue:e.value.primary,"onUpdate:modelValue":t[0]||(t[0]=s=>e.value.primary=s),size:"default",onChange:c},null,8,["modelValue"])])]),a("div",Oe,[a("div",Re,m(l.$t("message.layout.fourIsDark")),1),a("div",He,[u(r,{modelValue:e.value.isIsDark,"onUpdate:modelValue":t[1]||(t[1]=s=>e.value.isIsDark=s),size:"small",onChange:K},null,8,["modelValue"])])]),u(n,{"content-position":"left"},{default:V(()=>[L(m(l.$t("message.layout.twoTopTitle")),1)]),_:1}),a("div",Ne,[a("div",Ge,m(l.$t("message.layout.twoTopBar")),1),a("div",je,[u(i,{modelValue:e.value.topBar,"onUpdate:modelValue":t[2]||(t[2]=s=>e.value.topBar=s),size:"default",onChange:t[3]||(t[3]=s=>y("topBar"))},null,8,["modelValue"])])]),a("div",Ye,[a("div",We,m(l.$t("message.layout.twoTopBarColor")),1),a("div",qe,[u(i,{modelValue:e.value.topBarColor,"onUpdate:modelValue":t[4]||(t[4]=s=>e.value.topBarColor=s),size:"default",onChange:t[5]||(t[5]=s=>y("topBarColor"))},null,8,["modelValue"])])]),a("div",Je,[a("div",Xe,m(l.$t("message.layout.twoIsTopBarColorGradual")),1),a("div",Ke,[u(r,{modelValue:e.value.isTopBarColorGradual,"onUpdate:modelValue":t[6]||(t[6]=s=>e.value.isTopBarColorGradual=s),size:"small",onChange:v},null,8,["modelValue"])])]),u(n,{"content-position":"left"},{default:V(()=>[L(m(l.$t("message.layout.twoMenuTitle")),1)]),_:1}),a("div",Qe,[a("div",Ze,m(l.$t("message.layout.twoMenuBar")),1),a("div",et,[u(i,{modelValue:e.value.menuBar,"onUpdate:modelValue":t[7]||(t[7]=s=>e.value.menuBar=s),size:"default",onChange:t[8]||(t[8]=s=>y("menuBar"))},null,8,["modelValue"])])]),a("div",tt,[a("div",at,m(l.$t("message.layout.twoMenuBarColor")),1),a("div",lt,[u(i,{modelValue:e.value.menuBarColor,"onUpdate:modelValue":t[9]||(t[9]=s=>e.value.menuBarColor=s),size:"default",onChange:t[10]||(t[10]=s=>y("menuBarColor"))},null,8,["modelValue"])])]),a("div",ot,[a("div",st,m(l.$t("message.layout.twoMenuBarActiveColor")),1),a("div",nt,[u(i,{modelValue:e.value.menuBarActiveColor,"onUpdate:modelValue":t[11]||(t[11]=s=>e.value.menuBarActiveColor=s),size:"default","show-alpha":"",onChange:t[12]||(t[12]=s=>y("menuBarActiveColor"))},null,8,["modelValue"])])]),a("div",rt,[a("div",ut,m(l.$t("message.layout.twoIsMenuBarColorGradual")),1),a("div",it,[u(r,{modelValue:e.value.isMenuBarColorGradual,"onUpdate:modelValue":t[13]||(t[13]=s=>e.value.isMenuBarColorGradual=s),size:"small",onChange:_},null,8,["modelValue"])])]),u(n,{"content-position":"left",style:A({opacity:e.value.layout!=="columns"?.5:1})},{default:V(()=>[L(m(l.$t("message.layout.twoColumnsTitle")),1)]),_:1},8,["style"]),a("div",{class:"layout-breadcrumb-seting-bar-flex",style:A({opacity:e.value.layout!=="columns"?.5:1})},[a("div",dt,m(l.$t("message.layout.twoColumnsMenuBar")),1),a("div",ct,[u(i,{modelValue:e.value.columnsMenuBar,"onUpdate:modelValue":t[14]||(t[14]=s=>e.value.columnsMenuBar=s),size:"default",onChange:t[15]||(t[15]=s=>y("columnsMenuBar")),disabled:e.value.layout!=="columns"},null,8,["modelValue","disabled"])])],4),a("div",{class:"layout-breadcrumb-seting-bar-flex",style:A({opacity:e.value.layout!=="columns"?.5:1})},[a("div",mt,m(l.$t("message.layout.twoColumnsMenuBarColor")),1),a("div",ft,[u(i,{modelValue:e.value.columnsMenuBarColor,"onUpdate:modelValue":t[16]||(t[16]=s=>e.value.columnsMenuBarColor=s),size:"default",onChange:t[17]||(t[17]=s=>y("columnsMenuBarColor")),disabled:e.value.layout!=="columns"},null,8,["modelValue","disabled"])])],4),a("div",{class:"layout-breadcrumb-seting-bar-flex mt14",style:A({opacity:e.value.layout!=="columns"?.5:1})},[a("div",bt,m(l.$t("message.layout.twoIsColumnsMenuBarColorGradual")),1),a("div",yt,[u(r,{modelValue:e.value.isColumnsMenuBarColorGradual,"onUpdate:modelValue":t[18]||(t[18]=s=>e.value.isColumnsMenuBarColorGradual=s),size:"small",onChange:h,disabled:e.value.layout!=="columns"},null,8,["modelValue","disabled"])])],4),a("div",{class:"layout-breadcrumb-seting-bar-flex mt14",style:A({opacity:e.value.layout!=="columns"?.5:1})},[a("div",vt,m(l.$t("message.layout.twoIsColumnsMenuHoverPreload")),1),a("div",gt,[u(r,{modelValue:e.value.isColumnsMenuHoverPreload,"onUpdate:modelValue":t[19]||(t[19]=s=>e.value.isColumnsMenuHoverPreload=s),size:"small",onChange:C,disabled:e.value.layout!=="columns"},null,8,["modelValue","disabled"])])],4),u(n,{"content-position":"left"},{default:V(()=>[L(m(l.$t("message.layout.threeTitle")),1)]),_:1}),a("div",{class:"layout-breadcrumb-seting-bar-flex",style:A({opacity:e.value.layout==="transverse"?.5:1})},[a("div",pt,m(l.$t("message.layout.threeIsCollapse")),1),a("div",ht,[u(r,{modelValue:e.value.isCollapse,"onUpdate:modelValue":t[20]||(t[20]=s=>e.value.isCollapse=s),disabled:e.value.layout==="transverse",size:"small",onChange:I},null,8,["modelValue","disabled"])])],4),a("div",{class:"layout-breadcrumb-seting-bar-flex mt15",style:A({opacity:e.value.layout==="transverse"?.5:1})},[a("div",Ct,m(l.$t("message.layout.threeIsUniqueOpened")),1),a("div",xt,[u(r,{modelValue:e.value.isUniqueOpened,"onUpdate:modelValue":t[21]||(t[21]=s=>e.value.isUniqueOpened=s),disabled:e.value.layout==="transverse",size:"small",onChange:x},null,8,["modelValue","disabled"])])],4),a("div",_t,[a("div",wt,m(l.$t("message.layout.threeIsFixedHeader")),1),a("div",Vt,[u(r,{modelValue:e.value.isFixedHeader,"onUpdate:modelValue":t[22]||(t[22]=s=>e.value.isFixedHeader=s),size:"small",onChange:X},null,8,["modelValue"])])]),a("div",{class:"layout-breadcrumb-seting-bar-flex mt15",style:A({opacity:e.value.layout!=="classic"?.5:1})},[a("div",Tt,m(l.$t("message.layout.threeIsClassicSplitMenu")),1),a("div",St,[u(r,{modelValue:e.value.isClassicSplitMenu,"onUpdate:modelValue":t[23]||(t[23]=s=>e.value.isClassicSplitMenu=s),disabled:e.value.layout!=="classic",size:"small",onChange:Q},null,8,["modelValue","disabled"])])],4),a("div",$t,[a("div",Bt,m(l.$t("message.layout.threeIsLockScreen")),1),a("div",Et,[u(r,{modelValue:e.value.isLockScreen,"onUpdate:modelValue":t[24]||(t[24]=s=>e.value.isLockScreen=s),size:"small",onChange:x},null,8,["modelValue"])])]),a("div",Mt,[a("div",kt,m(l.$t("message.layout.threeLockScreenTime")),1),a("div",It,[u(p,{modelValue:e.value.lockScreenTime,"onUpdate:modelValue":t[25]||(t[25]=s=>e.value.lockScreenTime=s),"controls-position":"right",min:1,max:9999,onChange:x,size:"default",style:{width:"90px"}},null,8,["modelValue"])])]),u(n,{"content-position":"left"},{default:V(()=>[L(m(l.$t("message.layout.fourTitle")),1)]),_:1}),a("div",At,[a("div",zt,m(l.$t("message.layout.fourIsShowLogo")),1),a("div",Ut,[u(r,{modelValue:e.value.isShowLogo,"onUpdate:modelValue":t[26]||(t[26]=s=>e.value.isShowLogo=s),size:"small",onChange:N},null,8,["modelValue"])])]),a("div",{class:"layout-breadcrumb-seting-bar-flex mt15",style:A({opacity:e.value.layout==="classic"||e.value.layout==="transverse"?.5:1})},[a("div",Lt,m(l.$t("message.layout.fourIsBreadcrumb")),1),a("div",Dt,[u(r,{modelValue:e.value.isBreadcrumb,"onUpdate:modelValue":t[27]||(t[27]=s=>e.value.isBreadcrumb=s),disabled:e.value.layout==="classic"||e.value.layout==="transverse",size:"small",onChange:O},null,8,["modelValue","disabled"])])],4),a("div",Ft,[a("div",Pt,m(l.$t("message.layout.fourIsBreadcrumbIcon")),1),a("div",Ot,[u(r,{modelValue:e.value.isBreadcrumbIcon,"onUpdate:modelValue":t[28]||(t[28]=s=>e.value.isBreadcrumbIcon=s),size:"small",onChange:x},null,8,["modelValue"])])]),a("div",Rt,[a("div",Ht,m(l.$t("message.layout.fourIsTagsview")),1),a("div",Nt,[u(r,{modelValue:e.value.isTagsview,"onUpdate:modelValue":t[29]||(t[29]=s=>e.value.isTagsview=s),size:"small",onChange:x},null,8,["modelValue"])])]),a("div",Gt,[a("div",jt,m(l.$t("message.layout.fourIsTagsviewIcon")),1),a("div",Yt,[u(r,{modelValue:e.value.isTagsviewIcon,"onUpdate:modelValue":t[30]||(t[30]=s=>e.value.isTagsviewIcon=s),size:"small",onChange:x},null,8,["modelValue"])])]),a("div",Wt,[a("div",qt,m(l.$t("message.layout.fourIsCacheTagsView")),1),a("div",Jt,[u(r,{modelValue:e.value.isCacheTagsView,"onUpdate:modelValue":t[31]||(t[31]=s=>e.value.isCacheTagsView=s),size:"small",onChange:x},null,8,["modelValue"])])]),a("div",{class:"layout-breadcrumb-seting-bar-flex mt15",style:A({opacity:d.isMobile?.5:1})},[a("div",Xt,m(l.$t("message.layout.fourIsSortableTagsView")),1),a("div",Kt,[u(r,{modelValue:e.value.isSortableTagsView,"onUpdate:modelValue":t[32]||(t[32]=s=>e.value.isSortableTagsView=s),disabled:!!d.isMobile,size:"small",onChange:Z},null,8,["modelValue","disabled"])])],4),a("div",Qt,[a("div",Zt,m(l.$t("message.layout.fourIsShareTagsView")),1),a("div",ea,[u(r,{modelValue:e.value.isShareTagsView,"onUpdate:modelValue":t[33]||(t[33]=s=>e.value.isShareTagsView=s),size:"small",onChange:ee},null,8,["modelValue"])])]),a("div",ta,[a("div",aa,m(l.$t("message.layout.fourIsFooter")),1),a("div",la,[u(r,{modelValue:e.value.isFooter,"onUpdate:modelValue":t[34]||(t[34]=s=>e.value.isFooter=s),size:"small",onChange:x},null,8,["modelValue"])])]),a("div",oa,[a("div",sa,m(l.$t("message.layout.fourIsGrayscale")),1),a("div",na,[u(r,{modelValue:e.value.isGrayscale,"onUpdate:modelValue":t[35]||(t[35]=s=>e.value.isGrayscale=s),size:"small",onChange:t[36]||(t[36]=s=>U("grayscale"))},null,8,["modelValue"])])]),a("div",ra,[a("div",ua,m(l.$t("message.layout.fourIsInvert")),1),a("div",ia,[u(r,{modelValue:e.value.isInvert,"onUpdate:modelValue":t[37]||(t[37]=s=>e.value.isInvert=s),size:"small",onChange:t[38]||(t[38]=s=>U("invert"))},null,8,["modelValue"])])]),a("div",da,[a("div",ca,m(l.$t("message.layout.fourIsWartermark")),1),a("div",ma,[u(r,{modelValue:e.value.isWartermark,"onUpdate:modelValue":t[39]||(t[39]=s=>e.value.isWartermark=s),size:"small",onChange:G},null,8,["modelValue"])])]),a("div",fa,[a("div",ba,m(l.$t("message.layout.fourWartermarkText")),1),a("div",ya,[u(k,{modelValue:e.value.wartermarkText,"onUpdate:modelValue":t[40]||(t[40]=s=>e.value.wartermarkText=s),size:"default",style:{width:"90px"},onInput:te},null,8,["modelValue"])])]),u(n,{"content-position":"left"},{default:V(()=>[L(m(l.$t("message.layout.fiveTitle")),1)]),_:1}),a("div",va,[a("div",ga,m(l.$t("message.layout.fiveTagsStyle")),1),a("div",pa,[u(W,{modelValue:e.value.tagsStyle,"onUpdate:modelValue":t[41]||(t[41]=s=>e.value.tagsStyle=s),placeholder:"请选择",size:"default",style:{width:"90px"},onChange:x},{default:V(()=>[u(E,{label:"风格1",value:"tags-style-one"}),u(E,{label:"风格4",value:"tags-style-four"}),u(E,{label:"风格5",value:"tags-style-five"})]),_:1},8,["modelValue"])])]),a("div",ha,[a("div",Ca,m(l.$t("message.layout.fiveAnimation")),1),a("div",xa,[u(W,{modelValue:e.value.animation,"onUpdate:modelValue":t[42]||(t[42]=s=>e.value.animation=s),placeholder:"请选择",size:"default",style:{width:"90px"},onChange:x},{default:V(()=>[u(E,{label:"slide-right",value:"slide-right"}),u(E,{label:"slide-left",value:"slide-left"}),u(E,{label:"opacitys",value:"opacitys"})]),_:1},8,["modelValue"])])]),a("div",{class:"layout-breadcrumb-seting-bar-flex mt15",style:A({opacity:e.value.layout!=="columns"?.5:1})},[a("div",_a,m(l.$t("message.layout.fiveColumnsAsideStyle")),1),a("div",wa,[u(W,{modelValue:e.value.columnsAsideStyle,"onUpdate:modelValue":t[43]||(t[43]=s=>e.value.columnsAsideStyle=s),placeholder:"请选择",size:"default",style:{width:"90px"},disabled:e.value.layout!=="columns",onChange:x},{default:V(()=>[u(E,{label:"圆角",value:"columns-round"}),u(E,{label:"卡片",value:"columns-card"})]),_:1},8,["modelValue","disabled"])])],4),a("div",{class:"layout-breadcrumb-seting-bar-flex mt15 mb27",style:A({opacity:e.value.layout!=="columns"?.5:1})},[a("div",Va,m(l.$t("message.layout.fiveColumnsAsideLayout")),1),a("div",Ta,[u(W,{modelValue:e.value.columnsAsideLayout,"onUpdate:modelValue":t[44]||(t[44]=s=>e.value.columnsAsideLayout=s),placeholder:"请选择",size:"default",style:{width:"90px"},disabled:e.value.layout!=="columns",onChange:x},{default:V(()=>[u(E,{label:"水平",value:"columns-horizontal"}),u(E,{label:"垂直",value:"columns-vertical"})]),_:1},8,["modelValue","disabled"])])],4),u(n,{"content-position":"left"},{default:V(()=>[L(m(l.$t("message.layout.sixTitle")),1)]),_:1}),a("div",Sa,[a("div",{class:"layout-drawer-content-item",onClick:t[45]||(t[45]=s=>R("defaults"))},[a("section",{class:D(["el-container el-circular",{"drawer-layout-active":e.value.layout==="defaults"}])},t[51]||(t[51]=[a("aside",{class:"el-aside",style:{width:"20px"}},null,-1),a("section",{class:"el-container is-vertical"},[a("header",{class:"el-header",style:{height:"10px"}}),a("main",{class:"el-main"})],-1)]),2),a("div",{class:D(["layout-tips-warp",{"layout-tips-warp-active":e.value.layout==="defaults"}])},[a("div",$a,[a("p",Ba,m(l.$t("message.layout.sixDefaults")),1)])],2)]),a("div",{class:"layout-drawer-content-item",onClick:t[46]||(t[46]=s=>R("classic"))},[a("section",{class:D(["el-container is-vertical el-circular",{"drawer-layout-active":e.value.layout==="classic"}])},t[52]||(t[52]=[a("header",{class:"el-header",style:{height:"10px"}},null,-1),a("section",{class:"el-container"},[a("aside",{class:"el-aside",style:{width:"20px"}}),a("section",{class:"el-container is-vertical"},[a("main",{class:"el-main"})])],-1)]),2),a("div",{class:D(["layout-tips-warp",{"layout-tips-warp-active":e.value.layout==="classic"}])},[a("div",Ea,[a("p",Ma,m(l.$t("message.layout.sixClassic")),1)])],2)]),a("div",{class:"layout-drawer-content-item",onClick:t[47]||(t[47]=s=>R("transverse"))},[a("section",{class:D(["el-container is-vertical el-circular",{"drawer-layout-active":e.value.layout==="transverse"}])},t[53]||(t[53]=[a("header",{class:"el-header",style:{height:"10px"}},null,-1),a("section",{class:"el-container"},[a("section",{class:"el-container is-vertical"},[a("main",{class:"el-main"})])],-1)]),2),a("div",{class:D(["layout-tips-warp",{"layout-tips-warp-active":e.value.layout==="transverse"}])},[a("div",ka,[a("p",Ia,m(l.$t("message.layout.sixTransverse")),1)])],2)]),a("div",{class:"layout-drawer-content-item",onClick:t[48]||(t[48]=s=>R("columns"))},[a("section",{class:D(["el-container el-circular",{"drawer-layout-active":e.value.layout==="columns"}])},t[54]||(t[54]=[a("aside",{class:"el-aside-dark",style:{width:"10px"}},null,-1),a("aside",{class:"el-aside",style:{width:"20px"}},null,-1),a("section",{class:"el-container is-vertical"},[a("header",{class:"el-header",style:{height:"10px"}}),a("main",{class:"el-main"})],-1)]),2),a("div",{class:D(["layout-tips-warp",{"layout-tips-warp-active":e.value.layout==="columns"}])},[a("div",Aa,[a("p",za,m(l.$t("message.layout.sixColumns")),1)])],2)])]),a("div",Ua,[u(be,{title:l.$t("message.layout.tipText"),type:"warning",closable:!1},null,8,["title"]),u(ie,{size:"default",class:"copy-config-btn",type:"primary",ref:"copyConfigBtnRef",onClick:Y},{default:V(()=>[u(ue,{class:"mr5"},{default:V(()=>[u(ye)]),_:1}),L(" "+m(l.$t("message.layout.copyText")),1)]),_:1},512),u(ie,{size:"default",class:"copy-config-btn-reset",type:"info",onClick:oe},{default:V(()=>[u(ue,{class:"mr5"},{default:V(()=>[u(ve)]),_:1}),L(" "+m(l.$t("message.layout.resetText")),1)]),_:1})])]),_:1})]),_:1},8,["title","modelValue"])])}}}),Ha=Ie(Da,[["__scopeId","data-v-fd1c7594"]]);export{Ha as default};
