{"version": 3, "sources": ["../../.pnpm/@fast-crud+fast-extends@1.2_48d83ac4990201ef9a8c3e5855a79123/node_modules/@fast-crud/src/input/components/fs-phone-input/phoneCodeCountries.ts"], "sourcesContent": [null], "mappings": ";;;AAAA,IAAMA,IAAwB;EAC5B,CAAC,8BAA8B,MAAM,IAAI;EACzC,CAAC,sBAAsB,MAAM,KAAK;EAClC,CAAC,wBAAwB,MAAM,KAAK;EACpC,CAAC,kBAAkB,MAAM,MAAM;EAC/B,CAAC,WAAW,MAAM,KAAK;EACvB,CAAC,UAAU,MAAM,KAAK;EACtB,CAAC,YAAY,MAAM,MAAM;EACzB,CAAC,uBAAuB,MAAM,MAAM;EACpC,CAAC,aAAa,MAAM,IAAI;EACxB,CAAC,sBAAsB,MAAM,KAAK;EAClC,CAAC,SAAS,MAAM,KAAK;EACrB,CAAC,aAAa,MAAM,MAAM,CAAC;EAC3B,CAAC,wBAAwB,MAAM,IAAI;EACnC,CAAC,2BAA2B,MAAM,KAAK;EACvC,CAAC,WAAW,MAAM,MAAM;EACxB,CAAC,wBAAwB,MAAM,KAAK;EACpC,CAAC,yBAAyB,MAAM,KAAK;EACrC,CAAC,YAAY,MAAM,MAAM;EACzB,CAAC,sBAAsB,MAAM,KAAK;EAClC,CAAC,oBAAoB,MAAM,IAAI;EAC/B,CAAC,UAAU,MAAM,KAAK;EACtB,CAAC,iBAAiB,MAAM,KAAK;EAC7B,CAAC,WAAW,MAAM,MAAM;EACxB,CAAC,kBAAkB,MAAM,KAAK;EAC9B,CAAC,WAAW,MAAM,KAAK;EACvB,CAAC,gDAAgD,MAAM,KAAK;EAC5D,CAAC,YAAY,MAAM,KAAK;EACxB,CAAC,mBAAmB,MAAM,IAAI;EAC9B,CAAC,kCAAkC,MAAM,KAAK;EAC9C,CAAC,0BAA0B,MAAM,MAAM;EACvC,CAAC,UAAU,MAAM,KAAK;EACtB,CAAC,uBAAuB,MAAM,KAAK;EACnC,CAAC,gBAAgB,MAAM,KAAK;EAC5B,CAAC,sBAAsB,MAAM,KAAK;EAClC,CAAC,sBAAsB,MAAM,KAAK;EAClC,CAAC,uBAAuB,MAAM,KAAK;EACnC;IACE;IACA;IACA;IACA;IACA;MACE;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACD;EACF;EACD,CAAC,2BAA2B,MAAM,KAAK;EACvC,CAAC,yBAAyB,MAAM,OAAO,CAAC;EACxC,CAAC,kBAAkB,MAAM,MAAM;EAC/B,CAAC,wDAAwD,MAAM,KAAK;EACpE,CAAC,gBAAgB,MAAM,KAAK;EAC5B,CAAC,SAAS,MAAM,IAAI;EACpB,CAAC,cAAc,MAAM,IAAI;EACzB,CAAC,oBAAoB,MAAM,MAAM,CAAC;EAClC,CAAC,2BAA2B,MAAM,MAAM,CAAC;EACzC,CAAC,YAAY,MAAM,IAAI;EACvB,CAAC,0BAA0B,MAAM,KAAK;EACtC,CAAC,kDAAkD,MAAM,KAAK;EAC9D,CAAC,wCAAwC,MAAM,KAAK;EACpD,CAAC,gBAAgB,MAAM,KAAK;EAC5B,CAAC,cAAc,MAAM,KAAK;EAC1B,CAAC,iBAAiB,MAAM,KAAK;EAC7B,CAAC,sBAAsB,MAAM,KAAK;EAClC,CAAC,QAAQ,MAAM,IAAI;EACnB,CAAC,WAAW,MAAM,OAAO,CAAC;EAC1B,CAAC,mBAAmB,MAAM,KAAK;EAC/B,CAAC,oCAAoC,MAAM,KAAK;EAChD,CAAC,qBAAqB,MAAM,IAAI;EAChC,CAAC,YAAY,MAAM,KAAK;EACxB,CAAC,YAAY,MAAM,MAAM;EACzB,CAAC,6CAA6C,MAAM,KAAK,GAAG,CAAC,OAAO,OAAO,KAAK,CAAC;EACjF,CAAC,WAAW,MAAM,KAAK;EACvB,CAAC,kBAAkB,MAAM,IAAI;EAC7B,CAAC,eAAe,MAAM,KAAK;EAC3B,CAAC,yCAAyC,MAAM,KAAK;EACrD,CAAC,WAAW,MAAM,KAAK;EACvB,CAAC,mBAAmB,MAAM,KAAK;EAC/B,CAAC,YAAY,MAAM,KAAK;EACxB,CAAC,qCAAqC,MAAM,KAAK;EACjD,CAAC,2BAA2B,MAAM,KAAK;EACvC,CAAC,QAAQ,MAAM,KAAK;EACpB,CAAC,mBAAmB,MAAM,OAAO,CAAC;EAClC,CAAC,UAAU,MAAM,IAAI;EACrB,CAAC,oCAAoC,MAAM,KAAK;EAChD,CAAC,0CAA0C,MAAM,KAAK;EACtD,CAAC,SAAS,MAAM,KAAK;EACrB,CAAC,UAAU,MAAM,KAAK;EACtB,CAAC,wBAAwB,MAAM,KAAK;EACpC,CAAC,yBAAyB,MAAM,IAAI;EACpC,CAAC,iBAAiB,MAAM,KAAK;EAC7B,CAAC,aAAa,MAAM,KAAK;EACzB,CAAC,mBAAmB,MAAM,IAAI;EAC9B,CAAC,gCAAgC,MAAM,KAAK;EAC5C,CAAC,WAAW,MAAM,MAAM;EACxB,CAAC,cAAc,MAAM,OAAO,CAAC;EAC7B,CAAC,QAAQ,MAAM,MAAM;EACrB,CAAC,aAAa,MAAM,KAAK;EACzB,CAAC,YAAY,MAAM,MAAM,CAAC;EAC1B,CAAC,mBAAmB,MAAM,KAAK;EAC/B,CAAC,gCAAgC,MAAM,KAAK;EAC5C,CAAC,UAAU,MAAM,KAAK;EACtB,CAAC,SAAS,MAAM,KAAK;EACrB,CAAC,YAAY,MAAM,KAAK;EACxB,CAAC,kBAAkB,MAAM,KAAK;EAC9B,CAAC,0BAA0B,MAAM,IAAI;EACrC,CAAC,oBAAoB,MAAM,KAAK;EAChC,CAAC,gBAAgB,MAAM,IAAI;EAC3B,CAAC,aAAa,MAAM,IAAI;EACxB,CAAC,mBAAmB,MAAM,IAAI;EAC9B,CAAC,oBAAoB,MAAM,KAAK;EAChC,CAAC,WAAW,MAAM,KAAK;EACvB,CAAC,eAAe,MAAM,MAAM,CAAC;EAC7B,CAAC,qBAAqB,MAAM,KAAK;EACjC,CAAC,kBAAkB,MAAM,MAAM,CAAC;EAChC,CAAC,WAAW,MAAM,MAAM;EACxB,CAAC,cAAc,MAAM,IAAI;EACzB,CAAC,UAAU,MAAM,MAAM,CAAC;EACxB,CAAC,sBAAsB,MAAM,KAAK;EAClC,CAAC,0BAA0B,MAAM,KAAK,CAAC;EACvC,CAAC,SAAS,MAAM,KAAK;EACrB,CAAC,YAAY,MAAM,KAAK;EACxB,CAAC,UAAU,MAAM,KAAK;EACtB,CAAC,sBAAsB,MAAM,KAAK;EAClC,CAAC,2BAA2B,MAAM,KAAK;EACvC,CAAC,cAAc,MAAM,KAAK;EAC1B,CAAC,oBAAoB,MAAM,KAAK;EAChC,CAAC,sBAAsB,MAAM,KAAK;EAClC,CAAC,WAAW,MAAM,KAAK;EACvB,CAAC,WAAW,MAAM,KAAK;EACvB,CAAC,oBAAoB,MAAM,KAAK;EAChC,CAAC,iBAAiB,MAAM,KAAK;EAC7B,CAAC,uBAAuB,MAAM,KAAK;EACnC,CAAC,cAAc,MAAM,KAAK;EAC1B,CAAC,cAAc,MAAM,KAAK;EAC1B,CAAC,kCAAkC,MAAM,KAAK;EAC9C,CAAC,6BAA6B,MAAM,KAAK;EACzC,CAAC,UAAU,MAAM,KAAK;EACtB,CAAC,YAAY,MAAM,IAAI;EACvB,CAAC,YAAY,MAAM,KAAK;EACxB,CAAC,QAAQ,MAAM,KAAK;EACpB,CAAC,SAAS,MAAM,KAAK;EACrB,CAAC,oBAAoB,MAAM,KAAK;EAChC,CAAC,cAAc,MAAM,KAAK;EAC1B,CAAC,6BAA6B,MAAM,KAAK;EACzC,CAAC,qBAAqB,MAAM,KAAK;EACjC,CAAC,WAAW,MAAM,OAAO,CAAC;EAC1B,CAAC,mBAAmB,MAAM,IAAI;EAC9B,CAAC,cAAc,MAAM,KAAK;EAC1B,CAAC,+BAA+B,MAAM,KAAK;EAC3C,CAAC,UAAU,MAAM,KAAK;EACtB,CAAC,qBAAqB,MAAM,KAAK;EACjC,CAAC,0BAA0B,MAAM,KAAK;EACtC,CAAC,cAAc,MAAM,MAAM;EAC3B,CAAC,uBAAuB,MAAM,OAAO,CAAC;EACtC,CAAC,2BAA2B,MAAM,KAAK;EACvC,CAAC,4BAA4B,MAAM,IAAI;EACvC,CAAC,qBAAqB,MAAM,KAAK;EACjC,CAAC,SAAS,MAAM,KAAK;EACrB,CAAC,iBAAiB,MAAM,KAAK;EAC7B,CAAC,2BAA2B,MAAM,IAAI;EACtC,CAAC,sCAAsC,MAAM,KAAK;EAClD,CAAC,eAAe,MAAM,IAAI;EAC1B,CAAC,aAAa,MAAM,KAAK;EACzB,CAAC,iBAAiB,MAAM,KAAK;EAC7B,CAAC,WAAW,MAAM,KAAK;EACvB,CAAC,QAAQ,MAAM,KAAK;EACpB,CAAC,kBAAkB,MAAM,KAAK;EAC9B,CAAC,gCAAgC,MAAM,KAAK;EAC5C,CAAC,4BAA4B,MAAM,MAAM;EACzC,CAAC,kBAAkB,MAAM,MAAM,CAAC;EAChC,CAAC,mBAAmB,MAAM,KAAK;EAC/B,CAAC,yBAAyB,MAAM,IAAI;EACpC,CAAC,SAAS,MAAM,KAAK;EACrB,CAAC,yBAAyB,MAAM,KAAK;EACrC,CAAC,mBAAmB,MAAM,KAAK;EAC/B,CAAC,oBAAoB,MAAM,KAAK;EAChC,CAAC,YAAY,MAAM,KAAK;EACxB,CAAC,eAAe,MAAM,IAAI;EAC1B,CAAC,eAAe,MAAM,IAAI;EAC1B,CAAC,mBAAmB,MAAM,IAAI;EAC9B,CAAC,YAAY,MAAM,KAAK;EACxB,CAAC,eAAe,MAAM,KAAK,GAAG,CAAC,OAAO,KAAK,CAAC;EAC5C,CAAC,kBAAkB,MAAM,KAAK;EAC9B,CAAC,wBAAwB,MAAM,OAAO,CAAC;EACvC,CAAC,qBAAqB,MAAM,IAAI;EAChC,CAAC,mBAAmB,MAAM,KAAK,CAAC;EAChC,CAAC,UAAU,MAAM,KAAK;EACtB,CAAC,oBAAoB,MAAM,OAAO,CAAC;EACnC,CAAC,gBAAgB,MAAM,KAAK;EAC5B,CAAC,yBAAyB,MAAM,MAAM;EACtC,CAAC,eAAe,MAAM,MAAM;EAC5B,CAAC,kDAAkD,MAAM,OAAO,CAAC;EACjE,CAAC,wDAAwD,MAAM,KAAK;EACpE,CAAC,oCAAoC,MAAM,MAAM;EACjD,CAAC,SAAS,MAAM,KAAK;EACrB,CAAC,cAAc,MAAM,KAAK;EAC1B,CAAC,+CAA+C,MAAM,KAAK;EAC3D,CAAC,8CAA8C,MAAM,KAAK;EAC1D,CAAC,qBAAqB,MAAM,KAAK;EACjC,CAAC,mBAAmB,MAAM,KAAK;EAC/B,CAAC,cAAc,MAAM,KAAK;EAC1B,CAAC,gBAAgB,MAAM,KAAK;EAC5B,CAAC,aAAa,MAAM,IAAI;EACxB,CAAC,gBAAgB,MAAM,MAAM;EAC7B,CAAC,wBAAwB,MAAM,KAAK;EACpC,CAAC,wBAAwB,MAAM,KAAK;EACpC,CAAC,mBAAmB,MAAM,KAAK;EAC/B,CAAC,wBAAwB,MAAM,KAAK;EACpC,CAAC,gBAAgB,MAAM,IAAI;EAC3B,CAAC,sBAAsB,MAAM,IAAI;EACjC,CAAC,iCAAiC,MAAM,KAAK;EAC7C,CAAC,kBAAkB,MAAM,IAAI;EAC7B,CAAC,2BAA2B,MAAM,IAAI;EACtC,CAAC,sBAAsB,MAAM,KAAK;EAClC,CAAC,YAAY,MAAM,KAAK;EACxB,CAAC,0BAA0B,MAAM,MAAM,CAAC;EACxC,CAAC,aAAa,MAAM,KAAK;EACzB,CAAC,oBAAoB,MAAM,IAAI;EAC/B,CAAC,yBAAyB,MAAM,IAAI;EACpC,CAAC,oBAAoB,MAAM,KAAK;EAChC,CAAC,eAAe,MAAM,KAAK;EAC3B,CAAC,cAAc,MAAM,KAAK;EAC1B,CAAC,YAAY,MAAM,KAAK;EACxB,CAAC,kBAAkB,MAAM,IAAI;EAC7B,CAAC,eAAe,MAAM,KAAK;EAC3B,CAAC,QAAQ,MAAM,KAAK;EACpB,CAAC,WAAW,MAAM,KAAK;EACvB,CAAC,SAAS,MAAM,KAAK;EACrB,CAAC,uBAAuB,MAAM,MAAM;EACpC,CAAC,qBAAqB,MAAM,KAAK;EACjC,CAAC,oBAAoB,MAAM,IAAI;EAC/B,CAAC,gBAAgB,MAAM,KAAK;EAC5B,CAAC,4BAA4B,MAAM,MAAM;EACzC,CAAC,UAAU,MAAM,KAAK;EACtB,CAAC,uBAAuB,MAAM,MAAM;EACpC,CAAC,UAAU,MAAM,KAAK;EACtB,CAAC,qBAAqB,MAAM,KAAK;EACjC,CAAC,sDAAsD,MAAM,KAAK;EAClE,CAAC,kBAAkB,MAAM,MAAM,CAAC;EAChC,CAAC,iBAAiB,MAAM,KAAK,CAAC;EAC9B,CAAC,WAAW,MAAM,KAAK;EACvB,CAAC,4BAA4B,MAAM,KAAK;EACxC,CAAC,WAAW,MAAM,KAAK;EACvB,CAAC,qCAAqC,MAAM,MAAM,CAAC;EACnD,CAAC,aAAa,MAAM,IAAI;EACxB,CAAC,sBAAsB,MAAM,IAAI;EACjC,CAAC,wCAAwC,MAAM,KAAK;EACpD,CAAC,uCAAuC,MAAM,OAAO,CAAC;EACtD,CAAC,oBAAoB,MAAM,KAAK;EAChC,CAAC,UAAU,MAAM,KAAK;EACtB,CAAC,YAAY,MAAM,KAAK;EACxB,CAAC,iBAAiB,MAAM,OAAO,CAAC;;AApSlC,IAuSaC,IAAeD,EAAa,IAAI,CAACE,MAAYA,EAAQ,CAAC,EAAE,YAAA,CAAa;AAvSlF,IAySaC,IAAYH,EAAa,IAAI,CAACE,OAAa;EACtD,MAAMA,EAAQ,CAAC;EACf,MAAMA,EAAQ,CAAC,EAAE,YAAa;EAC9B,UAAUA,EAAQ,CAAC;EACnB,UAAUA,EAAQ,CAAC,KAAK;EACxB,WAAWA,EAAQ,CAAC,KAAK;AAC1B,EAAC;", "names": ["allCountries", "countriesIso", "country", "countries"]}