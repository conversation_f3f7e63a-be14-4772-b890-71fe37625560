const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/jsonMode.BWQ6L-jv.js","assets/editor.api.AfgADwdP.js","assets/index.BHZI5pdK.js","assets/vue.BNx9QYep.js","assets/index.Dg-OhEXY.css","assets/editor.BaEgnEXM.css"])))=>i.map(i=>d[i]);
import{_ as g}from"./index.BHZI5pdK.js";import{m as u}from"./editor.api.AfgADwdP.js";import"./vue.BNx9QYep.js";/*!-----------------------------------------------------------------------------
* Copyright (c) Microsoft Corporation. All rights reserved.
* Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)
* Released under the MIT license
* https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
*-----------------------------------------------------------------------------*/var c=Object.defineProperty,l=Object.getOwnPropertyDescriptor,d=Object.getOwnPropertyNames,_=Object.prototype.hasOwnProperty,m=(e,t,r,a)=>{if(t&&typeof t=="object"||typeof t=="function")for(let n of d(t))!_.call(e,n)&&n!==r&&c(e,n,{get:()=>t[n],enumerable:!(a=l(t,n))||a.enumerable});return e},p=(e,t,r)=>(m(e,t,"default"),r),o={};p(o,u);var h=class{constructor(e,t,r){this._onDidChange=new o.Emitter,this._languageId=e,this.setDiagnosticsOptions(t),this.setModeConfiguration(r)}get onDidChange(){return this._onDidChange.event}get languageId(){return this._languageId}get modeConfiguration(){return this._modeConfiguration}get diagnosticsOptions(){return this._diagnosticsOptions}setDiagnosticsOptions(e){this._diagnosticsOptions=e||Object.create(null),this._onDidChange.fire(this)}setModeConfiguration(e){this._modeConfiguration=e||Object.create(null),this._onDidChange.fire(this)}},f={validate:!0,allowComments:!0,schemas:[],enableSchemaRequest:!1,schemaRequest:"warning",schemaValidation:"warning",comments:"error",trailingCommas:"error"},O={documentFormattingEdits:!0,documentRangeFormattingEdits:!0,completionItems:!0,hovers:!0,documentSymbols:!0,tokens:!0,colors:!0,foldingRanges:!0,diagnostics:!0,selectionRanges:!0},s=new h("json",f,O),v=()=>i().then(e=>e.getWorker());o.languages.json={jsonDefaults:s,getWorker:v};function i(){return g(()=>import("./jsonMode.BWQ6L-jv.js"),__vite__mapDeps([0,1,2,3,4,5]))}o.languages.register({id:"json",extensions:[".json",".bowerrc",".jshintrc",".jscsrc",".eslintrc",".babelrc",".har"],aliases:["JSON","json"],mimetypes:["application/json"]});o.languages.onLanguage("json",()=>{i().then(e=>e.setupMode(s))});export{v as getWorker,s as jsonDefaults};
