function p(d){return{name:"element-tree-line",props:{node:{type:Object,required:!0},data:{type:Object},treeData:{type:Array},indent:{type:Number,default(){return 16}},showLabelLine:{type:Boolean,default:!0}},render(n){const l=d||n,t=this.getScopedSlot("default"),s=this.getScopedSlot("node-label"),r=this.getScopedSlot("after-node-label"),i=t?this.getScopedSlotValue(t,{node:this.node,data:this.data}):[s?this.getScopedSlotValue(s,{node:this.node,data:this.data}):l("span",{class:"element-tree-node-label"},this.node.label),this.showLabelLine?l("span",{class:"element-tree-node-label-line"}):null,this.getScopedSlotValue(r,{node:this.node,data:this.data})],a=[];let o=this.node;for(;o;){let e=o.parent;if(o.level===1&&!o.parent){if(!this.treeData||!Array.isArray(this.treeData))throw Error("if you using el-tree-v2 (Virtualized Tree) of element-plus,element-tree-line required data.");e={children:Array.isArray(this.treeData)?this.treeData.map(c=>({...c,key:c.id})):[],level:0,key:"node-0",parent:null}}if(e){const c=(e.children||e.childNodes).findIndex(u=>(u.key||u.id)===(o.key||o.id));a.unshift(c===(e.children||e.childNodes).length-1)}o=e}const h=[];for(let e=0;e<this.node.level;e++)a[e]&&this.node.level-1!==e||h.push(l("span",{class:{"element-tree-node-line-ver":!0,"last-node-isLeaf-line":a[e]&&this.node.level-1===e},style:{left:this.indent*e+"px"}}));return l("span",{class:"element-tree-node-label-wrapper"},[i].concat(h).concat([l("span",{class:"element-tree-node-line-hor",style:{width:(this.node.isLeaf?24:8)+"px",left:(this.node.level-1)*this.indent+"px"}})]))},methods:{getScopedSlot(n){if(!n)return null;const l=n.split("||");let t=null;for(let s=0;s<l.length;s++){const r=l[s],i=(this.$slots||{})[r];if(i){t=i;break}if(t=(this.$scopedSlots||{})[r],t)break}return t},getScopedSlotValue(n,l,t=null){return typeof n=="function"?n(l)||t:n||t}}}}function f(d){const n=p(d);return d&&(n.methods.getScopedSlot=function(t){if(!t)return null;const s=t.split("||");let r=null;for(let i=0;i<s.length;i++){const a=s[i],o=(this.$slots||{})[a];if(o){r=o;break}}return r}),n}f();export{f as g};
