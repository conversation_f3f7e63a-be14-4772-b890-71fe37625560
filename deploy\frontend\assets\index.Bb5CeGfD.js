import{d as J,h as u,v as K,k as a,a as w,o as C,l as t,e as v,u as r,$ as E,w as l,A as x,D as N,q as S,s as g,F as Q,E as W,t as X}from"./vue.BNx9QYep.js";import{g as Y}from"./index.es.DmevZXPX.js";import{ai as Z,ab as ee,a1 as z}from"./index.BHZI5pdK.js";import{d as te,a as oe,l as le}from"./api.B8LmnnXE.js";import{_ as ne}from"./_plugin-vue_export-helper.DlAUqK2U.js";const ae={class:"dept-tree-com"},se={class:"tc-head"},ie={key:0,class:"text-center font-black font-normal"},de={key:1,color:"var(--el-color-primary)"},re={class:"tree-tags"},ce=J({__name:"index",props:{treeData:{default:()=>[]}},emits:["treeClick","deleteDept","updateDept"],setup(ue,{expose:B,emit:F}){const H=Y(W),I={children:"children",label:"name",isLeaf:(o,e)=>!e.data.hasChild},h=F;let p=u(""),c=u(!1),f=u(!1),s=u({}),L=u(null);const D=u();K(p,o=>{D.value.filter(o)});const R=(o,e)=>{var n;return o?((n=X(e).name)==null?void 0:n.indexOf(o))!==-1:!0},$=(o,e)=>{o.level!==0&&le({parent:o.data.id}).then(n=>{e(n.data)})},M=(o,e)=>{s.value=o,L.value=e,h("treeClick",o)},T=o=>{if(o==="update"){if(!s.value.id){z("请选择菜单！");return}h("updateDept",o,s.value)}else h("updateDept",o)},P=()=>{if(!s.value.id){z("请选择菜单！");return}h("deleteDept",s.value.id,()=>{s.value={}})},V=async o=>{var i;if(!s.value.id){z("请选择菜单！");return}if(f.value)return;const e=((i=L.value)==null?void 0:i.parent.childNodes)||[],n=e.findIndex(m=>m.data.id===s.value.id),k=e.find(m=>m.data.id===s.value.id);if(o==="up"){if(n===0)return;e.splice(n-1,0,k),e.splice(n+1,1),f.value=!0,await te({dept_id:s.value.id}),f.value=!1}o==="down"&&(e.splice(n+2,0,k),e.splice(n,1),f.value=!0,await oe({dept_id:s.value.id}),f.value=!1)};return B({treeRef:D}),(o,e)=>{const n=a("el-input"),k=a("HomeFilled"),i=a("el-icon"),m=a("View"),U=a("Hide"),b=a("SvgIcon"),q=a("Plus"),_=a("el-tooltip"),A=a("Edit"),O=a("Top"),j=a("Bottom"),G=a("Delete");return C(),w(Q,null,[t(n,{modelValue:r(p),"onUpdate:modelValue":e[0]||(e[0]=d=>E(p)?p.value=d:p=d),"prefix-icon":r(Z),placeholder:"请输入部门名称"},null,8,["modelValue","prefix-icon"]),v("div",ae,[v("div",se,[t(i,{size:"16",color:"#606266",class:"tc-head-icon"},{default:l(()=>[t(k)]),_:1}),e[6]||(e[6]=v("span",{class:"tc-head-txt"},"部门架构",-1)),t(i,{size:"16",color:"#606266",onClick:e[1]||(e[1]=()=>E(c)?c.value=!r(c):c=!r(c)),class:"tc-head-icon"},{default:l(()=>[x(t(m,null,null,512),[[N,!r(c)]]),x(t(U,null,null,512),[[N,r(c)]])]),_:1})]),t(r(ee),{ref_key:"treeRef",ref:D,data:o.treeData,props:I,"filter-node-method":R,load:$,lazy:"",indent:38,onNodeClick:M,"highlight-current":""},{default:l(({node:d,data:y})=>[t(r(H),{node:d,showLabelLine:!1,indent:32},{default:l(()=>[y.status?(C(),w("span",ie,[t(b,{name:"iconfont icon-shouye",color:"var(--el-color-primary)"}),S(" "+g(d.label)+" ",1),x(v("span",null,"（"+g(y.dept_user_count)+"人）",513),[[N,r(c)]])])):(C(),w("span",de,[t(b,{name:"iconfont icon-shouye"}),S(" "+g(d.label),1)]))]),_:2},1032,["node"])]),_:1},8,["data"]),v("div",re,[t(_,{effect:"dark",content:"新增"},{default:l(()=>[t(i,{size:"16",onClick:e[2]||(e[2]=d=>T("create")),class:"mlt-icon"},{default:l(()=>[t(q)]),_:1})]),_:1}),t(_,{effect:"dark",content:"编辑"},{default:l(()=>[t(i,{size:"16",onClick:e[3]||(e[3]=d=>T("update")),class:"mlt-icon"},{default:l(()=>[t(A)]),_:1})]),_:1}),t(_,{effect:"dark",content:"上移"},{default:l(()=>[t(i,{size:"16",onClick:e[4]||(e[4]=d=>V("up")),class:"mlt-icon"},{default:l(()=>[t(O)]),_:1})]),_:1}),t(_,{effect:"dark",content:"下移"},{default:l(()=>[t(i,{size:"16",onClick:e[5]||(e[5]=d=>V("down")),class:"mlt-icon"},{default:l(()=>[t(j)]),_:1})]),_:1}),t(_,{effect:"dark",content:"删除"},{default:l(()=>[t(i,{size:"16",onClick:P,class:"mlt-icon"},{default:l(()=>[t(G)]),_:1})]),_:1})])])],64)}}}),he=ne(ce,[["__scopeId","data-v-0a1dffe4"]]);export{he as default};
