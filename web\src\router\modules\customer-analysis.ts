import { RouteRecordRaw } from 'vue-router';

/**
 * 客户分析相关路由
 */
const customerAnalysisRoutes: Array<RouteRecordRaw> = [
  {
    path: '/customer-analysis',
    name: 'customerAnalysis',
    component: () => import('/@/views/customer-analysis/index.vue'),
    meta: {
      title: '客户分析',
      isLink: '',
      isHide: false,
      isKeepAlive: true,
      isAffix: false,
      isIframe: false,
      icon: 'iconfont icon-kehu',
      roles: ['admin', 'common'],
    },
  },
];

export default customerAnalysisRoutes;
