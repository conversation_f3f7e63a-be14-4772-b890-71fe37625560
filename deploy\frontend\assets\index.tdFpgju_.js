import{r as V,X as R}from"./index.BHZI5pdK.js";import{d as B,h as p,g as P,j as A,k as r,b as v,o as m,w as _,e as E,l as u,m as j,a as D,F,p as L}from"./vue.BNx9QYep.js";import{_ as X}from"./_plugin-vue_export-helper.DlAUqK2U.js";const q={class:"option"},I=B({__name:"index",props:{modelValue:{type:Array||String||Number,default:()=>[]},tableConfig:{type:Object,default:{url:null,label:null,value:null,isTree:!1,lazy:!0,size:"default",load:()=>{},data:[],isMultiple:!1,collapseTags:!1,treeProps:{children:"children",hasChildren:"hasChildren"},columns:[]}},displayLabel:{}},emits:["update:modelValue"],setup(w,{emit:x}){const a=w,y=x,f=p(),d=p();p();const b=p(void 0),c=p([]),l=P({page:1,limit:10,total:0}),h=t=>{const{tableConfig:e}=a,i=t.map(n=>n[e.value]);d.value=t.map(n=>n[e.label]),y("update:modelValue",i)},z=t=>{const{tableConfig:e}=a;!e.isMultiple&&t&&y("update:modelValue",t[e.value])},g=async()=>{const t=a.tableConfig.url,e={page:l.page,limit:l.limit,search:b.value},{data:i,page:n,limit:s,total:C}=await V({url:t,params:e});l.page=n,l.limit=s,l.total=C,a.tableConfig.data===void 0||a.tableConfig.data.length===0?a.tableConfig.isTree?c.value=R.toArrayTree(i,{parentKey:"parent",key:"id",children:"children"}):c.value=i:c.value=a.tableConfig.data},S=()=>{V({url:a.tableConfig.valueUrl,method:"get",params:{ids:a.modelValue}}).then(t=>{t.data.length>0&&(d.value=t.data.map(e=>e[a.tableConfig.label]),f.value.clearSelection(),t.data.forEach(e=>{f.value.toggleRowSelection(e,!0,!1)}))})},T=t=>{t&&g()},k=t=>{l.page=t,g()};return A(()=>{setTimeout(()=>{S()},1e3)}),(t,e)=>{const i=r("el-button"),n=r("el-input"),s=r("el-table-column"),C=r("el-table"),U=r("el-pagination"),M=r("el-select");return m(),v(M,{"popper-class":"popperClass",class:"tableSelector",multiple:"",collapseTags:a.tableConfig.collapseTags,onRemoveTag:t.removeTag,modelValue:d.value,"onUpdate:modelValue":e[3]||(e[3]=o=>d.value=o),placeholder:"请选择",onVisibleChange:T},{empty:_(()=>[E("div",q,[u(n,{style:{"margin-bottom":"10px"},modelValue:b.value,"onUpdate:modelValue":e[0]||(e[0]=o=>b.value=o),clearable:"",placeholder:"请输入关键词",onChange:g,onClear:g},{append:_(()=>[u(i,{type:"primary",icon:"Search"})]),_:1},8,["modelValue"]),u(C,{ref_key:"tableRef",ref:f,data:c.value,size:a.tableConfig.size,border:"","row-key":"id",lazy:a.tableConfig.lazy,load:a.tableConfig.load,"tree-props":a.tableConfig.treeProps,style:{width:"600px"},"max-height":"200",height:"200","highlight-current-row":!a.tableConfig.isMultiple,onSelectionChange:h,onSelect:h,onSelectAll:h,onCurrentChange:z},{default:_(()=>[a.tableConfig.isMultiple?(m(),v(s,{key:0,fixed:"",type:"selection","reserve-selection":"",width:"55"})):j("",!0),u(s,{fixed:"",type:"index",label:"#",width:"50"}),(m(!0),D(F,null,L(a.tableConfig.columns,(o,N)=>(m(),v(s,{prop:o.prop,label:o.label,width:o.width,key:N},null,8,["prop","label","width"]))),128))]),_:1},8,["data","size","lazy","load","tree-props","highlight-current-row"]),u(U,{style:{"margin-top":"10px"},background:"","current-page":l.page,"onUpdate:currentPage":e[1]||(e[1]=o=>l.page=o),"page-size":l.limit,"onUpdate:pageSize":e[2]||(e[2]=o=>l.limit=o),layout:"prev, pager, next",total:l.total,onCurrentChange:k},null,8,["current-page","page-size","total"])])]),_:1},8,["collapseTags","onRemoveTag","modelValue"])}}}),H=X(I,[["__scopeId","data-v-df9efb6a"]]);export{H as t};
