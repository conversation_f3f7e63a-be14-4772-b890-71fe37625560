import { request } from '/@/utils/service';

/**
 * 客户分析API接口
 */

// API路径前缀
export const apiPrefix = '/api/customer-analysis/';

/**
 * 客户分析查询
 * @param params 查询参数
 */
export function customerAnalysisQuery(params: {
  thresholdDays: number;
  company?: string;
  customerName?: string;
  page?: number;
  pageSize?: number;
}) {
  return request({
    url: apiPrefix + 'query/',
    method: 'post',
    data: params,
  });
}

/**
 * 客户分析数据导出
 * @param params 导出参数
 */
export function customerAnalysisExport(params: {
  thresholdDays: number;
  company?: string;
  customerName?: string;
}) {
  return request({
    url: apiPrefix + 'export/',
    method: 'post',
    data: params,
    responseType: 'blob',
    timeout: 60000  // 增加超时时间到60秒
  });
}

/**
 * 格式化货币显示
 * @param value 数值
 * @param precision 小数位数，默认2位
 */
export function formatCurrency(value: number, precision: number = 2): string {
  if (value === null || value === undefined || isNaN(value)) {
    return '0.00';
  }
  return value.toLocaleString('zh-CN', {
    minimumFractionDigits: precision,
    maximumFractionDigits: precision
  });
}

/**
 * 获取客户状态标签类型
 * @param status 客户状态
 */
export function getStatusTagType(status: string): string {
  const statusMap: { [key: string]: string } = {
    '今天下单': 'success',
    '昨天下单': 'success', 
    '活跃客户': 'success',
    '一般客户': 'warning',
    '沉睡客户': 'danger',
    '流失客户': 'danger',
    '从未下单': 'info'
  };
  return statusMap[status] || 'info';
}

/**
 * 获取风险等级标签类型
 * @param risk 风险等级
 */
export function getRiskTagType(risk: string): string {
  const riskMap: { [key: string]: string } = {
    '低': 'success',
    '中': 'warning',
    '高': 'danger'
  };
  return riskMap[risk] || 'info';
}

/**
 * 获取公司列表
 */
export function getCompanyList() {
  return request({
    url: apiPrefix + 'companies/',
    method: 'get',
  });
}
