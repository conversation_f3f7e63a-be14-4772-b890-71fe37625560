import{a as c}from"./index.BHZI5pdK.js";import{d as m,v as p,k as i,a as f,o as h,l as _,x as w,u as b}from"./vue.BNx9QYep.js";const k=m({__name:"crudTable",props:{modelValue:{type:Array,default:()=>[]}},emits:["update:modelValue"],setup(o,{emit:a}){const s=function({crudExpose:r,context:n}){return{crudOptions:{mode:{name:"local",isMergeWhenUpdate:!0,isAppendWhenAdd:!0},actionbar:{buttons:{add:{show:!0},addRow:{show:!1}}},editable:{enabled:!0,mode:"row",activeDefault:!0},form:{wrapper:{width:"500px"},col:{span:24},afterSubmit({mode:t}){l("update:modelValue",e.value.data)}},toolbar:{show:!1},search:{disabled:!0,show:!1},pagination:{show:!1},columns:{title:{title:"标题",form:{component:{placeholder:"请输入标题"},rules:[{required:!0,message:"必须填写"}]}},key:{title:"键名",form:{component:{placeholder:"请输入键名"},rules:[{required:!0,message:"必须填写"}]}},value:{title:"键值",form:{component:{placeholder:"请输入键值"},rules:[{required:!0,message:"必须填写"}]}}}}}},{crudBinding:e,crudRef:u,crudExpose:x}=c({createCrudOptions:s}),d=o,l=a;return p(()=>d.modelValue,(r=[])=>{e.value.data=r},{immediate:!0}),(r,n)=>{const t=i("fs-crud");return h(),f("div",null,[_(t,w({ref_key:"crudRef",ref:u},b(e)),null,16)])}}});export{k as _};
