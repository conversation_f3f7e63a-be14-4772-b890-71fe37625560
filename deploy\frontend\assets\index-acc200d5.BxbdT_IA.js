import{j as Be,C as Ie,m as <PERSON>,M as <PERSON>e,<PERSON> as He}from"./index.BHZI5pdK.js";import{d as Fe,a as <PERSON>,o as Oe,e as De,A as Qe,W as Ue}from"./vue.BNx9QYep.js";function Ye(Z){return Z&&Z.__esModule&&Object.prototype.hasOwnProperty.call(Z,"default")?Z.default:Z}var Ne={exports:{}};(function(Z,oe){(function(u,n){Z.exports=n()})(window,function(){return function(u){var n={};function e(t){if(n[t])return n[t].exports;var i=n[t]={i:t,l:!1,exports:{}};return u[t].call(i.exports,i,i.exports,e),i.l=!0,i.exports}return e.m=u,e.c=n,e.d=function(t,i,o){e.o(t,i)||Object.defineProperty(t,i,{enumerable:!0,get:o})},e.r=function(t){typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},e.t=function(t,i){if(i&1&&(t=e(t)),i&8||i&4&&typeof t=="object"&&t&&t.__esModule)return t;var o=Object.create(null);if(e.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:t}),i&2&&typeof t!="string")for(var r in t)e.d(o,r,(function(f){return t[f]}).bind(null,r));return o},e.n=function(t){var i=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(i,"a",i),i},e.o=function(t,i){return Object.prototype.hasOwnProperty.call(t,i)},e.p="",e(e.s=141)}([function(u,n){function e(t){return t&&t.__esModule?t:{default:t}}u.exports=e},function(u,n,e){u.exports=e(142)},function(u,n,e){e.r(n),e.d(n,"__extends",function(){return i}),e.d(n,"__assign",function(){return o}),e.d(n,"__rest",function(){return r}),e.d(n,"__decorate",function(){return f}),e.d(n,"__param",function(){return g}),e.d(n,"__metadata",function(){return c}),e.d(n,"__awaiter",function(){return m}),e.d(n,"__generator",function(){return s}),e.d(n,"__createBinding",function(){return a}),e.d(n,"__exportStar",function(){return d}),e.d(n,"__values",function(){return l}),e.d(n,"__read",function(){return v}),e.d(n,"__spread",function(){return p}),e.d(n,"__spreadArrays",function(){return h}),e.d(n,"__spreadArray",function(){return A}),e.d(n,"__await",function(){return y}),e.d(n,"__asyncGenerator",function(){return x}),e.d(n,"__asyncDelegator",function(){return b}),e.d(n,"__asyncValues",function(){return E}),e.d(n,"__makeTemplateObject",function(){return C}),e.d(n,"__importStar",function(){return w}),e.d(n,"__importDefault",function(){return S}),e.d(n,"__classPrivateFieldGet",function(){return M}),e.d(n,"__classPrivateFieldSet",function(){return D});/*! *****************************************************************************
	Copyright (c) Microsoft Corporation.

	Permission to use, copy, modify, and/or distribute this software for any
	purpose with or without fee is hereby granted.

	THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
	REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
	AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
	INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
	LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
	OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
	PERFORMANCE OF THIS SOFTWARE.
	***************************************************************************** */var t=function(k,N){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(T,B){T.__proto__=B}||function(T,B){for(var R in B)Object.prototype.hasOwnProperty.call(B,R)&&(T[R]=B[R])},t(k,N)};function i(k,N){if(typeof N!="function"&&N!==null)throw new TypeError("Class extends value "+String(N)+" is not a constructor or null");t(k,N);function T(){this.constructor=k}k.prototype=N===null?Object.create(N):(T.prototype=N.prototype,new T)}var o=function(){return o=Object.assign||function(k){for(var N,T=1,B=arguments.length;T<B;T++){N=arguments[T];for(var R in N)Object.prototype.hasOwnProperty.call(N,R)&&(k[R]=N[R])}return k},o.apply(this,arguments)};function r(k,N){var T={};for(var B in k)Object.prototype.hasOwnProperty.call(k,B)&&N.indexOf(B)<0&&(T[B]=k[B]);if(k!=null&&typeof Object.getOwnPropertySymbols=="function")for(var R=0,B=Object.getOwnPropertySymbols(k);R<B.length;R++)N.indexOf(B[R])<0&&Object.prototype.propertyIsEnumerable.call(k,B[R])&&(T[B[R]]=k[B[R]]);return T}function f(k,N,T,B){var R=arguments.length,I=R<3?N:B===null?B=Object.getOwnPropertyDescriptor(N,T):B,P;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")I=Reflect.decorate(k,N,T,B);else for(var H=k.length-1;H>=0;H--)(P=k[H])&&(I=(R<3?P(I):R>3?P(N,T,I):P(N,T))||I);return R>3&&I&&Object.defineProperty(N,T,I),I}function g(k,N){return function(T,B){N(T,B,k)}}function c(k,N){if(typeof Reflect=="object"&&typeof Reflect.metadata=="function")return Reflect.metadata(k,N)}function m(k,N,T,B){function R(I){return I instanceof T?I:new T(function(P){P(I)})}return new(T||(T=Promise))(function(I,P){function H($){try{O(B.next($))}catch(L){P(L)}}function z($){try{O(B.throw($))}catch(L){P(L)}}function O($){$.done?I($.value):R($.value).then(H,z)}O((B=B.apply(k,N||[])).next())})}function s(k,N){var T={label:0,sent:function(){if(I[0]&1)throw I[1];return I[1]},trys:[],ops:[]},B,R,I,P;return P={next:H(0),throw:H(1),return:H(2)},typeof Symbol=="function"&&(P[Symbol.iterator]=function(){return this}),P;function H(O){return function($){return z([O,$])}}function z(O){if(B)throw new TypeError("Generator is already executing.");for(;T;)try{if(B=1,R&&(I=O[0]&2?R.return:O[0]?R.throw||((I=R.return)&&I.call(R),0):R.next)&&!(I=I.call(R,O[1])).done)return I;switch(R=0,I&&(O=[O[0]&2,I.value]),O[0]){case 0:case 1:I=O;break;case 4:return T.label++,{value:O[1],done:!1};case 5:T.label++,R=O[1],O=[0];continue;case 7:O=T.ops.pop(),T.trys.pop();continue;default:if(I=T.trys,!(I=I.length>0&&I[I.length-1])&&(O[0]===6||O[0]===2)){T=0;continue}if(O[0]===3&&(!I||O[1]>I[0]&&O[1]<I[3])){T.label=O[1];break}if(O[0]===6&&T.label<I[1]){T.label=I[1],I=O;break}if(I&&T.label<I[2]){T.label=I[2],T.ops.push(O);break}I[2]&&T.ops.pop(),T.trys.pop();continue}O=N.call(k,T)}catch($){O=[6,$],R=0}finally{B=I=0}if(O[0]&5)throw O[1];return{value:O[0]?O[1]:void 0,done:!0}}}var a=Object.create?function(k,N,T,B){B===void 0&&(B=T),Object.defineProperty(k,B,{enumerable:!0,get:function(){return N[T]}})}:function(k,N,T,B){B===void 0&&(B=T),k[B]=N[T]};function d(k,N){for(var T in k)T!=="default"&&!Object.prototype.hasOwnProperty.call(N,T)&&a(N,k,T)}function l(k){var N=typeof Symbol=="function"&&Symbol.iterator,T=N&&k[N],B=0;if(T)return T.call(k);if(k&&typeof k.length=="number")return{next:function(){return k&&B>=k.length&&(k=void 0),{value:k&&k[B++],done:!k}}};throw new TypeError(N?"Object is not iterable.":"Symbol.iterator is not defined.")}function v(k,N){var T=typeof Symbol=="function"&&k[Symbol.iterator];if(!T)return k;var B=T.call(k),R,I=[],P;try{for(;(N===void 0||N-- >0)&&!(R=B.next()).done;)I.push(R.value)}catch(H){P={error:H}}finally{try{R&&!R.done&&(T=B.return)&&T.call(B)}finally{if(P)throw P.error}}return I}function p(){for(var k=[],N=0;N<arguments.length;N++)k=k.concat(v(arguments[N]));return k}function h(){for(var k=0,N=0,T=arguments.length;N<T;N++)k+=arguments[N].length;for(var B=Array(k),R=0,N=0;N<T;N++)for(var I=arguments[N],P=0,H=I.length;P<H;P++,R++)B[R]=I[P];return B}function A(k,N){for(var T=0,B=N.length,R=k.length;T<B;T++,R++)k[R]=N[T];return k}function y(k){return this instanceof y?(this.v=k,this):new y(k)}function x(k,N,T){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var B=T.apply(k,N||[]),R,I=[];return R={},P("next"),P("throw"),P("return"),R[Symbol.asyncIterator]=function(){return this},R;function P(U){B[U]&&(R[U]=function(Y){return new Promise(function(G,q){I.push([U,Y,G,q])>1||H(U,Y)})})}function H(U,Y){try{z(B[U](Y))}catch(G){L(I[0][3],G)}}function z(U){U.value instanceof y?Promise.resolve(U.value.v).then(O,$):L(I[0][2],U)}function O(U){H("next",U)}function $(U){H("throw",U)}function L(U,Y){U(Y),I.shift(),I.length&&H(I[0][0],I[0][1])}}function b(k){var N,T;return N={},B("next"),B("throw",function(R){throw R}),B("return"),N[Symbol.iterator]=function(){return this},N;function B(R,I){N[R]=k[R]?function(P){return(T=!T)?{value:y(k[R](P)),done:R==="return"}:I?I(P):P}:I}}function E(k){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var N=k[Symbol.asyncIterator],T;return N?N.call(k):(k=typeof l=="function"?l(k):k[Symbol.iterator](),T={},B("next"),B("throw"),B("return"),T[Symbol.asyncIterator]=function(){return this},T);function B(I){T[I]=k[I]&&function(P){return new Promise(function(H,z){P=k[I](P),R(H,z,P.done,P.value)})}}function R(I,P,H,z){Promise.resolve(z).then(function(O){I({value:O,done:H})},P)}}function C(k,N){return Object.defineProperty?Object.defineProperty(k,"raw",{value:N}):k.raw=N,k}var _=Object.create?function(k,N){Object.defineProperty(k,"default",{enumerable:!0,value:N})}:function(k,N){k.default=N};function w(k){if(k&&k.__esModule)return k;var N={};if(k!=null)for(var T in k)T!=="default"&&Object.prototype.hasOwnProperty.call(k,T)&&a(N,k,T);return _(N,k),N}function S(k){return k&&k.__esModule?k:{default:k}}function M(k,N){if(!N.has(k))throw new TypeError("attempted to get private field on non-instance");return N.get(k)}function D(k,N,T){if(!N.has(k))throw new TypeError("attempted to set private field on non-instance");return N.set(k,T),T}},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(89)),r=t(e(4)),f=t(e(26)),g=t(e(17)),c=t(e(121)),m=t(e(27)),s=t(e(91)),a=t(e(70)),d=t(e(28)),l=t(e(57));(0,i.default)(n,"__esModule",{value:!0}),n.DomElement=void 0;var v=e(2),p=e(6),h=[];function A(_){var w=document.createElement("div");w.innerHTML=_;var S=w.children;return p.toArray(S)}function y(_){return _?_ instanceof HTMLCollection||_ instanceof NodeList:!1}function x(_){var w=document.querySelectorAll(_);return p.toArray(w)}function b(_){var w=[],S=[];return(0,o.default)(_)?w=_:w=_.split(";"),(0,r.default)(w).call(w,function(M){var D,k=(0,f.default)(D=M.split(":")).call(D,function(N){return(0,g.default)(N).call(N)});k.length===2&&S.push(k[0]+":"+k[1])}),S}var E=function(){function _(w){if(this.elems=[],this.length=this.elems.length,this.dataSource=new c.default,!!w){if(w instanceof _)return w;var S=[],M=w instanceof Node?w.nodeType:-1;if(this.selector=w,M===1||M===9)S=[w];else if(y(w))S=p.toArray(w);else if(w instanceof Array)S=w;else if(typeof w=="string"){var D,k=(0,g.default)(D=w.replace(`/
/mg`,"")).call(D);(0,m.default)(k).call(k,"<")===0?S=A(k):S=x(k)}var N=S.length;if(!N)return this;for(var T=0;T<N;T++)this.elems.push(S[T]);this.length=N}}return(0,i.default)(_.prototype,"id",{get:function(){return this.elems[0].id},enumerable:!1,configurable:!0}),_.prototype.forEach=function(w){for(var S=0;S<this.length;S++){var M=this.elems[S],D=w.call(M,M,S);if(D===!1)break}return this},_.prototype.clone=function(w){var S;w===void 0&&(w=!1);var M=[];return(0,r.default)(S=this.elems).call(S,function(D){M.push(D.cloneNode(!!w))}),C(M)},_.prototype.get=function(w){w===void 0&&(w=0);var S=this.length;return w>=S&&(w=w%S),C(this.elems[w])},_.prototype.first=function(){return this.get(0)},_.prototype.last=function(){var w=this.length;return this.get(w-1)},_.prototype.on=function(w,S,M){var D;return w?(typeof S=="function"&&(M=S,S=""),(0,r.default)(D=this).call(D,function(k){if(!S){k.addEventListener(w,M);return}var N=function(T){var B=T.target;B.matches(S)&&M.call(B,T)};k.addEventListener(w,N),h.push({elem:k,selector:S,fn:M,agentFn:N})})):this},_.prototype.off=function(w,S,M){var D;return w?(typeof S=="function"&&(M=S,S=""),(0,r.default)(D=this).call(D,function(k){if(S){for(var N=-1,T=0;T<h.length;T++){var B=h[T];if(B.selector===S&&B.fn===M&&B.elem===k){N=T;break}}if(N!==-1){var R=(0,s.default)(h).call(h,N,1)[0].agentFn;k.removeEventListener(w,R)}}else k.removeEventListener(w,M)})):this},_.prototype.attr=function(w,S){var M;return S==null?this.elems[0].getAttribute(w)||"":(0,r.default)(M=this).call(M,function(D){D.setAttribute(w,S)})},_.prototype.removeAttr=function(w){var S;(0,r.default)(S=this).call(S,function(M){M.removeAttribute(w)})},_.prototype.addClass=function(w){var S;return w?(0,r.default)(S=this).call(S,function(M){if(M.className){var D=M.className.split(/\s/);D=(0,a.default)(D).call(D,function(k){return!!(0,g.default)(k).call(k)}),(0,m.default)(D).call(D,w)<0&&D.push(w),M.className=D.join(" ")}else M.className=w}):this},_.prototype.removeClass=function(w){var S;return w?(0,r.default)(S=this).call(S,function(M){if(M.className){var D=M.className.split(/\s/);D=(0,a.default)(D).call(D,function(k){return k=(0,g.default)(k).call(k),!(!k||k===w)}),M.className=D.join(" ")}}):this},_.prototype.hasClass=function(w){if(!w)return!1;var S=this.elems[0];if(!S.className)return!1;var M=S.className.split(/\s/);return(0,d.default)(M).call(M,w)},_.prototype.css=function(w,S){var M,D;return S==""?D="":D=w+":"+S+";",(0,r.default)(M=this).call(M,function(k){var N,T=(0,g.default)(N=k.getAttribute("style")||"").call(N);if(T){var B=b(T);B=(0,f.default)(B).call(B,function(R){return(0,m.default)(R).call(R,w)===0?D:R}),D!=""&&(0,m.default)(B).call(B,D)<0&&B.push(D),D==""&&(B=b(B)),k.setAttribute("style",B.join("; "))}else k.setAttribute("style",D)})},_.prototype.getBoundingClientRect=function(){var w=this.elems[0];return w.getBoundingClientRect()},_.prototype.show=function(){return this.css("display","block")},_.prototype.hide=function(){return this.css("display","none")},_.prototype.children=function(){var w=this.elems[0];return w?C(w.children):null},_.prototype.childNodes=function(){var w=this.elems[0];return w?C(w.childNodes):null},_.prototype.replaceChildAll=function(w){for(var S=this.getNode(),M=this.elems[0];M.hasChildNodes();)S.firstChild&&M.removeChild(S.firstChild);this.append(w)},_.prototype.append=function(w){var S;return(0,r.default)(S=this).call(S,function(M){(0,r.default)(w).call(w,function(D){M.appendChild(D)})})},_.prototype.remove=function(){var w;return(0,r.default)(w=this).call(w,function(S){if(S.remove)S.remove();else{var M=S.parentElement;M&&M.removeChild(S)}})},_.prototype.isContain=function(w){var S=this.elems[0],M=w.elems[0];return S.contains(M)},_.prototype.getNodeName=function(){var w=this.elems[0];return w.nodeName},_.prototype.getNode=function(w){w===void 0&&(w=0);var S;return S=this.elems[w],S},_.prototype.find=function(w){var S=this.elems[0];return C(S.querySelectorAll(w))},_.prototype.text=function(w){if(w){var S;return(0,r.default)(S=this).call(S,function(D){D.innerHTML=w})}else{var M=this.elems[0];return M.innerHTML.replace(/<[^>]+>/g,function(){return""})}},_.prototype.html=function(w){var S=this.elems[0];return w?(S.innerHTML=w,this):S.innerHTML},_.prototype.val=function(){var w,S=this.elems[0];return(0,g.default)(w=S.value).call(w)},_.prototype.focus=function(){var w;return(0,r.default)(w=this).call(w,function(S){S.focus()})},_.prototype.prev=function(){var w=this.elems[0];return C(w.previousElementSibling)},_.prototype.next=function(){var w=this.elems[0];return C(w.nextElementSibling)},_.prototype.getNextSibling=function(){var w=this.elems[0];return C(w.nextSibling)},_.prototype.parent=function(){var w=this.elems[0];return C(w.parentElement)},_.prototype.parentUntil=function(w,S){var M=S||this.elems[0];if(M.nodeName==="BODY")return null;var D=M.parentElement;return D===null?null:D.matches(w)?C(D):this.parentUntil(w,D)},_.prototype.parentUntilEditor=function(w,S,M){var D=M||this.elems[0];if(C(D).equal(S.$textContainerElem)||C(D).equal(S.$toolbarElem))return null;var k=D.parentElement;return k===null?null:k.matches(w)?C(k):this.parentUntilEditor(w,S,k)},_.prototype.equal=function(w){return w instanceof _?this.elems[0]===w.elems[0]:w instanceof HTMLElement?this.elems[0]===w:!1},_.prototype.insertBefore=function(w){var S,M=C(w),D=M.elems[0];return D?(0,r.default)(S=this).call(S,function(k){var N=D.parentNode;N==null||N.insertBefore(k,D)}):this},_.prototype.insertAfter=function(w){var S,M=C(w),D=M.elems[0],k=D&&D.nextSibling;return D?(0,r.default)(S=this).call(S,function(N){var T=D.parentNode;k?T.insertBefore(N,k):T.appendChild(N)}):this},_.prototype.data=function(w,S){if(S!=null)this.dataSource.set(w,S);else return this.dataSource.get(w)},_.prototype.getNodeTop=function(w){if(this.length<1)return this;var S=this.parent();return w.$textElem.equal(this)||w.$textElem.equal(S)?this:(S.prior=this,S.getNodeTop(w))},_.prototype.getOffsetData=function(){var w=this.elems[0];return{top:w.offsetTop,left:w.offsetLeft,width:w.offsetWidth,height:w.offsetHeight,parent:w.offsetParent}},_.prototype.scrollTop=function(w){var S=this.elems[0];S.scrollTo({top:w})},_}();n.DomElement=E;function C(){for(var _=[],w=0;w<arguments.length;w++)_[w]=arguments[w];return new((0,l.default)(E).apply(E,v.__spreadArrays([void 0],_)))}n.default=C},function(u,n,e){u.exports=e(180)},function(u,n,e){var t=e(8),i=e(71).f,o=e(101),r=e(9),f=e(40),g=e(19),c=e(16),m=function(s){var a=function(d,l,v){if(this instanceof s){switch(arguments.length){case 0:return new s;case 1:return new s(d);case 2:return new s(d,l)}return new s(d,l,v)}return s.apply(this,arguments)};return a.prototype=s.prototype,a};u.exports=function(s,a){var d=s.target,l=s.global,v=s.stat,p=s.proto,h=l?t:v?t[d]:(t[d]||{}).prototype,A=l?r:r[d]||(r[d]={}),y=A.prototype,x,b,E,C,_,w,S,M,D;for(C in a)x=o(l?C:d+(v?".":"#")+C,s.forced),b=!x&&h&&c(h,C),w=A[C],b&&(s.noTargetGet?(D=i(h,C),S=D&&D.value):S=h[C]),_=b&&S?S:a[C],!(b&&typeof w==typeof _)&&(s.bind&&b?M=f(_,t):s.wrap&&b?M=m(_):p&&typeof _=="function"?M=f(Function.call,_):M=_,(s.sham||_&&_.sham||w&&w.sham)&&g(M,"sham",!0),A[C]=M,p&&(E=d+"Prototype",c(r,E)||g(r,E,{}),r[E][C]=_,s.real&&y&&!y[C]&&g(y,C,_)))}},function(u,n,e){var t=e(0),i=t(e(92)),o=t(e(1)),r=t(e(256)),f=t(e(45)),g=t(e(46)),c=t(e(89)),m=t(e(26));(0,o.default)(n,"__esModule",{value:!0}),n.hexToRgb=n.getRandomCode=n.toArray=n.deepClone=n.isFunction=n.debounce=n.throttle=n.arrForEach=n.forEach=n.replaceSpecialSymbol=n.replaceHtmlSymbol=n.getRandom=n.UA=void 0;var s=e(2),a=function(){function w(){this._ua=navigator.userAgent;var S=this._ua.match(/(Edge?)\/(\d+)/);this.isOldEdge=!!(S&&S[1]=="Edge"&&(0,r.default)(S[2])<19),this.isFirefox=!!(/Firefox\/\d+/.test(this._ua)&&!/Seamonkey\/\d+/.test(this._ua))}return w.prototype.isIE=function(){return"ActiveXObject"in window},w.prototype.isWebkit=function(){return/webkit/i.test(this._ua)},w}();n.UA=new a;function d(w){var S;return w===void 0&&(w=""),w+(0,f.default)(S=Math.random().toString()).call(S,2)}n.getRandom=d;function l(w){return w.replace(/</gm,"&lt;").replace(/>/gm,"&gt;").replace(/"/gm,"&quot;").replace(/(\r\n|\r|\n)/g,"<br/>")}n.replaceHtmlSymbol=l;function v(w){return w.replace(/&lt;/gm,"<").replace(/&gt;/gm,">").replace(/&quot;/gm,'"')}n.replaceSpecialSymbol=v;function p(w,S){for(var M in w)if(Object.prototype.hasOwnProperty.call(w,M)){var D=S(M,w[M]);if(D===!1)break}}n.forEach=p;function h(w,S){var M,D,k,N=w.length||0;for(M=0;M<N&&(D=w[M],k=S.call(w,D,M),k!==!1);M++);}n.arrForEach=h;function A(w,S){S===void 0&&(S=200);var M=!1;return function(){for(var D=this,k=[],N=0;N<arguments.length;N++)k[N]=arguments[N];M||(M=!0,(0,g.default)(function(){M=!1,w.call.apply(w,s.__spreadArrays([D],k))},S))}}n.throttle=A;function y(w,S){S===void 0&&(S=200);var M=0;return function(){for(var D=this,k=[],N=0;N<arguments.length;N++)k[N]=arguments[N];M&&window.clearTimeout(M),M=(0,g.default)(function(){M=0,w.call.apply(w,s.__spreadArrays([D],k))},S)}}n.debounce=y;function x(w){return typeof w=="function"}n.isFunction=x;function b(w){if((0,i.default)(w)!=="object"||typeof w=="function"||w===null)return w;var S;(0,c.default)(w)&&(S=[]),(0,c.default)(w)||(S={});for(var M in w)Object.prototype.hasOwnProperty.call(w,M)&&(S[M]=b(w[M]));return S}n.deepClone=b;function E(w){return(0,f.default)(Array.prototype).call(w)}n.toArray=E;function C(){var w;return(0,f.default)(w=Math.random().toString(36)).call(w,-5)}n.getRandomCode=C;function _(w){var S=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(w);if(S==null)return null;var M=(0,m.default)(S).call(S,function(T){return(0,r.default)(T,16)}),D=M[1],k=M[2],N=M[3];return"rgb("+D+", "+k+", "+N+")"}n.hexToRgb=_},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0}),n.EMPTY_P_REGEX=n.EMPTY_P_LAST_REGEX=n.EMPTY_P=n.urlRegex=n.EMPTY_FN=void 0;function o(){}n.EMPTY_FN=o,n.urlRegex=/(http|ftp|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-.,@?^=%&amp;:/~+#]*[\w\-@?^=%&amp;/~+#])?/g,n.EMPTY_P='<p data-we-empty-p=""><br></p>',n.EMPTY_P_LAST_REGEX=/<p data-we-empty-p=""><br\/?><\/p>$/gim,n.EMPTY_P_REGEX=/<p data-we-empty-p="">/gim},function(u,n,e){(function(t){var i=function(o){return o&&o.Math==Math&&o};u.exports=i(typeof globalThis=="object"&&globalThis)||i(typeof window=="object"&&window)||i(typeof self=="object"&&self)||i(typeof t=="object"&&t)||Function("return this")()}).call(this,e(145))},function(u,n){u.exports={}},function(u,n,e){var t=e(8),i=e(74),o=e(16),r=e(64),f=e(76),g=e(106),c=i("wks"),m=t.Symbol,s=g?m:m&&m.withoutSetter||r;u.exports=function(a){return o(c,a)||(f&&o(m,a)?c[a]=m[a]:c[a]=s("Symbol."+a)),c[a]}},function(u,n){u.exports=function(e){try{return!!e()}catch{return!0}}},function(u,n,e){var t=e(9),i=e(16),o=e(93),r=e(18).f;u.exports=function(f){var g=t.Symbol||(t.Symbol={});i(g,f)||r(g,f,{value:o.f(f)})}},function(u,n){u.exports=function(e){return typeof e=="object"?e!==null:typeof e=="function"}},function(u,n,e){var t=e(11);u.exports=!t(function(){return Object.defineProperty({},1,{get:function(){return 7}})[1]!=7})},function(u,n,e){var t=e(9);u.exports=function(i){return t[i+"Prototype"]}},function(u,n){var e={}.hasOwnProperty;u.exports=function(t,i){return e.call(t,i)}},function(u,n,e){u.exports=e(192)},function(u,n,e){var t=e(14),i=e(100),o=e(25),r=e(60),f=Object.defineProperty;n.f=t?f:function(g,c,m){if(o(g),c=r(c,!0),o(m),i)try{return f(g,c,m)}catch{}if("get"in m||"set"in m)throw TypeError("Accessors not supported");return"value"in m&&(g[c]=m.value),g}},function(u,n,e){var t=e(14),i=e(18),o=e(48);u.exports=t?function(r,f,g){return i.f(r,f,o(1,g))}:function(r,f,g){return r[f]=g,r}},function(u,n,e){var t=function(){var p;return function(){return typeof p>"u"&&(p=!!(window&&document&&document.all&&!window.atob)),p}}(),i=function(){var p={};return function(h){if(typeof p[h]>"u"){var A=document.querySelector(h);if(window.HTMLIFrameElement&&A instanceof window.HTMLIFrameElement)try{A=A.contentDocument.head}catch{A=null}p[h]=A}return p[h]}}(),o=[];function r(p){for(var h=-1,A=0;A<o.length;A++)if(o[A].identifier===p){h=A;break}return h}function f(p,h){for(var A={},y=[],x=0;x<p.length;x++){var b=p[x],E=h.base?b[0]+h.base:b[0],C=A[E]||0,_="".concat(E," ").concat(C);A[E]=C+1;var w=r(_),S={css:b[1],media:b[2],sourceMap:b[3]};w!==-1?(o[w].references++,o[w].updater(S)):o.push({identifier:_,updater:v(S,h),references:1}),y.push(_)}return y}function g(p){var h=document.createElement("style"),A=p.attributes||{};if(typeof A.nonce>"u"){var y=e.nc;y&&(A.nonce=y)}if(Object.keys(A).forEach(function(b){h.setAttribute(b,A[b])}),typeof p.insert=="function")p.insert(h);else{var x=i(p.insert||"head");if(!x)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");x.appendChild(h)}return h}function c(p){if(p.parentNode===null)return!1;p.parentNode.removeChild(p)}var m=function(){var p=[];return function(h,A){return p[h]=A,p.filter(Boolean).join(`
`)}}();function s(p,h,A,y){var x=A?"":y.media?"@media ".concat(y.media," {").concat(y.css,"}"):y.css;if(p.styleSheet)p.styleSheet.cssText=m(h,x);else{var b=document.createTextNode(x),E=p.childNodes;E[h]&&p.removeChild(E[h]),E.length?p.insertBefore(b,E[h]):p.appendChild(b)}}function a(p,h,A){var y=A.css,x=A.media,b=A.sourceMap;if(x?p.setAttribute("media",x):p.removeAttribute("media"),b&&typeof btoa<"u"&&(y+=`
/*# sourceMappingURL=data:application/json;base64,`.concat(btoa(unescape(encodeURIComponent(JSON.stringify(b))))," */")),p.styleSheet)p.styleSheet.cssText=y;else{for(;p.firstChild;)p.removeChild(p.firstChild);p.appendChild(document.createTextNode(y))}}var d=null,l=0;function v(p,h){var A,y,x;if(h.singleton){var b=l++;A=d||(d=g(h)),y=s.bind(null,A,b,!1),x=s.bind(null,A,b,!0)}else A=g(h),y=a.bind(null,A,h),x=function(){c(A)};return y(p),function(E){if(E){if(E.css===p.css&&E.media===p.media&&E.sourceMap===p.sourceMap)return;y(p=E)}else x()}}u.exports=function(p,h){h=h||{},!h.singleton&&typeof h.singleton!="boolean"&&(h.singleton=t()),p=p||[];var A=f(p,h);return function(y){if(y=y||[],Object.prototype.toString.call(y)==="[object Array]"){for(var x=0;x<A.length;x++){var b=A[x],E=r(b);o[E].references--}for(var C=f(y,h),_=0;_<A.length;_++){var w=A[_],S=r(w);o[S].references===0&&(o[S].updater(),o.splice(S,1))}A=C}}}},function(u,n,e){u.exports=function(o){var r=[];return r.toString=function(){return this.map(function(f){var g=t(f,o);return f[2]?"@media ".concat(f[2]," {").concat(g,"}"):g}).join("")},r.i=function(f,g,c){typeof f=="string"&&(f=[[null,f,""]]);var m={};if(c)for(var s=0;s<this.length;s++){var a=this[s][0];a!=null&&(m[a]=!0)}for(var d=0;d<f.length;d++){var l=[].concat(f[d]);c&&m[l[0]]||(g&&(l[2]?l[2]="".concat(g," and ").concat(l[2]):l[2]=g),r.push(l))}},r};function t(o,r){var f=o[1]||"",g=o[3];if(!g)return f;if(r&&typeof btoa=="function"){var c=i(g),m=g.sources.map(function(s){return"/*# sourceURL=".concat(g.sourceRoot||"").concat(s," */")});return[f].concat(m).concat([c]).join(`
`)}return[f].join(`
`)}function i(o){var r=btoa(unescape(encodeURIComponent(JSON.stringify(o)))),f="sourceMappingURL=data:application/json;charset=utf-8;base64,".concat(r);return"/*# ".concat(f," */")}},function(u,n,e){var t=e(14),i=e(11),o=e(16),r=Object.defineProperty,f={},g=function(c){throw c};u.exports=function(c,m){if(o(f,c))return f[c];m||(m={});var s=[][c],a=o(m,"ACCESSORS")?m.ACCESSORS:!1,d=o(m,0)?m[0]:g,l=o(m,1)?m[1]:void 0;return f[c]=!!s&&!i(function(){if(a&&!t)return!0;var v={length:-1};a?r(v,1,{enumerable:!0,get:g}):v[1]=1,s.call(v,d,l)})}},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(2),r=o.__importDefault(e(95)),f=function(g){o.__extends(c,g);function c(m,s){return g.call(this,m,s)||this}return c}(r.default);n.default=f},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(4)),r=t(e(46));(0,i.default)(n,"__esModule",{value:!0});var f=e(2),g=f.__importDefault(e(3)),c=f.__importDefault(e(95)),m=f.__importDefault(e(134)),s=function(a){f.__extends(d,a);function d(l,v,p){var h=a.call(this,l,v)||this;p.title=v.i18next.t("menus.dropListMenu."+p.title);var A=v.config.lang==="zh-CN"?"":"w-e-drop-list-tl";if(A!==""&&p.type==="list"){var y;(0,o.default)(y=p.list).call(y,function(b){var E=b.$elem,C=g.default(E.children());if(C.length>0){var _=C==null?void 0:C.getNodeName();_&&_==="I"&&E.addClass(A)}})}var x=new m.default(h,p);return h.dropList=x,l.on("click",function(){var b;v.selection.getRange()!=null&&(l.css("z-index",v.zIndex.get("menu")),(0,o.default)(b=v.txt.eventHooks.dropListMenuHoverEvents).call(b,function(E){return E()}),x.show())}).on("mouseleave",function(){l.css("z-index","auto"),x.hideTimeoutId=(0,r.default)(function(){x.hide()})}),h}return d}(c.default);n.default=s},function(u,n,e){var t=e(13);u.exports=function(i){if(!t(i))throw TypeError(String(i)+" is not an object");return i}},function(u,n,e){u.exports=e(188)},function(u,n,e){u.exports=e(201)},function(u,n,e){u.exports=e(213)},function(u,n,e){u.exports=e(283)},function(u,n,e){var t=e(72),i=e(49);u.exports=function(o){return t(i(o))}},function(u,n,e){var t=e(49);u.exports=function(i){return Object(t(i))}},function(u,n,e){var t=e(40),i=e(72),o=e(31),r=e(35),f=e(88),g=[].push,c=function(m){var s=m==1,a=m==2,d=m==3,l=m==4,v=m==6,p=m==5||v;return function(h,A,y,x){for(var b=o(h),E=i(b),C=t(A,y,3),_=r(E.length),w=0,S=x||f,M=s?S(h,_):a?S(h,0):void 0,D,k;_>w;w++)if((p||w in E)&&(D=E[w],k=C(D,w,b),m)){if(s)M[w]=k;else if(k)switch(m){case 3:return!0;case 5:return D;case 6:return w;case 2:g.call(M,D)}else if(l)return!1}return v?-1:d||l?l:M}};u.exports={forEach:c(0),map:c(1),filter:c(2),some:c(3),every:c(4),find:c(5),findIndex:c(6)}},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(4)),r=t(e(29)),f=t(e(132));(0,i.default)(n,"__esModule",{value:!0});var g=e(2),c=g.__importDefault(e(3)),m=e(7),s=function(){function a(d,l){this.menu=d,this.conf=l,this.$container=c.default('<div class="w-e-panel-container"></div>');var v=d.editor;v.txt.eventHooks.clickEvents.push(a.hideCurAllPanels),v.txt.eventHooks.toolbarClickEvents.push(a.hideCurAllPanels),v.txt.eventHooks.dropListMenuHoverEvents.push(a.hideCurAllPanels)}return a.prototype.create=function(){var d=this,l=this.menu;if(!a.createdMenus.has(l)){var v=this.conf,p=this.$container,h=v.width||300,A=l.editor.$toolbarElem.getBoundingClientRect(),y=l.$elem.getBoundingClientRect(),x=A.height+A.top-y.top,b=(A.width-h)/2+A.left-y.left,E=300;Math.abs(b)>E&&(y.left<document.documentElement.clientWidth/2?b=-y.width/2:b=-h+y.width/2),p.css("width",h+"px").css("margin-top",x+"px").css("margin-left",b+"px").css("z-index",l.editor.zIndex.get("panel"));var C=c.default('<i class="w-e-icon-close w-e-panel-close"></i>');p.append(C),C.on("click",function(){d.remove()});var _=c.default('<ul class="w-e-panel-tab-title"></ul>'),w=c.default('<div class="w-e-panel-tab-content"></div>');p.append(_).append(w);var S=v.height;S&&w.css("height",S+"px").css("overflow-y","auto");var M=v.tabs||[],D=[],k=[];(0,o.default)(M).call(M,function(T,B){if(T){var R=T.title||"",I=T.tpl||"",P=c.default('<li class="w-e-item">'+R+"</li>");_.append(P);var H=c.default(I);w.append(H),D.push(P),k.push(H),B===0?(P.data("active",!0),P.addClass("w-e-active")):H.hide(),P.on("click",function(){P.data("active")||((0,o.default)(D).call(D,function(z){z.data("active",!1),z.removeClass("w-e-active")}),(0,o.default)(k).call(k,function(z){z.hide()}),P.data("active",!0),P.addClass("w-e-active"),H.show())})}}),p.on("click",function(T){T.stopPropagation()}),l.$elem.append(p),v.setLinkValue&&v.setLinkValue(p,"text"),v.setLinkValue&&v.setLinkValue(p,"link"),(0,o.default)(M).call(M,function(T,B){if(T){var R=T.events||[];(0,o.default)(R).call(R,function(I){var P,H=I.selector,z=I.type,O=I.fn||m.EMPTY_FN,$=k[B],L=(P=I.bindEnter)!==null&&P!==void 0?P:!1,U=function(Y){return g.__awaiter(d,void 0,void 0,function(){var G;return g.__generator(this,function(q){switch(q.label){case 0:return Y.stopPropagation(),[4,O(Y)];case 1:return G=q.sent(),G&&this.remove(),[2]}})})};(0,r.default)($).call($,H).on(z,U),L&&z==="click"&&$.on("keyup",function(Y){Y.keyCode==13&&U(Y)})})}});var N=(0,r.default)(p).call(p,"input[type=text],textarea");N.length&&N.get(0).focus(),a.hideCurAllPanels(),l.setPanel(this),a.createdMenus.add(l)}},a.prototype.remove=function(){var d=this.menu,l=this.$container;l&&l.remove(),a.createdMenus.delete(d)},a.hideCurAllPanels=function(){var d;a.createdMenus.size!==0&&(0,o.default)(d=a.createdMenus).call(d,function(l){var v=l.panel;v&&v.remove()})},a.createdMenus=new f.default,a}();n.default=s},function(u,n){var e={}.toString;u.exports=function(t){return e.call(t).slice(8,-1)}},function(u,n,e){var t=e(62),i=Math.min;u.exports=function(o){return o>0?i(t(o),9007199254740991):0}},function(u,n,e){var t=e(9),i=e(8),o=function(r){return typeof r=="function"?r:void 0};u.exports=function(r,f){return arguments.length<2?o(t[r])||o(i[r]):t[r]&&t[r][f]||i[r]&&i[r][f]}},function(u,n,e){var t=e(81),i=e(18).f,o=e(19),r=e(16),f=e(170),g=e(10),c=g("toStringTag");u.exports=function(m,s,a,d){if(m){var l=a?m:m.prototype;r(l,c)||i(l,c,{configurable:!0,value:s}),d&&!t&&o(l,"toString",f)}}},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(2),r=o.__importDefault(e(95)),f=function(g){o.__extends(c,g);function c(m,s){return g.call(this,m,s)||this}return c.prototype.setPanel=function(m){this.panel=m},c}(r.default);n.default=f},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(4)),r=t(e(57));(0,i.default)(n,"__esModule",{value:!0});var f=e(2),g=f.__importDefault(e(3)),c=function(){function m(s,a,d){this.editor=s,this.$targetElem=a,this.conf=d,this._show=!1,this._isInsertTextContainer=!1;var l=g.default("<div></div>");l.addClass("w-e-tooltip"),this.$container=l}return m.prototype.getPositionData=function(){var s=this.$container,a=0,d=0,l=20,v=document.documentElement.scrollTop,p=this.$targetElem.getBoundingClientRect(),h=this.editor.$textElem.getBoundingClientRect(),A=this.$targetElem.getOffsetData(),y=g.default(A.parent),x=this.editor.$textElem.elems[0].scrollTop;if(this._isInsertTextContainer=y.equal(this.editor.$textContainerElem),this._isInsertTextContainer){var b=y.getBoundingClientRect().height,E=A.top,C=A.left,_=A.height,w=E-x;w>l+5?(a=w-l-15,s.addClass("w-e-tooltip-up")):w+_+l<b?(a=w+_+10,s.addClass("w-e-tooltip-down")):(a=(w>0?w:0)+l+10,s.addClass("w-e-tooltip-down")),C<0?d=0:d=C}else p.top<l||p.top-h.top<l?(a=p.bottom+v+5,s.addClass("w-e-tooltip-down")):(a=p.top+v-l-15,s.addClass("w-e-tooltip-up")),p.left<0?d=0:d=p.left;return{top:a,left:d}},m.prototype.appendMenus=function(){var s=this,a=this.conf,d=this.editor,l=this.$targetElem,v=this.$container;(0,o.default)(a).call(a,function(p,h){var A=p.$elem,y=g.default("<div></div>");y.addClass("w-e-tooltip-item-wrapper "),y.append(A),v.append(y),A.on("click",function(x){x.preventDefault();var b=p.onClick(d,l);b&&s.remove()})})},m.prototype.create=function(){var s,a,d=this.editor,l=this.$container;this.appendMenus();var v=this.getPositionData(),p=v.top,h=v.left;l.css("top",p+"px"),l.css("left",h+"px"),l.css("z-index",d.zIndex.get("tooltip")),this._isInsertTextContainer?this.editor.$textContainerElem.append(l):g.default("body").append(l),this._show=!0,d.beforeDestroy((0,r.default)(s=this.remove).call(s,this)),d.txt.eventHooks.onBlurEvents.push((0,r.default)(a=this.remove).call(a,this))},m.prototype.remove=function(){this.$container.remove(),this._show=!1},(0,i.default)(m.prototype,"isShow",{get:function(){return this._show},enumerable:!1,configurable:!0}),m}();n.default=c},function(u,n,e){var t=e(41);u.exports=function(i,o,r){if(t(i),o===void 0)return i;switch(r){case 0:return function(){return i.call(o)};case 1:return function(f){return i.call(o,f)};case 2:return function(f,g){return i.call(o,f,g)};case 3:return function(f,g,c){return i.call(o,f,g,c)}}return function(){return i.apply(o,arguments)}}},function(u,n){u.exports=function(e){if(typeof e!="function")throw TypeError(String(e)+" is not a function");return e}},function(u,n,e){var t=e(165),i=e(8),o=e(13),r=e(19),f=e(16),g=e(63),c=e(51),m=i.WeakMap,s,a,d,l=function(b){return d(b)?a(b):s(b,{})},v=function(b){return function(E){var C;if(!o(E)||(C=a(E)).type!==b)throw TypeError("Incompatible receiver, "+b+" required");return C}};if(t){var p=new m,h=p.get,A=p.has,y=p.set;s=function(b,E){return y.call(p,b,E),E},a=function(b){return h.call(p,b)||{}},d=function(b){return A.call(p,b)}}else{var x=g("state");c[x]=!0,s=function(b,E){return r(b,x,E),E},a=function(b){return f(b,x)?b[x]:{}},d=function(b){return f(b,x)}}u.exports={set:s,get:a,has:d,enforce:l,getterFor:v}},function(u,n){u.exports=!0},function(u,n){u.exports={}},function(u,n,e){u.exports=e(261)},function(u,n,e){u.exports=e(265)},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(4));(0,i.default)(n,"__esModule",{value:!0}),n.createElementFragment=n.createDocumentFragment=n.createElement=n.insertBefore=n.getEndPoint=n.getStartPoint=n.updateRange=n.filterSelectionNodes=void 0;var r=e(2),f=e(137),g=r.__importDefault(e(3));function c(h){var A=[];return(0,o.default)(h).call(h,function(y){var x=y.getNodeName();if(x!==f.ListType.OrderedList&&x!==f.ListType.UnorderedList)A.push(y);else if(y.prior)A.push(y.prior);else{var b=y.children();b==null||(0,o.default)(b).call(b,function(E){A.push(g.default(E))})}}),A}n.filterSelectionNodes=c;function m(h,A,y){var x=h.selection,b=document.createRange();A.length>1?(b.setStart(A.elems[0],0),b.setEnd(A.elems[A.length-1],A.elems[A.length-1].childNodes.length)):b.selectNodeContents(A.elems[0]),y&&b.collapse(!1),x.saveRange(b),x.restoreSelection()}n.updateRange=m;function s(h){var A;return h.prior?h.prior:g.default((A=h.children())===null||A===void 0?void 0:A.elems[0])}n.getStartPoint=s;function a(h){var A;return h.prior?h.prior:g.default((A=h.children())===null||A===void 0?void 0:A.last().elems[0])}n.getEndPoint=a;function d(h,A,y){y===void 0&&(y=null),h.parent().elems[0].insertBefore(A,y)}n.insertBefore=d;function l(h){return document.createElement(h)}n.createElement=l;function v(){return document.createDocumentFragment()}n.createDocumentFragment=v;function p(h,A,y){return y===void 0&&(y="li"),(0,o.default)(h).call(h,function(x){var b=l(y);b.innerHTML=x.html(),A.appendChild(b),x.remove()}),A}n.createElementFragment=p},function(u,n){u.exports=function(e,t){return{enumerable:!(e&1),configurable:!(e&2),writable:!(e&4),value:t}}},function(u,n){u.exports=function(e){if(e==null)throw TypeError("Can't call method on "+e);return e}},function(u,n,e){var t=e(164).charAt,i=e(42),o=e(75),r="String Iterator",f=i.set,g=i.getterFor(r);o(String,"String",function(c){f(this,{type:r,string:String(c),index:0})},function(){var c=g(this),m=c.string,s=c.index,a;return s>=m.length?{value:void 0,done:!0}:(a=t(m,s),c.index+=a.length,{value:a,done:!1})})},function(u,n){u.exports={}},function(u,n,e){var t=e(107),i=e(80);u.exports=Object.keys||function(o){return t(o,i)}},function(u,n,e){var t=e(19);u.exports=function(i,o,r,f){f&&f.enumerable?i[o]=r:t(i,o,r)}},function(u,n,e){e(173);var t=e(174),i=e(8),o=e(65),r=e(19),f=e(44),g=e(10),c=g("toStringTag");for(var m in t){var s=i[m],a=s&&s.prototype;a&&o(a)!==c&&r(a,c,m),f[m]=f.Array}},function(u,n,e){var t=e(34);u.exports=Array.isArray||function(i){return t(i)=="Array"}},function(u,n,e){var t=e(11),i=e(10),o=e(86),r=i("species");u.exports=function(f){return o>=51||!t(function(){var g=[],c=g.constructor={};return c[r]=function(){return{foo:1}},g[f](Boolean).foo!==1})}},function(u,n,e){u.exports=e(222)},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0}),n.ListHandle=void 0;var o=e(2),r=o.__importDefault(e(373)),f=function(){function g(c){this.options=c,this.selectionRangeElem=new r.default}return g}();n.ListHandle=f},function(u,n,e){var t={}.propertyIsEnumerable,i=Object.getOwnPropertyDescriptor,o=i&&!t.call({1:2},1);n.f=o?function(r){var f=i(this,r);return!!f&&f.enumerable}:t},function(u,n,e){var t=e(13);u.exports=function(i,o){if(!t(i))return i;var r,f;if(o&&typeof(r=i.toString)=="function"&&!t(f=r.call(i))||typeof(r=i.valueOf)=="function"&&!t(f=r.call(i))||!o&&typeof(r=i.toString)=="function"&&!t(f=r.call(i)))return f;throw TypeError("Can't convert object to primitive value")}},function(u,n){},function(u,n){var e=Math.ceil,t=Math.floor;u.exports=function(i){return isNaN(i=+i)?0:(i>0?t:e)(i)}},function(u,n,e){var t=e(74),i=e(64),o=t("keys");u.exports=function(r){return o[r]||(o[r]=i(r))}},function(u,n){var e=0,t=Math.random();u.exports=function(i){return"Symbol("+String(i===void 0?"":i)+")_"+(++e+t).toString(36)}},function(u,n,e){var t=e(81),i=e(34),o=e(10),r=o("toStringTag"),f=i(function(){return arguments}())=="Arguments",g=function(c,m){try{return c[m]}catch{}};u.exports=t?i:function(c){var m,s,a;return c===void 0?"Undefined":c===null?"Null":typeof(s=g(m=Object(c),r))=="string"?s:f?i(m):(a=i(m))=="Object"&&typeof m.callee=="function"?"Arguments":a}},function(u,n,e){var t=e(25),i=e(112),o=e(35),r=e(40),f=e(113),g=e(114),c=function(s,a){this.stopped=s,this.result=a},m=u.exports=function(s,a,d,l,v){var p=r(a,d,l?2:1),h,A,y,x,b,E,C;if(v)h=s;else{if(A=f(s),typeof A!="function")throw TypeError("Target is not iterable");if(i(A)){for(y=0,x=o(s.length);x>y;y++)if(b=l?p(t(C=s[y])[0],C[1]):p(s[y]),b&&b instanceof c)return b;return new c(!1)}h=A.call(s)}for(E=h.next;!(C=E.call(h)).done;)if(b=g(h,p,C.value,l),typeof b=="object"&&b&&b instanceof c)return b;return new c(!1)};m.stop=function(s){return new c(!0,s)}},function(u,n,e){var t=e(11);u.exports=function(i,o){var r=[][i];return!!r&&t(function(){r.call(null,o||function(){throw 1},1)})}},function(u,n){u.exports=`	
\v\f\r                　\u2028\u2029\uFEFF`},function(u,n,e){var t=e(60),i=e(18),o=e(48);u.exports=function(r,f,g){var c=t(f);c in r?i.f(r,c,o(0,g)):r[c]=g}},function(u,n,e){u.exports=e(209)},function(u,n,e){var t=e(14),i=e(59),o=e(48),r=e(30),f=e(60),g=e(16),c=e(100),m=Object.getOwnPropertyDescriptor;n.f=t?m:function(s,a){if(s=r(s),a=f(a,!0),c)try{return m(s,a)}catch{}if(g(s,a))return o(!i.f.call(s,a),s[a])}},function(u,n,e){var t=e(11),i=e(34),o="".split;u.exports=t(function(){return!Object("z").propertyIsEnumerable(0)})?function(r){return i(r)=="String"?o.call(r,""):Object(r)}:Object},function(u,n,e){var t=e(8),i=e(13),o=t.document,r=i(o)&&i(o.createElement);u.exports=function(f){return r?o.createElement(f):{}}},function(u,n,e){var t=e(43),i=e(103);(u.exports=function(o,r){return i[o]||(i[o]=r!==void 0?r:{})})("versions",[]).push({version:"3.6.4",mode:t?"pure":"global",copyright:"© 2020 Denis Pushkarev (zloirock.ru)"})},function(u,n,e){var t=e(5),i=e(167),o=e(105),r=e(171),f=e(37),g=e(19),c=e(53),m=e(10),s=e(43),a=e(44),d=e(104),l=d.IteratorPrototype,v=d.BUGGY_SAFARI_ITERATORS,p=m("iterator"),h="keys",A="values",y="entries",x=function(){return this};u.exports=function(b,E,C,_,w,S,M){i(C,E,_);var D=function(O){if(O===w&&R)return R;if(!v&&O in T)return T[O];switch(O){case h:return function(){return new C(this,O)};case A:return function(){return new C(this,O)};case y:return function(){return new C(this,O)}}return function(){return new C(this)}},k=E+" Iterator",N=!1,T=b.prototype,B=T[p]||T["@@iterator"]||w&&T[w],R=!v&&B||D(w),I=E=="Array"&&T.entries||B,P,H,z;if(I&&(P=o(I.call(new b)),l!==Object.prototype&&P.next&&(!s&&o(P)!==l&&(r?r(P,l):typeof P[p]!="function"&&g(P,p,x)),f(P,k,!0,!0),s&&(a[k]=x))),w==A&&B&&B.name!==A&&(N=!0,R=function(){return B.call(this)}),(!s||M)&&T[p]!==R&&g(T,p,R),a[E]=R,w)if(H={values:D(A),keys:S?R:D(h),entries:D(y)},M)for(z in H)(v||N||!(z in T))&&c(T,z,H[z]);else t({target:E,proto:!0,forced:v||N},H);return H}},function(u,n,e){var t=e(11);u.exports=!!Object.getOwnPropertySymbols&&!t(function(){return!String(Symbol())})},function(u,n,e){var t=e(25),i=e(169),o=e(80),r=e(51),f=e(108),g=e(73),c=e(63),m=">",s="<",a="prototype",d="script",l=c("IE_PROTO"),v=function(){},p=function(b){return s+d+m+b+s+"/"+d+m},h=function(b){b.write(p("")),b.close();var E=b.parentWindow.Object;return b=null,E},A=function(){var b=g("iframe"),E="java"+d+":",C;return b.style.display="none",f.appendChild(b),b.src=String(E),C=b.contentWindow.document,C.open(),C.write(p("document.F=Object")),C.close(),C.F},y,x=function(){try{y=document.domain&&new ActiveXObject("htmlfile")}catch{}x=y?h(y):A();for(var b=o.length;b--;)delete x[a][o[b]];return x()};r[l]=!0,u.exports=Object.create||function(b,E){var C;return b!==null?(v[a]=t(b),C=new v,v[a]=null,C[l]=b):C=x(),E===void 0?C:i(C,E)}},function(u,n,e){var t=e(30),i=e(35),o=e(79),r=function(f){return function(g,c,m){var s=t(g),a=i(s.length),d=o(m,a),l;if(f&&c!=c){for(;a>d;)if(l=s[d++],l!=l)return!0}else for(;a>d;d++)if((f||d in s)&&s[d]===c)return f||d||0;return!f&&-1}};u.exports={includes:r(!0),indexOf:r(!1)}},function(u,n,e){var t=e(62),i=Math.max,o=Math.min;u.exports=function(r,f){var g=t(r);return g<0?i(g+f,0):o(g,f)}},function(u,n){u.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},function(u,n,e){var t=e(10),i=t("toStringTag"),o={};o[i]="z",u.exports=String(o)==="[object z]"},function(u,n){u.exports=function(){}},function(u,n){u.exports=function(e,t,i){if(!(e instanceof t))throw TypeError("Incorrect "+(i?i+" ":"")+"invocation");return e}},function(u,n,e){var t=e(36);u.exports=t("navigator","userAgent")||""},function(u,n,e){var t=e(41),i=function(o){var r,f;this.promise=new o(function(g,c){if(r!==void 0||f!==void 0)throw TypeError("Bad Promise constructor");r=g,f=c}),this.resolve=t(r),this.reject=t(f)};u.exports.f=function(o){return new i(o)}},function(u,n,e){var t=e(8),i=e(84),o=t.process,r=o&&o.versions,f=r&&r.v8,g,c;f?(g=f.split("."),c=g[0]+g[1]):i&&(g=i.match(/Edge\/(\d+)/),(!g||g[1]>=74)&&(g=i.match(/Chrome\/(\d+)/),g&&(c=g[1]))),u.exports=c&&+c},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(4));(0,i.default)(n,"__esModule",{value:!0});var r=e(2),f=r.__importDefault(e(3)),g=e(6),c=r.__importDefault(e(267)),m=r.__importDefault(e(280)),s=r.__importDefault(e(281)),a=r.__importDefault(e(282)),d=r.__importDefault(e(301)),l=r.__importStar(e(416)),v=r.__importDefault(e(417)),p=r.__importDefault(e(418)),h=r.__importDefault(e(419)),A=r.__importStar(e(420)),y=r.__importDefault(e(423)),x=r.__importDefault(e(424)),b=r.__importDefault(e(425)),E=r.__importDefault(e(427)),C=r.__importDefault(e(437)),_=r.__importDefault(e(440)),w=r.__importStar(e(441)),S=r.__importDefault(e(23)),M=r.__importDefault(e(134)),D=r.__importDefault(e(24)),k=r.__importDefault(e(33)),N=r.__importDefault(e(38)),T=r.__importDefault(e(39)),B=1,R=function(){function I(P,H){this.pluginsFunctionList={},this.beforeDestroyHooks=[],this.id="wangEditor-"+B++,this.toolbarSelector=P,this.textSelector=H,l.selectorValidator(this),this.config=g.deepClone(c.default),this.$toolbarElem=f.default("<div></div>"),this.$textContainerElem=f.default("<div></div>"),this.$textElem=f.default("<div></div>"),this.toolbarElemId="",this.textElemId="",this.isFocus=!1,this.isComposing=!1,this.isCompatibleMode=!1,this.selection=new m.default(this),this.cmd=new s.default(this),this.txt=new a.default(this),this.menus=new d.default(this),this.zIndex=new x.default,this.change=new b.default(this),this.history=new E.default(this),this.onSelectionChange=new _.default(this);var z=C.default(this),O=z.disable,$=z.enable;this.disable=O,this.enable=$,this.isEnable=!0}return I.prototype.initSelection=function(P){v.default(this,P)},I.prototype.create=function(){this.zIndex.init(this),this.isCompatibleMode=this.config.compatibleMode(),this.isCompatibleMode||(this.config.onchangeTimeout=30),h.default(this),l.default(this),this.txt.init(),this.menus.init(),A.default(this),this.initSelection(!0),p.default(this),this.change.observe(),this.history.observe(),w.default(this)},I.prototype.beforeDestroy=function(P){return this.beforeDestroyHooks.push(P),this},I.prototype.destroy=function(){var P,H=this;(0,o.default)(P=this.beforeDestroyHooks).call(P,function(z){return z.call(H)}),this.$toolbarElem.remove(),this.$textContainerElem.remove()},I.prototype.fullScreen=function(){A.setFullScreen(this)},I.prototype.unFullScreen=function(){A.setUnFullScreen(this)},I.prototype.scrollToHead=function(P){y.default(this,P)},I.registerMenu=function(P,H){!H||typeof H!="function"||(I.globalCustomMenuConstructorList[P]=H)},I.prototype.registerPlugin=function(P,H){w.registerPlugin(P,H,this.pluginsFunctionList)},I.registerPlugin=function(P,H){w.registerPlugin(P,H,I.globalPluginsFunctionList)},I.$=f.default,I.BtnMenu=S.default,I.DropList=M.default,I.DropListMenu=D.default,I.Panel=k.default,I.PanelMenu=N.default,I.Tooltip=T.default,I.globalCustomMenuConstructorList={},I.globalPluginsFunctionList={},I}();n.default=R},function(u,n,e){var t=e(13),i=e(55),o=e(10),r=o("species");u.exports=function(f,g){var c;return i(f)&&(c=f.constructor,typeof c=="function"&&(c===Array||i(c.prototype))?c=void 0:t(c)&&(c=c[r],c===null&&(c=void 0))),new(c===void 0?Array:c)(g===0?0:g)}},function(u,n,e){u.exports=e(185)},function(u,n,e){var t=e(49),i=e(68),o="["+i+"]",r=RegExp("^"+o+o+"*"),f=RegExp(o+o+"*$"),g=function(c){return function(m){var s=String(t(m));return c&1&&(s=s.replace(r,"")),c&2&&(s=s.replace(f,"")),s}};u.exports={start:g(1),end:g(2),trim:g(3)}},function(u,n,e){u.exports=e(205)},function(u,n,e){var t=e(227),i=e(230);function o(r){"@babel/helpers - typeof";return typeof i=="function"&&typeof t=="symbol"?u.exports=o=function(f){return typeof f}:u.exports=o=function(f){return f&&typeof i=="function"&&f.constructor===i&&f!==i.prototype?"symbol":typeof f},o(r)}u.exports=o},function(u,n,e){var t=e(10);n.f=t},function(u,n,e){u.exports=e(306)},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(4));(0,i.default)(n,"__esModule",{value:!0});var r=e(2),f=r.__importDefault(e(33)),g=function(){function c(m,s){var a=this;this.$elem=m,this.editor=s,this._active=!1,m.on("click",function(d){var l;f.default.hideCurAllPanels(),(0,o.default)(l=s.txt.eventHooks.menuClickEvents).call(l,function(v){return v()}),d.stopPropagation(),s.selection.getRange()!=null&&a.clickHandler(d)})}return c.prototype.clickHandler=function(m){},c.prototype.active=function(){this._active=!0,this.$elem.addClass("w-e-active")},c.prototype.unActive=function(){this._active=!1,this.$elem.removeClass("w-e-active")},(0,i.default)(c.prototype,"isActive",{get:function(){return this._active},enumerable:!1,configurable:!0}),c}();n.default=g},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(28));(0,i.default)(n,"__esModule",{value:!0}),n.getParentNodeA=n.EXTRA_TAG=void 0,n.EXTRA_TAG=["B","FONT","I","STRIKE"];function r(g){for(var c=g.elems[0];c&&(0,o.default)(m=n.EXTRA_TAG).call(m,c.nodeName);){var m;if(c=c.parentElement,c.nodeName==="A")return c}}n.getParentNodeA=r;function f(g){var c,m=g.selection.getSelectionContainerElem();if(!(!((c=m==null?void 0:m.elems)===null||c===void 0)&&c.length))return!1;if(m.getNodeName()==="A")return!0;var s=r(m);return!!(s&&s.nodeName==="A")}n.default=f},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(57)),r=t(e(4)),f=t(e(27));(0,i.default)(n,"__esModule",{value:!0});var g=e(2),c=e(6),m=g.__importDefault(e(135)),s=g.__importDefault(e(136)),a=function(){function d(l){this.editor=l}return d.prototype.insertImg=function(l,v,p){var h=this.editor,A=h.config,y="validate.",x=function(w,S){return S===void 0&&(S=y),h.i18next.t(S+w)},b=l.replace(/</g,"&lt;").replace(/>/g,"&gt;");b=b.replace("'",'"');var E="";p&&(E=p.replace("'",'"'),E="data-href='"+encodeURIComponent(E)+"' ");var C="";v&&(C=v.replace(/</g,"&lt;").replace(/>/g,"&gt;"),C=C.replace("'",'"'),C="alt='"+C+"' "),h.cmd.do("insertHTML","<img src='"+b+"' "+C+E+'style="max-width:100%;" contenteditable="false"/>'),A.linkImgCallback(l,v,p);var _=document.createElement("img");_.onload=function(){_=null},_.onerror=function(){A.customAlert(x("插入图片错误"),"error","wangEditor: "+x("插入图片错误")+"，"+x("图片链接")+' "'+l+'"，'+x("下载链接失败")),_=null},_.onabort=function(){return _=null},_.src=l},d.prototype.uploadImg=function(l){var v=this;if(l.length){var p=this.editor,h=p.config,A="validate.",y=function(L){return p.i18next.t(A+L)},x=h.uploadImgServer,b=h.uploadImgShowBase64,E=h.uploadImgMaxSize,C=E/1024/1024,_=h.uploadImgMaxLength,w=h.uploadFileName,S=h.uploadImgParams,M=h.uploadImgParamsWithUrl,D=h.uploadImgHeaders,k=h.uploadImgHooks,N=h.uploadImgTimeout,T=h.withCredentials,B=h.customUploadImg;if(!(!B&&!x&&!b)){var R=[],I=[];if(c.arrForEach(l,function(L){if(L){var U=L.name||L.type.replace("/","."),Y=L.size;if(!(!U||!Y)){var G=p.config.uploadImgAccept.join("|"),q=".("+G+")$",ee=new RegExp(q,"i");if(ee.test(U)===!1){I.push("【"+U+"】"+y("不是图片"));return}if(E<Y){I.push("【"+U+"】"+y("大于")+" "+C+"M");return}R.push(L)}}}),I.length){h.customAlert(y("图片验证未通过")+`: 
`+I.join(`
`),"warning");return}if(R.length===0){h.customAlert(y("传入的文件不合法"),"warning");return}if(R.length>_){h.customAlert(y("一次最多上传")+_+y("张图片"),"warning");return}if(B&&typeof B=="function"){var P;B(R,(0,o.default)(P=this.insertImg).call(P,this));return}var H=new FormData;if((0,r.default)(R).call(R,function(L,U){var Y=w||L.name;R.length>1&&(Y=Y+(U+1)),H.append(Y,L)}),x){var z=x.split("#");x=z[0];var O=z[1]||"";(0,r.default)(c).call(c,S,function(L,U){M&&((0,f.default)(x).call(x,"?")>0?x+="&":x+="?",x=x+L+"="+U),H.append(L,U)}),O&&(x+="#"+O);var $=m.default(x,{timeout:N,formData:H,headers:D,withCredentials:!!T,beforeSend:function(L){if(k.before)return k.before(L,p,R)},onTimeout:function(L){h.customAlert(y("上传图片超时"),"error"),k.timeout&&k.timeout(L,p)},onProgress:function(L,U){var Y=new s.default(p);U.lengthComputable&&(L=U.loaded/U.total,Y.show(L))},onError:function(L){h.customAlert(y("上传图片错误"),"error",y("上传图片错误")+"，"+y("服务器返回状态")+": "+L.status),k.error&&k.error(L,p)},onFail:function(L,U){h.customAlert(y("上传图片失败"),"error",y("上传图片返回结果错误")+("，"+y("返回结果")+": ")+U),k.fail&&k.fail(L,p,U)},onSuccess:function(L,U){if(k.customInsert){var Y;k.customInsert((0,o.default)(Y=v.insertImg).call(Y,v),U,p);return}if(U.errno!="0"){h.customAlert(y("上传图片失败"),"error",y("上传图片返回结果错误")+"，"+y("返回结果")+" errno="+U.errno),k.fail&&k.fail(L,p,U);return}var G=U.data;(0,r.default)(G).call(G,function(q){typeof q=="string"?v.insertImg(q):v.insertImg(q.url,q.alt,q.href)}),k.success&&k.success(L,p,U)}});typeof $=="string"&&h.customAlert($,"error");return}b&&c.arrForEach(l,function(L){var U=v,Y=new FileReader;Y.readAsDataURL(L),Y.onload=function(){if(this.result){var G=this.result.toString();U.insertImg(G,G)}}})}}},d}();n.default=a},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(410)),r=t(e(4)),f=t(e(45));(0,i.default)(n,"__esModule",{value:!0}),n.dealTextNode=n.isAllTodo=n.isTodo=n.getCursorNextNode=void 0;function g(d){return d.length?d.attr("class")==="w-e-todo":!1}n.isTodo=g;function c(d){var l=d.selection.getSelectionRangeTopNodes();if(l.length!==0)return(0,o.default)(l).call(l,function(v){return g(v)})}n.isAllTodo=c;function m(d,l,v){var p;if(d.hasChildNodes()){var h=d.cloneNode(),A=!1;l.nodeValue===""&&(A=!0);var y=[];return(0,r.default)(p=d.childNodes).call(p,function(x){if(!s(x,l)&&A&&(h.appendChild(x.cloneNode(!0)),x.nodeName!=="BR"&&y.push(x)),s(x,l)){if(x.nodeType===1){var b=m(x,l,v);b&&b.textContent!==""&&(h==null||h.appendChild(b))}if(x.nodeType===3&&l.isEqualNode(x)){var E=a(x,v);h.textContent=E}A=!0}}),(0,r.default)(y).call(y,function(x){var b=x;b.remove()}),h}}n.getCursorNextNode=m;function s(d,l){return d.nodeType===3?d.nodeValue===l.nodeValue:d.contains(l)}function a(d,l,v){v===void 0&&(v=!0);var p=d.nodeValue,h=p==null?void 0:(0,f.default)(p).call(p,0,l);if(p=p==null?void 0:(0,f.default)(p).call(p,l),!v){var A=p;p=h,h=A}return d.nodeValue=h,p}n.dealTextNode=a},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(430),r=function(){function f(g){this.maxSize=g,this.isRe=!1,this.data=new o.CeilStack(g),this.revokeData=new o.CeilStack(g)}return(0,i.default)(f.prototype,"size",{get:function(){return[this.data.size,this.revokeData.size]},enumerable:!1,configurable:!0}),f.prototype.resetMaxSize=function(g){this.data.resetMax(g),this.revokeData.resetMax(g)},f.prototype.save=function(g){return this.isRe&&(this.revokeData.clear(),this.isRe=!1),this.data.instack(g),this},f.prototype.revoke=function(g){!this.isRe&&(this.isRe=!0);var c=this.data.outstack();return c?(this.revokeData.instack(c),g(c),!0):!1},f.prototype.restore=function(g){!this.isRe&&(this.isRe=!0);var c=this.revokeData.outstack();return c?(this.data.instack(c),g(c),!0):!1},f}();n.default=r},function(u,n,e){var t=e(14),i=e(11),o=e(73);u.exports=!t&&!i(function(){return Object.defineProperty(o("div"),"a",{get:function(){return 7}}).a!=7})},function(u,n,e){var t=e(11),i=/#|\.prototype\./,o=function(m,s){var a=f[r(m)];return a==c?!0:a==g?!1:typeof s=="function"?t(s):!!s},r=o.normalize=function(m){return String(m).replace(i,".").toLowerCase()},f=o.data={},g=o.NATIVE="N",c=o.POLYFILL="P";u.exports=o},function(u,n,e){var t=e(103),i=Function.toString;typeof t.inspectSource!="function"&&(t.inspectSource=function(o){return i.call(o)}),u.exports=t.inspectSource},function(u,n,e){var t=e(8),i=e(166),o="__core-js_shared__",r=t[o]||i(o,{});u.exports=r},function(u,n,e){var t=e(105),i=e(19),o=e(16),r=e(10),f=e(43),g=r("iterator"),c=!1,m=function(){return this},s,a,d;[].keys&&(d=[].keys(),"next"in d?(a=t(t(d)),a!==Object.prototype&&(s=a)):c=!0),s==null&&(s={}),!f&&!o(s,g)&&i(s,g,m),u.exports={IteratorPrototype:s,BUGGY_SAFARI_ITERATORS:c}},function(u,n,e){var t=e(16),i=e(31),o=e(63),r=e(168),f=o("IE_PROTO"),g=Object.prototype;u.exports=r?Object.getPrototypeOf:function(c){return c=i(c),t(c,f)?c[f]:typeof c.constructor=="function"&&c instanceof c.constructor?c.constructor.prototype:c instanceof Object?g:null}},function(u,n,e){var t=e(76);u.exports=t&&!Symbol.sham&&typeof Symbol.iterator=="symbol"},function(u,n,e){var t=e(16),i=e(30),o=e(78).indexOf,r=e(51);u.exports=function(f,g){var c=i(f),m=0,s=[],a;for(a in c)!t(r,a)&&t(c,a)&&s.push(a);for(;g.length>m;)t(c,a=g[m++])&&(~o(s,a)||s.push(a));return s}},function(u,n,e){var t=e(36);u.exports=t("document","documentElement")},function(u,n,e){var t=e(8);u.exports=t.Promise},function(u,n,e){var t=e(53);u.exports=function(i,o,r){for(var f in o)r&&r.unsafe&&i[f]?i[f]=o[f]:t(i,f,o[f],r);return i}},function(u,n,e){var t=e(36),i=e(18),o=e(10),r=e(14),f=o("species");u.exports=function(g){var c=t(g),m=i.f;r&&c&&!c[f]&&m(c,f,{configurable:!0,get:function(){return this}})}},function(u,n,e){var t=e(10),i=e(44),o=t("iterator"),r=Array.prototype;u.exports=function(f){return f!==void 0&&(i.Array===f||r[o]===f)}},function(u,n,e){var t=e(65),i=e(44),o=e(10),r=o("iterator");u.exports=function(f){if(f!=null)return f[r]||f["@@iterator"]||i[t(f)]}},function(u,n,e){var t=e(25);u.exports=function(i,o,r,f){try{return f?o(t(r)[0],r[1]):o(r)}catch(c){var g=i.return;throw g!==void 0&&t(g.call(i)),c}}},function(u,n,e){var t=e(10),i=t("iterator"),o=!1;try{var r=0,f={next:function(){return{done:!!r++}},return:function(){o=!0}};f[i]=function(){return this},Array.from(f,function(){throw 2})}catch{}u.exports=function(g,c){if(!c&&!o)return!1;var m=!1;try{var s={};s[i]=function(){return{next:function(){return{done:m=!0}}}},g(s)}catch{}return m}},function(u,n,e){var t=e(25),i=e(41),o=e(10),r=o("species");u.exports=function(f,g){var c=t(f).constructor,m;return c===void 0||(m=t(c)[r])==null?g:i(m)}},function(u,n,e){var t=e(8),i=e(11),o=e(34),r=e(40),f=e(108),g=e(73),c=e(118),m=t.location,s=t.setImmediate,a=t.clearImmediate,d=t.process,l=t.MessageChannel,v=t.Dispatch,p=0,h={},A="onreadystatechange",y,x,b,E=function(S){if(h.hasOwnProperty(S)){var M=h[S];delete h[S],M()}},C=function(S){return function(){E(S)}},_=function(S){E(S.data)},w=function(S){t.postMessage(S+"",m.protocol+"//"+m.host)};(!s||!a)&&(s=function(S){for(var M=[],D=1;arguments.length>D;)M.push(arguments[D++]);return h[++p]=function(){(typeof S=="function"?S:Function(S)).apply(void 0,M)},y(p),p},a=function(S){delete h[S]},o(d)=="process"?y=function(S){d.nextTick(C(S))}:v&&v.now?y=function(S){v.now(C(S))}:l&&!c?(x=new l,b=x.port2,x.port1.onmessage=_,y=r(b.postMessage,b,1)):t.addEventListener&&typeof postMessage=="function"&&!t.importScripts&&!i(w)&&m.protocol!=="file:"?(y=w,t.addEventListener("message",_,!1)):A in g("script")?y=function(S){f.appendChild(g("script"))[A]=function(){f.removeChild(this),E(S)}}:y=function(S){setTimeout(C(S),0)}),u.exports={set:s,clear:a}},function(u,n,e){var t=e(84);u.exports=/(iphone|ipod|ipad).*applewebkit/i.test(t)},function(u,n,e){var t=e(25),i=e(13),o=e(85);u.exports=function(r,f){if(t(r),i(f)&&f.constructor===r)return f;var g=o.f(r),c=g.resolve;return c(f),g.promise}},function(u,n){u.exports=function(e){try{return{error:!1,value:e()}}catch(t){return{error:!0,value:t}}}},function(u,n,e){u.exports=e(197)},function(u,n,e){var t=e(5),i=e(8),o=e(123),r=e(11),f=e(19),g=e(66),c=e(83),m=e(13),s=e(37),a=e(18).f,d=e(32).forEach,l=e(14),v=e(42),p=v.set,h=v.getterFor;u.exports=function(A,y,x){var b=A.indexOf("Map")!==-1,E=A.indexOf("Weak")!==-1,C=b?"set":"add",_=i[A],w=_&&_.prototype,S={},M;if(!l||typeof _!="function"||!(E||w.forEach&&!r(function(){new _().entries().next()})))M=x.getConstructor(y,A,b,C),o.REQUIRED=!0;else{M=y(function(k,N){p(c(k,M,A),{type:A,collection:new _}),N!=null&&g(N,k[C],k,b)});var D=h(A);d(["add","clear","delete","forEach","get","has","set","keys","values","entries"],function(k){var N=k=="add"||k=="set";k in w&&!(E&&k=="clear")&&f(M.prototype,k,function(T,B){var R=D(this).collection;if(!N&&E&&!m(T))return k=="get"?void 0:!1;var I=R[k](T===0?0:T,B);return N?this:I})}),E||a(M.prototype,"size",{configurable:!0,get:function(){return D(this).collection.size}})}return s(M,A,!1,!0),S[A]=M,t({global:!0,forced:!0},S),E||x.setStrong(M,A,b),M}},function(u,n,e){var t=e(51),i=e(13),o=e(16),r=e(18).f,f=e(64),g=e(200),c=f("meta"),m=0,s=Object.isExtensible||function(){return!0},a=function(h){r(h,c,{value:{objectID:"O"+ ++m,weakData:{}}})},d=function(h,A){if(!i(h))return typeof h=="symbol"?h:(typeof h=="string"?"S":"P")+h;if(!o(h,c)){if(!s(h))return"F";if(!A)return"E";a(h)}return h[c].objectID},l=function(h,A){if(!o(h,c)){if(!s(h))return!0;if(!A)return!1;a(h)}return h[c].weakData},v=function(h){return g&&p.REQUIRED&&s(h)&&!o(h,c)&&a(h),h},p=u.exports={REQUIRED:!1,fastKey:d,getWeakData:l,onFreeze:v};t[c]=!0},function(u,n,e){var t=e(18).f,i=e(77),o=e(110),r=e(40),f=e(83),g=e(66),c=e(75),m=e(111),s=e(14),a=e(123).fastKey,d=e(42),l=d.set,v=d.getterFor;u.exports={getConstructor:function(p,h,A,y){var x=p(function(_,w){f(_,x,h),l(_,{type:h,index:i(null),first:void 0,last:void 0,size:0}),s||(_.size=0),w!=null&&g(w,_[y],_,A)}),b=v(h),E=function(_,w,S){var M=b(_),D=C(_,w),k,N;return D?D.value=S:(M.last=D={index:N=a(w,!0),key:w,value:S,previous:k=M.last,next:void 0,removed:!1},M.first||(M.first=D),k&&(k.next=D),s?M.size++:_.size++,N!=="F"&&(M.index[N]=D)),_},C=function(_,w){var S=b(_),M=a(w),D;if(M!=="F")return S.index[M];for(D=S.first;D;D=D.next)if(D.key==w)return D};return o(x.prototype,{clear:function(){for(var _=this,w=b(_),S=w.index,M=w.first;M;)M.removed=!0,M.previous&&(M.previous=M.previous.next=void 0),delete S[M.index],M=M.next;w.first=w.last=void 0,s?w.size=0:_.size=0},delete:function(_){var w=this,S=b(w),M=C(w,_);if(M){var D=M.next,k=M.previous;delete S.index[M.index],M.removed=!0,k&&(k.next=D),D&&(D.previous=k),S.first==M&&(S.first=D),S.last==M&&(S.last=k),s?S.size--:w.size--}return!!M},forEach:function(_){for(var w=b(this),S=r(_,arguments.length>1?arguments[1]:void 0,3),M;M=M?M.next:w.first;)for(S(M.value,M.key,this);M&&M.removed;)M=M.previous},has:function(_){return!!C(this,_)}}),o(x.prototype,A?{get:function(_){var w=C(this,_);return w&&w.value},set:function(_,w){return E(this,_===0?0:_,w)}}:{add:function(_){return E(this,_=_===0?0:_,_)}}),s&&t(x.prototype,"size",{get:function(){return b(this).size}}),x},setStrong:function(p,h,A){var y=h+" Iterator",x=v(h),b=v(y);c(p,h,function(E,C){l(this,{type:y,target:E,state:x(E),kind:C,last:void 0})},function(){for(var E=b(this),C=E.kind,_=E.last;_&&_.removed;)_=_.previous;return!E.target||!(E.last=_=_?_.next:E.state.first)?(E.target=void 0,{value:void 0,done:!0}):C=="keys"?{value:_.key,done:!1}:C=="values"?{value:_.value,done:!1}:{value:[_.key,_.value],done:!1}},A?"entries":"values",!A,!0),m(h)}}},function(u,n,e){var t=e(12);t("iterator")},function(u,n,e){var t=e(107),i=e(80),o=i.concat("length","prototype");n.f=Object.getOwnPropertyNames||function(r){return t(r,o)}},function(u,n){n.f=Object.getOwnPropertySymbols},function(u,n,e){u.exports=e(268)},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0}),n.default={zIndex:1e4}},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0}),n.default={focus:!0,height:300,placeholder:"请输入正文",zIndexFullScreen:10002,showFullScreen:!0}},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(4));(0,i.default)(n,"__esModule",{value:!0}),n.getPasteImgs=n.getPasteHtml=n.getPasteText=void 0;var r=e(2),f=e(6),g=r.__importDefault(e(292));function c(a){var d=a.clipboardData,l="";return d==null?l=window.clipboardData&&window.clipboardData.getData("text"):l=d.getData("text/plain"),f.replaceHtmlSymbol(l)}n.getPasteText=c;function m(a,d,l){d===void 0&&(d=!0),l===void 0&&(l=!1);var v=a.clipboardData,p="";if(v&&(p=v.getData("text/html")),!p){var h=c(a);if(!h)return"";p="<p>"+h+"</p>"}return p=p.replace(/<(\d)/gm,function(A,y){return"&lt;"+y}),p=p.replace(/<(\/?meta.*?)>/gim,""),p=g.default(p,d,l),p}n.getPasteHtml=m;function s(a){var d,l=[],v=c(a);if(v)return l;var p=(d=a.clipboardData)===null||d===void 0?void 0:d.items;return p&&(0,o.default)(f).call(f,p,function(h,A){var y=A.type;/image/i.test(y)&&l.push(A.getAsFile())}),l}n.getPasteImgs=s},function(u,n,e){u.exports=e(294)},function(u,n,e){u.exports=e(310)},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(4)),r=t(e(46));(0,i.default)(n,"__esModule",{value:!0});var f=e(2),g=f.__importDefault(e(3)),c=e(7),m=function(){function s(a,d){var l=this;this.hideTimeoutId=0,this.menu=a,this.conf=d;var v=g.default('<div class="w-e-droplist"></div>'),p=g.default("<p>"+d.title+"</p>");p.addClass("w-e-dp-title"),v.append(p);var h=d.list||[],A=d.type||"list",y=d.clickHandler||c.EMPTY_FN,x=g.default('<ul class="'+(A==="list"?"w-e-list":"w-e-block")+'"></ul>');(0,o.default)(h).call(h,function(b){var E=b.$elem,C=b.value,_=g.default('<li class="w-e-item"></li>');E&&(_.append(E),x.append(_),_.on("click",function(w){y(C),w.stopPropagation(),l.hideTimeoutId=(0,r.default)(function(){l.hide()})}))}),v.append(x),v.on("mouseleave",function(){l.hideTimeoutId=(0,r.default)(function(){l.hide()})}),this.$container=v,this.rendered=!1,this._show=!1}return s.prototype.show=function(){this.hideTimeoutId&&clearTimeout(this.hideTimeoutId);var a=this.menu,d=a.$elem,l=this.$container;if(!this._show){if(this.rendered)l.show();else{var v=d.getBoundingClientRect().height||0,p=this.conf.width||100;l.css("margin-top",v+"px").css("width",p+"px"),d.append(l),this.rendered=!0}this._show=!0}},s.prototype.hide=function(){var a=this.$container;this._show&&(a.hide(),this._show=!1)},(0,i.default)(s.prototype,"isShow",{get:function(){return this._show},enumerable:!1,configurable:!0}),s}();n.default=m},function(u,n,e){var t=e(0),i=t(e(92)),o=t(e(1)),r=t(e(4));(0,o.default)(n,"__esModule",{value:!0});var f=e(6);function g(c,m){var s=new XMLHttpRequest;if(s.open("POST",c),s.timeout=m.timeout||10*1e3,s.ontimeout=function(){m.onTimeout&&m.onTimeout(s)},s.upload&&(s.upload.onprogress=function(d){var l=d.loaded/d.total;m.onProgress&&m.onProgress(l,d)}),m.headers&&(0,r.default)(f).call(f,m.headers,function(d,l){s.setRequestHeader(d,l)}),s.withCredentials=!!m.withCredentials,m.beforeSend){var a=m.beforeSend(s);if(a&&(0,i.default)(a)==="object"&&a.prevent)return a.msg}return s.onreadystatechange=function(){if(s.readyState===4){var d=s.status;if(!(d<200)&&!(d>=300&&d<400)){if(d>=400){m.onError&&m.onError(s);return}var l=s.responseText,v;if((0,i.default)(l)!=="object")try{v=JSON.parse(l)}catch{m.onFail&&m.onFail(s,l);return}else v=l;m.onSuccess(s,v)}}},s.send(m.formData||null),s}n.default=g},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(342)),r=t(e(46));(0,i.default)(n,"__esModule",{value:!0});var f=e(2),g=f.__importDefault(e(3)),c=function(){function m(s){this.editor=s,this.$textContainer=s.$textContainerElem,this.$bar=g.default('<div class="w-e-progress"></div>'),this.isShow=!1,this.time=0,this.timeoutId=0}return m.prototype.show=function(s){var a=this;if(!this.isShow){this.isShow=!0;var d=this.$bar,l=this.$textContainer;l.append(d),(0,o.default)()-this.time>100&&s<=1&&(d.css("width",s*100+"%"),this.time=(0,o.default)());var v=this.timeoutId;v&&clearTimeout(v),this.timeoutId=(0,r.default)(function(){a.hide()},500)}},m.prototype.hide=function(){var s=this.$bar;s.remove(),this.isShow=!1,this.time=0,this.timeoutId=0},m}();n.default=c},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0}),n.ListType=void 0;var o=e(2),r=o.__importDefault(e(3)),f=o.__importDefault(e(24)),g=e(47),c=o.__importStar(e(371)),m;(function(a){a.OrderedList="OL",a.UnorderedList="UL"})(m=n.ListType||(n.ListType={}));var s=function(a){o.__extends(d,a);function d(l){var v=this,p=r.default(`<div class="w-e-menu" data-title="序列">
                <i class="w-e-icon-list2"></i>
            </div>`),h={width:130,title:"序列",type:"list",list:[{$elem:r.default(`
                        <p>
                            <i class="w-e-icon-list2 w-e-drop-list-item"></i>
                            `+l.i18next.t("menus.dropListMenu.list.无序列表")+`
                        <p>`),value:m.UnorderedList},{$elem:r.default(`<p>
                            <i class="w-e-icon-list-numbered w-e-drop-list-item"></i>
                            `+l.i18next.t("menus.dropListMenu.list.有序列表")+`
                        <p>`),value:m.OrderedList}],clickHandler:function(A){v.command(A)}};return v=a.call(this,p,l,h)||this,v}return d.prototype.command=function(l){var v=this.editor,p=v.selection.getSelectionContainerElem();p!==void 0&&(this.handleSelectionRangeNodes(l),this.tryChangeActive())},d.prototype.validator=function(l,v,p){return!(!l.length||!v.length||p.equal(l)||p.equal(v))},d.prototype.handleSelectionRangeNodes=function(l){var v=this.editor,p=v.selection,h=l.toLowerCase(),A=p.getSelectionContainerElem(),y=p.getSelectionStartElem().getNodeTop(v),x=p.getSelectionEndElem().getNodeTop(v);if(this.validator(y,x,v.$textElem)){var b=p.getRange(),E=b==null?void 0:b.collapsed;v.$textElem.equal(A)||(A=A.getNodeTop(v));var C={editor:v,listType:l,listTarget:h,$selectionElem:A,$startElem:y,$endElem:x},_;this.isOrderElem(A)?_=c.ClassType.Wrap:this.isOrderElem(y)&&this.isOrderElem(x)?_=c.ClassType.Join:this.isOrderElem(y)?_=c.ClassType.StartJoin:this.isOrderElem(x)?_=c.ClassType.EndJoin:_=c.ClassType.Other;var w=new c.default(c.createListHandle(_,C,b));g.updateRange(v,w.getSelectionRangeElem(),!!E)}},d.prototype.isOrderElem=function(l){var v=l.getNodeName();return v===m.OrderedList||v===m.UnorderedList},d.prototype.tryChangeActive=function(){},d}(f.default);n.default=s},function(u,n,e){u.exports=e(395)},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});function o(r){var f=r.selection.getSelectionContainerElem();return f!=null&&f.length?!!(f.getNodeName()=="CODE"||f.getNodeName()=="PRE"||f.parent().getNodeName()=="CODE"||f.parent().getNodeName()=="PRE"||/hljs/.test(f.parent().attr("class"))):!1}n.default=o},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(29));(0,i.default)(n,"__esModule",{value:!0}),n.todo=void 0;var r=e(2),f=r.__importDefault(e(3)),g=function(){function m(s){var a;this.template='<ul class="w-e-todo"><li><span contenteditable="false"><input type="checkbox"></span></li></ul>',this.checked=!1,this.$todo=f.default(this.template),this.$child=(a=s==null?void 0:s.childNodes())===null||a===void 0?void 0:a.clone(!0)}return m.prototype.init=function(){var s=this.$child,a=this.getInputContainer();s&&s.insertAfter(a)},m.prototype.getInput=function(){var s=this.$todo,a=(0,o.default)(s).call(s,"input");return a},m.prototype.getInputContainer=function(){var s=this.getInput().parent();return s},m.prototype.getTodo=function(){return this.$todo},m}();n.todo=g;function c(m){var s=new g(m);return s.init(),s}n.default=c},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(2);e(146),e(148),e(152),e(154),e(156),e(158),e(160);var r=o.__importDefault(e(87));o.__exportStar(e(442),n),n.default=r.default},function(u,n,e){var t=e(143);u.exports=t},function(u,n,e){e(144);var t=e(9),i=t.Object,o=u.exports=function(r,f,g){return i.defineProperty(r,f,g)};i.defineProperty.sham&&(o.sham=!0)},function(u,n,e){var t=e(5),i=e(14),o=e(18);t({target:"Object",stat:!0,forced:!i,sham:!i},{defineProperty:o.f})},function(u,n){var e;e=function(){return this}();try{e=e||new Function("return this")()}catch{typeof window=="object"&&(e=window)}u.exports=e},function(u,n,e){var t=e(20),i=e(147);i=i.__esModule?i.default:i,typeof i=="string"&&(i=[[u.i,i,""]]);var o={};o.insert="head",o.singleton=!1,t(i,o),u.exports=i.locals||{}},function(u,n,e){var t=e(21);n=t(!1),n.push([u.i,`.w-e-toolbar,
.w-e-text-container,
.w-e-menu-panel {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  background-color: #fff;
  /*表情菜单样式*/
  /*分割线样式*/
}
.w-e-toolbar h1,
.w-e-text-container h1,
.w-e-menu-panel h1 {
  font-size: 32px !important;
}
.w-e-toolbar h2,
.w-e-text-container h2,
.w-e-menu-panel h2 {
  font-size: 24px !important;
}
.w-e-toolbar h3,
.w-e-text-container h3,
.w-e-menu-panel h3 {
  font-size: 18.72px !important;
}
.w-e-toolbar h4,
.w-e-text-container h4,
.w-e-menu-panel h4 {
  font-size: 16px !important;
}
.w-e-toolbar h5,
.w-e-text-container h5,
.w-e-menu-panel h5 {
  font-size: 13.28px !important;
}
.w-e-toolbar p,
.w-e-text-container p,
.w-e-menu-panel p {
  font-size: 16px !important;
}
.w-e-toolbar .eleImg,
.w-e-text-container .eleImg,
.w-e-menu-panel .eleImg {
  cursor: pointer;
  display: inline-block;
  font-size: 18px;
  padding: 0 3px;
}
.w-e-toolbar *,
.w-e-text-container *,
.w-e-menu-panel * {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}
.w-e-toolbar hr,
.w-e-text-container hr,
.w-e-menu-panel hr {
  cursor: pointer;
  display: block;
  height: 0px;
  border: 0;
  border-top: 3px solid #ccc;
  margin: 20px 0;
}
.w-e-clear-fix:after {
  content: "";
  display: table;
  clear: both;
}
.w-e-drop-list-item {
  position: relative;
  top: 1px;
  padding-right: 7px;
  color: #333 !important;
}
.w-e-drop-list-tl {
  padding-left: 10px;
  text-align: left;
}
`,""]),u.exports=n},function(u,n,e){var t=e(20),i=e(149);i=i.__esModule?i.default:i,typeof i=="string"&&(i=[[u.i,i,""]]);var o={};o.insert="head",o.singleton=!1,t(i,o),u.exports=i.locals||{}},function(u,n,e){var t=e(21),i=e(150),o=e(151);n=t(!1);var r=i(o);n.push([u.i,`@font-face {
  font-family: 'w-e-icon';
  src: url(`+r+`) format('truetype');
  font-weight: normal;
  font-style: normal;
}
[class^="w-e-icon-"],
[class*=" w-e-icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'w-e-icon' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.w-e-icon-close:before {
  content: "\\f00d";
}
.w-e-icon-upload2:before {
  content: "\\e9c6";
}
.w-e-icon-trash-o:before {
  content: "\\f014";
}
.w-e-icon-header:before {
  content: "\\f1dc";
}
.w-e-icon-pencil2:before {
  content: "\\e906";
}
.w-e-icon-paint-brush:before {
  content: "\\f1fc";
}
.w-e-icon-image:before {
  content: "\\e90d";
}
.w-e-icon-play:before {
  content: "\\e912";
}
.w-e-icon-location:before {
  content: "\\e947";
}
.w-e-icon-undo:before {
  content: "\\e965";
}
.w-e-icon-redo:before {
  content: "\\e966";
}
.w-e-icon-quotes-left:before {
  content: "\\e977";
}
.w-e-icon-list-numbered:before {
  content: "\\e9b9";
}
.w-e-icon-list2:before {
  content: "\\e9bb";
}
.w-e-icon-link:before {
  content: "\\e9cb";
}
.w-e-icon-happy:before {
  content: "\\e9df";
}
.w-e-icon-bold:before {
  content: "\\ea62";
}
.w-e-icon-underline:before {
  content: "\\ea63";
}
.w-e-icon-italic:before {
  content: "\\ea64";
}
.w-e-icon-strikethrough:before {
  content: "\\ea65";
}
.w-e-icon-table2:before {
  content: "\\ea71";
}
.w-e-icon-paragraph-left:before {
  content: "\\ea77";
}
.w-e-icon-paragraph-center:before {
  content: "\\ea78";
}
.w-e-icon-paragraph-right:before {
  content: "\\ea79";
}
.w-e-icon-paragraph-justify:before {
  content: "\\ea7a";
}
.w-e-icon-terminal:before {
  content: "\\f120";
}
.w-e-icon-page-break:before {
  content: "\\ea68";
}
.w-e-icon-cancel-circle:before {
  content: "\\ea0d";
}
.w-e-icon-font:before {
  content: "\\ea5c";
}
.w-e-icon-text-heigh:before {
  content: "\\ea5f";
}
.w-e-icon-paint-format:before {
  content: "\\e90c";
}
.w-e-icon-indent-increase:before {
  content: "\\ea7b";
}
.w-e-icon-indent-decrease:before {
  content: "\\ea7c";
}
.w-e-icon-row-height:before {
  content: "\\e9be";
}
.w-e-icon-fullscreen_exit:before {
  content: "\\e900";
}
.w-e-icon-fullscreen:before {
  content: "\\e901";
}
.w-e-icon-split-line:before {
  content: "\\ea0b";
}
.w-e-icon-checkbox-checked:before {
  content: "\\ea52";
}
`,""]),u.exports=n},function(u,n,e){u.exports=function(t,i){return i||(i={}),t=t&&t.__esModule?t.default:t,typeof t!="string"?t:(/^['"].*['"]$/.test(t)&&(t=t.slice(1,-1)),i.hash&&(t+=i.hash),/["'() \t\n]/.test(t)||i.needQuotes?'"'.concat(t.replace(/"/g,'\\"').replace(/\n/g,"\\n"),'"'):t)}},function(u,n,e){e.r(n),n.default="data:font/woff;base64,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"},function(u,n,e){var t=e(20),i=e(153);i=i.__esModule?i.default:i,typeof i=="string"&&(i=[[u.i,i,""]]);var o={};o.insert="head",o.singleton=!1,t(i,o),u.exports=i.locals||{}},function(u,n,e){var t=e(21);n=t(!1),n.push([u.i,`.w-e-toolbar {
  display: flex;
  padding: 0 6px;
  flex-wrap: wrap;
  position: relative;
  /* 单个菜单 */
}
.w-e-toolbar .w-e-menu {
  position: relative;
  display: flex;
  width: 40px;
  height: 40px;
  align-items: center;
  justify-content: center;
  text-align: center;
  cursor: pointer;
}
.w-e-toolbar .w-e-menu i {
  color: #999;
}
.w-e-toolbar .w-e-menu:hover {
  background-color: #F6F6F6;
}
.w-e-toolbar .w-e-menu:hover i {
  color: #333;
}
.w-e-toolbar .w-e-active i {
  color: #1e88e5;
}
.w-e-toolbar .w-e-active:hover i {
  color: #1e88e5;
}
.w-e-menu-tooltip {
  position: absolute;
  display: flex;
  color: #f1f1f1;
  background-color: rgba(0, 0, 0, 0.75);
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  padding: 4px 5px 6px;
  justify-content: center;
  align-items: center;
}
.w-e-menu-tooltip-up::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border: 5px solid rgba(0, 0, 0, 0);
  border-top-color: rgba(0, 0, 0, 0.73);
}
.w-e-menu-tooltip-down::after {
  content: "";
  position: absolute;
  bottom: 100%;
  left: 50%;
  margin-left: -5px;
  border: 5px solid rgba(0, 0, 0, 0);
  border-bottom-color: rgba(0, 0, 0, 0.73);
}
.w-e-menu-tooltip-item-wrapper {
  font-size: 14px;
  margin: 0 5px;
}
`,""]),u.exports=n},function(u,n,e){var t=e(20),i=e(155);i=i.__esModule?i.default:i,typeof i=="string"&&(i=[[u.i,i,""]]);var o={};o.insert="head",o.singleton=!1,t(i,o),u.exports=i.locals||{}},function(u,n,e){var t=e(21);n=t(!1),n.push([u.i,`.w-e-text-container {
  position: relative;
  height: 100%;
}
.w-e-text-container .w-e-progress {
  position: absolute;
  background-color: #1e88e5;
  top: 0;
  left: 0;
  height: 1px;
}
.w-e-text-container .placeholder {
  color: #D4D4D4;
  position: absolute;
  font-size: 11pt;
  line-height: 22px;
  left: 10px;
  top: 10px;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  z-index: -1;
}
.w-e-text {
  padding: 0 10px;
  overflow-y: auto;
}
.w-e-text p,
.w-e-text h1,
.w-e-text h2,
.w-e-text h3,
.w-e-text h4,
.w-e-text h5,
.w-e-text table,
.w-e-text pre {
  margin: 10px 0;
  line-height: 1.5;
}
.w-e-text ul,
.w-e-text ol {
  margin: 10px 0 10px 20px;
}
.w-e-text blockquote {
  display: block;
  border-left: 8px solid #d0e5f2;
  padding: 5px 10px;
  margin: 10px 0;
  line-height: 1.4;
  font-size: 100%;
  background-color: #f1f1f1;
}
.w-e-text code {
  display: inline-block;
  background-color: #f1f1f1;
  border-radius: 3px;
  padding: 3px 5px;
  margin: 0 3px;
}
.w-e-text pre code {
  display: block;
}
.w-e-text table {
  border-top: 1px solid #ccc;
  border-left: 1px solid #ccc;
}
.w-e-text table td,
.w-e-text table th {
  border-bottom: 1px solid #ccc;
  border-right: 1px solid #ccc;
  padding: 3px 5px;
  min-height: 30px;
  height: 30px;
}
.w-e-text table th {
  border-bottom: 2px solid #ccc;
  text-align: center;
  background-color: #f1f1f1;
}
.w-e-text:focus {
  outline: none;
}
.w-e-text img {
  cursor: pointer;
}
.w-e-text img:hover {
  box-shadow: 0 0 5px #333;
}
.w-e-text .w-e-todo {
  margin: 0 0 0 20px;
}
.w-e-text .w-e-todo li {
  list-style: none;
  font-size: 1em;
}
.w-e-text .w-e-todo li span:nth-child(1) {
  position: relative;
  left: -18px;
}
.w-e-text .w-e-todo li span:nth-child(1) input {
  position: absolute;
  margin-right: 3px;
}
.w-e-text .w-e-todo li span:nth-child(1) input[type=checkbox] {
  top: 50%;
  margin-top: -6px;
}
.w-e-tooltip {
  position: absolute;
  display: flex;
  color: #f1f1f1;
  background-color: rgba(0, 0, 0, 0.75);
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  padding: 4px 5px 6px;
  justify-content: center;
  align-items: center;
}
.w-e-tooltip-up::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border: 5px solid rgba(0, 0, 0, 0);
  border-top-color: rgba(0, 0, 0, 0.73);
}
.w-e-tooltip-down::after {
  content: "";
  position: absolute;
  bottom: 100%;
  left: 50%;
  margin-left: -5px;
  border: 5px solid rgba(0, 0, 0, 0);
  border-bottom-color: rgba(0, 0, 0, 0.73);
}
.w-e-tooltip-item-wrapper {
  cursor: pointer;
  font-size: 14px;
  margin: 0 5px;
}
.w-e-tooltip-item-wrapper:hover {
  color: #ccc;
  text-decoration: underline;
}
`,""]),u.exports=n},function(u,n,e){var t=e(20),i=e(157);i=i.__esModule?i.default:i,typeof i=="string"&&(i=[[u.i,i,""]]);var o={};o.insert="head",o.singleton=!1,t(i,o),u.exports=i.locals||{}},function(u,n,e){var t=e(21);n=t(!1),n.push([u.i,`.w-e-menu .w-e-panel-container {
  position: absolute;
  top: 0;
  left: 50%;
  border: 1px solid #ccc;
  border-top: 0;
  box-shadow: 1px 1px 2px #ccc;
  color: #333;
  background-color: #fff;
  text-align: left;
  /* 为 emotion panel 定制的样式 */
  /* 上传图片、上传视频的 panel 定制样式 */
}
.w-e-menu .w-e-panel-container .w-e-panel-close {
  position: absolute;
  right: 0;
  top: 0;
  padding: 5px;
  margin: 2px 5px 0 0;
  cursor: pointer;
  color: #999;
}
.w-e-menu .w-e-panel-container .w-e-panel-close:hover {
  color: #333;
}
.w-e-menu .w-e-panel-container .w-e-panel-tab-title {
  list-style: none;
  display: flex;
  font-size: 14px;
  margin: 2px 10px 0 10px;
  border-bottom: 1px solid #f1f1f1;
}
.w-e-menu .w-e-panel-container .w-e-panel-tab-title .w-e-item {
  padding: 3px 5px;
  color: #999;
  cursor: pointer;
  margin: 0 3px;
  position: relative;
  top: 1px;
}
.w-e-menu .w-e-panel-container .w-e-panel-tab-title .w-e-active {
  color: #333;
  border-bottom: 1px solid #333;
  cursor: default;
  font-weight: 700;
}
.w-e-menu .w-e-panel-container .w-e-panel-tab-content {
  padding: 10px 15px 10px 15px;
  font-size: 16px;
  /* 输入框的样式 */
  /* 按钮的样式 */
}
.w-e-menu .w-e-panel-container .w-e-panel-tab-content input:focus,
.w-e-menu .w-e-panel-container .w-e-panel-tab-content textarea:focus,
.w-e-menu .w-e-panel-container .w-e-panel-tab-content button:focus {
  outline: none;
}
.w-e-menu .w-e-panel-container .w-e-panel-tab-content textarea {
  width: 100%;
  border: 1px solid #ccc;
  padding: 5px;
  margin-top: 10px;
}
.w-e-menu .w-e-panel-container .w-e-panel-tab-content textarea:focus {
  border-color: #1e88e5;
}
.w-e-menu .w-e-panel-container .w-e-panel-tab-content input[type=text] {
  border: none;
  border-bottom: 1px solid #ccc;
  font-size: 14px;
  height: 20px;
  color: #333;
  text-align: left;
}
.w-e-menu .w-e-panel-container .w-e-panel-tab-content input[type=text].small {
  width: 30px;
  text-align: center;
}
.w-e-menu .w-e-panel-container .w-e-panel-tab-content input[type=text].block {
  display: block;
  width: 100%;
  margin: 10px 0;
}
.w-e-menu .w-e-panel-container .w-e-panel-tab-content input[type=text]:focus {
  border-bottom: 2px solid #1e88e5;
}
.w-e-menu .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button {
  font-size: 14px;
  color: #1e88e5;
  border: none;
  padding: 5px 10px;
  background-color: #fff;
  cursor: pointer;
  border-radius: 3px;
}
.w-e-menu .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button.left {
  float: left;
  margin-right: 10px;
}
.w-e-menu .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button.right {
  float: right;
  margin-left: 10px;
}
.w-e-menu .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button.gray {
  color: #999;
}
.w-e-menu .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button.red {
  color: #c24f4a;
}
.w-e-menu .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button:hover {
  background-color: #f1f1f1;
}
.w-e-menu .w-e-panel-container .w-e-panel-tab-content .w-e-button-container:after {
  content: "";
  display: table;
  clear: both;
}
.w-e-menu .w-e-panel-container .w-e-emoticon-container .w-e-item {
  cursor: pointer;
  font-size: 18px;
  padding: 0 3px;
  display: inline-block;
}
.w-e-menu .w-e-panel-container .w-e-up-img-container,
.w-e-menu .w-e-panel-container .w-e-up-video-container {
  text-align: center;
}
.w-e-menu .w-e-panel-container .w-e-up-img-container .w-e-up-btn,
.w-e-menu .w-e-panel-container .w-e-up-video-container .w-e-up-btn {
  display: inline-block;
  color: #999;
  cursor: pointer;
  font-size: 60px;
  line-height: 1;
}
.w-e-menu .w-e-panel-container .w-e-up-img-container .w-e-up-btn:hover,
.w-e-menu .w-e-panel-container .w-e-up-video-container .w-e-up-btn:hover {
  color: #333;
}
`,""]),u.exports=n},function(u,n,e){var t=e(20),i=e(159);i=i.__esModule?i.default:i,typeof i=="string"&&(i=[[u.i,i,""]]);var o={};o.insert="head",o.singleton=!1,t(i,o),u.exports=i.locals||{}},function(u,n,e){var t=e(21);n=t(!1),n.push([u.i,`.w-e-toolbar .w-e-droplist {
  position: absolute;
  left: 0;
  top: 0;
  background-color: #fff;
  border: 1px solid #f1f1f1;
  border-right-color: #ccc;
  border-bottom-color: #ccc;
}
.w-e-toolbar .w-e-droplist .w-e-dp-title {
  text-align: center;
  color: #999;
  line-height: 2;
  border-bottom: 1px solid #f1f1f1;
  font-size: 13px;
}
.w-e-toolbar .w-e-droplist ul.w-e-list {
  list-style: none;
  line-height: 1;
}
.w-e-toolbar .w-e-droplist ul.w-e-list li.w-e-item {
  color: #333;
  padding: 5px 0;
}
.w-e-toolbar .w-e-droplist ul.w-e-list li.w-e-item:hover {
  background-color: #f1f1f1;
}
.w-e-toolbar .w-e-droplist ul.w-e-block {
  list-style: none;
  text-align: left;
  padding: 5px;
}
.w-e-toolbar .w-e-droplist ul.w-e-block li.w-e-item {
  display: inline-block;
  padding: 3px 5px;
}
.w-e-toolbar .w-e-droplist ul.w-e-block li.w-e-item:hover {
  background-color: #f1f1f1;
}
`,""]),u.exports=n},function(u,n,e){var t=e(0),i=t(e(161));Element.prototype.matches||(Element.prototype.matches=function(o){var r=this.ownerDocument.querySelectorAll(o),f=r.length;for(f;f>=0&&r.item(f)!==this;f--);return f>-1}),i.default||(window.Promise=i.default)},function(u,n,e){u.exports=e(162)},function(u,n,e){var t=e(163);u.exports=t},function(u,n,e){e(61),e(50),e(54),e(175),e(178),e(179);var t=e(9);u.exports=t.Promise},function(u,n,e){var t=e(62),i=e(49),o=function(r){return function(f,g){var c=String(i(f)),m=t(g),s=c.length,a,d;return m<0||m>=s?r?"":void 0:(a=c.charCodeAt(m),a<55296||a>56319||m+1===s||(d=c.charCodeAt(m+1))<56320||d>57343?r?c.charAt(m):a:r?c.slice(m,m+2):(a-55296<<10)+(d-56320)+65536)}};u.exports={codeAt:o(!1),charAt:o(!0)}},function(u,n,e){var t=e(8),i=e(102),o=t.WeakMap;u.exports=typeof o=="function"&&/native code/.test(i(o))},function(u,n,e){var t=e(8),i=e(19);u.exports=function(o,r){try{i(t,o,r)}catch{t[o]=r}return r}},function(u,n,e){var t=e(104).IteratorPrototype,i=e(77),o=e(48),r=e(37),f=e(44),g=function(){return this};u.exports=function(c,m,s){var a=m+" Iterator";return c.prototype=i(t,{next:o(1,s)}),r(c,a,!1,!0),f[a]=g,c}},function(u,n,e){var t=e(11);u.exports=!t(function(){function i(){}return i.prototype.constructor=null,Object.getPrototypeOf(new i)!==i.prototype})},function(u,n,e){var t=e(14),i=e(18),o=e(25),r=e(52);u.exports=t?Object.defineProperties:function(f,g){o(f);for(var c=r(g),m=c.length,s=0,a;m>s;)i.f(f,a=c[s++],g[a]);return f}},function(u,n,e){var t=e(81),i=e(65);u.exports=t?{}.toString:function(){return"[object "+i(this)+"]"}},function(u,n,e){var t=e(25),i=e(172);u.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var o=!1,r={},f;try{f=Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set,f.call(r,[]),o=r instanceof Array}catch{}return function(g,c){return t(g),i(c),o?f.call(g,c):g.__proto__=c,g}}():void 0)},function(u,n,e){var t=e(13);u.exports=function(i){if(!t(i)&&i!==null)throw TypeError("Can't set "+String(i)+" as a prototype");return i}},function(u,n,e){var t=e(30),i=e(82),o=e(44),r=e(42),f=e(75),g="Array Iterator",c=r.set,m=r.getterFor(g);u.exports=f(Array,"Array",function(s,a){c(this,{type:g,target:t(s),index:0,kind:a})},function(){var s=m(this),a=s.target,d=s.kind,l=s.index++;return!a||l>=a.length?(s.target=void 0,{value:void 0,done:!0}):d=="keys"?{value:l,done:!1}:d=="values"?{value:a[l],done:!1}:{value:[l,a[l]],done:!1}},"values"),o.Arguments=o.Array,i("keys"),i("values"),i("entries")},function(u,n){u.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},function(u,n,e){var t=e(5),i=e(43),o=e(8),r=e(36),f=e(109),g=e(53),c=e(110),m=e(37),s=e(111),a=e(13),d=e(41),l=e(83),v=e(34),p=e(102),h=e(66),A=e(115),y=e(116),x=e(117).set,b=e(176),E=e(119),C=e(177),_=e(85),w=e(120),S=e(42),M=e(101),D=e(10),k=e(86),N=D("species"),T="Promise",B=S.get,R=S.set,I=S.getterFor(T),P=f,H=o.TypeError,z=o.document,O=o.process,$=r("fetch"),L=_.f,U=L,Y=v(O)=="process",G=!!(z&&z.createEvent&&o.dispatchEvent),q="unhandledrejection",ee="rejectionhandled",te=0,de=1,Ce=2,ie=1,be=2,ge,ae,ue,Ae,se=M(T,function(){var j=p(P)!==String(P);if(!j&&(k===66||!Y&&typeof PromiseRejectionEvent!="function")||i&&!P.prototype.finally)return!0;if(k>=51&&/native code/.test(P))return!1;var Q=P.resolve(1),W=function(F){F(function(){},function(){})},X=Q.constructor={};return X[N]=W,!(Q.then(function(){})instanceof W)}),Me=se||!A(function(j){P.all(j).catch(function(){})}),ye=function(j){var Q;return a(j)&&typeof(Q=j.then)=="function"?Q:!1},he=function(j,Q,W){if(!Q.notified){Q.notified=!0;var X=Q.reactions;b(function(){for(var F=Q.value,V=Q.state==de,J=0;X.length>J;){var K=X[J++],ne=V?K.ok:K.fail,le=K.resolve,ve=K.reject,re=K.domain,fe,Se,ke;try{ne?(V||(Q.rejection===be&&_e(j,Q),Q.rejection=ie),ne===!0?fe=F:(re&&re.enter(),fe=ne(F),re&&(re.exit(),ke=!0)),fe===K.promise?ve(H("Promise-chain cycle")):(Se=ye(fe))?Se.call(fe,le,ve):le(fe)):ve(F)}catch(Te){re&&!ke&&re.exit(),ve(Te)}}Q.reactions=[],Q.notified=!1,W&&!Q.rejection&&Ee(j,Q)})}},we=function(j,Q,W){var X,F;G?(X=z.createEvent("Event"),X.promise=Q,X.reason=W,X.initEvent(j,!1,!0),o.dispatchEvent(X)):X={promise:Q,reason:W},(F=o["on"+j])?F(X):j===q&&C("Unhandled promise rejection",W)},Ee=function(j,Q){x.call(o,function(){var W=Q.value,X=xe(Q),F;if(X&&(F=w(function(){Y?O.emit("unhandledRejection",W,j):we(q,j,W)}),Q.rejection=Y||xe(Q)?be:ie,F.error))throw F.value})},xe=function(j){return j.rejection!==ie&&!j.parent},_e=function(j,Q){x.call(o,function(){Y?O.emit("rejectionHandled",j):we(ee,j,Q.value)})},ce=function(j,Q,W,X){return function(F){j(Q,W,F,X)}},pe=function(j,Q,W,X){Q.done||(Q.done=!0,X&&(Q=X),Q.value=W,Q.state=Ce,he(j,Q,!0))},me=function(j,Q,W,X){if(!Q.done){Q.done=!0,X&&(Q=X);try{if(j===W)throw H("Promise can't be resolved itself");var F=ye(W);F?b(function(){var V={done:!1};try{F.call(W,ce(me,j,V,Q),ce(pe,j,V,Q))}catch(J){pe(j,V,J,Q)}}):(Q.value=W,Q.state=de,he(j,Q,!1))}catch(V){pe(j,{done:!1},V,Q)}}};se&&(P=function(j){l(this,P,T),d(j),ge.call(this);var Q=B(this);try{j(ce(me,this,Q),ce(pe,this,Q))}catch(W){pe(this,Q,W)}},ge=function(j){R(this,{type:T,done:!1,notified:!1,parent:!1,reactions:[],rejection:!1,state:te,value:void 0})},ge.prototype=c(P.prototype,{then:function(j,Q){var W=I(this),X=L(y(this,P));return X.ok=typeof j=="function"?j:!0,X.fail=typeof Q=="function"&&Q,X.domain=Y?O.domain:void 0,W.parent=!0,W.reactions.push(X),W.state!=te&&he(this,W,!1),X.promise},catch:function(j){return this.then(void 0,j)}}),ae=function(){var j=new ge,Q=B(j);this.promise=j,this.resolve=ce(me,j,Q),this.reject=ce(pe,j,Q)},_.f=L=function(j){return j===P||j===ue?new ae(j):U(j)},!i&&typeof f=="function"&&(Ae=f.prototype.then,g(f.prototype,"then",function(j,Q){var W=this;return new P(function(X,F){Ae.call(W,X,F)}).then(j,Q)},{unsafe:!0}),typeof $=="function"&&t({global:!0,enumerable:!0,forced:!0},{fetch:function(j){return E(P,$.apply(o,arguments))}}))),t({global:!0,wrap:!0,forced:se},{Promise:P}),m(P,T,!1,!0),s(T),ue=r(T),t({target:T,stat:!0,forced:se},{reject:function(j){var Q=L(this);return Q.reject.call(void 0,j),Q.promise}}),t({target:T,stat:!0,forced:i||se},{resolve:function(j){return E(i&&this===ue?P:this,j)}}),t({target:T,stat:!0,forced:Me},{all:function(j){var Q=this,W=L(Q),X=W.resolve,F=W.reject,V=w(function(){var J=d(Q.resolve),K=[],ne=0,le=1;h(j,function(ve){var re=ne++,fe=!1;K.push(void 0),le++,J.call(Q,ve).then(function(Se){fe||(fe=!0,K[re]=Se,--le||X(K))},F)}),--le||X(K)});return V.error&&F(V.value),W.promise},race:function(j){var Q=this,W=L(Q),X=W.reject,F=w(function(){var V=d(Q.resolve);h(j,function(J){V.call(Q,J).then(W.resolve,X)})});return F.error&&X(F.value),W.promise}})},function(u,n,e){var t=e(8),i=e(71).f,o=e(34),r=e(117).set,f=e(118),g=t.MutationObserver||t.WebKitMutationObserver,c=t.process,m=t.Promise,s=o(c)=="process",a=i(t,"queueMicrotask"),d=a&&a.value,l,v,p,h,A,y,x,b;d||(l=function(){var E,C;for(s&&(E=c.domain)&&E.exit();v;){C=v.fn,v=v.next;try{C()}catch(_){throw v?h():p=void 0,_}}p=void 0,E&&E.enter()},s?h=function(){c.nextTick(l)}:g&&!f?(A=!0,y=document.createTextNode(""),new g(l).observe(y,{characterData:!0}),h=function(){y.data=A=!A}):m&&m.resolve?(x=m.resolve(void 0),b=x.then,h=function(){b.call(x,l)}):h=function(){r.call(t,l)}),u.exports=d||function(E){var C={fn:E,next:void 0};p&&(p.next=C),v||(v=C,h()),p=C}},function(u,n,e){var t=e(8);u.exports=function(i,o){var r=t.console;r&&r.error}},function(u,n,e){var t=e(5),i=e(41),o=e(85),r=e(120),f=e(66);t({target:"Promise",stat:!0},{allSettled:function(g){var c=this,m=o.f(c),s=m.resolve,a=m.reject,d=r(function(){var l=i(c.resolve),v=[],p=0,h=1;f(g,function(A){var y=p++,x=!1;v.push(void 0),h++,l.call(c,A).then(function(b){x||(x=!0,v[y]={status:"fulfilled",value:b},--h||s(v))},function(b){x||(x=!0,v[y]={status:"rejected",reason:b},--h||s(v))})}),--h||s(v)});return d.error&&a(d.value),m.promise}})},function(u,n,e){var t=e(5),i=e(43),o=e(109),r=e(11),f=e(36),g=e(116),c=e(119),m=e(53),s=!!o&&r(function(){o.prototype.finally.call({then:function(){}},function(){})});t({target:"Promise",proto:!0,real:!0,forced:s},{finally:function(a){var d=g(this,f("Promise")),l=typeof a=="function";return this.then(l?function(v){return c(d,a()).then(function(){return v})}:a,l?function(v){return c(d,a()).then(function(){throw v})}:a)}}),!i&&typeof o=="function"&&!o.prototype.finally&&m(o.prototype,"finally",f("Promise").prototype.finally)},function(u,n,e){e(54);var t=e(181),i=e(65),o=Array.prototype,r={DOMTokenList:!0,NodeList:!0};u.exports=function(f){var g=f.forEach;return f===o||f instanceof Array&&g===o.forEach||r.hasOwnProperty(i(f))?t:g}},function(u,n,e){var t=e(182);u.exports=t},function(u,n,e){e(183);var t=e(15);u.exports=t("Array").forEach},function(u,n,e){var t=e(5),i=e(184);t({target:"Array",proto:!0,forced:[].forEach!=i},{forEach:i})},function(u,n,e){var t=e(32).forEach,i=e(67),o=e(22),r=i("forEach"),f=o("forEach");u.exports=!r||!f?function(g){return t(this,g,arguments.length>1?arguments[1]:void 0)}:[].forEach},function(u,n,e){var t=e(186);u.exports=t},function(u,n,e){e(187);var t=e(9);u.exports=t.Array.isArray},function(u,n,e){var t=e(5),i=e(55);t({target:"Array",stat:!0},{isArray:i})},function(u,n,e){var t=e(189);u.exports=t},function(u,n,e){var t=e(190),i=Array.prototype;u.exports=function(o){var r=o.map;return o===i||o instanceof Array&&r===i.map?t:r}},function(u,n,e){e(191);var t=e(15);u.exports=t("Array").map},function(u,n,e){var t=e(5),i=e(32).map,o=e(56),r=e(22),f=o("map"),g=r("map");t({target:"Array",proto:!0,forced:!f||!g},{map:function(c){return i(this,c,arguments.length>1?arguments[1]:void 0)}})},function(u,n,e){var t=e(193);u.exports=t},function(u,n,e){var t=e(194),i=String.prototype;u.exports=function(o){var r=o.trim;return typeof o=="string"||o===i||o instanceof String&&r===i.trim?t:r}},function(u,n,e){e(195);var t=e(15);u.exports=t("String").trim},function(u,n,e){var t=e(5),i=e(90).trim,o=e(196);t({target:"String",proto:!0,forced:o("trim")},{trim:function(){return i(this)}})},function(u,n,e){var t=e(11),i=e(68),o="​᠎";u.exports=function(r){return t(function(){return!!i[r]()||o[r]()!=o||i[r].name!==r})}},function(u,n,e){var t=e(198);u.exports=t},function(u,n,e){e(199),e(61),e(50),e(54);var t=e(9);u.exports=t.Map},function(u,n,e){var t=e(122),i=e(124);u.exports=t("Map",function(o){return function(){return o(this,arguments.length?arguments[0]:void 0)}},i)},function(u,n,e){var t=e(11);u.exports=!t(function(){return Object.isExtensible(Object.preventExtensions({}))})},function(u,n,e){var t=e(202);u.exports=t},function(u,n,e){var t=e(203),i=Array.prototype;u.exports=function(o){var r=o.indexOf;return o===i||o instanceof Array&&r===i.indexOf?t:r}},function(u,n,e){e(204);var t=e(15);u.exports=t("Array").indexOf},function(u,n,e){var t=e(5),i=e(78).indexOf,o=e(67),r=e(22),f=[].indexOf,g=!!f&&1/[1].indexOf(1,-0)<0,c=o("indexOf"),m=r("indexOf",{ACCESSORS:!0,1:0});t({target:"Array",proto:!0,forced:g||!c||!m},{indexOf:function(s){return g?f.apply(this,arguments)||0:i(this,s,arguments.length>1?arguments[1]:void 0)}})},function(u,n,e){var t=e(206);u.exports=t},function(u,n,e){var t=e(207),i=Array.prototype;u.exports=function(o){var r=o.splice;return o===i||o instanceof Array&&r===i.splice?t:r}},function(u,n,e){e(208);var t=e(15);u.exports=t("Array").splice},function(u,n,e){var t=e(5),i=e(79),o=e(62),r=e(35),f=e(31),g=e(88),c=e(69),m=e(56),s=e(22),a=m("splice"),d=s("splice",{ACCESSORS:!0,0:0,1:2}),l=Math.max,v=Math.min,p=9007199254740991,h="Maximum allowed length exceeded";t({target:"Array",proto:!0,forced:!a||!d},{splice:function(A,y){var x=f(this),b=r(x.length),E=i(A,b),C=arguments.length,_,w,S,M,D,k;if(C===0?_=w=0:C===1?(_=0,w=b-E):(_=C-2,w=v(l(o(y),0),b-E)),b+_-w>p)throw TypeError(h);for(S=g(x,w),M=0;M<w;M++)D=E+M,D in x&&c(S,M,x[D]);if(S.length=w,_<w){for(M=E;M<b-w;M++)D=M+w,k=M+_,D in x?x[k]=x[D]:delete x[k];for(M=b;M>b-w+_;M--)delete x[M-1]}else if(_>w)for(M=b-w;M>E;M--)D=M+w-1,k=M+_-1,D in x?x[k]=x[D]:delete x[k];for(M=0;M<_;M++)x[M+E]=arguments[M+2];return x.length=b-w+_,S}})},function(u,n,e){var t=e(210);u.exports=t},function(u,n,e){var t=e(211),i=Array.prototype;u.exports=function(o){var r=o.filter;return o===i||o instanceof Array&&r===i.filter?t:r}},function(u,n,e){e(212);var t=e(15);u.exports=t("Array").filter},function(u,n,e){var t=e(5),i=e(32).filter,o=e(56),r=e(22),f=o("filter"),g=r("filter");t({target:"Array",proto:!0,forced:!f||!g},{filter:function(c){return i(this,c,arguments.length>1?arguments[1]:void 0)}})},function(u,n,e){var t=e(214);u.exports=t},function(u,n,e){var t=e(215),i=e(217),o=Array.prototype,r=String.prototype;u.exports=function(f){var g=f.includes;return f===o||f instanceof Array&&g===o.includes?t:typeof f=="string"||f===r||f instanceof String&&g===r.includes?i:g}},function(u,n,e){e(216);var t=e(15);u.exports=t("Array").includes},function(u,n,e){var t=e(5),i=e(78).includes,o=e(82),r=e(22),f=r("indexOf",{ACCESSORS:!0,1:0});t({target:"Array",proto:!0,forced:!f},{includes:function(g){return i(this,g,arguments.length>1?arguments[1]:void 0)}}),o("includes")},function(u,n,e){e(218);var t=e(15);u.exports=t("String").includes},function(u,n,e){var t=e(5),i=e(219),o=e(49),r=e(221);t({target:"String",proto:!0,forced:!r("includes")},{includes:function(f){return!!~String(o(this)).indexOf(i(f),arguments.length>1?arguments[1]:void 0)}})},function(u,n,e){var t=e(220);u.exports=function(i){if(t(i))throw TypeError("The method doesn't accept regular expressions");return i}},function(u,n,e){var t=e(13),i=e(34),o=e(10),r=o("match");u.exports=function(f){var g;return t(f)&&((g=f[r])!==void 0?!!g:i(f)=="RegExp")}},function(u,n,e){var t=e(10),i=t("match");u.exports=function(o){var r=/./;try{"/./"[o](r)}catch{try{return r[i]=!1,"/./"[o](r)}catch{}}return!1}},function(u,n,e){var t=e(223);u.exports=t},function(u,n,e){var t=e(224),i=Function.prototype;u.exports=function(o){var r=o.bind;return o===i||o instanceof Function&&r===i.bind?t:r}},function(u,n,e){e(225);var t=e(15);u.exports=t("Function").bind},function(u,n,e){var t=e(5),i=e(226);t({target:"Function",proto:!0},{bind:i})},function(u,n,e){var t=e(41),i=e(13),o=[].slice,r={},f=function(g,c,m){if(!(c in r)){for(var s=[],a=0;a<c;a++)s[a]="a["+a+"]";r[c]=Function("C,a","return new C("+s.join(",")+")")}return r[c](g,m)};u.exports=Function.bind||function(g){var c=t(this),m=o.call(arguments,1),s=function(){var a=m.concat(o.call(arguments));return this instanceof s?f(c,a.length,a):c.apply(g,a)};return i(c.prototype)&&(s.prototype=c.prototype),s}},function(u,n,e){u.exports=e(228)},function(u,n,e){var t=e(229);u.exports=t},function(u,n,e){e(125),e(50),e(54);var t=e(93);u.exports=t.f("iterator")},function(u,n,e){u.exports=e(231)},function(u,n,e){var t=e(232);e(251),e(252),e(253),e(254),e(255),u.exports=t},function(u,n,e){e(233),e(61),e(234),e(236),e(237),e(238),e(239),e(125),e(240),e(241),e(242),e(243),e(244),e(245),e(246),e(247),e(248),e(249),e(250);var t=e(9);u.exports=t.Symbol},function(u,n,e){var t=e(5),i=e(11),o=e(55),r=e(13),f=e(31),g=e(35),c=e(69),m=e(88),s=e(56),a=e(10),d=e(86),l=a("isConcatSpreadable"),v=9007199254740991,p="Maximum allowed index exceeded",h=d>=51||!i(function(){var b=[];return b[l]=!1,b.concat()[0]!==b}),A=s("concat"),y=function(b){if(!r(b))return!1;var E=b[l];return E!==void 0?!!E:o(b)},x=!h||!A;t({target:"Array",proto:!0,forced:x},{concat:function(b){var E=f(this),C=m(E,0),_=0,w,S,M,D,k;for(w=-1,M=arguments.length;w<M;w++)if(k=w===-1?E:arguments[w],y(k)){if(D=g(k.length),_+D>v)throw TypeError(p);for(S=0;S<D;S++,_++)S in k&&c(C,_,k[S])}else{if(_>=v)throw TypeError(p);c(C,_++,k)}return C.length=_,C}})},function(u,n,e){var t=e(5),i=e(8),o=e(36),r=e(43),f=e(14),g=e(76),c=e(106),m=e(11),s=e(16),a=e(55),d=e(13),l=e(25),v=e(31),p=e(30),h=e(60),A=e(48),y=e(77),x=e(52),b=e(126),E=e(235),C=e(127),_=e(71),w=e(18),S=e(59),M=e(19),D=e(53),k=e(74),N=e(63),T=e(51),B=e(64),R=e(10),I=e(93),P=e(12),H=e(37),z=e(42),O=e(32).forEach,$=N("hidden"),L="Symbol",U="prototype",Y=R("toPrimitive"),G=z.set,q=z.getterFor(L),ee=Object[U],te=i.Symbol,de=o("JSON","stringify"),Ce=_.f,ie=w.f,be=E.f,ge=S.f,ae=k("symbols"),ue=k("op-symbols"),Ae=k("string-to-symbol-registry"),se=k("symbol-to-string-registry"),Me=k("wks"),ye=i.QObject,he=!ye||!ye[U]||!ye[U].findChild,we=f&&m(function(){return y(ie({},"a",{get:function(){return ie(this,"a",{value:7}).a}})).a!=7})?function(F,V,J){var K=Ce(ee,V);K&&delete ee[V],ie(F,V,J),K&&F!==ee&&ie(ee,V,K)}:ie,Ee=function(F,V){var J=ae[F]=y(te[U]);return G(J,{type:L,tag:F,description:V}),f||(J.description=V),J},xe=c?function(F){return typeof F=="symbol"}:function(F){return Object(F)instanceof te},_e=function(F,V,J){F===ee&&_e(ue,V,J),l(F);var K=h(V,!0);return l(J),s(ae,K)?(J.enumerable?(s(F,$)&&F[$][K]&&(F[$][K]=!1),J=y(J,{enumerable:A(0,!1)})):(s(F,$)||ie(F,$,A(1,{})),F[$][K]=!0),we(F,K,J)):ie(F,K,J)},ce=function(F,V){l(F);var J=p(V),K=x(J).concat(W(J));return O(K,function(ne){(!f||me.call(J,ne))&&_e(F,ne,J[ne])}),F},pe=function(F,V){return V===void 0?y(F):ce(y(F),V)},me=function(F){var V=h(F,!0),J=ge.call(this,V);return this===ee&&s(ae,V)&&!s(ue,V)?!1:J||!s(this,V)||!s(ae,V)||s(this,$)&&this[$][V]?J:!0},j=function(F,V){var J=p(F),K=h(V,!0);if(!(J===ee&&s(ae,K)&&!s(ue,K))){var ne=Ce(J,K);return ne&&s(ae,K)&&!(s(J,$)&&J[$][K])&&(ne.enumerable=!0),ne}},Q=function(F){var V=be(p(F)),J=[];return O(V,function(K){!s(ae,K)&&!s(T,K)&&J.push(K)}),J},W=function(F){var V=F===ee,J=be(V?ue:p(F)),K=[];return O(J,function(ne){s(ae,ne)&&(!V||s(ee,ne))&&K.push(ae[ne])}),K};if(g||(te=function(){if(this instanceof te)throw TypeError("Symbol is not a constructor");var F=!arguments.length||arguments[0]===void 0?void 0:String(arguments[0]),V=B(F),J=function(K){this===ee&&J.call(ue,K),s(this,$)&&s(this[$],V)&&(this[$][V]=!1),we(this,V,A(1,K))};return f&&he&&we(ee,V,{configurable:!0,set:J}),Ee(V,F)},D(te[U],"toString",function(){return q(this).tag}),D(te,"withoutSetter",function(F){return Ee(B(F),F)}),S.f=me,w.f=_e,_.f=j,b.f=E.f=Q,C.f=W,I.f=function(F){return Ee(R(F),F)},f&&(ie(te[U],"description",{configurable:!0,get:function(){return q(this).description}}),r||D(ee,"propertyIsEnumerable",me,{unsafe:!0}))),t({global:!0,wrap:!0,forced:!g,sham:!g},{Symbol:te}),O(x(Me),function(F){P(F)}),t({target:L,stat:!0,forced:!g},{for:function(F){var V=String(F);if(s(Ae,V))return Ae[V];var J=te(V);return Ae[V]=J,se[J]=V,J},keyFor:function(F){if(!xe(F))throw TypeError(F+" is not a symbol");if(s(se,F))return se[F]},useSetter:function(){he=!0},useSimple:function(){he=!1}}),t({target:"Object",stat:!0,forced:!g,sham:!f},{create:pe,defineProperty:_e,defineProperties:ce,getOwnPropertyDescriptor:j}),t({target:"Object",stat:!0,forced:!g},{getOwnPropertyNames:Q,getOwnPropertySymbols:W}),t({target:"Object",stat:!0,forced:m(function(){C.f(1)})},{getOwnPropertySymbols:function(F){return C.f(v(F))}}),de){var X=!g||m(function(){var F=te();return de([F])!="[null]"||de({a:F})!="{}"||de(Object(F))!="{}"});t({target:"JSON",stat:!0,forced:X},{stringify:function(F,V,J){for(var K=[F],ne=1,le;arguments.length>ne;)K.push(arguments[ne++]);if(le=V,!(!d(V)&&F===void 0||xe(F)))return a(V)||(V=function(ve,re){if(typeof le=="function"&&(re=le.call(this,ve,re)),!xe(re))return re}),K[1]=V,de.apply(null,K)}})}te[U][Y]||M(te[U],Y,te[U].valueOf),H(te,L),T[$]=!0},function(u,n,e){var t=e(30),i=e(126).f,o={}.toString,r=typeof window=="object"&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],f=function(g){try{return i(g)}catch{return r.slice()}};u.exports.f=function(g){return r&&o.call(g)=="[object Window]"?f(g):i(t(g))}},function(u,n,e){var t=e(12);t("asyncIterator")},function(u,n){},function(u,n,e){var t=e(12);t("hasInstance")},function(u,n,e){var t=e(12);t("isConcatSpreadable")},function(u,n,e){var t=e(12);t("match")},function(u,n,e){var t=e(12);t("matchAll")},function(u,n,e){var t=e(12);t("replace")},function(u,n,e){var t=e(12);t("search")},function(u,n,e){var t=e(12);t("species")},function(u,n,e){var t=e(12);t("split")},function(u,n,e){var t=e(12);t("toPrimitive")},function(u,n,e){var t=e(12);t("toStringTag")},function(u,n,e){var t=e(12);t("unscopables")},function(u,n,e){var t=e(37);t(Math,"Math",!0)},function(u,n,e){var t=e(8),i=e(37);i(t.JSON,"JSON",!0)},function(u,n,e){var t=e(12);t("asyncDispose")},function(u,n,e){var t=e(12);t("dispose")},function(u,n,e){var t=e(12);t("observable")},function(u,n,e){var t=e(12);t("patternMatch")},function(u,n,e){var t=e(12);t("replaceAll")},function(u,n,e){u.exports=e(257)},function(u,n,e){var t=e(258);u.exports=t},function(u,n,e){e(259);var t=e(9);u.exports=t.parseInt},function(u,n,e){var t=e(5),i=e(260);t({global:!0,forced:parseInt!=i},{parseInt:i})},function(u,n,e){var t=e(8),i=e(90).trim,o=e(68),r=t.parseInt,f=/^[+-]?0[Xx]/,g=r(o+"08")!==8||r(o+"0x16")!==22;u.exports=g?function(c,m){var s=i(String(c));return r(s,m>>>0||(f.test(s)?16:10))}:r},function(u,n,e){var t=e(262);u.exports=t},function(u,n,e){var t=e(263),i=Array.prototype;u.exports=function(o){var r=o.slice;return o===i||o instanceof Array&&r===i.slice?t:r}},function(u,n,e){e(264);var t=e(15);u.exports=t("Array").slice},function(u,n,e){var t=e(5),i=e(13),o=e(55),r=e(79),f=e(35),g=e(30),c=e(69),m=e(10),s=e(56),a=e(22),d=s("slice"),l=a("slice",{ACCESSORS:!0,0:0,1:2}),v=m("species"),p=[].slice,h=Math.max;t({target:"Array",proto:!0,forced:!d||!l},{slice:function(A,y){var x=g(this),b=f(x.length),E=r(A,b),C=r(y===void 0?b:y,b),_,w,S;if(o(x)&&(_=x.constructor,typeof _=="function"&&(_===Array||o(_.prototype))?_=void 0:i(_)&&(_=_[v],_===null&&(_=void 0)),_===Array||_===void 0))return p.call(x,E,C);for(w=new(_===void 0?Array:_)(h(C-E,0)),S=0;E<C;E++,S++)E in x&&c(w,S,x[E]);return w.length=S,w}})},function(u,n,e){e(266);var t=e(9);u.exports=t.setTimeout},function(u,n,e){var t=e(5),i=e(8),o=e(84),r=[].slice,f=/MSIE .\./.test(o),g=function(c){return function(m,s){var a=arguments.length>2,d=a?r.call(arguments,2):void 0;return c(a?function(){(typeof m=="function"?m:Function(m)).apply(this,d)}:m,s)}};t({global:!0,bind:!0,forced:f},{setTimeout:g(i.setTimeout),setInterval:g(i.setInterval)})},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(128));(0,i.default)(n,"__esModule",{value:!0});var r=e(2),f=r.__importDefault(e(272)),g=r.__importDefault(e(273)),c=r.__importDefault(e(129)),m=r.__importDefault(e(274)),s=r.__importDefault(e(275)),a=r.__importDefault(e(276)),d=r.__importDefault(e(130)),l=r.__importDefault(e(277)),v=r.__importDefault(e(278)),p=r.__importDefault(e(279)),h=(0,o.default)({},f.default,g.default,c.default,s.default,m.default,a.default,d.default,l.default,v.default,p.default,{linkCheck:function(A,y){return!0}});n.default=h},function(u,n,e){var t=e(269);u.exports=t},function(u,n,e){e(270);var t=e(9);u.exports=t.Object.assign},function(u,n,e){var t=e(5),i=e(271);t({target:"Object",stat:!0,forced:Object.assign!==i},{assign:i})},function(u,n,e){var t=e(14),i=e(11),o=e(52),r=e(127),f=e(59),g=e(31),c=e(72),m=Object.assign,s=Object.defineProperty;u.exports=!m||i(function(){if(t&&m({b:1},m(s({},"a",{enumerable:!0,get:function(){s(this,"b",{value:3,enumerable:!1})}}),{b:2})).b!==1)return!0;var a={},d={},l=Symbol(),v="abcdefghijklmnopqrst";return a[l]=7,v.split("").forEach(function(p){d[p]=p}),m({},a)[l]!=7||o(m({},d)).join("")!=v})?function(a,d){for(var l=g(a),v=arguments.length,p=1,h=r.f,A=f.f;v>p;)for(var y=c(arguments[p++]),x=h?o(y).concat(h(y)):o(y),b=x.length,E=0,C;b>E;)C=x[E++],(!t||A.call(y,C))&&(l[C]=y[C]);return l}:m},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0}),n.default={menus:["head","bold","fontSize","fontName","italic","underline","strikeThrough","indent","lineHeight","foreColor","backColor","link","list","todo","justify","quote","emoticon","image","video","table","code","splitLine","undo","redo"],fontNames:["黑体","仿宋","楷体","标楷体","华文仿宋","华文楷体","宋体","微软雅黑","Arial","Tahoma","Verdana","Times New Roman","Courier New"],fontSizes:{"x-small":{name:"10px",value:"1"},small:{name:"13px",value:"2"},normal:{name:"16px",value:"3"},large:{name:"18px",value:"4"},"x-large":{name:"24px",value:"5"},"xx-large":{name:"32px",value:"6"},"xxx-large":{name:"48px",value:"7"}},colors:["#000000","#ffffff","#eeece0","#1c487f","#4d80bf","#c24f4a","#8baa4a","#7b5ba1","#46acc8","#f9963b"],languageType:["Bash","C","C#","C++","CSS","Java","JavaScript","JSON","TypeScript","Plain text","Html","XML","SQL","Go","Kotlin","Lua","Markdown","PHP","Python","Shell Session","Ruby"],languageTab:"　　　　",emotions:[{title:"表情",type:"emoji",content:"😀 😃 😄 😁 😆 😅 😂 🤣 😊 😇 🙂 🙃 😉 😌 😍 😘 😗 😙 😚 😋 😛 😝 😜 🤓 😎 😏 😒 😞 😔 😟 😕 🙁 😣 😖 😫 😩 😢 😭 😤 😠 😡 😳 😱 😨 🤗 🤔 😶 😑 😬 🙄 😯 😴 😷 🤑 😈 🤡 💩 👻 💀 👀 👣".split(/\s/)},{title:"手势",type:"emoji",content:"👐 🙌 👏 🤝 👍 👎 👊 ✊ 🤛 🤜 🤞 ✌️ 🤘 👌 👈 👉 👆 👇 ☝️ ✋ 🤚 🖐 🖖 👋 🤙 💪 🖕 ✍️ 🙏".split(/\s/)}],lineHeights:["1","1.15","1.6","2","2.5","3"],undoLimit:20,indentation:"2em",showMenuTooltips:!0,menuTooltipPosition:"up"}},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(7);function r(f,g,c){window.alert(f)}n.default={onchangeTimeout:200,onchange:null,onfocus:o.EMPTY_FN,onblur:o.EMPTY_FN,onCatalogChange:null,customAlert:r}},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0}),n.default={pasteFilterStyle:!0,pasteIgnoreImg:!1,pasteTextHandle:function(o){return o}}},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0}),n.default={styleWithCSS:!1}},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(7);n.default={linkImgCheck:function(r,f,g){return!0},showLinkImg:!0,showLinkImgAlt:!0,showLinkImgHref:!0,linkImgCallback:o.EMPTY_FN,uploadImgAccept:["jpg","jpeg","png","gif","bmp"],uploadImgServer:"",uploadImgShowBase64:!1,uploadImgMaxSize:5*1024*1024,uploadImgMaxLength:100,uploadFileName:"",uploadImgParams:{},uploadImgParamsWithUrl:!1,uploadImgHeaders:{},uploadImgHooks:{},uploadImgTimeout:10*1e3,withCredentials:!1,customUploadImg:null,uploadImgFromMedia:null}},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0}),n.default={lang:"zh-CN",languages:{"zh-CN":{wangEditor:{重置:"重置",插入:"插入",默认:"默认",创建:"创建",修改:"修改",如:"如",请输入正文:"请输入正文",menus:{title:{标题:"标题",加粗:"加粗",字号:"字号",字体:"字体",斜体:"斜体",下划线:"下划线",删除线:"删除线",缩进:"缩进",行高:"行高",文字颜色:"文字颜色",背景色:"背景色",链接:"链接",序列:"序列",对齐:"对齐",引用:"引用",表情:"表情",图片:"图片",视频:"视频",表格:"表格",代码:"代码",分割线:"分割线",恢复:"恢复",撤销:"撤销",全屏:"全屏",取消全屏:"取消全屏",待办事项:"待办事项"},dropListMenu:{设置标题:"设置标题",背景颜色:"背景颜色",文字颜色:"文字颜色",设置字号:"设置字号",设置字体:"设置字体",设置缩进:"设置缩进",对齐方式:"对齐方式",设置行高:"设置行高",序列:"序列",head:{正文:"正文"},indent:{增加缩进:"增加缩进",减少缩进:"减少缩进"},justify:{靠左:"靠左",居中:"居中",靠右:"靠右",两端:"两端"},list:{无序列表:"无序列表",有序列表:"有序列表"}},panelMenus:{emoticon:{默认:"默认",新浪:"新浪",emoji:"emoji",手势:"手势"},image:{上传图片:"上传图片",网络图片:"网络图片",图片地址:"图片地址",图片文字说明:"图片文字说明",跳转链接:"跳转链接"},link:{链接:"链接",链接文字:"链接文字",取消链接:"取消链接",查看链接:"查看链接"},video:{插入视频:"插入视频",上传视频:"上传视频"},table:{行:"行",列:"列",的:"的",表格:"表格",添加行:"添加行",删除行:"删除行",添加列:"添加列",删除列:"删除列",设置表头:"设置表头",取消表头:"取消表头",插入表格:"插入表格",删除表格:"删除表格"},code:{删除代码:"删除代码",修改代码:"修改代码",插入代码:"插入代码"}}},validate:{张图片:"张图片",大于:"大于",图片链接:"图片链接",不是图片:"不是图片",返回结果:"返回结果",上传图片超时:"上传图片超时",上传图片错误:"上传图片错误",上传图片失败:"上传图片失败",插入图片错误:"插入图片错误",一次最多上传:"一次最多上传",下载链接失败:"下载链接失败",图片验证未通过:"图片验证未通过",服务器返回状态:"服务器返回状态",上传图片返回结果错误:"上传图片返回结果错误",请替换为支持的图片类型:"请替换为支持的图片类型",您插入的网络图片无法识别:"您插入的网络图片无法识别",您刚才插入的图片链接未通过编辑器校验:"您刚才插入的图片链接未通过编辑器校验",插入视频错误:"插入视频错误",视频链接:"视频链接",不是视频:"不是视频",视频验证未通过:"视频验证未通过",个视频:"个视频",上传视频超时:"上传视频超时",上传视频错误:"上传视频错误",上传视频失败:"上传视频失败",上传视频返回结果错误:"上传视频返回结果错误"}}},en:{wangEditor:{重置:"reset",插入:"insert",默认:"default",创建:"create",修改:"edit",如:"like",请输入正文:"please enter the text",menus:{title:{标题:"head",加粗:"bold",字号:"font size",字体:"font family",斜体:"italic",下划线:"underline",删除线:"strikethrough",缩进:"indent",行高:"line heihgt",文字颜色:"font color",背景色:"background",链接:"link",序列:"numbered list",对齐:"align",引用:"quote",表情:"emoticons",图片:"image",视频:"media",表格:"table",代码:"code",分割线:"split line",恢复:"redo",撤销:"undo",全屏:"fullscreen",取消全屏:"cancel fullscreen",待办事项:"todo"},dropListMenu:{设置标题:"title",背景颜色:"background",文字颜色:"font color",设置字号:"font size",设置字体:"font family",设置缩进:"indent",对齐方式:"align",设置行高:"line heihgt",序列:"list",head:{正文:"text"},indent:{增加缩进:"indent",减少缩进:"outdent"},justify:{靠左:"left",居中:"center",靠右:"right",两端:"justify"},list:{无序列表:"unordered",有序列表:"ordered"}},panelMenus:{emoticon:{表情:"emoji",手势:"gesture"},image:{上传图片:"upload image",网络图片:"network image",图片地址:"image link",图片文字说明:"image alt",跳转链接:"hyperlink"},link:{链接:"link",链接文字:"link text",取消链接:"unlink",查看链接:"view links"},video:{插入视频:"insert video",上传视频:"upload local video"},table:{行:"rows",列:"columns",的:" ",表格:"table",添加行:"insert row",删除行:"delete row",添加列:"insert column",删除列:"delete column",设置表头:"set header",取消表头:"cancel header",插入表格:"insert table",删除表格:"delete table"},code:{删除代码:"delete code",修改代码:"edit code",插入代码:"insert code"}}},validate:{张图片:"images",大于:"greater than",图片链接:"image link",不是图片:"is not image",返回结果:"return results",上传图片超时:"upload image timeout",上传图片错误:"upload image error",上传图片失败:"upload image failed",插入图片错误:"insert image error",一次最多上传:"once most at upload",下载链接失败:"download link failed",图片验证未通过:"image validate failed",服务器返回状态:"server return status",上传图片返回结果错误:"upload image return results error",请替换为支持的图片类型:"please replace with a supported image type",您插入的网络图片无法识别:"the network picture you inserted is not recognized",您刚才插入的图片链接未通过编辑器校验:"the image link you just inserted did not pass the editor verification",插入视频错误:"insert video error",视频链接:"video link",不是视频:"is not video",视频验证未通过:"video validate failed",个视频:"videos",上传视频超时:"upload video timeout",上传视频错误:"upload video error",上传视频失败:"upload video failed",上传视频返回结果错误:"upload video return results error"}}}}}},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(6);function r(){return!!(o.UA.isIE()||o.UA.isOldEdge)}n.default={compatibleMode:r,historyMaxSize:30}},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(7);n.default={onlineVideoCheck:function(r){return!0},onlineVideoCallback:o.EMPTY_FN,showLinkVideo:!0,uploadVideoAccept:["mp4"],uploadVideoServer:"",uploadVideoMaxSize:1*1024*1024*1024,uploadVideoName:"",uploadVideoParams:{},uploadVideoParamsWithUrl:!1,uploadVideoHeaders:{},uploadVideoHooks:{},uploadVideoTimeout:1e3*60*60*2,withVideoCredentials:!1,customUploadVideo:null,customInsertVideo:null}},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(17));(0,i.default)(n,"__esModule",{value:!0});var r=e(2),f=r.__importDefault(e(3)),g=e(6),c=e(7),m=function(){function s(a){this._currentRange=null,this.editor=a}return s.prototype.getRange=function(){return this._currentRange},s.prototype.saveRange=function(a){if(a){this._currentRange=a;return}var d=window.getSelection();if(d.rangeCount!==0){var l=d.getRangeAt(0),v=this.getSelectionContainerElem(l);if(v!=null&&v.length&&!(v.attr("contenteditable")==="false"||v.parentUntil("[contenteditable=false]"))){var p=this.editor,h=p.$textElem;if(h.isContain(v)){if(h.elems[0]===v.elems[0]){var A;if((0,o.default)(A=h.html()).call(A)===c.EMPTY_P){var y=h.children(),x=y==null?void 0:y.last();p.selection.createRangeByElem(x,!0,!0),p.selection.restoreSelection()}}this._currentRange=l}}}},s.prototype.collapseRange=function(a){a===void 0&&(a=!1);var d=this._currentRange;d&&d.collapse(a)},s.prototype.getSelectionText=function(){var a=this._currentRange;return a?a.toString():""},s.prototype.getSelectionContainerElem=function(a){var d;d=a||this._currentRange;var l;if(d)return l=d.commonAncestorContainer,f.default(l.nodeType===1?l:l.parentNode)},s.prototype.getSelectionStartElem=function(a){var d;d=a||this._currentRange;var l;if(d)return l=d.startContainer,f.default(l.nodeType===1?l:l.parentNode)},s.prototype.getSelectionEndElem=function(a){var d;d=a||this._currentRange;var l;if(d)return l=d.endContainer,f.default(l.nodeType===1?l:l.parentNode)},s.prototype.isSelectionEmpty=function(){var a=this._currentRange;return!!(a&&a.startContainer&&a.startContainer===a.endContainer&&a.startOffset===a.endOffset)},s.prototype.restoreSelection=function(){var a=window.getSelection(),d=this._currentRange;a&&d&&(a.removeAllRanges(),a.addRange(d))},s.prototype.createEmptyRange=function(){var a=this.editor,d=this.getRange(),l;if(d&&this.isSelectionEmpty())try{g.UA.isWebkit()?(a.cmd.do("insertHTML","&#8203;"),d.setEnd(d.endContainer,d.endOffset+1),this.saveRange(d)):(l=f.default("<strong>&#8203;</strong>"),a.cmd.do("insertElem",l),this.createRangeByElem(l,!0))}catch{}},s.prototype.createRangeByElems=function(a,d){var l=window.getSelection?window.getSelection():document.getSelection();l==null||l.removeAllRanges();var v=document.createRange();v.setStart(a,0),v.setEnd(d,d.childNodes.length||1),this.saveRange(v),this.restoreSelection()},s.prototype.createRangeByElem=function(a,d,l){if(a.length){var v=a.elems[0],p=document.createRange();l?p.selectNodeContents(v):p.selectNode(v),d!=null&&(p.collapse(d),d||(this.saveRange(p),this.editor.selection.moveCursor(v))),this.saveRange(p)}},s.prototype.getSelectionRangeTopNodes=function(){var a,d,l,v=(a=this.getSelectionStartElem())===null||a===void 0?void 0:a.getNodeTop(this.editor),p=(d=this.getSelectionEndElem())===null||d===void 0?void 0:d.getNodeTop(this.editor);return l=this.recordSelectionNodes(f.default(v),f.default(p)),l},s.prototype.moveCursor=function(a,d){var l,v=this.getRange(),p=a.nodeType===3?(l=a.nodeValue)===null||l===void 0?void 0:l.length:a.childNodes.length;(g.UA.isFirefox||g.UA.isIE())&&p!==0&&(a.nodeType===3||a.childNodes[p-1].nodeName==="BR")&&(p=p-1);var h=d??p;v&&a&&(v.setStart(a,h),v.setEnd(a,h),this.restoreSelection())},s.prototype.getCursorPos=function(){var a=window.getSelection();return a==null?void 0:a.anchorOffset},s.prototype.clearWindowSelectionRange=function(){var a=window.getSelection();a&&a.removeAllRanges()},s.prototype.recordSelectionNodes=function(a,d){var l=[],v=!0;try{for(var p=a,h=this.editor.$textElem;v;){var A=p==null?void 0:p.getNodeTop(this.editor);A.getNodeName()==="BODY"&&(v=!1),A.length>0&&(l.push(f.default(p)),d!=null&&d.equal(A)||h.equal(A)?v=!1:p=A.getNextSibling())}}catch{v=!1}return l},s.prototype.setRangeToElem=function(a){var d=this.getRange();d==null||d.setStart(a,0),d==null||d.setEnd(a,0)},s}();n.default=m},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(2),r=o.__importDefault(e(3)),f=function(){function g(c){this.editor=c}return g.prototype.do=function(c,m){var s=this.editor;s.config.styleWithCSS&&document.execCommand("styleWithCSS",!1,"true");var a=s.selection;if(a.getRange()){switch(a.restoreSelection(),c){case"insertHTML":this.insertHTML(m);break;case"insertElem":this.insertElem(m);break;default:this.execCommand(c,m);break}s.menus.changeActive(),a.saveRange(),a.restoreSelection()}},g.prototype.insertHTML=function(c){var m=this.editor,s=m.selection.getRange();if(s!=null){if(this.queryCommandSupported("insertHTML"))this.execCommand("insertHTML",c);else if(s.insertNode){if(s.deleteContents(),r.default(c).elems.length>0)s.insertNode(r.default(c).elems[0]);else{var a=document.createElement("p");a.appendChild(document.createTextNode(c)),s.insertNode(a)}m.selection.collapseRange()}}},g.prototype.insertElem=function(c){var m=this.editor,s=m.selection.getRange();s!=null&&s.insertNode&&(s.deleteContents(),s.insertNode(c.elems[0]))},g.prototype.execCommand=function(c,m){document.execCommand(c,!1,m)},g.prototype.queryCommandValue=function(c){return document.queryCommandValue(c)},g.prototype.queryCommandState=function(c){return document.queryCommandState(c)},g.prototype.queryCommandSupported=function(c){return document.queryCommandSupported(c)},g}();n.default=f},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(29)),r=t(e(4)),f=t(e(17)),g=t(e(27)),c=t(e(46));(0,i.default)(n,"__esModule",{value:!0});var m=e(2),s=m.__importDefault(e(3)),a=m.__importDefault(e(287)),d=e(6),l=m.__importDefault(e(299)),v=m.__importDefault(e(300)),p=e(7),h=function(){function A(y){this.editor=y,this.eventHooks={onBlurEvents:[],changeEvents:[],dropEvents:[],clickEvents:[],keydownEvents:[],keyupEvents:[],tabUpEvents:[],tabDownEvents:[],enterUpEvents:[],enterDownEvents:[],deleteUpEvents:[],deleteDownEvents:[],pasteEvents:[],linkClickEvents:[],codeClickEvents:[],textScrollEvents:[],toolbarClickEvents:[],imgClickEvents:[],imgDragBarMouseDownEvents:[],tableClickEvents:[],menuClickEvents:[],dropListMenuHoverEvents:[],splitLineEvents:[],videoClickEvents:[]}}return A.prototype.init=function(){this._saveRange(),this._bindEventHooks(),a.default(this)},A.prototype.togglePlaceholder=function(){var y,x=this.html(),b=(0,o.default)(y=this.editor.$textContainerElem).call(y,".placeholder");b.hide(),!this.editor.isComposing&&(!x||x===" ")&&b.show()},A.prototype.clear=function(){this.html(p.EMPTY_P)},A.prototype.html=function(y){var x=this.editor,b=x.$textElem;if(y==null){var E=b.html();E=E.replace(/\u200b/gm,""),E=E.replace(/<p><\/p>/gim,""),E=E.replace(p.EMPTY_P_LAST_REGEX,""),E=E.replace(p.EMPTY_P_REGEX,"<p>");var C=E.match(/<(img|br|hr|input)[^>]*>/gi);return C!==null&&(0,r.default)(C).call(C,function(_){_.match(/\/>/)||(E=E.replace(_,_.substring(0,_.length-1)+"/>"))}),E}y=(0,f.default)(y).call(y),y===""&&(y=p.EMPTY_P),(0,g.default)(y).call(y,"<")!==0&&(y="<p>"+y+"</p>"),b.html(y),x.initSelection()},A.prototype.setJSON=function(y){var x=v.default(y).children(),b=this.editor,E=b.$textElem;x&&E.replaceChildAll(x)},A.prototype.getJSON=function(){var y=this.editor,x=y.$textElem;return l.default(x)},A.prototype.text=function(y){var x=this.editor,b=x.$textElem;if(y==null){var E=b.text();return E=E.replace(/\u200b/gm,""),E}b.text("<p>"+y+"</p>"),x.initSelection()},A.prototype.append=function(y){var x=this.editor;(0,g.default)(y).call(y,"<")!==0&&(y="<p>"+y+"</p>"),this.html(this.html()+y),x.initSelection()},A.prototype._saveRange=function(){var y=this.editor,x=y.$textElem,b=s.default(document);function E(){y.selection.saveRange(),y.menus.changeActive()}x.on("keyup",E);function C(){E(),x.off("click",C)}x.on("click",C);function _(){E(),b.off("mouseup",_)}function w(){b.on("mouseup",_),x.off("mouseleave",w)}x.on("mousedown",function(){x.on("mouseleave",w)}),x.on("mouseup",function(S){x.off("mouseleave",w),(0,c.default)(function(){var M=y.selection,D=M.getRange();D!==null&&E()},0)})},A.prototype._bindEventHooks=function(){var y=this.editor,x=y.$textElem,b=this.eventHooks;x.on("click",function(C){var _=b.clickEvents;(0,r.default)(_).call(_,function(w){return w(C)})}),x.on("keyup",function(C){if(C.keyCode===13){var _=b.enterUpEvents;(0,r.default)(_).call(_,function(w){return w(C)})}}),x.on("keyup",function(C){var _=b.keyupEvents;(0,r.default)(_).call(_,function(w){return w(C)})}),x.on("keydown",function(C){var _=b.keydownEvents;(0,r.default)(_).call(_,function(w){return w(C)})}),x.on("keyup",function(C){if(!(C.keyCode!==8&&C.keyCode!==46)){var _=b.deleteUpEvents;(0,r.default)(_).call(_,function(w){return w(C)})}}),x.on("keydown",function(C){if(!(C.keyCode!==8&&C.keyCode!==46)){var _=b.deleteDownEvents;(0,r.default)(_).call(_,function(w){return w(C)})}}),x.on("paste",function(C){if(!d.UA.isIE()){C.preventDefault();var _=b.pasteEvents;(0,r.default)(_).call(_,function(w){return w(C)})}}),x.on("keydown",function(C){(y.isFocus||y.isCompatibleMode)&&(C.ctrlKey||C.metaKey)&&C.keyCode===90&&(C.preventDefault(),C.shiftKey?y.history.restore():y.history.revoke())}),x.on("keyup",function(C){if(C.keyCode===9){C.preventDefault();var _=b.tabUpEvents;(0,r.default)(_).call(_,function(w){return w(C)})}}),x.on("keydown",function(C){if(C.keyCode===9){C.preventDefault();var _=b.tabDownEvents;(0,r.default)(_).call(_,function(w){return w(C)})}}),x.on("scroll",d.throttle(function(C){var _=b.textScrollEvents;(0,r.default)(_).call(_,function(w){return w(C)})},100));function E(C){C.preventDefault()}s.default(document).on("dragleave",E).on("drop",E).on("dragenter",E).on("dragover",E),y.beforeDestroy(function(){s.default(document).off("dragleave",E).off("drop",E).off("dragenter",E).off("dragover",E)}),x.on("drop",function(C){C.preventDefault();var _=b.dropEvents;(0,r.default)(_).call(_,function(w){return w(C)})}),x.on("click",function(C){var _=null,w=C.target,S=s.default(w);if(S.getNodeName()==="A")_=S;else{var M=S.parentUntil("a");M!=null&&(_=M)}if(_){var D=b.linkClickEvents;(0,r.default)(D).call(D,function(k){return k(_)})}}),x.on("click",function(C){var _=null,w=C.target,S=s.default(w);if(S.getNodeName()==="IMG"&&!S.elems[0].getAttribute("data-emoji")&&(C.stopPropagation(),_=S),!!_){var M=b.imgClickEvents;(0,r.default)(M).call(M,function(D){return D(_)})}}),x.on("click",function(C){var _=null,w=C.target,S=s.default(w);if(S.getNodeName()==="PRE")_=S;else{var M=S.parentUntil("pre");M!==null&&(_=M)}if(_){var D=b.codeClickEvents;(0,r.default)(D).call(D,function(k){return k(_)})}}),x.on("click",function(C){var _=null,w=C.target,S=s.default(w);if(S.getNodeName()==="HR"&&(_=S),!!_){y.selection.createRangeByElem(_),y.selection.restoreSelection();var M=b.splitLineEvents;(0,r.default)(M).call(M,function(D){return D(_)})}}),y.$toolbarElem.on("click",function(C){var _=b.toolbarClickEvents;(0,r.default)(_).call(_,function(w){return w(C)})}),y.$textContainerElem.on("mousedown",function(C){var _=C.target,w=s.default(_);if(w.hasClass("w-e-img-drag-rb")){var S=b.imgDragBarMouseDownEvents;(0,r.default)(S).call(S,function(M){return M()})}}),x.on("click",function(C){var _=null,w=C.target;if(_=s.default(w).parentUntilEditor("TABLE",y,w),!!_){var S=b.tableClickEvents;(0,r.default)(S).call(S,function(M){return M(_,C)})}}),x.on("keydown",function(C){if(C.keyCode===13){var _=b.enterDownEvents;(0,r.default)(_).call(_,function(w){return w(C)})}}),x.on("click",function(C){var _=null,w=C.target,S=s.default(w);if(S.getNodeName()==="VIDEO"&&(C.stopPropagation(),_=S),!!_){var M=b.videoClickEvents;(0,r.default)(M).call(M,function(D){return D(_)})}})},A}();n.default=h},function(u,n,e){var t=e(284);u.exports=t},function(u,n,e){var t=e(285),i=Array.prototype;u.exports=function(o){var r=o.find;return o===i||o instanceof Array&&r===i.find?t:r}},function(u,n,e){e(286);var t=e(15);u.exports=t("Array").find},function(u,n,e){var t=e(5),i=e(32).find,o=e(82),r=e(22),f="find",g=!0,c=r(f);f in[]&&Array(1)[f](function(){g=!1}),t({target:"Array",proto:!0,forced:g||!c},{find:function(m){return i(this,m,arguments.length>1?arguments[1]:void 0)}}),o(f)},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(2),r=o.__importDefault(e(288)),f=o.__importStar(e(289)),g=o.__importDefault(e(290)),c=o.__importDefault(e(291)),m=o.__importDefault(e(298));function s(a){var d=a.editor,l=a.eventHooks;r.default(d,l.enterUpEvents,l.enterDownEvents),f.default(d,l.deleteUpEvents,l.deleteDownEvents),f.cutToKeepP(d,l.keyupEvents),g.default(d,l.tabDownEvents),c.default(d,l.pasteEvents),m.default(d,l.imgClickEvents)}n.default=s},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(27));(0,i.default)(n,"__esModule",{value:!0});var r=e(2),f=e(7),g=r.__importDefault(e(3));function c(m,s,a){function d(p){var h,A=g.default(f.EMPTY_P);if(A.insertBefore(p),(0,o.default)(h=p.html()).call(h,"<img")>=0){A.remove();return}m.selection.createRangeByElem(A,!0,!0),m.selection.restoreSelection(),p.remove()}function l(){var p=m.$textElem,h=m.selection.getSelectionContainerElem(),A=h.parent();if(A.html()==="<code><br></code>"){d(A);return}if(h.getNodeName()==="FONT"&&h.text()===""&&h.attr("face")==="monospace"){d(A);return}if(A.equal(p)){var y=h.getNodeName();y==="P"&&h.attr("data-we-empty-p")===null||h.text()||d(h)}}s.push(l);function v(p){var h;m.selection.saveRange((h=getSelection())===null||h===void 0?void 0:h.getRangeAt(0));var A=m.selection.getSelectionContainerElem();A.id===m.textElemId&&(p.preventDefault(),m.cmd.do("insertHTML","<p><br></p>"))}a.push(v)}n.default=c},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(17)),r=t(e(28));(0,i.default)(n,"__esModule",{value:!0}),n.cutToKeepP=void 0;var f=e(2),g=e(7),c=f.__importDefault(e(3));function m(a,d,l){function v(){var h=a.$textElem,A=a.$textElem.html(),y=a.$textElem.text(),x=(0,o.default)(A).call(A),b=["<p><br></p>","<br>",'<p data-we-empty-p=""></p>',g.EMPTY_P];if(/^\s*$/.test(y)&&(!x||(0,r.default)(b).call(b,x))){h.html(g.EMPTY_P);var E=h.getNode();a.selection.createRangeByElems(E.childNodes[0],E.childNodes[0]);var C=a.selection.getSelectionContainerElem();a.selection.restoreSelection(),a.selection.moveCursor(C.getNode(),0)}}d.push(v);function p(h){var A,y=a.$textElem,x=(0,o.default)(A=y.html().toLowerCase()).call(A);if(x===g.EMPTY_P){h.preventDefault();return}}l.push(p)}function s(a,d){function l(v){var p;if(v.keyCode===88){var h=a.$textElem,A=(0,o.default)(p=h.html().toLowerCase()).call(p);if(!A||A==="<br>"){var y=c.default(g.EMPTY_P);h.html(" "),h.append(y),a.selection.createRangeByElem(y,!1,!0),a.selection.restoreSelection(),a.selection.moveCursor(y.getNode(),0)}}}d.push(l)}n.cutToKeepP=s,n.default=m},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});function o(r,f){function g(){if(r.cmd.queryCommandSupported("insertHTML")){var c=r.selection.getSelectionContainerElem();if(c){var m=c.parent(),s=c.getNodeName(),a=m.getNodeName();s=="CODE"||a==="CODE"||a==="PRE"||/hljs/.test(a)?r.cmd.do("insertHTML",r.config.languageTab):r.cmd.do("insertHTML","&nbsp;&nbsp;&nbsp;&nbsp;")}}}f.push(g)}n.default=o},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(17)),r=t(e(4));(0,i.default)(n,"__esModule",{value:!0});var f=e(131),g=e(6),c=e(7);function m(v){var p,h=(0,o.default)(p=v.replace(/<div>/gim,"<p>").replace(/<\/div>/gim,"</p>")).call(p),A=document.createElement("div");return A.innerHTML=h,A.innerHTML.replace(/<p><\/p>/gim,"")}function s(v){var p=v.replace(/<br>|<br\/>/gm,`
`).replace(/<[^>]+>/gm,"");return p}function a(v){var p;if(v==="")return!1;var h=document.createElement("div");return h.innerHTML=v,((p=h.firstChild)===null||p===void 0?void 0:p.nodeName)==="P"}function d(v){if(!(v!=null&&v.length))return!1;var p=v.elems[0];return p.nodeName==="P"&&p.innerHTML==="<br>"}function l(v,p){function h(A){var y=v.config,x=y.pasteFilterStyle,b=y.pasteIgnoreImg,E=y.pasteTextHandle,C=f.getPasteHtml(A,x,b),_=f.getPasteText(A);_=_.replace(/\n/gm,"<br>");var w=v.selection.getSelectionContainerElem();if(w){var S=w==null?void 0:w.getNodeName(),M=w==null?void 0:w.getNodeTop(v),D="";if(M.elems[0]&&(D=M==null?void 0:M.getNodeName()),S==="CODE"||D==="PRE"){E&&g.isFunction(E)&&(_=""+(E(_)||"")),v.cmd.do("insertHTML",s(_));return}if(c.urlRegex.test(_)&&x){E&&g.isFunction(E)&&(_=""+(E(_)||""));var k=_.replace(c.urlRegex,function(z){return'<a href="'+z+'" target="_blank">'+z+"</a>"}),N=v.selection.getRange(),T=document.createElement("div"),B=document.createDocumentFragment();if(T.innerHTML=k,N==null)return;for(;T.childNodes.length;)B.append(T.childNodes[0]);var R=B.querySelectorAll("a");(0,r.default)(R).call(R,function(z){z.innerText=z.href}),N.insertNode&&(N.deleteContents(),N.insertNode(B)),v.selection.clearWindowSelectionRange();return}if(C)try{E&&g.isFunction(E)&&(C=""+(E(C)||""));var I=/[\.\#\@]?\w+[ ]+\{[^}]*\}/.test(C);if(I&&x)v.cmd.do("insertHTML",""+m(_));else{var P=m(C);if(a(P)){var H=v.$textElem;if(v.cmd.do("insertHTML",P),H.equal(w)){v.selection.createEmptyRange();return}d(M)&&M.remove()}else v.cmd.do("insertHTML",P)}}catch{E&&g.isFunction(E)&&(_=""+(E(_)||"")),v.cmd.do("insertHTML",""+m(_))}}}p.push(h)}n.default=l},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(17)),r=t(e(4)),f=t(e(28));(0,i.default)(n,"__esModule",{value:!0});var g=e(2),c=e(293),m=g.__importDefault(e(297));function s(p){var h=/<span>.*?<\/span>/gi,A=/<span>(.*?)<\/span>/;return p.replace(h,function(y){var x=y.match(A);return x==null?"":x[1]})}function a(p,h){var A;return p=(0,o.default)(A=p.toLowerCase()).call(A),!!(c.IGNORE_TAGS.has(p)||h&&p==="img")}function d(p,h){var A="";A="<"+p;var y=[];(0,r.default)(h).call(h,function(b){y.push(b.name+'="'+b.value+'"')}),y.length>0&&(A=A+" "+y.join(" "));var x=c.EMPTY_TAGS.has(p);return A=A+(x?"/":"")+">",A}function l(p){return"</"+p+">"}function v(p,h,A){h===void 0&&(h=!0),A===void 0&&(A=!1);var y=[],x="";function b(w){w=(0,o.default)(w).call(w),w&&(c.EMPTY_TAGS.has(w)||(x=w))}function E(){x=""}var C=new m.default;C.parse(p,{startElement:function(w,S){if(b(w),!a(w,A)){var M=c.NECESSARY_ATTRS.get(w)||[],D=[];(0,r.default)(S).call(S,function(N){var T=N.name;if(T==="style"){h||D.push(N);return}(0,f.default)(M).call(M,T)!==!1&&D.push(N)});var k=d(w,D);y.push(k)}},characters:function(w){w&&(a(x,A)||y.push(w))},endElement:function(w){if(!a(w,A)){var S=l(w);y.push(S),E()}},comment:function(w){b(w)}});var _=y.join("");return _=s(_),_}n.default=v},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(132)),r=t(e(121));(0,i.default)(n,"__esModule",{value:!0}),n.TOP_LEVEL_TAGS=n.EMPTY_TAGS=n.NECESSARY_ATTRS=n.IGNORE_TAGS=void 0,n.IGNORE_TAGS=new o.default(["doctype","!doctype","html","head","meta","body","script","style","link","frame","iframe","title","svg","center","o:p"]),n.NECESSARY_ATTRS=new r.default([["img",["src","alt"]],["a",["href","target"]],["td",["colspan","rowspan"]],["th",["colspan","rowspan"]]]),n.EMPTY_TAGS=new o.default(["area","base","basefont","br","col","hr","img","input","isindex","embed"]),n.TOP_LEVEL_TAGS=new o.default(["h1","h2","h3","h4","h5","p","ul","ol","table","blockquote","pre","hr","form"])},function(u,n,e){var t=e(295);u.exports=t},function(u,n,e){e(296),e(61),e(50),e(54);var t=e(9);u.exports=t.Set},function(u,n,e){var t=e(122),i=e(124);u.exports=t("Set",function(o){return function(){return o(this,arguments.length?arguments[0]:void 0)}},i)},function(u,n){function e(){}e.prototype={handler:null,startTagRe:/^<([^>\s\/]+)((\s+[^=>\s]+(\s*=\s*((\"[^"]*\")|(\'[^']*\')|[^>\s]+))?)*)\s*\/?\s*>/m,endTagRe:/^<\/([^>\s]+)[^>]*>/m,attrRe:/([^=\s]+)(\s*=\s*((\"([^"]*)\")|(\'([^']*)\')|[^>\s]+))?/gm,parse:function(t,i){i&&(this.contentHandler=i);for(var o,r,f,g=!1,c=this;t.length>0;)t.substring(0,4)=="<!--"?(f=t.indexOf("-->"),f!=-1?(this.contentHandler.comment(t.substring(4,f)),t=t.substring(f+3),g=!1):g=!0):t.substring(0,2)=="</"?this.endTagRe.test(t)?(o=RegExp.lastMatch,r=RegExp.rightContext,o.replace(this.endTagRe,function(){return c.parseEndTag.apply(c,arguments)}),t=r,g=!1):g=!0:t.charAt(0)=="<"&&(this.startTagRe.test(t)?(o=RegExp.lastMatch,r=RegExp.rightContext,o.replace(this.startTagRe,function(){return c.parseStartTag.apply(c,arguments)}),t=r,g=!1):g=!0),g&&(f=t.indexOf("<"),f==-1?(this.contentHandler.characters(t),t=""):(this.contentHandler.characters(t.substring(0,f)),t=t.substring(f))),g=!0},parseStartTag:function(t,i,o){var r=this.parseAttributes(i,o);this.contentHandler.startElement(i,r)},parseEndTag:function(t,i){this.contentHandler.endElement(i)},parseAttributes:function(t,i){var o=this,r=[];return i.replace(this.attrRe,function(f,g,c,m,s,a,d,l){r.push(o.parseAttribute(t,f,g,c,m,s,a,d,l))}),r},parseAttribute:function(t,i,o){var r="";arguments[7]?r=arguments[8]:arguments[5]?r=arguments[6]:arguments[3]&&(r=arguments[4]);var f=!r&&!arguments[3];return{name:o,value:f?null:r}}},u.exports=e},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});function o(r,f){function g(c){r.selection.createRangeByElem(c),r.selection.restoreSelection()}f.push(g)}n.default=o},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(4));(0,i.default)(n,"__esModule",{value:!0});var r=e(2),f=e(6),g=r.__importDefault(e(3));function c(m){var s=[],a=m.childNodes()||[];return(0,o.default)(a).call(a,function(d){var l,v=d.nodeType;if(v===3&&(l=d.textContent||"",l=f.replaceHtmlSymbol(l)),v===1){l={},l=l,l.tag=d.nodeName.toLowerCase();for(var p=[],h=d.attributes,A=h.length||0,y=0;y<A;y++){var x=h[y];p.push({name:x.name,value:x.value})}l.attrs=p,l.children=c(g.default(d))}l&&s.push(l)}),s}n.default=c},function(u,n,e){var t=e(0),i=t(e(92)),o=t(e(1)),r=t(e(4));(0,o.default)(n,"__esModule",{value:!0});var f=e(2),g=f.__importDefault(e(3));function c(m,s){s===void 0&&(s=document.createElement("div"));var a=s;return(0,r.default)(m).call(m,function(d){var l;if(typeof d=="string"&&(l=document.createTextNode(d)),(0,i.default)(d)==="object"){var v;l=document.createElement(d.tag),(0,r.default)(v=d.attrs).call(v,function(p){g.default(l).attr(p.name,p.value)}),d.children&&d.children.length>0&&c(d.children,l.getRootNode())}l&&a.appendChild(l)}),g.default(a)}n.default=c},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(89)),r=t(e(70)),f=t(e(28)),g=t(e(302)),c=t(e(4)),m=t(e(94)),s=t(e(133)),a=t(e(46)),d=t(e(57));(0,i.default)(n,"__esModule",{value:!0});var l=e(2),v=l.__importDefault(e(87)),p=l.__importDefault(e(314)),h=l.__importDefault(e(3)),A=function(){function y(x){this.editor=x,this.menuList=[],this.constructorList=p.default}return y.prototype.extend=function(x,b){!b||typeof b!="function"||(this.constructorList[x]=b)},y.prototype.init=function(){var x,b,E=this,C=this.editor.config,_=C.excludeMenus;(0,o.default)(_)===!1&&(_=[]),C.menus=(0,r.default)(x=C.menus).call(x,function(B){return(0,f.default)(_).call(_,B)===!1});var w=(0,g.default)(v.default.globalCustomMenuConstructorList);w=(0,r.default)(w).call(w,function(B){return(0,f.default)(_).call(_,B)}),(0,c.default)(w).call(w,function(B){delete v.default.globalCustomMenuConstructorList[B]}),(0,c.default)(b=C.menus).call(b,function(B){var R=E.constructorList[B];E._initMenuList(B,R)});for(var S=0,M=(0,m.default)(v.default.globalCustomMenuConstructorList);S<M.length;S++){var D=M[S],k=D[0],N=D[1],T=N;this._initMenuList(k,T)}this._addToToolbar(),C.showMenuTooltips&&this._bindMenuTooltips()},y.prototype._initMenuList=function(x,b){var E;if(!(b==null||typeof b!="function")&&!(0,s.default)(E=this.menuList).call(E,function(_){return _.key===x})){var C=new b(this.editor);C.key=x,this.menuList.push(C)}},y.prototype._bindMenuTooltips=function(){var x=this.editor,b=x.$toolbarElem,E=x.config,C=E.menuTooltipPosition,_=h.default('<div class="w-e-menu-tooltip w-e-menu-tooltip-'+C+`">
            <div class="w-e-menu-tooltip-item-wrapper">
              <div></div>
            </div>
          </div>`);_.css("visibility","hidden"),b.append(_),_.css("z-index",x.zIndex.get("tooltip"));var w=0;function S(){w&&clearTimeout(w)}function M(){S(),_.css("visibility","hidden")}b.on("mouseover",function(D){var k=D.target,N=h.default(k),T,B;if(N.isContain(b)){M();return}if(N.parentUntil(".w-e-droplist")!=null)M();else if(N.attr("data-title"))T=N.attr("data-title"),B=N;else{var R=N.parentUntil(".w-e-menu");R!=null&&(T=R.attr("data-title"),B=R)}if(T&&B){S();var I=B.getOffsetData();_.text(x.i18next.t("menus.title."+T));var P=_.getOffsetData(),H=I.left+I.width/2-P.width/2;_.css("left",H+"px"),C==="up"?_.css("top",I.top-P.height-8+"px"):C==="down"&&_.css("top",I.top+I.height+8+"px"),w=(0,a.default)(function(){_.css("visibility","visible")},200)}else M()}).on("mouseleave",function(){M()})},y.prototype._addToToolbar=function(){var x,b=this.editor,E=b.$toolbarElem;(0,c.default)(x=this.menuList).call(x,function(C){var _=C.$elem;_&&E.append(_)})},y.prototype.menuFind=function(x){for(var b=this.menuList,E=0,C=b.length;E<C;E++)if(b[E].key===x)return b[E];return b[0]},y.prototype.changeActive=function(){var x;(0,c.default)(x=this.menuList).call(x,function(b){var E;(0,a.default)((0,d.default)(E=b.tryChangeActive).call(E,b),100)})},y}();n.default=A},function(u,n,e){u.exports=e(303)},function(u,n,e){var t=e(304);u.exports=t},function(u,n,e){e(305);var t=e(9);u.exports=t.Object.keys},function(u,n,e){var t=e(5),i=e(31),o=e(52),r=e(11),f=r(function(){o(1)});t({target:"Object",stat:!0,forced:f},{keys:function(g){return o(i(g))}})},function(u,n,e){var t=e(307);u.exports=t},function(u,n,e){e(308);var t=e(9);u.exports=t.Object.entries},function(u,n,e){var t=e(5),i=e(309).entries;t({target:"Object",stat:!0},{entries:function(o){return i(o)}})},function(u,n,e){var t=e(14),i=e(52),o=e(30),r=e(59).f,f=function(g){return function(c){for(var m=o(c),s=i(m),a=s.length,d=0,l=[],v;a>d;)v=s[d++],(!t||r.call(m,v))&&l.push(g?[v,m[v]]:m[v]);return l}};u.exports={entries:f(!0),values:f(!1)}},function(u,n,e){var t=e(311);u.exports=t},function(u,n,e){var t=e(312),i=Array.prototype;u.exports=function(o){var r=o.some;return o===i||o instanceof Array&&r===i.some?t:r}},function(u,n,e){e(313);var t=e(15);u.exports=t("Array").some},function(u,n,e){var t=e(5),i=e(32).some,o=e(67),r=e(22),f=o("some"),g=r("some");t({target:"Array",proto:!0,forced:!f||!g},{some:function(c){return i(this,c,arguments.length>1?arguments[1]:void 0)}})},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(2),r=o.__importDefault(e(315)),f=o.__importDefault(e(316)),g=o.__importDefault(e(321)),c=o.__importDefault(e(326)),m=o.__importDefault(e(327)),s=o.__importDefault(e(328)),a=o.__importDefault(e(329)),d=o.__importDefault(e(331)),l=o.__importDefault(e(333)),v=o.__importDefault(e(334)),p=o.__importDefault(e(337)),h=o.__importDefault(e(338)),A=o.__importDefault(e(339)),y=o.__importDefault(e(350)),x=o.__importDefault(e(365)),b=o.__importDefault(e(369)),E=o.__importDefault(e(137)),C=o.__importDefault(e(378)),_=o.__importDefault(e(380)),w=o.__importDefault(e(381)),S=o.__importDefault(e(382)),M=o.__importDefault(e(401)),D=o.__importDefault(e(406)),k=o.__importDefault(e(409));n.default={bold:r.default,head:f.default,italic:c.default,link:g.default,underline:m.default,strikeThrough:s.default,fontName:a.default,fontSize:d.default,justify:l.default,quote:v.default,backColor:p.default,foreColor:h.default,video:A.default,image:y.default,indent:x.default,emoticon:b.default,list:E.default,lineHeight:C.default,undo:_.default,redo:w.default,table:S.default,code:M.default,splitLine:D.default,todo:k.default}},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(2),r=o.__importDefault(e(23)),f=o.__importDefault(e(3)),g=function(c){o.__extends(m,c);function m(s){var a=this,d=f.default(`<div class="w-e-menu" data-title="加粗">
                <i class="w-e-icon-bold"></i>
            </div>`);return a=c.call(this,d,s)||this,a}return m.prototype.clickHandler=function(){var s=this.editor,a=s.selection.isSelectionEmpty();a&&s.selection.createEmptyRange(),s.cmd.do("bold"),a&&(s.selection.collapseRange(),s.selection.restoreSelection())},m.prototype.tryChangeActive=function(){var s=this.editor;s.cmd.queryCommandState("bold")?this.active():this.unActive()},m}(r.default);n.default=g},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(27)),r=t(e(29)),f=t(e(4)),g=t(e(317)),c=t(e(28));(0,i.default)(n,"__esModule",{value:!0});var m=e(2),s=m.__importDefault(e(24)),a=m.__importDefault(e(3)),d=e(6),l=e(7),v=function(p){m.__extends(h,p);function h(A){var y=this,x=a.default('<div class="w-e-menu" data-title="标题"><i class="w-e-icon-header"></i></div>'),b={width:100,title:"设置标题",type:"list",list:[{$elem:a.default("<h1>H1</h1>"),value:"<h1>"},{$elem:a.default("<h2>H2</h2>"),value:"<h2>"},{$elem:a.default("<h3>H3</h3>"),value:"<h3>"},{$elem:a.default("<h4>H4</h4>"),value:"<h4>"},{$elem:a.default("<h5>H5</h5>"),value:"<h5>"},{$elem:a.default("<p>"+A.i18next.t("menus.dropListMenu.head.正文")+"</p>"),value:"<p>"}],clickHandler:function(C){y.command(C)}};y=p.call(this,x,A,b)||this;var E=A.config.onCatalogChange;return E&&(y.oldCatalogs=[],y.addListenerCatalog(),y.getCatalogs()),y}return h.prototype.command=function(A){var y=this.editor,x=y.selection.getSelectionContainerElem();if(x&&y.$textElem.equal(x))this.setMultilineHead(A);else{var b;if((0,o.default)(b=["OL","UL","LI","TABLE","TH","TR","CODE","HR"]).call(b,a.default(x).getNodeName())>-1)return;y.cmd.do("formatBlock",A)}A!=="<p>"&&this.addUidForSelectionElem()},h.prototype.addUidForSelectionElem=function(){var A=this.editor,y=A.selection.getSelectionContainerElem(),x=d.getRandomCode();a.default(y).attr("id",x)},h.prototype.addListenerCatalog=function(){var A=this,y=this.editor;y.txt.eventHooks.changeEvents.push(function(){A.getCatalogs()})},h.prototype.getCatalogs=function(){var A=this.editor,y=this.editor.$textElem,x=A.config.onCatalogChange,b=(0,r.default)(y).call(y,"h1,h2,h3,h4,h5"),E=[];(0,f.default)(b).call(b,function(C,_){var w=a.default(C),S=w.attr("id"),M=w.getNodeName(),D=w.text();S||(S=d.getRandomCode(),w.attr("id",S)),D&&E.push({tag:M,id:S,text:D})}),(0,g.default)(this.oldCatalogs)!==(0,g.default)(E)&&(this.oldCatalogs=E,x&&x(E))},h.prototype.setMultilineHead=function(A){var y=this,x,b,E=this.editor,C=E.selection,_=(x=C.getSelectionContainerElem())===null||x===void 0?void 0:x.elems[0],w=["IMG","VIDEO","TABLE","TH","TR","UL","OL","PRE","HR","BLOCKQUOTE"],S=a.default(C.getSelectionStartElem()),M=a.default(C.getSelectionEndElem());M.elems[0].outerHTML===a.default(l.EMPTY_P).elems[0].outerHTML&&!M.elems[0].nextSibling&&(M=M.prev());var D=[];D.push(S.getNodeTop(E));var k=[],N=(b=C.getRange())===null||b===void 0?void 0:b.commonAncestorContainer.childNodes;N==null||(0,f.default)(N).call(N,function(R,I){R===D[0].getNode()&&k.push(I),R===M.getNodeTop(E).getNode()&&k.push(I)});for(var T=0;D[T].getNode()!==M.getNodeTop(E).getNode();){if(!D[T].elems[0])return;var B=a.default(D[T].next().getNode());D.push(B),T++}D==null||(0,f.default)(D).call(D,function(R,I){if(!y.hasTag(R,w)){var P=a.default(A),H=R.parent().getNode();P.html(""+R.html()),H.insertBefore(P.getNode(),R.getNode()),R.remove()}}),C.createRangeByElems(_.children[k[0]],_.children[k[1]])},h.prototype.hasTag=function(A,y){var x=this,b;if(!A)return!1;if((0,c.default)(y).call(y,A==null?void 0:A.getNodeName()))return!0;var E=!1;return(b=A.children())===null||b===void 0||(0,f.default)(b).call(b,function(C){E=x.hasTag(a.default(C),y)}),E},h.prototype.tryChangeActive=function(){var A=this.editor,y=/^h/i,x=A.cmd.queryCommandValue("formatBlock");y.test(x)?this.active():this.unActive()},h}(s.default);n.default=v},function(u,n,e){u.exports=e(318)},function(u,n,e){var t=e(319);u.exports=t},function(u,n,e){e(320);var t=e(9);t.JSON||(t.JSON={stringify:JSON.stringify}),u.exports=function(i,o,r){return t.JSON.stringify.apply(null,arguments)}},function(u,n,e){var t=e(5),i=e(36),o=e(11),r=i("JSON","stringify"),f=/[\uD800-\uDFFF]/g,g=/^[\uD800-\uDBFF]$/,c=/^[\uDC00-\uDFFF]$/,m=function(a,d,l){var v=l.charAt(d-1),p=l.charAt(d+1);return g.test(a)&&!c.test(p)||c.test(a)&&!g.test(v)?"\\u"+a.charCodeAt(0).toString(16):a},s=o(function(){return r("\uDF06\uD834")!=='"\\udf06\\ud834"'||r("\uDEAD")!=='"\\udead"'});r&&t({target:"JSON",stat:!0,forced:s},{stringify:function(a,d,l){var v=r.apply(null,arguments);return typeof v=="string"?v.replace(f,m):v}})},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(17));(0,i.default)(n,"__esModule",{value:!0});var r=e(2),f=r.__importDefault(e(38)),g=r.__importDefault(e(3)),c=r.__importDefault(e(322)),m=r.__importStar(e(96)),s=r.__importDefault(e(33)),a=r.__importDefault(e(324)),d=e(7),l=function(v){r.__extends(p,v);function p(h){var A=this,y=g.default('<div class="w-e-menu" data-title="链接"><i class="w-e-icon-link"></i></div>');return A=v.call(this,y,h)||this,a.default(h),A}return p.prototype.clickHandler=function(){var h=this.editor,A,y=h.selection.getSelectionContainerElem(),x=h.$textElem,b=x.html(),E=(0,o.default)(b).call(b);if(E===d.EMPTY_P){var C=x.children();h.selection.createRangeByElem(C,!0,!0),y=h.selection.getSelectionContainerElem()}if(!(y&&h.$textElem.equal(y)))if(this.isActive){var _="",w="";if(A=h.selection.getSelectionContainerElem(),!A)return;if(A.getNodeName()!=="A"){var S=m.getParentNodeA(A);A=g.default(S)}_=A.elems[0].innerText,w=A.attr("href"),this.createPanel(_,w)}else h.selection.isSelectionEmpty()?this.createPanel("",""):this.createPanel(h.selection.getSelectionText(),"")},p.prototype.createPanel=function(h,A){var y=c.default(this.editor,h,A),x=new s.default(this,y);x.create()},p.prototype.tryChangeActive=function(){var h=this.editor;m.default(h)?this.active():this.unActive()},p}(f.default);n.default=l},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(28)),r=t(e(17)),f=t(e(29));(0,i.default)(n,"__esModule",{value:!0});var g=e(2),c=e(6),m=g.__importDefault(e(3)),s=g.__importStar(e(96)),a=e(323);function d(l,v,p){var h=c.getRandom("input-link"),A=c.getRandom("input-text"),y=c.getRandom("btn-ok"),x=c.getRandom("btn-del"),b=s.default(l)?"inline-block":"none",E;function C(){if(s.default(l)){var D=l.selection.getSelectionContainerElem();D&&(l.selection.createRangeByElem(D),l.selection.restoreSelection(),E=D)}}function _(D,k){var N=D.replace(/</g,"&lt;").replace(/>/g,"&gt;"),T=m.default('<a target="_blank">'+N+"</a>"),B=T.elems[0];B.innerText=D,B.href=k,s.default(l)&&C(),l.cmd.do("insertElem",T)}function w(){if(s.default(l))if(C(),E.getNodeName()==="A"){var D,k=E.elems[0],N=k.parentElement;N&&(0,o.default)(D=s.EXTRA_TAG).call(D,N.nodeName)?N.innerHTML=k.innerHTML:l.cmd.do("insertHTML","<span>"+k.innerHTML+"</span>")}else{var T=s.getParentNodeA(E),B=T.innerHTML;l.cmd.do("insertHTML","<span>"+B+"</span>")}}function S(D,k){var N=l.config.linkCheck(D,k);if(N!==void 0){if(N===!0)return!0;l.config.customAlert(N,"warning")}return!1}var M={width:300,height:0,tabs:[{title:l.i18next.t("menus.panelMenus.link.链接"),tpl:`<div>
                        <input
                            id="`+A+`"
                            type="text"
                            class="block"
                            placeholder="`+l.i18next.t("menus.panelMenus.link.链接文字")+`"/>
                        </td>
                        <input
                            id="`+h+`"
                            type="text"
                            class="block"
                            placeholder="`+l.i18next.t("如")+` https://..."/>
                        </td>
                        <div class="w-e-button-container">
                            <button type="button" id="`+y+`" class="right">
                                `+l.i18next.t("插入")+`
                            </button>
                            <button type="button" id="`+x+'" class="gray right" style="display:'+b+`">
                                `+l.i18next.t("menus.panelMenus.link.取消链接")+`
                            </button>
                        </div>
                    </div>`,events:[{selector:"#"+y,type:"click",fn:function(){var D,k,N,T,B,R=l.selection.getSelectionContainerElem(),I=R==null?void 0:R.elems[0];l.selection.restoreSelection();var P=l.selection.getSelectionRangeTopNodes()[0].getNode(),H=window.getSelection(),z=m.default("#"+h),O=m.default("#"+A),$=(0,r.default)(D=z.val()).call(D),L=(0,r.default)(k=O.val()).call(k),U="";H&&!(H!=null&&H.isCollapsed)&&(U=(T=a.insertHtml(H,P))===null||T===void 0?void 0:(0,r.default)(T).call(T));var Y=U==null?void 0:U.replace(/<.*?>/g,""),G=(B=Y==null?void 0:Y.length)!==null&&B!==void 0?B:0;if(G<=L.length){var q=L.substring(0,G),ee=L.substring(G);Y===q&&(L=Y+ee)}if($&&(L||(L=$),!!S(L,$))){if((I==null?void 0:I.nodeName)==="A")return I.setAttribute("href",$),I.innerText=L,!0;if((I==null?void 0:I.nodeName)!=="A"&&(0,o.default)(N=s.EXTRA_TAG).call(N,I.nodeName)){var te=s.getParentNodeA(R);if(te)return te.setAttribute("href",$),I.innerText=L,!0}return _(L,$),!0}},bindEnter:!0},{selector:"#"+x,type:"click",fn:function(){return w(),!0}}]}],setLinkValue:function(D,k){var N="",T="",B;k==="text"&&(N="#"+A,T=v),k==="link"&&(N="#"+h,T=p),B=(0,f.default)(D).call(D,N).elems[0],B.value=T}};return M}n.default=d},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(4));(0,i.default)(n,"__esModule",{value:!0}),n.insertHtml=n.createPartHtml=n.makeHtmlString=n.getTopNode=void 0;function r(a,d){var l=a,v=a;do{if(l.textContent===d)break;v=l,l.parentNode&&(l=l==null?void 0:l.parentNode)}while((l==null?void 0:l.nodeName)!=="P");return v}n.getTopNode=r;function f(a,d){var l=a.nodeName,v="";if(a.nodeType===3||/^(h|H)[1-6]$/.test(l))return d;if(a.nodeType===1){var p=a.getAttribute("style"),h=a.getAttribute("face"),A=a.getAttribute("color");p&&(v=v+(' style="'+p+'"')),h&&(v=v+(' face="'+h+'"')),A&&(v=v+(' color="'+A+'"'))}return l=l.toLowerCase(),"<"+l+v+">"+d+"</"+l+">"}n.makeHtmlString=f;function g(a,d,l,v){var p,h=(p=d.textContent)===null||p===void 0?void 0:p.substring(l,v),A=d,y="";do y=f(A,h??""),h=y,A=A==null?void 0:A.parentElement;while(A&&A.textContent!==a);return y}n.createPartHtml=g;function c(a,d){var l,v,p,h,A,y=a.anchorNode,x=a.focusNode,b=a.anchorOffset,E=a.focusOffset,C=(l=d.textContent)!==null&&l!==void 0?l:"",_=m(d),w="",S="",M="",D="",k=y,N=x,T=y;if(y!=null&&y.isEqualNode(x??null)){var B=g(C,y,b,E);return B=s(_,B),B}for(y&&(S=g(C,y,b??0)),x&&(D=g(C,x,0,E)),y&&(k=r(y,C)),x&&(N=r(x,C)),T=(v=k==null?void 0:k.nextSibling)!==null&&v!==void 0?v:y;!(T!=null&&T.isEqualNode(N??null));){var R=T==null?void 0:T.nodeName;if(R==="#text")M=M+(T==null?void 0:T.textContent);else{var I=(h=(p=T==null?void 0:T.firstChild)===null||p===void 0?void 0:p.parentElement)===null||h===void 0?void 0:h.innerHTML;T&&(M=M+f(T,I??""))}var P=(A=T==null?void 0:T.nextSibling)!==null&&A!==void 0?A:T;if(P===T)break;T=P}return w=""+S+M+D,w=s(_,w),w}n.insertHtml=c;function m(a){for(var d,l=(d=a.textContent)!==null&&d!==void 0?d:"",v=[];(a==null?void 0:a.textContent)===l;)a.nodeName!=="P"&&a.nodeName!=="TABLE"&&v.push(a),a=a.childNodes[0];return v}function s(a,d){return(0,o.default)(a).call(a,function(l){d=f(l,d)}),d}},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(2),r=o.__importDefault(e(325));function f(g){r.default(g)}n.default=f},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(28));(0,i.default)(n,"__esModule",{value:!0});var r=e(2),f=r.__importDefault(e(3)),g=r.__importDefault(e(39)),c=e(96);function m(a){var d;function l(p){var h=[{$elem:f.default("<span>"+a.i18next.t("menus.panelMenus.link.查看链接")+"</span>"),onClick:function(A,y){var x=y.attr("href");return window.open(x,"_target"),!0}},{$elem:f.default("<span>"+a.i18next.t("menus.panelMenus.link.取消链接")+"</span>"),onClick:function(A,y){var x,b;A.selection.createRangeByElem(y),A.selection.restoreSelection();var E=y.childNodes();if((E==null?void 0:E.getNodeName())==="IMG"){var C=(b=(x=A.selection.getSelectionContainerElem())===null||x===void 0?void 0:x.children())===null||b===void 0?void 0:b.elems[0].children[0];A.cmd.do("insertHTML",`<img 
                                src=`+(C==null?void 0:C.getAttribute("src"))+` 
                                style=`+(C==null?void 0:C.getAttribute("style"))+">")}else{var _,w=y.elems[0],S=w.innerHTML,M=w.parentElement;M&&(0,o.default)(_=c.EXTRA_TAG).call(_,M.nodeName)?M.innerHTML=S:A.cmd.do("insertHTML","<span>"+S+"</span>")}return!0}}];d=new g.default(a,p,h),d.create()}function v(){d&&(d.remove(),d=null)}return{showLinkTooltip:l,hideLinkTooltip:v}}function s(a){var d=m(a),l=d.showLinkTooltip,v=d.hideLinkTooltip;a.txt.eventHooks.linkClickEvents.push(l),a.txt.eventHooks.clickEvents.push(v),a.txt.eventHooks.keyupEvents.push(v),a.txt.eventHooks.toolbarClickEvents.push(v),a.txt.eventHooks.menuClickEvents.push(v),a.txt.eventHooks.textScrollEvents.push(v)}n.default=s},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(2),r=o.__importDefault(e(23)),f=o.__importDefault(e(3)),g=function(c){o.__extends(m,c);function m(s){var a=this,d=f.default(`<div class="w-e-menu" data-title="斜体">
                <i class="w-e-icon-italic"></i>
            </div>`);return a=c.call(this,d,s)||this,a}return m.prototype.clickHandler=function(){var s=this.editor,a=s.selection.isSelectionEmpty();a&&s.selection.createEmptyRange(),s.cmd.do("italic"),a&&(s.selection.collapseRange(),s.selection.restoreSelection())},m.prototype.tryChangeActive=function(){var s=this.editor;s.cmd.queryCommandState("italic")?this.active():this.unActive()},m}(r.default);n.default=g},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(2),r=o.__importDefault(e(23)),f=o.__importDefault(e(3)),g=function(c){o.__extends(m,c);function m(s){var a=this,d=f.default(`<div class="w-e-menu" data-title="下划线">
                <i class="w-e-icon-underline"></i>
            </div>`);return a=c.call(this,d,s)||this,a}return m.prototype.clickHandler=function(){var s=this.editor,a=s.selection.isSelectionEmpty();a&&s.selection.createEmptyRange(),s.cmd.do("underline"),a&&(s.selection.collapseRange(),s.selection.restoreSelection())},m.prototype.tryChangeActive=function(){var s=this.editor;s.cmd.queryCommandState("underline")?this.active():this.unActive()},m}(r.default);n.default=g},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(2),r=o.__importDefault(e(23)),f=o.__importDefault(e(3)),g=function(c){o.__extends(m,c);function m(s){var a=this,d=f.default(`<div class="w-e-menu" data-title="删除线">
                <i class="w-e-icon-strikethrough"></i>
            </div>`);return a=c.call(this,d,s)||this,a}return m.prototype.clickHandler=function(){var s=this.editor,a=s.selection.isSelectionEmpty();a&&s.selection.createEmptyRange(),s.cmd.do("strikeThrough"),a&&(s.selection.collapseRange(),s.selection.restoreSelection())},m.prototype.tryChangeActive=function(){var s=this.editor;s.cmd.queryCommandState("strikeThrough")?this.active():this.unActive()},m}(r.default);n.default=g},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(2),r=o.__importDefault(e(24)),f=o.__importDefault(e(3)),g=o.__importDefault(e(330)),c=function(m){o.__extends(s,m);function s(a){var d=this,l=f.default(`<div class="w-e-menu" data-title="字体">
                <i class="w-e-icon-font"></i>
            </div>`),v=new g.default(a.config.fontNames),p={width:100,title:"设置字体",type:"list",list:v.getItemList(),clickHandler:function(h){d.command(h)}};return d=m.call(this,l,a,p)||this,d}return s.prototype.command=function(a){var d,l=this.editor,v=l.selection.isSelectionEmpty(),p=(d=l.selection.getSelectionContainerElem())===null||d===void 0?void 0:d.elems[0];if(p!=null){var h=(p==null?void 0:p.nodeName.toLowerCase())!=="p",A=(p==null?void 0:p.getAttribute("face"))===a;if(v){if(h&&!A){var y=l.selection.getSelectionRangeTopNodes();l.selection.createRangeByElem(y[0]),l.selection.moveCursor(y[0].elems[0])}l.selection.setRangeToElem(p),l.selection.createEmptyRange()}l.cmd.do("fontName",a),v&&(l.selection.collapseRange(),l.selection.restoreSelection())}},s.prototype.tryChangeActive=function(){},s}(r.default);n.default=c},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(4));(0,i.default)(n,"__esModule",{value:!0});var r=e(2),f=r.__importDefault(e(3)),g=function(){function c(m){var s=this;this.itemList=[],(0,o.default)(m).call(m,function(a){var d=typeof a=="string"?a:a.value,l=typeof a=="string"?a:a.name;s.itemList.push({$elem:f.default(`<p style="font-family:'`+d+`'">`+l+"</p>"),value:l})})}return c.prototype.getItemList=function(){return this.itemList},c}();n.default=g},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(2),r=o.__importDefault(e(24)),f=o.__importDefault(e(3)),g=o.__importDefault(e(332)),c=function(m){o.__extends(s,m);function s(a){var d=this,l=f.default(`<div class="w-e-menu" data-title="字号">
                <i class="w-e-icon-text-heigh"></i>
            </div>`),v=new g.default(a.config.fontSizes),p={width:160,title:"设置字号",type:"list",list:v.getItemList(),clickHandler:function(h){d.command(h)}};return d=m.call(this,l,a,p)||this,d}return s.prototype.command=function(a){var d,l=this.editor,v=l.selection.isSelectionEmpty(),p=(d=l.selection.getSelectionContainerElem())===null||d===void 0?void 0:d.elems[0];p!=null&&(l.cmd.do("fontSize",a),v&&(l.selection.collapseRange(),l.selection.restoreSelection()))},s.prototype.tryChangeActive=function(){},s}(r.default);n.default=c},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(2),r=o.__importDefault(e(3)),f=function(){function g(c){this.itemList=[];for(var m in c){var s=c[m];this.itemList.push({$elem:r.default('<p style="font-size:'+m+'">'+s.name+"</p>"),value:s.value})}}return g.prototype.getItemList=function(){return this.itemList},g}();n.default=f},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(4)),r=t(e(27));(0,i.default)(n,"__esModule",{value:!0});var f=e(2),g=f.__importDefault(e(24)),c=f.__importDefault(e(3)),m=["LI"],s=["BLOCKQUOTE"],a=function(d){f.__extends(l,d);function l(v){var p=this,h=c.default('<div class="w-e-menu" data-title="对齐"><i class="w-e-icon-paragraph-left"></i></div>'),A={width:100,title:"对齐方式",type:"list",list:[{$elem:c.default(`<p>
                            <i class="w-e-icon-paragraph-left w-e-drop-list-item"></i>
                            `+v.i18next.t("menus.dropListMenu.justify.靠左")+`
                        </p>`),value:"left"},{$elem:c.default(`<p>
                            <i class="w-e-icon-paragraph-center w-e-drop-list-item"></i>
                            `+v.i18next.t("menus.dropListMenu.justify.居中")+`
                        </p>`),value:"center"},{$elem:c.default(`<p>
                            <i class="w-e-icon-paragraph-right w-e-drop-list-item"></i>
                            `+v.i18next.t("menus.dropListMenu.justify.靠右")+`
                        </p>`),value:"right"},{$elem:c.default(`<p>
                            <i class="w-e-icon-paragraph-justify w-e-drop-list-item"></i>
                            `+v.i18next.t("menus.dropListMenu.justify.两端")+`
                        </p>`),value:"justify"}],clickHandler:function(y){p.command(y)}};return p=d.call(this,h,v,A)||this,p}return l.prototype.command=function(v){var p=this.editor,h=p.selection,A=h.getSelectionContainerElem();h.saveRange();var y=p.selection.getSelectionRangeTopNodes();if(A!=null&&A.length)if(this.isSpecialNode(A,y[0])||this.isSpecialTopNode(y[0])){var x=this.getSpecialNodeUntilTop(A,y[0]);if(x==null)return;c.default(x).css("text-align",v)}else(0,o.default)(y).call(y,function(b){b.css("text-align",v)});h.restoreSelection()},l.prototype.getSpecialNodeUntilTop=function(v,p){for(var h=v.elems[0],A=p.elems[0];h!=null;){if((0,r.default)(m).call(m,h==null?void 0:h.nodeName)!==-1||h.parentNode===A)return h;h=h.parentNode}return h},l.prototype.isSpecialNode=function(v,p){var h=this.getSpecialNodeUntilTop(v,p);return h==null?!1:(0,r.default)(m).call(m,h.nodeName)!==-1},l.prototype.isSpecialTopNode=function(v){var p;return v==null?!1:(0,r.default)(s).call(s,(p=v.elems[0])===null||p===void 0?void 0:p.nodeName)!==-1},l.prototype.tryChangeActive=function(){},l}(g.default);n.default=a},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(4));(0,i.default)(n,"__esModule",{value:!0});var r=e(2),f=r.__importDefault(e(3)),g=r.__importDefault(e(23)),c=r.__importDefault(e(335)),m=r.__importDefault(e(336)),s=e(7),a=function(d){r.__extends(l,d);function l(v){var p=this,h=f.default(`<div class="w-e-menu" data-title="引用">
                <i class="w-e-icon-quotes-left"></i>
            </div>`);return p=d.call(this,h,v)||this,c.default(v),p}return l.prototype.clickHandler=function(){var v,p,h=this.editor,A=h.selection.isSelectionEmpty(),y=h.selection.getSelectionRangeTopNodes(),x=y[y.length-1],b=this.getTopNodeName();if(b==="BLOCKQUOTE"){var E=f.default(x.childNodes()),C=E.length,_=x;(0,o.default)(E).call(E,function(D){var k=f.default(D);k.insertAfter(_),_=k}),x.remove(),h.selection.moveCursor(E.elems[C-1]),this.tryChangeActive()}else{var w=m.default(y);if(h.$textElem.equal(x)){var S=(v=h.selection.getSelectionContainerElem())===null||v===void 0?void 0:v.elems[0];h.selection.createRangeByElems(S.children[0],S.children[0]),y=h.selection.getSelectionRangeTopNodes(),w=m.default(y),x.append(w)}else w.insertAfter(x);this.delSelectNode(y);var M=(p=w.childNodes())===null||p===void 0?void 0:p.last().getNode();if(M==null)return;M.textContent?h.selection.moveCursor(M):h.selection.moveCursor(M,0),this.tryChangeActive(),f.default(s.EMPTY_P).insertAfter(w);return}A&&(h.selection.collapseRange(),h.selection.restoreSelection())},l.prototype.tryChangeActive=function(){var v,p=this.editor,h=(v=p.selection.getSelectionRangeTopNodes()[0])===null||v===void 0?void 0:v.getNodeName();h==="BLOCKQUOTE"?this.active():this.unActive()},l.prototype.getTopNodeName=function(){var v=this.editor,p=v.selection.getSelectionRangeTopNodes()[0],h=p==null?void 0:p.getNodeName();return h},l.prototype.delSelectNode=function(v){(0,o.default)(v).call(v,function(p){p.remove()})},l}(g.default);n.default=a},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(2),r=e(7),f=o.__importDefault(e(3));function g(c){function m(s){var a,d=c.selection.getSelectionContainerElem(),l=c.selection.getSelectionRangeTopNodes()[0];if((l==null?void 0:l.getNodeName())==="BLOCKQUOTE"){if(d.getNodeName()==="BLOCKQUOTE"){var v=(a=d.childNodes())===null||a===void 0?void 0:a.getNode();c.selection.moveCursor(v)}if(d.text()===""){s.preventDefault(),d.remove();var p=f.default(r.EMPTY_P);p.insertAfter(l),c.selection.moveCursor(p.getNode(),0)}l.text()===""&&l.remove()}}c.txt.eventHooks.enterDownEvents.push(m)}n.default=g},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(4));(0,i.default)(n,"__esModule",{value:!0});var r=e(2),f=r.__importDefault(e(3));function g(c){var m=f.default("<blockquote></blockquote>");return(0,o.default)(c).call(c,function(s){m.append(s.clone(!0))}),m}n.default=g},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(26));(0,i.default)(n,"__esModule",{value:!0});var r=e(2),f=r.__importDefault(e(24)),g=r.__importDefault(e(3)),c=e(6),m=function(s){r.__extends(a,s);function a(d){var l,v=this,p=g.default(`<div class="w-e-menu" data-title="背景色">
                <i class="w-e-icon-paint-brush"></i>
            </div>`),h={width:120,title:"背景颜色",type:"inline-block",list:(0,o.default)(l=d.config.colors).call(l,function(A){return{$elem:g.default('<i style="color:'+A+';" class="w-e-icon-paint-brush"></i>'),value:A}}),clickHandler:function(A){v.command(A)}};return v=s.call(this,p,d,h)||this,v}return a.prototype.command=function(d){var l,v=this.editor,p=v.selection.isSelectionEmpty(),h=(l=v.selection.getSelectionContainerElem())===null||l===void 0?void 0:l.elems[0];if(h!=null){var A=(h==null?void 0:h.nodeName.toLowerCase())!=="p",y=h==null?void 0:h.style.backgroundColor,x=c.hexToRgb(d)===y;if(p){if(A&&!x){var b=v.selection.getSelectionRangeTopNodes();v.selection.createRangeByElem(b[0]),v.selection.moveCursor(b[0].elems[0])}v.selection.createEmptyRange()}v.cmd.do("backColor",d),p&&(v.selection.collapseRange(),v.selection.restoreSelection())}},a.prototype.tryChangeActive=function(){},a}(f.default);n.default=m},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(26));(0,i.default)(n,"__esModule",{value:!0});var r=e(2),f=r.__importDefault(e(24)),g=r.__importDefault(e(3)),c=function(m){r.__extends(s,m);function s(a){var d,l=this,v=g.default(`<div class="w-e-menu" data-title="文字颜色">
                <i class="w-e-icon-pencil2"></i>
            </div>`),p={width:120,title:"文字颜色",type:"inline-block",list:(0,o.default)(d=a.config.colors).call(d,function(h){return{$elem:g.default('<i style="color:'+h+';" class="w-e-icon-pencil2"></i>'),value:h}}),clickHandler:function(h){l.command(h)}};return l=m.call(this,v,a,p)||this,l}return s.prototype.command=function(a){var d,l=this.editor,v=l.selection.isSelectionEmpty(),p=(d=l.selection.getSelectionContainerElem())===null||d===void 0?void 0:d.elems[0];if(p!=null){var h=l.selection.getSelectionText();if(p.nodeName==="A"&&p.textContent===h){var A=g.default("<span>&#8203;</span>").getNode();p.appendChild(A)}l.cmd.do("foreColor",a),v&&(l.selection.collapseRange(),l.selection.restoreSelection())}},s.prototype.tryChangeActive=function(){},s}(f.default);n.default=c},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(2),r=o.__importDefault(e(3)),f=o.__importDefault(e(33)),g=o.__importDefault(e(38)),c=o.__importDefault(e(340)),m=o.__importDefault(e(346)),s=function(a){o.__extends(d,a);function d(l){var v=this,p=r.default(`<div class="w-e-menu" data-title="视频">
                <i class="w-e-icon-play"></i>
            </div>`);return v=a.call(this,p,l)||this,m.default(l),v}return d.prototype.clickHandler=function(){this.createPanel("")},d.prototype.createPanel=function(l){var v=c.default(this.editor,l),p=new f.default(this,v);p.create()},d.prototype.tryChangeActive=function(){},d}(g.default);n.default=s},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(17));(0,i.default)(n,"__esModule",{value:!0});var r=e(2),f=e(6),g=r.__importDefault(e(3)),c=r.__importDefault(e(341)),m=e(7);function s(a,d){var l=a.config,v=new c.default(a),p=f.getRandom("input-iframe"),h=f.getRandom("btn-ok"),A=f.getRandom("input-upload"),y=f.getRandom("btn-local-ok");function x(_){a.cmd.do("insertHTML",_+m.EMPTY_P),a.config.onlineVideoCallback(_)}function b(_){var w=a.config.onlineVideoCheck(_);return w===!0?!0:(typeof w=="string"&&a.config.customAlert(w,"error"),!1)}var E=[{title:a.i18next.t("menus.panelMenus.video.上传视频"),tpl:`<div class="w-e-up-video-container">
                    <div id="`+y+`" class="w-e-up-btn">
                        <i class="w-e-icon-upload2"></i>
                    </div>
                    <div style="display:none;">
                        <input id="`+A+`" type="file" accept="video/*"/>
                    </div>
                 </div>`,events:[{selector:"#"+y,type:"click",fn:function(){var _=g.default("#"+A),w=_.elems[0];if(w)w.click();else return!0}},{selector:"#"+A,type:"change",fn:function(){var _=g.default("#"+A),w=_.elems[0];if(!w)return!0;var S=w.files;return S.length&&v.uploadVideo(S),!0}}]},{title:a.i18next.t("menus.panelMenus.video.插入视频"),tpl:`<div>
                    <input 
                        id="`+p+`" 
                        type="text" 
                        class="block" 
                        placeholder="`+a.i18next.t("如")+`：<iframe src=... ></iframe>"/>
                    </td>
                    <div class="w-e-button-container">
                        <button type="button" id="`+h+`" class="right">
                            `+a.i18next.t("插入")+`
                        </button>
                    </div>
                </div>`,events:[{selector:"#"+h,type:"click",fn:function(){var _,w=g.default("#"+p),S=(0,o.default)(_=w.val()).call(_);if(S&&b(S))return x(S),!0},bindEnter:!0}]}],C={width:300,height:0,tabs:[]};return window.FileReader&&(l.uploadVideoServer||l.customUploadVideo)&&C.tabs.push(E[0]),l.showLinkVideo&&C.tabs.push(E[1]),C}n.default=s},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(133)),r=t(e(57)),f=t(e(4)),g=t(e(27));(0,i.default)(n,"__esModule",{value:!0});var c=e(2),m=e(6),s=c.__importDefault(e(135)),a=c.__importDefault(e(136)),d=e(7),l=e(6),v=function(){function p(h){this.editor=h}return p.prototype.uploadVideo=function(h){var A=this;if(h.length){var y=this.editor,x=y.config,b="validate.",E=function(Y){return y.i18next.t(b+Y)},C=x.uploadVideoServer,_=x.uploadVideoMaxSize,w=_/1024,S=x.uploadVideoName,M=x.uploadVideoParams,D=x.uploadVideoParamsWithUrl,k=x.uploadVideoHeaders,N=x.uploadVideoHooks,T=x.uploadVideoTimeout,B=x.withVideoCredentials,R=x.customUploadVideo,I=x.uploadVideoAccept,P=[],H=[];if(m.arrForEach(h,function(Y){var G=Y.name,q=Y.size/1024/1024;if(!(!G||!q)){if(!(I instanceof Array)){H.push("【"+I+"】"+E("uploadVideoAccept 不是Array"));return}if(!(0,o.default)(I).call(I,function(ee){return ee===G.split(".")[G.split(".").length-1]})){H.push("【"+G+"】"+E("不是视频"));return}if(w<q){H.push("【"+G+"】"+E("大于")+" "+w+"M");return}P.push(Y)}}),H.length){x.customAlert(E("视频验证未通过")+`: 
`+H.join(`
`),"warning");return}if(P.length===0){x.customAlert(E("传入的文件不合法"),"warning");return}if(R&&typeof R=="function"){var z;R(P,(0,r.default)(z=this.insertVideo).call(z,this));return}var O=new FormData;if((0,f.default)(P).call(P,function(Y,G){var q=S||Y.name;P.length>1&&(q=q+(G+1)),O.append(q,Y)}),C){var $=C.split("#");C=$[0];var L=$[1]||"";(0,f.default)(m).call(m,M,function(Y,G){D&&((0,g.default)(C).call(C,"?")>0?C+="&":C+="?",C=C+Y+"="+G),O.append(Y,G)}),L&&(C+="#"+L);var U=s.default(C,{timeout:T,formData:O,headers:k,withCredentials:!!B,beforeSend:function(Y){if(N.before)return N.before(Y,y,P)},onTimeout:function(Y){x.customAlert(E("上传视频超时"),"error"),N.timeout&&N.timeout(Y,y)},onProgress:function(Y,G){var q=new a.default(y);G.lengthComputable&&(Y=G.loaded/G.total,q.show(Y))},onError:function(Y){x.customAlert(E("上传视频错误"),"error",E("上传视频错误")+"，"+E("服务器返回状态")+": "+Y.status),N.error&&N.error(Y,y)},onFail:function(Y,G){x.customAlert(E("上传视频失败"),"error",E("上传视频返回结果错误")+("，"+E("返回结果")+": ")+G),N.fail&&N.fail(Y,y,G)},onSuccess:function(Y,G){if(N.customInsert){var q;N.customInsert((0,r.default)(q=A.insertVideo).call(q,A),G,y);return}if(G.errno!="0"){x.customAlert(E("上传视频失败"),"error",E("上传视频返回结果错误")+"，"+E("返回结果")+" errno="+G.errno),N.fail&&N.fail(Y,y,G);return}var ee=G.data;A.insertVideo(ee.url),N.success&&N.success(Y,y,G)}});typeof U=="string"&&x.customAlert(U,"error")}}},p.prototype.insertVideo=function(h){var A=this.editor,y=A.config,x="validate.",b=function(C,_){return _===void 0&&(_=x),A.i18next.t(_+C)};if(!y.customInsertVideo)l.UA.isFirefox?A.cmd.do("insertHTML",'<p data-we-video-p="true"><video src="'+h+'" controls="controls" style="max-width:100%"></video></p><p>&#8203</p>'):A.cmd.do("insertHTML",'<video src="'+h+'" controls="controls" style="max-width:100%"></video>'+d.EMPTY_P);else{y.customInsertVideo(h);return}var E=document.createElement("video");E.onload=function(){E=null},E.onerror=function(){y.customAlert(b("插入视频错误"),"error","wangEditor: "+b("插入视频错误")+"，"+b("视频链接")+' "'+h+'"，'+b("下载链接失败")),E=null},E.onabort=function(){return E=null},E.src=h},p}();n.default=v},function(u,n,e){u.exports=e(343)},function(u,n,e){var t=e(344);u.exports=t},function(u,n,e){e(345);var t=e(9);u.exports=t.Date.now},function(u,n,e){var t=e(5);t({target:"Date",stat:!0},{now:function(){return new Date().getTime()}})},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(2),r=o.__importDefault(e(347)),f=o.__importDefault(e(349));function g(c){r.default(c),f.default(c)}n.default=g},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0}),n.createShowHideFn=void 0;var o=e(2),r=o.__importDefault(e(3)),f=o.__importDefault(e(39)),g=o.__importDefault(e(348));function c(s){var a,d=function(p,h){return h===void 0&&(h=""),s.i18next.t(h+p)};function l(p){var h=[{$elem:r.default("<span class='w-e-icon-trash-o'></span>"),onClick:function(A,y){return y.remove(),!0}},{$elem:r.default("<span>100%</span>"),onClick:function(A,y){return y.attr("width","100%"),y.removeAttr("height"),!0}},{$elem:r.default("<span>50%</span>"),onClick:function(A,y){return y.attr("width","50%"),y.removeAttr("height"),!0}},{$elem:r.default("<span>30%</span>"),onClick:function(A,y){return y.attr("width","30%"),y.removeAttr("height"),!0}},{$elem:r.default("<span>"+d("重置")+"</span>"),onClick:function(A,y){return y.removeAttr("width"),y.removeAttr("height"),!0}},{$elem:r.default("<span>"+d("menus.justify.靠左")+"</span>"),onClick:function(A,y){return g.default(y,"left"),!0}},{$elem:r.default("<span>"+d("menus.justify.居中")+"</span>"),onClick:function(A,y){return g.default(y,"center"),!0}},{$elem:r.default("<span>"+d("menus.justify.靠右")+"</span>"),onClick:function(A,y){return g.default(y,"right"),!0}}];a=new f.default(s,p,h),a.create()}function v(){a&&(a.remove(),a=null)}return{showVideoTooltip:l,hideVideoTooltip:v}}n.createShowHideFn=c;function m(s){var a=c(s),d=a.showVideoTooltip,l=a.hideVideoTooltip;s.txt.eventHooks.videoClickEvents.push(d),s.txt.eventHooks.clickEvents.push(l),s.txt.eventHooks.keyupEvents.push(l),s.txt.eventHooks.toolbarClickEvents.push(l),s.txt.eventHooks.menuClickEvents.push(l),s.txt.eventHooks.textScrollEvents.push(l),s.txt.eventHooks.changeEvents.push(l)}n.default=m},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(28));(0,i.default)(n,"__esModule",{value:!0});var r=e(2),f=r.__importDefault(e(3));function g(m,s){var a=["P"],d=c(m,a);d&&f.default(d).css("text-align",s)}n.default=g;function c(m,s){for(var a,d=m.elems[0];d!=null;){if((0,o.default)(s).call(s,d==null?void 0:d.nodeName))return d;if(((a=d==null?void 0:d.parentNode)===null||a===void 0?void 0:a.nodeName)==="BODY")return null;d=d.parentNode}return d}},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(6);function r(f){if(o.UA.isFirefox){var g=f.txt,c=f.selection,m=g.eventHooks.keydownEvents;m.push(function(s){var a=c.getSelectionContainerElem();if(a){var d=a.getNodeTop(f),l=d.length&&d.prev().length?d.prev():null;l&&l.attr("data-we-video-p")&&c.getCursorPos()===0&&s.keyCode===8&&l.remove()}})}}n.default=r},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(26));(0,i.default)(n,"__esModule",{value:!0});var r=e(2),f=e(7),g=r.__importDefault(e(3)),c=r.__importDefault(e(33)),m=r.__importDefault(e(38)),s=r.__importDefault(e(351)),a=r.__importDefault(e(364)),d=function(l){r.__extends(v,l);function v(p){var h=this,A=g.default('<div class="w-e-menu" data-title="图片"><i class="w-e-icon-image"></i></div>'),y=a.default(p);if(y.onlyUploadConf){var x;A=y.onlyUploadConf.$elem,(0,o.default)(x=y.onlyUploadConf.events).call(x,function(b){var E=b.type,C=b.fn||f.EMPTY_FN;A.on(E,function(_){_.stopPropagation(),C(_)})})}return h=l.call(this,A,p)||this,h.imgPanelConfig=y,s.default(p),h}return v.prototype.clickHandler=function(){this.imgPanelConfig.onlyUploadConf||this.createPanel()},v.prototype.createPanel=function(){var p=this.imgPanelConfig,h=new c.default(this,p);this.setPanel(h),h.create()},v.prototype.tryChangeActive=function(){},v}(m.default);n.default=d},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(2),r=o.__importDefault(e(352)),f=o.__importDefault(e(353)),g=o.__importDefault(e(354)),c=o.__importDefault(e(362)),m=o.__importDefault(e(363));function s(a){r.default(a),f.default(a),g.default(a),c.default(a),m.default(a)}n.default=s},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(2),r=e(131),f=o.__importDefault(e(97));function g(a,d){var l=a.config,v=l.pasteFilterStyle,p=l.pasteIgnoreImg,h=r.getPasteHtml(d,v,p);if(h)return!0;var A=r.getPasteText(d);return!!A}function c(a,d){for(var l,v=((l=d.clipboardData)===null||l===void 0?void 0:l.types)||[],p=0;p<v.length;p++){var h=v[p];if(h==="Files")return!0}return!1}function m(a,d){if(!(!c(d,a)&&g(d,a))){var l=r.getPasteImgs(a);if(l.length){var v=new f.default(d);v.uploadImg(l)}}}function s(a){a.txt.eventHooks.pasteEvents.unshift(function(d){m(d,a)})}n.default=s},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(2),r=o.__importDefault(e(97));function f(g){function c(m){var s=m.dataTransfer&&m.dataTransfer.files;if(!(!s||!s.length)){var a=new r.default(g);a.uploadImg(s)}}g.txt.eventHooks.dropEvents.push(c)}n.default=f},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(29)),r=t(e(355));(0,i.default)(n,"__esModule",{value:!0}),n.createShowHideFn=void 0;var f=e(2),g=f.__importDefault(e(3));e(360);var c=e(6);function m(v,p,h,A,y){v.attr("style","width:"+p+"px; height:"+h+"px; left:"+A+"px; top:"+y+"px;")}function s(v,p){var h=g.default(`<div class="w-e-img-drag-mask">
            <div class="w-e-img-drag-show-size"></div>
            <div class="w-e-img-drag-rb"></div>
         </div>`);return h.hide(),p.append(h),h}function a(v,p,h){var A=v.getBoundingClientRect(),y=h.getBoundingClientRect(),x=y.width.toFixed(2),b=y.height.toFixed(2);(0,o.default)(p).call(p,".w-e-img-drag-show-size").text(x+"px * "+b+"px"),m(p,(0,r.default)(x),(0,r.default)(b),y.left-A.left,y.top-A.top),p.show()}function d(v){var p=v.$textContainerElem,h,A=s(v,p);function y(E,C){E.on("click",function(_){_.stopPropagation()}),E.on("mousedown",".w-e-img-drag-rb",function(_){if(_.preventDefault(),!h)return;var w=_.clientX,S=_.clientY,M=C.getBoundingClientRect(),D=h.getBoundingClientRect(),k=D.width,N=D.height,T=D.left-M.left,B=D.top-M.top,R=k/N,I=k,P=N,H=g.default(document);function z(){H.off("mousemove",O),H.off("mouseup",$)}function O(L){L.stopPropagation(),L.preventDefault(),I=k+(L.clientX-w),P=N+(L.clientY-S),I/P!=R&&(P=I/R),I=(0,r.default)(I.toFixed(2)),P=(0,r.default)(P.toFixed(2)),(0,o.default)(E).call(E,".w-e-img-drag-show-size").text(I.toFixed(2).replace(".00","")+"px * "+P.toFixed(2).replace(".00","")+"px"),m(E,I,P,T,B)}H.on("mousemove",O);function $(){h.attr("width",I+""),h.attr("height",P+"");var L=h.getBoundingClientRect();m(E,I,P,L.left-M.left,L.top-M.top),z()}H.on("mouseup",$),H.on("mouseleave",z)})}function x(E){if(c.UA.isIE())return!1;E&&(h=E,a(p,A,h))}function b(){(0,o.default)(p).call(p,".w-e-img-drag-mask").hide()}return y(A,p),g.default(document).on("click",b),v.beforeDestroy(function(){g.default(document).off("click",b)}),{showDrag:x,hideDrag:b}}n.createShowHideFn=d;function l(v){var p=d(v),h=p.showDrag,A=p.hideDrag;v.txt.eventHooks.imgClickEvents.push(h),v.txt.eventHooks.textScrollEvents.push(A),v.txt.eventHooks.keyupEvents.push(A),v.txt.eventHooks.toolbarClickEvents.push(A),v.txt.eventHooks.menuClickEvents.push(A),v.txt.eventHooks.changeEvents.push(A)}n.default=l},function(u,n,e){u.exports=e(356)},function(u,n,e){var t=e(357);u.exports=t},function(u,n,e){e(358);var t=e(9);u.exports=t.parseFloat},function(u,n,e){var t=e(5),i=e(359);t({global:!0,forced:parseFloat!=i},{parseFloat:i})},function(u,n,e){var t=e(8),i=e(90).trim,o=e(68),r=t.parseFloat,f=1/r(o+"-0")!==-1/0;u.exports=f?function(g){var c=i(String(g)),m=r(c);return m===0&&c.charAt(0)=="-"?-0:m}:r},function(u,n,e){var t=e(20),i=e(361);i=i.__esModule?i.default:i,typeof i=="string"&&(i=[[u.i,i,""]]);var o={};o.insert="head",o.singleton=!1,t(i,o),u.exports=i.locals||{}},function(u,n,e){var t=e(21);n=t(!1),n.push([u.i,`.w-e-text-container {
  overflow: hidden;
}
.w-e-img-drag-mask {
  position: absolute;
  z-index: 1;
  border: 1px dashed #ccc;
  box-sizing: border-box;
}
.w-e-img-drag-mask .w-e-img-drag-rb {
  position: absolute;
  right: -5px;
  bottom: -5px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #ccc;
  cursor: se-resize;
}
.w-e-img-drag-mask .w-e-img-drag-show-size {
  min-width: 110px;
  height: 22px;
  line-height: 22px;
  font-size: 14px;
  color: #999;
  position: absolute;
  left: 0;
  top: 0;
  background-color: #999;
  color: #fff;
  border-radius: 2px;
  padding: 0 5px;
}
`,""]),u.exports=n},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0}),n.createShowHideFn=void 0;var o=e(2),r=o.__importDefault(e(3)),f=o.__importDefault(e(39));function g(m){var s,a=function(v,p){return p===void 0&&(p=""),m.i18next.t(p+v)};function d(v){var p=[{$elem:r.default("<span class='w-e-icon-trash-o'></span>"),onClick:function(h,A){return h.selection.createRangeByElem(A),h.selection.restoreSelection(),h.cmd.do("delete"),!0}},{$elem:r.default("<span>30%</span>"),onClick:function(h,A){return A.attr("width","30%"),A.removeAttr("height"),!0}},{$elem:r.default("<span>50%</span>"),onClick:function(h,A){return A.attr("width","50%"),A.removeAttr("height"),!0}},{$elem:r.default("<span>100%</span>"),onClick:function(h,A){return A.attr("width","100%"),A.removeAttr("height"),!0}}];p.push({$elem:r.default("<span>"+a("重置")+"</span>"),onClick:function(h,A){return A.removeAttr("width"),A.removeAttr("height"),!0}}),v.attr("data-href")&&p.push({$elem:r.default("<span>"+a("查看链接")+"</span>"),onClick:function(h,A){var y=A.attr("data-href");return y&&(y=decodeURIComponent(y),window.open(y,"_target")),!0}}),s=new f.default(m,v,p),s.create()}function l(){s&&(s.remove(),s=null)}return{showImgTooltip:d,hideImgTooltip:l}}n.createShowHideFn=g;function c(m){var s=g(m),a=s.showImgTooltip,d=s.hideImgTooltip;m.txt.eventHooks.imgClickEvents.push(a),m.txt.eventHooks.clickEvents.push(d),m.txt.eventHooks.keyupEvents.push(d),m.txt.eventHooks.toolbarClickEvents.push(d),m.txt.eventHooks.menuClickEvents.push(d),m.txt.eventHooks.textScrollEvents.push(d),m.txt.eventHooks.imgDragBarMouseDownEvents.push(d),m.txt.eventHooks.changeEvents.push(d)}n.default=c},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});function o(r){var f=r.txt,g=r.selection,c=f.eventHooks.keydownEvents;c.push(function(m){var s=g.getSelectionContainerElem(),a=g.getRange();if(!(!a||!s||m.keyCode!==8||!g.isSelectionEmpty())){var d=a.startContainer,l=a.startOffset,v=null;if(l===0)for(;d!==s.elems[0]&&s.elems[0].contains(d)&&d.parentNode&&!v;){if(d.previousSibling){v=d.previousSibling;break}d=d.parentNode}else d.nodeType!==3&&(v=d.childNodes[l-1]);if(v){for(var p=v;p.childNodes.length;)p=p.childNodes[p.childNodes.length-1];p instanceof HTMLElement&&p.tagName==="IMG"&&(p.remove(),m.preventDefault())}}})}n.default=o},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(26)),r=t(e(17));(0,i.default)(n,"__esModule",{value:!0});var f=e(2),g=f.__importDefault(e(3)),c=e(6),m=f.__importDefault(e(97));function s(a){var d,l=a.config,v=new m.default(a),p=c.getRandom("up-trigger-id"),h=c.getRandom("up-file-id"),A=c.getRandom("input-link-url"),y=c.getRandom("input-link-url-alt"),x=c.getRandom("input-link-url-href"),b=c.getRandom("btn-link"),E="menus.panelMenus.image.",C=function(B,R){return R===void 0&&(R=E),a.i18next.t(R+B)};function _(B,R,I){var P=l.linkImgCheck(B);return P===!0?!0:(typeof P=="string"&&l.customAlert(P,"error"),!1)}var w=l.uploadImgMaxLength===1?"":'multiple="multiple"',S=(0,o.default)(d=l.uploadImgAccept).call(d,function(B){return"image/"+B}).join(","),M=function(B,R,I){return'<div class="'+B+'" data-title="'+I+`">
            <div id="`+p+`" class="w-e-up-btn">
                <i class="`+R+`"></i>
            </div>
            <div style="display:none;">
                <input id="`+h+'" type="file" '+w+' accept="'+S+`"/>
            </div>
        </div>`},D=[{selector:"#"+p,type:"click",fn:function(){var B=l.uploadImgFromMedia;if(B&&typeof B=="function")return B(),!0;var R=g.default("#"+h),I=R.elems[0];if(I)I.click();else return!0}},{selector:"#"+h,type:"change",fn:function(){var B=g.default("#"+h),R=B.elems[0];if(!R)return!0;var I=R.files;return I!=null&&I.length&&v.uploadImg(I),R&&(R.value=""),!0}}],k=[`<input
            id="`+A+`"
            type="text"
            class="block"
            placeholder="`+C("图片地址")+'"/>'];l.showLinkImgAlt&&k.push(`
        <input
            id="`+y+`"
            type="text"
            class="block"
            placeholder="`+C("图片文字说明")+'"/>'),l.showLinkImgHref&&k.push(`
        <input
            id="`+x+`"
            type="text"
            class="block"
            placeholder="`+C("跳转链接")+'"/>');var N=[{title:C("上传图片"),tpl:M("w-e-up-img-container","w-e-icon-upload2",""),events:D},{title:C("网络图片"),tpl:`<div>
                    `+k.join("")+`
                    <div class="w-e-button-container">
                        <button type="button" id="`+b+'" class="right">'+C("插入","")+`</button>
                    </div>
                </div>`,events:[{selector:"#"+b,type:"click",fn:function(){var B,R=g.default("#"+A),I=(0,r.default)(B=R.val()).call(B);if(I){var P;if(l.showLinkImgAlt){var H;P=(0,r.default)(H=g.default("#"+y).val()).call(H)}var z;if(l.showLinkImgHref){var O;z=(0,r.default)(O=g.default("#"+x).val()).call(O)}if(_(I))return v.insertImg(I,P,z),!0}},bindEnter:!0}]}],T={width:300,height:0,tabs:[],onlyUploadConf:{$elem:g.default(M("w-e-menu","w-e-icon-image","图片")),events:D}};return window.FileReader&&(l.uploadImgShowBase64||l.uploadImgServer||l.customUploadImg||l.uploadImgFromMedia)&&T.tabs.push(N[0]),l.showLinkImg&&(T.tabs.push(N[1]),T.onlyUploadConf=void 0),T}n.default=s},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(4));(0,i.default)(n,"__esModule",{value:!0});var r=e(2),f=r.__importDefault(e(3)),g=r.__importDefault(e(24)),c=r.__importDefault(e(366)),m=function(s){r.__extends(a,s);function a(d){var l=this,v=f.default(`<div class="w-e-menu" data-title="缩进">
                <i class="w-e-icon-indent-increase"></i>
            </div>`),p={width:130,title:"设置缩进",type:"list",list:[{$elem:f.default(`<p>
                            <i class="w-e-icon-indent-increase w-e-drop-list-item"></i>
                            `+d.i18next.t("menus.dropListMenu.indent.增加缩进")+`
                        <p>`),value:"increase"},{$elem:f.default(`<p>
                            <i class="w-e-icon-indent-decrease w-e-drop-list-item"></i>
                            `+d.i18next.t("menus.dropListMenu.indent.减少缩进")+`
                        <p>`),value:"decrease"}],clickHandler:function(h){l.command(h)}};return l=s.call(this,v,d,p)||this,l}return a.prototype.command=function(d){var l=this.editor,v=l.selection.getSelectionContainerElem();if(v&&l.$textElem.equal(v)){var p=l.selection.getSelectionRangeTopNodes();p.length>0&&(0,o.default)(p).call(p,function(h){c.default(f.default(h),d,l)})}else v&&v.length>0&&(0,o.default)(v).call(v,function(h){c.default(f.default(h),d,l)});l.selection.restoreSelection(),this.tryChangeActive()},a.prototype.tryChangeActive=function(){var d=this.editor,l=d.selection.getSelectionStartElem(),v=f.default(l).getNodeTop(d);v.length<=0||(v.elems[0].style.paddingLeft!=""?this.active():this.unActive())},a}(g.default);n.default=m},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(45)),r=t(e(17));(0,i.default)(n,"__esModule",{value:!0});var f=e(2),g=f.__importDefault(e(367)),c=f.__importDefault(e(368)),m=/^(\d+)(\w+)$/,s=/^(\d+)%$/;function a(l){var v=l.config.indentation;if(typeof v=="string"){if(m.test(v)){var p,h=(0,o.default)(p=(0,r.default)(v).call(v).match(m)).call(p,1,3),A=h[0],y=h[1];return{value:Number(A),unit:y}}else if(s.test(v))return{value:Number((0,r.default)(v).call(v).match(s)[1]),unit:"%"}}else if(v.value!==void 0&&v.unit)return v;return{value:2,unit:"em"}}function d(l,v,p){var h=l.getNodeTop(p),A=/^(P|H[0-9]*)$/;A.test(h.getNodeName())&&(v==="increase"?g.default(h,a(p)):v==="decrease"&&c.default(h,a(p)))}n.default=d},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(45));(0,i.default)(n,"__esModule",{value:!0});function r(f,g){var c=f.elems[0];if(c.style.paddingLeft==="")f.css("padding-left",g.value+g.unit);else{var m=c.style.paddingLeft,s=(0,o.default)(m).call(m,0,m.length-g.unit.length),a=Number(s)+g.value;f.css("padding-left",""+a+g.unit)}}n.default=r},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(45));(0,i.default)(n,"__esModule",{value:!0});function r(f,g){var c=f.elems[0];if(c.style.paddingLeft!==""){var m=c.style.paddingLeft,s=(0,o.default)(m).call(m,0,m.length-g.unit.length),a=Number(s)-g.value;a>0?f.css("padding-left",""+a+g.unit):f.css("padding-left","")}}n.default=r},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(2),r=o.__importDefault(e(3)),f=o.__importDefault(e(38)),g=o.__importDefault(e(33)),c=o.__importDefault(e(370)),m=function(s){o.__extends(a,s);function a(d){var l=this,v=r.default(`<div class="w-e-menu" data-title="表情">
                <i class="w-e-icon-happy"></i>
            </div>`);return l=s.call(this,v,d)||this,l}return a.prototype.createPanel=function(){var d=c.default(this.editor),l=new g.default(this,d);l.create()},a.prototype.clickHandler=function(){this.createPanel()},a.prototype.tryChangeActive=function(){},a}(f.default);n.default=m},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(26)),r=t(e(70)),f=t(e(17));(0,i.default)(n,"__esModule",{value:!0});var g=e(2),c=g.__importDefault(e(3));function m(s){var a=s.config.emotions;function d(p){var h=[];if(p.type=="image"){var A;h=(0,o.default)(A=p.content).call(A,function(x){return typeof x=="string"?"":'<span  title="'+x.alt+`">
                    <img class="eleImg" data-emoji="`+x.alt+'" style src="'+x.src+'" alt="['+x.alt+`]">
                </span>`}),h=(0,r.default)(h).call(h,function(x){return x!==""})}else{var y;h=(0,o.default)(y=p.content).call(y,function(x){return'<span class="eleImg" title="'+x+'">'+x+"</span>"})}return h.join("").replace(/&nbsp;/g,"")}var l=(0,o.default)(a).call(a,function(p){return{title:s.i18next.t("menus.panelMenus.emoticon."+p.title),tpl:"<div>"+d(p)+"</div>",events:[{selector:".eleImg",type:"click",fn:function(h){var A=c.default(h.target),y=A.getNodeName(),x;if(y==="IMG"){var b;x=(0,f.default)(b=A.parent().html()).call(b)}else x="<span>"+A.html()+"</span>";return s.cmd.do("insertHTML",x),!0}}]}}),v={width:300,height:230,tabs:l};return v}n.default=m},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0}),n.createListHandle=n.ClassType=void 0;var o=e(2),r=o.__importDefault(e(3)),f=o.__importDefault(e(372)),g=o.__importDefault(e(374)),c=o.__importDefault(e(375)),m=o.__importDefault(e(376)),s=o.__importDefault(e(377)),a;(function(p){p.Wrap="WrapListHandle",p.Join="JoinListHandle",p.StartJoin="StartJoinListHandle",p.EndJoin="EndJoinListHandle",p.Other="OtherListHandle"})(a=n.ClassType||(n.ClassType={}));var d={WrapListHandle:f.default,JoinListHandle:g.default,StartJoinListHandle:c.default,EndJoinListHandle:m.default,OtherListHandle:s.default};function l(p,h,A){if(p===a.Other&&A===void 0)throw new Error("other 类需要传入 range");return p!==a.Other?new d[p](h):new d[p](h,A)}n.createListHandle=l;var v=function(){function p(h){this.handle=h,this.handle.exec()}return p.prototype.getSelectionRangeElem=function(){return r.default(this.handle.selectionRangeElem.get())},p}();n.default=v},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(4));(0,i.default)(n,"__esModule",{value:!0});var r=e(2),f=r.__importDefault(e(3)),g=e(58),c=e(47),m=function(s){r.__extends(a,s);function a(d){return s.call(this,d)||this}return a.prototype.exec=function(){var d=this.options,l=d.listType,v=d.listTarget,p=d.$selectionElem,h=d.$startElem,A=d.$endElem,y,x=[],b=p==null?void 0:p.getNodeName(),E=h.prior,C=A.prior;if(!h.prior&&!A.prior||!(E!=null&&E.prev().length)&&!(C!=null&&C.next().length)){var _;(0,o.default)(_=p==null?void 0:p.children()).call(_,function(T){x.push(f.default(T))}),b===l?y=c.createElementFragment(x,c.createDocumentFragment(),"p"):(y=c.createElement(v),(0,o.default)(x).call(x,function(T){y.appendChild(T.elems[0])})),this.selectionRangeElem.set(y),c.insertBefore(p,y,p.elems[0]),p.remove()}else{for(var w=E;w.length;)x.push(w),C!=null&&C.equal(w)?w=f.default(void 0):w=w.next();var S=E.prev(),M=C.next();if(b===l?y=c.createElementFragment(x,c.createDocumentFragment(),"p"):(y=c.createElement(v),(0,o.default)(x).call(x,function(T){y.append(T.elems[0])})),S.length&&M.length){for(var D=[];M.length;)D.push(M),M=M.next();var k=c.createElement(b);(0,o.default)(D).call(D,function(T){k.append(T.elems[0])}),f.default(k).insertAfter(p),this.selectionRangeElem.set(y);var N=p.next();N.length?c.insertBefore(p,y,N.elems[0]):p.parent().elems[0].append(y)}else if(!S.length)this.selectionRangeElem.set(y),c.insertBefore(p,y,p.elems[0]);else{this.selectionRangeElem.set(y);var N=p.next();N.length?c.insertBefore(p,y,N.elems[0]):p.parent().elems[0].append(y)}}},a}(g.ListHandle);n.default=m},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(4));(0,i.default)(n,"__esModule",{value:!0});var r=function(){function f(){this._element=null}return f.prototype.set=function(g){if(g instanceof DocumentFragment){var c,m=[];(0,o.default)(c=g.childNodes).call(c,function(s){m.push(s)}),g=m}this._element=g},f.prototype.get=function(){return this._element},f.prototype.clear=function(){this._element=null},f}();n.default=r},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(4));(0,i.default)(n,"__esModule",{value:!0});var r=e(2),f=r.__importDefault(e(3)),g=e(58),c=e(47),m=function(s){r.__extends(a,s);function a(d){return s.call(this,d)||this}return a.prototype.exec=function(){var d,l,v,p,h,A,y,x=this.options,b=x.editor,E=x.listType,C=x.listTarget,_=x.$startElem,w=x.$endElem,S,M=b.selection.getSelectionRangeTopNodes(),D=_==null?void 0:_.getNodeName(),k=w==null?void 0:w.getNodeName();if(D===k)if(M.length>2)if(M.shift(),M.pop(),S=c.createElementFragment(c.filterSelectionNodes(M),c.createDocumentFragment()),D===E)(d=w.children())===null||d===void 0||(0,o.default)(d).call(d,function(L){S.append(L)}),w.remove(),this.selectionRangeElem.set(S),_.elems[0].append(S);else{for(var N=document.createDocumentFragment(),T=document.createDocumentFragment(),B=c.getStartPoint(_);B.length;){var R=B.elems[0];B=B.next(),N.append(R)}for(var I=c.getEndPoint(w),P=[];I.length;)P.unshift(I.elems[0]),I=I.prev();(0,o.default)(P).call(P,function(L){T.append(L)});var H=c.createElement(C);H.append(N),H.append(S),H.append(T),S=H,this.selectionRangeElem.set(S),f.default(H).insertAfter(_),!(!((l=_.children())===null||l===void 0)&&l.length)&&_.remove(),!(!((v=w.children())===null||v===void 0)&&v.length)&&w.remove()}else{M.length=0;for(var B=c.getStartPoint(_);B.length;)M.push(B),B=B.next();for(var I=c.getEndPoint(w),P=[];I.length;)P.unshift(I),I=I.prev();M.push.apply(M,P),D===E?(S=c.createElementFragment(M,c.createDocumentFragment(),"p"),this.selectionRangeElem.set(S),c.insertBefore(_,S,w.elems[0])):(S=c.createElement(C),(0,o.default)(M).call(M,function(L){S.append(L.elems[0])}),this.selectionRangeElem.set(S),f.default(S).insertAfter(_)),!(!((p=_.children())===null||p===void 0)&&p.length)&&w.remove(),!(!((h=w.children())===null||h===void 0)&&h.length)&&w.remove()}else{for(var z=[],I=c.getEndPoint(w);I.length;)z.unshift(I),I=I.prev();for(var O=[],B=c.getStartPoint(_);B.length;)O.push(B),B=B.next();if(S=c.createDocumentFragment(),M.shift(),M.pop(),(0,o.default)(O).call(O,function(L){return S.append(L.elems[0])}),S=c.createElementFragment(c.filterSelectionNodes(M),S),(0,o.default)(z).call(z,function(L){return S.append(L.elems[0])}),this.selectionRangeElem.set(S),D===E)_.elems[0].append(S),!(!((A=w.children())===null||A===void 0)&&A.length)&&w.remove();else if(!((y=w.children())===null||y===void 0)&&y.length){var $=w.children();c.insertBefore($,S,$.elems[0])}else w.elems[0].append(S)}},a}(g.ListHandle);n.default=m},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(4));(0,i.default)(n,"__esModule",{value:!0});var r=e(2),f=r.__importDefault(e(3)),g=e(58),c=e(47),m=function(s){r.__extends(a,s);function a(d){return s.call(this,d)||this}return a.prototype.exec=function(){var d,l=this.options,v=l.editor,p=l.listType,h=l.listTarget,A=l.$startElem,y,x=v.selection.getSelectionRangeTopNodes(),b=A==null?void 0:A.getNodeName();x.shift();for(var E=[],C=c.getStartPoint(A);C.length;)E.push(C),C=C.next();b===p?(y=c.createDocumentFragment(),(0,o.default)(E).call(E,function(_){return y.append(_.elems[0])}),y=c.createElementFragment(c.filterSelectionNodes(x),y),this.selectionRangeElem.set(y),A.elems[0].append(y)):(y=c.createElement(h),(0,o.default)(E).call(E,function(_){return y.append(_.elems[0])}),y=c.createElementFragment(c.filterSelectionNodes(x),y),this.selectionRangeElem.set(y),f.default(y).insertAfter(A),!(!((d=A.children())===null||d===void 0)&&d.length)&&A.remove())},a}(g.ListHandle);n.default=m},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(4));(0,i.default)(n,"__esModule",{value:!0});var r=e(2),f=r.__importDefault(e(3)),g=e(58),c=e(47),m=function(s){r.__extends(a,s);function a(d){return s.call(this,d)||this}return a.prototype.exec=function(){var d,l,v=this.options,p=v.editor,h=v.listType,A=v.listTarget,y=v.$endElem,x,b=p.selection.getSelectionRangeTopNodes(),E=y==null?void 0:y.getNodeName();b.pop();for(var C=[],_=c.getEndPoint(y);_.length;)C.unshift(_),_=_.prev();if(E===h)if(x=c.createElementFragment(c.filterSelectionNodes(b),c.createDocumentFragment()),(0,o.default)(C).call(C,function(M){return x.append(M.elems[0])}),this.selectionRangeElem.set(x),!((d=y.children())===null||d===void 0)&&d.length){var w=y.children();c.insertBefore(w,x,w.elems[0])}else y.elems[0].append(x);else{var S=c.filterSelectionNodes(b);S.push.apply(S,C),x=c.createElementFragment(S,c.createElement(A)),this.selectionRangeElem.set(x),f.default(x).insertBefore(y),!(!((l=y.children())===null||l===void 0)&&l.length)&&y.remove()}},a}(g.ListHandle);n.default=m},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(2),r=e(58),f=e(47),g=function(c){o.__extends(m,c);function m(s,a){var d=c.call(this,s)||this;return d.range=a,d}return m.prototype.exec=function(){var s=this.options,a=s.editor,d=s.listTarget,l=a.selection.getSelectionRangeTopNodes(),v=f.createElementFragment(f.filterSelectionNodes(l),f.createElement(d));this.selectionRangeElem.set(v),this.range.insertNode(v)},m}(r.ListHandle);n.default=g},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(4)),r=t(e(27));(0,i.default)(n,"__esModule",{value:!0});var f=e(2),g=f.__importDefault(e(24)),c=f.__importDefault(e(3)),m=f.__importDefault(e(379)),s=function(a){f.__extends(d,a);function d(l){var v=this,p=c.default(`<div class="w-e-menu" data-title="行高">
                    <i class="w-e-icon-row-height"></i>
                </div>`),h=new m.default(l,l.config.lineHeights),A={width:100,title:"设置行高",type:"list",list:h.getItemList(),clickHandler:function(y){l.selection.saveRange(),v.command(y)}};return v=a.call(this,p,l,A)||this,v}return d.prototype.command=function(l){var v=this.editor;v.selection.restoreSelection();var p=c.default(v.selection.getSelectionContainerElem());if(p.elems.length){if(p&&v.$textElem.equal(p)){for(var h=!1,A=c.default(v.selection.getSelectionStartElem()).elems[0],y=c.default(v.selection.getSelectionEndElem()).elems[0],x=this.getDom(A),b=this.getDom(y),E=p.elems[0].children,C=0;C<E.length;C++){var _=E[C];if(c.default(_).getNodeName()==="P"&&(_===x&&(h=!0),h&&(c.default(_).css("line-height",l),_===b))){h=!1;return}}v.selection.createRangeByElems(A,y);return}var w=p.elems[0],S=this.getDom(w);c.default(S).getNodeName()==="P"&&(c.default(S).css("line-height",l),v.selection.createRangeByElems(S,S))}},d.prototype.getDom=function(l){var v=c.default(l).elems[0];if(!v.parentNode)return v;function p(h,A){var y=c.default(h.parentNode);return A.$textElem.equal(y)?h:p(y.elems[0],A)}return v=p(v,this.editor),v},d.prototype.styleProcessing=function(l){var v="";return(0,o.default)(l).call(l,function(p){p!==""&&(0,r.default)(p).call(p,"line-height")===-1&&(v=v+p+";")}),v},d.prototype.setRange=function(l,v){var p=this.editor,h=window.getSelection?window.getSelection():document.getSelection();h==null||h.removeAllRanges();var A=document.createRange(),y=l,x=v;A.setStart(y,0),A.setEnd(x,1),h==null||h.addRange(A),p.selection.saveRange(),h==null||h.removeAllRanges(),p.selection.restoreSelection()},d.prototype.tryChangeActive=function(){var l=this.editor,v=l.selection.getSelectionContainerElem();if(!(v&&l.$textElem.equal(v))){var p=c.default(l.selection.getSelectionStartElem());if(p.length!==0){p=this.getDom(p.elems[0]);var h=p.getAttribute("style")?p.getAttribute("style"):"";h&&(0,r.default)(h).call(h,"line-height")!==-1?this.active():this.unActive()}}},d}(g.default);n.default=s},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(4));(0,i.default)(n,"__esModule",{value:!0});var r=e(2),f=r.__importDefault(e(3)),g=function(){function c(m,s){var a=this;this.itemList=[{$elem:f.default("<span>"+m.i18next.t("默认")+"</span>"),value:""}],(0,o.default)(s).call(s,function(d){a.itemList.push({$elem:f.default("<span>"+d+"</span>"),value:d})})}return c.prototype.getItemList=function(){return this.itemList},c}();n.default=g},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(2),r=o.__importDefault(e(3)),f=o.__importDefault(e(23)),g=function(c){o.__extends(m,c);function m(s){var a=this,d=r.default(`<div class="w-e-menu" data-title="撤销">
                <i class="w-e-icon-undo"></i>
            </div>`);return a=c.call(this,d,s)||this,a}return m.prototype.clickHandler=function(){var s=this.editor;s.history.revoke();var a=s.$textElem.children();if(a!=null&&a.length){var d=a.last();s.selection.createRangeByElem(d,!1,!0),s.selection.restoreSelection()}},m.prototype.tryChangeActive=function(){this.editor.isCompatibleMode||(this.editor.history.size[0]?this.active():this.unActive())},m}(f.default);n.default=g},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(2),r=o.__importDefault(e(3)),f=o.__importDefault(e(23)),g=function(c){o.__extends(m,c);function m(s){var a=this,d=r.default(`<div class="w-e-menu" data-title="恢复">
                <i class="w-e-icon-redo"></i>
            </div>`);return a=c.call(this,d,s)||this,a}return m.prototype.clickHandler=function(){var s=this.editor;s.history.restore();var a=s.$textElem.children();if(a!=null&&a.length){var d=a.last();s.selection.createRangeByElem(d,!1,!0),s.selection.restoreSelection()}},m.prototype.tryChangeActive=function(){this.editor.isCompatibleMode||(this.editor.history.size[1]?this.active():this.unActive())},m}(f.default);n.default=g},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(2),r=o.__importDefault(e(38)),f=o.__importDefault(e(3)),g=o.__importDefault(e(383)),c=o.__importDefault(e(33)),m=o.__importDefault(e(392)),s=function(a){o.__extends(d,a);function d(l){var v=this,p=f.default('<div class="w-e-menu" data-title="表格"><i class="w-e-icon-table2"></i></div>');return v=a.call(this,p,l)||this,m.default(l),v}return d.prototype.clickHandler=function(){this.createPanel()},d.prototype.createPanel=function(){var l=g.default(this.editor),v=new c.default(this,l);v.create()},d.prototype.tryChangeActive=function(){},d}(r.default);n.default=s},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(384));(0,i.default)(n,"__esModule",{value:!0});var r=e(2),f=e(6),g=r.__importDefault(e(3));e(389);var c=r.__importDefault(e(391));function m(a){return a>0&&(0,o.default)(a)}function s(a){var d=new c.default(a),l=f.getRandom("w-col-id"),v=f.getRandom("w-row-id"),p=f.getRandom("btn-link"),h="menus.panelMenus.table.",A=function(b){return a.i18next.t(b)},y=[{title:A(h+"插入表格"),tpl:`<div>
                    <div class="w-e-table">
                        <span>`+A("创建")+`</span>
                        <input id="`+v+`"  type="text" class="w-e-table-input" value="5"/></td>
                        <span>`+A(h+"行")+`</span>
                        <input id="`+l+`" type="text" class="w-e-table-input" value="5"/></td>
                        <span>`+(A(h+"列")+A(h+"的")+A(h+"表格"))+`</span>
                    </div>
                    <div class="w-e-button-container">
                        <button type="button" id="`+p+'" class="right">'+A("插入")+`</button>
                    </div>
                </div>`,events:[{selector:"#"+p,type:"click",fn:function(){var b=Number(g.default("#"+l).val()),E=Number(g.default("#"+v).val());return m(E)&&m(b)?(d.createAction(E,b),!0):(a.config.customAlert("表格行列请输入正整数","warning"),!1)},bindEnter:!0}]}],x={width:330,height:0,tabs:[]};return x.tabs.push(y[0]),x}n.default=s},function(u,n,e){u.exports=e(385)},function(u,n,e){var t=e(386);u.exports=t},function(u,n,e){e(387);var t=e(9);u.exports=t.Number.isInteger},function(u,n,e){var t=e(5),i=e(388);t({target:"Number",stat:!0},{isInteger:i})},function(u,n,e){var t=e(13),i=Math.floor;u.exports=function(o){return!t(o)&&isFinite(o)&&i(o)===o}},function(u,n,e){var t=e(20),i=e(390);i=i.__esModule?i.default:i,typeof i=="string"&&(i=[[u.i,i,""]]);var o={};o.insert="head",o.singleton=!1,t(i,o),u.exports=i.locals||{}},function(u,n,e){var t=e(21);n=t(!1),n.push([u.i,`.w-e-table {
  display: flex;
}
.w-e-table .w-e-table-input {
  width: 40px;
  text-align: center!important;
  margin: 0 5px;
}
`,""]),u.exports=n},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(2),r=e(7),f=o.__importDefault(e(3)),g=function(){function c(m){this.editor=m}return c.prototype.createAction=function(m,s){var a=this.editor,d=f.default(a.selection.getSelectionContainerElem()),l=f.default(d.elems[0]).parentUntilEditor("UL",a),v=f.default(d.elems[0]).parentUntilEditor("OL",a);if(!(l||v)){var p=this.createTableHtml(m,s);a.cmd.do("insertHTML",p)}},c.prototype.createTableHtml=function(m,s){for(var a="",d="",l=0;l<m;l++){d="";for(var v=0;v<s;v++)l===0?d=d+"<th></th>":d=d+"<td></td>";a=a+"<tr>"+d+"</tr>"}var p='<table border="0" width="100%" cellpadding="0" cellspacing="0"><tbody>'+a+("</tbody></table>"+r.EMPTY_P);return p},c}();n.default=g},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(2),r=o.__importDefault(e(393)),f=e(400);function g(c){r.default(c),f.bindEventKeyboardEvent(c),f.bindClickEvent(c)}n.default=g},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(2),r=o.__importDefault(e(3)),f=o.__importDefault(e(39)),g=o.__importDefault(e(394)),c=o.__importDefault(e(399)),m=e(7);function s(v){var p;function h(y){var x=new c.default(v),b="menus.panelMenus.table.",E=function(_,w){return w===void 0&&(w=b),v.i18next.t(w+_)},C=[{$elem:r.default("<span>"+E("删除表格")+"</span>"),onClick:function(_,w){return _.selection.createRangeByElem(w),_.selection.restoreSelection(),_.cmd.do("insertHTML",m.EMPTY_P),!0}},{$elem:r.default("<span>"+E("添加行")+"</span>"),onClick:function(_,w){var S=a(_);if(S)return!0;var M=r.default(_.selection.getSelectionStartElem()),D=x.getRowNode(M.elems[0]);if(!D)return!0;var k=Number(x.getCurrentRowIndex(w.elems[0],D)),N=x.getTableHtml(w.elems[0]),T=x.getTableHtml(g.default.ProcessingRow(r.default(N),k).elems[0]);return T=l(w,T),_.selection.createRangeByElem(w),_.selection.restoreSelection(),_.cmd.do("insertHTML",T),!0}},{$elem:r.default("<span>"+E("删除行")+"</span>"),onClick:function(_,w){var S=a(_);if(S)return!0;var M=r.default(_.selection.getSelectionStartElem()),D=x.getRowNode(M.elems[0]);if(!D)return!0;var k=Number(x.getCurrentRowIndex(w.elems[0],D)),N=x.getTableHtml(w.elems[0]),T=g.default.DeleteRow(r.default(N),k).elems[0].children[0].children.length,B="";return _.selection.createRangeByElem(w),_.selection.restoreSelection(),T===0?B=m.EMPTY_P:B=x.getTableHtml(g.default.DeleteRow(r.default(N),k).elems[0]),B=l(w,B),_.cmd.do("insertHTML",B),!0}},{$elem:r.default("<span>"+E("添加列")+"</span>"),onClick:function(_,w){var S=a(_);if(S)return!0;var M=r.default(_.selection.getSelectionStartElem()),D=x.getCurrentColIndex(M.elems[0]),k=x.getTableHtml(w.elems[0]),N=x.getTableHtml(g.default.ProcessingCol(r.default(k),D).elems[0]);return N=l(w,N),_.selection.createRangeByElem(w),_.selection.restoreSelection(),_.cmd.do("insertHTML",N),!0}},{$elem:r.default("<span>"+E("删除列")+"</span>"),onClick:function(_,w){var S=a(_);if(S)return!0;var M=r.default(_.selection.getSelectionStartElem()),D=x.getCurrentColIndex(M.elems[0]),k=x.getTableHtml(w.elems[0]),N=g.default.DeleteCol(r.default(k),D),T=N.elems[0].children[0].children[0].children.length,B="";return _.selection.createRangeByElem(w),_.selection.restoreSelection(),T===0?B=m.EMPTY_P:B=x.getTableHtml(N.elems[0]),B=l(w,B),_.cmd.do("insertHTML",B),!0}},{$elem:r.default("<span>"+E("设置表头")+"</span>"),onClick:function(_,w){var S=a(_);if(S)return!0;var M=r.default(_.selection.getSelectionStartElem()),D=x.getRowNode(M.elems[0]);if(!D)return!0;var k=Number(x.getCurrentRowIndex(w.elems[0],D));k!==0&&(k=0);var N=x.getTableHtml(w.elems[0]),T=x.getTableHtml(g.default.setTheHeader(r.default(N),k,"th").elems[0]);return T=l(w,T),_.selection.createRangeByElem(w),_.selection.restoreSelection(),_.cmd.do("insertHTML",T),!0}},{$elem:r.default("<span>"+E("取消表头")+"</span>"),onClick:function(_,w){var S=r.default(_.selection.getSelectionStartElem()),M=x.getRowNode(S.elems[0]);if(!M)return!0;var D=Number(x.getCurrentRowIndex(w.elems[0],M));D!==0&&(D=0);var k=x.getTableHtml(w.elems[0]),N=x.getTableHtml(g.default.setTheHeader(r.default(k),D,"td").elems[0]);return N=l(w,N),_.selection.createRangeByElem(w),_.selection.restoreSelection(),_.cmd.do("insertHTML",N),!0}}];p=new f.default(v,y,C),p.create()}function A(){p&&(p.remove(),p=null)}return{showTableTooltip:h,hideTableTooltip:A}}function a(v){var p=v.selection.getSelectionStartElem(),h=v.selection.getSelectionEndElem();return(p==null?void 0:p.elems[0])!==(h==null?void 0:h.elems[0])}function d(v){var p=s(v),h=p.showTableTooltip,A=p.hideTableTooltip;v.txt.eventHooks.tableClickEvents.push(h),v.txt.eventHooks.clickEvents.push(A),v.txt.eventHooks.keyupEvents.push(A),v.txt.eventHooks.toolbarClickEvents.push(A),v.txt.eventHooks.menuClickEvents.push(A),v.txt.eventHooks.textScrollEvents.push(A)}n.default=d;function l(v,p){var h=v.elems[0].nextSibling;return(!h||h.innerHTML==="<br>")&&(p+=""+m.EMPTY_P),p}},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(45)),r=t(e(91)),f=t(e(4)),g=t(e(138));(0,i.default)(n,"__esModule",{value:!0});var c=e(2),m=c.__importDefault(e(3));function s(A,y){for(var x=h(A),b=(0,o.default)(Array.prototype).apply(x.children),E=b[0].children.length,C=document.createElement("tr"),_=0;_<E;_++){var w=document.createElement("td");C.appendChild(w)}return(0,r.default)(b).call(b,y+1,0,C),p(x,b),m.default(x.parentNode)}function a(A,y){for(var x=h(A),b=(0,o.default)(Array.prototype).apply(x.children),E=function(_){var w,S=[];for((0,f.default)(w=(0,g.default)(b[_].children)).call(w,function(k){S.push(k)});b[_].children.length!==0;)b[_].removeChild(b[_].children[0]);var M=m.default(S[0]).getNodeName()!=="TH"?document.createElement("td"):document.createElement("th");(0,r.default)(S).call(S,y+1,0,M);for(var D=0;D<S.length;D++)b[_].appendChild(S[D])},C=0;C<b.length;C++)E(C);return p(x,b),m.default(x.parentNode)}function d(A,y){var x=h(A),b=(0,o.default)(Array.prototype).apply(x.children);return(0,r.default)(b).call(b,y,1),p(x,b),m.default(x.parentNode)}function l(A,y){for(var x=h(A),b=(0,o.default)(Array.prototype).apply(x.children),E=function(_){var w,S=[];for((0,f.default)(w=(0,g.default)(b[_].children)).call(w,function(D){S.push(D)});b[_].children.length!==0;)b[_].removeChild(b[_].children[0]);(0,r.default)(S).call(S,y,1);for(var M=0;M<S.length;M++)b[_].appendChild(S[M])},C=0;C<b.length;C++)E(C);return p(x,b),m.default(x.parentNode)}function v(A,y,x){for(var b=h(A),E=(0,o.default)(Array.prototype).apply(b.children),C=E[y].children,_=document.createElement("tr"),w=function(M){var D,k=document.createElement(x),N=C[M];(0,f.default)(D=(0,g.default)(N.childNodes)).call(D,function(T){k.appendChild(T)}),_.appendChild(k)},S=0;S<C.length;S++)w(S);return(0,r.default)(E).call(E,y,1,_),p(b,E),m.default(b.parentNode)}function p(A,y){for(;A.children.length!==0;)A.removeChild(A.children[0]);for(var x=0;x<y.length;x++)A.appendChild(y[x])}function h(A){var y=A.elems[0].children[0];return y.nodeName==="COLGROUP"&&(y=A.elems[0].children[A.elems[0].children.length-1]),y}n.default={ProcessingRow:s,ProcessingCol:a,DeleteRow:d,DeleteCol:l,setTheHeader:v}},function(u,n,e){var t=e(396);u.exports=t},function(u,n,e){e(50),e(397);var t=e(9);u.exports=t.Array.from},function(u,n,e){var t=e(5),i=e(398),o=e(115),r=!o(function(f){Array.from(f)});t({target:"Array",stat:!0,forced:r},{from:i})},function(u,n,e){var t=e(40),i=e(31),o=e(114),r=e(112),f=e(35),g=e(69),c=e(113);u.exports=function(m){var s=i(m),a=typeof this=="function"?this:Array,d=arguments.length,l=d>1?arguments[1]:void 0,v=l!==void 0,p=c(s),h=0,A,y,x,b,E,C;if(v&&(l=t(l,d>2?arguments[2]:void 0,2)),p!=null&&!(a==Array&&r(p)))for(b=p.call(s),E=b.next,y=new a;!(x=E.call(b)).done;h++)C=v?o(b,l,[x.value,h],!0):x.value,g(y,h,C);else for(A=f(s.length),y=new a(A);A>h;h++)C=v?l(s[h],h):s[h],g(y,h,C);return y.length=h,y}},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(4)),r=t(e(138));(0,i.default)(n,"__esModule",{value:!0});var f=e(2),g=f.__importDefault(e(3)),c=function(){function m(s){this.editor=s}return m.prototype.getRowNode=function(s){var a,d=g.default(s).elems[0];return d.parentNode&&(d=(a=g.default(d).parentUntil("TR",d))===null||a===void 0?void 0:a.elems[0]),d},m.prototype.getCurrentRowIndex=function(s,a){var d,l=0,v=s.children[0];return v.nodeName==="COLGROUP"&&(v=s.children[s.children.length-1]),(0,o.default)(d=(0,r.default)(v.children)).call(d,function(p,h){p===a&&(l=h)}),l},m.prototype.getCurrentColIndex=function(s){var a,d,l=0,v=g.default(s).getNodeName()==="TD"||g.default(s).getNodeName()==="TH"?s:(d=g.default(s).parentUntil("TD",s))===null||d===void 0?void 0:d.elems[0],p=g.default(v).parent();return(0,o.default)(a=(0,r.default)(p.elems[0].children)).call(a,function(h,A){h===v&&(l=A)}),l},m.prototype.getTableHtml=function(s){var a='<table border="0" width="100%" cellpadding="0" cellspacing="0">'+g.default(s).html()+"</table>";return a},m}();n.default=c},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0}),n.bindEventKeyboardEvent=n.bindClickEvent=void 0;var o=e(2),r=o.__importDefault(e(3));function f(m){if(!m.length)return!1;var s=m.elems[0];return s.nodeName==="P"&&s.innerHTML==="<br>"}function g(m){function s(a,d){if(d.detail>=3){var l=window.getSelection();if(l){var v=l.focusNode,p=l.anchorNode,h=r.default(p==null?void 0:p.parentElement);if(!a.isContain(r.default(v))){var A=h.elems[0].tagName==="TD"?h:h.parentUntilEditor("td",m);if(A){var y=m.selection.getRange();y==null||y.setEnd(A.elems[0],A.elems[0].childNodes.length),m.selection.restoreSelection()}}}}}m.txt.eventHooks.tableClickEvents.push(s)}n.bindClickEvent=g;function c(m){var s=m.txt,a=m.selection,d=s.eventHooks.keydownEvents;d.push(function(l){m.selection.saveRange();var v=a.getSelectionContainerElem();if(v){var p=v.getNodeTop(m),h=p.length&&p.prev().length?p.prev():null;if(h&&h.getNodeName()==="TABLE"&&a.isSelectionEmpty()&&a.getCursorPos()===0&&l.keyCode===8){var A=p.next(),y=!!A.length;y&&f(p)&&(p.remove(),m.selection.setRangeToElem(A.elems[0])),l.preventDefault()}}})}n.bindEventKeyboardEvent=c},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(26));(0,i.default)(n,"__esModule",{value:!0}),n.formatCodeHtml=void 0;var r=e(2),f=r.__importDefault(e(38)),g=r.__importDefault(e(3)),c=e(6),m=r.__importDefault(e(402)),s=r.__importDefault(e(139)),a=r.__importDefault(e(33)),d=r.__importDefault(e(403));function l(p,h){if(!h)return h;return h=y(h),h=A(h),h=c.replaceSpecialSymbol(h),h;function A(x){var b=x.match(/<pre[\s|\S]+?\/pre>/g);return b===null||(0,o.default)(b).call(b,function(E){x=x.replace(E,E.replace(/<\/code><code>/g,`
`).replace(/<br>/g,""))}),x}function y(x){var b,E=x.match(/<span\sclass="hljs[\s|\S]+?\/span>/gm);if(!E||!E.length)return x;for(var C=(0,o.default)(b=c.deepClone(E)).call(b,function(w){return w=w.replace(/<span\sclass="hljs[^>]+>/,""),w.replace(/<\/span>/,"")}),_=0;_<E.length;_++)x=x.replace(E[_],C[_]);return y(x)}}n.formatCodeHtml=l;var v=function(p){r.__extends(h,p);function h(A){var y=this,x=g.default('<div class="w-e-menu" data-title="代码"><i class="w-e-icon-terminal"></i></div>');return y=p.call(this,x,A)||this,d.default(A),y}return h.prototype.insertLineCode=function(A){var y=this.editor,x=g.default("<code>"+A+"</code>");y.cmd.do("insertElem",x),y.selection.createRangeByElem(x,!1),y.selection.restoreSelection()},h.prototype.clickHandler=function(){var A=this.editor,y=A.selection.getSelectionText();this.isActive||(A.selection.isSelectionEmpty()?this.createPanel("",""):this.insertLineCode(y))},h.prototype.createPanel=function(A,y){var x=m.default(this.editor,A,y),b=new a.default(this,x);b.create()},h.prototype.tryChangeActive=function(){var A=this.editor;s.default(A)?this.active():this.unActive()},h}(f.default);n.default=v},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(26));(0,i.default)(n,"__esModule",{value:!0});var r=e(2),f=e(6),g=r.__importDefault(e(3)),c=r.__importDefault(e(139)),m=e(7);function s(a,d,l){var v,p=f.getRandom("input-iframe"),h=f.getRandom("select"),A=f.getRandom("btn-ok");function y(C,_){var w,S=c.default(a);S&&x();var M=(w=a.selection.getSelectionStartElem())===null||w===void 0?void 0:w.elems[0].innerHTML;M&&a.cmd.do("insertHTML",m.EMPTY_P);var D=_.replace(/</g,"&lt;").replace(/>/g,"&gt;");a.highlight&&(D=a.highlight.highlightAuto(D).value),a.cmd.do("insertHTML",'<pre><code class="'+C+'">'+D+"</code></pre>");var k=a.selection.getSelectionStartElem(),N=k==null?void 0:k.getNodeTop(a);(N==null?void 0:N.getNextSibling().elems.length)===0&&g.default(m.EMPTY_P).insertAfter(N)}function x(){if(c.default(a)){var C=a.selection.getSelectionStartElem(),_=C==null?void 0:C.getNodeTop(a);_&&(a.selection.createRangeByElem(_),a.selection.restoreSelection())}}var b=function(C){return a.i18next.t(C)},E={width:500,height:0,tabs:[{title:b("menus.panelMenus.code.插入代码"),tpl:`<div>
                        <select name="" id="`+h+`">
                            `+(0,o.default)(v=a.config.languageType).call(v,function(C){return"<option "+(l==C?"selected":"")+' value ="'+C+'">'+C+"</option>"})+`
                        </select>
                        <textarea id="`+p+'" type="text" class="wang-code-textarea" placeholder="" style="height: 160px">'+d.replace(/&quot;/g,'"')+`</textarea>
                        <div class="w-e-button-container">
                            <button type="button" id="`+A+'" class="right">'+(c.default(a)?b("修改"):b("插入"))+`</button>
                        </div>
                    </div>`,events:[{selector:"#"+A,type:"click",fn:function(){var C=document.getElementById(p),_=g.default("#"+h),w=_.val(),S=C.value;if(S)return c.default(a)?!1:(y(w,S),!0)}}]}]};return E}n.default=s},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(2),r=o.__importDefault(e(404)),f=o.__importDefault(e(405));function g(c){r.default(c),f.default(c)}n.default=g},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0}),n.createShowHideFn=void 0;var o=e(2),r=o.__importDefault(e(3)),f=o.__importDefault(e(39));function g(m){var s;function a(l){var v="menus.panelMenus.code.",p=function(A,y){return y===void 0&&(y=v),m.i18next.t(y+A)},h=[{$elem:r.default("<span>"+p("删除代码")+"</span>"),onClick:function(A,y){return y.remove(),!0}}];s=new f.default(m,l,h),s.create()}function d(){s&&(s.remove(),s=null)}return{showCodeTooltip:a,hideCodeTooltip:d}}n.createShowHideFn=g;function c(m){var s=g(m),a=s.showCodeTooltip,d=s.hideCodeTooltip;m.txt.eventHooks.codeClickEvents.push(a),m.txt.eventHooks.clickEvents.push(d),m.txt.eventHooks.toolbarClickEvents.push(d),m.txt.eventHooks.menuClickEvents.push(d),m.txt.eventHooks.textScrollEvents.push(d)}n.default=c},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(2),r=e(7),f=o.__importDefault(e(3));function g(c){var m=c.$textElem,s=c.selection,a=c.txt,d=a.eventHooks.keydownEvents;d.push(function(l){var v;if(l.keyCode===40){var p=s.getSelectionContainerElem(),h=(v=m.children())===null||v===void 0?void 0:v.last();if((p==null?void 0:p.elems[0].tagName)==="XMP"&&(h==null?void 0:h.elems[0].tagName)==="PRE"){var A=f.default(r.EMPTY_P);m.append(A)}}}),d.push(function(l){c.selection.saveRange();var v=s.getSelectionContainerElem();if(v){var p=v.getNodeTop(c),h=p==null?void 0:p.prev(),A=p==null?void 0:p.getNextSibling();if(h.length&&(h==null?void 0:h.getNodeName())==="PRE"&&A.length===0&&s.getCursorPos()===0&&l.keyCode===8){var y=f.default(r.EMPTY_P);m.append(y)}}})}n.default=g},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(2),r=o.__importDefault(e(23)),f=o.__importDefault(e(3)),g=o.__importDefault(e(407)),c=e(6),m=e(7),s=function(a){o.__extends(d,a);function d(l){var v=this,p=f.default('<div class="w-e-menu" data-title="分割线"><i class="w-e-icon-split-line"></i></div>');return v=a.call(this,p,l)||this,g.default(l),v}return d.prototype.clickHandler=function(){var l=this.editor,v=l.selection.getRange(),p=l.selection.getSelectionContainerElem();if(p!=null&&p.length){var h=f.default(p.elems[0]),A=h.parentUntil("TABLE",p.elems[0]),y=h.children();h.getNodeName()!=="CODE"&&(A&&f.default(A.elems[0]).getNodeName()==="TABLE"||y&&y.length!==0&&f.default(y.elems[0]).getNodeName()==="IMG"&&!(v!=null&&v.collapsed)||this.createSplitLine())}},d.prototype.createSplitLine=function(){var l="<hr/>"+m.EMPTY_P;c.UA.isFirefox&&(l="<hr/><p></p>"),this.editor.cmd.do("insertHTML",l)},d.prototype.tryChangeActive=function(){},d}(r.default);n.default=s},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(2),r=o.__importDefault(e(408));function f(g){r.default(g)}n.default=f},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(2),r=o.__importDefault(e(3)),f=o.__importDefault(e(39));function g(m){var s;function a(l){var v=[{$elem:r.default("<span>"+m.i18next.t("menus.panelMenus.删除")+"</span>"),onClick:function(p,h){return p.selection.createRangeByElem(h),p.selection.restoreSelection(),p.cmd.do("delete"),!0}}];s=new f.default(m,l,v),s.create()}function d(){s&&(s.remove(),s=null)}return{showSplitLineTooltip:a,hideSplitLineTooltip:d}}function c(m){var s=g(m),a=s.showSplitLineTooltip,d=s.hideSplitLineTooltip;m.txt.eventHooks.splitLineEvents.push(a),m.txt.eventHooks.clickEvents.push(d),m.txt.eventHooks.keyupEvents.push(d),m.txt.eventHooks.toolbarClickEvents.push(d),m.txt.eventHooks.menuClickEvents.push(d),m.txt.eventHooks.textScrollEvents.push(d)}n.default=c},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(4));(0,i.default)(n,"__esModule",{value:!0});var r=e(2),f=r.__importDefault(e(3)),g=r.__importDefault(e(23)),c=e(98),m=r.__importDefault(e(415)),s=r.__importDefault(e(140)),a=function(d){r.__extends(l,d);function l(v){var p=this,h=f.default(`<div class="w-e-menu" data-title="待办事项">
                    <i class="w-e-icon-checkbox-checked"></i>
                </div>`);return p=d.call(this,h,v)||this,m.default(v),p}return l.prototype.clickHandler=function(){var v=this.editor;c.isAllTodo(v)?(this.cancelTodo(),this.tryChangeActive()):this.setTodo()},l.prototype.tryChangeActive=function(){c.isAllTodo(this.editor)?this.active():this.unActive()},l.prototype.setTodo=function(){var v=this.editor,p=v.selection.getSelectionRangeTopNodes();(0,o.default)(p).call(p,function(h){var A,y=h==null?void 0:h.getNodeName();if(y==="P"){var x=s.default(h),b=x.getTodo(),E=(A=b.children())===null||A===void 0?void 0:A.getNode();b.insertAfter(h),v.selection.moveCursor(E),h.remove()}}),this.tryChangeActive()},l.prototype.cancelTodo=function(){var v=this.editor,p=v.selection.getSelectionRangeTopNodes();(0,o.default)(p).call(p,function(h){var A,y,x,b=(y=(A=h.childNodes())===null||A===void 0?void 0:A.childNodes())===null||y===void 0?void 0:y.clone(!0),E=f.default("<p></p>");E.append(b),E.insertAfter(h),(x=E.childNodes())===null||x===void 0||x.get(0).remove(),v.selection.moveCursor(E.getNode()),h.remove()})},l}(g.default);n.default=a},function(u,n,e){u.exports=e(411)},function(u,n,e){var t=e(412);u.exports=t},function(u,n,e){var t=e(413),i=Array.prototype;u.exports=function(o){var r=o.every;return o===i||o instanceof Array&&r===i.every?t:r}},function(u,n,e){e(414);var t=e(15);u.exports=t("Array").every},function(u,n,e){var t=e(5),i=e(32).every,o=e(67),r=e(22),f=o("every"),g=r("every");t({target:"Array",proto:!0,forced:!f||!g},{every:function(c){return i(this,c,arguments.length>1?arguments[1]:void 0)}})},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(4));(0,i.default)(n,"__esModule",{value:!0});var r=e(2),f=r.__importDefault(e(3)),g=e(98),c=r.__importDefault(e(140)),m=e(98),s=e(7);function a(d){function l(A){var y,x;if(g.isAllTodo(d)){A.preventDefault();var b=d.selection,E=b.getSelectionRangeTopNodes()[0],C=(y=E.childNodes())===null||y===void 0?void 0:y.get(0),_=(x=window.getSelection())===null||x===void 0?void 0:x.anchorNode,w=b.getRange();if(!(w!=null&&w.collapsed)){var S=w==null?void 0:w.commonAncestorContainer.childNodes,M=w==null?void 0:w.startContainer,D=w==null?void 0:w.endContainer,k=w==null?void 0:w.startOffset,N=w==null?void 0:w.endOffset,T=0,B=0,R=[];S==null||(0,o.default)(S).call(S,function(G,q){G.contains(M)&&(T=q),G.contains(D)&&(B=q)}),B-T>1&&(S==null||(0,o.default)(S).call(S,function(G,q){q<=T||q>=B||R.push(G)}),(0,o.default)(R).call(R,function(G){G.remove()})),m.dealTextNode(M,k),m.dealTextNode(D,N,!1),d.selection.moveCursor(D,0)}if(E.text()===""){var I=f.default(s.EMPTY_P);I.insertAfter(E),b.moveCursor(I.getNode()),E.remove();return}var P=b.getCursorPos(),H=g.getCursorNextNode(C==null?void 0:C.getNode(),_,P),z=c.default(f.default(H)),O=z.getInputContainer(),$=O.parent().getNode(),L=z.getTodo(),U=O.getNode().nextSibling;if((C==null?void 0:C.text())===""&&(C==null||C.append(f.default("<br>"))),L.insertAfter(E),!U||(U==null?void 0:U.textContent)===""){if((U==null?void 0:U.nodeName)!=="BR"){var Y=f.default("<br>");Y.insertAfter(O)}b.moveCursor($,1)}else b.moveCursor($)}}function v(A){var y,x;if(g.isAllTodo(d)){var b=d.selection,E=b.getSelectionRangeTopNodes()[0],C=(y=E.childNodes())===null||y===void 0?void 0:y.getNode(),_=f.default("<p></p>"),w=_.getNode(),S=(x=window.getSelection())===null||x===void 0?void 0:x.anchorNode,M=b.getCursorPos(),D=S.previousSibling;if(E.text()===""){A.preventDefault();var k=f.default(s.EMPTY_P);k.insertAfter(E),E.remove(),b.moveCursor(k.getNode(),0);return}if((D==null?void 0:D.nodeName)==="SPAN"&&D.childNodes[0].nodeName==="INPUT"&&M===0){var N;A.preventDefault(),C==null||(0,o.default)(N=C.childNodes).call(N,function(T,B){B!==0&&w.appendChild(T.cloneNode(!0))}),_.insertAfter(E),E.remove()}}}function p(){var A=d.selection,y=A.getSelectionRangeTopNodes()[0];y&&m.isTodo(y)&&y.text()===""&&(f.default(s.EMPTY_P).insertAfter(y),y.remove())}function h(A){A&&A.target instanceof HTMLInputElement&&A.target.type==="checkbox"&&(A.target.checked?A.target.setAttribute("checked","true"):A.target.removeAttribute("checked"))}d.txt.eventHooks.enterDownEvents.push(l),d.txt.eventHooks.deleteUpEvents.push(p),d.txt.eventHooks.deleteDownEvents.push(v),d.txt.eventHooks.clickEvents.push(h)}n.default=a},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0}),n.selectorValidator=void 0;var o=e(2),r=o.__importDefault(e(3)),f=e(6),g=e(7),c=o.__importDefault(e(130)),m={border:"1px solid #c9d8db",toolbarBgColor:"#FFF",toolbarBottomBorder:"1px solid #EEE"};function s(d){var l=d.toolbarSelector,v=r.default(l),p=d.textSelector,h=d.config,A=h.height,y=d.i18next,x=r.default("<div></div>"),b=r.default("<div></div>"),E,C,_=null;p==null?(C=v.children(),v.append(x).append(b),x.css("background-color",m.toolbarBgColor).css("border",m.border).css("border-bottom",m.toolbarBottomBorder),b.css("border",m.border).css("border-top","none").css("height",A+"px")):(v.append(x),_=r.default(p).children(),r.default(p).append(b),C=b.children()),E=r.default("<div></div>"),E.attr("contenteditable","true").css("width","100%").css("height","100%");var w,S=d.config.placeholder;S!==c.default.placeholder?w=r.default("<div>"+S+"</div>"):w=r.default("<div>"+y.t(S)+"</div>"),w.addClass("placeholder"),C&&C.length?(E.append(C),w.hide()):E.append(r.default(g.EMPTY_P)),_&&_.length&&(E.append(_),w.hide()),b.append(E),b.append(w),x.addClass("w-e-toolbar").css("z-index",d.zIndex.get("toolbar")),b.addClass("w-e-text-container"),b.css("z-index",d.zIndex.get()),E.addClass("w-e-text");var M=f.getRandom("toolbar-elem");x.attr("id",M);var D=f.getRandom("text-elem");E.attr("id",D);var k=b.getBoundingClientRect().height,N=E.getBoundingClientRect().height;k!==N&&E.css("min-height",k+"px"),d.$toolbarElem=x,d.$textContainerElem=b,d.$textElem=E,d.toolbarElemId=M,d.textElemId=D}n.default=s;function a(d){var l="data-we-id",v=/^wangEditor-\d+$/,p=d.textSelector,h=d.toolbarSelector,A={bar:r.default("<div></div>"),text:r.default("<div></div>")};if(h==null)throw new Error("错误：初始化编辑器时候未传入任何参数，请查阅文档");if(A.bar=r.default(h),!A.bar.elems.length)throw new Error("无效的节点选择器："+h);if(v.test(A.bar.attr(l)))throw new Error("初始化节点已存在编辑器实例，无法重复创建编辑器");if(p){if(A.text=r.default(p),!A.text.elems.length)throw new Error("无效的节点选择器："+p);if(v.test(A.text.attr(l)))throw new Error("初始化节点已存在编辑器实例，无法重复创建编辑器")}A.bar.attr(l,d.id),A.text.attr(l,d.id),d.beforeDestroy(function(){A.bar.removeAttr(l),A.text.removeAttr(l)})}n.selectorValidator=a},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(2),r=o.__importDefault(e(3)),f=e(7);function g(c,m){var s=c.$textElem,a=s.children();if(!a||!a.length){s.append(r.default(f.EMPTY_P)),g(c);return}var d=a.last();if(m){var l=d.html().toLowerCase(),v=d.getNodeName();if(l!=="<br>"&&l!=="<br/>"||v!=="P"){s.append(r.default(f.EMPTY_P)),g(c);return}}c.selection.createRangeByElem(d,!1,!0),c.config.focus?c.selection.restoreSelection():c.selection.clearWindowSelectionRange()}n.default=g},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(4));(0,i.default)(n,"__esModule",{value:!0});var r=e(2),f=r.__importDefault(e(3));function g(l){c(l),m(l),s(l)}function c(l){l.txt.eventHooks.changeEvents.push(function(){var v=l.config.onchange;if(v){var p=l.txt.html()||"";l.isFocus=!0,v(p)}l.txt.togglePlaceholder()})}function m(l){l.isFocus=!1;function v(p){var h=p.target,A=f.default(h),y=l.$textElem,x=l.$toolbarElem,b=y.isContain(A),E=x.isContain(A),C=x.elems[0]==p.target;if(b)l.isFocus||d(l),l.isFocus=!0;else{if(E&&!C||!l.isFocus)return;a(l),l.isFocus=!1}}document.activeElement===l.$textElem.elems[0]&&l.config.focus&&(d(l),l.isFocus=!0),f.default(document).on("click",v),l.beforeDestroy(function(){f.default(document).off("click",v)})}function s(l){l.$textElem.on("compositionstart",function(){l.isComposing=!0,l.txt.togglePlaceholder()}).on("compositionend",function(){l.isComposing=!1,l.txt.togglePlaceholder()})}function a(l){var v,p=l.config,h=p.onblur,A=l.txt.html()||"";(0,o.default)(v=l.txt.eventHooks.onBlurEvents).call(v,function(y){return y()}),h(A)}function d(l){var v=l.config,p=v.onfocus,h=l.txt.html()||"";p(h)}n.default=g},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});function o(r){var f=r.config,g=f.lang,c=f.languages;if(r.i18next!=null){try{r.i18next.init({ns:"wangEditor",lng:g,defaultNS:"wangEditor",resources:c})}catch(m){throw new Error("i18next:"+m)}return}r.i18next={t:function(m){var s=m.split(".");return s[s.length-1]}}}n.default=o},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(29));(0,i.default)(n,"__esModule",{value:!0}),n.setUnFullScreen=n.setFullScreen=void 0;var r=e(2),f=r.__importDefault(e(3));e(421);var g="w-e-icon-fullscreen",c="w-e-icon-fullscreen_exit",m="w-e-full-screen-editor";n.setFullScreen=function(a){var d=f.default(a.toolbarSelector),l=a.$textContainerElem,v=a.$toolbarElem,p=(0,o.default)(v).call(v,"i."+g),h=a.config;p.removeClass(g),p.addClass(c),d.addClass(m),d.css("z-index",h.zIndexFullScreen);var A=v.getBoundingClientRect();l.css("height","calc(100% - "+A.height+"px)")},n.setUnFullScreen=function(a){var d=f.default(a.toolbarSelector),l=a.$textContainerElem,v=a.$toolbarElem,p=(0,o.default)(v).call(v,"i."+c),h=a.config;p.removeClass(c),p.addClass(g),d.removeClass(m),d.css("z-index","auto"),l.css("height",h.height+"px")};var s=function(a){if(!a.textSelector&&a.config.showFullScreen){var d=a.$toolbarElem,l=f.default(`<div class="w-e-menu" data-title="全屏">
            <i class="`+g+`"></i>
        </div>`);l.on("click",function(v){var p,h=(0,o.default)(p=f.default(v.currentTarget)).call(p,"i");h.hasClass(g)?(l.attr("data-title","取消全屏"),n.setFullScreen(a)):(l.attr("data-title","全屏"),n.setUnFullScreen(a))}),d.append(l)}};n.default=s},function(u,n,e){var t=e(20),i=e(422);i=i.__esModule?i.default:i,typeof i=="string"&&(i=[[u.i,i,""]]);var o={};o.insert="head",o.singleton=!1,t(i,o),u.exports=i.locals||{}},function(u,n,e){var t=e(21);n=t(!1),n.push([u.i,`.w-e-full-screen-editor {
  position: fixed;
  width: 100%!important;
  height: 100%!important;
  left: 0;
  top: 0;
}
`,""]),u.exports=n},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(29));(0,i.default)(n,"__esModule",{value:!0});var r=function(f,g){var c,m=f.isEnable?f.$textElem:(0,o.default)(c=f.$textContainerElem).call(c,".w-e-content-mantle"),s=(0,o.default)(m).call(m,"[id='"+g+"']"),a=s.getOffsetData().top;m.scrollTop(a)};n.default=r},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(2),r=o.__importDefault(e(129)),f={menu:2,panel:2,toolbar:1,tooltip:1,textContainer:1},g=function(){function c(){this.tier=f,this.baseZIndex=r.default.zIndex}return c.prototype.get=function(m){return m&&this.tier[m]?this.baseZIndex+this.tier[m]:this.baseZIndex},c.prototype.init=function(m){this.baseZIndex==r.default.zIndex&&(this.baseZIndex=m.config.zIndex)},c}();n.default=g},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(70)),r=t(e(4));(0,i.default)(n,"__esModule",{value:!0});var f=e(2),g=f.__importDefault(e(426)),c=e(6),m=e(7);function s(d,l){return(0,o.default)(d).call(d,function(v){var p=v.type,h=v.target,A=v.attributeName;return p!="attributes"||p=="attributes"&&(A=="contenteditable"||h!=l)})}var a=function(d){f.__extends(l,d);function l(v){var p=d.call(this,function(h,A){var y;if(h=s(h,A.target),(y=p.data).push.apply(y,h),v.isCompatibleMode)p.asyncSave();else if(!v.isComposing)return p.asyncSave()})||this;return p.editor=v,p.data=[],p.asyncSave=m.EMPTY_FN,p}return l.prototype.save=function(){this.data.length&&(this.editor.history.save(this.data),this.data.length=0,this.emit())},l.prototype.emit=function(){var v;(0,r.default)(v=this.editor.txt.eventHooks.changeEvents).call(v,function(p){return p()})},l.prototype.observe=function(){var v=this;d.prototype.observe.call(this,this.editor.$textElem.elems[0]);var p=this.editor.config.onchangeTimeout;this.asyncSave=c.debounce(function(){v.save()},p),this.editor.isCompatibleMode||this.editor.$textElem.on("compositionend",function(){v.asyncSave()})},l}(g.default);n.default=a},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=function(){function r(f,g){var c=this;this.options={subtree:!0,childList:!0,attributes:!0,attributeOldValue:!0,characterData:!0,characterDataOldValue:!0},this.callback=function(m){f(m,c)},this.observer=new MutationObserver(this.callback),g&&(this.options=g)}return(0,i.default)(r.prototype,"target",{get:function(){return this.node},enumerable:!1,configurable:!0}),r.prototype.observe=function(f){this.node instanceof Node||(this.node=f,this.connect())},r.prototype.connect=function(){if(this.node)return this.observer.observe(this.node,this.options),this;throw new Error("还未初始化绑定，请您先绑定有效的 Node 节点")},r.prototype.disconnect=function(){var f=this.observer.takeRecords();f.length&&this.callback(f),this.observer.disconnect()},r}();n.default=o},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(2),r=o.__importDefault(e(428)),f=o.__importDefault(e(435)),g=o.__importDefault(e(436)),c=function(){function m(s){this.editor=s,this.content=new r.default(s),this.scroll=new f.default(s),this.range=new g.default(s)}return(0,i.default)(m.prototype,"size",{get:function(){return this.scroll.size},enumerable:!1,configurable:!0}),m.prototype.observe=function(){this.content.observe(),this.scroll.observe(),!this.editor.isCompatibleMode&&this.range.observe()},m.prototype.save=function(s){s.length&&(this.content.save(s),this.scroll.save(),!this.editor.isCompatibleMode&&this.range.save())},m.prototype.revoke=function(){this.editor.change.disconnect();var s=this.content.revoke();s&&(this.scroll.revoke(),this.editor.isCompatibleMode||(this.range.revoke(),this.editor.$textElem.focus())),this.editor.change.connect(),s&&this.editor.change.emit()},m.prototype.restore=function(){this.editor.change.disconnect();var s=this.content.restore();s&&(this.scroll.restore(),this.editor.isCompatibleMode||(this.range.restore(),this.editor.$textElem.focus())),this.editor.change.connect(),s&&this.editor.change.emit()},m}();n.default=c},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(2),r=o.__importDefault(e(429)),f=o.__importDefault(e(433)),g=function(){function c(m){this.editor=m}return c.prototype.observe=function(){this.editor.isCompatibleMode?this.cache=new f.default(this.editor):this.cache=new r.default(this.editor),this.cache.observe()},c.prototype.save=function(m){this.editor.isCompatibleMode?this.cache.save():this.cache.compile(m)},c.prototype.revoke=function(){var m;return(m=this.cache)===null||m===void 0?void 0:m.revoke()},c.prototype.restore=function(){var m;return(m=this.cache)===null||m===void 0?void 0:m.restore()},c}();n.default=g},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(2),r=o.__importDefault(e(99)),f=o.__importDefault(e(431)),g=e(432),c=function(m){o.__extends(s,m);function s(a){var d=m.call(this,a.config.historyMaxSize)||this;return d.editor=a,d}return s.prototype.observe=function(){this.resetMaxSize(this.editor.config.historyMaxSize)},s.prototype.compile=function(a){return this.save(f.default(a)),this},s.prototype.revoke=function(){return m.prototype.revoke.call(this,function(a){g.revoke(a)})},s.prototype.restore=function(){return m.prototype.restore.call(this,function(a){g.restore(a)})},s}(r.default);n.default=c},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0}),n.CeilStack=void 0;var o=function(){function r(f){f===void 0&&(f=0),this.data=[],this.max=0,this.reset=!1,f=Math.abs(f),f&&(this.max=f)}return r.prototype.resetMax=function(f){f=Math.abs(f),!this.reset&&!isNaN(f)&&(this.max=f,this.reset=!0)},(0,i.default)(r.prototype,"size",{get:function(){return this.data.length},enumerable:!1,configurable:!0}),r.prototype.instack=function(f){return this.data.unshift(f),this.max&&this.size>this.max&&(this.data.length=this.max),this},r.prototype.outstack=function(){return this.data.shift()},r.prototype.clear=function(){return this.data.length=0,this},r}();n.CeilStack=o},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(4)),r=t(e(27));(0,i.default)(n,"__esModule",{value:!0}),n.compliePosition=n.complieNodes=n.compileValue=n.compileType=void 0;var f=e(6);function g(v){switch(v){case"childList":return"node";case"attributes":return"attr";default:return"text"}}n.compileType=g;function c(v){switch(v.type){case"attributes":return v.target.getAttribute(v.attributeName)||"";case"characterData":return v.target.textContent;default:return""}}n.compileValue=c;function m(v){var p={};return v.addedNodes.length&&(p.add=f.toArray(v.addedNodes)),v.removedNodes.length&&(p.remove=f.toArray(v.removedNodes)),p}n.complieNodes=m;function s(v){var p;return v.previousSibling?p={type:"before",target:v.previousSibling}:v.nextSibling?p={type:"after",target:v.nextSibling}:p={type:"parent",target:v.target},p}n.compliePosition=s;var a=["UL","OL","H1","H2","H3","H4","H5","H6"];function d(v){var p=[],h=!1,A=[];return(0,o.default)(v).call(v,function(y,x){var b={type:g(y.type),target:y.target,attr:y.attributeName||"",value:c(y)||"",oldValue:y.oldValue||"",nodes:m(y),position:s(y)};if(p.push(b),!!f.UA.isFirefox){if(h&&y.addedNodes.length&&y.addedNodes[0].nodeType==1){var E=y.addedNodes[0],C={type:"node",target:E,attr:"",value:"",oldValue:"",nodes:{add:[h]},position:{type:"parent",target:E}};(0,r.default)(a).call(a,E.nodeName)!=-1?(C.nodes.add=f.toArray(E.childNodes),p.push(C)):h.nodeType==3?(l(E,A)&&(C.nodes.add=f.toArray(E.childNodes)),p.push(C)):(0,r.default)(a).call(a,y.target.nodeName)==-1&&l(E,A)&&(C.nodes.add=f.toArray(E.childNodes),p.push(C))}b.type=="node"&&y.removedNodes.length==1?(h=y.removedNodes[0],A.push(h)):(h=!1,A.length=0)}}),p}n.default=d;function l(v,p){for(var h=0,A=p.length-1;A>0&&v.contains(p[A]);A--)h++;return h}},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(4)),r=t(e(94));(0,i.default)(n,"__esModule",{value:!0}),n.restore=n.revoke=void 0;function f(A,y){var x=A.position.target;switch(A.position.type){case"before":x.nextSibling?(x=x.nextSibling,(0,o.default)(y).call(y,function(b){A.target.insertBefore(b,x)})):(0,o.default)(y).call(y,function(b){A.target.appendChild(b)});break;case"after":(0,o.default)(y).call(y,function(b){A.target.insertBefore(b,x)});break;default:(0,o.default)(y).call(y,function(b){x.appendChild(b)});break}}function g(A){for(var y=0,x=(0,r.default)(A.nodes);y<x.length;y++){var b=x[y],E=b[0],C=b[1];switch(E){case"add":(0,o.default)(C).call(C,function(_){A.target.removeChild(_)});break;default:{f(A,C);break}}}}function c(A){var y=A.target;A.oldValue==null?y.removeAttribute(A.attr):y.setAttribute(A.attr,A.oldValue)}function m(A){A.target.textContent=A.oldValue}var s={node:g,text:m,attr:c};function a(A){for(var y=A.length-1;y>-1;y--){var x=A[y];s[x.type](x)}}n.revoke=a;function d(A){for(var y=0,x=(0,r.default)(A.nodes);y<x.length;y++){var b=x[y],E=b[0],C=b[1];switch(E){case"add":{f(A,C);break}default:{(0,o.default)(C).call(C,function(_){_.parentNode.removeChild(_)});break}}}}function l(A){A.target.textContent=A.value}function v(A){A.target.setAttribute(A.attr,A.value)}var p={node:d,text:l,attr:v};function h(A){for(var y=0,x=A;y<x.length;y++){var b=x[y];p[b.type](b)}}n.restore=h},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(434),r=function(){function f(g){this.editor=g,this.data=new o.TailChain}return f.prototype.observe=function(){this.data.resetMax(this.editor.config.historyMaxSize),this.data.insertLast(this.editor.$textElem.html())},f.prototype.save=function(){return this.data.insertLast(this.editor.$textElem.html()),this},f.prototype.revoke=function(){var g=this.data.prev();return g?(this.editor.$textElem.html(g),!0):!1},f.prototype.restore=function(){var g=this.data.next();return g?(this.editor.$textElem.html(g),!0):!1},f}();n.default=r},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(91));(0,i.default)(n,"__esModule",{value:!0}),n.TailChain=void 0;var r=function(){function f(){this.data=[],this.max=0,this.point=0,this.isRe=!1}return f.prototype.resetMax=function(g){g=Math.abs(g),g&&(this.max=g)},(0,i.default)(f.prototype,"size",{get:function(){return this.data.length},enumerable:!1,configurable:!0}),f.prototype.insertLast=function(g){if(this.isRe){var c;(0,o.default)(c=this.data).call(c,this.point+1),this.isRe=!1}for(this.data.push(g);this.max&&this.size>this.max;)this.data.shift();return this.point=this.size-1,this},f.prototype.current=function(){return this.data[this.point]},f.prototype.prev=function(){if(!this.isRe&&(this.isRe=!0),this.point--,this.point<0){this.point=0;return}return this.current()},f.prototype.next=function(){if(!this.isRe&&(this.isRe=!0),this.point++,this.point>=this.size){this.point=this.size-1;return}return this.current()},f}();n.TailChain=r},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(2),r=o.__importDefault(e(99)),f=function(g){o.__extends(c,g);function c(m){var s=g.call(this,m.config.historyMaxSize)||this;return s.editor=m,s.last=0,s.target=m.$textElem.elems[0],s}return c.prototype.observe=function(){var m=this;this.target=this.editor.$textElem.elems[0],this.editor.$textElem.on("scroll",function(){m.last=m.target.scrollTop}),this.resetMaxSize(this.editor.config.historyMaxSize)},c.prototype.save=function(){return g.prototype.save.call(this,[this.last,this.target.scrollTop]),this},c.prototype.revoke=function(){var m=this;return g.prototype.revoke.call(this,function(s){m.target.scrollTop=s[0]})},c.prototype.restore=function(){var m=this;return g.prototype.restore.call(this,function(s){m.target.scrollTop=s[1]})},c}(r.default);n.default=f},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=e(2),r=o.__importDefault(e(99)),f=o.__importDefault(e(3)),g=e(6);function c(s){return{start:[s.startContainer,s.startOffset],end:[s.endContainer,s.endOffset],root:s.commonAncestorContainer,collapsed:s.collapsed}}var m=function(s){o.__extends(a,s);function a(d){var l=s.call(this,d.config.historyMaxSize)||this;return l.editor=d,l.lastRange=c(document.createRange()),l.root=d.$textElem.elems[0],l.updateLastRange=g.debounce(function(){l.lastRange=c(l.rangeHandle)},d.config.onchangeTimeout),l}return(0,i.default)(a.prototype,"rangeHandle",{get:function(){var d=document.getSelection();return d&&d.rangeCount?d.getRangeAt(0):document.createRange()},enumerable:!1,configurable:!0}),a.prototype.observe=function(){var d=this;this.root=this.editor.$textElem.elems[0],this.resetMaxSize(this.editor.config.historyMaxSize);function l(){var p=d.rangeHandle;(d.root===p.commonAncestorContainer||d.root.contains(p.commonAncestorContainer))&&(d.editor.isComposing||d.updateLastRange())}function v(p){(p.key=="Backspace"||p.key=="Delete")&&d.updateLastRange()}f.default(document).on("selectionchange",l),this.editor.beforeDestroy(function(){f.default(document).off("selectionchange",l)}),d.editor.$textElem.on("keydown",v)},a.prototype.save=function(){var d=c(this.rangeHandle);return s.prototype.save.call(this,[this.lastRange,d]),this.lastRange=d,this},a.prototype.set=function(d){try{if(d){var l=this.rangeHandle;return l.setStart.apply(l,d.start),l.setEnd.apply(l,d.end),this.editor.menus.changeActive(),!0}}catch{return!1}return!1},a.prototype.revoke=function(){var d=this;return s.prototype.revoke.call(this,function(l){d.set(l[0])})},a.prototype.restore=function(){var d=this;return s.prototype.restore.call(this,function(l){d.set(l[1])})},a}(r.default);n.default=m},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(29));(0,i.default)(n,"__esModule",{value:!0});var r=e(2),f=r.__importDefault(e(3));e(438);function g(c){var m=!1,s,a;c.txt.eventHooks.changeEvents.push(function(){m&&(0,o.default)(s).call(s,".w-e-content-preview").html(c.$textElem.html())});function d(){if(!m){c.$textElem.hide();var v=c.zIndex.get("textContainer"),p=c.txt.html();s=f.default('<div class="w-e-content-mantle" style="z-index:'+v+`">
                <div class="w-e-content-preview w-e-text">`+p+`</div>
            </div>`),c.$textContainerElem.append(s);var h=c.zIndex.get("menu");a=f.default('<div class="w-e-menue-mantle" style="z-index:'+h+'"></div>'),c.$toolbarElem.append(a),m=!0,c.isEnable=!1}}function l(){m&&(s.remove(),a.remove(),c.$textElem.show(),m=!1,c.isEnable=!0)}return{disable:d,enable:l}}n.default=g},function(u,n,e){var t=e(20),i=e(439);i=i.__esModule?i.default:i,typeof i=="string"&&(i=[[u.i,i,""]]);var o={};o.insert="head",o.singleton=!1,t(i,o),u.exports=i.locals||{}},function(u,n,e){var t=e(21);n=t(!1),n.push([u.i,`.w-e-content-mantle {
  width: 100%;
  height: 100%;
  overflow-y: auto;
}
.w-e-content-mantle .w-e-content-preview {
  width: 100%;
  min-height: 100%;
  padding: 0 10px;
  line-height: 1.5;
}
.w-e-content-mantle .w-e-content-preview img {
  cursor: default;
}
.w-e-content-mantle .w-e-content-preview img:hover {
  box-shadow: none;
}
.w-e-menue-mantle {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
}
`,""]),u.exports=n},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0});var o=function(){function r(f){var g=this;this.editor=f;var c=function(){var m=document.activeElement;m===f.$textElem.elems[0]&&g.emit()};window.document.addEventListener("selectionchange",c),this.editor.beforeDestroy(function(){window.document.removeEventListener("selectionchange",c)})}return r.prototype.emit=function(){var f,g=this.editor.config.onSelectionChange;if(g){var c=this.editor.selection;c.saveRange(),c.isSelectionEmpty()||g({text:c.getSelectionText(),html:(f=c.getSelectionContainerElem())===null||f===void 0?void 0:f.elems[0].innerHTML,selection:c})}},r}();n.default=o},function(u,n,e){var t=e(0),i=t(e(1)),o=t(e(128)),r=t(e(94)),f=t(e(4));(0,i.default)(n,"__esModule",{value:!0}),n.registerPlugin=void 0;var g=e(2),c=g.__importDefault(e(87)),m=e(6);function s(d,l,v){if(!d)throw new TypeError("name is not define");if(!l)throw new TypeError("options is not define");if(!l.intention)throw new TypeError("options.intention is not define");if(l.intention&&typeof l.intention!="function")throw new TypeError("options.intention is not function");v[d],v[d]=l}n.registerPlugin=s;function a(d){var l=(0,o.default)({},m.deepClone(c.default.globalPluginsFunctionList),m.deepClone(d.pluginsFunctionList)),v=(0,r.default)(l);(0,f.default)(v).call(v,function(p){p[0];var h=p[1],A=h.intention,y=h.config;A(d,y)})}n.default=a},function(u,n,e){var t=e(0),i=t(e(1));(0,i.default)(n,"__esModule",{value:!0})}]).default})})(Ne);var je=Ne.exports;const ze=Ye(je),Ve={menus:["head","bold","fontSize","fontName","italic","underline","strikeThrough","indent","lineHeight","foreColor","backColor","link","list","todo","justify","quote","emoticon","image","video","table","code","splitLine","undo","redo"],excludeMenus:[],showMenuTooltips:!1,colors:["#000000","#eeece0","#1c487f","#4d80bf"],fontNames:["黑体","仿宋","楷体","标楷体","华文仿宋","华文楷体","宋体","微软雅黑","Arial","Tahoma","Verdana","Times New Roman","Courier New"],fontSizes:{"x-small":{name:"10px",value:"1"},small:{name:"13px",value:"2"},normal:{name:"16px",value:"3"},large:{name:"18px",value:"4"},"x-large":{name:"24px",value:"5"},"xx-large":{name:"32px",value:"6"},"xxx-large":{name:"48px",value:"7"}},lineHeights:["1","1.15","1.6","2","2.5","3"],pasteFilterStyle:!0,pasteIgnoreImg:!0,withCredentials:!0,uploadFileName:"file",uploadImgMaxSize:2*1024*1024,uploadImgAccept:["jpg","jpeg","png","gif","bmp"],uploadImgMaxLength:5,uploadImgTimeout:5*1e3},$e=Fe({name:"FsEditorWang",props:{modelValue:{type:String,required:!1,default:""},config:{type:Object},id:{default:"1"},uploader:{type:Object},disabled:{type:Boolean}},emits:["update:modelValue","change","ready"],data(){return{editor:null,currentValue:"",options:{}}},computed:{uniqueId(){return"fs-wang-editor-"+this.id}},watch:{modelValue:{handler(Z){Z!==this.currentValue&&(this.currentValue=Z,this.editor&&this.editor.txt.html(Z))},immediate:!0},disabled:{handler(Z){this.setDisabled(Z)},immediate:!0}},mounted(){this.init()},beforeUnmount(){this.editor.destroy(),this.editor=null},methods:{init(){let Z=null;try{Z=new ze("#"+this.uniqueId)}catch(oe){console.error(oe);return}if(Re(Z.config,Ve,Pe.wangEditor,this.config),Z.config.onchange=oe=>{this.$emit("update:modelValue",oe),this.$emit("change",oe),this.currentValue=oe},this.uploader){const oe=async(u,n)=>{var e;const t={status:"uploading",progress:0},i=c=>{t.progress=c.percent},o=c=>{t.status="error",t.message="文件上传出错:"+c.message,console.error(t.message,c)},r={file:u,fileName:u.name,onProgress:i,onError:o},f=await this.doUpload(r);let g=f==null?void 0:f.url;(e=this.uploader)!=null&&e.buildUrl&&(g=await this.uploader.buildUrl(f)),n(g)};Z.config.customUploadImg=async(u,n)=>{He(u,e=>{oe(e,n)})}}Z.create(),Z.txt.html(this.currentValue),this.editor=Z,this.setDisabled(this.disabled),this.$emit("ready",{editor:Z})},async doUpload(Z){Z.options=this.uploader;const{getUploaderImpl:oe}=Ie();let u=await oe(Z.options.type);if(u==null)throw new Error("Sorry，The component is not ready yet");return await(u==null?void 0:u.upload(Z))},setDisabled(Z=!1){this.editor&&(Z===!0?this.editor.disable():this.editor.enable())}}}),Ge={class:"fs-editor-wang"},Je=["id"];function Ke(Z,oe,u,n,e,t){return Oe(),Le("div",Ge,[De("div",{id:Z.uniqueId},null,8,Je),Qe(De("textarea",{"onUpdate:modelValue":oe[0]||(oe[0]=i=>Z.currentValue=i),class:"fs-editor-wang-preview",readonly:""},null,512),[[Ue,Z.currentValue]])])}const Xe=Be($e,[["render",Ke]]);export{Xe as default};
