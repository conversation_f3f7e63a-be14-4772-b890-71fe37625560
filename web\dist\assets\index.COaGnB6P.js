import{n as G,p as H,q as J,t as K,w as W,x as X,r as L,e as V}from"./index.BHZI5pdK.js";import{_ as Z}from"./_plugin-vue_export-helper.DlAUqK2U.js";import{h as g,g as ee,c as te,j as le,k as m,y as ae,a as b,o as p,l as e,m as q,b as T,w as t,e as s,s as d,q as i,u as x,F as D,p as S,A as se}from"./vue.BNx9QYep.js";const oe={class:"performance-analysis-container"},ne={class:"card-header"},re={class:"header-info"},de={class:"update-time"},ie={class:"filter-form"},ue={class:"filter-item"},pe={class:"filter-item"},ce={class:"filter-item"},_e={class:"filter-item"},fe={class:"filter-item"},ye={class:"filter-item"},me={class:"filter-buttons"},ve={key:0,class:"stats-section"},he={class:"stat-content"},be={class:"stat-icon"},we={class:"stat-info"},ge={class:"stat-value"},qe={class:"stat-content"},ke={class:"stat-icon"},Ve={class:"stat-info"},Te={class:"stat-value"},xe={class:"stat-content"},De={class:"stat-icon"},Ce={class:"stat-info"},Le={class:"stat-value"},Ne={class:"stat-content"},Se={class:"stat-icon"},Ue={class:"stat-info"},Ye={class:"stat-value"},$e={class:"table-header"},Fe={__name:"index",setup(ze){const z=g(!1),C=g(!1),N=g(!1),k=g([]),w=g(null),U=g([]),Y=g([]),o=ee({queryType:"monthly",year:new Date().getFullYear(),month:new Date().getMonth()+1,quarter:1,salesperson:"",region:""}),M=te(()=>new Date().toLocaleString("zh-CN")),u=r=>{if(r==null||r==="")return"0";const a=parseFloat(r);return isNaN(a)?"0":a.toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2})},R=()=>{o.queryType==="monthly"?o.month=new Date().getMonth()+1:o.queryType==="quarterly"&&(o.quarter=Math.ceil((new Date().getMonth()+1)/3))},$=async()=>{C.value=!0;try{const r=await L({url:"/api/performance-analysis/query/",method:"post",data:o});r.code===200?(k.value=r.data.list,w.value=r.data.statistics,V.success("查询成功")):V.error(r.message||"查询失败")}catch(r){console.error("查询失败:",r),V.error("查询失败，请稍后重试")}finally{C.value=!1}},B=()=>{o.queryType="monthly",o.year=new Date().getFullYear(),o.month=new Date().getMonth()+1,o.quarter=1,o.salesperson="",o.region="",k.value=[],w.value=null},Q=()=>{k.value.length>0&&$()},E=async()=>{if(!k.value.length){V.warning("没有数据可导出");return}N.value=!0;try{const r=await L({url:"/api/performance-analysis/export/",method:"post",data:o,responseType:"blob"}),a=new Blob([r],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),v=window.URL.createObjectURL(a),f=document.createElement("a");f.href=v;const c={monthly:"月度",quarterly:"季度",yearly:"年度"}[o.queryType]||"绩效";let y="";o.queryType==="monthly"?y=`${o.year}年${o.month}月`:o.queryType==="quarterly"?y=`${o.year}年Q${o.quarter}`:y=`${o.year}年`,f.download=`业务员${c}绩效分析_${y}_${new Date().toISOString().slice(0,10)}.xlsx`,document.body.appendChild(f),f.click(),document.body.removeChild(f),window.URL.revokeObjectURL(v),V.success("导出成功")}catch(r){console.error("导出失败:",r),V.error("导出失败，请稍后重试")}finally{N.value=!1}},j=async()=>{try{const r=await L({url:"/api/performance-analysis/salesperson-list/",method:"get"});r.code===200&&(U.value=r.data)}catch(r){console.error("获取业务员列表失败:",r)}},A=async()=>{try{const r=await L({url:"/api/performance-analysis/region-list/",method:"get"});r.code===200&&(Y.value=r.data)}catch(r){console.error("获取地区列表失败:",r)}};return le(()=>{j(),A()}),(r,a)=>{const v=m("el-icon"),f=m("el-button"),h=m("el-card"),c=m("el-option"),y=m("el-select"),_=m("el-col"),O=m("el-date-picker"),F=m("el-row"),n=m("el-table-column"),I=m("el-table"),P=ae("loading");return p(),b("div",oe,[e(h,{class:"header-card"},{header:t(()=>[s("div",ne,[s("div",re,[s("span",de,"更新时间: "+d(M.value),1),e(f,{type:"primary",size:"small",onClick:Q,loading:z.value},{default:t(()=>[e(v,null,{default:t(()=>[e(x(G))]),_:1}),a[6]||(a[6]=i(" 刷新数据 "))]),_:1,__:[6]},8,["loading"])])])]),default:t(()=>[a[7]||(a[7]=s("div",{class:"welcome-content"},[s("h2",null,"业务员绩效分析"),s("p",null,"统计分析业务员销售绩效，支持月度、季度、年度多维度分析")],-1))]),_:1,__:[7]}),e(h,{class:"filter-card"},{default:t(()=>[s("div",ie,[e(F,{gutter:16,style:{"align-items":"center"}},{default:t(()=>[e(_,{span:4},{default:t(()=>[s("div",ue,[a[8]||(a[8]=s("label",null,"查询类型：",-1)),e(y,{modelValue:o.queryType,"onUpdate:modelValue":a[0]||(a[0]=l=>o.queryType=l),onChange:R,style:{width:"100%"}},{default:t(()=>[e(c,{label:"月度分析",value:"monthly"}),e(c,{label:"季度分析",value:"quarterly"}),e(c,{label:"年度分析",value:"yearly"})]),_:1},8,["modelValue"])])]),_:1}),e(_,{span:3},{default:t(()=>[s("div",pe,[a[9]||(a[9]=s("label",null,"年份：",-1)),e(O,{modelValue:o.year,"onUpdate:modelValue":a[1]||(a[1]=l=>o.year=l),type:"year",placeholder:"选择年份",format:"YYYY","value-format":"YYYY",style:{width:"100%"}},null,8,["modelValue"])])]),_:1}),o.queryType==="monthly"?(p(),T(_,{key:0,span:3},{default:t(()=>[s("div",ce,[a[10]||(a[10]=s("label",null,"月份：",-1)),e(y,{modelValue:o.month,"onUpdate:modelValue":a[2]||(a[2]=l=>o.month=l),style:{width:"100%"}},{default:t(()=>[(p(),b(D,null,S(12,l=>e(c,{key:l,label:`${l}月`,value:l},null,8,["label","value"])),64))]),_:1},8,["modelValue"])])]),_:1})):q("",!0),o.queryType==="quarterly"?(p(),T(_,{key:1,span:3},{default:t(()=>[s("div",_e,[a[11]||(a[11]=s("label",null,"季度：",-1)),e(y,{modelValue:o.quarter,"onUpdate:modelValue":a[3]||(a[3]=l=>o.quarter=l),style:{width:"100%"}},{default:t(()=>[e(c,{label:"第一季度",value:1}),e(c,{label:"第二季度",value:2}),e(c,{label:"第三季度",value:3}),e(c,{label:"第四季度",value:4})]),_:1},8,["modelValue"])])]),_:1})):q("",!0),e(_,{span:4},{default:t(()=>[s("div",fe,[a[12]||(a[12]=s("label",null,"业务员：",-1)),e(y,{modelValue:o.salesperson,"onUpdate:modelValue":a[4]||(a[4]=l=>o.salesperson=l),clearable:"",placeholder:"全部业务员",style:{width:"100%"}},{default:t(()=>[(p(!0),b(D,null,S(U.value,l=>(p(),T(c,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])])]),_:1}),e(_,{span:4},{default:t(()=>[s("div",ye,[a[13]||(a[13]=s("label",null,"地区：",-1)),e(y,{modelValue:o.region,"onUpdate:modelValue":a[5]||(a[5]=l=>o.region=l),clearable:"",placeholder:"全部地区",style:{width:"100%"}},{default:t(()=>[(p(!0),b(D,null,S(Y.value,l=>(p(),T(c,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])])]),_:1}),e(_,{span:3},{default:t(()=>[s("div",me,[e(f,{type:"primary",onClick:$,loading:C.value},{default:t(()=>a[14]||(a[14]=[i(" 查询绩效 ")])),_:1,__:[14]},8,["loading"]),e(f,{onClick:B},{default:t(()=>a[15]||(a[15]=[i("重置")])),_:1,__:[15]})])]),_:1})]),_:1})])]),_:1}),w.value?(p(),b("div",ve,[e(F,{gutter:20},{default:t(()=>[e(_,{span:6},{default:t(()=>[e(h,{class:"stat-card",shadow:"hover"},{default:t(()=>[s("div",he,[s("div",be,[e(v,{size:"32"},{default:t(()=>[e(x(H))]),_:1})]),s("div",we,[a[16]||(a[16]=s("div",{class:"stat-title"},"业务员总数",-1)),s("div",ge,d(w.value.totalSalespersons),1)])])]),_:1})]),_:1}),e(_,{span:6},{default:t(()=>[e(h,{class:"stat-card",shadow:"hover"},{default:t(()=>[s("div",qe,[s("div",ke,[e(v,{size:"32"},{default:t(()=>[e(x(J))]),_:1})]),s("div",Ve,[a[17]||(a[17]=s("div",{class:"stat-title"},"总销售金额",-1)),s("div",Te,d(u(w.value.totalSalesAmount)),1)])])]),_:1})]),_:1}),e(_,{span:6},{default:t(()=>[e(h,{class:"stat-card",shadow:"hover"},{default:t(()=>[s("div",xe,[s("div",De,[e(v,{size:"32"},{default:t(()=>[e(x(K))]),_:1})]),s("div",Ce,[a[18]||(a[18]=s("div",{class:"stat-title"},"总销售数量",-1)),s("div",Le,d(u(w.value.totalSalesQuantity)),1)])])]),_:1})]),_:1}),e(_,{span:6},{default:t(()=>[e(h,{class:"stat-card",shadow:"hover"},{default:t(()=>[s("div",Ne,[s("div",Se,[e(v,{size:"32"},{default:t(()=>[e(x(W))]),_:1})]),s("div",Ue,[a[19]||(a[19]=s("div",{class:"stat-title"},"人均销售金额",-1)),s("div",Ye,d(u(w.value.averageSalesAmount)),1)])])]),_:1})]),_:1})]),_:1})])):q("",!0),k.value.length>0?(p(),T(h,{key:1,class:"table-card"},{header:t(()=>[s("div",$e,[a[21]||(a[21]=s("h3",null,"绩效分析结果",-1)),e(f,{type:"success",onClick:E,loading:N.value},{default:t(()=>[e(v,null,{default:t(()=>[e(x(X))]),_:1}),a[20]||(a[20]=i(" 导出Excel "))]),_:1,__:[20]},8,["loading"])])]),default:t(()=>[se((p(),T(I,{data:k.value,border:"",stripe:"",style:{width:"100%"},"max-height":600},{default:t(()=>[e(n,{prop:"业务员",label:"业务员",width:"120",fixed:"left"}),e(n,{prop:"负责地区",label:"负责地区",width:"120"}),o.queryType==="monthly"?(p(),b(D,{key:0},[e(n,{prop:"订单数量",label:"订单数量",width:"100"}),e(n,{prop:"客户数量",label:"客户数量",width:"100"}),e(n,{prop:"产品种类",label:"产品种类",width:"100"}),e(n,{prop:"总销量",label:"总销量",width:"120"},{default:t(({row:l})=>[i(d(u(l.总销量)),1)]),_:1}),e(n,{prop:"总销售金额",label:"总销售金额",width:"140"},{default:t(({row:l})=>[i(d(u(l.总销售金额)),1)]),_:1}),e(n,{prop:"平均单价",label:"平均单价",width:"120"},{default:t(({row:l})=>[i(d(u(l.平均单价)),1)]),_:1}),e(n,{prop:"客户平均贡献",label:"客户平均贡献",width:"140"},{default:t(({row:l})=>[i(d(u(l.客户平均贡献)),1)]),_:1}),e(n,{prop:"单均销量",label:"单均销量",width:"120"},{default:t(({row:l})=>[i(d(u(l.单均销量)),1)]),_:1}),e(n,{prop:"统计月份",label:"统计月份",width:"120"})],64)):q("",!0),o.queryType==="quarterly"?(p(),b(D,{key:1},[e(n,{prop:"季度",label:"季度",width:"80"}),e(n,{prop:"季度订单数",label:"季度订单数",width:"120"}),e(n,{prop:"季度客户数",label:"季度客户数",width:"120"}),e(n,{prop:"季度总销量",label:"季度总销量",width:"140"},{default:t(({row:l})=>[i(d(u(l.季度总销量)),1)]),_:1}),e(n,{prop:"季度总销售金额",label:"季度总销售金额",width:"160"},{default:t(({row:l})=>[i(d(u(l.季度总销售金额)),1)]),_:1}),e(n,{prop:"季度平均单价",label:"季度平均单价",width:"140"},{default:t(({row:l})=>[i(d(u(l.季度平均单价)),1)]),_:1}),e(n,{prop:"月均销售金额",label:"月均销售金额",width:"140"},{default:t(({row:l})=>[i(d(u(l.月均销售金额)),1)]),_:1}),e(n,{prop:"统计年份",label:"统计年份",width:"120"})],64)):q("",!0),o.queryType==="yearly"?(p(),b(D,{key:2},[e(n,{prop:"年度订单数",label:"年度订单数",width:"120"}),e(n,{prop:"年度客户数",label:"年度客户数",width:"120"}),e(n,{prop:"年度产品种类",label:"年度产品种类",width:"140"}),e(n,{prop:"年度总销量",label:"年度总销量",width:"140"},{default:t(({row:l})=>[i(d(u(l.年度总销量)),1)]),_:1}),e(n,{prop:"年度总销售金额",label:"年度总销售金额",width:"160"},{default:t(({row:l})=>[i(d(u(l.年度总销售金额)),1)]),_:1}),e(n,{prop:"年度平均单价",label:"年度平均单价",width:"140"},{default:t(({row:l})=>[i(d(u(l.年度平均单价)),1)]),_:1}),e(n,{prop:"月均销售金额",label:"月均销售金额",width:"140"},{default:t(({row:l})=>[i(d(u(l.月均销售金额)),1)]),_:1}),e(n,{prop:"月均销量",label:"月均销量",width:"120"},{default:t(({row:l})=>[i(d(u(l.月均销量)),1)]),_:1}),e(n,{prop:"销售金额排名",label:"销售金额排名",width:"140"}),e(n,{prop:"销量排名",label:"销量排名",width:"120"}),e(n,{prop:"统计年份",label:"统计年份",width:"120"})],64)):q("",!0)]),_:1},8,["data"])),[[P,C.value]])]),_:1})):q("",!0)])}}},Qe=Z(Fe,[["__scopeId","data-v-f4e8bd75"]]);export{Qe as default};
