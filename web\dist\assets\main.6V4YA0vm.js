const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/parent.W0n6eI2_.js","assets/index.BHZI5pdK.js","assets/vue.BNx9QYep.js","assets/index.Dg-OhEXY.css","assets/index.CpK7cMYn.js","assets/_plugin-vue_export-helper.DlAUqK2U.js","assets/index.DPSUD4VB.css"])))=>i.map(i=>d[i]);
import{T as M,u as H,Q as L,_ as c}from"./index.BHZI5pdK.js";import{d as f,h as P,Q as E,M as u,c as a,j as A,k as o,b as i,o as _,w as m,l as t,m as B,u as p,P as d,n as I}from"./vue.BNx9QYep.js";const N=f({name:"layoutMain"}),Q=f({...N,setup(S,{expose:y}){const v=d(()=>c(()=>import("./parent.W0n6eI2_.js"),__vite__mapDeps([0,1,2,3]))),g=d(()=>c(()=>import("./index.CpK7cMYn.js"),__vite__mapDeps([4,2,5,6]))),s=P(),x=E(),b=M(),h=H(),{themeConfig:e}=u(h),{isTagsViewCurrenFull:k}=u(b),w=a(()=>e.value.isFooter&&!x.meta.isIframe),C=a(()=>e.value.isFixedHeader),T=a(()=>e.value.isFixedHeader?".layout-backtop-header-fixed .el-scrollbar__wrap":".layout-backtop .el-scrollbar__wrap"),r=a(()=>{if(k.value)return"0px";const{isTagsview:l,layout:n}=e.value;return l&&n!=="classic"?"85px":"51px"});return A(()=>{L.done(600)}),y({layoutMainScrollbarRef:s}),(l,n)=>{const R=o("el-scrollbar"),V=o("el-backtop"),F=o("el-main");return _(),i(F,{class:"layout-main",style:I(C.value?`height: calc(100% - ${r.value})`:`minHeight: calc(100% - ${r.value})`)},{default:m(()=>[t(R,{ref_key:"layoutMainScrollbarRef",ref:s,class:"layout-main-scroll layout-backtop-header-fixed","wrap-class":"layout-main-scroll","view-class":"layout-main-scroll"},{default:m(()=>[t(p(v)),w.value?(_(),i(p(g),{key:0})):B("",!0)]),_:1},512),t(V,{target:T.value},null,8,["target"])]),_:1},8,["style"])}}});export{Q as default};
