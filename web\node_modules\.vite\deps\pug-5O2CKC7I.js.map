{"version": 3, "sources": ["../../.pnpm/monaco-editor@0.52.2/node_modules/monaco-editor/esm/vs/basic-languages/pug/pug.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/pug/pug.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: \"{\", close: \"}\", notIn: [\"string\", \"comment\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\", \"comment\"] },\n    { open: \"(\", close: \")\", notIn: [\"string\", \"comment\"] }\n  ],\n  folding: {\n    offSide: true\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".pug\",\n  ignoreCase: true,\n  brackets: [\n    { token: \"delimiter.curly\", open: \"{\", close: \"}\" },\n    { token: \"delimiter.array\", open: \"[\", close: \"]\" },\n    { token: \"delimiter.parenthesis\", open: \"(\", close: \")\" }\n  ],\n  keywords: [\n    \"append\",\n    \"block\",\n    \"case\",\n    \"default\",\n    \"doctype\",\n    \"each\",\n    \"else\",\n    \"extends\",\n    \"for\",\n    \"if\",\n    \"in\",\n    \"include\",\n    \"mixin\",\n    \"typeof\",\n    \"unless\",\n    \"var\",\n    \"when\"\n  ],\n  tags: [\n    \"a\",\n    \"abbr\",\n    \"acronym\",\n    \"address\",\n    \"area\",\n    \"article\",\n    \"aside\",\n    \"audio\",\n    \"b\",\n    \"base\",\n    \"basefont\",\n    \"bdi\",\n    \"bdo\",\n    \"blockquote\",\n    \"body\",\n    \"br\",\n    \"button\",\n    \"canvas\",\n    \"caption\",\n    \"center\",\n    \"cite\",\n    \"code\",\n    \"col\",\n    \"colgroup\",\n    \"command\",\n    \"datalist\",\n    \"dd\",\n    \"del\",\n    \"details\",\n    \"dfn\",\n    \"div\",\n    \"dl\",\n    \"dt\",\n    \"em\",\n    \"embed\",\n    \"fieldset\",\n    \"figcaption\",\n    \"figure\",\n    \"font\",\n    \"footer\",\n    \"form\",\n    \"frame\",\n    \"frameset\",\n    \"h1\",\n    \"h2\",\n    \"h3\",\n    \"h4\",\n    \"h5\",\n    \"h6\",\n    \"head\",\n    \"header\",\n    \"hgroup\",\n    \"hr\",\n    \"html\",\n    \"i\",\n    \"iframe\",\n    \"img\",\n    \"input\",\n    \"ins\",\n    \"keygen\",\n    \"kbd\",\n    \"label\",\n    \"li\",\n    \"link\",\n    \"map\",\n    \"mark\",\n    \"menu\",\n    \"meta\",\n    \"meter\",\n    \"nav\",\n    \"noframes\",\n    \"noscript\",\n    \"object\",\n    \"ol\",\n    \"optgroup\",\n    \"option\",\n    \"output\",\n    \"p\",\n    \"param\",\n    \"pre\",\n    \"progress\",\n    \"q\",\n    \"rp\",\n    \"rt\",\n    \"ruby\",\n    \"s\",\n    \"samp\",\n    \"script\",\n    \"section\",\n    \"select\",\n    \"small\",\n    \"source\",\n    \"span\",\n    \"strike\",\n    \"strong\",\n    \"style\",\n    \"sub\",\n    \"summary\",\n    \"sup\",\n    \"table\",\n    \"tbody\",\n    \"td\",\n    \"textarea\",\n    \"tfoot\",\n    \"th\",\n    \"thead\",\n    \"time\",\n    \"title\",\n    \"tr\",\n    \"tracks\",\n    \"tt\",\n    \"u\",\n    \"ul\",\n    \"video\",\n    \"wbr\"\n  ],\n  // we include these common regular expressions\n  symbols: /[\\+\\-\\*\\%\\&\\|\\!\\=\\/\\.\\,\\:]+/,\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  tokenizer: {\n    root: [\n      // Tag or a keyword at start\n      [\n        /^(\\s*)([a-zA-Z_-][\\w-]*)/,\n        {\n          cases: {\n            \"$2@tags\": {\n              cases: {\n                \"@eos\": [\"\", \"tag\"],\n                \"@default\": [\"\", { token: \"tag\", next: \"@tag.$1\" }]\n              }\n            },\n            \"$2@keywords\": [\"\", { token: \"keyword.$2\" }],\n            \"@default\": [\"\", \"\"]\n          }\n        }\n      ],\n      // id\n      [\n        /^(\\s*)(#[a-zA-Z_-][\\w-]*)/,\n        {\n          cases: {\n            \"@eos\": [\"\", \"tag.id\"],\n            \"@default\": [\"\", { token: \"tag.id\", next: \"@tag.$1\" }]\n          }\n        }\n      ],\n      // class\n      [\n        /^(\\s*)(\\.[a-zA-Z_-][\\w-]*)/,\n        {\n          cases: {\n            \"@eos\": [\"\", \"tag.class\"],\n            \"@default\": [\"\", { token: \"tag.class\", next: \"@tag.$1\" }]\n          }\n        }\n      ],\n      // plain text with pipe\n      [/^(\\s*)(\\|.*)$/, \"\"],\n      { include: \"@whitespace\" },\n      // keywords\n      [\n        /[a-zA-Z_$][\\w$]*/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // delimiters and operators\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/@symbols/, \"delimiter\"],\n      // numbers\n      [/\\d+\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float\"],\n      [/\\d+/, \"number\"],\n      // strings:\n      [/\"/, \"string\", '@string.\"'],\n      [/'/, \"string\", \"@string.'\"]\n    ],\n    tag: [\n      [/(\\.)(\\s*$)/, [{ token: \"delimiter\", next: \"@blockText.$S2.\" }, \"\"]],\n      [/\\s+/, { token: \"\", next: \"@simpleText\" }],\n      // id\n      [\n        /#[a-zA-Z_-][\\w-]*/,\n        {\n          cases: {\n            \"@eos\": { token: \"tag.id\", next: \"@pop\" },\n            \"@default\": \"tag.id\"\n          }\n        }\n      ],\n      // class\n      [\n        /\\.[a-zA-Z_-][\\w-]*/,\n        {\n          cases: {\n            \"@eos\": { token: \"tag.class\", next: \"@pop\" },\n            \"@default\": \"tag.class\"\n          }\n        }\n      ],\n      // attributes\n      [/\\(/, { token: \"delimiter.parenthesis\", next: \"@attributeList\" }]\n    ],\n    simpleText: [\n      [/[^#]+$/, { token: \"\", next: \"@popall\" }],\n      [/[^#]+/, { token: \"\" }],\n      // interpolation\n      [\n        /(#{)([^}]*)(})/,\n        {\n          cases: {\n            \"@eos\": [\n              \"interpolation.delimiter\",\n              \"interpolation\",\n              {\n                token: \"interpolation.delimiter\",\n                next: \"@popall\"\n              }\n            ],\n            \"@default\": [\"interpolation.delimiter\", \"interpolation\", \"interpolation.delimiter\"]\n          }\n        }\n      ],\n      [/#$/, { token: \"\", next: \"@popall\" }],\n      [/#/, \"\"]\n    ],\n    attributeList: [\n      [/\\s+/, \"\"],\n      [\n        /(\\w+)(\\s*=\\s*)(\"|')/,\n        [\"attribute.name\", \"delimiter\", { token: \"attribute.value\", next: \"@value.$3\" }]\n      ],\n      [/\\w+/, \"attribute.name\"],\n      [\n        /,/,\n        {\n          cases: {\n            \"@eos\": {\n              token: \"attribute.delimiter\",\n              next: \"@popall\"\n            },\n            \"@default\": \"attribute.delimiter\"\n          }\n        }\n      ],\n      [/\\)$/, { token: \"delimiter.parenthesis\", next: \"@popall\" }],\n      [/\\)/, { token: \"delimiter.parenthesis\", next: \"@pop\" }]\n    ],\n    whitespace: [\n      [/^(\\s*)(\\/\\/.*)$/, { token: \"comment\", next: \"@blockText.$1.comment\" }],\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/<!--/, { token: \"comment\", next: \"@comment\" }]\n    ],\n    blockText: [\n      [\n        /^\\s+.*$/,\n        {\n          cases: {\n            \"($S2\\\\s+.*$)\": { token: \"$S3\" },\n            \"@default\": { token: \"@rematch\", next: \"@popall\" }\n          }\n        }\n      ],\n      [/./, { token: \"@rematch\", next: \"@popall\" }]\n    ],\n    comment: [\n      [/[^<\\-]+/, \"comment.content\"],\n      [/-->/, { token: \"comment\", next: \"@pop\" }],\n      [/<!--/, \"comment.content.invalid\"],\n      [/[<\\-]/, \"comment.content\"]\n    ],\n    string: [\n      [\n        /[^\\\\\"'#]+/,\n        {\n          cases: {\n            \"@eos\": { token: \"string\", next: \"@popall\" },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      [\n        /@escapes/,\n        {\n          cases: {\n            \"@eos\": { token: \"string.escape\", next: \"@popall\" },\n            \"@default\": \"string.escape\"\n          }\n        }\n      ],\n      [\n        /\\\\./,\n        {\n          cases: {\n            \"@eos\": {\n              token: \"string.escape.invalid\",\n              next: \"@popall\"\n            },\n            \"@default\": \"string.escape.invalid\"\n          }\n        }\n      ],\n      // interpolation\n      [/(#{)([^}]*)(})/, [\"interpolation.delimiter\", \"interpolation\", \"interpolation.delimiter\"]],\n      [/#/, \"string\"],\n      [\n        /[\"']/,\n        {\n          cases: {\n            \"$#==$S2\": { token: \"string\", next: \"@pop\" },\n            \"@default\": { token: \"string\" }\n          }\n        }\n      ]\n    ],\n    // Almost identical to above, except for escapes and the output token\n    value: [\n      [\n        /[^\\\\\"']+/,\n        {\n          cases: {\n            \"@eos\": { token: \"attribute.value\", next: \"@popall\" },\n            \"@default\": \"attribute.value\"\n          }\n        }\n      ],\n      [\n        /\\\\./,\n        {\n          cases: {\n            \"@eos\": { token: \"attribute.value\", next: \"@popall\" },\n            \"@default\": \"attribute.value\"\n          }\n        }\n      ],\n      [\n        /[\"']/,\n        {\n          cases: {\n            \"$#==$S2\": { token: \"attribute.value\", next: \"@pop\" },\n            \"@default\": { token: \"attribute.value\" }\n          }\n        }\n      ]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AASA,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,EACxD;AAAA,EACA,SAAS;AAAA,IACP,SAAS;AAAA,EACX;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,EAAE,OAAO,mBAAmB,MAAM,KAAK,OAAO,IAAI;AAAA,IAClD,EAAE,OAAO,mBAAmB,MAAM,KAAK,OAAO,IAAI;AAAA,IAClD,EAAE,OAAO,yBAAyB,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1D;AAAA,EACA,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA;AAAA,EAEA,SAAS;AAAA,EACT,SAAS;AAAA,EACT,WAAW;AAAA,IACT,MAAM;AAAA;AAAA,MAEJ;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,WAAW;AAAA,cACT,OAAO;AAAA,gBACL,QAAQ,CAAC,IAAI,KAAK;AAAA,gBAClB,YAAY,CAAC,IAAI,EAAE,OAAO,OAAO,MAAM,UAAU,CAAC;AAAA,cACpD;AAAA,YACF;AAAA,YACA,eAAe,CAAC,IAAI,EAAE,OAAO,aAAa,CAAC;AAAA,YAC3C,YAAY,CAAC,IAAI,EAAE;AAAA,UACrB;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAEA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ,CAAC,IAAI,QAAQ;AAAA,YACrB,YAAY,CAAC,IAAI,EAAE,OAAO,UAAU,MAAM,UAAU,CAAC;AAAA,UACvD;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAEA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ,CAAC,IAAI,WAAW;AAAA,YACxB,YAAY,CAAC,IAAI,EAAE,OAAO,aAAa,MAAM,UAAU,CAAC;AAAA,UAC1D;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAEA,CAAC,iBAAiB,EAAE;AAAA,MACpB,EAAE,SAAS,cAAc;AAAA;AAAA,MAEzB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,aAAa,EAAE,OAAO,aAAa;AAAA,YACnC,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAEA,CAAC,cAAc,WAAW;AAAA,MAC1B,CAAC,YAAY,WAAW;AAAA;AAAA,MAExB,CAAC,4BAA4B,cAAc;AAAA,MAC3C,CAAC,OAAO,QAAQ;AAAA;AAAA,MAEhB,CAAC,KAAK,UAAU,WAAW;AAAA,MAC3B,CAAC,KAAK,UAAU,WAAW;AAAA,IAC7B;AAAA,IACA,KAAK;AAAA,MACH,CAAC,cAAc,CAAC,EAAE,OAAO,aAAa,MAAM,kBAAkB,GAAG,EAAE,CAAC;AAAA,MACpE,CAAC,OAAO,EAAE,OAAO,IAAI,MAAM,cAAc,CAAC;AAAA;AAAA,MAE1C;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ,EAAE,OAAO,UAAU,MAAM,OAAO;AAAA,YACxC,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAEA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ,EAAE,OAAO,aAAa,MAAM,OAAO;AAAA,YAC3C,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAEA,CAAC,MAAM,EAAE,OAAO,yBAAyB,MAAM,iBAAiB,CAAC;AAAA,IACnE;AAAA,IACA,YAAY;AAAA,MACV,CAAC,UAAU,EAAE,OAAO,IAAI,MAAM,UAAU,CAAC;AAAA,MACzC,CAAC,SAAS,EAAE,OAAO,GAAG,CAAC;AAAA;AAAA,MAEvB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ;AAAA,cACN;AAAA,cACA;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,gBACP,MAAM;AAAA,cACR;AAAA,YACF;AAAA,YACA,YAAY,CAAC,2BAA2B,iBAAiB,yBAAyB;AAAA,UACpF;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,MAAM,EAAE,OAAO,IAAI,MAAM,UAAU,CAAC;AAAA,MACrC,CAAC,KAAK,EAAE;AAAA,IACV;AAAA,IACA,eAAe;AAAA,MACb,CAAC,OAAO,EAAE;AAAA,MACV;AAAA,QACE;AAAA,QACA,CAAC,kBAAkB,aAAa,EAAE,OAAO,mBAAmB,MAAM,YAAY,CAAC;AAAA,MACjF;AAAA,MACA,CAAC,OAAO,gBAAgB;AAAA,MACxB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ;AAAA,cACN,OAAO;AAAA,cACP,MAAM;AAAA,YACR;AAAA,YACA,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,OAAO,EAAE,OAAO,yBAAyB,MAAM,UAAU,CAAC;AAAA,MAC3D,CAAC,MAAM,EAAE,OAAO,yBAAyB,MAAM,OAAO,CAAC;AAAA,IACzD;AAAA,IACA,YAAY;AAAA,MACV,CAAC,mBAAmB,EAAE,OAAO,WAAW,MAAM,wBAAwB,CAAC;AAAA,MACvE,CAAC,cAAc,EAAE;AAAA,MACjB,CAAC,QAAQ,EAAE,OAAO,WAAW,MAAM,WAAW,CAAC;AAAA,IACjD;AAAA,IACA,WAAW;AAAA,MACT;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,gBAAgB,EAAE,OAAO,MAAM;AAAA,YAC/B,YAAY,EAAE,OAAO,YAAY,MAAM,UAAU;AAAA,UACnD;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,KAAK,EAAE,OAAO,YAAY,MAAM,UAAU,CAAC;AAAA,IAC9C;AAAA,IACA,SAAS;AAAA,MACP,CAAC,WAAW,iBAAiB;AAAA,MAC7B,CAAC,OAAO,EAAE,OAAO,WAAW,MAAM,OAAO,CAAC;AAAA,MAC1C,CAAC,QAAQ,yBAAyB;AAAA,MAClC,CAAC,SAAS,iBAAiB;AAAA,IAC7B;AAAA,IACA,QAAQ;AAAA,MACN;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ,EAAE,OAAO,UAAU,MAAM,UAAU;AAAA,YAC3C,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ,EAAE,OAAO,iBAAiB,MAAM,UAAU;AAAA,YAClD,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ;AAAA,cACN,OAAO;AAAA,cACP,MAAM;AAAA,YACR;AAAA,YACA,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAEA,CAAC,kBAAkB,CAAC,2BAA2B,iBAAiB,yBAAyB,CAAC;AAAA,MAC1F,CAAC,KAAK,QAAQ;AAAA,MACd;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,WAAW,EAAE,OAAO,UAAU,MAAM,OAAO;AAAA,YAC3C,YAAY,EAAE,OAAO,SAAS;AAAA,UAChC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA;AAAA,IAEA,OAAO;AAAA,MACL;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ,EAAE,OAAO,mBAAmB,MAAM,UAAU;AAAA,YACpD,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ,EAAE,OAAO,mBAAmB,MAAM,UAAU;AAAA,YACpD,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,WAAW,EAAE,OAAO,mBAAmB,MAAM,OAAO;AAAA,YACpD,YAAY,EAAE,OAAO,kBAAkB;AAAA,UACzC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;", "names": []}