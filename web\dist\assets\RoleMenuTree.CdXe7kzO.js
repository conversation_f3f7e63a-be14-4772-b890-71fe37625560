import{R as f,a as p,s as R,b as M}from"./api.CRWbL0zW.js";import{R as k}from"./RoleMenuTreeStores.BHTvmwPg.js";import{R as _}from"./RoleMenuBtnStores.WLEOorzd.js";import{R as g,a as C}from"./RoleMenuFieldStores.CgsZ7drj.js";import{X as y,e as S}from"./index.BHZI5pdK.js";import{d as I,h as c,j as v,k as T,b,o as w}from"./vue.BNx9QYep.js";const j=I({__name:"RoleMenuTree",setup(B){const o=f(),d=k(),s=_(),l=g(),u=C(),a=c([]),r=c([]),i={children:"children",label:"name",value:"id"},m=(e,t)=>{R({roleId:o.roleId,menuId:e.id,isCheck:t}).then(n=>{S({message:n.msg,type:"success"})})},h=async e=>{if(e.is_catalog)s.setState([]),l.setState([]);else{d.setRoleMenuTree(e);const{data:t}=await M({roleId:o.roleId,menuId:e.id});s.setState(t.menu_btn),l.setState(t.menu_field)}u.$reset()};return v(async()=>{a.value=await p({roleId:o.roleId}),r.value=y.toTreeArray(a.value).filter(e=>e.isCheck).map(e=>e.id)}),(e,t)=>{const n=T("el-tree");return w(),b(n,{ref:"treeRef",data:a.value,props:i,"default-checked-keys":r.value,onCheckChange:m,onNodeClick:h,"node-key":"id","check-strictly":"","highlight-current":"","show-checkbox":"","default-expand-all":""},null,8,["data","default-checked-keys"])}}});export{j as default};
