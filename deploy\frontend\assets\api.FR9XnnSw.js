import{r as e}from"./index.BHZI5pdK.js";const r="/api/system/system_config/";function a(t){return e({url:r,method:"get",params:t})}function s(t){return e({url:r,method:"post",data:t})}function u(t){return e({url:r+t.id+"/",method:"put",data:t})}function o(t){return e({url:r+t+"/",method:"delete",data:{id:t}})}function d(t){return e({url:r+"save_content/",method:"put",data:t})}export{s as A,o as D,a as G,u as U,d as s};
