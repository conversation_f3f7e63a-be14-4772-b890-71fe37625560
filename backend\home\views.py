import json
import logging
import base64
import hashlib
import hmac
import time
import random
import string
from django.conf import settings
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import redirect
from django.contrib.auth import login
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from rest_framework.views import APIView
from rest_framework.permissions import AllowAny
from rest_framework_simplejwt.tokens import RefreshToken
from dvadmin.system.models import Users
from dvadmin.utils.json_response import DetailResponse, ErrorResponse

# 获取logger
logger = logging.getLogger(__name__)

# 添加一个简单的视图函数来处理/web/路径
@method_decorator(csrf_exempt, name='dispatch')
class WebIndexView(APIView):
    """
    处理/web/路径的请求
    """
    permission_classes = [AllowAny]
    authentication_classes = []  # 禁用认证

    def get(self, request):
        """
        重定向到前端首页
        """
        # 重定向到前端首页
        return redirect('http://************:8080/#/home')

# SSO配置
SSO_SECRET_KEY = getattr(settings, 'SSO_SECRET_KEY', 'default_sso_secret_key')
ENCRYPTION_KEY = getattr(settings, 'ENCRYPTION_KEY', 'default_encryption_key')

@method_decorator(csrf_exempt, name='dispatch')
class SSOLoginView(APIView):
    """
    SSO登录接口
    """
    permission_classes = [AllowAny]
    authentication_classes = []  # 禁用认证

    def get(self, request):
        """
        处理SSO登录请求
        """
        try:
            # 1. 获取请求参数
            username = request.GET.get('username')
            timestamp = request.GET.get('timestamp')
            nonce = request.GET.get('nonce')
            token = request.GET.get('token')
            encrypted_data = request.GET.get('data')
            return_url = request.GET.get('returnUrl', '/web/')

            # 记录请求信息
            logger.info(f"SSO登录请求: username={username}, timestamp={timestamp}, nonce={nonce}, token={token[:10] if token else None}..., return_url={return_url}")

            # 2. 验证请求来源
            referer = request.META.get('HTTP_REFERER', '')
            if not self.validate_direct_access(referer):
                logger.error(f"非法的请求来源: {referer}")
                return HttpResponseRedirect(f"/web/#/error?message=非法的请求来源")

            # 3. 验证参数
            if not all([username, timestamp, nonce, token]):
                logger.error("SSO登录参数不完整")
                return HttpResponseRedirect(f"/web/#/error?message=缺少必要的SSO参数")

            # 4. 验证时间戳是否有效（防止重放攻击）
            try:
                current_time = int(time.time())
                if abs(current_time - int(timestamp)) > 120:  # 2分钟有效期
                    logger.error(f"SSO登录时间戳过期: {timestamp}, 当前时间: {current_time}")
                    return HttpResponseRedirect(f"/web/#/error?message=SSO登录链接已过期")
            except ValueError:
                logger.error(f"无效的时间戳格式: {timestamp}")
                return HttpResponseRedirect(f"/web/#/error?message=无效的时间戳格式")

            # 5. 验证token
            if not self.validate_sso_token(username, token, timestamp, nonce):
                logger.error("SSO登录token验证失败")
                return HttpResponseRedirect(f"/web/#/error?message=无效的SSO令牌")

            # 6. 解密用户信息
            user_info = self.decrypt_user_info(encrypted_data)
            if user_info is None:
                logger.error("用户信息解析失败")
                return HttpResponseRedirect(f"/web/#/error?message=用户信息解析失败")

            # 7. 查找或创建用户
            try:
                user = Users.objects.filter(username=username).first()
                if not user:
                    # 创建新用户
                    logger.info(f"用户不存在，创建新用户: {username}")

                    # 生成随机密码
                    password = ''.join(random.choices(string.ascii_letters + string.digits, k=12))

                    # 创建用户
                    user = Users.objects.create(
                        username=username,
                        name=user_info.get('DisplayName', username),
                        email=user_info.get('Email', ''),
                        mobile=user_info.get('Mobile', ''),
                        is_active=True,
                        user_type=1,  # 前台用户
                        pwd_change_count=1  # 设置为1，避免首次登录修改密码
                    )
                    # 设置密码
                    user.set_password(password)
                    user.save()
                    logger.info(f"新用户创建成功: {username}")
                else:
                    # 更新用户信息
                    logger.info(f"用户已存在，更新用户信息: {username}")
                    user.name = user_info.get('DisplayName', user.name)
                    user.email = user_info.get('Email', user.email)
                    user.mobile = user_info.get('Mobile', user.mobile)
                    user.save()

                # 8. 检查用户状态
                if not user.is_active:
                    logger.error(f"用户已被禁用: {username}")
                    return HttpResponseRedirect(f"/web/#/error?message=账号已被禁用，请联系管理员")

                # 9. 生成JWT令牌
                refresh = RefreshToken.for_user(user)
                access_token = str(refresh.access_token)

                # 10. 构建登录成功HTML页面
                is_persistent = user_info.get('IsPersistent', True)
                # 使用固定的前端URL，而不是使用return_url参数
                frontend_url = "http://************:8080/#/home"
                html = f"""
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>登录成功，正在跳转...</title>
                    <script>
                        // 内联js-cookie库的核心功能，避免使用CDN
                        // 简单的Cookie操作函数
                        var Cookies = {{
                            set: function(name, value, options) {{
                                options = options || {{}};
                                var expires = options.expires;

                                if (typeof expires === 'number') {{
                                    var days = expires;
                                    expires = new Date();
                                    expires.setDate(expires.getDate() + days);
                                }}

                                document.cookie = name + '=' + encodeURIComponent(value) +
                                    (expires ? '; expires=' + expires.toUTCString() : '') +
                                    '; path=' + (options.path || '/');
                            }},

                            get: function(name) {{
                                var nameEQ = name + '=';
                                var ca = document.cookie.split(';');
                                for(var i=0; i < ca.length; i++) {{
                                    var c = ca[i];
                                    while (c.charAt(0) === ' ') c = c.substring(1, c.length);
                                    if (c.indexOf(nameEQ) === 0) return decodeURIComponent(c.substring(nameEQ.length, c.length));
                                }}
                                return null;
                            }},

                            remove: function(name, options) {{
                                options = options || {{}};
                                options.expires = -1;
                                this.set(name, '', options);
                            }}
                        }};

                        function clearAuthInfo() {{
                            // 清除token (使用Cookies而不是localStorage)
                            Cookies.remove('token');
                            // 同时清除sessionStorage
                            sessionStorage.clear();
                        }}

                        function setAuthInfo(token, username) {{
                            // 使用Cookies存储token (与前端一致)
                            Cookies.set('token', token);
                            // 存储用户名
                            Cookies.set('username', username);

                            // 同时也设置localStorage，以防前端使用localStorage
                            try {{
                                localStorage.setItem('token', token);
                                localStorage.setItem('username', username);
                            }} catch(e) {{
                                console.log('无法设置localStorage');
                            }}
                        }}

                        function redirectToHome() {{
                            // 等待一下确保cookie设置完成
                            setTimeout(function() {{
                                window.location.href = '{frontend_url}';
                            }}, 300);
                        }}

                        // 执行登录流程
                        window.onload = function() {{
                            clearAuthInfo();
                            setAuthInfo('{access_token}', '{username}');
                            console.log('Token已设置:', '{access_token}');
                            redirectToHome();
                        }};
                    </script>
                </head>
                <body>
                    <h3>登录成功，正在跳转...</h3>
                    <p>如果页面没有自动跳转，请<a href="{frontend_url}">点击这里</a>。</p>
                </body>
                </html>
                """

                # 11. 返回HTML响应
                return HttpResponse(html, content_type='text/html')

            except Exception as e:
                logger.error(f"处理用户时出错: {str(e)}")
                return HttpResponseRedirect(f"/web/#/error?message=SSO登录处理失败，请稍后重试")

        except Exception as e:
            logger.error(f"SSO登录处理失败: {str(e)}")
            return HttpResponseRedirect(f"/web/#/error?message=SSO登录处理失败，请稍后重试")

    def validate_direct_access(self, referer):
        """验证请求来源"""
        if not referer:
            return False

        try:
            # 从配置获取允许的域名列表
            allowed_domains = ['localhost', '127.0.0.1', '************', '*************', '*************']

            # 解析referer
            from urllib.parse import urlparse
            referer_host = urlparse(referer).netloc.split(':')[0].lower()

            # 验证是否来自允许的域名
            return any(domain == referer_host or referer_host.endswith('.' + domain) for domain in allowed_domains)
        except:
            return False

    def validate_sso_token(self, username, token, timestamp, nonce):
        """验证SSO令牌"""
        try:
            # 计算预期的令牌值
            raw_string = username + timestamp + nonce + SSO_SECRET_KEY
            expected_token = hmac_sha256(raw_string, SSO_SECRET_KEY)

            return token == expected_token
        except:
            return False

    def decrypt_user_info(self, encrypted_data):
        """解密用户信息"""
        try:
            if not encrypted_data:
                return None

            # 解密数据
            decrypted_json = decrypt_aes(encrypted_data, ENCRYPTION_KEY)

            if not decrypted_json:
                return None

            # 解析JSON
            return json.loads(decrypted_json)
        except Exception as e:
            logger.error(f"解密用户信息失败: {str(e)}")
            return None


# 辅助函数：HMAC-SHA256
def hmac_sha256(data, key):
    key_bytes = key.encode('utf-8')
    data_bytes = data.encode('utf-8')
    signature = hmac.new(key_bytes, data_bytes, hashlib.sha256).hexdigest()
    return signature


# 辅助函数：简化版解密（暂时不使用AES解密）
def decrypt_aes(encrypted_data, key):
    try:
        # 简化处理，直接解码Base64
        # 在实际生产环境中，应该使用真正的AES解密
        # 这里为了简化实现，我们假设数据已经是JSON格式的字符串
        try:
            # 尝试Base64解码
            decoded_data = base64.b64decode(encrypted_data).decode('utf-8')
            # 尝试解析JSON
            json.loads(decoded_data)
            return decoded_data
        except:
            # 如果解码失败，返回一个默认的JSON字符串
            logger.warning("解密失败，使用默认用户信息")
            return json.dumps({
                "DisplayName": "默认用户",
                "Email": "",
                "Mobile": "",
                "IsPersistent": True
            })
    except Exception as e:
        logger.error(f"解密失败: {str(e)}")
        raise
