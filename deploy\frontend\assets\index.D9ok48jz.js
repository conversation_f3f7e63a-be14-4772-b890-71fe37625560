import{X as M,E as V,J as N}from"./index.BHZI5pdK.js";import U from"./index.CJcEk6oX.js";import{_ as E}from"./index.vue_vue_type_script_setup_true_lang.CR5pk3cY.js";import j from"./index.Dl1XV0yb.js";import G from"./index.ljeM_6LD.js";import{G as X,D as z}from"./api.x73xLuMC.js";import{d as x,h as r,j as A,k as s,b as C,o as T,w as n,l as o,e as _,u,$ as I,m as J}from"./vue.BNx9QYep.js";import{_ as K}from"./_plugin-vue_export-helper.DlAUqK2U.js";import"./index.es.DmevZXPX.js";import"./index.vue_vue_type_script_setup_true_name_svgIcon_lang.D5K-qD_P.js";import"./crud.BYCW0fyj.js";import"./authFunction.D3Be3hRy.js";import"./crud.P6CIYdWQ.js";import"./api.BEBRHkAk.js";const L={class:"menu-box menu-left-box"},O={style:{height:"72vh"}},P={style:{height:"72vh"}},$=x({name:"menuPages"}),q=x({...$,setup(H){let m=r([]),p=r([]),l=r(!1),i=r({}),h=r(null),D=r(null),b=r(null);const f=()=>{X({}).then(a=>{const t=a.data,e=M.toArrayTree(t,{parentKey:"parent",children:"children",strict:!0});m.value=e})},y=a=>{var t,e;(t=D.value)==null||t.handleRefreshTable(a),(e=b.value)==null||e.handleRefreshTable(a)},k=(a,t)=>{var e,c;if(a==="update"&&t){const d=((c=(e=h.value)==null?void 0:e.treeRef)==null?void 0:c.currentNode.parent.data)||{};p.value=[d],i.value=t}l.value=!0},v=a=>{a==="submit"&&f(),l.value=!1,i.value={}},R=(a,t)=>{V.confirm("您确认删除该菜单项吗?","温馨提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(async()=>{const e=await z(a);t(),(e==null?void 0:e.code)===2e3&&(N(e.msg),f())})};return A(()=>{f()}),(a,t)=>{const e=s("el-col"),c=s("el-tab-pane"),d=s("el-tabs"),g=s("el-row"),B=s("el-drawer"),F=s("fs-page");return T(),C(F,null,{default:n(()=>[o(g,{class:"menu-el-row"},{default:n(()=>[o(e,{span:6},{default:n(()=>[_("div",L,[o(U,{ref_key:"menuTreeRef",ref:h,treeData:u(m),onTreeClick:y,onUpdateDept:k,onDeleteDept:R},null,8,["treeData"])])]),_:1}),o(e,{span:18},{default:n(()=>[o(d,{type:"border-card"},{default:n(()=>[o(c,{label:"按钮权限配置"},{default:n(()=>[_("div",O,[o(E,{ref_key:"menuButtonRef",ref:D},null,512)])]),_:1}),o(c,{label:"列权限配置"},{default:n(()=>[_("div",P,[o(G,{ref_key:"menuFieldRef",ref:b},null,512)])]),_:1})]),_:1})]),_:1})]),_:1}),o(B,{modelValue:u(l),"onUpdate:modelValue":t[0]||(t[0]=w=>I(l)?l.value=w:l=w),title:"菜单配置",direction:"rtl",size:"500px","close-on-click-modal":!1,"before-close":v},{default:n(()=>[u(l)?(T(),C(j,{key:0,initFormData:u(i),cacheData:u(p),treeData:u(m),onDrawerClose:v},null,8,["initFormData","cacheData","treeData"])):J("",!0)]),_:1},8,["modelValue"])]),_:1})}}}),ce=K(q,[["__scopeId","data-v-29f7b6c6"]]);export{ce as default};
