import{B as a}from"./vue.BNx9QYep.js";const l=a("RoleMenuFieldStores",{state:()=>[],actions:{setState(e){this.$state=e,this.$state.length=e.length}}}),t=a("RoleMenuFieldHeaderStores",{state:()=>[{value:"is_create",label:"新增可见",disabled:"disabled_create",checked:!1},{value:"is_update",label:"编辑可见",disabled:"disabled_update",checked:!1},{value:"is_query",label:"列表可见",disabled:"disabled_query",checked:!1}]});export{l as R,t as a};
