import{d as Ie,k as Gt,b as St,o as Ct,w as Wt,e as X,A as Et,D as Rt,l as Dt,s as te,x as Ht,r as ee,a as Pe,F as Ue,p as _e,f as $e,h as q,c as Mt,E as ie}from"./vue.BNx9QYep.js";import{j as Fe,k as qe,o as Qe}from"./index.BHZI5pdK.js";/*!
* Cropper.js v1.6.2
* https://fengyuanchen.github.io/cropperjs
*
* Copyright 2015-present <PERSON>
* Released under the MIT license
*
* Date: 2024-04-21T07:43:05.335Z
*/function ae(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter(function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable})),i.push.apply(i,a)}return i}function be(t){for(var e=1;e<arguments.length;e++){var i=arguments[e]!=null?arguments[e]:{};e%2?ae(Object(i),!0).forEach(function(a){Je(t,a,i[a])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):ae(Object(i)).forEach(function(a){Object.defineProperty(t,a,Object.getOwnPropertyDescriptor(i,a))})}return t}function Ve(t,e){if(typeof t!="object"||!t)return t;var i=t[Symbol.toPrimitive];if(i!==void 0){var a=i.call(t,e);if(typeof a!="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}function ye(t){var e=Ve(t,"string");return typeof e=="symbol"?e:e+""}function Xt(t){"@babel/helpers - typeof";return Xt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Xt(t)}function Ze(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function re(t,e){for(var i=0;i<e.length;i++){var a=e[i];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,ye(a.key),a)}}function Ke(t,e,i){return e&&re(t.prototype,e),i&&re(t,i),Object.defineProperty(t,"prototype",{writable:!1}),t}function Je(t,e,i){return e=ye(e),e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function xe(t){return Ge(t)||ti(t)||ei(t)||ii()}function Ge(t){if(Array.isArray(t))return At(t)}function ti(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function ei(t,e){if(t){if(typeof t=="string")return At(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);if(i==="Object"&&t.constructor&&(i=t.constructor.name),i==="Map"||i==="Set")return Array.from(t);if(i==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return At(t,e)}}function At(t,e){(e==null||e>t.length)&&(e=t.length);for(var i=0,a=new Array(e);i<e;i++)a[i]=t[i];return a}function ii(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var Tt=typeof window<"u"&&typeof window.document<"u",_=Tt?window:{},Qt=Tt&&_.document.documentElement?"ontouchstart"in _.document.documentElement:!1,Vt=Tt?"PointerEvent"in _:!1,x="cropper",Zt="all",Ce="crop",De="move",Me="zoom",K="e",J="w",at="s",Q="n",ut="ne",dt="nw",mt="se",ft="sw",jt="".concat(x,"-crop"),ne="".concat(x,"-disabled"),W="".concat(x,"-hidden"),oe="".concat(x,"-hide"),ai="".concat(x,"-invisible"),Ot="".concat(x,"-modal"),It="".concat(x,"-move"),vt="".concat(x,"Action"),Bt="".concat(x,"Preview"),Kt="crop",Be="move",ke="none",Pt="crop",Ut="cropend",_t="cropmove",$t="cropstart",se="dblclick",ri=Qt?"touchstart":"mousedown",ni=Qt?"touchmove":"mousemove",oi=Qt?"touchend touchcancel":"mouseup",he=Vt?"pointerdown":ri,le=Vt?"pointermove":ni,ce=Vt?"pointerup pointercancel":oi,pe="ready",ue="resize",de="wheel",Ft="zoom",me="image/jpeg",si=/^e|w|s|n|se|sw|ne|nw|all|crop|move|zoom$/,hi=/^data:/,li=/^data:image\/jpeg;base64,/,ci=/^img|canvas$/i,Oe=200,Te=100,fe={viewMode:0,dragMode:Kt,initialAspectRatio:NaN,aspectRatio:NaN,data:null,preview:"",responsive:!0,restore:!0,checkCrossOrigin:!0,checkOrientation:!0,modal:!0,guides:!0,center:!0,highlight:!0,background:!0,autoCrop:!0,autoCropArea:.8,movable:!0,rotatable:!0,scalable:!0,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,wheelZoomRatio:.1,cropBoxMovable:!0,cropBoxResizable:!0,toggleDragModeOnDblclick:!0,minCanvasWidth:0,minCanvasHeight:0,minCropBoxWidth:0,minCropBoxHeight:0,minContainerWidth:Oe,minContainerHeight:Te,ready:null,cropstart:null,cropmove:null,cropend:null,crop:null,zoom:null},pi='<div class="cropper-container" touch-action="none"><div class="cropper-wrap-box"><div class="cropper-canvas"></div></div><div class="cropper-drag-box"></div><div class="cropper-crop-box"><span class="cropper-view-box"></span><span class="cropper-dashed dashed-h"></span><span class="cropper-dashed dashed-v"></span><span class="cropper-center"></span><span class="cropper-face"></span><span class="cropper-line line-e" data-cropper-action="e"></span><span class="cropper-line line-n" data-cropper-action="n"></span><span class="cropper-line line-w" data-cropper-action="w"></span><span class="cropper-line line-s" data-cropper-action="s"></span><span class="cropper-point point-e" data-cropper-action="e"></span><span class="cropper-point point-n" data-cropper-action="n"></span><span class="cropper-point point-w" data-cropper-action="w"></span><span class="cropper-point point-s" data-cropper-action="s"></span><span class="cropper-point point-ne" data-cropper-action="ne"></span><span class="cropper-point point-nw" data-cropper-action="nw"></span><span class="cropper-point point-sw" data-cropper-action="sw"></span><span class="cropper-point point-se" data-cropper-action="se"></span></div></div>',ui=Number.isNaN||_.isNaN;function f(t){return typeof t=="number"&&!ui(t)}var ge=function(t){return t>0&&t<1/0};function Lt(t){return typeof t>"u"}function G(t){return Xt(t)==="object"&&t!==null}var di=Object.prototype.hasOwnProperty;function rt(t){if(!G(t))return!1;try{var e=t.constructor,i=e.prototype;return e&&i&&di.call(i,"isPrototypeOf")}catch{return!1}}function S(t){return typeof t=="function"}var mi=Array.prototype.slice;function ze(t){return Array.from?Array.from(t):mi.call(t)}function O(t,e){return t&&S(e)&&(Array.isArray(t)||f(t.length)?ze(t).forEach(function(i,a){e.call(t,i,a,t)}):G(t)&&Object.keys(t).forEach(function(i){e.call(t,t[i],i,t)})),t}var C=Object.assign||function(t){for(var e=arguments.length,i=new Array(e>1?e-1:0),a=1;a<e;a++)i[a-1]=arguments[a];return G(t)&&i.length>0&&i.forEach(function(r){G(r)&&Object.keys(r).forEach(function(n){t[n]=r[n]})}),t},fi=/\.\d*(?:0|9){12}\d*$/;function ot(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1e11;return fi.test(t)?Math.round(t*e)/e:t}var gi=/^width|height|left|top|marginLeft|marginTop$/;function V(t,e){var i=t.style;O(e,function(a,r){gi.test(r)&&f(a)&&(a="".concat(a,"px")),i[r]=a})}function vi(t,e){return t.classList?t.classList.contains(e):t.className.indexOf(e)>-1}function z(t,e){if(e){if(f(t.length)){O(t,function(a){z(a,e)});return}if(t.classList){t.classList.add(e);return}var i=t.className.trim();i?i.indexOf(e)<0&&(t.className="".concat(i," ").concat(e)):t.className=e}}function U(t,e){if(e){if(f(t.length)){O(t,function(i){U(i,e)});return}if(t.classList){t.classList.remove(e);return}t.className.indexOf(e)>=0&&(t.className=t.className.replace(e,""))}}function nt(t,e,i){if(e){if(f(t.length)){O(t,function(a){nt(a,e,i)});return}i?z(t,e):U(t,e)}}var wi=/([a-z\d])([A-Z])/g;function Jt(t){return t.replace(wi,"$1-$2").toLowerCase()}function qt(t,e){return G(t[e])?t[e]:t.dataset?t.dataset[e]:t.getAttribute("data-".concat(Jt(e)))}function wt(t,e,i){G(i)?t[e]=i:t.dataset?t.dataset[e]=i:t.setAttribute("data-".concat(Jt(e)),i)}function bi(t,e){if(G(t[e]))try{delete t[e]}catch{t[e]=void 0}else if(t.dataset)try{delete t.dataset[e]}catch{t.dataset[e]=void 0}else t.removeAttribute("data-".concat(Jt(e)))}var Ne=/\s\s*/,Se=function(){var t=!1;if(Tt){var e=!1,i=function(){},a=Object.defineProperty({},"once",{get:function(){return t=!0,e},set:function(r){e=r}});_.addEventListener("test",i,a),_.removeEventListener("test",i,a)}return t}();function I(t,e,i){var a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},r=i;e.trim().split(Ne).forEach(function(n){if(!Se){var o=t.listeners;o&&o[n]&&o[n][i]&&(r=o[n][i],delete o[n][i],Object.keys(o[n]).length===0&&delete o[n],Object.keys(o).length===0&&delete t.listeners)}t.removeEventListener(n,r,a)})}function A(t,e,i){var a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},r=i;e.trim().split(Ne).forEach(function(n){if(a.once&&!Se){var o=t.listeners,l=o===void 0?{}:o;r=function(){delete l[n][i],t.removeEventListener(n,r,a);for(var c=arguments.length,s=new Array(c),h=0;h<c;h++)s[h]=arguments[h];i.apply(t,s)},l[n]||(l[n]={}),l[n][i]&&t.removeEventListener(n,l[n][i],a),l[n][i]=r,t.listeners=l}t.addEventListener(n,r,a)})}function st(t,e,i){var a;return S(Event)&&S(CustomEvent)?a=new CustomEvent(e,{detail:i,bubbles:!0,cancelable:!0}):(a=document.createEvent("CustomEvent"),a.initCustomEvent(e,!0,!0,i)),t.dispatchEvent(a)}function We(t){var e=t.getBoundingClientRect();return{left:e.left+(window.pageXOffset-document.documentElement.clientLeft),top:e.top+(window.pageYOffset-document.documentElement.clientTop)}}var Yt=_.location,yi=/^(\w+:)\/\/([^:/?#]*):?(\d*)/i;function ve(t){var e=t.match(yi);return e!==null&&(e[1]!==Yt.protocol||e[2]!==Yt.hostname||e[3]!==Yt.port)}function we(t){var e="timestamp=".concat(new Date().getTime());return t+(t.indexOf("?")===-1?"?":"&")+e}function gt(t){var e=t.rotate,i=t.scaleX,a=t.scaleY,r=t.translateX,n=t.translateY,o=[];f(r)&&r!==0&&o.push("translateX(".concat(r,"px)")),f(n)&&n!==0&&o.push("translateY(".concat(n,"px)")),f(e)&&e!==0&&o.push("rotate(".concat(e,"deg)")),f(i)&&i!==1&&o.push("scaleX(".concat(i,")")),f(a)&&a!==1&&o.push("scaleY(".concat(a,")"));var l=o.length?o.join(" "):"none";return{WebkitTransform:l,msTransform:l,transform:l}}function xi(t){var e=be({},t),i=0;return O(t,function(a,r){delete e[r],O(e,function(n){var o=Math.abs(a.startX-n.startX),l=Math.abs(a.startY-n.startY),c=Math.abs(a.endX-n.endX),s=Math.abs(a.endY-n.endY),h=Math.sqrt(o*o+l*l),p=Math.sqrt(c*c+s*s),g=(p-h)/h;Math.abs(g)>Math.abs(i)&&(i=g)})}),i}function kt(t,e){var i=t.pageX,a=t.pageY,r={endX:i,endY:a};return e?r:be({startX:i,startY:a},r)}function Ci(t){var e=0,i=0,a=0;return O(t,function(r){var n=r.startX,o=r.startY;e+=n,i+=o,a+=1}),e/=a,i/=a,{pageX:e,pageY:i}}function Z(t){var e=t.aspectRatio,i=t.height,a=t.width,r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"contain",n=ge(a),o=ge(i);if(n&&o){var l=i*e;r==="contain"&&l>a||r==="cover"&&l<a?i=a/e:a=i*e}else n?i=a/e:o&&(a=i*e);return{width:a,height:i}}function Di(t){var e=t.width,i=t.height,a=t.degree;if(a=Math.abs(a)%180,a===90)return{width:i,height:e};var r=a%90*Math.PI/180,n=Math.sin(r),o=Math.cos(r),l=e*o+i*n,c=e*n+i*o;return a>90?{width:c,height:l}:{width:l,height:c}}function Mi(t,e,i,a){var r=e.aspectRatio,n=e.naturalWidth,o=e.naturalHeight,l=e.rotate,c=l===void 0?0:l,s=e.scaleX,h=s===void 0?1:s,p=e.scaleY,g=p===void 0?1:p,m=i.aspectRatio,b=i.naturalWidth,w=i.naturalHeight,M=a.fillColor,k=M===void 0?"transparent":M,y=a.imageSmoothingEnabled,R=y===void 0?!0:y,N=a.imageSmoothingQuality,u=N===void 0?"low":N,v=a.maxWidth,B=v===void 0?1/0:v,E=a.maxHeight,H=E===void 0?1/0:E,P=a.minWidth,$=P===void 0?0:P,L=a.minHeight,Y=L===void 0?0:L,j=document.createElement("canvas"),T=j.getContext("2d"),tt=Z({aspectRatio:m,width:B,height:H}),ht=Z({aspectRatio:m,width:$,height:Y},"cover"),et=Math.min(tt.width,Math.max(ht.width,b)),lt=Math.min(tt.height,Math.max(ht.height,w)),bt=Z({aspectRatio:r,width:B,height:H}),yt=Z({aspectRatio:r,width:$,height:Y},"cover"),ct=Math.min(bt.width,Math.max(yt.width,n)),it=Math.min(bt.height,Math.max(yt.height,o)),zt=[-ct/2,-it/2,ct,it];return j.width=ot(et),j.height=ot(lt),T.fillStyle=k,T.fillRect(0,0,et,lt),T.save(),T.translate(et/2,lt/2),T.rotate(c*Math.PI/180),T.scale(h,g),T.imageSmoothingEnabled=R,T.imageSmoothingQuality=u,T.drawImage.apply(T,[t].concat(xe(zt.map(function(Nt){return Math.floor(ot(Nt))})))),T.restore(),j}var Ee=String.fromCharCode;function Bi(t,e,i){var a="";i+=e;for(var r=e;r<i;r+=1)a+=Ee(t.getUint8(r));return a}var ki=/^data:.*,/;function Oi(t){var e=t.replace(ki,""),i=atob(e),a=new ArrayBuffer(i.length),r=new Uint8Array(a);return O(r,function(n,o){r[o]=i.charCodeAt(o)}),a}function Ti(t,e){for(var i=[],a=8192,r=new Uint8Array(t);r.length>0;)i.push(Ee.apply(null,ze(r.subarray(0,a)))),r=r.subarray(a);return"data:".concat(e,";base64,").concat(btoa(i.join("")))}function zi(t){var e=new DataView(t),i;try{var a,r,n;if(e.getUint8(0)===255&&e.getUint8(1)===216)for(var o=e.byteLength,l=2;l+1<o;){if(e.getUint8(l)===255&&e.getUint8(l+1)===225){r=l;break}l+=1}if(r){var c=r+4,s=r+10;if(Bi(e,c,4)==="Exif"){var h=e.getUint16(s);if(a=h===18761,(a||h===19789)&&e.getUint16(s+2,a)===42){var p=e.getUint32(s+4,a);p>=8&&(n=s+p)}}}if(n){var g=e.getUint16(n,a),m,b;for(b=0;b<g;b+=1)if(m=n+b*12+2,e.getUint16(m,a)===274){m+=8,i=e.getUint16(m,a),e.setUint16(m,1,a);break}}}catch{i=1}return i}function Ni(t){var e=0,i=1,a=1;switch(t){case 2:i=-1;break;case 3:e=-180;break;case 4:a=-1;break;case 5:e=90,a=-1;break;case 6:e=90;break;case 7:e=90,i=-1;break;case 8:e=-90;break}return{rotate:e,scaleX:i,scaleY:a}}var Si={render:function(){this.initContainer(),this.initCanvas(),this.initCropBox(),this.renderCanvas(),this.cropped&&this.renderCropBox()},initContainer:function(){var t=this.element,e=this.options,i=this.container,a=this.cropper,r=Number(e.minContainerWidth),n=Number(e.minContainerHeight);z(a,W),U(t,W);var o={width:Math.max(i.offsetWidth,r>=0?r:Oe),height:Math.max(i.offsetHeight,n>=0?n:Te)};this.containerData=o,V(a,{width:o.width,height:o.height}),z(t,W),U(a,W)},initCanvas:function(){var t=this.containerData,e=this.imageData,i=this.options.viewMode,a=Math.abs(e.rotate)%180===90,r=a?e.naturalHeight:e.naturalWidth,n=a?e.naturalWidth:e.naturalHeight,o=r/n,l=t.width,c=t.height;t.height*o>t.width?i===3?l=t.height*o:c=t.width/o:i===3?c=t.width/o:l=t.height*o;var s={aspectRatio:o,naturalWidth:r,naturalHeight:n,width:l,height:c};this.canvasData=s,this.limited=i===1||i===2,this.limitCanvas(!0,!0),s.width=Math.min(Math.max(s.width,s.minWidth),s.maxWidth),s.height=Math.min(Math.max(s.height,s.minHeight),s.maxHeight),s.left=(t.width-s.width)/2,s.top=(t.height-s.height)/2,s.oldLeft=s.left,s.oldTop=s.top,this.initialCanvasData=C({},s)},limitCanvas:function(t,e){var i=this.options,a=this.containerData,r=this.canvasData,n=this.cropBoxData,o=i.viewMode,l=r.aspectRatio,c=this.cropped&&n;if(t){var s=Number(i.minCanvasWidth)||0,h=Number(i.minCanvasHeight)||0;o>1?(s=Math.max(s,a.width),h=Math.max(h,a.height),o===3&&(h*l>s?s=h*l:h=s/l)):o>0&&(s?s=Math.max(s,c?n.width:0):h?h=Math.max(h,c?n.height:0):c&&(s=n.width,h=n.height,h*l>s?s=h*l:h=s/l));var p=Z({aspectRatio:l,width:s,height:h});s=p.width,h=p.height,r.minWidth=s,r.minHeight=h,r.maxWidth=1/0,r.maxHeight=1/0}if(e)if(o>(c?0:1)){var g=a.width-r.width,m=a.height-r.height;r.minLeft=Math.min(0,g),r.minTop=Math.min(0,m),r.maxLeft=Math.max(0,g),r.maxTop=Math.max(0,m),c&&this.limited&&(r.minLeft=Math.min(n.left,n.left+(n.width-r.width)),r.minTop=Math.min(n.top,n.top+(n.height-r.height)),r.maxLeft=n.left,r.maxTop=n.top,o===2&&(r.width>=a.width&&(r.minLeft=Math.min(0,g),r.maxLeft=Math.max(0,g)),r.height>=a.height&&(r.minTop=Math.min(0,m),r.maxTop=Math.max(0,m))))}else r.minLeft=-r.width,r.minTop=-r.height,r.maxLeft=a.width,r.maxTop=a.height},renderCanvas:function(t,e){var i=this.canvasData,a=this.imageData;if(e){var r=Di({width:a.naturalWidth*Math.abs(a.scaleX||1),height:a.naturalHeight*Math.abs(a.scaleY||1),degree:a.rotate||0}),n=r.width,o=r.height,l=i.width*(n/i.naturalWidth),c=i.height*(o/i.naturalHeight);i.left-=(l-i.width)/2,i.top-=(c-i.height)/2,i.width=l,i.height=c,i.aspectRatio=n/o,i.naturalWidth=n,i.naturalHeight=o,this.limitCanvas(!0,!1)}(i.width>i.maxWidth||i.width<i.minWidth)&&(i.left=i.oldLeft),(i.height>i.maxHeight||i.height<i.minHeight)&&(i.top=i.oldTop),i.width=Math.min(Math.max(i.width,i.minWidth),i.maxWidth),i.height=Math.min(Math.max(i.height,i.minHeight),i.maxHeight),this.limitCanvas(!1,!0),i.left=Math.min(Math.max(i.left,i.minLeft),i.maxLeft),i.top=Math.min(Math.max(i.top,i.minTop),i.maxTop),i.oldLeft=i.left,i.oldTop=i.top,V(this.canvas,C({width:i.width,height:i.height},gt({translateX:i.left,translateY:i.top}))),this.renderImage(t),this.cropped&&this.limited&&this.limitCropBox(!0,!0)},renderImage:function(t){var e=this.canvasData,i=this.imageData,a=i.naturalWidth*(e.width/e.naturalWidth),r=i.naturalHeight*(e.height/e.naturalHeight);C(i,{width:a,height:r,left:(e.width-a)/2,top:(e.height-r)/2}),V(this.image,C({width:i.width,height:i.height},gt(C({translateX:i.left,translateY:i.top},i)))),t&&this.output()},initCropBox:function(){var t=this.options,e=this.canvasData,i=t.aspectRatio||t.initialAspectRatio,a=Number(t.autoCropArea)||.8,r={width:e.width,height:e.height};i&&(e.height*i>e.width?r.height=r.width/i:r.width=r.height*i),this.cropBoxData=r,this.limitCropBox(!0,!0),r.width=Math.min(Math.max(r.width,r.minWidth),r.maxWidth),r.height=Math.min(Math.max(r.height,r.minHeight),r.maxHeight),r.width=Math.max(r.minWidth,r.width*a),r.height=Math.max(r.minHeight,r.height*a),r.left=e.left+(e.width-r.width)/2,r.top=e.top+(e.height-r.height)/2,r.oldLeft=r.left,r.oldTop=r.top,this.initialCropBoxData=C({},r)},limitCropBox:function(t,e){var i=this.options,a=this.containerData,r=this.canvasData,n=this.cropBoxData,o=this.limited,l=i.aspectRatio;if(t){var c=Number(i.minCropBoxWidth)||0,s=Number(i.minCropBoxHeight)||0,h=o?Math.min(a.width,r.width,r.width+r.left,a.width-r.left):a.width,p=o?Math.min(a.height,r.height,r.height+r.top,a.height-r.top):a.height;c=Math.min(c,a.width),s=Math.min(s,a.height),l&&(c&&s?s*l>c?s=c/l:c=s*l:c?s=c/l:s&&(c=s*l),p*l>h?p=h/l:h=p*l),n.minWidth=Math.min(c,h),n.minHeight=Math.min(s,p),n.maxWidth=h,n.maxHeight=p}e&&(o?(n.minLeft=Math.max(0,r.left),n.minTop=Math.max(0,r.top),n.maxLeft=Math.min(a.width,r.left+r.width)-n.width,n.maxTop=Math.min(a.height,r.top+r.height)-n.height):(n.minLeft=0,n.minTop=0,n.maxLeft=a.width-n.width,n.maxTop=a.height-n.height))},renderCropBox:function(){var t=this.options,e=this.containerData,i=this.cropBoxData;(i.width>i.maxWidth||i.width<i.minWidth)&&(i.left=i.oldLeft),(i.height>i.maxHeight||i.height<i.minHeight)&&(i.top=i.oldTop),i.width=Math.min(Math.max(i.width,i.minWidth),i.maxWidth),i.height=Math.min(Math.max(i.height,i.minHeight),i.maxHeight),this.limitCropBox(!1,!0),i.left=Math.min(Math.max(i.left,i.minLeft),i.maxLeft),i.top=Math.min(Math.max(i.top,i.minTop),i.maxTop),i.oldLeft=i.left,i.oldTop=i.top,t.movable&&t.cropBoxMovable&&wt(this.face,vt,i.width>=e.width&&i.height>=e.height?De:Zt),V(this.cropBox,C({width:i.width,height:i.height},gt({translateX:i.left,translateY:i.top}))),this.cropped&&this.limited&&this.limitCanvas(!0,!0),this.disabled||this.output()},output:function(){this.preview(),st(this.element,Pt,this.getData())}},Wi={initPreview:function(){var t=this.element,e=this.crossOrigin,i=this.options.preview,a=e?this.crossOriginUrl:this.url,r=t.alt||"The image to preview",n=document.createElement("img");if(e&&(n.crossOrigin=e),n.src=a,n.alt=r,this.viewBox.appendChild(n),this.viewBoxImage=n,!!i){var o=i;typeof i=="string"?o=t.ownerDocument.querySelectorAll(i):i.querySelector&&(o=[i]),this.previews=o,O(o,function(l){var c=document.createElement("img");wt(l,Bt,{width:l.offsetWidth,height:l.offsetHeight,html:l.innerHTML}),e&&(c.crossOrigin=e),c.src=a,c.alt=r,c.style.cssText='display:block;width:100%;height:auto;min-width:0!important;min-height:0!important;max-width:none!important;max-height:none!important;image-orientation:0deg!important;"',l.innerHTML="",l.appendChild(c)})}},resetPreview:function(){O(this.previews,function(t){var e=qt(t,Bt);V(t,{width:e.width,height:e.height}),t.innerHTML=e.html,bi(t,Bt)})},preview:function(){var t=this.imageData,e=this.canvasData,i=this.cropBoxData,a=i.width,r=i.height,n=t.width,o=t.height,l=i.left-e.left-t.left,c=i.top-e.top-t.top;!this.cropped||this.disabled||(V(this.viewBoxImage,C({width:n,height:o},gt(C({translateX:-l,translateY:-c},t)))),O(this.previews,function(s){var h=qt(s,Bt),p=h.width,g=h.height,m=p,b=g,w=1;a&&(w=p/a,b=r*w),r&&b>g&&(w=g/r,m=a*w,b=g),V(s,{width:m,height:b}),V(s.getElementsByTagName("img")[0],C({width:n*w,height:o*w},gt(C({translateX:-l*w,translateY:-c*w},t))))}))}},Ei={bind:function(){var t=this.element,e=this.options,i=this.cropper;S(e.cropstart)&&A(t,$t,e.cropstart),S(e.cropmove)&&A(t,_t,e.cropmove),S(e.cropend)&&A(t,Ut,e.cropend),S(e.crop)&&A(t,Pt,e.crop),S(e.zoom)&&A(t,Ft,e.zoom),A(i,he,this.onCropStart=this.cropStart.bind(this)),e.zoomable&&e.zoomOnWheel&&A(i,de,this.onWheel=this.wheel.bind(this),{passive:!1,capture:!0}),e.toggleDragModeOnDblclick&&A(i,se,this.onDblclick=this.dblclick.bind(this)),A(t.ownerDocument,le,this.onCropMove=this.cropMove.bind(this)),A(t.ownerDocument,ce,this.onCropEnd=this.cropEnd.bind(this)),e.responsive&&A(window,ue,this.onResize=this.resize.bind(this))},unbind:function(){var t=this.element,e=this.options,i=this.cropper;S(e.cropstart)&&I(t,$t,e.cropstart),S(e.cropmove)&&I(t,_t,e.cropmove),S(e.cropend)&&I(t,Ut,e.cropend),S(e.crop)&&I(t,Pt,e.crop),S(e.zoom)&&I(t,Ft,e.zoom),I(i,he,this.onCropStart),e.zoomable&&e.zoomOnWheel&&I(i,de,this.onWheel,{passive:!1,capture:!0}),e.toggleDragModeOnDblclick&&I(i,se,this.onDblclick),I(t.ownerDocument,le,this.onCropMove),I(t.ownerDocument,ce,this.onCropEnd),e.responsive&&I(window,ue,this.onResize)}},Ri={resize:function(){if(!this.disabled){var t=this.options,e=this.container,i=this.containerData,a=e.offsetWidth/i.width,r=e.offsetHeight/i.height,n=Math.abs(a-1)>Math.abs(r-1)?a:r;if(n!==1){var o,l;t.restore&&(o=this.getCanvasData(),l=this.getCropBoxData()),this.render(),t.restore&&(this.setCanvasData(O(o,function(c,s){o[s]=c*n})),this.setCropBoxData(O(l,function(c,s){l[s]=c*n})))}}},dblclick:function(){this.disabled||this.options.dragMode===ke||this.setDragMode(vi(this.dragBox,jt)?Be:Kt)},wheel:function(t){var e=this,i=Number(this.options.wheelZoomRatio)||.1,a=1;this.disabled||(t.preventDefault(),!this.wheeling&&(this.wheeling=!0,setTimeout(function(){e.wheeling=!1},50),t.deltaY?a=t.deltaY>0?1:-1:t.wheelDelta?a=-t.wheelDelta/120:t.detail&&(a=t.detail>0?1:-1),this.zoom(-a*i,t)))},cropStart:function(t){var e=t.buttons,i=t.button;if(!(this.disabled||(t.type==="mousedown"||t.type==="pointerdown"&&t.pointerType==="mouse")&&(f(e)&&e!==1||f(i)&&i!==0||t.ctrlKey))){var a=this.options,r=this.pointers,n;t.changedTouches?O(t.changedTouches,function(o){r[o.identifier]=kt(o)}):r[t.pointerId||0]=kt(t),Object.keys(r).length>1&&a.zoomable&&a.zoomOnTouch?n=Me:n=qt(t.target,vt),si.test(n)&&st(this.element,$t,{originalEvent:t,action:n})!==!1&&(t.preventDefault(),this.action=n,this.cropping=!1,n===Ce&&(this.cropping=!0,z(this.dragBox,Ot)))}},cropMove:function(t){var e=this.action;if(!(this.disabled||!e)){var i=this.pointers;t.preventDefault(),st(this.element,_t,{originalEvent:t,action:e})!==!1&&(t.changedTouches?O(t.changedTouches,function(a){C(i[a.identifier]||{},kt(a,!0))}):C(i[t.pointerId||0]||{},kt(t,!0)),this.change(t))}},cropEnd:function(t){if(!this.disabled){var e=this.action,i=this.pointers;t.changedTouches?O(t.changedTouches,function(a){delete i[a.identifier]}):delete i[t.pointerId||0],e&&(t.preventDefault(),Object.keys(i).length||(this.action=""),this.cropping&&(this.cropping=!1,nt(this.dragBox,Ot,this.cropped&&this.options.modal)),st(this.element,Ut,{originalEvent:t,action:e}))}}},Hi={change:function(t){var e=this.options,i=this.canvasData,a=this.containerData,r=this.cropBoxData,n=this.pointers,o=this.action,l=e.aspectRatio,c=r.left,s=r.top,h=r.width,p=r.height,g=c+h,m=s+p,b=0,w=0,M=a.width,k=a.height,y=!0,R;!l&&t.shiftKey&&(l=h&&p?h/p:1),this.limited&&(b=r.minLeft,w=r.minTop,M=b+Math.min(a.width,i.width,i.left+i.width),k=w+Math.min(a.height,i.height,i.top+i.height));var N=n[Object.keys(n)[0]],u={x:N.endX-N.startX,y:N.endY-N.startY},v=function(B){switch(B){case K:g+u.x>M&&(u.x=M-g);break;case J:c+u.x<b&&(u.x=b-c);break;case Q:s+u.y<w&&(u.y=w-s);break;case at:m+u.y>k&&(u.y=k-m);break}};switch(o){case Zt:c+=u.x,s+=u.y;break;case K:if(u.x>=0&&(g>=M||l&&(s<=w||m>=k))){y=!1;break}v(K),h+=u.x,h<0&&(o=J,h=-h,c-=h),l&&(p=h/l,s+=(r.height-p)/2);break;case Q:if(u.y<=0&&(s<=w||l&&(c<=b||g>=M))){y=!1;break}v(Q),p-=u.y,s+=u.y,p<0&&(o=at,p=-p,s-=p),l&&(h=p*l,c+=(r.width-h)/2);break;case J:if(u.x<=0&&(c<=b||l&&(s<=w||m>=k))){y=!1;break}v(J),h-=u.x,c+=u.x,h<0&&(o=K,h=-h,c-=h),l&&(p=h/l,s+=(r.height-p)/2);break;case at:if(u.y>=0&&(m>=k||l&&(c<=b||g>=M))){y=!1;break}v(at),p+=u.y,p<0&&(o=Q,p=-p,s-=p),l&&(h=p*l,c+=(r.width-h)/2);break;case ut:if(l){if(u.y<=0&&(s<=w||g>=M)){y=!1;break}v(Q),p-=u.y,s+=u.y,h=p*l}else v(Q),v(K),u.x>=0?g<M?h+=u.x:u.y<=0&&s<=w&&(y=!1):h+=u.x,u.y<=0?s>w&&(p-=u.y,s+=u.y):(p-=u.y,s+=u.y);h<0&&p<0?(o=ft,p=-p,h=-h,s-=p,c-=h):h<0?(o=dt,h=-h,c-=h):p<0&&(o=mt,p=-p,s-=p);break;case dt:if(l){if(u.y<=0&&(s<=w||c<=b)){y=!1;break}v(Q),p-=u.y,s+=u.y,h=p*l,c+=r.width-h}else v(Q),v(J),u.x<=0?c>b?(h-=u.x,c+=u.x):u.y<=0&&s<=w&&(y=!1):(h-=u.x,c+=u.x),u.y<=0?s>w&&(p-=u.y,s+=u.y):(p-=u.y,s+=u.y);h<0&&p<0?(o=mt,p=-p,h=-h,s-=p,c-=h):h<0?(o=ut,h=-h,c-=h):p<0&&(o=ft,p=-p,s-=p);break;case ft:if(l){if(u.x<=0&&(c<=b||m>=k)){y=!1;break}v(J),h-=u.x,c+=u.x,p=h/l}else v(at),v(J),u.x<=0?c>b?(h-=u.x,c+=u.x):u.y>=0&&m>=k&&(y=!1):(h-=u.x,c+=u.x),u.y>=0?m<k&&(p+=u.y):p+=u.y;h<0&&p<0?(o=ut,p=-p,h=-h,s-=p,c-=h):h<0?(o=mt,h=-h,c-=h):p<0&&(o=dt,p=-p,s-=p);break;case mt:if(l){if(u.x>=0&&(g>=M||m>=k)){y=!1;break}v(K),h+=u.x,p=h/l}else v(at),v(K),u.x>=0?g<M?h+=u.x:u.y>=0&&m>=k&&(y=!1):h+=u.x,u.y>=0?m<k&&(p+=u.y):p+=u.y;h<0&&p<0?(o=dt,p=-p,h=-h,s-=p,c-=h):h<0?(o=ft,h=-h,c-=h):p<0&&(o=ut,p=-p,s-=p);break;case De:this.move(u.x,u.y),y=!1;break;case Me:this.zoom(xi(n),t),y=!1;break;case Ce:if(!u.x||!u.y){y=!1;break}R=We(this.cropper),c=N.startX-R.left,s=N.startY-R.top,h=r.minWidth,p=r.minHeight,u.x>0?o=u.y>0?mt:ut:u.x<0&&(c-=h,o=u.y>0?ft:dt),u.y<0&&(s-=p),this.cropped||(U(this.cropBox,W),this.cropped=!0,this.limited&&this.limitCropBox(!0,!0));break}y&&(r.width=h,r.height=p,r.left=c,r.top=s,this.action=o,this.renderCropBox()),O(n,function(B){B.startX=B.endX,B.startY=B.endY})}},Li={crop:function(){return this.ready&&!this.cropped&&!this.disabled&&(this.cropped=!0,this.limitCropBox(!0,!0),this.options.modal&&z(this.dragBox,Ot),U(this.cropBox,W),this.setCropBoxData(this.initialCropBoxData)),this},reset:function(){return this.ready&&!this.disabled&&(this.imageData=C({},this.initialImageData),this.canvasData=C({},this.initialCanvasData),this.cropBoxData=C({},this.initialCropBoxData),this.renderCanvas(),this.cropped&&this.renderCropBox()),this},clear:function(){return this.cropped&&!this.disabled&&(C(this.cropBoxData,{left:0,top:0,width:0,height:0}),this.cropped=!1,this.renderCropBox(),this.limitCanvas(!0,!0),this.renderCanvas(),U(this.dragBox,Ot),z(this.cropBox,W)),this},replace:function(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;return!this.disabled&&t&&(this.isImg&&(this.element.src=t),e?(this.url=t,this.image.src=t,this.ready&&(this.viewBoxImage.src=t,O(this.previews,function(i){i.getElementsByTagName("img")[0].src=t}))):(this.isImg&&(this.replaced=!0),this.options.data=null,this.uncreate(),this.load(t))),this},enable:function(){return this.ready&&this.disabled&&(this.disabled=!1,U(this.cropper,ne)),this},disable:function(){return this.ready&&!this.disabled&&(this.disabled=!0,z(this.cropper,ne)),this},destroy:function(){var t=this.element;return t[x]?(t[x]=void 0,this.isImg&&this.replaced&&(t.src=this.originalUrl),this.uncreate(),this):this},move:function(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,i=this.canvasData,a=i.left,r=i.top;return this.moveTo(Lt(t)?t:a+Number(t),Lt(e)?e:r+Number(e))},moveTo:function(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,i=this.canvasData,a=!1;return t=Number(t),e=Number(e),this.ready&&!this.disabled&&this.options.movable&&(f(t)&&(i.left=t,a=!0),f(e)&&(i.top=e,a=!0),a&&this.renderCanvas(!0)),this},zoom:function(t,e){var i=this.canvasData;return t=Number(t),t<0?t=1/(1-t):t=1+t,this.zoomTo(i.width*t/i.naturalWidth,null,e)},zoomTo:function(t,e,i){var a=this.options,r=this.canvasData,n=r.width,o=r.height,l=r.naturalWidth,c=r.naturalHeight;if(t=Number(t),t>=0&&this.ready&&!this.disabled&&a.zoomable){var s=l*t,h=c*t;if(st(this.element,Ft,{ratio:t,oldRatio:n/l,originalEvent:i})===!1)return this;if(i){var p=this.pointers,g=We(this.cropper),m=p&&Object.keys(p).length?Ci(p):{pageX:i.pageX,pageY:i.pageY};r.left-=(s-n)*((m.pageX-g.left-r.left)/n),r.top-=(h-o)*((m.pageY-g.top-r.top)/o)}else rt(e)&&f(e.x)&&f(e.y)?(r.left-=(s-n)*((e.x-r.left)/n),r.top-=(h-o)*((e.y-r.top)/o)):(r.left-=(s-n)/2,r.top-=(h-o)/2);r.width=s,r.height=h,this.renderCanvas(!0)}return this},rotate:function(t){return this.rotateTo((this.imageData.rotate||0)+Number(t))},rotateTo:function(t){return t=Number(t),f(t)&&this.ready&&!this.disabled&&this.options.rotatable&&(this.imageData.rotate=t%360,this.renderCanvas(!0,!0)),this},scaleX:function(t){var e=this.imageData.scaleY;return this.scale(t,f(e)?e:1)},scaleY:function(t){var e=this.imageData.scaleX;return this.scale(f(e)?e:1,t)},scale:function(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,i=this.imageData,a=!1;return t=Number(t),e=Number(e),this.ready&&!this.disabled&&this.options.scalable&&(f(t)&&(i.scaleX=t,a=!0),f(e)&&(i.scaleY=e,a=!0),a&&this.renderCanvas(!0,!0)),this},getData:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,e=this.options,i=this.imageData,a=this.canvasData,r=this.cropBoxData,n;if(this.ready&&this.cropped){n={x:r.left-a.left,y:r.top-a.top,width:r.width,height:r.height};var o=i.width/i.naturalWidth;if(O(n,function(s,h){n[h]=s/o}),t){var l=Math.round(n.y+n.height),c=Math.round(n.x+n.width);n.x=Math.round(n.x),n.y=Math.round(n.y),n.width=c-n.x,n.height=l-n.y}}else n={x:0,y:0,width:0,height:0};return e.rotatable&&(n.rotate=i.rotate||0),e.scalable&&(n.scaleX=i.scaleX||1,n.scaleY=i.scaleY||1),n},setData:function(t){var e=this.options,i=this.imageData,a=this.canvasData,r={};if(this.ready&&!this.disabled&&rt(t)){var n=!1;e.rotatable&&f(t.rotate)&&t.rotate!==i.rotate&&(i.rotate=t.rotate,n=!0),e.scalable&&(f(t.scaleX)&&t.scaleX!==i.scaleX&&(i.scaleX=t.scaleX,n=!0),f(t.scaleY)&&t.scaleY!==i.scaleY&&(i.scaleY=t.scaleY,n=!0)),n&&this.renderCanvas(!0,!0);var o=i.width/i.naturalWidth;f(t.x)&&(r.left=t.x*o+a.left),f(t.y)&&(r.top=t.y*o+a.top),f(t.width)&&(r.width=t.width*o),f(t.height)&&(r.height=t.height*o),this.setCropBoxData(r)}return this},getContainerData:function(){return this.ready?C({},this.containerData):{}},getImageData:function(){return this.sized?C({},this.imageData):{}},getCanvasData:function(){var t=this.canvasData,e={};return this.ready&&O(["left","top","width","height","naturalWidth","naturalHeight"],function(i){e[i]=t[i]}),e},setCanvasData:function(t){var e=this.canvasData,i=e.aspectRatio;return this.ready&&!this.disabled&&rt(t)&&(f(t.left)&&(e.left=t.left),f(t.top)&&(e.top=t.top),f(t.width)?(e.width=t.width,e.height=t.width/i):f(t.height)&&(e.height=t.height,e.width=t.height*i),this.renderCanvas(!0)),this},getCropBoxData:function(){var t=this.cropBoxData,e;return this.ready&&this.cropped&&(e={left:t.left,top:t.top,width:t.width,height:t.height}),e||{}},setCropBoxData:function(t){var e=this.cropBoxData,i=this.options.aspectRatio,a,r;return this.ready&&this.cropped&&!this.disabled&&rt(t)&&(f(t.left)&&(e.left=t.left),f(t.top)&&(e.top=t.top),f(t.width)&&t.width!==e.width&&(a=!0,e.width=t.width),f(t.height)&&t.height!==e.height&&(r=!0,e.height=t.height),i&&(a?e.height=e.width/i:r&&(e.width=e.height*i)),this.renderCropBox()),this},getCroppedCanvas:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!this.ready||!window.HTMLCanvasElement)return null;var e=this.canvasData,i=Mi(this.image,this.imageData,e,t);if(!this.cropped)return i;var a=this.getData(t.rounded),r=a.x,n=a.y,o=a.width,l=a.height,c=i.width/Math.floor(e.naturalWidth);c!==1&&(r*=c,n*=c,o*=c,l*=c);var s=o/l,h=Z({aspectRatio:s,width:t.maxWidth||1/0,height:t.maxHeight||1/0}),p=Z({aspectRatio:s,width:t.minWidth||0,height:t.minHeight||0},"cover"),g=Z({aspectRatio:s,width:t.width||(c!==1?i.width:o),height:t.height||(c!==1?i.height:l)}),m=g.width,b=g.height;m=Math.min(h.width,Math.max(p.width,m)),b=Math.min(h.height,Math.max(p.height,b));var w=document.createElement("canvas"),M=w.getContext("2d");w.width=ot(m),w.height=ot(b),M.fillStyle=t.fillColor||"transparent",M.fillRect(0,0,m,b);var k=t.imageSmoothingEnabled,y=k===void 0?!0:k,R=t.imageSmoothingQuality;M.imageSmoothingEnabled=y,R&&(M.imageSmoothingQuality=R);var N=i.width,u=i.height,v=r,B=n,E,H,P,$,L,Y;v<=-o||v>N?(v=0,E=0,P=0,L=0):v<=0?(P=-v,v=0,E=Math.min(N,o+v),L=E):v<=N&&(P=0,E=Math.min(o,N-v),L=E),E<=0||B<=-l||B>u?(B=0,H=0,$=0,Y=0):B<=0?($=-B,B=0,H=Math.min(u,l+B),Y=H):B<=u&&($=0,H=Math.min(l,u-B),Y=H);var j=[v,B,E,H];if(L>0&&Y>0){var T=m/o;j.push(P*T,$*T,L*T,Y*T)}return M.drawImage.apply(M,[i].concat(xe(j.map(function(tt){return Math.floor(ot(tt))})))),w},setAspectRatio:function(t){var e=this.options;return!this.disabled&&!Lt(t)&&(e.aspectRatio=Math.max(0,t)||NaN,this.ready&&(this.initCropBox(),this.cropped&&this.renderCropBox())),this},setDragMode:function(t){var e=this.options,i=this.dragBox,a=this.face;if(this.ready&&!this.disabled){var r=t===Kt,n=e.movable&&t===Be;t=r||n?t:ke,e.dragMode=t,wt(i,vt,t),nt(i,jt,r),nt(i,It,n),e.cropBoxMovable||(wt(a,vt,t),nt(a,jt,r),nt(a,It,n))}return this}},Yi=_.Cropper,Re=function(){function t(e){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(Ze(this,t),!e||!ci.test(e.tagName))throw new Error("The first argument is required and must be an <img> or <canvas> element.");this.element=e,this.options=C({},fe,rt(i)&&i),this.cropped=!1,this.disabled=!1,this.pointers={},this.ready=!1,this.reloading=!1,this.replaced=!1,this.sized=!1,this.sizing=!1,this.init()}return Ke(t,[{key:"init",value:function(){var e=this.element,i=e.tagName.toLowerCase(),a;if(!e[x]){if(e[x]=this,i==="img"){if(this.isImg=!0,a=e.getAttribute("src")||"",this.originalUrl=a,!a)return;a=e.src}else i==="canvas"&&window.HTMLCanvasElement&&(a=e.toDataURL());this.load(a)}}},{key:"load",value:function(e){var i=this;if(e){this.url=e,this.imageData={};var a=this.element,r=this.options;if(!r.rotatable&&!r.scalable&&(r.checkOrientation=!1),!r.checkOrientation||!window.ArrayBuffer){this.clone();return}if(hi.test(e)){li.test(e)?this.read(Oi(e)):this.clone();return}var n=new XMLHttpRequest,o=this.clone.bind(this);this.reloading=!0,this.xhr=n,n.onabort=o,n.onerror=o,n.ontimeout=o,n.onprogress=function(){n.getResponseHeader("content-type")!==me&&n.abort()},n.onload=function(){i.read(n.response)},n.onloadend=function(){i.reloading=!1,i.xhr=null},r.checkCrossOrigin&&ve(e)&&a.crossOrigin&&(e=we(e)),n.open("GET",e,!0),n.responseType="arraybuffer",n.withCredentials=a.crossOrigin==="use-credentials",n.send()}}},{key:"read",value:function(e){var i=this.options,a=this.imageData,r=zi(e),n=0,o=1,l=1;if(r>1){this.url=Ti(e,me);var c=Ni(r);n=c.rotate,o=c.scaleX,l=c.scaleY}i.rotatable&&(a.rotate=n),i.scalable&&(a.scaleX=o,a.scaleY=l),this.clone()}},{key:"clone",value:function(){var e=this.element,i=this.url,a=e.crossOrigin,r=i;this.options.checkCrossOrigin&&ve(i)&&(a||(a="anonymous"),r=we(i)),this.crossOrigin=a,this.crossOriginUrl=r;var n=document.createElement("img");a&&(n.crossOrigin=a),n.src=r||i,n.alt=e.alt||"The image to crop",this.image=n,n.onload=this.start.bind(this),n.onerror=this.stop.bind(this),z(n,oe),e.parentNode.insertBefore(n,e.nextSibling)}},{key:"start",value:function(){var e=this,i=this.image;i.onload=null,i.onerror=null,this.sizing=!0;var a=_.navigator&&/(?:iPad|iPhone|iPod).*?AppleWebKit/i.test(_.navigator.userAgent),r=function(l,c){C(e.imageData,{naturalWidth:l,naturalHeight:c,aspectRatio:l/c}),e.initialImageData=C({},e.imageData),e.sizing=!1,e.sized=!0,e.build()};if(i.naturalWidth&&!a){r(i.naturalWidth,i.naturalHeight);return}var n=document.createElement("img"),o=document.body||document.documentElement;this.sizingImage=n,n.onload=function(){r(n.width,n.height),a||o.removeChild(n)},n.src=i.src,a||(n.style.cssText="left:0;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;opacity:0;position:absolute;top:0;z-index:-1;",o.appendChild(n))}},{key:"stop",value:function(){var e=this.image;e.onload=null,e.onerror=null,e.parentNode.removeChild(e),this.image=null}},{key:"build",value:function(){if(!(!this.sized||this.ready)){var e=this.element,i=this.options,a=this.image,r=e.parentNode,n=document.createElement("div");n.innerHTML=pi;var o=n.querySelector(".".concat(x,"-container")),l=o.querySelector(".".concat(x,"-canvas")),c=o.querySelector(".".concat(x,"-drag-box")),s=o.querySelector(".".concat(x,"-crop-box")),h=s.querySelector(".".concat(x,"-face"));this.container=r,this.cropper=o,this.canvas=l,this.dragBox=c,this.cropBox=s,this.viewBox=o.querySelector(".".concat(x,"-view-box")),this.face=h,l.appendChild(a),z(e,W),r.insertBefore(o,e.nextSibling),U(a,oe),this.initPreview(),this.bind(),i.initialAspectRatio=Math.max(0,i.initialAspectRatio)||NaN,i.aspectRatio=Math.max(0,i.aspectRatio)||NaN,i.viewMode=Math.max(0,Math.min(3,Math.round(i.viewMode)))||0,z(s,W),i.guides||z(s.getElementsByClassName("".concat(x,"-dashed")),W),i.center||z(s.getElementsByClassName("".concat(x,"-center")),W),i.background&&z(o,"".concat(x,"-bg")),i.highlight||z(h,ai),i.cropBoxMovable&&(z(h,It),wt(h,vt,Zt)),i.cropBoxResizable||(z(s.getElementsByClassName("".concat(x,"-line")),W),z(s.getElementsByClassName("".concat(x,"-point")),W)),this.render(),this.ready=!0,this.setDragMode(i.dragMode),i.autoCrop&&this.crop(),this.setData(i.data),S(i.ready)&&A(e,pe,i.ready,{once:!0}),st(e,pe)}}},{key:"unbuild",value:function(){if(this.ready){this.ready=!1,this.unbind(),this.resetPreview();var e=this.cropper.parentNode;e&&e.removeChild(this.cropper),U(this.element,W)}}},{key:"uncreate",value:function(){this.ready?(this.unbuild(),this.ready=!1,this.cropped=!1):this.sizing?(this.sizingImage.onload=null,this.sizing=!1,this.sized=!1):this.reloading?(this.xhr.onabort=null,this.xhr.abort()):this.image&&this.stop()}}],[{key:"noConflict",value:function(){return window.Cropper=Yi,t}},{key:"setDefaults",value:function(e){C(fe,rt(e)&&e)}}])}();C(Re.prototype,Si,Wi,Ei,Ri,Hi,Li);const Xi=typeof window>"u"?[String,Array]:[String,Array,Element,NodeList],Ai={render(){const t=this.crossorigin||void 0;return ie("div",{style:this.containerStyle},[ie("img",{ref:"img",src:this.src,alt:this.alt||"image",style:[{"max-width":"100%"},this.imgStyle],crossorigin:t})])},props:{containerStyle:Object,src:{type:String,default:""},alt:String,imgStyle:Object,viewMode:Number,dragMode:String,initialAspectRatio:Number,aspectRatio:Number,data:Object,preview:Xi,responsive:{type:Boolean,default:!0},restore:{type:Boolean,default:!0},checkCrossOrigin:{type:Boolean,default:!0},checkOrientation:{type:Boolean,default:!0},crossorigin:{type:String},modal:{type:Boolean,default:!0},guides:{type:Boolean,default:!0},center:{type:Boolean,default:!0},highlight:{type:Boolean,default:!0},background:{type:Boolean,default:!0},autoCrop:{type:Boolean,default:!0},autoCropArea:Number,movable:{type:Boolean,default:!0},rotatable:{type:Boolean,default:!0},scalable:{type:Boolean,default:!0},zoomable:{type:Boolean,default:!0},zoomOnTouch:{type:Boolean,default:!0},zoomOnWheel:{type:Boolean,default:!0},wheelZoomRatio:Number,cropBoxMovable:{type:Boolean,default:!0},cropBoxResizable:{type:Boolean,default:!0},toggleDragModeOnDblclick:{type:Boolean,default:!0},minCanvasWidth:Number,minCanvasHeight:Number,minCropBoxWidth:Number,minCropBoxHeight:Number,minContainerWidth:Number,minContainerHeight:Number,ready:Function,cropstart:Function,cropmove:Function,cropend:Function,crop:Function,zoom:Function},mounted(){const{containerStyle:t,src:e,alt:i,imgStyle:a,...r}=this.$options.props,n={};for(const o in r)this[o]!==void 0&&(n[o]=this[o]);this.cropper=new Re(this.$refs.img,n)},methods:{reset(){return this.cropper.reset()},clear(){return this.cropper.clear()},initCrop(){return this.cropper.crop()},replace(t,e=!1){return this.cropper.replace(t,e)},enable(){return this.cropper.enable()},disable(){return this.cropper.disable()},destroy(){return this.cropper.destroy()},move(t,e){return this.cropper.move(t,e)},moveTo(t,e=t){return this.cropper.moveTo(t,e)},relativeZoom(t,e){return this.cropper.zoom(t,e)},zoomTo(t,e){return this.cropper.zoomTo(t,e)},rotate(t){return this.cropper.rotate(t)},rotateTo(t){return this.cropper.rotateTo(t)},scaleX(t){return this.cropper.scaleX(t)},scaleY(t){return this.cropper.scaleY(t)},scale(t,e=t){return this.cropper.scale(t,e)},getData(t=!1){return this.cropper.getData(t)},setData(t){return this.cropper.setData(t)},getContainerData(){return this.cropper.getContainerData()},getImageData(){return this.cropper.getImageData()},getCanvasData(){return this.cropper.getCanvasData()},setCanvasData(t){return this.cropper.setCanvasData(t)},getCropBoxData(){return this.cropper.getCropBoxData()},setCropBoxData(t){return this.cropper.setCropBoxData(t)},getCroppedCanvas(t={}){return this.cropper.getCroppedCanvas(t)},setAspectRatio(t){return this.cropper.setAspectRatio(t)},setDragMode(t){return this.cropper.setDragMode(t)}}},ji=Ie({name:"FsCropper",components:{VueCropper:Ai},props:{title:{type:String},cropperHeight:{type:[String,Number]},dialogWidth:{type:[String,Number],default:"50%"},maxSize:{type:Number,default:5},uploadTip:{type:String},cropper:{type:Object},accept:{type:String,default:".jpg, .jpeg, .png, .gif, .webp"},output:{type:String,default:"blob"},compressQuality:{type:Number,default:.8}},emits:["cancel","done","ready"],setup(t,e){const{ui:i}=qe(),{t:a}=Qe(),r=q(!1),n=q(),o=q(),l=q(!1),c=q(),s=q(),h=q(),p=q({x:1,y:1});function g(){r.value=!1}function m(){R(),e.emit("cancel")}const b=i.dialog.buildOnClosedBind(m),w=i.dialog.customClass,M=Mt(()=>({...b,[w]:"fs-cropper-dialog",...i.formWrapper.buildWidthBind(i.dialog.name,"960px"),...i.formWrapper.buildInitBind(i.dialog.name),title:t.title||a("fs.extends.cropper.title")}));function k(d){r.value=!0,d!=null&&d!==""&&(c.value=d)}function y(){r.value=!1}function R(){l.value=!1,o.value!=null&&(o.value.value=null,o.value=null),n.value!=null&&n.value.clear()}function N(){return n.value}const u={cropper:N(),zoom:xt,clear:R,close:y,open:k};function v(d){e.emit("ready",{event:d,...u})}function B(d){return d.preventDefault(),!1}function E(){o.value.click()}function H(d){return d.type.indexOf("image")===-1?(i.message.warn("请选择合适的文件类型"),!1):t.maxSize>0&&d.size/1024/1024>t.maxSize?(i.message.warn(`图片大小超出最大限制（${t.maxSize}MB），请重新选择.`),!1):!0}function P(d){const D=d.target.files[0];if(D.type.indexOf("image/")===-1){i.message.warn("Please select an image file");return}if(typeof FileReader=="function"){const F=new FileReader;F.onload=pt=>{c.value=pt.target.result,n.value.replace(pt.target.result)},F.readAsDataURL(D)}else i.message.error("Sorry, FileReader API not supported")}function $(d){d.preventDefault();const D=d.target.files||d.dataTransfer.files;if(D==null)return;l.value=!0;const F=D[0];H(F)&&(h.value=F,P(d))}function L(d,D){return D==null&&(D=t.compressQuality),n.value.getCroppedCanvas().toDataURL(d,D)}async function Y(d,D){return D==null&&(D=t.compressQuality),new Promise((F,pt)=>{function Ae(je){F(je)}n.value.getCroppedCanvas().toBlob(Ae,d,D)})}function j(d){e.emit("done",d)}async function T(d){const D={file:d};if(t.output==="all"){const F=await Y(d.type),pt=L(d.type);D.blob=F,D.dataUrl=pt,j(D);return}if(t.output==="blob"){D.blob=await Y(d.type),j(D);return}t.output==="dataUrl"&&(D.dataUrl=L(d.type),j(D))}async function tt(){if(!l.value){i.message.warn("请先选择图片");return}await T(h.value),r.value=!1}function ht(){n.value.scaleX(p.value.x*=-1)}function et(){n.value.scaleY(p.value.y*=-1)}function lt(){s.value=JSON.stringify(n.value.getCropBoxData(),null,4)}function bt(){s.value=JSON.stringify(n.value.getData(),null,4)}function yt(d,D){n.value.move(d,D)}function ct(){n.value.reset()}function it(d){n.value.rotate(d)}function zt(){n.value.setCropBoxData(JSON.parse(s.value))}function Nt(){n.value.setData(JSON.parse(s.value))}function He(){o.value.click()}function xt(d){n.value.relativeZoom(d)}const Le=Mt(()=>{const d="small";return[{size:d,round:!0,icon:i.icons.edit,text:a("fs.extends.cropper.reChoose"),onClick(){E()}},{size:d,round:!0,text:a("fs.extends.cropper.flipX"),onClick(){ht()}},{size:d,round:!0,text:a("fs.extends.cropper.flipY"),onClick(){et()}},{size:d,round:!0,icon:i.icons.zoomIn,onClick(){xt(.1)}},{size:d,round:!0,icon:i.icons.zoomOut,onClick(){xt(-.1)}},{size:d,round:!0,icon:i.icons.refreshLeft,onClick(){it(90)}},{size:d,round:!0,icon:i.icons.refreshRight,onClick(){it(-90)}},{size:d,round:!0,icon:i.icons.refresh,text:a("fs.extends.cropper.reset"),onClick(){ct()}}]}),Ye=Mt(()=>({title:a("fs.extends.cropper.title"),preview:a("fs.extends.cropper.preview"),cancel:a("fs.extends.cropper.cancel"),confirm:a("fs.extends.cropper.confirm"),chooseImage:a("fs.extends.cropper.chooseImage")})),Xe=Mt(()=>t.uploadTip!=null&&t.uploadTip!==""?t.uploadTip:t.maxSize>0?`${a("fs.extends.cropper.onlySupport")} ${t.accept.replace(/,/g,"、")},
        ${a("fs.extends.cropper.sizeLimit")} ${t.maxSize}M`:`${a("fs.extends.cropper.onlySupport")}${t.accept},${a("fs.extends.cropper.sizeNoLimit")}`);return{ui:i,cropperRef:n,fileInputRef:o,dialogVisible:r,dialogBinding:M,isLoaded:l,imgSrc:c,data:s,file:h,scale:p,computedButtons:Le,handleClose:g,setData:Nt,handleClosed:m,close:y,showFileChooser:He,zoom:xt,setCropBoxData:zt,rotate:it,reset:ct,move:yt,getData:bt,getCropBoxData:lt,flipY:et,flipX:ht,doCropper:tt,doOutput:T,getCropImageBlob:Y,getCropImageDataUrl:L,handleChange:$,setImage:P,checkFile:H,handleClick:E,preventDefault:B,open:k,clear:R,getCropperRef:N,ready:v,computedTexts:Ye,computedUploadTip:Xe}},data(){return{}},computed:{_cropper(){const t={aspectRatio:1,ready:this.ready};return this.cropper==null?t:Object.assign(t,this.cropper)},_cropperHeight(){let t=this.cropperHeight;return t==null&&(t=document.documentElement.clientHeight*.55,t<270&&(t=270)),typeof t=="number"?t+"px":t},_dialogWidth(){let t=this.dialogWidth;return t==null&&(t="50%"),typeof t=="number"?t+"px":t}}}),Ii={class:"fs-cropper-dialog-wrap"},Pi=["accept"],Ui={class:"fs-cropper-dialog__choose fs-cropper-dialog_left"},_i={class:"fs-cropper-dialog__edit fs-cropper-dialog_left"},$i={class:"fs-cropper-dialog__edit-area"},Fi={class:"tool-bar"},qi={class:"fs-cropper-dialog__preview"},Qi={class:"fs-cropper-dialog__preview-title"},Vi={class:"dialog-footer"};function Zi(t,e,i,a,r,n){const o=Gt("fs-button"),l=Gt("vue-cropper");return Ct(),St(ee(t.ui.dialog.name),Ht({ref:"cropperDialogRef",[t.ui.dialog.visible]:t.dialogVisible,["onUpdate:"+t.ui.dialog.visible]:e[2]||(e[2]=c=>t.dialogVisible=c),"append-to-body":"",width:"900px","close-on-click-modal":!0},t.dialogBinding,{"destroy-on-close":!1}),{footer:Wt(()=>[X("div",Vi,[Dt(o,{size:"small",text:t.computedTexts.cancel,onClick:t.handleClose},null,8,["text","onClick"]),Dt(o,{type:"primary",size:"small",text:t.computedTexts.confirm,onClick:e[1]||(e[1]=c=>t.doCropper())},null,8,["text"])])]),default:Wt(()=>[X("div",Ii,[Et(X("input",{ref:"fileInputRef",type:"file",accept:t.accept,onChange:e[0]||(e[0]=(...c)=>t.handleChange&&t.handleChange(...c))},null,40,Pi),[[Rt,!1]]),Et(X("div",Ui,[Dt(o,{round:"",text:t.computedTexts.chooseImage,onClick:t.showFileChooser},null,8,["text","onClick"]),X("p",null,te(t.computedUploadTip),1)],512),[[Rt,!t.isLoaded]]),Et(X("div",_i,[X("div",$i,[Dt(l,Ht({ref:"cropperRef",src:t.imgSrc,preview:".preview",style:{height:t._cropperHeight}},t._cropper),null,16,["src","style"])]),X("div",Fi,[(Ct(),St(ee(t.ui.buttonGroup.name),null,{default:Wt(()=>[(Ct(!0),Pe(Ue,null,_e(t.computedButtons,(c,s)=>(Ct(),St(o,Ht({key:s,ref_for:!0},c),null,16))),128))]),_:1}))])],512),[[Rt,t.isLoaded]]),X("div",qi,[X("span",Qi,te(t.computedTexts.preview),1),e[3]||(e[3]=X("div",{class:"fs-cropper-dialog__preview-120 preview"},null,-1)),X("div",{class:$e(["fs-cropper-dialog__preview-65 preview",{round:t._cropper.aspectRatio===1}])},null,2)])])]),_:1},16)}const Gi=Fe(ji,[["render",Zi]]);export{Gi as default};
