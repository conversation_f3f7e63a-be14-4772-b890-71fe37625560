{"version": 3, "sources": ["../../.pnpm/js-yaml@4.1.0/node_modules/js-yaml/dist/js-yaml.mjs"], "sourcesContent": ["\n/*! js-yaml 4.1.0 https://github.com/nodeca/js-yaml @license MIT */\nfunction isNothing(subject) {\n  return (typeof subject === 'undefined') || (subject === null);\n}\n\n\nfunction isObject(subject) {\n  return (typeof subject === 'object') && (subject !== null);\n}\n\n\nfunction toArray(sequence) {\n  if (Array.isArray(sequence)) return sequence;\n  else if (isNothing(sequence)) return [];\n\n  return [ sequence ];\n}\n\n\nfunction extend(target, source) {\n  var index, length, key, sourceKeys;\n\n  if (source) {\n    sourceKeys = Object.keys(source);\n\n    for (index = 0, length = sourceKeys.length; index < length; index += 1) {\n      key = sourceKeys[index];\n      target[key] = source[key];\n    }\n  }\n\n  return target;\n}\n\n\nfunction repeat(string, count) {\n  var result = '', cycle;\n\n  for (cycle = 0; cycle < count; cycle += 1) {\n    result += string;\n  }\n\n  return result;\n}\n\n\nfunction isNegativeZero(number) {\n  return (number === 0) && (Number.NEGATIVE_INFINITY === 1 / number);\n}\n\n\nvar isNothing_1      = isNothing;\nvar isObject_1       = isObject;\nvar toArray_1        = toArray;\nvar repeat_1         = repeat;\nvar isNegativeZero_1 = isNegativeZero;\nvar extend_1         = extend;\n\nvar common = {\n\tisNothing: isNothing_1,\n\tisObject: isObject_1,\n\ttoArray: toArray_1,\n\trepeat: repeat_1,\n\tisNegativeZero: isNegativeZero_1,\n\textend: extend_1\n};\n\n// YAML error class. http://stackoverflow.com/questions/8458984\n\n\nfunction formatError(exception, compact) {\n  var where = '', message = exception.reason || '(unknown reason)';\n\n  if (!exception.mark) return message;\n\n  if (exception.mark.name) {\n    where += 'in \"' + exception.mark.name + '\" ';\n  }\n\n  where += '(' + (exception.mark.line + 1) + ':' + (exception.mark.column + 1) + ')';\n\n  if (!compact && exception.mark.snippet) {\n    where += '\\n\\n' + exception.mark.snippet;\n  }\n\n  return message + ' ' + where;\n}\n\n\nfunction YAMLException$1(reason, mark) {\n  // Super constructor\n  Error.call(this);\n\n  this.name = 'YAMLException';\n  this.reason = reason;\n  this.mark = mark;\n  this.message = formatError(this, false);\n\n  // Include stack trace in error object\n  if (Error.captureStackTrace) {\n    // Chrome and NodeJS\n    Error.captureStackTrace(this, this.constructor);\n  } else {\n    // FF, IE 10+ and Safari 6+. Fallback for others\n    this.stack = (new Error()).stack || '';\n  }\n}\n\n\n// Inherit from Error\nYAMLException$1.prototype = Object.create(Error.prototype);\nYAMLException$1.prototype.constructor = YAMLException$1;\n\n\nYAMLException$1.prototype.toString = function toString(compact) {\n  return this.name + ': ' + formatError(this, compact);\n};\n\n\nvar exception = YAMLException$1;\n\n// get snippet for a single line, respecting maxLength\nfunction getLine(buffer, lineStart, lineEnd, position, maxLineLength) {\n  var head = '';\n  var tail = '';\n  var maxHalfLength = Math.floor(maxLineLength / 2) - 1;\n\n  if (position - lineStart > maxHalfLength) {\n    head = ' ... ';\n    lineStart = position - maxHalfLength + head.length;\n  }\n\n  if (lineEnd - position > maxHalfLength) {\n    tail = ' ...';\n    lineEnd = position + maxHalfLength - tail.length;\n  }\n\n  return {\n    str: head + buffer.slice(lineStart, lineEnd).replace(/\\t/g, '→') + tail,\n    pos: position - lineStart + head.length // relative position\n  };\n}\n\n\nfunction padStart(string, max) {\n  return common.repeat(' ', max - string.length) + string;\n}\n\n\nfunction makeSnippet(mark, options) {\n  options = Object.create(options || null);\n\n  if (!mark.buffer) return null;\n\n  if (!options.maxLength) options.maxLength = 79;\n  if (typeof options.indent      !== 'number') options.indent      = 1;\n  if (typeof options.linesBefore !== 'number') options.linesBefore = 3;\n  if (typeof options.linesAfter  !== 'number') options.linesAfter  = 2;\n\n  var re = /\\r?\\n|\\r|\\0/g;\n  var lineStarts = [ 0 ];\n  var lineEnds = [];\n  var match;\n  var foundLineNo = -1;\n\n  while ((match = re.exec(mark.buffer))) {\n    lineEnds.push(match.index);\n    lineStarts.push(match.index + match[0].length);\n\n    if (mark.position <= match.index && foundLineNo < 0) {\n      foundLineNo = lineStarts.length - 2;\n    }\n  }\n\n  if (foundLineNo < 0) foundLineNo = lineStarts.length - 1;\n\n  var result = '', i, line;\n  var lineNoLength = Math.min(mark.line + options.linesAfter, lineEnds.length).toString().length;\n  var maxLineLength = options.maxLength - (options.indent + lineNoLength + 3);\n\n  for (i = 1; i <= options.linesBefore; i++) {\n    if (foundLineNo - i < 0) break;\n    line = getLine(\n      mark.buffer,\n      lineStarts[foundLineNo - i],\n      lineEnds[foundLineNo - i],\n      mark.position - (lineStarts[foundLineNo] - lineStarts[foundLineNo - i]),\n      maxLineLength\n    );\n    result = common.repeat(' ', options.indent) + padStart((mark.line - i + 1).toString(), lineNoLength) +\n      ' | ' + line.str + '\\n' + result;\n  }\n\n  line = getLine(mark.buffer, lineStarts[foundLineNo], lineEnds[foundLineNo], mark.position, maxLineLength);\n  result += common.repeat(' ', options.indent) + padStart((mark.line + 1).toString(), lineNoLength) +\n    ' | ' + line.str + '\\n';\n  result += common.repeat('-', options.indent + lineNoLength + 3 + line.pos) + '^' + '\\n';\n\n  for (i = 1; i <= options.linesAfter; i++) {\n    if (foundLineNo + i >= lineEnds.length) break;\n    line = getLine(\n      mark.buffer,\n      lineStarts[foundLineNo + i],\n      lineEnds[foundLineNo + i],\n      mark.position - (lineStarts[foundLineNo] - lineStarts[foundLineNo + i]),\n      maxLineLength\n    );\n    result += common.repeat(' ', options.indent) + padStart((mark.line + i + 1).toString(), lineNoLength) +\n      ' | ' + line.str + '\\n';\n  }\n\n  return result.replace(/\\n$/, '');\n}\n\n\nvar snippet = makeSnippet;\n\nvar TYPE_CONSTRUCTOR_OPTIONS = [\n  'kind',\n  'multi',\n  'resolve',\n  'construct',\n  'instanceOf',\n  'predicate',\n  'represent',\n  'representName',\n  'defaultStyle',\n  'styleAliases'\n];\n\nvar YAML_NODE_KINDS = [\n  'scalar',\n  'sequence',\n  'mapping'\n];\n\nfunction compileStyleAliases(map) {\n  var result = {};\n\n  if (map !== null) {\n    Object.keys(map).forEach(function (style) {\n      map[style].forEach(function (alias) {\n        result[String(alias)] = style;\n      });\n    });\n  }\n\n  return result;\n}\n\nfunction Type$1(tag, options) {\n  options = options || {};\n\n  Object.keys(options).forEach(function (name) {\n    if (TYPE_CONSTRUCTOR_OPTIONS.indexOf(name) === -1) {\n      throw new exception('Unknown option \"' + name + '\" is met in definition of \"' + tag + '\" YAML type.');\n    }\n  });\n\n  // TODO: Add tag format check.\n  this.options       = options; // keep original options in case user wants to extend this type later\n  this.tag           = tag;\n  this.kind          = options['kind']          || null;\n  this.resolve       = options['resolve']       || function () { return true; };\n  this.construct     = options['construct']     || function (data) { return data; };\n  this.instanceOf    = options['instanceOf']    || null;\n  this.predicate     = options['predicate']     || null;\n  this.represent     = options['represent']     || null;\n  this.representName = options['representName'] || null;\n  this.defaultStyle  = options['defaultStyle']  || null;\n  this.multi         = options['multi']         || false;\n  this.styleAliases  = compileStyleAliases(options['styleAliases'] || null);\n\n  if (YAML_NODE_KINDS.indexOf(this.kind) === -1) {\n    throw new exception('Unknown kind \"' + this.kind + '\" is specified for \"' + tag + '\" YAML type.');\n  }\n}\n\nvar type = Type$1;\n\n/*eslint-disable max-len*/\n\n\n\n\n\nfunction compileList(schema, name) {\n  var result = [];\n\n  schema[name].forEach(function (currentType) {\n    var newIndex = result.length;\n\n    result.forEach(function (previousType, previousIndex) {\n      if (previousType.tag === currentType.tag &&\n          previousType.kind === currentType.kind &&\n          previousType.multi === currentType.multi) {\n\n        newIndex = previousIndex;\n      }\n    });\n\n    result[newIndex] = currentType;\n  });\n\n  return result;\n}\n\n\nfunction compileMap(/* lists... */) {\n  var result = {\n        scalar: {},\n        sequence: {},\n        mapping: {},\n        fallback: {},\n        multi: {\n          scalar: [],\n          sequence: [],\n          mapping: [],\n          fallback: []\n        }\n      }, index, length;\n\n  function collectType(type) {\n    if (type.multi) {\n      result.multi[type.kind].push(type);\n      result.multi['fallback'].push(type);\n    } else {\n      result[type.kind][type.tag] = result['fallback'][type.tag] = type;\n    }\n  }\n\n  for (index = 0, length = arguments.length; index < length; index += 1) {\n    arguments[index].forEach(collectType);\n  }\n  return result;\n}\n\n\nfunction Schema$1(definition) {\n  return this.extend(definition);\n}\n\n\nSchema$1.prototype.extend = function extend(definition) {\n  var implicit = [];\n  var explicit = [];\n\n  if (definition instanceof type) {\n    // Schema.extend(type)\n    explicit.push(definition);\n\n  } else if (Array.isArray(definition)) {\n    // Schema.extend([ type1, type2, ... ])\n    explicit = explicit.concat(definition);\n\n  } else if (definition && (Array.isArray(definition.implicit) || Array.isArray(definition.explicit))) {\n    // Schema.extend({ explicit: [ type1, type2, ... ], implicit: [ type1, type2, ... ] })\n    if (definition.implicit) implicit = implicit.concat(definition.implicit);\n    if (definition.explicit) explicit = explicit.concat(definition.explicit);\n\n  } else {\n    throw new exception('Schema.extend argument should be a Type, [ Type ], ' +\n      'or a schema definition ({ implicit: [...], explicit: [...] })');\n  }\n\n  implicit.forEach(function (type$1) {\n    if (!(type$1 instanceof type)) {\n      throw new exception('Specified list of YAML types (or a single Type object) contains a non-Type object.');\n    }\n\n    if (type$1.loadKind && type$1.loadKind !== 'scalar') {\n      throw new exception('There is a non-scalar type in the implicit list of a schema. Implicit resolving of such types is not supported.');\n    }\n\n    if (type$1.multi) {\n      throw new exception('There is a multi type in the implicit list of a schema. Multi tags can only be listed as explicit.');\n    }\n  });\n\n  explicit.forEach(function (type$1) {\n    if (!(type$1 instanceof type)) {\n      throw new exception('Specified list of YAML types (or a single Type object) contains a non-Type object.');\n    }\n  });\n\n  var result = Object.create(Schema$1.prototype);\n\n  result.implicit = (this.implicit || []).concat(implicit);\n  result.explicit = (this.explicit || []).concat(explicit);\n\n  result.compiledImplicit = compileList(result, 'implicit');\n  result.compiledExplicit = compileList(result, 'explicit');\n  result.compiledTypeMap  = compileMap(result.compiledImplicit, result.compiledExplicit);\n\n  return result;\n};\n\n\nvar schema = Schema$1;\n\nvar str = new type('tag:yaml.org,2002:str', {\n  kind: 'scalar',\n  construct: function (data) { return data !== null ? data : ''; }\n});\n\nvar seq = new type('tag:yaml.org,2002:seq', {\n  kind: 'sequence',\n  construct: function (data) { return data !== null ? data : []; }\n});\n\nvar map = new type('tag:yaml.org,2002:map', {\n  kind: 'mapping',\n  construct: function (data) { return data !== null ? data : {}; }\n});\n\nvar failsafe = new schema({\n  explicit: [\n    str,\n    seq,\n    map\n  ]\n});\n\nfunction resolveYamlNull(data) {\n  if (data === null) return true;\n\n  var max = data.length;\n\n  return (max === 1 && data === '~') ||\n         (max === 4 && (data === 'null' || data === 'Null' || data === 'NULL'));\n}\n\nfunction constructYamlNull() {\n  return null;\n}\n\nfunction isNull(object) {\n  return object === null;\n}\n\nvar _null = new type('tag:yaml.org,2002:null', {\n  kind: 'scalar',\n  resolve: resolveYamlNull,\n  construct: constructYamlNull,\n  predicate: isNull,\n  represent: {\n    canonical: function () { return '~';    },\n    lowercase: function () { return 'null'; },\n    uppercase: function () { return 'NULL'; },\n    camelcase: function () { return 'Null'; },\n    empty:     function () { return '';     }\n  },\n  defaultStyle: 'lowercase'\n});\n\nfunction resolveYamlBoolean(data) {\n  if (data === null) return false;\n\n  var max = data.length;\n\n  return (max === 4 && (data === 'true' || data === 'True' || data === 'TRUE')) ||\n         (max === 5 && (data === 'false' || data === 'False' || data === 'FALSE'));\n}\n\nfunction constructYamlBoolean(data) {\n  return data === 'true' ||\n         data === 'True' ||\n         data === 'TRUE';\n}\n\nfunction isBoolean(object) {\n  return Object.prototype.toString.call(object) === '[object Boolean]';\n}\n\nvar bool = new type('tag:yaml.org,2002:bool', {\n  kind: 'scalar',\n  resolve: resolveYamlBoolean,\n  construct: constructYamlBoolean,\n  predicate: isBoolean,\n  represent: {\n    lowercase: function (object) { return object ? 'true' : 'false'; },\n    uppercase: function (object) { return object ? 'TRUE' : 'FALSE'; },\n    camelcase: function (object) { return object ? 'True' : 'False'; }\n  },\n  defaultStyle: 'lowercase'\n});\n\nfunction isHexCode(c) {\n  return ((0x30/* 0 */ <= c) && (c <= 0x39/* 9 */)) ||\n         ((0x41/* A */ <= c) && (c <= 0x46/* F */)) ||\n         ((0x61/* a */ <= c) && (c <= 0x66/* f */));\n}\n\nfunction isOctCode(c) {\n  return ((0x30/* 0 */ <= c) && (c <= 0x37/* 7 */));\n}\n\nfunction isDecCode(c) {\n  return ((0x30/* 0 */ <= c) && (c <= 0x39/* 9 */));\n}\n\nfunction resolveYamlInteger(data) {\n  if (data === null) return false;\n\n  var max = data.length,\n      index = 0,\n      hasDigits = false,\n      ch;\n\n  if (!max) return false;\n\n  ch = data[index];\n\n  // sign\n  if (ch === '-' || ch === '+') {\n    ch = data[++index];\n  }\n\n  if (ch === '0') {\n    // 0\n    if (index + 1 === max) return true;\n    ch = data[++index];\n\n    // base 2, base 8, base 16\n\n    if (ch === 'b') {\n      // base 2\n      index++;\n\n      for (; index < max; index++) {\n        ch = data[index];\n        if (ch === '_') continue;\n        if (ch !== '0' && ch !== '1') return false;\n        hasDigits = true;\n      }\n      return hasDigits && ch !== '_';\n    }\n\n\n    if (ch === 'x') {\n      // base 16\n      index++;\n\n      for (; index < max; index++) {\n        ch = data[index];\n        if (ch === '_') continue;\n        if (!isHexCode(data.charCodeAt(index))) return false;\n        hasDigits = true;\n      }\n      return hasDigits && ch !== '_';\n    }\n\n\n    if (ch === 'o') {\n      // base 8\n      index++;\n\n      for (; index < max; index++) {\n        ch = data[index];\n        if (ch === '_') continue;\n        if (!isOctCode(data.charCodeAt(index))) return false;\n        hasDigits = true;\n      }\n      return hasDigits && ch !== '_';\n    }\n  }\n\n  // base 10 (except 0)\n\n  // value should not start with `_`;\n  if (ch === '_') return false;\n\n  for (; index < max; index++) {\n    ch = data[index];\n    if (ch === '_') continue;\n    if (!isDecCode(data.charCodeAt(index))) {\n      return false;\n    }\n    hasDigits = true;\n  }\n\n  // Should have digits and should not end with `_`\n  if (!hasDigits || ch === '_') return false;\n\n  return true;\n}\n\nfunction constructYamlInteger(data) {\n  var value = data, sign = 1, ch;\n\n  if (value.indexOf('_') !== -1) {\n    value = value.replace(/_/g, '');\n  }\n\n  ch = value[0];\n\n  if (ch === '-' || ch === '+') {\n    if (ch === '-') sign = -1;\n    value = value.slice(1);\n    ch = value[0];\n  }\n\n  if (value === '0') return 0;\n\n  if (ch === '0') {\n    if (value[1] === 'b') return sign * parseInt(value.slice(2), 2);\n    if (value[1] === 'x') return sign * parseInt(value.slice(2), 16);\n    if (value[1] === 'o') return sign * parseInt(value.slice(2), 8);\n  }\n\n  return sign * parseInt(value, 10);\n}\n\nfunction isInteger(object) {\n  return (Object.prototype.toString.call(object)) === '[object Number]' &&\n         (object % 1 === 0 && !common.isNegativeZero(object));\n}\n\nvar int = new type('tag:yaml.org,2002:int', {\n  kind: 'scalar',\n  resolve: resolveYamlInteger,\n  construct: constructYamlInteger,\n  predicate: isInteger,\n  represent: {\n    binary:      function (obj) { return obj >= 0 ? '0b' + obj.toString(2) : '-0b' + obj.toString(2).slice(1); },\n    octal:       function (obj) { return obj >= 0 ? '0o'  + obj.toString(8) : '-0o'  + obj.toString(8).slice(1); },\n    decimal:     function (obj) { return obj.toString(10); },\n    /* eslint-disable max-len */\n    hexadecimal: function (obj) { return obj >= 0 ? '0x' + obj.toString(16).toUpperCase() :  '-0x' + obj.toString(16).toUpperCase().slice(1); }\n  },\n  defaultStyle: 'decimal',\n  styleAliases: {\n    binary:      [ 2,  'bin' ],\n    octal:       [ 8,  'oct' ],\n    decimal:     [ 10, 'dec' ],\n    hexadecimal: [ 16, 'hex' ]\n  }\n});\n\nvar YAML_FLOAT_PATTERN = new RegExp(\n  // 2.5e4, 2.5 and integers\n  '^(?:[-+]?(?:[0-9][0-9_]*)(?:\\\\.[0-9_]*)?(?:[eE][-+]?[0-9]+)?' +\n  // .2e4, .2\n  // special case, seems not from spec\n  '|\\\\.[0-9_]+(?:[eE][-+]?[0-9]+)?' +\n  // .inf\n  '|[-+]?\\\\.(?:inf|Inf|INF)' +\n  // .nan\n  '|\\\\.(?:nan|NaN|NAN))$');\n\nfunction resolveYamlFloat(data) {\n  if (data === null) return false;\n\n  if (!YAML_FLOAT_PATTERN.test(data) ||\n      // Quick hack to not allow integers end with `_`\n      // Probably should update regexp & check speed\n      data[data.length - 1] === '_') {\n    return false;\n  }\n\n  return true;\n}\n\nfunction constructYamlFloat(data) {\n  var value, sign;\n\n  value  = data.replace(/_/g, '').toLowerCase();\n  sign   = value[0] === '-' ? -1 : 1;\n\n  if ('+-'.indexOf(value[0]) >= 0) {\n    value = value.slice(1);\n  }\n\n  if (value === '.inf') {\n    return (sign === 1) ? Number.POSITIVE_INFINITY : Number.NEGATIVE_INFINITY;\n\n  } else if (value === '.nan') {\n    return NaN;\n  }\n  return sign * parseFloat(value, 10);\n}\n\n\nvar SCIENTIFIC_WITHOUT_DOT = /^[-+]?[0-9]+e/;\n\nfunction representYamlFloat(object, style) {\n  var res;\n\n  if (isNaN(object)) {\n    switch (style) {\n      case 'lowercase': return '.nan';\n      case 'uppercase': return '.NAN';\n      case 'camelcase': return '.NaN';\n    }\n  } else if (Number.POSITIVE_INFINITY === object) {\n    switch (style) {\n      case 'lowercase': return '.inf';\n      case 'uppercase': return '.INF';\n      case 'camelcase': return '.Inf';\n    }\n  } else if (Number.NEGATIVE_INFINITY === object) {\n    switch (style) {\n      case 'lowercase': return '-.inf';\n      case 'uppercase': return '-.INF';\n      case 'camelcase': return '-.Inf';\n    }\n  } else if (common.isNegativeZero(object)) {\n    return '-0.0';\n  }\n\n  res = object.toString(10);\n\n  // JS stringifier can build scientific format without dots: 5e-100,\n  // while YAML requres dot: 5.e-100. Fix it with simple hack\n\n  return SCIENTIFIC_WITHOUT_DOT.test(res) ? res.replace('e', '.e') : res;\n}\n\nfunction isFloat(object) {\n  return (Object.prototype.toString.call(object) === '[object Number]') &&\n         (object % 1 !== 0 || common.isNegativeZero(object));\n}\n\nvar float = new type('tag:yaml.org,2002:float', {\n  kind: 'scalar',\n  resolve: resolveYamlFloat,\n  construct: constructYamlFloat,\n  predicate: isFloat,\n  represent: representYamlFloat,\n  defaultStyle: 'lowercase'\n});\n\nvar json = failsafe.extend({\n  implicit: [\n    _null,\n    bool,\n    int,\n    float\n  ]\n});\n\nvar core = json;\n\nvar YAML_DATE_REGEXP = new RegExp(\n  '^([0-9][0-9][0-9][0-9])'          + // [1] year\n  '-([0-9][0-9])'                    + // [2] month\n  '-([0-9][0-9])$');                   // [3] day\n\nvar YAML_TIMESTAMP_REGEXP = new RegExp(\n  '^([0-9][0-9][0-9][0-9])'          + // [1] year\n  '-([0-9][0-9]?)'                   + // [2] month\n  '-([0-9][0-9]?)'                   + // [3] day\n  '(?:[Tt]|[ \\\\t]+)'                 + // ...\n  '([0-9][0-9]?)'                    + // [4] hour\n  ':([0-9][0-9])'                    + // [5] minute\n  ':([0-9][0-9])'                    + // [6] second\n  '(?:\\\\.([0-9]*))?'                 + // [7] fraction\n  '(?:[ \\\\t]*(Z|([-+])([0-9][0-9]?)' + // [8] tz [9] tz_sign [10] tz_hour\n  '(?::([0-9][0-9]))?))?$');           // [11] tz_minute\n\nfunction resolveYamlTimestamp(data) {\n  if (data === null) return false;\n  if (YAML_DATE_REGEXP.exec(data) !== null) return true;\n  if (YAML_TIMESTAMP_REGEXP.exec(data) !== null) return true;\n  return false;\n}\n\nfunction constructYamlTimestamp(data) {\n  var match, year, month, day, hour, minute, second, fraction = 0,\n      delta = null, tz_hour, tz_minute, date;\n\n  match = YAML_DATE_REGEXP.exec(data);\n  if (match === null) match = YAML_TIMESTAMP_REGEXP.exec(data);\n\n  if (match === null) throw new Error('Date resolve error');\n\n  // match: [1] year [2] month [3] day\n\n  year = +(match[1]);\n  month = +(match[2]) - 1; // JS month starts with 0\n  day = +(match[3]);\n\n  if (!match[4]) { // no hour\n    return new Date(Date.UTC(year, month, day));\n  }\n\n  // match: [4] hour [5] minute [6] second [7] fraction\n\n  hour = +(match[4]);\n  minute = +(match[5]);\n  second = +(match[6]);\n\n  if (match[7]) {\n    fraction = match[7].slice(0, 3);\n    while (fraction.length < 3) { // milli-seconds\n      fraction += '0';\n    }\n    fraction = +fraction;\n  }\n\n  // match: [8] tz [9] tz_sign [10] tz_hour [11] tz_minute\n\n  if (match[9]) {\n    tz_hour = +(match[10]);\n    tz_minute = +(match[11] || 0);\n    delta = (tz_hour * 60 + tz_minute) * 60000; // delta in mili-seconds\n    if (match[9] === '-') delta = -delta;\n  }\n\n  date = new Date(Date.UTC(year, month, day, hour, minute, second, fraction));\n\n  if (delta) date.setTime(date.getTime() - delta);\n\n  return date;\n}\n\nfunction representYamlTimestamp(object /*, style*/) {\n  return object.toISOString();\n}\n\nvar timestamp = new type('tag:yaml.org,2002:timestamp', {\n  kind: 'scalar',\n  resolve: resolveYamlTimestamp,\n  construct: constructYamlTimestamp,\n  instanceOf: Date,\n  represent: representYamlTimestamp\n});\n\nfunction resolveYamlMerge(data) {\n  return data === '<<' || data === null;\n}\n\nvar merge = new type('tag:yaml.org,2002:merge', {\n  kind: 'scalar',\n  resolve: resolveYamlMerge\n});\n\n/*eslint-disable no-bitwise*/\n\n\n\n\n\n// [ 64, 65, 66 ] -> [ padding, CR, LF ]\nvar BASE64_MAP = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\\n\\r';\n\n\nfunction resolveYamlBinary(data) {\n  if (data === null) return false;\n\n  var code, idx, bitlen = 0, max = data.length, map = BASE64_MAP;\n\n  // Convert one by one.\n  for (idx = 0; idx < max; idx++) {\n    code = map.indexOf(data.charAt(idx));\n\n    // Skip CR/LF\n    if (code > 64) continue;\n\n    // Fail on illegal characters\n    if (code < 0) return false;\n\n    bitlen += 6;\n  }\n\n  // If there are any bits left, source was corrupted\n  return (bitlen % 8) === 0;\n}\n\nfunction constructYamlBinary(data) {\n  var idx, tailbits,\n      input = data.replace(/[\\r\\n=]/g, ''), // remove CR/LF & padding to simplify scan\n      max = input.length,\n      map = BASE64_MAP,\n      bits = 0,\n      result = [];\n\n  // Collect by 6*4 bits (3 bytes)\n\n  for (idx = 0; idx < max; idx++) {\n    if ((idx % 4 === 0) && idx) {\n      result.push((bits >> 16) & 0xFF);\n      result.push((bits >> 8) & 0xFF);\n      result.push(bits & 0xFF);\n    }\n\n    bits = (bits << 6) | map.indexOf(input.charAt(idx));\n  }\n\n  // Dump tail\n\n  tailbits = (max % 4) * 6;\n\n  if (tailbits === 0) {\n    result.push((bits >> 16) & 0xFF);\n    result.push((bits >> 8) & 0xFF);\n    result.push(bits & 0xFF);\n  } else if (tailbits === 18) {\n    result.push((bits >> 10) & 0xFF);\n    result.push((bits >> 2) & 0xFF);\n  } else if (tailbits === 12) {\n    result.push((bits >> 4) & 0xFF);\n  }\n\n  return new Uint8Array(result);\n}\n\nfunction representYamlBinary(object /*, style*/) {\n  var result = '', bits = 0, idx, tail,\n      max = object.length,\n      map = BASE64_MAP;\n\n  // Convert every three bytes to 4 ASCII characters.\n\n  for (idx = 0; idx < max; idx++) {\n    if ((idx % 3 === 0) && idx) {\n      result += map[(bits >> 18) & 0x3F];\n      result += map[(bits >> 12) & 0x3F];\n      result += map[(bits >> 6) & 0x3F];\n      result += map[bits & 0x3F];\n    }\n\n    bits = (bits << 8) + object[idx];\n  }\n\n  // Dump tail\n\n  tail = max % 3;\n\n  if (tail === 0) {\n    result += map[(bits >> 18) & 0x3F];\n    result += map[(bits >> 12) & 0x3F];\n    result += map[(bits >> 6) & 0x3F];\n    result += map[bits & 0x3F];\n  } else if (tail === 2) {\n    result += map[(bits >> 10) & 0x3F];\n    result += map[(bits >> 4) & 0x3F];\n    result += map[(bits << 2) & 0x3F];\n    result += map[64];\n  } else if (tail === 1) {\n    result += map[(bits >> 2) & 0x3F];\n    result += map[(bits << 4) & 0x3F];\n    result += map[64];\n    result += map[64];\n  }\n\n  return result;\n}\n\nfunction isBinary(obj) {\n  return Object.prototype.toString.call(obj) ===  '[object Uint8Array]';\n}\n\nvar binary = new type('tag:yaml.org,2002:binary', {\n  kind: 'scalar',\n  resolve: resolveYamlBinary,\n  construct: constructYamlBinary,\n  predicate: isBinary,\n  represent: representYamlBinary\n});\n\nvar _hasOwnProperty$3 = Object.prototype.hasOwnProperty;\nvar _toString$2       = Object.prototype.toString;\n\nfunction resolveYamlOmap(data) {\n  if (data === null) return true;\n\n  var objectKeys = [], index, length, pair, pairKey, pairHasKey,\n      object = data;\n\n  for (index = 0, length = object.length; index < length; index += 1) {\n    pair = object[index];\n    pairHasKey = false;\n\n    if (_toString$2.call(pair) !== '[object Object]') return false;\n\n    for (pairKey in pair) {\n      if (_hasOwnProperty$3.call(pair, pairKey)) {\n        if (!pairHasKey) pairHasKey = true;\n        else return false;\n      }\n    }\n\n    if (!pairHasKey) return false;\n\n    if (objectKeys.indexOf(pairKey) === -1) objectKeys.push(pairKey);\n    else return false;\n  }\n\n  return true;\n}\n\nfunction constructYamlOmap(data) {\n  return data !== null ? data : [];\n}\n\nvar omap = new type('tag:yaml.org,2002:omap', {\n  kind: 'sequence',\n  resolve: resolveYamlOmap,\n  construct: constructYamlOmap\n});\n\nvar _toString$1 = Object.prototype.toString;\n\nfunction resolveYamlPairs(data) {\n  if (data === null) return true;\n\n  var index, length, pair, keys, result,\n      object = data;\n\n  result = new Array(object.length);\n\n  for (index = 0, length = object.length; index < length; index += 1) {\n    pair = object[index];\n\n    if (_toString$1.call(pair) !== '[object Object]') return false;\n\n    keys = Object.keys(pair);\n\n    if (keys.length !== 1) return false;\n\n    result[index] = [ keys[0], pair[keys[0]] ];\n  }\n\n  return true;\n}\n\nfunction constructYamlPairs(data) {\n  if (data === null) return [];\n\n  var index, length, pair, keys, result,\n      object = data;\n\n  result = new Array(object.length);\n\n  for (index = 0, length = object.length; index < length; index += 1) {\n    pair = object[index];\n\n    keys = Object.keys(pair);\n\n    result[index] = [ keys[0], pair[keys[0]] ];\n  }\n\n  return result;\n}\n\nvar pairs = new type('tag:yaml.org,2002:pairs', {\n  kind: 'sequence',\n  resolve: resolveYamlPairs,\n  construct: constructYamlPairs\n});\n\nvar _hasOwnProperty$2 = Object.prototype.hasOwnProperty;\n\nfunction resolveYamlSet(data) {\n  if (data === null) return true;\n\n  var key, object = data;\n\n  for (key in object) {\n    if (_hasOwnProperty$2.call(object, key)) {\n      if (object[key] !== null) return false;\n    }\n  }\n\n  return true;\n}\n\nfunction constructYamlSet(data) {\n  return data !== null ? data : {};\n}\n\nvar set = new type('tag:yaml.org,2002:set', {\n  kind: 'mapping',\n  resolve: resolveYamlSet,\n  construct: constructYamlSet\n});\n\nvar _default = core.extend({\n  implicit: [\n    timestamp,\n    merge\n  ],\n  explicit: [\n    binary,\n    omap,\n    pairs,\n    set\n  ]\n});\n\n/*eslint-disable max-len,no-use-before-define*/\n\n\n\n\n\n\n\nvar _hasOwnProperty$1 = Object.prototype.hasOwnProperty;\n\n\nvar CONTEXT_FLOW_IN   = 1;\nvar CONTEXT_FLOW_OUT  = 2;\nvar CONTEXT_BLOCK_IN  = 3;\nvar CONTEXT_BLOCK_OUT = 4;\n\n\nvar CHOMPING_CLIP  = 1;\nvar CHOMPING_STRIP = 2;\nvar CHOMPING_KEEP  = 3;\n\n\nvar PATTERN_NON_PRINTABLE         = /[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F-\\x84\\x86-\\x9F\\uFFFE\\uFFFF]|[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])|(?:[^\\uD800-\\uDBFF]|^)[\\uDC00-\\uDFFF]/;\nvar PATTERN_NON_ASCII_LINE_BREAKS = /[\\x85\\u2028\\u2029]/;\nvar PATTERN_FLOW_INDICATORS       = /[,\\[\\]\\{\\}]/;\nvar PATTERN_TAG_HANDLE            = /^(?:!|!!|![a-z\\-]+!)$/i;\nvar PATTERN_TAG_URI               = /^(?:!|[^,\\[\\]\\{\\}])(?:%[0-9a-f]{2}|[0-9a-z\\-#;\\/\\?:@&=\\+\\$,_\\.!~\\*'\\(\\)\\[\\]])*$/i;\n\n\nfunction _class(obj) { return Object.prototype.toString.call(obj); }\n\nfunction is_EOL(c) {\n  return (c === 0x0A/* LF */) || (c === 0x0D/* CR */);\n}\n\nfunction is_WHITE_SPACE(c) {\n  return (c === 0x09/* Tab */) || (c === 0x20/* Space */);\n}\n\nfunction is_WS_OR_EOL(c) {\n  return (c === 0x09/* Tab */) ||\n         (c === 0x20/* Space */) ||\n         (c === 0x0A/* LF */) ||\n         (c === 0x0D/* CR */);\n}\n\nfunction is_FLOW_INDICATOR(c) {\n  return c === 0x2C/* , */ ||\n         c === 0x5B/* [ */ ||\n         c === 0x5D/* ] */ ||\n         c === 0x7B/* { */ ||\n         c === 0x7D/* } */;\n}\n\nfunction fromHexCode(c) {\n  var lc;\n\n  if ((0x30/* 0 */ <= c) && (c <= 0x39/* 9 */)) {\n    return c - 0x30;\n  }\n\n  /*eslint-disable no-bitwise*/\n  lc = c | 0x20;\n\n  if ((0x61/* a */ <= lc) && (lc <= 0x66/* f */)) {\n    return lc - 0x61 + 10;\n  }\n\n  return -1;\n}\n\nfunction escapedHexLen(c) {\n  if (c === 0x78/* x */) { return 2; }\n  if (c === 0x75/* u */) { return 4; }\n  if (c === 0x55/* U */) { return 8; }\n  return 0;\n}\n\nfunction fromDecimalCode(c) {\n  if ((0x30/* 0 */ <= c) && (c <= 0x39/* 9 */)) {\n    return c - 0x30;\n  }\n\n  return -1;\n}\n\nfunction simpleEscapeSequence(c) {\n  /* eslint-disable indent */\n  return (c === 0x30/* 0 */) ? '\\x00' :\n        (c === 0x61/* a */) ? '\\x07' :\n        (c === 0x62/* b */) ? '\\x08' :\n        (c === 0x74/* t */) ? '\\x09' :\n        (c === 0x09/* Tab */) ? '\\x09' :\n        (c === 0x6E/* n */) ? '\\x0A' :\n        (c === 0x76/* v */) ? '\\x0B' :\n        (c === 0x66/* f */) ? '\\x0C' :\n        (c === 0x72/* r */) ? '\\x0D' :\n        (c === 0x65/* e */) ? '\\x1B' :\n        (c === 0x20/* Space */) ? ' ' :\n        (c === 0x22/* \" */) ? '\\x22' :\n        (c === 0x2F/* / */) ? '/' :\n        (c === 0x5C/* \\ */) ? '\\x5C' :\n        (c === 0x4E/* N */) ? '\\x85' :\n        (c === 0x5F/* _ */) ? '\\xA0' :\n        (c === 0x4C/* L */) ? '\\u2028' :\n        (c === 0x50/* P */) ? '\\u2029' : '';\n}\n\nfunction charFromCodepoint(c) {\n  if (c <= 0xFFFF) {\n    return String.fromCharCode(c);\n  }\n  // Encode UTF-16 surrogate pair\n  // https://en.wikipedia.org/wiki/UTF-16#Code_points_U.2B010000_to_U.2B10FFFF\n  return String.fromCharCode(\n    ((c - 0x010000) >> 10) + 0xD800,\n    ((c - 0x010000) & 0x03FF) + 0xDC00\n  );\n}\n\nvar simpleEscapeCheck = new Array(256); // integer, for fast access\nvar simpleEscapeMap = new Array(256);\nfor (var i = 0; i < 256; i++) {\n  simpleEscapeCheck[i] = simpleEscapeSequence(i) ? 1 : 0;\n  simpleEscapeMap[i] = simpleEscapeSequence(i);\n}\n\n\nfunction State$1(input, options) {\n  this.input = input;\n\n  this.filename  = options['filename']  || null;\n  this.schema    = options['schema']    || _default;\n  this.onWarning = options['onWarning'] || null;\n  // (Hidden) Remove? makes the loader to expect YAML 1.1 documents\n  // if such documents have no explicit %YAML directive\n  this.legacy    = options['legacy']    || false;\n\n  this.json      = options['json']      || false;\n  this.listener  = options['listener']  || null;\n\n  this.implicitTypes = this.schema.compiledImplicit;\n  this.typeMap       = this.schema.compiledTypeMap;\n\n  this.length     = input.length;\n  this.position   = 0;\n  this.line       = 0;\n  this.lineStart  = 0;\n  this.lineIndent = 0;\n\n  // position of first leading tab in the current line,\n  // used to make sure there are no tabs in the indentation\n  this.firstTabInLine = -1;\n\n  this.documents = [];\n\n  /*\n  this.version;\n  this.checkLineBreaks;\n  this.tagMap;\n  this.anchorMap;\n  this.tag;\n  this.anchor;\n  this.kind;\n  this.result;*/\n\n}\n\n\nfunction generateError(state, message) {\n  var mark = {\n    name:     state.filename,\n    buffer:   state.input.slice(0, -1), // omit trailing \\0\n    position: state.position,\n    line:     state.line,\n    column:   state.position - state.lineStart\n  };\n\n  mark.snippet = snippet(mark);\n\n  return new exception(message, mark);\n}\n\nfunction throwError(state, message) {\n  throw generateError(state, message);\n}\n\nfunction throwWarning(state, message) {\n  if (state.onWarning) {\n    state.onWarning.call(null, generateError(state, message));\n  }\n}\n\n\nvar directiveHandlers = {\n\n  YAML: function handleYamlDirective(state, name, args) {\n\n    var match, major, minor;\n\n    if (state.version !== null) {\n      throwError(state, 'duplication of %YAML directive');\n    }\n\n    if (args.length !== 1) {\n      throwError(state, 'YAML directive accepts exactly one argument');\n    }\n\n    match = /^([0-9]+)\\.([0-9]+)$/.exec(args[0]);\n\n    if (match === null) {\n      throwError(state, 'ill-formed argument of the YAML directive');\n    }\n\n    major = parseInt(match[1], 10);\n    minor = parseInt(match[2], 10);\n\n    if (major !== 1) {\n      throwError(state, 'unacceptable YAML version of the document');\n    }\n\n    state.version = args[0];\n    state.checkLineBreaks = (minor < 2);\n\n    if (minor !== 1 && minor !== 2) {\n      throwWarning(state, 'unsupported YAML version of the document');\n    }\n  },\n\n  TAG: function handleTagDirective(state, name, args) {\n\n    var handle, prefix;\n\n    if (args.length !== 2) {\n      throwError(state, 'TAG directive accepts exactly two arguments');\n    }\n\n    handle = args[0];\n    prefix = args[1];\n\n    if (!PATTERN_TAG_HANDLE.test(handle)) {\n      throwError(state, 'ill-formed tag handle (first argument) of the TAG directive');\n    }\n\n    if (_hasOwnProperty$1.call(state.tagMap, handle)) {\n      throwError(state, 'there is a previously declared suffix for \"' + handle + '\" tag handle');\n    }\n\n    if (!PATTERN_TAG_URI.test(prefix)) {\n      throwError(state, 'ill-formed tag prefix (second argument) of the TAG directive');\n    }\n\n    try {\n      prefix = decodeURIComponent(prefix);\n    } catch (err) {\n      throwError(state, 'tag prefix is malformed: ' + prefix);\n    }\n\n    state.tagMap[handle] = prefix;\n  }\n};\n\n\nfunction captureSegment(state, start, end, checkJson) {\n  var _position, _length, _character, _result;\n\n  if (start < end) {\n    _result = state.input.slice(start, end);\n\n    if (checkJson) {\n      for (_position = 0, _length = _result.length; _position < _length; _position += 1) {\n        _character = _result.charCodeAt(_position);\n        if (!(_character === 0x09 ||\n              (0x20 <= _character && _character <= 0x10FFFF))) {\n          throwError(state, 'expected valid JSON character');\n        }\n      }\n    } else if (PATTERN_NON_PRINTABLE.test(_result)) {\n      throwError(state, 'the stream contains non-printable characters');\n    }\n\n    state.result += _result;\n  }\n}\n\nfunction mergeMappings(state, destination, source, overridableKeys) {\n  var sourceKeys, key, index, quantity;\n\n  if (!common.isObject(source)) {\n    throwError(state, 'cannot merge mappings; the provided source object is unacceptable');\n  }\n\n  sourceKeys = Object.keys(source);\n\n  for (index = 0, quantity = sourceKeys.length; index < quantity; index += 1) {\n    key = sourceKeys[index];\n\n    if (!_hasOwnProperty$1.call(destination, key)) {\n      destination[key] = source[key];\n      overridableKeys[key] = true;\n    }\n  }\n}\n\nfunction storeMappingPair(state, _result, overridableKeys, keyTag, keyNode, valueNode,\n  startLine, startLineStart, startPos) {\n\n  var index, quantity;\n\n  // The output is a plain object here, so keys can only be strings.\n  // We need to convert keyNode to a string, but doing so can hang the process\n  // (deeply nested arrays that explode exponentially using aliases).\n  if (Array.isArray(keyNode)) {\n    keyNode = Array.prototype.slice.call(keyNode);\n\n    for (index = 0, quantity = keyNode.length; index < quantity; index += 1) {\n      if (Array.isArray(keyNode[index])) {\n        throwError(state, 'nested arrays are not supported inside keys');\n      }\n\n      if (typeof keyNode === 'object' && _class(keyNode[index]) === '[object Object]') {\n        keyNode[index] = '[object Object]';\n      }\n    }\n  }\n\n  // Avoid code execution in load() via toString property\n  // (still use its own toString for arrays, timestamps,\n  // and whatever user schema extensions happen to have @@toStringTag)\n  if (typeof keyNode === 'object' && _class(keyNode) === '[object Object]') {\n    keyNode = '[object Object]';\n  }\n\n\n  keyNode = String(keyNode);\n\n  if (_result === null) {\n    _result = {};\n  }\n\n  if (keyTag === 'tag:yaml.org,2002:merge') {\n    if (Array.isArray(valueNode)) {\n      for (index = 0, quantity = valueNode.length; index < quantity; index += 1) {\n        mergeMappings(state, _result, valueNode[index], overridableKeys);\n      }\n    } else {\n      mergeMappings(state, _result, valueNode, overridableKeys);\n    }\n  } else {\n    if (!state.json &&\n        !_hasOwnProperty$1.call(overridableKeys, keyNode) &&\n        _hasOwnProperty$1.call(_result, keyNode)) {\n      state.line = startLine || state.line;\n      state.lineStart = startLineStart || state.lineStart;\n      state.position = startPos || state.position;\n      throwError(state, 'duplicated mapping key');\n    }\n\n    // used for this specific key only because Object.defineProperty is slow\n    if (keyNode === '__proto__') {\n      Object.defineProperty(_result, keyNode, {\n        configurable: true,\n        enumerable: true,\n        writable: true,\n        value: valueNode\n      });\n    } else {\n      _result[keyNode] = valueNode;\n    }\n    delete overridableKeys[keyNode];\n  }\n\n  return _result;\n}\n\nfunction readLineBreak(state) {\n  var ch;\n\n  ch = state.input.charCodeAt(state.position);\n\n  if (ch === 0x0A/* LF */) {\n    state.position++;\n  } else if (ch === 0x0D/* CR */) {\n    state.position++;\n    if (state.input.charCodeAt(state.position) === 0x0A/* LF */) {\n      state.position++;\n    }\n  } else {\n    throwError(state, 'a line break is expected');\n  }\n\n  state.line += 1;\n  state.lineStart = state.position;\n  state.firstTabInLine = -1;\n}\n\nfunction skipSeparationSpace(state, allowComments, checkIndent) {\n  var lineBreaks = 0,\n      ch = state.input.charCodeAt(state.position);\n\n  while (ch !== 0) {\n    while (is_WHITE_SPACE(ch)) {\n      if (ch === 0x09/* Tab */ && state.firstTabInLine === -1) {\n        state.firstTabInLine = state.position;\n      }\n      ch = state.input.charCodeAt(++state.position);\n    }\n\n    if (allowComments && ch === 0x23/* # */) {\n      do {\n        ch = state.input.charCodeAt(++state.position);\n      } while (ch !== 0x0A/* LF */ && ch !== 0x0D/* CR */ && ch !== 0);\n    }\n\n    if (is_EOL(ch)) {\n      readLineBreak(state);\n\n      ch = state.input.charCodeAt(state.position);\n      lineBreaks++;\n      state.lineIndent = 0;\n\n      while (ch === 0x20/* Space */) {\n        state.lineIndent++;\n        ch = state.input.charCodeAt(++state.position);\n      }\n    } else {\n      break;\n    }\n  }\n\n  if (checkIndent !== -1 && lineBreaks !== 0 && state.lineIndent < checkIndent) {\n    throwWarning(state, 'deficient indentation');\n  }\n\n  return lineBreaks;\n}\n\nfunction testDocumentSeparator(state) {\n  var _position = state.position,\n      ch;\n\n  ch = state.input.charCodeAt(_position);\n\n  // Condition state.position === state.lineStart is tested\n  // in parent on each call, for efficiency. No needs to test here again.\n  if ((ch === 0x2D/* - */ || ch === 0x2E/* . */) &&\n      ch === state.input.charCodeAt(_position + 1) &&\n      ch === state.input.charCodeAt(_position + 2)) {\n\n    _position += 3;\n\n    ch = state.input.charCodeAt(_position);\n\n    if (ch === 0 || is_WS_OR_EOL(ch)) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction writeFoldedLines(state, count) {\n  if (count === 1) {\n    state.result += ' ';\n  } else if (count > 1) {\n    state.result += common.repeat('\\n', count - 1);\n  }\n}\n\n\nfunction readPlainScalar(state, nodeIndent, withinFlowCollection) {\n  var preceding,\n      following,\n      captureStart,\n      captureEnd,\n      hasPendingContent,\n      _line,\n      _lineStart,\n      _lineIndent,\n      _kind = state.kind,\n      _result = state.result,\n      ch;\n\n  ch = state.input.charCodeAt(state.position);\n\n  if (is_WS_OR_EOL(ch)      ||\n      is_FLOW_INDICATOR(ch) ||\n      ch === 0x23/* # */    ||\n      ch === 0x26/* & */    ||\n      ch === 0x2A/* * */    ||\n      ch === 0x21/* ! */    ||\n      ch === 0x7C/* | */    ||\n      ch === 0x3E/* > */    ||\n      ch === 0x27/* ' */    ||\n      ch === 0x22/* \" */    ||\n      ch === 0x25/* % */    ||\n      ch === 0x40/* @ */    ||\n      ch === 0x60/* ` */) {\n    return false;\n  }\n\n  if (ch === 0x3F/* ? */ || ch === 0x2D/* - */) {\n    following = state.input.charCodeAt(state.position + 1);\n\n    if (is_WS_OR_EOL(following) ||\n        withinFlowCollection && is_FLOW_INDICATOR(following)) {\n      return false;\n    }\n  }\n\n  state.kind = 'scalar';\n  state.result = '';\n  captureStart = captureEnd = state.position;\n  hasPendingContent = false;\n\n  while (ch !== 0) {\n    if (ch === 0x3A/* : */) {\n      following = state.input.charCodeAt(state.position + 1);\n\n      if (is_WS_OR_EOL(following) ||\n          withinFlowCollection && is_FLOW_INDICATOR(following)) {\n        break;\n      }\n\n    } else if (ch === 0x23/* # */) {\n      preceding = state.input.charCodeAt(state.position - 1);\n\n      if (is_WS_OR_EOL(preceding)) {\n        break;\n      }\n\n    } else if ((state.position === state.lineStart && testDocumentSeparator(state)) ||\n               withinFlowCollection && is_FLOW_INDICATOR(ch)) {\n      break;\n\n    } else if (is_EOL(ch)) {\n      _line = state.line;\n      _lineStart = state.lineStart;\n      _lineIndent = state.lineIndent;\n      skipSeparationSpace(state, false, -1);\n\n      if (state.lineIndent >= nodeIndent) {\n        hasPendingContent = true;\n        ch = state.input.charCodeAt(state.position);\n        continue;\n      } else {\n        state.position = captureEnd;\n        state.line = _line;\n        state.lineStart = _lineStart;\n        state.lineIndent = _lineIndent;\n        break;\n      }\n    }\n\n    if (hasPendingContent) {\n      captureSegment(state, captureStart, captureEnd, false);\n      writeFoldedLines(state, state.line - _line);\n      captureStart = captureEnd = state.position;\n      hasPendingContent = false;\n    }\n\n    if (!is_WHITE_SPACE(ch)) {\n      captureEnd = state.position + 1;\n    }\n\n    ch = state.input.charCodeAt(++state.position);\n  }\n\n  captureSegment(state, captureStart, captureEnd, false);\n\n  if (state.result) {\n    return true;\n  }\n\n  state.kind = _kind;\n  state.result = _result;\n  return false;\n}\n\nfunction readSingleQuotedScalar(state, nodeIndent) {\n  var ch,\n      captureStart, captureEnd;\n\n  ch = state.input.charCodeAt(state.position);\n\n  if (ch !== 0x27/* ' */) {\n    return false;\n  }\n\n  state.kind = 'scalar';\n  state.result = '';\n  state.position++;\n  captureStart = captureEnd = state.position;\n\n  while ((ch = state.input.charCodeAt(state.position)) !== 0) {\n    if (ch === 0x27/* ' */) {\n      captureSegment(state, captureStart, state.position, true);\n      ch = state.input.charCodeAt(++state.position);\n\n      if (ch === 0x27/* ' */) {\n        captureStart = state.position;\n        state.position++;\n        captureEnd = state.position;\n      } else {\n        return true;\n      }\n\n    } else if (is_EOL(ch)) {\n      captureSegment(state, captureStart, captureEnd, true);\n      writeFoldedLines(state, skipSeparationSpace(state, false, nodeIndent));\n      captureStart = captureEnd = state.position;\n\n    } else if (state.position === state.lineStart && testDocumentSeparator(state)) {\n      throwError(state, 'unexpected end of the document within a single quoted scalar');\n\n    } else {\n      state.position++;\n      captureEnd = state.position;\n    }\n  }\n\n  throwError(state, 'unexpected end of the stream within a single quoted scalar');\n}\n\nfunction readDoubleQuotedScalar(state, nodeIndent) {\n  var captureStart,\n      captureEnd,\n      hexLength,\n      hexResult,\n      tmp,\n      ch;\n\n  ch = state.input.charCodeAt(state.position);\n\n  if (ch !== 0x22/* \" */) {\n    return false;\n  }\n\n  state.kind = 'scalar';\n  state.result = '';\n  state.position++;\n  captureStart = captureEnd = state.position;\n\n  while ((ch = state.input.charCodeAt(state.position)) !== 0) {\n    if (ch === 0x22/* \" */) {\n      captureSegment(state, captureStart, state.position, true);\n      state.position++;\n      return true;\n\n    } else if (ch === 0x5C/* \\ */) {\n      captureSegment(state, captureStart, state.position, true);\n      ch = state.input.charCodeAt(++state.position);\n\n      if (is_EOL(ch)) {\n        skipSeparationSpace(state, false, nodeIndent);\n\n        // TODO: rework to inline fn with no type cast?\n      } else if (ch < 256 && simpleEscapeCheck[ch]) {\n        state.result += simpleEscapeMap[ch];\n        state.position++;\n\n      } else if ((tmp = escapedHexLen(ch)) > 0) {\n        hexLength = tmp;\n        hexResult = 0;\n\n        for (; hexLength > 0; hexLength--) {\n          ch = state.input.charCodeAt(++state.position);\n\n          if ((tmp = fromHexCode(ch)) >= 0) {\n            hexResult = (hexResult << 4) + tmp;\n\n          } else {\n            throwError(state, 'expected hexadecimal character');\n          }\n        }\n\n        state.result += charFromCodepoint(hexResult);\n\n        state.position++;\n\n      } else {\n        throwError(state, 'unknown escape sequence');\n      }\n\n      captureStart = captureEnd = state.position;\n\n    } else if (is_EOL(ch)) {\n      captureSegment(state, captureStart, captureEnd, true);\n      writeFoldedLines(state, skipSeparationSpace(state, false, nodeIndent));\n      captureStart = captureEnd = state.position;\n\n    } else if (state.position === state.lineStart && testDocumentSeparator(state)) {\n      throwError(state, 'unexpected end of the document within a double quoted scalar');\n\n    } else {\n      state.position++;\n      captureEnd = state.position;\n    }\n  }\n\n  throwError(state, 'unexpected end of the stream within a double quoted scalar');\n}\n\nfunction readFlowCollection(state, nodeIndent) {\n  var readNext = true,\n      _line,\n      _lineStart,\n      _pos,\n      _tag     = state.tag,\n      _result,\n      _anchor  = state.anchor,\n      following,\n      terminator,\n      isPair,\n      isExplicitPair,\n      isMapping,\n      overridableKeys = Object.create(null),\n      keyNode,\n      keyTag,\n      valueNode,\n      ch;\n\n  ch = state.input.charCodeAt(state.position);\n\n  if (ch === 0x5B/* [ */) {\n    terminator = 0x5D;/* ] */\n    isMapping = false;\n    _result = [];\n  } else if (ch === 0x7B/* { */) {\n    terminator = 0x7D;/* } */\n    isMapping = true;\n    _result = {};\n  } else {\n    return false;\n  }\n\n  if (state.anchor !== null) {\n    state.anchorMap[state.anchor] = _result;\n  }\n\n  ch = state.input.charCodeAt(++state.position);\n\n  while (ch !== 0) {\n    skipSeparationSpace(state, true, nodeIndent);\n\n    ch = state.input.charCodeAt(state.position);\n\n    if (ch === terminator) {\n      state.position++;\n      state.tag = _tag;\n      state.anchor = _anchor;\n      state.kind = isMapping ? 'mapping' : 'sequence';\n      state.result = _result;\n      return true;\n    } else if (!readNext) {\n      throwError(state, 'missed comma between flow collection entries');\n    } else if (ch === 0x2C/* , */) {\n      // \"flow collection entries can never be completely empty\", as per YAML 1.2, section 7.4\n      throwError(state, \"expected the node content, but found ','\");\n    }\n\n    keyTag = keyNode = valueNode = null;\n    isPair = isExplicitPair = false;\n\n    if (ch === 0x3F/* ? */) {\n      following = state.input.charCodeAt(state.position + 1);\n\n      if (is_WS_OR_EOL(following)) {\n        isPair = isExplicitPair = true;\n        state.position++;\n        skipSeparationSpace(state, true, nodeIndent);\n      }\n    }\n\n    _line = state.line; // Save the current line.\n    _lineStart = state.lineStart;\n    _pos = state.position;\n    composeNode(state, nodeIndent, CONTEXT_FLOW_IN, false, true);\n    keyTag = state.tag;\n    keyNode = state.result;\n    skipSeparationSpace(state, true, nodeIndent);\n\n    ch = state.input.charCodeAt(state.position);\n\n    if ((isExplicitPair || state.line === _line) && ch === 0x3A/* : */) {\n      isPair = true;\n      ch = state.input.charCodeAt(++state.position);\n      skipSeparationSpace(state, true, nodeIndent);\n      composeNode(state, nodeIndent, CONTEXT_FLOW_IN, false, true);\n      valueNode = state.result;\n    }\n\n    if (isMapping) {\n      storeMappingPair(state, _result, overridableKeys, keyTag, keyNode, valueNode, _line, _lineStart, _pos);\n    } else if (isPair) {\n      _result.push(storeMappingPair(state, null, overridableKeys, keyTag, keyNode, valueNode, _line, _lineStart, _pos));\n    } else {\n      _result.push(keyNode);\n    }\n\n    skipSeparationSpace(state, true, nodeIndent);\n\n    ch = state.input.charCodeAt(state.position);\n\n    if (ch === 0x2C/* , */) {\n      readNext = true;\n      ch = state.input.charCodeAt(++state.position);\n    } else {\n      readNext = false;\n    }\n  }\n\n  throwError(state, 'unexpected end of the stream within a flow collection');\n}\n\nfunction readBlockScalar(state, nodeIndent) {\n  var captureStart,\n      folding,\n      chomping       = CHOMPING_CLIP,\n      didReadContent = false,\n      detectedIndent = false,\n      textIndent     = nodeIndent,\n      emptyLines     = 0,\n      atMoreIndented = false,\n      tmp,\n      ch;\n\n  ch = state.input.charCodeAt(state.position);\n\n  if (ch === 0x7C/* | */) {\n    folding = false;\n  } else if (ch === 0x3E/* > */) {\n    folding = true;\n  } else {\n    return false;\n  }\n\n  state.kind = 'scalar';\n  state.result = '';\n\n  while (ch !== 0) {\n    ch = state.input.charCodeAt(++state.position);\n\n    if (ch === 0x2B/* + */ || ch === 0x2D/* - */) {\n      if (CHOMPING_CLIP === chomping) {\n        chomping = (ch === 0x2B/* + */) ? CHOMPING_KEEP : CHOMPING_STRIP;\n      } else {\n        throwError(state, 'repeat of a chomping mode identifier');\n      }\n\n    } else if ((tmp = fromDecimalCode(ch)) >= 0) {\n      if (tmp === 0) {\n        throwError(state, 'bad explicit indentation width of a block scalar; it cannot be less than one');\n      } else if (!detectedIndent) {\n        textIndent = nodeIndent + tmp - 1;\n        detectedIndent = true;\n      } else {\n        throwError(state, 'repeat of an indentation width identifier');\n      }\n\n    } else {\n      break;\n    }\n  }\n\n  if (is_WHITE_SPACE(ch)) {\n    do { ch = state.input.charCodeAt(++state.position); }\n    while (is_WHITE_SPACE(ch));\n\n    if (ch === 0x23/* # */) {\n      do { ch = state.input.charCodeAt(++state.position); }\n      while (!is_EOL(ch) && (ch !== 0));\n    }\n  }\n\n  while (ch !== 0) {\n    readLineBreak(state);\n    state.lineIndent = 0;\n\n    ch = state.input.charCodeAt(state.position);\n\n    while ((!detectedIndent || state.lineIndent < textIndent) &&\n           (ch === 0x20/* Space */)) {\n      state.lineIndent++;\n      ch = state.input.charCodeAt(++state.position);\n    }\n\n    if (!detectedIndent && state.lineIndent > textIndent) {\n      textIndent = state.lineIndent;\n    }\n\n    if (is_EOL(ch)) {\n      emptyLines++;\n      continue;\n    }\n\n    // End of the scalar.\n    if (state.lineIndent < textIndent) {\n\n      // Perform the chomping.\n      if (chomping === CHOMPING_KEEP) {\n        state.result += common.repeat('\\n', didReadContent ? 1 + emptyLines : emptyLines);\n      } else if (chomping === CHOMPING_CLIP) {\n        if (didReadContent) { // i.e. only if the scalar is not empty.\n          state.result += '\\n';\n        }\n      }\n\n      // Break this `while` cycle and go to the funciton's epilogue.\n      break;\n    }\n\n    // Folded style: use fancy rules to handle line breaks.\n    if (folding) {\n\n      // Lines starting with white space characters (more-indented lines) are not folded.\n      if (is_WHITE_SPACE(ch)) {\n        atMoreIndented = true;\n        // except for the first content line (cf. Example 8.1)\n        state.result += common.repeat('\\n', didReadContent ? 1 + emptyLines : emptyLines);\n\n      // End of more-indented block.\n      } else if (atMoreIndented) {\n        atMoreIndented = false;\n        state.result += common.repeat('\\n', emptyLines + 1);\n\n      // Just one line break - perceive as the same line.\n      } else if (emptyLines === 0) {\n        if (didReadContent) { // i.e. only if we have already read some scalar content.\n          state.result += ' ';\n        }\n\n      // Several line breaks - perceive as different lines.\n      } else {\n        state.result += common.repeat('\\n', emptyLines);\n      }\n\n    // Literal style: just add exact number of line breaks between content lines.\n    } else {\n      // Keep all line breaks except the header line break.\n      state.result += common.repeat('\\n', didReadContent ? 1 + emptyLines : emptyLines);\n    }\n\n    didReadContent = true;\n    detectedIndent = true;\n    emptyLines = 0;\n    captureStart = state.position;\n\n    while (!is_EOL(ch) && (ch !== 0)) {\n      ch = state.input.charCodeAt(++state.position);\n    }\n\n    captureSegment(state, captureStart, state.position, false);\n  }\n\n  return true;\n}\n\nfunction readBlockSequence(state, nodeIndent) {\n  var _line,\n      _tag      = state.tag,\n      _anchor   = state.anchor,\n      _result   = [],\n      following,\n      detected  = false,\n      ch;\n\n  // there is a leading tab before this token, so it can't be a block sequence/mapping;\n  // it can still be flow sequence/mapping or a scalar\n  if (state.firstTabInLine !== -1) return false;\n\n  if (state.anchor !== null) {\n    state.anchorMap[state.anchor] = _result;\n  }\n\n  ch = state.input.charCodeAt(state.position);\n\n  while (ch !== 0) {\n    if (state.firstTabInLine !== -1) {\n      state.position = state.firstTabInLine;\n      throwError(state, 'tab characters must not be used in indentation');\n    }\n\n    if (ch !== 0x2D/* - */) {\n      break;\n    }\n\n    following = state.input.charCodeAt(state.position + 1);\n\n    if (!is_WS_OR_EOL(following)) {\n      break;\n    }\n\n    detected = true;\n    state.position++;\n\n    if (skipSeparationSpace(state, true, -1)) {\n      if (state.lineIndent <= nodeIndent) {\n        _result.push(null);\n        ch = state.input.charCodeAt(state.position);\n        continue;\n      }\n    }\n\n    _line = state.line;\n    composeNode(state, nodeIndent, CONTEXT_BLOCK_IN, false, true);\n    _result.push(state.result);\n    skipSeparationSpace(state, true, -1);\n\n    ch = state.input.charCodeAt(state.position);\n\n    if ((state.line === _line || state.lineIndent > nodeIndent) && (ch !== 0)) {\n      throwError(state, 'bad indentation of a sequence entry');\n    } else if (state.lineIndent < nodeIndent) {\n      break;\n    }\n  }\n\n  if (detected) {\n    state.tag = _tag;\n    state.anchor = _anchor;\n    state.kind = 'sequence';\n    state.result = _result;\n    return true;\n  }\n  return false;\n}\n\nfunction readBlockMapping(state, nodeIndent, flowIndent) {\n  var following,\n      allowCompact,\n      _line,\n      _keyLine,\n      _keyLineStart,\n      _keyPos,\n      _tag          = state.tag,\n      _anchor       = state.anchor,\n      _result       = {},\n      overridableKeys = Object.create(null),\n      keyTag        = null,\n      keyNode       = null,\n      valueNode     = null,\n      atExplicitKey = false,\n      detected      = false,\n      ch;\n\n  // there is a leading tab before this token, so it can't be a block sequence/mapping;\n  // it can still be flow sequence/mapping or a scalar\n  if (state.firstTabInLine !== -1) return false;\n\n  if (state.anchor !== null) {\n    state.anchorMap[state.anchor] = _result;\n  }\n\n  ch = state.input.charCodeAt(state.position);\n\n  while (ch !== 0) {\n    if (!atExplicitKey && state.firstTabInLine !== -1) {\n      state.position = state.firstTabInLine;\n      throwError(state, 'tab characters must not be used in indentation');\n    }\n\n    following = state.input.charCodeAt(state.position + 1);\n    _line = state.line; // Save the current line.\n\n    //\n    // Explicit notation case. There are two separate blocks:\n    // first for the key (denoted by \"?\") and second for the value (denoted by \":\")\n    //\n    if ((ch === 0x3F/* ? */ || ch === 0x3A/* : */) && is_WS_OR_EOL(following)) {\n\n      if (ch === 0x3F/* ? */) {\n        if (atExplicitKey) {\n          storeMappingPair(state, _result, overridableKeys, keyTag, keyNode, null, _keyLine, _keyLineStart, _keyPos);\n          keyTag = keyNode = valueNode = null;\n        }\n\n        detected = true;\n        atExplicitKey = true;\n        allowCompact = true;\n\n      } else if (atExplicitKey) {\n        // i.e. 0x3A/* : */ === character after the explicit key.\n        atExplicitKey = false;\n        allowCompact = true;\n\n      } else {\n        throwError(state, 'incomplete explicit mapping pair; a key node is missed; or followed by a non-tabulated empty line');\n      }\n\n      state.position += 1;\n      ch = following;\n\n    //\n    // Implicit notation case. Flow-style node as the key first, then \":\", and the value.\n    //\n    } else {\n      _keyLine = state.line;\n      _keyLineStart = state.lineStart;\n      _keyPos = state.position;\n\n      if (!composeNode(state, flowIndent, CONTEXT_FLOW_OUT, false, true)) {\n        // Neither implicit nor explicit notation.\n        // Reading is done. Go to the epilogue.\n        break;\n      }\n\n      if (state.line === _line) {\n        ch = state.input.charCodeAt(state.position);\n\n        while (is_WHITE_SPACE(ch)) {\n          ch = state.input.charCodeAt(++state.position);\n        }\n\n        if (ch === 0x3A/* : */) {\n          ch = state.input.charCodeAt(++state.position);\n\n          if (!is_WS_OR_EOL(ch)) {\n            throwError(state, 'a whitespace character is expected after the key-value separator within a block mapping');\n          }\n\n          if (atExplicitKey) {\n            storeMappingPair(state, _result, overridableKeys, keyTag, keyNode, null, _keyLine, _keyLineStart, _keyPos);\n            keyTag = keyNode = valueNode = null;\n          }\n\n          detected = true;\n          atExplicitKey = false;\n          allowCompact = false;\n          keyTag = state.tag;\n          keyNode = state.result;\n\n        } else if (detected) {\n          throwError(state, 'can not read an implicit mapping pair; a colon is missed');\n\n        } else {\n          state.tag = _tag;\n          state.anchor = _anchor;\n          return true; // Keep the result of `composeNode`.\n        }\n\n      } else if (detected) {\n        throwError(state, 'can not read a block mapping entry; a multiline key may not be an implicit key');\n\n      } else {\n        state.tag = _tag;\n        state.anchor = _anchor;\n        return true; // Keep the result of `composeNode`.\n      }\n    }\n\n    //\n    // Common reading code for both explicit and implicit notations.\n    //\n    if (state.line === _line || state.lineIndent > nodeIndent) {\n      if (atExplicitKey) {\n        _keyLine = state.line;\n        _keyLineStart = state.lineStart;\n        _keyPos = state.position;\n      }\n\n      if (composeNode(state, nodeIndent, CONTEXT_BLOCK_OUT, true, allowCompact)) {\n        if (atExplicitKey) {\n          keyNode = state.result;\n        } else {\n          valueNode = state.result;\n        }\n      }\n\n      if (!atExplicitKey) {\n        storeMappingPair(state, _result, overridableKeys, keyTag, keyNode, valueNode, _keyLine, _keyLineStart, _keyPos);\n        keyTag = keyNode = valueNode = null;\n      }\n\n      skipSeparationSpace(state, true, -1);\n      ch = state.input.charCodeAt(state.position);\n    }\n\n    if ((state.line === _line || state.lineIndent > nodeIndent) && (ch !== 0)) {\n      throwError(state, 'bad indentation of a mapping entry');\n    } else if (state.lineIndent < nodeIndent) {\n      break;\n    }\n  }\n\n  //\n  // Epilogue.\n  //\n\n  // Special case: last mapping's node contains only the key in explicit notation.\n  if (atExplicitKey) {\n    storeMappingPair(state, _result, overridableKeys, keyTag, keyNode, null, _keyLine, _keyLineStart, _keyPos);\n  }\n\n  // Expose the resulting mapping.\n  if (detected) {\n    state.tag = _tag;\n    state.anchor = _anchor;\n    state.kind = 'mapping';\n    state.result = _result;\n  }\n\n  return detected;\n}\n\nfunction readTagProperty(state) {\n  var _position,\n      isVerbatim = false,\n      isNamed    = false,\n      tagHandle,\n      tagName,\n      ch;\n\n  ch = state.input.charCodeAt(state.position);\n\n  if (ch !== 0x21/* ! */) return false;\n\n  if (state.tag !== null) {\n    throwError(state, 'duplication of a tag property');\n  }\n\n  ch = state.input.charCodeAt(++state.position);\n\n  if (ch === 0x3C/* < */) {\n    isVerbatim = true;\n    ch = state.input.charCodeAt(++state.position);\n\n  } else if (ch === 0x21/* ! */) {\n    isNamed = true;\n    tagHandle = '!!';\n    ch = state.input.charCodeAt(++state.position);\n\n  } else {\n    tagHandle = '!';\n  }\n\n  _position = state.position;\n\n  if (isVerbatim) {\n    do { ch = state.input.charCodeAt(++state.position); }\n    while (ch !== 0 && ch !== 0x3E/* > */);\n\n    if (state.position < state.length) {\n      tagName = state.input.slice(_position, state.position);\n      ch = state.input.charCodeAt(++state.position);\n    } else {\n      throwError(state, 'unexpected end of the stream within a verbatim tag');\n    }\n  } else {\n    while (ch !== 0 && !is_WS_OR_EOL(ch)) {\n\n      if (ch === 0x21/* ! */) {\n        if (!isNamed) {\n          tagHandle = state.input.slice(_position - 1, state.position + 1);\n\n          if (!PATTERN_TAG_HANDLE.test(tagHandle)) {\n            throwError(state, 'named tag handle cannot contain such characters');\n          }\n\n          isNamed = true;\n          _position = state.position + 1;\n        } else {\n          throwError(state, 'tag suffix cannot contain exclamation marks');\n        }\n      }\n\n      ch = state.input.charCodeAt(++state.position);\n    }\n\n    tagName = state.input.slice(_position, state.position);\n\n    if (PATTERN_FLOW_INDICATORS.test(tagName)) {\n      throwError(state, 'tag suffix cannot contain flow indicator characters');\n    }\n  }\n\n  if (tagName && !PATTERN_TAG_URI.test(tagName)) {\n    throwError(state, 'tag name cannot contain such characters: ' + tagName);\n  }\n\n  try {\n    tagName = decodeURIComponent(tagName);\n  } catch (err) {\n    throwError(state, 'tag name is malformed: ' + tagName);\n  }\n\n  if (isVerbatim) {\n    state.tag = tagName;\n\n  } else if (_hasOwnProperty$1.call(state.tagMap, tagHandle)) {\n    state.tag = state.tagMap[tagHandle] + tagName;\n\n  } else if (tagHandle === '!') {\n    state.tag = '!' + tagName;\n\n  } else if (tagHandle === '!!') {\n    state.tag = 'tag:yaml.org,2002:' + tagName;\n\n  } else {\n    throwError(state, 'undeclared tag handle \"' + tagHandle + '\"');\n  }\n\n  return true;\n}\n\nfunction readAnchorProperty(state) {\n  var _position,\n      ch;\n\n  ch = state.input.charCodeAt(state.position);\n\n  if (ch !== 0x26/* & */) return false;\n\n  if (state.anchor !== null) {\n    throwError(state, 'duplication of an anchor property');\n  }\n\n  ch = state.input.charCodeAt(++state.position);\n  _position = state.position;\n\n  while (ch !== 0 && !is_WS_OR_EOL(ch) && !is_FLOW_INDICATOR(ch)) {\n    ch = state.input.charCodeAt(++state.position);\n  }\n\n  if (state.position === _position) {\n    throwError(state, 'name of an anchor node must contain at least one character');\n  }\n\n  state.anchor = state.input.slice(_position, state.position);\n  return true;\n}\n\nfunction readAlias(state) {\n  var _position, alias,\n      ch;\n\n  ch = state.input.charCodeAt(state.position);\n\n  if (ch !== 0x2A/* * */) return false;\n\n  ch = state.input.charCodeAt(++state.position);\n  _position = state.position;\n\n  while (ch !== 0 && !is_WS_OR_EOL(ch) && !is_FLOW_INDICATOR(ch)) {\n    ch = state.input.charCodeAt(++state.position);\n  }\n\n  if (state.position === _position) {\n    throwError(state, 'name of an alias node must contain at least one character');\n  }\n\n  alias = state.input.slice(_position, state.position);\n\n  if (!_hasOwnProperty$1.call(state.anchorMap, alias)) {\n    throwError(state, 'unidentified alias \"' + alias + '\"');\n  }\n\n  state.result = state.anchorMap[alias];\n  skipSeparationSpace(state, true, -1);\n  return true;\n}\n\nfunction composeNode(state, parentIndent, nodeContext, allowToSeek, allowCompact) {\n  var allowBlockStyles,\n      allowBlockScalars,\n      allowBlockCollections,\n      indentStatus = 1, // 1: this>parent, 0: this=parent, -1: this<parent\n      atNewLine  = false,\n      hasContent = false,\n      typeIndex,\n      typeQuantity,\n      typeList,\n      type,\n      flowIndent,\n      blockIndent;\n\n  if (state.listener !== null) {\n    state.listener('open', state);\n  }\n\n  state.tag    = null;\n  state.anchor = null;\n  state.kind   = null;\n  state.result = null;\n\n  allowBlockStyles = allowBlockScalars = allowBlockCollections =\n    CONTEXT_BLOCK_OUT === nodeContext ||\n    CONTEXT_BLOCK_IN  === nodeContext;\n\n  if (allowToSeek) {\n    if (skipSeparationSpace(state, true, -1)) {\n      atNewLine = true;\n\n      if (state.lineIndent > parentIndent) {\n        indentStatus = 1;\n      } else if (state.lineIndent === parentIndent) {\n        indentStatus = 0;\n      } else if (state.lineIndent < parentIndent) {\n        indentStatus = -1;\n      }\n    }\n  }\n\n  if (indentStatus === 1) {\n    while (readTagProperty(state) || readAnchorProperty(state)) {\n      if (skipSeparationSpace(state, true, -1)) {\n        atNewLine = true;\n        allowBlockCollections = allowBlockStyles;\n\n        if (state.lineIndent > parentIndent) {\n          indentStatus = 1;\n        } else if (state.lineIndent === parentIndent) {\n          indentStatus = 0;\n        } else if (state.lineIndent < parentIndent) {\n          indentStatus = -1;\n        }\n      } else {\n        allowBlockCollections = false;\n      }\n    }\n  }\n\n  if (allowBlockCollections) {\n    allowBlockCollections = atNewLine || allowCompact;\n  }\n\n  if (indentStatus === 1 || CONTEXT_BLOCK_OUT === nodeContext) {\n    if (CONTEXT_FLOW_IN === nodeContext || CONTEXT_FLOW_OUT === nodeContext) {\n      flowIndent = parentIndent;\n    } else {\n      flowIndent = parentIndent + 1;\n    }\n\n    blockIndent = state.position - state.lineStart;\n\n    if (indentStatus === 1) {\n      if (allowBlockCollections &&\n          (readBlockSequence(state, blockIndent) ||\n           readBlockMapping(state, blockIndent, flowIndent)) ||\n          readFlowCollection(state, flowIndent)) {\n        hasContent = true;\n      } else {\n        if ((allowBlockScalars && readBlockScalar(state, flowIndent)) ||\n            readSingleQuotedScalar(state, flowIndent) ||\n            readDoubleQuotedScalar(state, flowIndent)) {\n          hasContent = true;\n\n        } else if (readAlias(state)) {\n          hasContent = true;\n\n          if (state.tag !== null || state.anchor !== null) {\n            throwError(state, 'alias node should not have any properties');\n          }\n\n        } else if (readPlainScalar(state, flowIndent, CONTEXT_FLOW_IN === nodeContext)) {\n          hasContent = true;\n\n          if (state.tag === null) {\n            state.tag = '?';\n          }\n        }\n\n        if (state.anchor !== null) {\n          state.anchorMap[state.anchor] = state.result;\n        }\n      }\n    } else if (indentStatus === 0) {\n      // Special case: block sequences are allowed to have same indentation level as the parent.\n      // http://www.yaml.org/spec/1.2/spec.html#id2799784\n      hasContent = allowBlockCollections && readBlockSequence(state, blockIndent);\n    }\n  }\n\n  if (state.tag === null) {\n    if (state.anchor !== null) {\n      state.anchorMap[state.anchor] = state.result;\n    }\n\n  } else if (state.tag === '?') {\n    // Implicit resolving is not allowed for non-scalar types, and '?'\n    // non-specific tag is only automatically assigned to plain scalars.\n    //\n    // We only need to check kind conformity in case user explicitly assigns '?'\n    // tag, for example like this: \"!<?> [0]\"\n    //\n    if (state.result !== null && state.kind !== 'scalar') {\n      throwError(state, 'unacceptable node kind for !<?> tag; it should be \"scalar\", not \"' + state.kind + '\"');\n    }\n\n    for (typeIndex = 0, typeQuantity = state.implicitTypes.length; typeIndex < typeQuantity; typeIndex += 1) {\n      type = state.implicitTypes[typeIndex];\n\n      if (type.resolve(state.result)) { // `state.result` updated in resolver if matched\n        state.result = type.construct(state.result);\n        state.tag = type.tag;\n        if (state.anchor !== null) {\n          state.anchorMap[state.anchor] = state.result;\n        }\n        break;\n      }\n    }\n  } else if (state.tag !== '!') {\n    if (_hasOwnProperty$1.call(state.typeMap[state.kind || 'fallback'], state.tag)) {\n      type = state.typeMap[state.kind || 'fallback'][state.tag];\n    } else {\n      // looking for multi type\n      type = null;\n      typeList = state.typeMap.multi[state.kind || 'fallback'];\n\n      for (typeIndex = 0, typeQuantity = typeList.length; typeIndex < typeQuantity; typeIndex += 1) {\n        if (state.tag.slice(0, typeList[typeIndex].tag.length) === typeList[typeIndex].tag) {\n          type = typeList[typeIndex];\n          break;\n        }\n      }\n    }\n\n    if (!type) {\n      throwError(state, 'unknown tag !<' + state.tag + '>');\n    }\n\n    if (state.result !== null && type.kind !== state.kind) {\n      throwError(state, 'unacceptable node kind for !<' + state.tag + '> tag; it should be \"' + type.kind + '\", not \"' + state.kind + '\"');\n    }\n\n    if (!type.resolve(state.result, state.tag)) { // `state.result` updated in resolver if matched\n      throwError(state, 'cannot resolve a node with !<' + state.tag + '> explicit tag');\n    } else {\n      state.result = type.construct(state.result, state.tag);\n      if (state.anchor !== null) {\n        state.anchorMap[state.anchor] = state.result;\n      }\n    }\n  }\n\n  if (state.listener !== null) {\n    state.listener('close', state);\n  }\n  return state.tag !== null ||  state.anchor !== null || hasContent;\n}\n\nfunction readDocument(state) {\n  var documentStart = state.position,\n      _position,\n      directiveName,\n      directiveArgs,\n      hasDirectives = false,\n      ch;\n\n  state.version = null;\n  state.checkLineBreaks = state.legacy;\n  state.tagMap = Object.create(null);\n  state.anchorMap = Object.create(null);\n\n  while ((ch = state.input.charCodeAt(state.position)) !== 0) {\n    skipSeparationSpace(state, true, -1);\n\n    ch = state.input.charCodeAt(state.position);\n\n    if (state.lineIndent > 0 || ch !== 0x25/* % */) {\n      break;\n    }\n\n    hasDirectives = true;\n    ch = state.input.charCodeAt(++state.position);\n    _position = state.position;\n\n    while (ch !== 0 && !is_WS_OR_EOL(ch)) {\n      ch = state.input.charCodeAt(++state.position);\n    }\n\n    directiveName = state.input.slice(_position, state.position);\n    directiveArgs = [];\n\n    if (directiveName.length < 1) {\n      throwError(state, 'directive name must not be less than one character in length');\n    }\n\n    while (ch !== 0) {\n      while (is_WHITE_SPACE(ch)) {\n        ch = state.input.charCodeAt(++state.position);\n      }\n\n      if (ch === 0x23/* # */) {\n        do { ch = state.input.charCodeAt(++state.position); }\n        while (ch !== 0 && !is_EOL(ch));\n        break;\n      }\n\n      if (is_EOL(ch)) break;\n\n      _position = state.position;\n\n      while (ch !== 0 && !is_WS_OR_EOL(ch)) {\n        ch = state.input.charCodeAt(++state.position);\n      }\n\n      directiveArgs.push(state.input.slice(_position, state.position));\n    }\n\n    if (ch !== 0) readLineBreak(state);\n\n    if (_hasOwnProperty$1.call(directiveHandlers, directiveName)) {\n      directiveHandlers[directiveName](state, directiveName, directiveArgs);\n    } else {\n      throwWarning(state, 'unknown document directive \"' + directiveName + '\"');\n    }\n  }\n\n  skipSeparationSpace(state, true, -1);\n\n  if (state.lineIndent === 0 &&\n      state.input.charCodeAt(state.position)     === 0x2D/* - */ &&\n      state.input.charCodeAt(state.position + 1) === 0x2D/* - */ &&\n      state.input.charCodeAt(state.position + 2) === 0x2D/* - */) {\n    state.position += 3;\n    skipSeparationSpace(state, true, -1);\n\n  } else if (hasDirectives) {\n    throwError(state, 'directives end mark is expected');\n  }\n\n  composeNode(state, state.lineIndent - 1, CONTEXT_BLOCK_OUT, false, true);\n  skipSeparationSpace(state, true, -1);\n\n  if (state.checkLineBreaks &&\n      PATTERN_NON_ASCII_LINE_BREAKS.test(state.input.slice(documentStart, state.position))) {\n    throwWarning(state, 'non-ASCII line breaks are interpreted as content');\n  }\n\n  state.documents.push(state.result);\n\n  if (state.position === state.lineStart && testDocumentSeparator(state)) {\n\n    if (state.input.charCodeAt(state.position) === 0x2E/* . */) {\n      state.position += 3;\n      skipSeparationSpace(state, true, -1);\n    }\n    return;\n  }\n\n  if (state.position < (state.length - 1)) {\n    throwError(state, 'end of the stream or a document separator is expected');\n  } else {\n    return;\n  }\n}\n\n\nfunction loadDocuments(input, options) {\n  input = String(input);\n  options = options || {};\n\n  if (input.length !== 0) {\n\n    // Add tailing `\\n` if not exists\n    if (input.charCodeAt(input.length - 1) !== 0x0A/* LF */ &&\n        input.charCodeAt(input.length - 1) !== 0x0D/* CR */) {\n      input += '\\n';\n    }\n\n    // Strip BOM\n    if (input.charCodeAt(0) === 0xFEFF) {\n      input = input.slice(1);\n    }\n  }\n\n  var state = new State$1(input, options);\n\n  var nullpos = input.indexOf('\\0');\n\n  if (nullpos !== -1) {\n    state.position = nullpos;\n    throwError(state, 'null byte is not allowed in input');\n  }\n\n  // Use 0 as string terminator. That significantly simplifies bounds check.\n  state.input += '\\0';\n\n  while (state.input.charCodeAt(state.position) === 0x20/* Space */) {\n    state.lineIndent += 1;\n    state.position += 1;\n  }\n\n  while (state.position < (state.length - 1)) {\n    readDocument(state);\n  }\n\n  return state.documents;\n}\n\n\nfunction loadAll$1(input, iterator, options) {\n  if (iterator !== null && typeof iterator === 'object' && typeof options === 'undefined') {\n    options = iterator;\n    iterator = null;\n  }\n\n  var documents = loadDocuments(input, options);\n\n  if (typeof iterator !== 'function') {\n    return documents;\n  }\n\n  for (var index = 0, length = documents.length; index < length; index += 1) {\n    iterator(documents[index]);\n  }\n}\n\n\nfunction load$1(input, options) {\n  var documents = loadDocuments(input, options);\n\n  if (documents.length === 0) {\n    /*eslint-disable no-undefined*/\n    return undefined;\n  } else if (documents.length === 1) {\n    return documents[0];\n  }\n  throw new exception('expected a single document in the stream, but found more');\n}\n\n\nvar loadAll_1 = loadAll$1;\nvar load_1    = load$1;\n\nvar loader = {\n\tloadAll: loadAll_1,\n\tload: load_1\n};\n\n/*eslint-disable no-use-before-define*/\n\n\n\n\n\nvar _toString       = Object.prototype.toString;\nvar _hasOwnProperty = Object.prototype.hasOwnProperty;\n\nvar CHAR_BOM                  = 0xFEFF;\nvar CHAR_TAB                  = 0x09; /* Tab */\nvar CHAR_LINE_FEED            = 0x0A; /* LF */\nvar CHAR_CARRIAGE_RETURN      = 0x0D; /* CR */\nvar CHAR_SPACE                = 0x20; /* Space */\nvar CHAR_EXCLAMATION          = 0x21; /* ! */\nvar CHAR_DOUBLE_QUOTE         = 0x22; /* \" */\nvar CHAR_SHARP                = 0x23; /* # */\nvar CHAR_PERCENT              = 0x25; /* % */\nvar CHAR_AMPERSAND            = 0x26; /* & */\nvar CHAR_SINGLE_QUOTE         = 0x27; /* ' */\nvar CHAR_ASTERISK             = 0x2A; /* * */\nvar CHAR_COMMA                = 0x2C; /* , */\nvar CHAR_MINUS                = 0x2D; /* - */\nvar CHAR_COLON                = 0x3A; /* : */\nvar CHAR_EQUALS               = 0x3D; /* = */\nvar CHAR_GREATER_THAN         = 0x3E; /* > */\nvar CHAR_QUESTION             = 0x3F; /* ? */\nvar CHAR_COMMERCIAL_AT        = 0x40; /* @ */\nvar CHAR_LEFT_SQUARE_BRACKET  = 0x5B; /* [ */\nvar CHAR_RIGHT_SQUARE_BRACKET = 0x5D; /* ] */\nvar CHAR_GRAVE_ACCENT         = 0x60; /* ` */\nvar CHAR_LEFT_CURLY_BRACKET   = 0x7B; /* { */\nvar CHAR_VERTICAL_LINE        = 0x7C; /* | */\nvar CHAR_RIGHT_CURLY_BRACKET  = 0x7D; /* } */\n\nvar ESCAPE_SEQUENCES = {};\n\nESCAPE_SEQUENCES[0x00]   = '\\\\0';\nESCAPE_SEQUENCES[0x07]   = '\\\\a';\nESCAPE_SEQUENCES[0x08]   = '\\\\b';\nESCAPE_SEQUENCES[0x09]   = '\\\\t';\nESCAPE_SEQUENCES[0x0A]   = '\\\\n';\nESCAPE_SEQUENCES[0x0B]   = '\\\\v';\nESCAPE_SEQUENCES[0x0C]   = '\\\\f';\nESCAPE_SEQUENCES[0x0D]   = '\\\\r';\nESCAPE_SEQUENCES[0x1B]   = '\\\\e';\nESCAPE_SEQUENCES[0x22]   = '\\\\\"';\nESCAPE_SEQUENCES[0x5C]   = '\\\\\\\\';\nESCAPE_SEQUENCES[0x85]   = '\\\\N';\nESCAPE_SEQUENCES[0xA0]   = '\\\\_';\nESCAPE_SEQUENCES[0x2028] = '\\\\L';\nESCAPE_SEQUENCES[0x2029] = '\\\\P';\n\nvar DEPRECATED_BOOLEANS_SYNTAX = [\n  'y', 'Y', 'yes', 'Yes', 'YES', 'on', 'On', 'ON',\n  'n', 'N', 'no', 'No', 'NO', 'off', 'Off', 'OFF'\n];\n\nvar DEPRECATED_BASE60_SYNTAX = /^[-+]?[0-9_]+(?::[0-9_]+)+(?:\\.[0-9_]*)?$/;\n\nfunction compileStyleMap(schema, map) {\n  var result, keys, index, length, tag, style, type;\n\n  if (map === null) return {};\n\n  result = {};\n  keys = Object.keys(map);\n\n  for (index = 0, length = keys.length; index < length; index += 1) {\n    tag = keys[index];\n    style = String(map[tag]);\n\n    if (tag.slice(0, 2) === '!!') {\n      tag = 'tag:yaml.org,2002:' + tag.slice(2);\n    }\n    type = schema.compiledTypeMap['fallback'][tag];\n\n    if (type && _hasOwnProperty.call(type.styleAliases, style)) {\n      style = type.styleAliases[style];\n    }\n\n    result[tag] = style;\n  }\n\n  return result;\n}\n\nfunction encodeHex(character) {\n  var string, handle, length;\n\n  string = character.toString(16).toUpperCase();\n\n  if (character <= 0xFF) {\n    handle = 'x';\n    length = 2;\n  } else if (character <= 0xFFFF) {\n    handle = 'u';\n    length = 4;\n  } else if (character <= 0xFFFFFFFF) {\n    handle = 'U';\n    length = 8;\n  } else {\n    throw new exception('code point within a string may not be greater than 0xFFFFFFFF');\n  }\n\n  return '\\\\' + handle + common.repeat('0', length - string.length) + string;\n}\n\n\nvar QUOTING_TYPE_SINGLE = 1,\n    QUOTING_TYPE_DOUBLE = 2;\n\nfunction State(options) {\n  this.schema        = options['schema'] || _default;\n  this.indent        = Math.max(1, (options['indent'] || 2));\n  this.noArrayIndent = options['noArrayIndent'] || false;\n  this.skipInvalid   = options['skipInvalid'] || false;\n  this.flowLevel     = (common.isNothing(options['flowLevel']) ? -1 : options['flowLevel']);\n  this.styleMap      = compileStyleMap(this.schema, options['styles'] || null);\n  this.sortKeys      = options['sortKeys'] || false;\n  this.lineWidth     = options['lineWidth'] || 80;\n  this.noRefs        = options['noRefs'] || false;\n  this.noCompatMode  = options['noCompatMode'] || false;\n  this.condenseFlow  = options['condenseFlow'] || false;\n  this.quotingType   = options['quotingType'] === '\"' ? QUOTING_TYPE_DOUBLE : QUOTING_TYPE_SINGLE;\n  this.forceQuotes   = options['forceQuotes'] || false;\n  this.replacer      = typeof options['replacer'] === 'function' ? options['replacer'] : null;\n\n  this.implicitTypes = this.schema.compiledImplicit;\n  this.explicitTypes = this.schema.compiledExplicit;\n\n  this.tag = null;\n  this.result = '';\n\n  this.duplicates = [];\n  this.usedDuplicates = null;\n}\n\n// Indents every line in a string. Empty lines (\\n only) are not indented.\nfunction indentString(string, spaces) {\n  var ind = common.repeat(' ', spaces),\n      position = 0,\n      next = -1,\n      result = '',\n      line,\n      length = string.length;\n\n  while (position < length) {\n    next = string.indexOf('\\n', position);\n    if (next === -1) {\n      line = string.slice(position);\n      position = length;\n    } else {\n      line = string.slice(position, next + 1);\n      position = next + 1;\n    }\n\n    if (line.length && line !== '\\n') result += ind;\n\n    result += line;\n  }\n\n  return result;\n}\n\nfunction generateNextLine(state, level) {\n  return '\\n' + common.repeat(' ', state.indent * level);\n}\n\nfunction testImplicitResolving(state, str) {\n  var index, length, type;\n\n  for (index = 0, length = state.implicitTypes.length; index < length; index += 1) {\n    type = state.implicitTypes[index];\n\n    if (type.resolve(str)) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\n// [33] s-white ::= s-space | s-tab\nfunction isWhitespace(c) {\n  return c === CHAR_SPACE || c === CHAR_TAB;\n}\n\n// Returns true if the character can be printed without escaping.\n// From YAML 1.2: \"any allowed characters known to be non-printable\n// should also be escaped. [However,] This isn’t mandatory\"\n// Derived from nb-char - \\t - #x85 - #xA0 - #x2028 - #x2029.\nfunction isPrintable(c) {\n  return  (0x00020 <= c && c <= 0x00007E)\n      || ((0x000A1 <= c && c <= 0x00D7FF) && c !== 0x2028 && c !== 0x2029)\n      || ((0x0E000 <= c && c <= 0x00FFFD) && c !== CHAR_BOM)\n      ||  (0x10000 <= c && c <= 0x10FFFF);\n}\n\n// [34] ns-char ::= nb-char - s-white\n// [27] nb-char ::= c-printable - b-char - c-byte-order-mark\n// [26] b-char  ::= b-line-feed | b-carriage-return\n// Including s-white (for some reason, examples doesn't match specs in this aspect)\n// ns-char ::= c-printable - b-line-feed - b-carriage-return - c-byte-order-mark\nfunction isNsCharOrWhitespace(c) {\n  return isPrintable(c)\n    && c !== CHAR_BOM\n    // - b-char\n    && c !== CHAR_CARRIAGE_RETURN\n    && c !== CHAR_LINE_FEED;\n}\n\n// [127]  ns-plain-safe(c) ::= c = flow-out  ⇒ ns-plain-safe-out\n//                             c = flow-in   ⇒ ns-plain-safe-in\n//                             c = block-key ⇒ ns-plain-safe-out\n//                             c = flow-key  ⇒ ns-plain-safe-in\n// [128] ns-plain-safe-out ::= ns-char\n// [129]  ns-plain-safe-in ::= ns-char - c-flow-indicator\n// [130]  ns-plain-char(c) ::=  ( ns-plain-safe(c) - “:” - “#” )\n//                            | ( /* An ns-char preceding */ “#” )\n//                            | ( “:” /* Followed by an ns-plain-safe(c) */ )\nfunction isPlainSafe(c, prev, inblock) {\n  var cIsNsCharOrWhitespace = isNsCharOrWhitespace(c);\n  var cIsNsChar = cIsNsCharOrWhitespace && !isWhitespace(c);\n  return (\n    // ns-plain-safe\n    inblock ? // c = flow-in\n      cIsNsCharOrWhitespace\n      : cIsNsCharOrWhitespace\n        // - c-flow-indicator\n        && c !== CHAR_COMMA\n        && c !== CHAR_LEFT_SQUARE_BRACKET\n        && c !== CHAR_RIGHT_SQUARE_BRACKET\n        && c !== CHAR_LEFT_CURLY_BRACKET\n        && c !== CHAR_RIGHT_CURLY_BRACKET\n  )\n    // ns-plain-char\n    && c !== CHAR_SHARP // false on '#'\n    && !(prev === CHAR_COLON && !cIsNsChar) // false on ': '\n    || (isNsCharOrWhitespace(prev) && !isWhitespace(prev) && c === CHAR_SHARP) // change to true on '[^ ]#'\n    || (prev === CHAR_COLON && cIsNsChar); // change to true on ':[^ ]'\n}\n\n// Simplified test for values allowed as the first character in plain style.\nfunction isPlainSafeFirst(c) {\n  // Uses a subset of ns-char - c-indicator\n  // where ns-char = nb-char - s-white.\n  // No support of ( ( “?” | “:” | “-” ) /* Followed by an ns-plain-safe(c)) */ ) part\n  return isPrintable(c) && c !== CHAR_BOM\n    && !isWhitespace(c) // - s-white\n    // - (c-indicator ::=\n    // “-” | “?” | “:” | “,” | “[” | “]” | “{” | “}”\n    && c !== CHAR_MINUS\n    && c !== CHAR_QUESTION\n    && c !== CHAR_COLON\n    && c !== CHAR_COMMA\n    && c !== CHAR_LEFT_SQUARE_BRACKET\n    && c !== CHAR_RIGHT_SQUARE_BRACKET\n    && c !== CHAR_LEFT_CURLY_BRACKET\n    && c !== CHAR_RIGHT_CURLY_BRACKET\n    // | “#” | “&” | “*” | “!” | “|” | “=” | “>” | “'” | “\"”\n    && c !== CHAR_SHARP\n    && c !== CHAR_AMPERSAND\n    && c !== CHAR_ASTERISK\n    && c !== CHAR_EXCLAMATION\n    && c !== CHAR_VERTICAL_LINE\n    && c !== CHAR_EQUALS\n    && c !== CHAR_GREATER_THAN\n    && c !== CHAR_SINGLE_QUOTE\n    && c !== CHAR_DOUBLE_QUOTE\n    // | “%” | “@” | “`”)\n    && c !== CHAR_PERCENT\n    && c !== CHAR_COMMERCIAL_AT\n    && c !== CHAR_GRAVE_ACCENT;\n}\n\n// Simplified test for values allowed as the last character in plain style.\nfunction isPlainSafeLast(c) {\n  // just not whitespace or colon, it will be checked to be plain character later\n  return !isWhitespace(c) && c !== CHAR_COLON;\n}\n\n// Same as 'string'.codePointAt(pos), but works in older browsers.\nfunction codePointAt(string, pos) {\n  var first = string.charCodeAt(pos), second;\n  if (first >= 0xD800 && first <= 0xDBFF && pos + 1 < string.length) {\n    second = string.charCodeAt(pos + 1);\n    if (second >= 0xDC00 && second <= 0xDFFF) {\n      // https://mathiasbynens.be/notes/javascript-encoding#surrogate-formulae\n      return (first - 0xD800) * 0x400 + second - 0xDC00 + 0x10000;\n    }\n  }\n  return first;\n}\n\n// Determines whether block indentation indicator is required.\nfunction needIndentIndicator(string) {\n  var leadingSpaceRe = /^\\n* /;\n  return leadingSpaceRe.test(string);\n}\n\nvar STYLE_PLAIN   = 1,\n    STYLE_SINGLE  = 2,\n    STYLE_LITERAL = 3,\n    STYLE_FOLDED  = 4,\n    STYLE_DOUBLE  = 5;\n\n// Determines which scalar styles are possible and returns the preferred style.\n// lineWidth = -1 => no limit.\n// Pre-conditions: str.length > 0.\n// Post-conditions:\n//    STYLE_PLAIN or STYLE_SINGLE => no \\n are in the string.\n//    STYLE_LITERAL => no lines are suitable for folding (or lineWidth is -1).\n//    STYLE_FOLDED => a line > lineWidth and can be folded (and lineWidth != -1).\nfunction chooseScalarStyle(string, singleLineOnly, indentPerLevel, lineWidth,\n  testAmbiguousType, quotingType, forceQuotes, inblock) {\n\n  var i;\n  var char = 0;\n  var prevChar = null;\n  var hasLineBreak = false;\n  var hasFoldableLine = false; // only checked if shouldTrackWidth\n  var shouldTrackWidth = lineWidth !== -1;\n  var previousLineBreak = -1; // count the first line correctly\n  var plain = isPlainSafeFirst(codePointAt(string, 0))\n          && isPlainSafeLast(codePointAt(string, string.length - 1));\n\n  if (singleLineOnly || forceQuotes) {\n    // Case: no block styles.\n    // Check for disallowed characters to rule out plain and single.\n    for (i = 0; i < string.length; char >= 0x10000 ? i += 2 : i++) {\n      char = codePointAt(string, i);\n      if (!isPrintable(char)) {\n        return STYLE_DOUBLE;\n      }\n      plain = plain && isPlainSafe(char, prevChar, inblock);\n      prevChar = char;\n    }\n  } else {\n    // Case: block styles permitted.\n    for (i = 0; i < string.length; char >= 0x10000 ? i += 2 : i++) {\n      char = codePointAt(string, i);\n      if (char === CHAR_LINE_FEED) {\n        hasLineBreak = true;\n        // Check if any line can be folded.\n        if (shouldTrackWidth) {\n          hasFoldableLine = hasFoldableLine ||\n            // Foldable line = too long, and not more-indented.\n            (i - previousLineBreak - 1 > lineWidth &&\n             string[previousLineBreak + 1] !== ' ');\n          previousLineBreak = i;\n        }\n      } else if (!isPrintable(char)) {\n        return STYLE_DOUBLE;\n      }\n      plain = plain && isPlainSafe(char, prevChar, inblock);\n      prevChar = char;\n    }\n    // in case the end is missing a \\n\n    hasFoldableLine = hasFoldableLine || (shouldTrackWidth &&\n      (i - previousLineBreak - 1 > lineWidth &&\n       string[previousLineBreak + 1] !== ' '));\n  }\n  // Although every style can represent \\n without escaping, prefer block styles\n  // for multiline, since they're more readable and they don't add empty lines.\n  // Also prefer folding a super-long line.\n  if (!hasLineBreak && !hasFoldableLine) {\n    // Strings interpretable as another type have to be quoted;\n    // e.g. the string 'true' vs. the boolean true.\n    if (plain && !forceQuotes && !testAmbiguousType(string)) {\n      return STYLE_PLAIN;\n    }\n    return quotingType === QUOTING_TYPE_DOUBLE ? STYLE_DOUBLE : STYLE_SINGLE;\n  }\n  // Edge case: block indentation indicator can only have one digit.\n  if (indentPerLevel > 9 && needIndentIndicator(string)) {\n    return STYLE_DOUBLE;\n  }\n  // At this point we know block styles are valid.\n  // Prefer literal style unless we want to fold.\n  if (!forceQuotes) {\n    return hasFoldableLine ? STYLE_FOLDED : STYLE_LITERAL;\n  }\n  return quotingType === QUOTING_TYPE_DOUBLE ? STYLE_DOUBLE : STYLE_SINGLE;\n}\n\n// Note: line breaking/folding is implemented for only the folded style.\n// NB. We drop the last trailing newline (if any) of a returned block scalar\n//  since the dumper adds its own newline. This always works:\n//    • No ending newline => unaffected; already using strip \"-\" chomping.\n//    • Ending newline    => removed then restored.\n//  Importantly, this keeps the \"+\" chomp indicator from gaining an extra line.\nfunction writeScalar(state, string, level, iskey, inblock) {\n  state.dump = (function () {\n    if (string.length === 0) {\n      return state.quotingType === QUOTING_TYPE_DOUBLE ? '\"\"' : \"''\";\n    }\n    if (!state.noCompatMode) {\n      if (DEPRECATED_BOOLEANS_SYNTAX.indexOf(string) !== -1 || DEPRECATED_BASE60_SYNTAX.test(string)) {\n        return state.quotingType === QUOTING_TYPE_DOUBLE ? ('\"' + string + '\"') : (\"'\" + string + \"'\");\n      }\n    }\n\n    var indent = state.indent * Math.max(1, level); // no 0-indent scalars\n    // As indentation gets deeper, let the width decrease monotonically\n    // to the lower bound min(state.lineWidth, 40).\n    // Note that this implies\n    //  state.lineWidth ≤ 40 + state.indent: width is fixed at the lower bound.\n    //  state.lineWidth > 40 + state.indent: width decreases until the lower bound.\n    // This behaves better than a constant minimum width which disallows narrower options,\n    // or an indent threshold which causes the width to suddenly increase.\n    var lineWidth = state.lineWidth === -1\n      ? -1 : Math.max(Math.min(state.lineWidth, 40), state.lineWidth - indent);\n\n    // Without knowing if keys are implicit/explicit, assume implicit for safety.\n    var singleLineOnly = iskey\n      // No block styles in flow mode.\n      || (state.flowLevel > -1 && level >= state.flowLevel);\n    function testAmbiguity(string) {\n      return testImplicitResolving(state, string);\n    }\n\n    switch (chooseScalarStyle(string, singleLineOnly, state.indent, lineWidth,\n      testAmbiguity, state.quotingType, state.forceQuotes && !iskey, inblock)) {\n\n      case STYLE_PLAIN:\n        return string;\n      case STYLE_SINGLE:\n        return \"'\" + string.replace(/'/g, \"''\") + \"'\";\n      case STYLE_LITERAL:\n        return '|' + blockHeader(string, state.indent)\n          + dropEndingNewline(indentString(string, indent));\n      case STYLE_FOLDED:\n        return '>' + blockHeader(string, state.indent)\n          + dropEndingNewline(indentString(foldString(string, lineWidth), indent));\n      case STYLE_DOUBLE:\n        return '\"' + escapeString(string) + '\"';\n      default:\n        throw new exception('impossible error: invalid scalar style');\n    }\n  }());\n}\n\n// Pre-conditions: string is valid for a block scalar, 1 <= indentPerLevel <= 9.\nfunction blockHeader(string, indentPerLevel) {\n  var indentIndicator = needIndentIndicator(string) ? String(indentPerLevel) : '';\n\n  // note the special case: the string '\\n' counts as a \"trailing\" empty line.\n  var clip =          string[string.length - 1] === '\\n';\n  var keep = clip && (string[string.length - 2] === '\\n' || string === '\\n');\n  var chomp = keep ? '+' : (clip ? '' : '-');\n\n  return indentIndicator + chomp + '\\n';\n}\n\n// (See the note for writeScalar.)\nfunction dropEndingNewline(string) {\n  return string[string.length - 1] === '\\n' ? string.slice(0, -1) : string;\n}\n\n// Note: a long line without a suitable break point will exceed the width limit.\n// Pre-conditions: every char in str isPrintable, str.length > 0, width > 0.\nfunction foldString(string, width) {\n  // In folded style, $k$ consecutive newlines output as $k+1$ newlines—\n  // unless they're before or after a more-indented line, or at the very\n  // beginning or end, in which case $k$ maps to $k$.\n  // Therefore, parse each chunk as newline(s) followed by a content line.\n  var lineRe = /(\\n+)([^\\n]*)/g;\n\n  // first line (possibly an empty line)\n  var result = (function () {\n    var nextLF = string.indexOf('\\n');\n    nextLF = nextLF !== -1 ? nextLF : string.length;\n    lineRe.lastIndex = nextLF;\n    return foldLine(string.slice(0, nextLF), width);\n  }());\n  // If we haven't reached the first content line yet, don't add an extra \\n.\n  var prevMoreIndented = string[0] === '\\n' || string[0] === ' ';\n  var moreIndented;\n\n  // rest of the lines\n  var match;\n  while ((match = lineRe.exec(string))) {\n    var prefix = match[1], line = match[2];\n    moreIndented = (line[0] === ' ');\n    result += prefix\n      + (!prevMoreIndented && !moreIndented && line !== ''\n        ? '\\n' : '')\n      + foldLine(line, width);\n    prevMoreIndented = moreIndented;\n  }\n\n  return result;\n}\n\n// Greedy line breaking.\n// Picks the longest line under the limit each time,\n// otherwise settles for the shortest line over the limit.\n// NB. More-indented lines *cannot* be folded, as that would add an extra \\n.\nfunction foldLine(line, width) {\n  if (line === '' || line[0] === ' ') return line;\n\n  // Since a more-indented line adds a \\n, breaks can't be followed by a space.\n  var breakRe = / [^ ]/g; // note: the match index will always be <= length-2.\n  var match;\n  // start is an inclusive index. end, curr, and next are exclusive.\n  var start = 0, end, curr = 0, next = 0;\n  var result = '';\n\n  // Invariants: 0 <= start <= length-1.\n  //   0 <= curr <= next <= max(0, length-2). curr - start <= width.\n  // Inside the loop:\n  //   A match implies length >= 2, so curr and next are <= length-2.\n  while ((match = breakRe.exec(line))) {\n    next = match.index;\n    // maintain invariant: curr - start <= width\n    if (next - start > width) {\n      end = (curr > start) ? curr : next; // derive end <= length-2\n      result += '\\n' + line.slice(start, end);\n      // skip the space that was output as \\n\n      start = end + 1;                    // derive start <= length-1\n    }\n    curr = next;\n  }\n\n  // By the invariants, start <= length-1, so there is something left over.\n  // It is either the whole string or a part starting from non-whitespace.\n  result += '\\n';\n  // Insert a break if the remainder is too long and there is a break available.\n  if (line.length - start > width && curr > start) {\n    result += line.slice(start, curr) + '\\n' + line.slice(curr + 1);\n  } else {\n    result += line.slice(start);\n  }\n\n  return result.slice(1); // drop extra \\n joiner\n}\n\n// Escapes a double-quoted string.\nfunction escapeString(string) {\n  var result = '';\n  var char = 0;\n  var escapeSeq;\n\n  for (var i = 0; i < string.length; char >= 0x10000 ? i += 2 : i++) {\n    char = codePointAt(string, i);\n    escapeSeq = ESCAPE_SEQUENCES[char];\n\n    if (!escapeSeq && isPrintable(char)) {\n      result += string[i];\n      if (char >= 0x10000) result += string[i + 1];\n    } else {\n      result += escapeSeq || encodeHex(char);\n    }\n  }\n\n  return result;\n}\n\nfunction writeFlowSequence(state, level, object) {\n  var _result = '',\n      _tag    = state.tag,\n      index,\n      length,\n      value;\n\n  for (index = 0, length = object.length; index < length; index += 1) {\n    value = object[index];\n\n    if (state.replacer) {\n      value = state.replacer.call(object, String(index), value);\n    }\n\n    // Write only valid elements, put null instead of invalid elements.\n    if (writeNode(state, level, value, false, false) ||\n        (typeof value === 'undefined' &&\n         writeNode(state, level, null, false, false))) {\n\n      if (_result !== '') _result += ',' + (!state.condenseFlow ? ' ' : '');\n      _result += state.dump;\n    }\n  }\n\n  state.tag = _tag;\n  state.dump = '[' + _result + ']';\n}\n\nfunction writeBlockSequence(state, level, object, compact) {\n  var _result = '',\n      _tag    = state.tag,\n      index,\n      length,\n      value;\n\n  for (index = 0, length = object.length; index < length; index += 1) {\n    value = object[index];\n\n    if (state.replacer) {\n      value = state.replacer.call(object, String(index), value);\n    }\n\n    // Write only valid elements, put null instead of invalid elements.\n    if (writeNode(state, level + 1, value, true, true, false, true) ||\n        (typeof value === 'undefined' &&\n         writeNode(state, level + 1, null, true, true, false, true))) {\n\n      if (!compact || _result !== '') {\n        _result += generateNextLine(state, level);\n      }\n\n      if (state.dump && CHAR_LINE_FEED === state.dump.charCodeAt(0)) {\n        _result += '-';\n      } else {\n        _result += '- ';\n      }\n\n      _result += state.dump;\n    }\n  }\n\n  state.tag = _tag;\n  state.dump = _result || '[]'; // Empty sequence if no valid values.\n}\n\nfunction writeFlowMapping(state, level, object) {\n  var _result       = '',\n      _tag          = state.tag,\n      objectKeyList = Object.keys(object),\n      index,\n      length,\n      objectKey,\n      objectValue,\n      pairBuffer;\n\n  for (index = 0, length = objectKeyList.length; index < length; index += 1) {\n\n    pairBuffer = '';\n    if (_result !== '') pairBuffer += ', ';\n\n    if (state.condenseFlow) pairBuffer += '\"';\n\n    objectKey = objectKeyList[index];\n    objectValue = object[objectKey];\n\n    if (state.replacer) {\n      objectValue = state.replacer.call(object, objectKey, objectValue);\n    }\n\n    if (!writeNode(state, level, objectKey, false, false)) {\n      continue; // Skip this pair because of invalid key;\n    }\n\n    if (state.dump.length > 1024) pairBuffer += '? ';\n\n    pairBuffer += state.dump + (state.condenseFlow ? '\"' : '') + ':' + (state.condenseFlow ? '' : ' ');\n\n    if (!writeNode(state, level, objectValue, false, false)) {\n      continue; // Skip this pair because of invalid value.\n    }\n\n    pairBuffer += state.dump;\n\n    // Both key and value are valid.\n    _result += pairBuffer;\n  }\n\n  state.tag = _tag;\n  state.dump = '{' + _result + '}';\n}\n\nfunction writeBlockMapping(state, level, object, compact) {\n  var _result       = '',\n      _tag          = state.tag,\n      objectKeyList = Object.keys(object),\n      index,\n      length,\n      objectKey,\n      objectValue,\n      explicitPair,\n      pairBuffer;\n\n  // Allow sorting keys so that the output file is deterministic\n  if (state.sortKeys === true) {\n    // Default sorting\n    objectKeyList.sort();\n  } else if (typeof state.sortKeys === 'function') {\n    // Custom sort function\n    objectKeyList.sort(state.sortKeys);\n  } else if (state.sortKeys) {\n    // Something is wrong\n    throw new exception('sortKeys must be a boolean or a function');\n  }\n\n  for (index = 0, length = objectKeyList.length; index < length; index += 1) {\n    pairBuffer = '';\n\n    if (!compact || _result !== '') {\n      pairBuffer += generateNextLine(state, level);\n    }\n\n    objectKey = objectKeyList[index];\n    objectValue = object[objectKey];\n\n    if (state.replacer) {\n      objectValue = state.replacer.call(object, objectKey, objectValue);\n    }\n\n    if (!writeNode(state, level + 1, objectKey, true, true, true)) {\n      continue; // Skip this pair because of invalid key.\n    }\n\n    explicitPair = (state.tag !== null && state.tag !== '?') ||\n                   (state.dump && state.dump.length > 1024);\n\n    if (explicitPair) {\n      if (state.dump && CHAR_LINE_FEED === state.dump.charCodeAt(0)) {\n        pairBuffer += '?';\n      } else {\n        pairBuffer += '? ';\n      }\n    }\n\n    pairBuffer += state.dump;\n\n    if (explicitPair) {\n      pairBuffer += generateNextLine(state, level);\n    }\n\n    if (!writeNode(state, level + 1, objectValue, true, explicitPair)) {\n      continue; // Skip this pair because of invalid value.\n    }\n\n    if (state.dump && CHAR_LINE_FEED === state.dump.charCodeAt(0)) {\n      pairBuffer += ':';\n    } else {\n      pairBuffer += ': ';\n    }\n\n    pairBuffer += state.dump;\n\n    // Both key and value are valid.\n    _result += pairBuffer;\n  }\n\n  state.tag = _tag;\n  state.dump = _result || '{}'; // Empty mapping if no valid pairs.\n}\n\nfunction detectType(state, object, explicit) {\n  var _result, typeList, index, length, type, style;\n\n  typeList = explicit ? state.explicitTypes : state.implicitTypes;\n\n  for (index = 0, length = typeList.length; index < length; index += 1) {\n    type = typeList[index];\n\n    if ((type.instanceOf  || type.predicate) &&\n        (!type.instanceOf || ((typeof object === 'object') && (object instanceof type.instanceOf))) &&\n        (!type.predicate  || type.predicate(object))) {\n\n      if (explicit) {\n        if (type.multi && type.representName) {\n          state.tag = type.representName(object);\n        } else {\n          state.tag = type.tag;\n        }\n      } else {\n        state.tag = '?';\n      }\n\n      if (type.represent) {\n        style = state.styleMap[type.tag] || type.defaultStyle;\n\n        if (_toString.call(type.represent) === '[object Function]') {\n          _result = type.represent(object, style);\n        } else if (_hasOwnProperty.call(type.represent, style)) {\n          _result = type.represent[style](object, style);\n        } else {\n          throw new exception('!<' + type.tag + '> tag resolver accepts not \"' + style + '\" style');\n        }\n\n        state.dump = _result;\n      }\n\n      return true;\n    }\n  }\n\n  return false;\n}\n\n// Serializes `object` and writes it to global `result`.\n// Returns true on success, or false on invalid object.\n//\nfunction writeNode(state, level, object, block, compact, iskey, isblockseq) {\n  state.tag = null;\n  state.dump = object;\n\n  if (!detectType(state, object, false)) {\n    detectType(state, object, true);\n  }\n\n  var type = _toString.call(state.dump);\n  var inblock = block;\n  var tagStr;\n\n  if (block) {\n    block = (state.flowLevel < 0 || state.flowLevel > level);\n  }\n\n  var objectOrArray = type === '[object Object]' || type === '[object Array]',\n      duplicateIndex,\n      duplicate;\n\n  if (objectOrArray) {\n    duplicateIndex = state.duplicates.indexOf(object);\n    duplicate = duplicateIndex !== -1;\n  }\n\n  if ((state.tag !== null && state.tag !== '?') || duplicate || (state.indent !== 2 && level > 0)) {\n    compact = false;\n  }\n\n  if (duplicate && state.usedDuplicates[duplicateIndex]) {\n    state.dump = '*ref_' + duplicateIndex;\n  } else {\n    if (objectOrArray && duplicate && !state.usedDuplicates[duplicateIndex]) {\n      state.usedDuplicates[duplicateIndex] = true;\n    }\n    if (type === '[object Object]') {\n      if (block && (Object.keys(state.dump).length !== 0)) {\n        writeBlockMapping(state, level, state.dump, compact);\n        if (duplicate) {\n          state.dump = '&ref_' + duplicateIndex + state.dump;\n        }\n      } else {\n        writeFlowMapping(state, level, state.dump);\n        if (duplicate) {\n          state.dump = '&ref_' + duplicateIndex + ' ' + state.dump;\n        }\n      }\n    } else if (type === '[object Array]') {\n      if (block && (state.dump.length !== 0)) {\n        if (state.noArrayIndent && !isblockseq && level > 0) {\n          writeBlockSequence(state, level - 1, state.dump, compact);\n        } else {\n          writeBlockSequence(state, level, state.dump, compact);\n        }\n        if (duplicate) {\n          state.dump = '&ref_' + duplicateIndex + state.dump;\n        }\n      } else {\n        writeFlowSequence(state, level, state.dump);\n        if (duplicate) {\n          state.dump = '&ref_' + duplicateIndex + ' ' + state.dump;\n        }\n      }\n    } else if (type === '[object String]') {\n      if (state.tag !== '?') {\n        writeScalar(state, state.dump, level, iskey, inblock);\n      }\n    } else if (type === '[object Undefined]') {\n      return false;\n    } else {\n      if (state.skipInvalid) return false;\n      throw new exception('unacceptable kind of an object to dump ' + type);\n    }\n\n    if (state.tag !== null && state.tag !== '?') {\n      // Need to encode all characters except those allowed by the spec:\n      //\n      // [35] ns-dec-digit    ::=  [#x30-#x39] /* 0-9 */\n      // [36] ns-hex-digit    ::=  ns-dec-digit\n      //                         | [#x41-#x46] /* A-F */ | [#x61-#x66] /* a-f */\n      // [37] ns-ascii-letter ::=  [#x41-#x5A] /* A-Z */ | [#x61-#x7A] /* a-z */\n      // [38] ns-word-char    ::=  ns-dec-digit | ns-ascii-letter | “-”\n      // [39] ns-uri-char     ::=  “%” ns-hex-digit ns-hex-digit | ns-word-char | “#”\n      //                         | “;” | “/” | “?” | “:” | “@” | “&” | “=” | “+” | “$” | “,”\n      //                         | “_” | “.” | “!” | “~” | “*” | “'” | “(” | “)” | “[” | “]”\n      //\n      // Also need to encode '!' because it has special meaning (end of tag prefix).\n      //\n      tagStr = encodeURI(\n        state.tag[0] === '!' ? state.tag.slice(1) : state.tag\n      ).replace(/!/g, '%21');\n\n      if (state.tag[0] === '!') {\n        tagStr = '!' + tagStr;\n      } else if (tagStr.slice(0, 18) === 'tag:yaml.org,2002:') {\n        tagStr = '!!' + tagStr.slice(18);\n      } else {\n        tagStr = '!<' + tagStr + '>';\n      }\n\n      state.dump = tagStr + ' ' + state.dump;\n    }\n  }\n\n  return true;\n}\n\nfunction getDuplicateReferences(object, state) {\n  var objects = [],\n      duplicatesIndexes = [],\n      index,\n      length;\n\n  inspectNode(object, objects, duplicatesIndexes);\n\n  for (index = 0, length = duplicatesIndexes.length; index < length; index += 1) {\n    state.duplicates.push(objects[duplicatesIndexes[index]]);\n  }\n  state.usedDuplicates = new Array(length);\n}\n\nfunction inspectNode(object, objects, duplicatesIndexes) {\n  var objectKeyList,\n      index,\n      length;\n\n  if (object !== null && typeof object === 'object') {\n    index = objects.indexOf(object);\n    if (index !== -1) {\n      if (duplicatesIndexes.indexOf(index) === -1) {\n        duplicatesIndexes.push(index);\n      }\n    } else {\n      objects.push(object);\n\n      if (Array.isArray(object)) {\n        for (index = 0, length = object.length; index < length; index += 1) {\n          inspectNode(object[index], objects, duplicatesIndexes);\n        }\n      } else {\n        objectKeyList = Object.keys(object);\n\n        for (index = 0, length = objectKeyList.length; index < length; index += 1) {\n          inspectNode(object[objectKeyList[index]], objects, duplicatesIndexes);\n        }\n      }\n    }\n  }\n}\n\nfunction dump$1(input, options) {\n  options = options || {};\n\n  var state = new State(options);\n\n  if (!state.noRefs) getDuplicateReferences(input, state);\n\n  var value = input;\n\n  if (state.replacer) {\n    value = state.replacer.call({ '': value }, '', value);\n  }\n\n  if (writeNode(state, 0, value, true, true)) return state.dump + '\\n';\n\n  return '';\n}\n\nvar dump_1 = dump$1;\n\nvar dumper = {\n\tdump: dump_1\n};\n\nfunction renamed(from, to) {\n  return function () {\n    throw new Error('Function yaml.' + from + ' is removed in js-yaml 4. ' +\n      'Use yaml.' + to + ' instead, which is now safe by default.');\n  };\n}\n\n\nvar Type                = type;\nvar Schema              = schema;\nvar FAILSAFE_SCHEMA     = failsafe;\nvar JSON_SCHEMA         = json;\nvar CORE_SCHEMA         = core;\nvar DEFAULT_SCHEMA      = _default;\nvar load                = loader.load;\nvar loadAll             = loader.loadAll;\nvar dump                = dumper.dump;\nvar YAMLException       = exception;\n\n// Re-export all types in case user wants to create custom schema\nvar types = {\n  binary:    binary,\n  float:     float,\n  map:       map,\n  null:      _null,\n  pairs:     pairs,\n  set:       set,\n  timestamp: timestamp,\n  bool:      bool,\n  int:       int,\n  merge:     merge,\n  omap:      omap,\n  seq:       seq,\n  str:       str\n};\n\n// Removed functions from JS-YAML 3.0.x\nvar safeLoad            = renamed('safeLoad', 'load');\nvar safeLoadAll         = renamed('safeLoadAll', 'loadAll');\nvar safeDump            = renamed('safeDump', 'dump');\n\nvar jsYaml = {\n\tType: Type,\n\tSchema: Schema,\n\tFAILSAFE_SCHEMA: FAILSAFE_SCHEMA,\n\tJSON_SCHEMA: JSON_SCHEMA,\n\tCORE_SCHEMA: CORE_SCHEMA,\n\tDEFAULT_SCHEMA: DEFAULT_SCHEMA,\n\tload: load,\n\tloadAll: loadAll,\n\tdump: dump,\n\tYAMLException: YAMLException,\n\ttypes: types,\n\tsafeLoad: safeLoad,\n\tsafeLoadAll: safeLoadAll,\n\tsafeDump: safeDump\n};\n\nexport default jsYaml;\nexport { CORE_SCHEMA, DEFAULT_SCHEMA, FAILSAFE_SCHEMA, JSON_SCHEMA, Schema, Type, YAMLException, dump, load, loadAll, safeDump, safeLoad, safeLoadAll, types };\n"], "mappings": ";;;AAEA,SAAS,UAAU,SAAS;AAC1B,SAAQ,OAAO,YAAY,eAAiB,YAAY;AAC1D;AAGA,SAAS,SAAS,SAAS;AACzB,SAAQ,OAAO,YAAY,YAAc,YAAY;AACvD;AAGA,SAAS,QAAQ,UAAU;AACzB,MAAI,MAAM,QAAQ,QAAQ,EAAG,QAAO;AAAA,WAC3B,UAAU,QAAQ,EAAG,QAAO,CAAC;AAEtC,SAAO,CAAE,QAAS;AACpB;AAGA,SAAS,OAAO,QAAQ,QAAQ;AAC9B,MAAI,OAAO,QAAQ,KAAK;AAExB,MAAI,QAAQ;AACV,iBAAa,OAAO,KAAK,MAAM;AAE/B,SAAK,QAAQ,GAAG,SAAS,WAAW,QAAQ,QAAQ,QAAQ,SAAS,GAAG;AACtE,YAAM,WAAW,KAAK;AACtB,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAC1B;AAAA,EACF;AAEA,SAAO;AACT;AAGA,SAAS,OAAO,QAAQ,OAAO;AAC7B,MAAI,SAAS,IAAI;AAEjB,OAAK,QAAQ,GAAG,QAAQ,OAAO,SAAS,GAAG;AACzC,cAAU;AAAA,EACZ;AAEA,SAAO;AACT;AAGA,SAAS,eAAe,QAAQ;AAC9B,SAAQ,WAAW,KAAO,OAAO,sBAAsB,IAAI;AAC7D;AAGA,IAAI,cAAmB;AACvB,IAAI,aAAmB;AACvB,IAAI,YAAmB;AACvB,IAAI,WAAmB;AACvB,IAAI,mBAAmB;AACvB,IAAI,WAAmB;AAEvB,IAAI,SAAS;AAAA,EACZ,WAAW;AAAA,EACX,UAAU;AAAA,EACV,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,gBAAgB;AAAA,EAChB,QAAQ;AACT;AAKA,SAAS,YAAYA,YAAW,SAAS;AACvC,MAAI,QAAQ,IAAI,UAAUA,WAAU,UAAU;AAE9C,MAAI,CAACA,WAAU,KAAM,QAAO;AAE5B,MAAIA,WAAU,KAAK,MAAM;AACvB,aAAS,SAASA,WAAU,KAAK,OAAO;AAAA,EAC1C;AAEA,WAAS,OAAOA,WAAU,KAAK,OAAO,KAAK,OAAOA,WAAU,KAAK,SAAS,KAAK;AAE/E,MAAI,CAAC,WAAWA,WAAU,KAAK,SAAS;AACtC,aAAS,SAASA,WAAU,KAAK;AAAA,EACnC;AAEA,SAAO,UAAU,MAAM;AACzB;AAGA,SAAS,gBAAgB,QAAQ,MAAM;AAErC,QAAM,KAAK,IAAI;AAEf,OAAK,OAAO;AACZ,OAAK,SAAS;AACd,OAAK,OAAO;AACZ,OAAK,UAAU,YAAY,MAAM,KAAK;AAGtC,MAAI,MAAM,mBAAmB;AAE3B,UAAM,kBAAkB,MAAM,KAAK,WAAW;AAAA,EAChD,OAAO;AAEL,SAAK,QAAS,IAAI,MAAM,EAAG,SAAS;AAAA,EACtC;AACF;AAIA,gBAAgB,YAAY,OAAO,OAAO,MAAM,SAAS;AACzD,gBAAgB,UAAU,cAAc;AAGxC,gBAAgB,UAAU,WAAW,SAAS,SAAS,SAAS;AAC9D,SAAO,KAAK,OAAO,OAAO,YAAY,MAAM,OAAO;AACrD;AAGA,IAAI,YAAY;AAGhB,SAAS,QAAQ,QAAQ,WAAW,SAAS,UAAU,eAAe;AACpE,MAAI,OAAO;AACX,MAAI,OAAO;AACX,MAAI,gBAAgB,KAAK,MAAM,gBAAgB,CAAC,IAAI;AAEpD,MAAI,WAAW,YAAY,eAAe;AACxC,WAAO;AACP,gBAAY,WAAW,gBAAgB,KAAK;AAAA,EAC9C;AAEA,MAAI,UAAU,WAAW,eAAe;AACtC,WAAO;AACP,cAAU,WAAW,gBAAgB,KAAK;AAAA,EAC5C;AAEA,SAAO;AAAA,IACL,KAAK,OAAO,OAAO,MAAM,WAAW,OAAO,EAAE,QAAQ,OAAO,GAAG,IAAI;AAAA,IACnE,KAAK,WAAW,YAAY,KAAK;AAAA;AAAA,EACnC;AACF;AAGA,SAAS,SAAS,QAAQ,KAAK;AAC7B,SAAO,OAAO,OAAO,KAAK,MAAM,OAAO,MAAM,IAAI;AACnD;AAGA,SAAS,YAAY,MAAM,SAAS;AAClC,YAAU,OAAO,OAAO,WAAW,IAAI;AAEvC,MAAI,CAAC,KAAK,OAAQ,QAAO;AAEzB,MAAI,CAAC,QAAQ,UAAW,SAAQ,YAAY;AAC5C,MAAI,OAAO,QAAQ,WAAgB,SAAU,SAAQ,SAAc;AACnE,MAAI,OAAO,QAAQ,gBAAgB,SAAU,SAAQ,cAAc;AACnE,MAAI,OAAO,QAAQ,eAAgB,SAAU,SAAQ,aAAc;AAEnE,MAAI,KAAK;AACT,MAAI,aAAa,CAAE,CAAE;AACrB,MAAI,WAAW,CAAC;AAChB,MAAI;AACJ,MAAI,cAAc;AAElB,SAAQ,QAAQ,GAAG,KAAK,KAAK,MAAM,GAAI;AACrC,aAAS,KAAK,MAAM,KAAK;AACzB,eAAW,KAAK,MAAM,QAAQ,MAAM,CAAC,EAAE,MAAM;AAE7C,QAAI,KAAK,YAAY,MAAM,SAAS,cAAc,GAAG;AACnD,oBAAc,WAAW,SAAS;AAAA,IACpC;AAAA,EACF;AAEA,MAAI,cAAc,EAAG,eAAc,WAAW,SAAS;AAEvD,MAAI,SAAS,IAAI,GAAG;AACpB,MAAI,eAAe,KAAK,IAAI,KAAK,OAAO,QAAQ,YAAY,SAAS,MAAM,EAAE,SAAS,EAAE;AACxF,MAAI,gBAAgB,QAAQ,aAAa,QAAQ,SAAS,eAAe;AAEzE,OAAK,IAAI,GAAG,KAAK,QAAQ,aAAa,KAAK;AACzC,QAAI,cAAc,IAAI,EAAG;AACzB,WAAO;AAAA,MACL,KAAK;AAAA,MACL,WAAW,cAAc,CAAC;AAAA,MAC1B,SAAS,cAAc,CAAC;AAAA,MACxB,KAAK,YAAY,WAAW,WAAW,IAAI,WAAW,cAAc,CAAC;AAAA,MACrE;AAAA,IACF;AACA,aAAS,OAAO,OAAO,KAAK,QAAQ,MAAM,IAAI,UAAU,KAAK,OAAO,IAAI,GAAG,SAAS,GAAG,YAAY,IACjG,QAAQ,KAAK,MAAM,OAAO;AAAA,EAC9B;AAEA,SAAO,QAAQ,KAAK,QAAQ,WAAW,WAAW,GAAG,SAAS,WAAW,GAAG,KAAK,UAAU,aAAa;AACxG,YAAU,OAAO,OAAO,KAAK,QAAQ,MAAM,IAAI,UAAU,KAAK,OAAO,GAAG,SAAS,GAAG,YAAY,IAC9F,QAAQ,KAAK,MAAM;AACrB,YAAU,OAAO,OAAO,KAAK,QAAQ,SAAS,eAAe,IAAI,KAAK,GAAG,IAAI;AAE7E,OAAK,IAAI,GAAG,KAAK,QAAQ,YAAY,KAAK;AACxC,QAAI,cAAc,KAAK,SAAS,OAAQ;AACxC,WAAO;AAAA,MACL,KAAK;AAAA,MACL,WAAW,cAAc,CAAC;AAAA,MAC1B,SAAS,cAAc,CAAC;AAAA,MACxB,KAAK,YAAY,WAAW,WAAW,IAAI,WAAW,cAAc,CAAC;AAAA,MACrE;AAAA,IACF;AACA,cAAU,OAAO,OAAO,KAAK,QAAQ,MAAM,IAAI,UAAU,KAAK,OAAO,IAAI,GAAG,SAAS,GAAG,YAAY,IAClG,QAAQ,KAAK,MAAM;AAAA,EACvB;AAEA,SAAO,OAAO,QAAQ,OAAO,EAAE;AACjC;AAGA,IAAI,UAAU;AAEd,IAAI,2BAA2B;AAAA,EAC7B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAI,kBAAkB;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AACF;AAEA,SAAS,oBAAoBC,MAAK;AAChC,MAAI,SAAS,CAAC;AAEd,MAAIA,SAAQ,MAAM;AAChB,WAAO,KAAKA,IAAG,EAAE,QAAQ,SAAU,OAAO;AACxC,MAAAA,KAAI,KAAK,EAAE,QAAQ,SAAU,OAAO;AAClC,eAAO,OAAO,KAAK,CAAC,IAAI;AAAA,MAC1B,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,SAAO;AACT;AAEA,SAAS,OAAO,KAAK,SAAS;AAC5B,YAAU,WAAW,CAAC;AAEtB,SAAO,KAAK,OAAO,EAAE,QAAQ,SAAU,MAAM;AAC3C,QAAI,yBAAyB,QAAQ,IAAI,MAAM,IAAI;AACjD,YAAM,IAAI,UAAU,qBAAqB,OAAO,gCAAgC,MAAM,cAAc;AAAA,IACtG;AAAA,EACF,CAAC;AAGD,OAAK,UAAgB;AACrB,OAAK,MAAgB;AACrB,OAAK,OAAgB,QAAQ,MAAM,KAAc;AACjD,OAAK,UAAgB,QAAQ,SAAS,KAAW,WAAY;AAAE,WAAO;AAAA,EAAM;AAC5E,OAAK,YAAgB,QAAQ,WAAW,KAAS,SAAU,MAAM;AAAE,WAAO;AAAA,EAAM;AAChF,OAAK,aAAgB,QAAQ,YAAY,KAAQ;AACjD,OAAK,YAAgB,QAAQ,WAAW,KAAS;AACjD,OAAK,YAAgB,QAAQ,WAAW,KAAS;AACjD,OAAK,gBAAgB,QAAQ,eAAe,KAAK;AACjD,OAAK,eAAgB,QAAQ,cAAc,KAAM;AACjD,OAAK,QAAgB,QAAQ,OAAO,KAAa;AACjD,OAAK,eAAgB,oBAAoB,QAAQ,cAAc,KAAK,IAAI;AAExE,MAAI,gBAAgB,QAAQ,KAAK,IAAI,MAAM,IAAI;AAC7C,UAAM,IAAI,UAAU,mBAAmB,KAAK,OAAO,yBAAyB,MAAM,cAAc;AAAA,EAClG;AACF;AAEA,IAAI,OAAO;AAQX,SAAS,YAAYC,SAAQ,MAAM;AACjC,MAAI,SAAS,CAAC;AAEd,EAAAA,QAAO,IAAI,EAAE,QAAQ,SAAU,aAAa;AAC1C,QAAI,WAAW,OAAO;AAEtB,WAAO,QAAQ,SAAU,cAAc,eAAe;AACpD,UAAI,aAAa,QAAQ,YAAY,OACjC,aAAa,SAAS,YAAY,QAClC,aAAa,UAAU,YAAY,OAAO;AAE5C,mBAAW;AAAA,MACb;AAAA,IACF,CAAC;AAED,WAAO,QAAQ,IAAI;AAAA,EACrB,CAAC;AAED,SAAO;AACT;AAGA,SAAS,aAA2B;AAClC,MAAI,SAAS;AAAA,IACP,QAAQ,CAAC;AAAA,IACT,UAAU,CAAC;AAAA,IACX,SAAS,CAAC;AAAA,IACV,UAAU,CAAC;AAAA,IACX,OAAO;AAAA,MACL,QAAQ,CAAC;AAAA,MACT,UAAU,CAAC;AAAA,MACX,SAAS,CAAC;AAAA,MACV,UAAU,CAAC;AAAA,IACb;AAAA,EACF,GAAG,OAAO;AAEd,WAAS,YAAYC,OAAM;AACzB,QAAIA,MAAK,OAAO;AACd,aAAO,MAAMA,MAAK,IAAI,EAAE,KAAKA,KAAI;AACjC,aAAO,MAAM,UAAU,EAAE,KAAKA,KAAI;AAAA,IACpC,OAAO;AACL,aAAOA,MAAK,IAAI,EAAEA,MAAK,GAAG,IAAI,OAAO,UAAU,EAAEA,MAAK,GAAG,IAAIA;AAAA,IAC/D;AAAA,EACF;AAEA,OAAK,QAAQ,GAAG,SAAS,UAAU,QAAQ,QAAQ,QAAQ,SAAS,GAAG;AACrE,cAAU,KAAK,EAAE,QAAQ,WAAW;AAAA,EACtC;AACA,SAAO;AACT;AAGA,SAAS,SAAS,YAAY;AAC5B,SAAO,KAAK,OAAO,UAAU;AAC/B;AAGA,SAAS,UAAU,SAAS,SAASC,QAAO,YAAY;AACtD,MAAI,WAAW,CAAC;AAChB,MAAI,WAAW,CAAC;AAEhB,MAAI,sBAAsB,MAAM;AAE9B,aAAS,KAAK,UAAU;AAAA,EAE1B,WAAW,MAAM,QAAQ,UAAU,GAAG;AAEpC,eAAW,SAAS,OAAO,UAAU;AAAA,EAEvC,WAAW,eAAe,MAAM,QAAQ,WAAW,QAAQ,KAAK,MAAM,QAAQ,WAAW,QAAQ,IAAI;AAEnG,QAAI,WAAW,SAAU,YAAW,SAAS,OAAO,WAAW,QAAQ;AACvE,QAAI,WAAW,SAAU,YAAW,SAAS,OAAO,WAAW,QAAQ;AAAA,EAEzE,OAAO;AACL,UAAM,IAAI,UAAU,kHAC6C;AAAA,EACnE;AAEA,WAAS,QAAQ,SAAU,QAAQ;AACjC,QAAI,EAAE,kBAAkB,OAAO;AAC7B,YAAM,IAAI,UAAU,oFAAoF;AAAA,IAC1G;AAEA,QAAI,OAAO,YAAY,OAAO,aAAa,UAAU;AACnD,YAAM,IAAI,UAAU,iHAAiH;AAAA,IACvI;AAEA,QAAI,OAAO,OAAO;AAChB,YAAM,IAAI,UAAU,oGAAoG;AAAA,IAC1H;AAAA,EACF,CAAC;AAED,WAAS,QAAQ,SAAU,QAAQ;AACjC,QAAI,EAAE,kBAAkB,OAAO;AAC7B,YAAM,IAAI,UAAU,oFAAoF;AAAA,IAC1G;AAAA,EACF,CAAC;AAED,MAAI,SAAS,OAAO,OAAO,SAAS,SAAS;AAE7C,SAAO,YAAY,KAAK,YAAY,CAAC,GAAG,OAAO,QAAQ;AACvD,SAAO,YAAY,KAAK,YAAY,CAAC,GAAG,OAAO,QAAQ;AAEvD,SAAO,mBAAmB,YAAY,QAAQ,UAAU;AACxD,SAAO,mBAAmB,YAAY,QAAQ,UAAU;AACxD,SAAO,kBAAmB,WAAW,OAAO,kBAAkB,OAAO,gBAAgB;AAErF,SAAO;AACT;AAGA,IAAI,SAAS;AAEb,IAAI,MAAM,IAAI,KAAK,yBAAyB;AAAA,EAC1C,MAAM;AAAA,EACN,WAAW,SAAU,MAAM;AAAE,WAAO,SAAS,OAAO,OAAO;AAAA,EAAI;AACjE,CAAC;AAED,IAAI,MAAM,IAAI,KAAK,yBAAyB;AAAA,EAC1C,MAAM;AAAA,EACN,WAAW,SAAU,MAAM;AAAE,WAAO,SAAS,OAAO,OAAO,CAAC;AAAA,EAAG;AACjE,CAAC;AAED,IAAI,MAAM,IAAI,KAAK,yBAAyB;AAAA,EAC1C,MAAM;AAAA,EACN,WAAW,SAAU,MAAM;AAAE,WAAO,SAAS,OAAO,OAAO,CAAC;AAAA,EAAG;AACjE,CAAC;AAED,IAAI,WAAW,IAAI,OAAO;AAAA,EACxB,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF,CAAC;AAED,SAAS,gBAAgB,MAAM;AAC7B,MAAI,SAAS,KAAM,QAAO;AAE1B,MAAI,MAAM,KAAK;AAEf,SAAQ,QAAQ,KAAK,SAAS,OACtB,QAAQ,MAAM,SAAS,UAAU,SAAS,UAAU,SAAS;AACvE;AAEA,SAAS,oBAAoB;AAC3B,SAAO;AACT;AAEA,SAAS,OAAO,QAAQ;AACtB,SAAO,WAAW;AACpB;AAEA,IAAI,QAAQ,IAAI,KAAK,0BAA0B;AAAA,EAC7C,MAAM;AAAA,EACN,SAAS;AAAA,EACT,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,IACT,WAAW,WAAY;AAAE,aAAO;AAAA,IAAQ;AAAA,IACxC,WAAW,WAAY;AAAE,aAAO;AAAA,IAAQ;AAAA,IACxC,WAAW,WAAY;AAAE,aAAO;AAAA,IAAQ;AAAA,IACxC,WAAW,WAAY;AAAE,aAAO;AAAA,IAAQ;AAAA,IACxC,OAAW,WAAY;AAAE,aAAO;AAAA,IAAQ;AAAA,EAC1C;AAAA,EACA,cAAc;AAChB,CAAC;AAED,SAAS,mBAAmB,MAAM;AAChC,MAAI,SAAS,KAAM,QAAO;AAE1B,MAAI,MAAM,KAAK;AAEf,SAAQ,QAAQ,MAAM,SAAS,UAAU,SAAS,UAAU,SAAS,WAC7D,QAAQ,MAAM,SAAS,WAAW,SAAS,WAAW,SAAS;AACzE;AAEA,SAAS,qBAAqB,MAAM;AAClC,SAAO,SAAS,UACT,SAAS,UACT,SAAS;AAClB;AAEA,SAAS,UAAU,QAAQ;AACzB,SAAO,OAAO,UAAU,SAAS,KAAK,MAAM,MAAM;AACpD;AAEA,IAAI,OAAO,IAAI,KAAK,0BAA0B;AAAA,EAC5C,MAAM;AAAA,EACN,SAAS;AAAA,EACT,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,IACT,WAAW,SAAU,QAAQ;AAAE,aAAO,SAAS,SAAS;AAAA,IAAS;AAAA,IACjE,WAAW,SAAU,QAAQ;AAAE,aAAO,SAAS,SAAS;AAAA,IAAS;AAAA,IACjE,WAAW,SAAU,QAAQ;AAAE,aAAO,SAAS,SAAS;AAAA,IAAS;AAAA,EACnE;AAAA,EACA,cAAc;AAChB,CAAC;AAED,SAAS,UAAU,GAAG;AACpB,SAAS,MAAe,KAAO,KAAK,MAC3B,MAAe,KAAO,KAAK,MAC3B,MAAe,KAAO,KAAK;AACtC;AAEA,SAAS,UAAU,GAAG;AACpB,SAAS,MAAe,KAAO,KAAK;AACtC;AAEA,SAAS,UAAU,GAAG;AACpB,SAAS,MAAe,KAAO,KAAK;AACtC;AAEA,SAAS,mBAAmB,MAAM;AAChC,MAAI,SAAS,KAAM,QAAO;AAE1B,MAAI,MAAM,KAAK,QACX,QAAQ,GACR,YAAY,OACZ;AAEJ,MAAI,CAAC,IAAK,QAAO;AAEjB,OAAK,KAAK,KAAK;AAGf,MAAI,OAAO,OAAO,OAAO,KAAK;AAC5B,SAAK,KAAK,EAAE,KAAK;AAAA,EACnB;AAEA,MAAI,OAAO,KAAK;AAEd,QAAI,QAAQ,MAAM,IAAK,QAAO;AAC9B,SAAK,KAAK,EAAE,KAAK;AAIjB,QAAI,OAAO,KAAK;AAEd;AAEA,aAAO,QAAQ,KAAK,SAAS;AAC3B,aAAK,KAAK,KAAK;AACf,YAAI,OAAO,IAAK;AAChB,YAAI,OAAO,OAAO,OAAO,IAAK,QAAO;AACrC,oBAAY;AAAA,MACd;AACA,aAAO,aAAa,OAAO;AAAA,IAC7B;AAGA,QAAI,OAAO,KAAK;AAEd;AAEA,aAAO,QAAQ,KAAK,SAAS;AAC3B,aAAK,KAAK,KAAK;AACf,YAAI,OAAO,IAAK;AAChB,YAAI,CAAC,UAAU,KAAK,WAAW,KAAK,CAAC,EAAG,QAAO;AAC/C,oBAAY;AAAA,MACd;AACA,aAAO,aAAa,OAAO;AAAA,IAC7B;AAGA,QAAI,OAAO,KAAK;AAEd;AAEA,aAAO,QAAQ,KAAK,SAAS;AAC3B,aAAK,KAAK,KAAK;AACf,YAAI,OAAO,IAAK;AAChB,YAAI,CAAC,UAAU,KAAK,WAAW,KAAK,CAAC,EAAG,QAAO;AAC/C,oBAAY;AAAA,MACd;AACA,aAAO,aAAa,OAAO;AAAA,IAC7B;AAAA,EACF;AAKA,MAAI,OAAO,IAAK,QAAO;AAEvB,SAAO,QAAQ,KAAK,SAAS;AAC3B,SAAK,KAAK,KAAK;AACf,QAAI,OAAO,IAAK;AAChB,QAAI,CAAC,UAAU,KAAK,WAAW,KAAK,CAAC,GAAG;AACtC,aAAO;AAAA,IACT;AACA,gBAAY;AAAA,EACd;AAGA,MAAI,CAAC,aAAa,OAAO,IAAK,QAAO;AAErC,SAAO;AACT;AAEA,SAAS,qBAAqB,MAAM;AAClC,MAAI,QAAQ,MAAM,OAAO,GAAG;AAE5B,MAAI,MAAM,QAAQ,GAAG,MAAM,IAAI;AAC7B,YAAQ,MAAM,QAAQ,MAAM,EAAE;AAAA,EAChC;AAEA,OAAK,MAAM,CAAC;AAEZ,MAAI,OAAO,OAAO,OAAO,KAAK;AAC5B,QAAI,OAAO,IAAK,QAAO;AACvB,YAAQ,MAAM,MAAM,CAAC;AACrB,SAAK,MAAM,CAAC;AAAA,EACd;AAEA,MAAI,UAAU,IAAK,QAAO;AAE1B,MAAI,OAAO,KAAK;AACd,QAAI,MAAM,CAAC,MAAM,IAAK,QAAO,OAAO,SAAS,MAAM,MAAM,CAAC,GAAG,CAAC;AAC9D,QAAI,MAAM,CAAC,MAAM,IAAK,QAAO,OAAO,SAAS,MAAM,MAAM,CAAC,GAAG,EAAE;AAC/D,QAAI,MAAM,CAAC,MAAM,IAAK,QAAO,OAAO,SAAS,MAAM,MAAM,CAAC,GAAG,CAAC;AAAA,EAChE;AAEA,SAAO,OAAO,SAAS,OAAO,EAAE;AAClC;AAEA,SAAS,UAAU,QAAQ;AACzB,SAAQ,OAAO,UAAU,SAAS,KAAK,MAAM,MAAO,sBAC5C,SAAS,MAAM,KAAK,CAAC,OAAO,eAAe,MAAM;AAC3D;AAEA,IAAI,MAAM,IAAI,KAAK,yBAAyB;AAAA,EAC1C,MAAM;AAAA,EACN,SAAS;AAAA,EACT,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,IACT,QAAa,SAAU,KAAK;AAAE,aAAO,OAAO,IAAI,OAAO,IAAI,SAAS,CAAC,IAAI,QAAQ,IAAI,SAAS,CAAC,EAAE,MAAM,CAAC;AAAA,IAAG;AAAA,IAC3G,OAAa,SAAU,KAAK;AAAE,aAAO,OAAO,IAAI,OAAQ,IAAI,SAAS,CAAC,IAAI,QAAS,IAAI,SAAS,CAAC,EAAE,MAAM,CAAC;AAAA,IAAG;AAAA,IAC7G,SAAa,SAAU,KAAK;AAAE,aAAO,IAAI,SAAS,EAAE;AAAA,IAAG;AAAA;AAAA,IAEvD,aAAa,SAAU,KAAK;AAAE,aAAO,OAAO,IAAI,OAAO,IAAI,SAAS,EAAE,EAAE,YAAY,IAAK,QAAQ,IAAI,SAAS,EAAE,EAAE,YAAY,EAAE,MAAM,CAAC;AAAA,IAAG;AAAA,EAC5I;AAAA,EACA,cAAc;AAAA,EACd,cAAc;AAAA,IACZ,QAAa,CAAE,GAAI,KAAM;AAAA,IACzB,OAAa,CAAE,GAAI,KAAM;AAAA,IACzB,SAAa,CAAE,IAAI,KAAM;AAAA,IACzB,aAAa,CAAE,IAAI,KAAM;AAAA,EAC3B;AACF,CAAC;AAED,IAAI,qBAAqB,IAAI;AAAA;AAAA,EAE3B;AAOuB;AAEzB,SAAS,iBAAiB,MAAM;AAC9B,MAAI,SAAS,KAAM,QAAO;AAE1B,MAAI,CAAC,mBAAmB,KAAK,IAAI;AAAA;AAAA,EAG7B,KAAK,KAAK,SAAS,CAAC,MAAM,KAAK;AACjC,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAEA,SAAS,mBAAmB,MAAM;AAChC,MAAI,OAAO;AAEX,UAAS,KAAK,QAAQ,MAAM,EAAE,EAAE,YAAY;AAC5C,SAAS,MAAM,CAAC,MAAM,MAAM,KAAK;AAEjC,MAAI,KAAK,QAAQ,MAAM,CAAC,CAAC,KAAK,GAAG;AAC/B,YAAQ,MAAM,MAAM,CAAC;AAAA,EACvB;AAEA,MAAI,UAAU,QAAQ;AACpB,WAAQ,SAAS,IAAK,OAAO,oBAAoB,OAAO;AAAA,EAE1D,WAAW,UAAU,QAAQ;AAC3B,WAAO;AAAA,EACT;AACA,SAAO,OAAO,WAAW,OAAO,EAAE;AACpC;AAGA,IAAI,yBAAyB;AAE7B,SAAS,mBAAmB,QAAQ,OAAO;AACzC,MAAI;AAEJ,MAAI,MAAM,MAAM,GAAG;AACjB,YAAQ,OAAO;AAAA,MACb,KAAK;AAAa,eAAO;AAAA,MACzB,KAAK;AAAa,eAAO;AAAA,MACzB,KAAK;AAAa,eAAO;AAAA,IAC3B;AAAA,EACF,WAAW,OAAO,sBAAsB,QAAQ;AAC9C,YAAQ,OAAO;AAAA,MACb,KAAK;AAAa,eAAO;AAAA,MACzB,KAAK;AAAa,eAAO;AAAA,MACzB,KAAK;AAAa,eAAO;AAAA,IAC3B;AAAA,EACF,WAAW,OAAO,sBAAsB,QAAQ;AAC9C,YAAQ,OAAO;AAAA,MACb,KAAK;AAAa,eAAO;AAAA,MACzB,KAAK;AAAa,eAAO;AAAA,MACzB,KAAK;AAAa,eAAO;AAAA,IAC3B;AAAA,EACF,WAAW,OAAO,eAAe,MAAM,GAAG;AACxC,WAAO;AAAA,EACT;AAEA,QAAM,OAAO,SAAS,EAAE;AAKxB,SAAO,uBAAuB,KAAK,GAAG,IAAI,IAAI,QAAQ,KAAK,IAAI,IAAI;AACrE;AAEA,SAAS,QAAQ,QAAQ;AACvB,SAAQ,OAAO,UAAU,SAAS,KAAK,MAAM,MAAM,sBAC3C,SAAS,MAAM,KAAK,OAAO,eAAe,MAAM;AAC1D;AAEA,IAAI,QAAQ,IAAI,KAAK,2BAA2B;AAAA,EAC9C,MAAM;AAAA,EACN,SAAS;AAAA,EACT,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,cAAc;AAChB,CAAC;AAED,IAAI,OAAO,SAAS,OAAO;AAAA,EACzB,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF,CAAC;AAED,IAAI,OAAO;AAEX,IAAI,mBAAmB,IAAI;AAAA,EACzB;AAEgB;AAElB,IAAI,wBAAwB,IAAI;AAAA,EAC9B;AASwB;AAE1B,SAAS,qBAAqB,MAAM;AAClC,MAAI,SAAS,KAAM,QAAO;AAC1B,MAAI,iBAAiB,KAAK,IAAI,MAAM,KAAM,QAAO;AACjD,MAAI,sBAAsB,KAAK,IAAI,MAAM,KAAM,QAAO;AACtD,SAAO;AACT;AAEA,SAAS,uBAAuB,MAAM;AACpC,MAAI,OAAO,MAAM,OAAO,KAAK,MAAM,QAAQ,QAAQ,WAAW,GAC1D,QAAQ,MAAM,SAAS,WAAW;AAEtC,UAAQ,iBAAiB,KAAK,IAAI;AAClC,MAAI,UAAU,KAAM,SAAQ,sBAAsB,KAAK,IAAI;AAE3D,MAAI,UAAU,KAAM,OAAM,IAAI,MAAM,oBAAoB;AAIxD,SAAO,CAAE,MAAM,CAAC;AAChB,UAAQ,CAAE,MAAM,CAAC,IAAK;AACtB,QAAM,CAAE,MAAM,CAAC;AAEf,MAAI,CAAC,MAAM,CAAC,GAAG;AACb,WAAO,IAAI,KAAK,KAAK,IAAI,MAAM,OAAO,GAAG,CAAC;AAAA,EAC5C;AAIA,SAAO,CAAE,MAAM,CAAC;AAChB,WAAS,CAAE,MAAM,CAAC;AAClB,WAAS,CAAE,MAAM,CAAC;AAElB,MAAI,MAAM,CAAC,GAAG;AACZ,eAAW,MAAM,CAAC,EAAE,MAAM,GAAG,CAAC;AAC9B,WAAO,SAAS,SAAS,GAAG;AAC1B,kBAAY;AAAA,IACd;AACA,eAAW,CAAC;AAAA,EACd;AAIA,MAAI,MAAM,CAAC,GAAG;AACZ,cAAU,CAAE,MAAM,EAAE;AACpB,gBAAY,EAAE,MAAM,EAAE,KAAK;AAC3B,aAAS,UAAU,KAAK,aAAa;AACrC,QAAI,MAAM,CAAC,MAAM,IAAK,SAAQ,CAAC;AAAA,EACjC;AAEA,SAAO,IAAI,KAAK,KAAK,IAAI,MAAM,OAAO,KAAK,MAAM,QAAQ,QAAQ,QAAQ,CAAC;AAE1E,MAAI,MAAO,MAAK,QAAQ,KAAK,QAAQ,IAAI,KAAK;AAE9C,SAAO;AACT;AAEA,SAAS,uBAAuB,QAAoB;AAClD,SAAO,OAAO,YAAY;AAC5B;AAEA,IAAI,YAAY,IAAI,KAAK,+BAA+B;AAAA,EACtD,MAAM;AAAA,EACN,SAAS;AAAA,EACT,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AACb,CAAC;AAED,SAAS,iBAAiB,MAAM;AAC9B,SAAO,SAAS,QAAQ,SAAS;AACnC;AAEA,IAAI,QAAQ,IAAI,KAAK,2BAA2B;AAAA,EAC9C,MAAM;AAAA,EACN,SAAS;AACX,CAAC;AASD,IAAI,aAAa;AAGjB,SAAS,kBAAkB,MAAM;AAC/B,MAAI,SAAS,KAAM,QAAO;AAE1B,MAAI,MAAM,KAAK,SAAS,GAAG,MAAM,KAAK,QAAQH,OAAM;AAGpD,OAAK,MAAM,GAAG,MAAM,KAAK,OAAO;AAC9B,WAAOA,KAAI,QAAQ,KAAK,OAAO,GAAG,CAAC;AAGnC,QAAI,OAAO,GAAI;AAGf,QAAI,OAAO,EAAG,QAAO;AAErB,cAAU;AAAA,EACZ;AAGA,SAAQ,SAAS,MAAO;AAC1B;AAEA,SAAS,oBAAoB,MAAM;AACjC,MAAI,KAAK,UACL,QAAQ,KAAK,QAAQ,YAAY,EAAE,GACnC,MAAM,MAAM,QACZA,OAAM,YACN,OAAO,GACP,SAAS,CAAC;AAId,OAAK,MAAM,GAAG,MAAM,KAAK,OAAO;AAC9B,QAAK,MAAM,MAAM,KAAM,KAAK;AAC1B,aAAO,KAAM,QAAQ,KAAM,GAAI;AAC/B,aAAO,KAAM,QAAQ,IAAK,GAAI;AAC9B,aAAO,KAAK,OAAO,GAAI;AAAA,IACzB;AAEA,WAAQ,QAAQ,IAAKA,KAAI,QAAQ,MAAM,OAAO,GAAG,CAAC;AAAA,EACpD;AAIA,aAAY,MAAM,IAAK;AAEvB,MAAI,aAAa,GAAG;AAClB,WAAO,KAAM,QAAQ,KAAM,GAAI;AAC/B,WAAO,KAAM,QAAQ,IAAK,GAAI;AAC9B,WAAO,KAAK,OAAO,GAAI;AAAA,EACzB,WAAW,aAAa,IAAI;AAC1B,WAAO,KAAM,QAAQ,KAAM,GAAI;AAC/B,WAAO,KAAM,QAAQ,IAAK,GAAI;AAAA,EAChC,WAAW,aAAa,IAAI;AAC1B,WAAO,KAAM,QAAQ,IAAK,GAAI;AAAA,EAChC;AAEA,SAAO,IAAI,WAAW,MAAM;AAC9B;AAEA,SAAS,oBAAoB,QAAoB;AAC/C,MAAI,SAAS,IAAI,OAAO,GAAG,KAAK,MAC5B,MAAM,OAAO,QACbA,OAAM;AAIV,OAAK,MAAM,GAAG,MAAM,KAAK,OAAO;AAC9B,QAAK,MAAM,MAAM,KAAM,KAAK;AAC1B,gBAAUA,KAAK,QAAQ,KAAM,EAAI;AACjC,gBAAUA,KAAK,QAAQ,KAAM,EAAI;AACjC,gBAAUA,KAAK,QAAQ,IAAK,EAAI;AAChC,gBAAUA,KAAI,OAAO,EAAI;AAAA,IAC3B;AAEA,YAAQ,QAAQ,KAAK,OAAO,GAAG;AAAA,EACjC;AAIA,SAAO,MAAM;AAEb,MAAI,SAAS,GAAG;AACd,cAAUA,KAAK,QAAQ,KAAM,EAAI;AACjC,cAAUA,KAAK,QAAQ,KAAM,EAAI;AACjC,cAAUA,KAAK,QAAQ,IAAK,EAAI;AAChC,cAAUA,KAAI,OAAO,EAAI;AAAA,EAC3B,WAAW,SAAS,GAAG;AACrB,cAAUA,KAAK,QAAQ,KAAM,EAAI;AACjC,cAAUA,KAAK,QAAQ,IAAK,EAAI;AAChC,cAAUA,KAAK,QAAQ,IAAK,EAAI;AAChC,cAAUA,KAAI,EAAE;AAAA,EAClB,WAAW,SAAS,GAAG;AACrB,cAAUA,KAAK,QAAQ,IAAK,EAAI;AAChC,cAAUA,KAAK,QAAQ,IAAK,EAAI;AAChC,cAAUA,KAAI,EAAE;AAChB,cAAUA,KAAI,EAAE;AAAA,EAClB;AAEA,SAAO;AACT;AAEA,SAAS,SAAS,KAAK;AACrB,SAAO,OAAO,UAAU,SAAS,KAAK,GAAG,MAAO;AAClD;AAEA,IAAI,SAAS,IAAI,KAAK,4BAA4B;AAAA,EAChD,MAAM;AAAA,EACN,SAAS;AAAA,EACT,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AACb,CAAC;AAED,IAAI,oBAAoB,OAAO,UAAU;AACzC,IAAI,cAAoB,OAAO,UAAU;AAEzC,SAAS,gBAAgB,MAAM;AAC7B,MAAI,SAAS,KAAM,QAAO;AAE1B,MAAI,aAAa,CAAC,GAAG,OAAO,QAAQ,MAAM,SAAS,YAC/C,SAAS;AAEb,OAAK,QAAQ,GAAG,SAAS,OAAO,QAAQ,QAAQ,QAAQ,SAAS,GAAG;AAClE,WAAO,OAAO,KAAK;AACnB,iBAAa;AAEb,QAAI,YAAY,KAAK,IAAI,MAAM,kBAAmB,QAAO;AAEzD,SAAK,WAAW,MAAM;AACpB,UAAI,kBAAkB,KAAK,MAAM,OAAO,GAAG;AACzC,YAAI,CAAC,WAAY,cAAa;AAAA,YACzB,QAAO;AAAA,MACd;AAAA,IACF;AAEA,QAAI,CAAC,WAAY,QAAO;AAExB,QAAI,WAAW,QAAQ,OAAO,MAAM,GAAI,YAAW,KAAK,OAAO;AAAA,QAC1D,QAAO;AAAA,EACd;AAEA,SAAO;AACT;AAEA,SAAS,kBAAkB,MAAM;AAC/B,SAAO,SAAS,OAAO,OAAO,CAAC;AACjC;AAEA,IAAI,OAAO,IAAI,KAAK,0BAA0B;AAAA,EAC5C,MAAM;AAAA,EACN,SAAS;AAAA,EACT,WAAW;AACb,CAAC;AAED,IAAI,cAAc,OAAO,UAAU;AAEnC,SAAS,iBAAiB,MAAM;AAC9B,MAAI,SAAS,KAAM,QAAO;AAE1B,MAAI,OAAO,QAAQ,MAAM,MAAM,QAC3B,SAAS;AAEb,WAAS,IAAI,MAAM,OAAO,MAAM;AAEhC,OAAK,QAAQ,GAAG,SAAS,OAAO,QAAQ,QAAQ,QAAQ,SAAS,GAAG;AAClE,WAAO,OAAO,KAAK;AAEnB,QAAI,YAAY,KAAK,IAAI,MAAM,kBAAmB,QAAO;AAEzD,WAAO,OAAO,KAAK,IAAI;AAEvB,QAAI,KAAK,WAAW,EAAG,QAAO;AAE9B,WAAO,KAAK,IAAI,CAAE,KAAK,CAAC,GAAG,KAAK,KAAK,CAAC,CAAC,CAAE;AAAA,EAC3C;AAEA,SAAO;AACT;AAEA,SAAS,mBAAmB,MAAM;AAChC,MAAI,SAAS,KAAM,QAAO,CAAC;AAE3B,MAAI,OAAO,QAAQ,MAAM,MAAM,QAC3B,SAAS;AAEb,WAAS,IAAI,MAAM,OAAO,MAAM;AAEhC,OAAK,QAAQ,GAAG,SAAS,OAAO,QAAQ,QAAQ,QAAQ,SAAS,GAAG;AAClE,WAAO,OAAO,KAAK;AAEnB,WAAO,OAAO,KAAK,IAAI;AAEvB,WAAO,KAAK,IAAI,CAAE,KAAK,CAAC,GAAG,KAAK,KAAK,CAAC,CAAC,CAAE;AAAA,EAC3C;AAEA,SAAO;AACT;AAEA,IAAI,QAAQ,IAAI,KAAK,2BAA2B;AAAA,EAC9C,MAAM;AAAA,EACN,SAAS;AAAA,EACT,WAAW;AACb,CAAC;AAED,IAAI,oBAAoB,OAAO,UAAU;AAEzC,SAAS,eAAe,MAAM;AAC5B,MAAI,SAAS,KAAM,QAAO;AAE1B,MAAI,KAAK,SAAS;AAElB,OAAK,OAAO,QAAQ;AAClB,QAAI,kBAAkB,KAAK,QAAQ,GAAG,GAAG;AACvC,UAAI,OAAO,GAAG,MAAM,KAAM,QAAO;AAAA,IACnC;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,iBAAiB,MAAM;AAC9B,SAAO,SAAS,OAAO,OAAO,CAAC;AACjC;AAEA,IAAI,MAAM,IAAI,KAAK,yBAAyB;AAAA,EAC1C,MAAM;AAAA,EACN,SAAS;AAAA,EACT,WAAW;AACb,CAAC;AAED,IAAI,WAAW,KAAK,OAAO;AAAA,EACzB,UAAU;AAAA,IACR;AAAA,IACA;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF,CAAC;AAUD,IAAI,oBAAoB,OAAO,UAAU;AAGzC,IAAI,kBAAoB;AACxB,IAAI,mBAAoB;AACxB,IAAI,mBAAoB;AACxB,IAAI,oBAAoB;AAGxB,IAAI,gBAAiB;AACrB,IAAI,iBAAiB;AACrB,IAAI,gBAAiB;AAGrB,IAAI,wBAAgC;AACpC,IAAI,gCAAgC;AACpC,IAAI,0BAAgC;AACpC,IAAI,qBAAgC;AACpC,IAAI,kBAAgC;AAGpC,SAAS,OAAO,KAAK;AAAE,SAAO,OAAO,UAAU,SAAS,KAAK,GAAG;AAAG;AAEnE,SAAS,OAAO,GAAG;AACjB,SAAQ,MAAM,MAAkB,MAAM;AACxC;AAEA,SAAS,eAAe,GAAG;AACzB,SAAQ,MAAM,KAAmB,MAAM;AACzC;AAEA,SAAS,aAAa,GAAG;AACvB,SAAQ,MAAM,KACN,MAAM,MACN,MAAM,MACN,MAAM;AAChB;AAEA,SAAS,kBAAkB,GAAG;AAC5B,SAAO,MAAM,MACN,MAAM,MACN,MAAM,MACN,MAAM,OACN,MAAM;AACf;AAEA,SAAS,YAAY,GAAG;AACtB,MAAI;AAEJ,MAAK,MAAe,KAAO,KAAK,IAAc;AAC5C,WAAO,IAAI;AAAA,EACb;AAGA,OAAK,IAAI;AAET,MAAK,MAAe,MAAQ,MAAM,KAAc;AAC9C,WAAO,KAAK,KAAO;AAAA,EACrB;AAEA,SAAO;AACT;AAEA,SAAS,cAAc,GAAG;AACxB,MAAI,MAAM,KAAa;AAAE,WAAO;AAAA,EAAG;AACnC,MAAI,MAAM,KAAa;AAAE,WAAO;AAAA,EAAG;AACnC,MAAI,MAAM,IAAa;AAAE,WAAO;AAAA,EAAG;AACnC,SAAO;AACT;AAEA,SAAS,gBAAgB,GAAG;AAC1B,MAAK,MAAe,KAAO,KAAK,IAAc;AAC5C,WAAO,IAAI;AAAA,EACb;AAEA,SAAO;AACT;AAEA,SAAS,qBAAqB,GAAG;AAE/B,SAAQ,MAAM,KAAe,OACtB,MAAM,KAAe,SACrB,MAAM,KAAe,OACrB,MAAM,MAAe,MACrB,MAAM,IAAiB,MACvB,MAAM,MAAe,OACrB,MAAM,MAAe,OACrB,MAAM,MAAe,OACrB,MAAM,MAAe,OACrB,MAAM,MAAe,SACrB,MAAM,KAAmB,MACzB,MAAM,KAAe,MACrB,MAAM,KAAe,MACrB,MAAM,KAAe,OACrB,MAAM,KAAe,MACrB,MAAM,KAAe,MACrB,MAAM,KAAe,WACrB,MAAM,KAAe,WAAW;AACzC;AAEA,SAAS,kBAAkB,GAAG;AAC5B,MAAI,KAAK,OAAQ;AACf,WAAO,OAAO,aAAa,CAAC;AAAA,EAC9B;AAGA,SAAO,OAAO;AAAA,KACV,IAAI,SAAa,MAAM;AAAA,KACvB,IAAI,QAAY,QAAU;AAAA,EAC9B;AACF;AAEA,IAAI,oBAAoB,IAAI,MAAM,GAAG;AACrC,IAAI,kBAAkB,IAAI,MAAM,GAAG;AACnC,KAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,oBAAkB,CAAC,IAAI,qBAAqB,CAAC,IAAI,IAAI;AACrD,kBAAgB,CAAC,IAAI,qBAAqB,CAAC;AAC7C;AAHS;AAMT,SAAS,QAAQ,OAAO,SAAS;AAC/B,OAAK,QAAQ;AAEb,OAAK,WAAY,QAAQ,UAAU,KAAM;AACzC,OAAK,SAAY,QAAQ,QAAQ,KAAQ;AACzC,OAAK,YAAY,QAAQ,WAAW,KAAK;AAGzC,OAAK,SAAY,QAAQ,QAAQ,KAAQ;AAEzC,OAAK,OAAY,QAAQ,MAAM,KAAU;AACzC,OAAK,WAAY,QAAQ,UAAU,KAAM;AAEzC,OAAK,gBAAgB,KAAK,OAAO;AACjC,OAAK,UAAgB,KAAK,OAAO;AAEjC,OAAK,SAAa,MAAM;AACxB,OAAK,WAAa;AAClB,OAAK,OAAa;AAClB,OAAK,YAAa;AAClB,OAAK,aAAa;AAIlB,OAAK,iBAAiB;AAEtB,OAAK,YAAY,CAAC;AAYpB;AAGA,SAAS,cAAc,OAAO,SAAS;AACrC,MAAI,OAAO;AAAA,IACT,MAAU,MAAM;AAAA,IAChB,QAAU,MAAM,MAAM,MAAM,GAAG,EAAE;AAAA;AAAA,IACjC,UAAU,MAAM;AAAA,IAChB,MAAU,MAAM;AAAA,IAChB,QAAU,MAAM,WAAW,MAAM;AAAA,EACnC;AAEA,OAAK,UAAU,QAAQ,IAAI;AAE3B,SAAO,IAAI,UAAU,SAAS,IAAI;AACpC;AAEA,SAAS,WAAW,OAAO,SAAS;AAClC,QAAM,cAAc,OAAO,OAAO;AACpC;AAEA,SAAS,aAAa,OAAO,SAAS;AACpC,MAAI,MAAM,WAAW;AACnB,UAAM,UAAU,KAAK,MAAM,cAAc,OAAO,OAAO,CAAC;AAAA,EAC1D;AACF;AAGA,IAAI,oBAAoB;AAAA,EAEtB,MAAM,SAAS,oBAAoB,OAAO,MAAM,MAAM;AAEpD,QAAI,OAAO,OAAO;AAElB,QAAI,MAAM,YAAY,MAAM;AAC1B,iBAAW,OAAO,gCAAgC;AAAA,IACpD;AAEA,QAAI,KAAK,WAAW,GAAG;AACrB,iBAAW,OAAO,6CAA6C;AAAA,IACjE;AAEA,YAAQ,uBAAuB,KAAK,KAAK,CAAC,CAAC;AAE3C,QAAI,UAAU,MAAM;AAClB,iBAAW,OAAO,2CAA2C;AAAA,IAC/D;AAEA,YAAQ,SAAS,MAAM,CAAC,GAAG,EAAE;AAC7B,YAAQ,SAAS,MAAM,CAAC,GAAG,EAAE;AAE7B,QAAI,UAAU,GAAG;AACf,iBAAW,OAAO,2CAA2C;AAAA,IAC/D;AAEA,UAAM,UAAU,KAAK,CAAC;AACtB,UAAM,kBAAmB,QAAQ;AAEjC,QAAI,UAAU,KAAK,UAAU,GAAG;AAC9B,mBAAa,OAAO,0CAA0C;AAAA,IAChE;AAAA,EACF;AAAA,EAEA,KAAK,SAAS,mBAAmB,OAAO,MAAM,MAAM;AAElD,QAAI,QAAQ;AAEZ,QAAI,KAAK,WAAW,GAAG;AACrB,iBAAW,OAAO,6CAA6C;AAAA,IACjE;AAEA,aAAS,KAAK,CAAC;AACf,aAAS,KAAK,CAAC;AAEf,QAAI,CAAC,mBAAmB,KAAK,MAAM,GAAG;AACpC,iBAAW,OAAO,6DAA6D;AAAA,IACjF;AAEA,QAAI,kBAAkB,KAAK,MAAM,QAAQ,MAAM,GAAG;AAChD,iBAAW,OAAO,gDAAgD,SAAS,cAAc;AAAA,IAC3F;AAEA,QAAI,CAAC,gBAAgB,KAAK,MAAM,GAAG;AACjC,iBAAW,OAAO,8DAA8D;AAAA,IAClF;AAEA,QAAI;AACF,eAAS,mBAAmB,MAAM;AAAA,IACpC,SAAS,KAAK;AACZ,iBAAW,OAAO,8BAA8B,MAAM;AAAA,IACxD;AAEA,UAAM,OAAO,MAAM,IAAI;AAAA,EACzB;AACF;AAGA,SAAS,eAAe,OAAO,OAAO,KAAK,WAAW;AACpD,MAAI,WAAW,SAAS,YAAY;AAEpC,MAAI,QAAQ,KAAK;AACf,cAAU,MAAM,MAAM,MAAM,OAAO,GAAG;AAEtC,QAAI,WAAW;AACb,WAAK,YAAY,GAAG,UAAU,QAAQ,QAAQ,YAAY,SAAS,aAAa,GAAG;AACjF,qBAAa,QAAQ,WAAW,SAAS;AACzC,YAAI,EAAE,eAAe,KACd,MAAQ,cAAc,cAAc,UAAY;AACrD,qBAAW,OAAO,+BAA+B;AAAA,QACnD;AAAA,MACF;AAAA,IACF,WAAW,sBAAsB,KAAK,OAAO,GAAG;AAC9C,iBAAW,OAAO,8CAA8C;AAAA,IAClE;AAEA,UAAM,UAAU;AAAA,EAClB;AACF;AAEA,SAAS,cAAc,OAAO,aAAa,QAAQ,iBAAiB;AAClE,MAAI,YAAY,KAAK,OAAO;AAE5B,MAAI,CAAC,OAAO,SAAS,MAAM,GAAG;AAC5B,eAAW,OAAO,mEAAmE;AAAA,EACvF;AAEA,eAAa,OAAO,KAAK,MAAM;AAE/B,OAAK,QAAQ,GAAG,WAAW,WAAW,QAAQ,QAAQ,UAAU,SAAS,GAAG;AAC1E,UAAM,WAAW,KAAK;AAEtB,QAAI,CAAC,kBAAkB,KAAK,aAAa,GAAG,GAAG;AAC7C,kBAAY,GAAG,IAAI,OAAO,GAAG;AAC7B,sBAAgB,GAAG,IAAI;AAAA,IACzB;AAAA,EACF;AACF;AAEA,SAAS,iBAAiB,OAAO,SAAS,iBAAiB,QAAQ,SAAS,WAC1E,WAAW,gBAAgB,UAAU;AAErC,MAAI,OAAO;AAKX,MAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,cAAU,MAAM,UAAU,MAAM,KAAK,OAAO;AAE5C,SAAK,QAAQ,GAAG,WAAW,QAAQ,QAAQ,QAAQ,UAAU,SAAS,GAAG;AACvE,UAAI,MAAM,QAAQ,QAAQ,KAAK,CAAC,GAAG;AACjC,mBAAW,OAAO,6CAA6C;AAAA,MACjE;AAEA,UAAI,OAAO,YAAY,YAAY,OAAO,QAAQ,KAAK,CAAC,MAAM,mBAAmB;AAC/E,gBAAQ,KAAK,IAAI;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AAKA,MAAI,OAAO,YAAY,YAAY,OAAO,OAAO,MAAM,mBAAmB;AACxE,cAAU;AAAA,EACZ;AAGA,YAAU,OAAO,OAAO;AAExB,MAAI,YAAY,MAAM;AACpB,cAAU,CAAC;AAAA,EACb;AAEA,MAAI,WAAW,2BAA2B;AACxC,QAAI,MAAM,QAAQ,SAAS,GAAG;AAC5B,WAAK,QAAQ,GAAG,WAAW,UAAU,QAAQ,QAAQ,UAAU,SAAS,GAAG;AACzE,sBAAc,OAAO,SAAS,UAAU,KAAK,GAAG,eAAe;AAAA,MACjE;AAAA,IACF,OAAO;AACL,oBAAc,OAAO,SAAS,WAAW,eAAe;AAAA,IAC1D;AAAA,EACF,OAAO;AACL,QAAI,CAAC,MAAM,QACP,CAAC,kBAAkB,KAAK,iBAAiB,OAAO,KAChD,kBAAkB,KAAK,SAAS,OAAO,GAAG;AAC5C,YAAM,OAAO,aAAa,MAAM;AAChC,YAAM,YAAY,kBAAkB,MAAM;AAC1C,YAAM,WAAW,YAAY,MAAM;AACnC,iBAAW,OAAO,wBAAwB;AAAA,IAC5C;AAGA,QAAI,YAAY,aAAa;AAC3B,aAAO,eAAe,SAAS,SAAS;AAAA,QACtC,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,OAAO;AAAA,MACT,CAAC;AAAA,IACH,OAAO;AACL,cAAQ,OAAO,IAAI;AAAA,IACrB;AACA,WAAO,gBAAgB,OAAO;AAAA,EAChC;AAEA,SAAO;AACT;AAEA,SAAS,cAAc,OAAO;AAC5B,MAAI;AAEJ,OAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAE1C,MAAI,OAAO,IAAc;AACvB,UAAM;AAAA,EACR,WAAW,OAAO,IAAc;AAC9B,UAAM;AACN,QAAI,MAAM,MAAM,WAAW,MAAM,QAAQ,MAAM,IAAc;AAC3D,YAAM;AAAA,IACR;AAAA,EACF,OAAO;AACL,eAAW,OAAO,0BAA0B;AAAA,EAC9C;AAEA,QAAM,QAAQ;AACd,QAAM,YAAY,MAAM;AACxB,QAAM,iBAAiB;AACzB;AAEA,SAAS,oBAAoB,OAAO,eAAe,aAAa;AAC9D,MAAI,aAAa,GACb,KAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAE9C,SAAO,OAAO,GAAG;AACf,WAAO,eAAe,EAAE,GAAG;AACzB,UAAI,OAAO,KAAiB,MAAM,mBAAmB,IAAI;AACvD,cAAM,iBAAiB,MAAM;AAAA,MAC/B;AACA,WAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,IAC9C;AAEA,QAAI,iBAAiB,OAAO,IAAa;AACvC,SAAG;AACD,aAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,MAC9C,SAAS,OAAO,MAAgB,OAAO,MAAgB,OAAO;AAAA,IAChE;AAEA,QAAI,OAAO,EAAE,GAAG;AACd,oBAAc,KAAK;AAEnB,WAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAC1C;AACA,YAAM,aAAa;AAEnB,aAAO,OAAO,IAAiB;AAC7B,cAAM;AACN,aAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,MAC9C;AAAA,IACF,OAAO;AACL;AAAA,IACF;AAAA,EACF;AAEA,MAAI,gBAAgB,MAAM,eAAe,KAAK,MAAM,aAAa,aAAa;AAC5E,iBAAa,OAAO,uBAAuB;AAAA,EAC7C;AAEA,SAAO;AACT;AAEA,SAAS,sBAAsB,OAAO;AACpC,MAAI,YAAY,MAAM,UAClB;AAEJ,OAAK,MAAM,MAAM,WAAW,SAAS;AAIrC,OAAK,OAAO,MAAe,OAAO,OAC9B,OAAO,MAAM,MAAM,WAAW,YAAY,CAAC,KAC3C,OAAO,MAAM,MAAM,WAAW,YAAY,CAAC,GAAG;AAEhD,iBAAa;AAEb,SAAK,MAAM,MAAM,WAAW,SAAS;AAErC,QAAI,OAAO,KAAK,aAAa,EAAE,GAAG;AAChC,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,iBAAiB,OAAO,OAAO;AACtC,MAAI,UAAU,GAAG;AACf,UAAM,UAAU;AAAA,EAClB,WAAW,QAAQ,GAAG;AACpB,UAAM,UAAU,OAAO,OAAO,MAAM,QAAQ,CAAC;AAAA,EAC/C;AACF;AAGA,SAAS,gBAAgB,OAAO,YAAY,sBAAsB;AAChE,MAAI,WACA,WACA,cACA,YACA,mBACA,OACA,YACA,aACA,QAAQ,MAAM,MACd,UAAU,MAAM,QAChB;AAEJ,OAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAE1C,MAAI,aAAa,EAAE,KACf,kBAAkB,EAAE,KACpB,OAAO,MACP,OAAO,MACP,OAAO,MACP,OAAO,MACP,OAAO,OACP,OAAO,MACP,OAAO,MACP,OAAO,MACP,OAAO,MACP,OAAO,MACP,OAAO,IAAa;AACtB,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,MAAe,OAAO,IAAa;AAC5C,gBAAY,MAAM,MAAM,WAAW,MAAM,WAAW,CAAC;AAErD,QAAI,aAAa,SAAS,KACtB,wBAAwB,kBAAkB,SAAS,GAAG;AACxD,aAAO;AAAA,IACT;AAAA,EACF;AAEA,QAAM,OAAO;AACb,QAAM,SAAS;AACf,iBAAe,aAAa,MAAM;AAClC,sBAAoB;AAEpB,SAAO,OAAO,GAAG;AACf,QAAI,OAAO,IAAa;AACtB,kBAAY,MAAM,MAAM,WAAW,MAAM,WAAW,CAAC;AAErD,UAAI,aAAa,SAAS,KACtB,wBAAwB,kBAAkB,SAAS,GAAG;AACxD;AAAA,MACF;AAAA,IAEF,WAAW,OAAO,IAAa;AAC7B,kBAAY,MAAM,MAAM,WAAW,MAAM,WAAW,CAAC;AAErD,UAAI,aAAa,SAAS,GAAG;AAC3B;AAAA,MACF;AAAA,IAEF,WAAY,MAAM,aAAa,MAAM,aAAa,sBAAsB,KAAK,KAClE,wBAAwB,kBAAkB,EAAE,GAAG;AACxD;AAAA,IAEF,WAAW,OAAO,EAAE,GAAG;AACrB,cAAQ,MAAM;AACd,mBAAa,MAAM;AACnB,oBAAc,MAAM;AACpB,0BAAoB,OAAO,OAAO,EAAE;AAEpC,UAAI,MAAM,cAAc,YAAY;AAClC,4BAAoB;AACpB,aAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAC1C;AAAA,MACF,OAAO;AACL,cAAM,WAAW;AACjB,cAAM,OAAO;AACb,cAAM,YAAY;AAClB,cAAM,aAAa;AACnB;AAAA,MACF;AAAA,IACF;AAEA,QAAI,mBAAmB;AACrB,qBAAe,OAAO,cAAc,YAAY,KAAK;AACrD,uBAAiB,OAAO,MAAM,OAAO,KAAK;AAC1C,qBAAe,aAAa,MAAM;AAClC,0BAAoB;AAAA,IACtB;AAEA,QAAI,CAAC,eAAe,EAAE,GAAG;AACvB,mBAAa,MAAM,WAAW;AAAA,IAChC;AAEA,SAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,EAC9C;AAEA,iBAAe,OAAO,cAAc,YAAY,KAAK;AAErD,MAAI,MAAM,QAAQ;AAChB,WAAO;AAAA,EACT;AAEA,QAAM,OAAO;AACb,QAAM,SAAS;AACf,SAAO;AACT;AAEA,SAAS,uBAAuB,OAAO,YAAY;AACjD,MAAI,IACA,cAAc;AAElB,OAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAE1C,MAAI,OAAO,IAAa;AACtB,WAAO;AAAA,EACT;AAEA,QAAM,OAAO;AACb,QAAM,SAAS;AACf,QAAM;AACN,iBAAe,aAAa,MAAM;AAElC,UAAQ,KAAK,MAAM,MAAM,WAAW,MAAM,QAAQ,OAAO,GAAG;AAC1D,QAAI,OAAO,IAAa;AACtB,qBAAe,OAAO,cAAc,MAAM,UAAU,IAAI;AACxD,WAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAE5C,UAAI,OAAO,IAAa;AACtB,uBAAe,MAAM;AACrB,cAAM;AACN,qBAAa,MAAM;AAAA,MACrB,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IAEF,WAAW,OAAO,EAAE,GAAG;AACrB,qBAAe,OAAO,cAAc,YAAY,IAAI;AACpD,uBAAiB,OAAO,oBAAoB,OAAO,OAAO,UAAU,CAAC;AACrE,qBAAe,aAAa,MAAM;AAAA,IAEpC,WAAW,MAAM,aAAa,MAAM,aAAa,sBAAsB,KAAK,GAAG;AAC7E,iBAAW,OAAO,8DAA8D;AAAA,IAElF,OAAO;AACL,YAAM;AACN,mBAAa,MAAM;AAAA,IACrB;AAAA,EACF;AAEA,aAAW,OAAO,4DAA4D;AAChF;AAEA,SAAS,uBAAuB,OAAO,YAAY;AACjD,MAAI,cACA,YACA,WACA,WACA,KACA;AAEJ,OAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAE1C,MAAI,OAAO,IAAa;AACtB,WAAO;AAAA,EACT;AAEA,QAAM,OAAO;AACb,QAAM,SAAS;AACf,QAAM;AACN,iBAAe,aAAa,MAAM;AAElC,UAAQ,KAAK,MAAM,MAAM,WAAW,MAAM,QAAQ,OAAO,GAAG;AAC1D,QAAI,OAAO,IAAa;AACtB,qBAAe,OAAO,cAAc,MAAM,UAAU,IAAI;AACxD,YAAM;AACN,aAAO;AAAA,IAET,WAAW,OAAO,IAAa;AAC7B,qBAAe,OAAO,cAAc,MAAM,UAAU,IAAI;AACxD,WAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAE5C,UAAI,OAAO,EAAE,GAAG;AACd,4BAAoB,OAAO,OAAO,UAAU;AAAA,MAG9C,WAAW,KAAK,OAAO,kBAAkB,EAAE,GAAG;AAC5C,cAAM,UAAU,gBAAgB,EAAE;AAClC,cAAM;AAAA,MAER,YAAY,MAAM,cAAc,EAAE,KAAK,GAAG;AACxC,oBAAY;AACZ,oBAAY;AAEZ,eAAO,YAAY,GAAG,aAAa;AACjC,eAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAE5C,eAAK,MAAM,YAAY,EAAE,MAAM,GAAG;AAChC,yBAAa,aAAa,KAAK;AAAA,UAEjC,OAAO;AACL,uBAAW,OAAO,gCAAgC;AAAA,UACpD;AAAA,QACF;AAEA,cAAM,UAAU,kBAAkB,SAAS;AAE3C,cAAM;AAAA,MAER,OAAO;AACL,mBAAW,OAAO,yBAAyB;AAAA,MAC7C;AAEA,qBAAe,aAAa,MAAM;AAAA,IAEpC,WAAW,OAAO,EAAE,GAAG;AACrB,qBAAe,OAAO,cAAc,YAAY,IAAI;AACpD,uBAAiB,OAAO,oBAAoB,OAAO,OAAO,UAAU,CAAC;AACrE,qBAAe,aAAa,MAAM;AAAA,IAEpC,WAAW,MAAM,aAAa,MAAM,aAAa,sBAAsB,KAAK,GAAG;AAC7E,iBAAW,OAAO,8DAA8D;AAAA,IAElF,OAAO;AACL,YAAM;AACN,mBAAa,MAAM;AAAA,IACrB;AAAA,EACF;AAEA,aAAW,OAAO,4DAA4D;AAChF;AAEA,SAAS,mBAAmB,OAAO,YAAY;AAC7C,MAAI,WAAW,MACX,OACA,YACA,MACA,OAAW,MAAM,KACjB,SACA,UAAW,MAAM,QACjB,WACA,YACA,QACA,gBACA,WACA,kBAAkB,uBAAO,OAAO,IAAI,GACpC,SACA,QACA,WACA;AAEJ,OAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAE1C,MAAI,OAAO,IAAa;AACtB,iBAAa;AACb,gBAAY;AACZ,cAAU,CAAC;AAAA,EACb,WAAW,OAAO,KAAa;AAC7B,iBAAa;AACb,gBAAY;AACZ,cAAU,CAAC;AAAA,EACb,OAAO;AACL,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,WAAW,MAAM;AACzB,UAAM,UAAU,MAAM,MAAM,IAAI;AAAA,EAClC;AAEA,OAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAE5C,SAAO,OAAO,GAAG;AACf,wBAAoB,OAAO,MAAM,UAAU;AAE3C,SAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAE1C,QAAI,OAAO,YAAY;AACrB,YAAM;AACN,YAAM,MAAM;AACZ,YAAM,SAAS;AACf,YAAM,OAAO,YAAY,YAAY;AACrC,YAAM,SAAS;AACf,aAAO;AAAA,IACT,WAAW,CAAC,UAAU;AACpB,iBAAW,OAAO,8CAA8C;AAAA,IAClE,WAAW,OAAO,IAAa;AAE7B,iBAAW,OAAO,0CAA0C;AAAA,IAC9D;AAEA,aAAS,UAAU,YAAY;AAC/B,aAAS,iBAAiB;AAE1B,QAAI,OAAO,IAAa;AACtB,kBAAY,MAAM,MAAM,WAAW,MAAM,WAAW,CAAC;AAErD,UAAI,aAAa,SAAS,GAAG;AAC3B,iBAAS,iBAAiB;AAC1B,cAAM;AACN,4BAAoB,OAAO,MAAM,UAAU;AAAA,MAC7C;AAAA,IACF;AAEA,YAAQ,MAAM;AACd,iBAAa,MAAM;AACnB,WAAO,MAAM;AACb,gBAAY,OAAO,YAAY,iBAAiB,OAAO,IAAI;AAC3D,aAAS,MAAM;AACf,cAAU,MAAM;AAChB,wBAAoB,OAAO,MAAM,UAAU;AAE3C,SAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAE1C,SAAK,kBAAkB,MAAM,SAAS,UAAU,OAAO,IAAa;AAClE,eAAS;AACT,WAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAC5C,0BAAoB,OAAO,MAAM,UAAU;AAC3C,kBAAY,OAAO,YAAY,iBAAiB,OAAO,IAAI;AAC3D,kBAAY,MAAM;AAAA,IACpB;AAEA,QAAI,WAAW;AACb,uBAAiB,OAAO,SAAS,iBAAiB,QAAQ,SAAS,WAAW,OAAO,YAAY,IAAI;AAAA,IACvG,WAAW,QAAQ;AACjB,cAAQ,KAAK,iBAAiB,OAAO,MAAM,iBAAiB,QAAQ,SAAS,WAAW,OAAO,YAAY,IAAI,CAAC;AAAA,IAClH,OAAO;AACL,cAAQ,KAAK,OAAO;AAAA,IACtB;AAEA,wBAAoB,OAAO,MAAM,UAAU;AAE3C,SAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAE1C,QAAI,OAAO,IAAa;AACtB,iBAAW;AACX,WAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,IAC9C,OAAO;AACL,iBAAW;AAAA,IACb;AAAA,EACF;AAEA,aAAW,OAAO,uDAAuD;AAC3E;AAEA,SAAS,gBAAgB,OAAO,YAAY;AAC1C,MAAI,cACA,SACA,WAAiB,eACjB,iBAAiB,OACjB,iBAAiB,OACjB,aAAiB,YACjB,aAAiB,GACjB,iBAAiB,OACjB,KACA;AAEJ,OAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAE1C,MAAI,OAAO,KAAa;AACtB,cAAU;AAAA,EACZ,WAAW,OAAO,IAAa;AAC7B,cAAU;AAAA,EACZ,OAAO;AACL,WAAO;AAAA,EACT;AAEA,QAAM,OAAO;AACb,QAAM,SAAS;AAEf,SAAO,OAAO,GAAG;AACf,SAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAE5C,QAAI,OAAO,MAAe,OAAO,IAAa;AAC5C,UAAI,kBAAkB,UAAU;AAC9B,mBAAY,OAAO,KAAe,gBAAgB;AAAA,MACpD,OAAO;AACL,mBAAW,OAAO,sCAAsC;AAAA,MAC1D;AAAA,IAEF,YAAY,MAAM,gBAAgB,EAAE,MAAM,GAAG;AAC3C,UAAI,QAAQ,GAAG;AACb,mBAAW,OAAO,8EAA8E;AAAA,MAClG,WAAW,CAAC,gBAAgB;AAC1B,qBAAa,aAAa,MAAM;AAChC,yBAAiB;AAAA,MACnB,OAAO;AACL,mBAAW,OAAO,2CAA2C;AAAA,MAC/D;AAAA,IAEF,OAAO;AACL;AAAA,IACF;AAAA,EACF;AAEA,MAAI,eAAe,EAAE,GAAG;AACtB,OAAG;AAAE,WAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,IAAG,SAC7C,eAAe,EAAE;AAExB,QAAI,OAAO,IAAa;AACtB,SAAG;AAAE,aAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,MAAG,SAC7C,CAAC,OAAO,EAAE,KAAM,OAAO;AAAA,IAChC;AAAA,EACF;AAEA,SAAO,OAAO,GAAG;AACf,kBAAc,KAAK;AACnB,UAAM,aAAa;AAEnB,SAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAE1C,YAAQ,CAAC,kBAAkB,MAAM,aAAa,eACtC,OAAO,IAAkB;AAC/B,YAAM;AACN,WAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,IAC9C;AAEA,QAAI,CAAC,kBAAkB,MAAM,aAAa,YAAY;AACpD,mBAAa,MAAM;AAAA,IACrB;AAEA,QAAI,OAAO,EAAE,GAAG;AACd;AACA;AAAA,IACF;AAGA,QAAI,MAAM,aAAa,YAAY;AAGjC,UAAI,aAAa,eAAe;AAC9B,cAAM,UAAU,OAAO,OAAO,MAAM,iBAAiB,IAAI,aAAa,UAAU;AAAA,MAClF,WAAW,aAAa,eAAe;AACrC,YAAI,gBAAgB;AAClB,gBAAM,UAAU;AAAA,QAClB;AAAA,MACF;AAGA;AAAA,IACF;AAGA,QAAI,SAAS;AAGX,UAAI,eAAe,EAAE,GAAG;AACtB,yBAAiB;AAEjB,cAAM,UAAU,OAAO,OAAO,MAAM,iBAAiB,IAAI,aAAa,UAAU;AAAA,MAGlF,WAAW,gBAAgB;AACzB,yBAAiB;AACjB,cAAM,UAAU,OAAO,OAAO,MAAM,aAAa,CAAC;AAAA,MAGpD,WAAW,eAAe,GAAG;AAC3B,YAAI,gBAAgB;AAClB,gBAAM,UAAU;AAAA,QAClB;AAAA,MAGF,OAAO;AACL,cAAM,UAAU,OAAO,OAAO,MAAM,UAAU;AAAA,MAChD;AAAA,IAGF,OAAO;AAEL,YAAM,UAAU,OAAO,OAAO,MAAM,iBAAiB,IAAI,aAAa,UAAU;AAAA,IAClF;AAEA,qBAAiB;AACjB,qBAAiB;AACjB,iBAAa;AACb,mBAAe,MAAM;AAErB,WAAO,CAAC,OAAO,EAAE,KAAM,OAAO,GAAI;AAChC,WAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,IAC9C;AAEA,mBAAe,OAAO,cAAc,MAAM,UAAU,KAAK;AAAA,EAC3D;AAEA,SAAO;AACT;AAEA,SAAS,kBAAkB,OAAO,YAAY;AAC5C,MAAI,OACA,OAAY,MAAM,KAClB,UAAY,MAAM,QAClB,UAAY,CAAC,GACb,WACA,WAAY,OACZ;AAIJ,MAAI,MAAM,mBAAmB,GAAI,QAAO;AAExC,MAAI,MAAM,WAAW,MAAM;AACzB,UAAM,UAAU,MAAM,MAAM,IAAI;AAAA,EAClC;AAEA,OAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAE1C,SAAO,OAAO,GAAG;AACf,QAAI,MAAM,mBAAmB,IAAI;AAC/B,YAAM,WAAW,MAAM;AACvB,iBAAW,OAAO,gDAAgD;AAAA,IACpE;AAEA,QAAI,OAAO,IAAa;AACtB;AAAA,IACF;AAEA,gBAAY,MAAM,MAAM,WAAW,MAAM,WAAW,CAAC;AAErD,QAAI,CAAC,aAAa,SAAS,GAAG;AAC5B;AAAA,IACF;AAEA,eAAW;AACX,UAAM;AAEN,QAAI,oBAAoB,OAAO,MAAM,EAAE,GAAG;AACxC,UAAI,MAAM,cAAc,YAAY;AAClC,gBAAQ,KAAK,IAAI;AACjB,aAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAC1C;AAAA,MACF;AAAA,IACF;AAEA,YAAQ,MAAM;AACd,gBAAY,OAAO,YAAY,kBAAkB,OAAO,IAAI;AAC5D,YAAQ,KAAK,MAAM,MAAM;AACzB,wBAAoB,OAAO,MAAM,EAAE;AAEnC,SAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAE1C,SAAK,MAAM,SAAS,SAAS,MAAM,aAAa,eAAgB,OAAO,GAAI;AACzE,iBAAW,OAAO,qCAAqC;AAAA,IACzD,WAAW,MAAM,aAAa,YAAY;AACxC;AAAA,IACF;AAAA,EACF;AAEA,MAAI,UAAU;AACZ,UAAM,MAAM;AACZ,UAAM,SAAS;AACf,UAAM,OAAO;AACb,UAAM,SAAS;AACf,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,SAAS,iBAAiB,OAAO,YAAY,YAAY;AACvD,MAAI,WACA,cACA,OACA,UACA,eACA,SACA,OAAgB,MAAM,KACtB,UAAgB,MAAM,QACtB,UAAgB,CAAC,GACjB,kBAAkB,uBAAO,OAAO,IAAI,GACpC,SAAgB,MAChB,UAAgB,MAChB,YAAgB,MAChB,gBAAgB,OAChB,WAAgB,OAChB;AAIJ,MAAI,MAAM,mBAAmB,GAAI,QAAO;AAExC,MAAI,MAAM,WAAW,MAAM;AACzB,UAAM,UAAU,MAAM,MAAM,IAAI;AAAA,EAClC;AAEA,OAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAE1C,SAAO,OAAO,GAAG;AACf,QAAI,CAAC,iBAAiB,MAAM,mBAAmB,IAAI;AACjD,YAAM,WAAW,MAAM;AACvB,iBAAW,OAAO,gDAAgD;AAAA,IACpE;AAEA,gBAAY,MAAM,MAAM,WAAW,MAAM,WAAW,CAAC;AACrD,YAAQ,MAAM;AAMd,SAAK,OAAO,MAAe,OAAO,OAAgB,aAAa,SAAS,GAAG;AAEzE,UAAI,OAAO,IAAa;AACtB,YAAI,eAAe;AACjB,2BAAiB,OAAO,SAAS,iBAAiB,QAAQ,SAAS,MAAM,UAAU,eAAe,OAAO;AACzG,mBAAS,UAAU,YAAY;AAAA,QACjC;AAEA,mBAAW;AACX,wBAAgB;AAChB,uBAAe;AAAA,MAEjB,WAAW,eAAe;AAExB,wBAAgB;AAChB,uBAAe;AAAA,MAEjB,OAAO;AACL,mBAAW,OAAO,mGAAmG;AAAA,MACvH;AAEA,YAAM,YAAY;AAClB,WAAK;AAAA,IAKP,OAAO;AACL,iBAAW,MAAM;AACjB,sBAAgB,MAAM;AACtB,gBAAU,MAAM;AAEhB,UAAI,CAAC,YAAY,OAAO,YAAY,kBAAkB,OAAO,IAAI,GAAG;AAGlE;AAAA,MACF;AAEA,UAAI,MAAM,SAAS,OAAO;AACxB,aAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAE1C,eAAO,eAAe,EAAE,GAAG;AACzB,eAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,QAC9C;AAEA,YAAI,OAAO,IAAa;AACtB,eAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAE5C,cAAI,CAAC,aAAa,EAAE,GAAG;AACrB,uBAAW,OAAO,yFAAyF;AAAA,UAC7G;AAEA,cAAI,eAAe;AACjB,6BAAiB,OAAO,SAAS,iBAAiB,QAAQ,SAAS,MAAM,UAAU,eAAe,OAAO;AACzG,qBAAS,UAAU,YAAY;AAAA,UACjC;AAEA,qBAAW;AACX,0BAAgB;AAChB,yBAAe;AACf,mBAAS,MAAM;AACf,oBAAU,MAAM;AAAA,QAElB,WAAW,UAAU;AACnB,qBAAW,OAAO,0DAA0D;AAAA,QAE9E,OAAO;AACL,gBAAM,MAAM;AACZ,gBAAM,SAAS;AACf,iBAAO;AAAA,QACT;AAAA,MAEF,WAAW,UAAU;AACnB,mBAAW,OAAO,gFAAgF;AAAA,MAEpG,OAAO;AACL,cAAM,MAAM;AACZ,cAAM,SAAS;AACf,eAAO;AAAA,MACT;AAAA,IACF;AAKA,QAAI,MAAM,SAAS,SAAS,MAAM,aAAa,YAAY;AACzD,UAAI,eAAe;AACjB,mBAAW,MAAM;AACjB,wBAAgB,MAAM;AACtB,kBAAU,MAAM;AAAA,MAClB;AAEA,UAAI,YAAY,OAAO,YAAY,mBAAmB,MAAM,YAAY,GAAG;AACzE,YAAI,eAAe;AACjB,oBAAU,MAAM;AAAA,QAClB,OAAO;AACL,sBAAY,MAAM;AAAA,QACpB;AAAA,MACF;AAEA,UAAI,CAAC,eAAe;AAClB,yBAAiB,OAAO,SAAS,iBAAiB,QAAQ,SAAS,WAAW,UAAU,eAAe,OAAO;AAC9G,iBAAS,UAAU,YAAY;AAAA,MACjC;AAEA,0BAAoB,OAAO,MAAM,EAAE;AACnC,WAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAAA,IAC5C;AAEA,SAAK,MAAM,SAAS,SAAS,MAAM,aAAa,eAAgB,OAAO,GAAI;AACzE,iBAAW,OAAO,oCAAoC;AAAA,IACxD,WAAW,MAAM,aAAa,YAAY;AACxC;AAAA,IACF;AAAA,EACF;AAOA,MAAI,eAAe;AACjB,qBAAiB,OAAO,SAAS,iBAAiB,QAAQ,SAAS,MAAM,UAAU,eAAe,OAAO;AAAA,EAC3G;AAGA,MAAI,UAAU;AACZ,UAAM,MAAM;AACZ,UAAM,SAAS;AACf,UAAM,OAAO;AACb,UAAM,SAAS;AAAA,EACjB;AAEA,SAAO;AACT;AAEA,SAAS,gBAAgB,OAAO;AAC9B,MAAI,WACA,aAAa,OACb,UAAa,OACb,WACA,SACA;AAEJ,OAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAE1C,MAAI,OAAO,GAAa,QAAO;AAE/B,MAAI,MAAM,QAAQ,MAAM;AACtB,eAAW,OAAO,+BAA+B;AAAA,EACnD;AAEA,OAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAE5C,MAAI,OAAO,IAAa;AACtB,iBAAa;AACb,SAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,EAE9C,WAAW,OAAO,IAAa;AAC7B,cAAU;AACV,gBAAY;AACZ,SAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,EAE9C,OAAO;AACL,gBAAY;AAAA,EACd;AAEA,cAAY,MAAM;AAElB,MAAI,YAAY;AACd,OAAG;AAAE,WAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,IAAG,SAC7C,OAAO,KAAK,OAAO;AAE1B,QAAI,MAAM,WAAW,MAAM,QAAQ;AACjC,gBAAU,MAAM,MAAM,MAAM,WAAW,MAAM,QAAQ;AACrD,WAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,IAC9C,OAAO;AACL,iBAAW,OAAO,oDAAoD;AAAA,IACxE;AAAA,EACF,OAAO;AACL,WAAO,OAAO,KAAK,CAAC,aAAa,EAAE,GAAG;AAEpC,UAAI,OAAO,IAAa;AACtB,YAAI,CAAC,SAAS;AACZ,sBAAY,MAAM,MAAM,MAAM,YAAY,GAAG,MAAM,WAAW,CAAC;AAE/D,cAAI,CAAC,mBAAmB,KAAK,SAAS,GAAG;AACvC,uBAAW,OAAO,iDAAiD;AAAA,UACrE;AAEA,oBAAU;AACV,sBAAY,MAAM,WAAW;AAAA,QAC/B,OAAO;AACL,qBAAW,OAAO,6CAA6C;AAAA,QACjE;AAAA,MACF;AAEA,WAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,IAC9C;AAEA,cAAU,MAAM,MAAM,MAAM,WAAW,MAAM,QAAQ;AAErD,QAAI,wBAAwB,KAAK,OAAO,GAAG;AACzC,iBAAW,OAAO,qDAAqD;AAAA,IACzE;AAAA,EACF;AAEA,MAAI,WAAW,CAAC,gBAAgB,KAAK,OAAO,GAAG;AAC7C,eAAW,OAAO,8CAA8C,OAAO;AAAA,EACzE;AAEA,MAAI;AACF,cAAU,mBAAmB,OAAO;AAAA,EACtC,SAAS,KAAK;AACZ,eAAW,OAAO,4BAA4B,OAAO;AAAA,EACvD;AAEA,MAAI,YAAY;AACd,UAAM,MAAM;AAAA,EAEd,WAAW,kBAAkB,KAAK,MAAM,QAAQ,SAAS,GAAG;AAC1D,UAAM,MAAM,MAAM,OAAO,SAAS,IAAI;AAAA,EAExC,WAAW,cAAc,KAAK;AAC5B,UAAM,MAAM,MAAM;AAAA,EAEpB,WAAW,cAAc,MAAM;AAC7B,UAAM,MAAM,uBAAuB;AAAA,EAErC,OAAO;AACL,eAAW,OAAO,4BAA4B,YAAY,GAAG;AAAA,EAC/D;AAEA,SAAO;AACT;AAEA,SAAS,mBAAmB,OAAO;AACjC,MAAI,WACA;AAEJ,OAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAE1C,MAAI,OAAO,GAAa,QAAO;AAE/B,MAAI,MAAM,WAAW,MAAM;AACzB,eAAW,OAAO,mCAAmC;AAAA,EACvD;AAEA,OAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAC5C,cAAY,MAAM;AAElB,SAAO,OAAO,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,kBAAkB,EAAE,GAAG;AAC9D,SAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,EAC9C;AAEA,MAAI,MAAM,aAAa,WAAW;AAChC,eAAW,OAAO,4DAA4D;AAAA,EAChF;AAEA,QAAM,SAAS,MAAM,MAAM,MAAM,WAAW,MAAM,QAAQ;AAC1D,SAAO;AACT;AAEA,SAAS,UAAU,OAAO;AACxB,MAAI,WAAW,OACX;AAEJ,OAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAE1C,MAAI,OAAO,GAAa,QAAO;AAE/B,OAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAC5C,cAAY,MAAM;AAElB,SAAO,OAAO,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,kBAAkB,EAAE,GAAG;AAC9D,SAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,EAC9C;AAEA,MAAI,MAAM,aAAa,WAAW;AAChC,eAAW,OAAO,2DAA2D;AAAA,EAC/E;AAEA,UAAQ,MAAM,MAAM,MAAM,WAAW,MAAM,QAAQ;AAEnD,MAAI,CAAC,kBAAkB,KAAK,MAAM,WAAW,KAAK,GAAG;AACnD,eAAW,OAAO,yBAAyB,QAAQ,GAAG;AAAA,EACxD;AAEA,QAAM,SAAS,MAAM,UAAU,KAAK;AACpC,sBAAoB,OAAO,MAAM,EAAE;AACnC,SAAO;AACT;AAEA,SAAS,YAAY,OAAO,cAAc,aAAa,aAAa,cAAc;AAChF,MAAI,kBACA,mBACA,uBACA,eAAe,GACf,YAAa,OACb,aAAa,OACb,WACA,cACA,UACAE,OACA,YACA;AAEJ,MAAI,MAAM,aAAa,MAAM;AAC3B,UAAM,SAAS,QAAQ,KAAK;AAAA,EAC9B;AAEA,QAAM,MAAS;AACf,QAAM,SAAS;AACf,QAAM,OAAS;AACf,QAAM,SAAS;AAEf,qBAAmB,oBAAoB,wBACrC,sBAAsB,eACtB,qBAAsB;AAExB,MAAI,aAAa;AACf,QAAI,oBAAoB,OAAO,MAAM,EAAE,GAAG;AACxC,kBAAY;AAEZ,UAAI,MAAM,aAAa,cAAc;AACnC,uBAAe;AAAA,MACjB,WAAW,MAAM,eAAe,cAAc;AAC5C,uBAAe;AAAA,MACjB,WAAW,MAAM,aAAa,cAAc;AAC1C,uBAAe;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AAEA,MAAI,iBAAiB,GAAG;AACtB,WAAO,gBAAgB,KAAK,KAAK,mBAAmB,KAAK,GAAG;AAC1D,UAAI,oBAAoB,OAAO,MAAM,EAAE,GAAG;AACxC,oBAAY;AACZ,gCAAwB;AAExB,YAAI,MAAM,aAAa,cAAc;AACnC,yBAAe;AAAA,QACjB,WAAW,MAAM,eAAe,cAAc;AAC5C,yBAAe;AAAA,QACjB,WAAW,MAAM,aAAa,cAAc;AAC1C,yBAAe;AAAA,QACjB;AAAA,MACF,OAAO;AACL,gCAAwB;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AAEA,MAAI,uBAAuB;AACzB,4BAAwB,aAAa;AAAA,EACvC;AAEA,MAAI,iBAAiB,KAAK,sBAAsB,aAAa;AAC3D,QAAI,oBAAoB,eAAe,qBAAqB,aAAa;AACvE,mBAAa;AAAA,IACf,OAAO;AACL,mBAAa,eAAe;AAAA,IAC9B;AAEA,kBAAc,MAAM,WAAW,MAAM;AAErC,QAAI,iBAAiB,GAAG;AACtB,UAAI,0BACC,kBAAkB,OAAO,WAAW,KACpC,iBAAiB,OAAO,aAAa,UAAU,MAChD,mBAAmB,OAAO,UAAU,GAAG;AACzC,qBAAa;AAAA,MACf,OAAO;AACL,YAAK,qBAAqB,gBAAgB,OAAO,UAAU,KACvD,uBAAuB,OAAO,UAAU,KACxC,uBAAuB,OAAO,UAAU,GAAG;AAC7C,uBAAa;AAAA,QAEf,WAAW,UAAU,KAAK,GAAG;AAC3B,uBAAa;AAEb,cAAI,MAAM,QAAQ,QAAQ,MAAM,WAAW,MAAM;AAC/C,uBAAW,OAAO,2CAA2C;AAAA,UAC/D;AAAA,QAEF,WAAW,gBAAgB,OAAO,YAAY,oBAAoB,WAAW,GAAG;AAC9E,uBAAa;AAEb,cAAI,MAAM,QAAQ,MAAM;AACtB,kBAAM,MAAM;AAAA,UACd;AAAA,QACF;AAEA,YAAI,MAAM,WAAW,MAAM;AACzB,gBAAM,UAAU,MAAM,MAAM,IAAI,MAAM;AAAA,QACxC;AAAA,MACF;AAAA,IACF,WAAW,iBAAiB,GAAG;AAG7B,mBAAa,yBAAyB,kBAAkB,OAAO,WAAW;AAAA,IAC5E;AAAA,EACF;AAEA,MAAI,MAAM,QAAQ,MAAM;AACtB,QAAI,MAAM,WAAW,MAAM;AACzB,YAAM,UAAU,MAAM,MAAM,IAAI,MAAM;AAAA,IACxC;AAAA,EAEF,WAAW,MAAM,QAAQ,KAAK;AAO5B,QAAI,MAAM,WAAW,QAAQ,MAAM,SAAS,UAAU;AACpD,iBAAW,OAAO,sEAAsE,MAAM,OAAO,GAAG;AAAA,IAC1G;AAEA,SAAK,YAAY,GAAG,eAAe,MAAM,cAAc,QAAQ,YAAY,cAAc,aAAa,GAAG;AACvG,MAAAA,QAAO,MAAM,cAAc,SAAS;AAEpC,UAAIA,MAAK,QAAQ,MAAM,MAAM,GAAG;AAC9B,cAAM,SAASA,MAAK,UAAU,MAAM,MAAM;AAC1C,cAAM,MAAMA,MAAK;AACjB,YAAI,MAAM,WAAW,MAAM;AACzB,gBAAM,UAAU,MAAM,MAAM,IAAI,MAAM;AAAA,QACxC;AACA;AAAA,MACF;AAAA,IACF;AAAA,EACF,WAAW,MAAM,QAAQ,KAAK;AAC5B,QAAI,kBAAkB,KAAK,MAAM,QAAQ,MAAM,QAAQ,UAAU,GAAG,MAAM,GAAG,GAAG;AAC9E,MAAAA,QAAO,MAAM,QAAQ,MAAM,QAAQ,UAAU,EAAE,MAAM,GAAG;AAAA,IAC1D,OAAO;AAEL,MAAAA,QAAO;AACP,iBAAW,MAAM,QAAQ,MAAM,MAAM,QAAQ,UAAU;AAEvD,WAAK,YAAY,GAAG,eAAe,SAAS,QAAQ,YAAY,cAAc,aAAa,GAAG;AAC5F,YAAI,MAAM,IAAI,MAAM,GAAG,SAAS,SAAS,EAAE,IAAI,MAAM,MAAM,SAAS,SAAS,EAAE,KAAK;AAClF,UAAAA,QAAO,SAAS,SAAS;AACzB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,QAAI,CAACA,OAAM;AACT,iBAAW,OAAO,mBAAmB,MAAM,MAAM,GAAG;AAAA,IACtD;AAEA,QAAI,MAAM,WAAW,QAAQA,MAAK,SAAS,MAAM,MAAM;AACrD,iBAAW,OAAO,kCAAkC,MAAM,MAAM,0BAA0BA,MAAK,OAAO,aAAa,MAAM,OAAO,GAAG;AAAA,IACrI;AAEA,QAAI,CAACA,MAAK,QAAQ,MAAM,QAAQ,MAAM,GAAG,GAAG;AAC1C,iBAAW,OAAO,kCAAkC,MAAM,MAAM,gBAAgB;AAAA,IAClF,OAAO;AACL,YAAM,SAASA,MAAK,UAAU,MAAM,QAAQ,MAAM,GAAG;AACrD,UAAI,MAAM,WAAW,MAAM;AACzB,cAAM,UAAU,MAAM,MAAM,IAAI,MAAM;AAAA,MACxC;AAAA,IACF;AAAA,EACF;AAEA,MAAI,MAAM,aAAa,MAAM;AAC3B,UAAM,SAAS,SAAS,KAAK;AAAA,EAC/B;AACA,SAAO,MAAM,QAAQ,QAAS,MAAM,WAAW,QAAQ;AACzD;AAEA,SAAS,aAAa,OAAO;AAC3B,MAAI,gBAAgB,MAAM,UACtB,WACA,eACA,eACA,gBAAgB,OAChB;AAEJ,QAAM,UAAU;AAChB,QAAM,kBAAkB,MAAM;AAC9B,QAAM,SAAS,uBAAO,OAAO,IAAI;AACjC,QAAM,YAAY,uBAAO,OAAO,IAAI;AAEpC,UAAQ,KAAK,MAAM,MAAM,WAAW,MAAM,QAAQ,OAAO,GAAG;AAC1D,wBAAoB,OAAO,MAAM,EAAE;AAEnC,SAAK,MAAM,MAAM,WAAW,MAAM,QAAQ;AAE1C,QAAI,MAAM,aAAa,KAAK,OAAO,IAAa;AAC9C;AAAA,IACF;AAEA,oBAAgB;AAChB,SAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAC5C,gBAAY,MAAM;AAElB,WAAO,OAAO,KAAK,CAAC,aAAa,EAAE,GAAG;AACpC,WAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,IAC9C;AAEA,oBAAgB,MAAM,MAAM,MAAM,WAAW,MAAM,QAAQ;AAC3D,oBAAgB,CAAC;AAEjB,QAAI,cAAc,SAAS,GAAG;AAC5B,iBAAW,OAAO,8DAA8D;AAAA,IAClF;AAEA,WAAO,OAAO,GAAG;AACf,aAAO,eAAe,EAAE,GAAG;AACzB,aAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,MAC9C;AAEA,UAAI,OAAO,IAAa;AACtB,WAAG;AAAE,eAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,QAAG,SAC7C,OAAO,KAAK,CAAC,OAAO,EAAE;AAC7B;AAAA,MACF;AAEA,UAAI,OAAO,EAAE,EAAG;AAEhB,kBAAY,MAAM;AAElB,aAAO,OAAO,KAAK,CAAC,aAAa,EAAE,GAAG;AACpC,aAAK,MAAM,MAAM,WAAW,EAAE,MAAM,QAAQ;AAAA,MAC9C;AAEA,oBAAc,KAAK,MAAM,MAAM,MAAM,WAAW,MAAM,QAAQ,CAAC;AAAA,IACjE;AAEA,QAAI,OAAO,EAAG,eAAc,KAAK;AAEjC,QAAI,kBAAkB,KAAK,mBAAmB,aAAa,GAAG;AAC5D,wBAAkB,aAAa,EAAE,OAAO,eAAe,aAAa;AAAA,IACtE,OAAO;AACL,mBAAa,OAAO,iCAAiC,gBAAgB,GAAG;AAAA,IAC1E;AAAA,EACF;AAEA,sBAAoB,OAAO,MAAM,EAAE;AAEnC,MAAI,MAAM,eAAe,KACrB,MAAM,MAAM,WAAW,MAAM,QAAQ,MAAU,MAC/C,MAAM,MAAM,WAAW,MAAM,WAAW,CAAC,MAAM,MAC/C,MAAM,MAAM,WAAW,MAAM,WAAW,CAAC,MAAM,IAAa;AAC9D,UAAM,YAAY;AAClB,wBAAoB,OAAO,MAAM,EAAE;AAAA,EAErC,WAAW,eAAe;AACxB,eAAW,OAAO,iCAAiC;AAAA,EACrD;AAEA,cAAY,OAAO,MAAM,aAAa,GAAG,mBAAmB,OAAO,IAAI;AACvE,sBAAoB,OAAO,MAAM,EAAE;AAEnC,MAAI,MAAM,mBACN,8BAA8B,KAAK,MAAM,MAAM,MAAM,eAAe,MAAM,QAAQ,CAAC,GAAG;AACxF,iBAAa,OAAO,kDAAkD;AAAA,EACxE;AAEA,QAAM,UAAU,KAAK,MAAM,MAAM;AAEjC,MAAI,MAAM,aAAa,MAAM,aAAa,sBAAsB,KAAK,GAAG;AAEtE,QAAI,MAAM,MAAM,WAAW,MAAM,QAAQ,MAAM,IAAa;AAC1D,YAAM,YAAY;AAClB,0BAAoB,OAAO,MAAM,EAAE;AAAA,IACrC;AACA;AAAA,EACF;AAEA,MAAI,MAAM,WAAY,MAAM,SAAS,GAAI;AACvC,eAAW,OAAO,uDAAuD;AAAA,EAC3E,OAAO;AACL;AAAA,EACF;AACF;AAGA,SAAS,cAAc,OAAO,SAAS;AACrC,UAAQ,OAAO,KAAK;AACpB,YAAU,WAAW,CAAC;AAEtB,MAAI,MAAM,WAAW,GAAG;AAGtB,QAAI,MAAM,WAAW,MAAM,SAAS,CAAC,MAAM,MACvC,MAAM,WAAW,MAAM,SAAS,CAAC,MAAM,IAAc;AACvD,eAAS;AAAA,IACX;AAGA,QAAI,MAAM,WAAW,CAAC,MAAM,OAAQ;AAClC,cAAQ,MAAM,MAAM,CAAC;AAAA,IACvB;AAAA,EACF;AAEA,MAAI,QAAQ,IAAI,QAAQ,OAAO,OAAO;AAEtC,MAAI,UAAU,MAAM,QAAQ,IAAI;AAEhC,MAAI,YAAY,IAAI;AAClB,UAAM,WAAW;AACjB,eAAW,OAAO,mCAAmC;AAAA,EACvD;AAGA,QAAM,SAAS;AAEf,SAAO,MAAM,MAAM,WAAW,MAAM,QAAQ,MAAM,IAAiB;AACjE,UAAM,cAAc;AACpB,UAAM,YAAY;AAAA,EACpB;AAEA,SAAO,MAAM,WAAY,MAAM,SAAS,GAAI;AAC1C,iBAAa,KAAK;AAAA,EACpB;AAEA,SAAO,MAAM;AACf;AAGA,SAAS,UAAU,OAAO,UAAU,SAAS;AAC3C,MAAI,aAAa,QAAQ,OAAO,aAAa,YAAY,OAAO,YAAY,aAAa;AACvF,cAAU;AACV,eAAW;AAAA,EACb;AAEA,MAAI,YAAY,cAAc,OAAO,OAAO;AAE5C,MAAI,OAAO,aAAa,YAAY;AAClC,WAAO;AAAA,EACT;AAEA,WAAS,QAAQ,GAAG,SAAS,UAAU,QAAQ,QAAQ,QAAQ,SAAS,GAAG;AACzE,aAAS,UAAU,KAAK,CAAC;AAAA,EAC3B;AACF;AAGA,SAAS,OAAO,OAAO,SAAS;AAC9B,MAAI,YAAY,cAAc,OAAO,OAAO;AAE5C,MAAI,UAAU,WAAW,GAAG;AAE1B,WAAO;AAAA,EACT,WAAW,UAAU,WAAW,GAAG;AACjC,WAAO,UAAU,CAAC;AAAA,EACpB;AACA,QAAM,IAAI,UAAU,0DAA0D;AAChF;AAGA,IAAI,YAAY;AAChB,IAAI,SAAY;AAEhB,IAAI,SAAS;AAAA,EACZ,SAAS;AAAA,EACT,MAAM;AACP;AAQA,IAAI,YAAkB,OAAO,UAAU;AACvC,IAAI,kBAAkB,OAAO,UAAU;AAEvC,IAAI,WAA4B;AAChC,IAAI,WAA4B;AAChC,IAAI,iBAA4B;AAChC,IAAI,uBAA4B;AAChC,IAAI,aAA4B;AAChC,IAAI,mBAA4B;AAChC,IAAI,oBAA4B;AAChC,IAAI,aAA4B;AAChC,IAAI,eAA4B;AAChC,IAAI,iBAA4B;AAChC,IAAI,oBAA4B;AAChC,IAAI,gBAA4B;AAChC,IAAI,aAA4B;AAChC,IAAI,aAA4B;AAChC,IAAI,aAA4B;AAChC,IAAI,cAA4B;AAChC,IAAI,oBAA4B;AAChC,IAAI,gBAA4B;AAChC,IAAI,qBAA4B;AAChC,IAAI,2BAA4B;AAChC,IAAI,4BAA4B;AAChC,IAAI,oBAA4B;AAChC,IAAI,0BAA4B;AAChC,IAAI,qBAA4B;AAChC,IAAI,2BAA4B;AAEhC,IAAI,mBAAmB,CAAC;AAExB,iBAAiB,CAAI,IAAM;AAC3B,iBAAiB,CAAI,IAAM;AAC3B,iBAAiB,CAAI,IAAM;AAC3B,iBAAiB,CAAI,IAAM;AAC3B,iBAAiB,EAAI,IAAM;AAC3B,iBAAiB,EAAI,IAAM;AAC3B,iBAAiB,EAAI,IAAM;AAC3B,iBAAiB,EAAI,IAAM;AAC3B,iBAAiB,EAAI,IAAM;AAC3B,iBAAiB,EAAI,IAAM;AAC3B,iBAAiB,EAAI,IAAM;AAC3B,iBAAiB,GAAI,IAAM;AAC3B,iBAAiB,GAAI,IAAM;AAC3B,iBAAiB,IAAM,IAAI;AAC3B,iBAAiB,IAAM,IAAI;AAE3B,IAAI,6BAA6B;AAAA,EAC/B;AAAA,EAAK;AAAA,EAAK;AAAA,EAAO;AAAA,EAAO;AAAA,EAAO;AAAA,EAAM;AAAA,EAAM;AAAA,EAC3C;AAAA,EAAK;AAAA,EAAK;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAO;AAAA,EAAO;AAC5C;AAEA,IAAI,2BAA2B;AAE/B,SAAS,gBAAgBD,SAAQD,MAAK;AACpC,MAAI,QAAQ,MAAM,OAAO,QAAQ,KAAK,OAAOE;AAE7C,MAAIF,SAAQ,KAAM,QAAO,CAAC;AAE1B,WAAS,CAAC;AACV,SAAO,OAAO,KAAKA,IAAG;AAEtB,OAAK,QAAQ,GAAG,SAAS,KAAK,QAAQ,QAAQ,QAAQ,SAAS,GAAG;AAChE,UAAM,KAAK,KAAK;AAChB,YAAQ,OAAOA,KAAI,GAAG,CAAC;AAEvB,QAAI,IAAI,MAAM,GAAG,CAAC,MAAM,MAAM;AAC5B,YAAM,uBAAuB,IAAI,MAAM,CAAC;AAAA,IAC1C;AACA,IAAAE,QAAOD,QAAO,gBAAgB,UAAU,EAAE,GAAG;AAE7C,QAAIC,SAAQ,gBAAgB,KAAKA,MAAK,cAAc,KAAK,GAAG;AAC1D,cAAQA,MAAK,aAAa,KAAK;AAAA,IACjC;AAEA,WAAO,GAAG,IAAI;AAAA,EAChB;AAEA,SAAO;AACT;AAEA,SAAS,UAAU,WAAW;AAC5B,MAAI,QAAQ,QAAQ;AAEpB,WAAS,UAAU,SAAS,EAAE,EAAE,YAAY;AAE5C,MAAI,aAAa,KAAM;AACrB,aAAS;AACT,aAAS;AAAA,EACX,WAAW,aAAa,OAAQ;AAC9B,aAAS;AACT,aAAS;AAAA,EACX,WAAW,aAAa,YAAY;AAClC,aAAS;AACT,aAAS;AAAA,EACX,OAAO;AACL,UAAM,IAAI,UAAU,+DAA+D;AAAA,EACrF;AAEA,SAAO,OAAO,SAAS,OAAO,OAAO,KAAK,SAAS,OAAO,MAAM,IAAI;AACtE;AAGA,IAAI,sBAAsB;AAA1B,IACI,sBAAsB;AAE1B,SAAS,MAAM,SAAS;AACtB,OAAK,SAAgB,QAAQ,QAAQ,KAAK;AAC1C,OAAK,SAAgB,KAAK,IAAI,GAAI,QAAQ,QAAQ,KAAK,CAAE;AACzD,OAAK,gBAAgB,QAAQ,eAAe,KAAK;AACjD,OAAK,cAAgB,QAAQ,aAAa,KAAK;AAC/C,OAAK,YAAiB,OAAO,UAAU,QAAQ,WAAW,CAAC,IAAI,KAAK,QAAQ,WAAW;AACvF,OAAK,WAAgB,gBAAgB,KAAK,QAAQ,QAAQ,QAAQ,KAAK,IAAI;AAC3E,OAAK,WAAgB,QAAQ,UAAU,KAAK;AAC5C,OAAK,YAAgB,QAAQ,WAAW,KAAK;AAC7C,OAAK,SAAgB,QAAQ,QAAQ,KAAK;AAC1C,OAAK,eAAgB,QAAQ,cAAc,KAAK;AAChD,OAAK,eAAgB,QAAQ,cAAc,KAAK;AAChD,OAAK,cAAgB,QAAQ,aAAa,MAAM,MAAM,sBAAsB;AAC5E,OAAK,cAAgB,QAAQ,aAAa,KAAK;AAC/C,OAAK,WAAgB,OAAO,QAAQ,UAAU,MAAM,aAAa,QAAQ,UAAU,IAAI;AAEvF,OAAK,gBAAgB,KAAK,OAAO;AACjC,OAAK,gBAAgB,KAAK,OAAO;AAEjC,OAAK,MAAM;AACX,OAAK,SAAS;AAEd,OAAK,aAAa,CAAC;AACnB,OAAK,iBAAiB;AACxB;AAGA,SAAS,aAAa,QAAQ,QAAQ;AACpC,MAAI,MAAM,OAAO,OAAO,KAAK,MAAM,GAC/B,WAAW,GACX,OAAO,IACP,SAAS,IACT,MACA,SAAS,OAAO;AAEpB,SAAO,WAAW,QAAQ;AACxB,WAAO,OAAO,QAAQ,MAAM,QAAQ;AACpC,QAAI,SAAS,IAAI;AACf,aAAO,OAAO,MAAM,QAAQ;AAC5B,iBAAW;AAAA,IACb,OAAO;AACL,aAAO,OAAO,MAAM,UAAU,OAAO,CAAC;AACtC,iBAAW,OAAO;AAAA,IACpB;AAEA,QAAI,KAAK,UAAU,SAAS,KAAM,WAAU;AAE5C,cAAU;AAAA,EACZ;AAEA,SAAO;AACT;AAEA,SAAS,iBAAiB,OAAO,OAAO;AACtC,SAAO,OAAO,OAAO,OAAO,KAAK,MAAM,SAAS,KAAK;AACvD;AAEA,SAAS,sBAAsB,OAAOE,MAAK;AACzC,MAAI,OAAO,QAAQF;AAEnB,OAAK,QAAQ,GAAG,SAAS,MAAM,cAAc,QAAQ,QAAQ,QAAQ,SAAS,GAAG;AAC/E,IAAAA,QAAO,MAAM,cAAc,KAAK;AAEhC,QAAIA,MAAK,QAAQE,IAAG,GAAG;AACrB,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AAGA,SAAS,aAAa,GAAG;AACvB,SAAO,MAAM,cAAc,MAAM;AACnC;AAMA,SAAS,YAAY,GAAG;AACtB,SAAS,MAAW,KAAK,KAAK,OACrB,OAAW,KAAK,KAAK,SAAa,MAAM,QAAU,MAAM,QACxD,SAAW,KAAK,KAAK,SAAa,MAAM,YACxC,SAAW,KAAK,KAAK;AAChC;AAOA,SAAS,qBAAqB,GAAG;AAC/B,SAAO,YAAY,CAAC,KACf,MAAM,YAEN,MAAM,wBACN,MAAM;AACb;AAWA,SAAS,YAAY,GAAG,MAAM,SAAS;AACrC,MAAI,wBAAwB,qBAAqB,CAAC;AAClD,MAAI,YAAY,yBAAyB,CAAC,aAAa,CAAC;AACxD;AAAA;AAAA,KAEE;AAAA;AAAA,MACE;AAAA,QACE,yBAEG,MAAM,cACN,MAAM,4BACN,MAAM,6BACN,MAAM,2BACN,MAAM,6BAGV,MAAM,cACN,EAAE,SAAS,cAAc,CAAC,cACzB,qBAAqB,IAAI,KAAK,CAAC,aAAa,IAAI,KAAK,MAAM,cAC3D,SAAS,cAAc;AAAA;AAC/B;AAGA,SAAS,iBAAiB,GAAG;AAI3B,SAAO,YAAY,CAAC,KAAK,MAAM,YAC1B,CAAC,aAAa,CAAC,KAGf,MAAM,cACN,MAAM,iBACN,MAAM,cACN,MAAM,cACN,MAAM,4BACN,MAAM,6BACN,MAAM,2BACN,MAAM,4BAEN,MAAM,cACN,MAAM,kBACN,MAAM,iBACN,MAAM,oBACN,MAAM,sBACN,MAAM,eACN,MAAM,qBACN,MAAM,qBACN,MAAM,qBAEN,MAAM,gBACN,MAAM,sBACN,MAAM;AACb;AAGA,SAAS,gBAAgB,GAAG;AAE1B,SAAO,CAAC,aAAa,CAAC,KAAK,MAAM;AACnC;AAGA,SAAS,YAAY,QAAQ,KAAK;AAChC,MAAI,QAAQ,OAAO,WAAW,GAAG,GAAG;AACpC,MAAI,SAAS,SAAU,SAAS,SAAU,MAAM,IAAI,OAAO,QAAQ;AACjE,aAAS,OAAO,WAAW,MAAM,CAAC;AAClC,QAAI,UAAU,SAAU,UAAU,OAAQ;AAExC,cAAQ,QAAQ,SAAU,OAAQ,SAAS,QAAS;AAAA,IACtD;AAAA,EACF;AACA,SAAO;AACT;AAGA,SAAS,oBAAoB,QAAQ;AACnC,MAAI,iBAAiB;AACrB,SAAO,eAAe,KAAK,MAAM;AACnC;AAEA,IAAI,cAAgB;AAApB,IACI,eAAgB;AADpB,IAEI,gBAAgB;AAFpB,IAGI,eAAgB;AAHpB,IAII,eAAgB;AASpB,SAAS,kBAAkB,QAAQ,gBAAgB,gBAAgB,WACjE,mBAAmB,aAAa,aAAa,SAAS;AAEtD,MAAI;AACJ,MAAI,OAAO;AACX,MAAI,WAAW;AACf,MAAI,eAAe;AACnB,MAAI,kBAAkB;AACtB,MAAI,mBAAmB,cAAc;AACrC,MAAI,oBAAoB;AACxB,MAAI,QAAQ,iBAAiB,YAAY,QAAQ,CAAC,CAAC,KACxC,gBAAgB,YAAY,QAAQ,OAAO,SAAS,CAAC,CAAC;AAEjE,MAAI,kBAAkB,aAAa;AAGjC,SAAK,IAAI,GAAG,IAAI,OAAO,QAAQ,QAAQ,QAAU,KAAK,IAAI,KAAK;AAC7D,aAAO,YAAY,QAAQ,CAAC;AAC5B,UAAI,CAAC,YAAY,IAAI,GAAG;AACtB,eAAO;AAAA,MACT;AACA,cAAQ,SAAS,YAAY,MAAM,UAAU,OAAO;AACpD,iBAAW;AAAA,IACb;AAAA,EACF,OAAO;AAEL,SAAK,IAAI,GAAG,IAAI,OAAO,QAAQ,QAAQ,QAAU,KAAK,IAAI,KAAK;AAC7D,aAAO,YAAY,QAAQ,CAAC;AAC5B,UAAI,SAAS,gBAAgB;AAC3B,uBAAe;AAEf,YAAI,kBAAkB;AACpB,4BAAkB;AAAA,UAEf,IAAI,oBAAoB,IAAI,aAC5B,OAAO,oBAAoB,CAAC,MAAM;AACrC,8BAAoB;AAAA,QACtB;AAAA,MACF,WAAW,CAAC,YAAY,IAAI,GAAG;AAC7B,eAAO;AAAA,MACT;AACA,cAAQ,SAAS,YAAY,MAAM,UAAU,OAAO;AACpD,iBAAW;AAAA,IACb;AAEA,sBAAkB,mBAAoB,qBACnC,IAAI,oBAAoB,IAAI,aAC5B,OAAO,oBAAoB,CAAC,MAAM;AAAA,EACvC;AAIA,MAAI,CAAC,gBAAgB,CAAC,iBAAiB;AAGrC,QAAI,SAAS,CAAC,eAAe,CAAC,kBAAkB,MAAM,GAAG;AACvD,aAAO;AAAA,IACT;AACA,WAAO,gBAAgB,sBAAsB,eAAe;AAAA,EAC9D;AAEA,MAAI,iBAAiB,KAAK,oBAAoB,MAAM,GAAG;AACrD,WAAO;AAAA,EACT;AAGA,MAAI,CAAC,aAAa;AAChB,WAAO,kBAAkB,eAAe;AAAA,EAC1C;AACA,SAAO,gBAAgB,sBAAsB,eAAe;AAC9D;AAQA,SAAS,YAAY,OAAO,QAAQ,OAAO,OAAO,SAAS;AACzD,QAAM,OAAQ,WAAY;AACxB,QAAI,OAAO,WAAW,GAAG;AACvB,aAAO,MAAM,gBAAgB,sBAAsB,OAAO;AAAA,IAC5D;AACA,QAAI,CAAC,MAAM,cAAc;AACvB,UAAI,2BAA2B,QAAQ,MAAM,MAAM,MAAM,yBAAyB,KAAK,MAAM,GAAG;AAC9F,eAAO,MAAM,gBAAgB,sBAAuB,MAAM,SAAS,MAAQ,MAAM,SAAS;AAAA,MAC5F;AAAA,IACF;AAEA,QAAI,SAAS,MAAM,SAAS,KAAK,IAAI,GAAG,KAAK;AAQ7C,QAAI,YAAY,MAAM,cAAc,KAChC,KAAK,KAAK,IAAI,KAAK,IAAI,MAAM,WAAW,EAAE,GAAG,MAAM,YAAY,MAAM;AAGzE,QAAI,iBAAiB,SAEf,MAAM,YAAY,MAAM,SAAS,MAAM;AAC7C,aAAS,cAAcC,SAAQ;AAC7B,aAAO,sBAAsB,OAAOA,OAAM;AAAA,IAC5C;AAEA,YAAQ;AAAA,MAAkB;AAAA,MAAQ;AAAA,MAAgB,MAAM;AAAA,MAAQ;AAAA,MAC9D;AAAA,MAAe,MAAM;AAAA,MAAa,MAAM,eAAe,CAAC;AAAA,MAAO;AAAA,IAAO,GAAG;AAAA,MAEzE,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO,MAAM,OAAO,QAAQ,MAAM,IAAI,IAAI;AAAA,MAC5C,KAAK;AACH,eAAO,MAAM,YAAY,QAAQ,MAAM,MAAM,IACzC,kBAAkB,aAAa,QAAQ,MAAM,CAAC;AAAA,MACpD,KAAK;AACH,eAAO,MAAM,YAAY,QAAQ,MAAM,MAAM,IACzC,kBAAkB,aAAa,WAAW,QAAQ,SAAS,GAAG,MAAM,CAAC;AAAA,MAC3E,KAAK;AACH,eAAO,MAAM,aAAa,MAAM,IAAI;AAAA,MACtC;AACE,cAAM,IAAI,UAAU,wCAAwC;AAAA,IAChE;AAAA,EACF,EAAE;AACJ;AAGA,SAAS,YAAY,QAAQ,gBAAgB;AAC3C,MAAI,kBAAkB,oBAAoB,MAAM,IAAI,OAAO,cAAc,IAAI;AAG7E,MAAI,OAAgB,OAAO,OAAO,SAAS,CAAC,MAAM;AAClD,MAAI,OAAO,SAAS,OAAO,OAAO,SAAS,CAAC,MAAM,QAAQ,WAAW;AACrE,MAAI,QAAQ,OAAO,MAAO,OAAO,KAAK;AAEtC,SAAO,kBAAkB,QAAQ;AACnC;AAGA,SAAS,kBAAkB,QAAQ;AACjC,SAAO,OAAO,OAAO,SAAS,CAAC,MAAM,OAAO,OAAO,MAAM,GAAG,EAAE,IAAI;AACpE;AAIA,SAAS,WAAW,QAAQ,OAAO;AAKjC,MAAI,SAAS;AAGb,MAAI,SAAU,WAAY;AACxB,QAAI,SAAS,OAAO,QAAQ,IAAI;AAChC,aAAS,WAAW,KAAK,SAAS,OAAO;AACzC,WAAO,YAAY;AACnB,WAAO,SAAS,OAAO,MAAM,GAAG,MAAM,GAAG,KAAK;AAAA,EAChD,EAAE;AAEF,MAAI,mBAAmB,OAAO,CAAC,MAAM,QAAQ,OAAO,CAAC,MAAM;AAC3D,MAAI;AAGJ,MAAI;AACJ,SAAQ,QAAQ,OAAO,KAAK,MAAM,GAAI;AACpC,QAAI,SAAS,MAAM,CAAC,GAAG,OAAO,MAAM,CAAC;AACrC,mBAAgB,KAAK,CAAC,MAAM;AAC5B,cAAU,UACL,CAAC,oBAAoB,CAAC,gBAAgB,SAAS,KAC9C,OAAO,MACT,SAAS,MAAM,KAAK;AACxB,uBAAmB;AAAA,EACrB;AAEA,SAAO;AACT;AAMA,SAAS,SAAS,MAAM,OAAO;AAC7B,MAAI,SAAS,MAAM,KAAK,CAAC,MAAM,IAAK,QAAO;AAG3C,MAAI,UAAU;AACd,MAAI;AAEJ,MAAI,QAAQ,GAAG,KAAK,OAAO,GAAG,OAAO;AACrC,MAAI,SAAS;AAMb,SAAQ,QAAQ,QAAQ,KAAK,IAAI,GAAI;AACnC,WAAO,MAAM;AAEb,QAAI,OAAO,QAAQ,OAAO;AACxB,YAAO,OAAO,QAAS,OAAO;AAC9B,gBAAU,OAAO,KAAK,MAAM,OAAO,GAAG;AAEtC,cAAQ,MAAM;AAAA,IAChB;AACA,WAAO;AAAA,EACT;AAIA,YAAU;AAEV,MAAI,KAAK,SAAS,QAAQ,SAAS,OAAO,OAAO;AAC/C,cAAU,KAAK,MAAM,OAAO,IAAI,IAAI,OAAO,KAAK,MAAM,OAAO,CAAC;AAAA,EAChE,OAAO;AACL,cAAU,KAAK,MAAM,KAAK;AAAA,EAC5B;AAEA,SAAO,OAAO,MAAM,CAAC;AACvB;AAGA,SAAS,aAAa,QAAQ;AAC5B,MAAI,SAAS;AACb,MAAI,OAAO;AACX,MAAI;AAEJ,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,QAAQ,QAAU,KAAK,IAAI,KAAK;AACjE,WAAO,YAAY,QAAQ,CAAC;AAC5B,gBAAY,iBAAiB,IAAI;AAEjC,QAAI,CAAC,aAAa,YAAY,IAAI,GAAG;AACnC,gBAAU,OAAO,CAAC;AAClB,UAAI,QAAQ,MAAS,WAAU,OAAO,IAAI,CAAC;AAAA,IAC7C,OAAO;AACL,gBAAU,aAAa,UAAU,IAAI;AAAA,IACvC;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,kBAAkB,OAAO,OAAO,QAAQ;AAC/C,MAAI,UAAU,IACV,OAAU,MAAM,KAChB,OACA,QACA;AAEJ,OAAK,QAAQ,GAAG,SAAS,OAAO,QAAQ,QAAQ,QAAQ,SAAS,GAAG;AAClE,YAAQ,OAAO,KAAK;AAEpB,QAAI,MAAM,UAAU;AAClB,cAAQ,MAAM,SAAS,KAAK,QAAQ,OAAO,KAAK,GAAG,KAAK;AAAA,IAC1D;AAGA,QAAI,UAAU,OAAO,OAAO,OAAO,OAAO,KAAK,KAC1C,OAAO,UAAU,eACjB,UAAU,OAAO,OAAO,MAAM,OAAO,KAAK,GAAI;AAEjD,UAAI,YAAY,GAAI,YAAW,OAAO,CAAC,MAAM,eAAe,MAAM;AAClE,iBAAW,MAAM;AAAA,IACnB;AAAA,EACF;AAEA,QAAM,MAAM;AACZ,QAAM,OAAO,MAAM,UAAU;AAC/B;AAEA,SAAS,mBAAmB,OAAO,OAAO,QAAQ,SAAS;AACzD,MAAI,UAAU,IACV,OAAU,MAAM,KAChB,OACA,QACA;AAEJ,OAAK,QAAQ,GAAG,SAAS,OAAO,QAAQ,QAAQ,QAAQ,SAAS,GAAG;AAClE,YAAQ,OAAO,KAAK;AAEpB,QAAI,MAAM,UAAU;AAClB,cAAQ,MAAM,SAAS,KAAK,QAAQ,OAAO,KAAK,GAAG,KAAK;AAAA,IAC1D;AAGA,QAAI,UAAU,OAAO,QAAQ,GAAG,OAAO,MAAM,MAAM,OAAO,IAAI,KACzD,OAAO,UAAU,eACjB,UAAU,OAAO,QAAQ,GAAG,MAAM,MAAM,MAAM,OAAO,IAAI,GAAI;AAEhE,UAAI,CAAC,WAAW,YAAY,IAAI;AAC9B,mBAAW,iBAAiB,OAAO,KAAK;AAAA,MAC1C;AAEA,UAAI,MAAM,QAAQ,mBAAmB,MAAM,KAAK,WAAW,CAAC,GAAG;AAC7D,mBAAW;AAAA,MACb,OAAO;AACL,mBAAW;AAAA,MACb;AAEA,iBAAW,MAAM;AAAA,IACnB;AAAA,EACF;AAEA,QAAM,MAAM;AACZ,QAAM,OAAO,WAAW;AAC1B;AAEA,SAAS,iBAAiB,OAAO,OAAO,QAAQ;AAC9C,MAAI,UAAgB,IAChB,OAAgB,MAAM,KACtB,gBAAgB,OAAO,KAAK,MAAM,GAClC,OACA,QACA,WACA,aACA;AAEJ,OAAK,QAAQ,GAAG,SAAS,cAAc,QAAQ,QAAQ,QAAQ,SAAS,GAAG;AAEzE,iBAAa;AACb,QAAI,YAAY,GAAI,eAAc;AAElC,QAAI,MAAM,aAAc,eAAc;AAEtC,gBAAY,cAAc,KAAK;AAC/B,kBAAc,OAAO,SAAS;AAE9B,QAAI,MAAM,UAAU;AAClB,oBAAc,MAAM,SAAS,KAAK,QAAQ,WAAW,WAAW;AAAA,IAClE;AAEA,QAAI,CAAC,UAAU,OAAO,OAAO,WAAW,OAAO,KAAK,GAAG;AACrD;AAAA,IACF;AAEA,QAAI,MAAM,KAAK,SAAS,KAAM,eAAc;AAE5C,kBAAc,MAAM,QAAQ,MAAM,eAAe,MAAM,MAAM,OAAO,MAAM,eAAe,KAAK;AAE9F,QAAI,CAAC,UAAU,OAAO,OAAO,aAAa,OAAO,KAAK,GAAG;AACvD;AAAA,IACF;AAEA,kBAAc,MAAM;AAGpB,eAAW;AAAA,EACb;AAEA,QAAM,MAAM;AACZ,QAAM,OAAO,MAAM,UAAU;AAC/B;AAEA,SAAS,kBAAkB,OAAO,OAAO,QAAQ,SAAS;AACxD,MAAI,UAAgB,IAChB,OAAgB,MAAM,KACtB,gBAAgB,OAAO,KAAK,MAAM,GAClC,OACA,QACA,WACA,aACA,cACA;AAGJ,MAAI,MAAM,aAAa,MAAM;AAE3B,kBAAc,KAAK;AAAA,EACrB,WAAW,OAAO,MAAM,aAAa,YAAY;AAE/C,kBAAc,KAAK,MAAM,QAAQ;AAAA,EACnC,WAAW,MAAM,UAAU;AAEzB,UAAM,IAAI,UAAU,0CAA0C;AAAA,EAChE;AAEA,OAAK,QAAQ,GAAG,SAAS,cAAc,QAAQ,QAAQ,QAAQ,SAAS,GAAG;AACzE,iBAAa;AAEb,QAAI,CAAC,WAAW,YAAY,IAAI;AAC9B,oBAAc,iBAAiB,OAAO,KAAK;AAAA,IAC7C;AAEA,gBAAY,cAAc,KAAK;AAC/B,kBAAc,OAAO,SAAS;AAE9B,QAAI,MAAM,UAAU;AAClB,oBAAc,MAAM,SAAS,KAAK,QAAQ,WAAW,WAAW;AAAA,IAClE;AAEA,QAAI,CAAC,UAAU,OAAO,QAAQ,GAAG,WAAW,MAAM,MAAM,IAAI,GAAG;AAC7D;AAAA,IACF;AAEA,mBAAgB,MAAM,QAAQ,QAAQ,MAAM,QAAQ,OACpC,MAAM,QAAQ,MAAM,KAAK,SAAS;AAElD,QAAI,cAAc;AAChB,UAAI,MAAM,QAAQ,mBAAmB,MAAM,KAAK,WAAW,CAAC,GAAG;AAC7D,sBAAc;AAAA,MAChB,OAAO;AACL,sBAAc;AAAA,MAChB;AAAA,IACF;AAEA,kBAAc,MAAM;AAEpB,QAAI,cAAc;AAChB,oBAAc,iBAAiB,OAAO,KAAK;AAAA,IAC7C;AAEA,QAAI,CAAC,UAAU,OAAO,QAAQ,GAAG,aAAa,MAAM,YAAY,GAAG;AACjE;AAAA,IACF;AAEA,QAAI,MAAM,QAAQ,mBAAmB,MAAM,KAAK,WAAW,CAAC,GAAG;AAC7D,oBAAc;AAAA,IAChB,OAAO;AACL,oBAAc;AAAA,IAChB;AAEA,kBAAc,MAAM;AAGpB,eAAW;AAAA,EACb;AAEA,QAAM,MAAM;AACZ,QAAM,OAAO,WAAW;AAC1B;AAEA,SAAS,WAAW,OAAO,QAAQ,UAAU;AAC3C,MAAI,SAAS,UAAU,OAAO,QAAQH,OAAM;AAE5C,aAAW,WAAW,MAAM,gBAAgB,MAAM;AAElD,OAAK,QAAQ,GAAG,SAAS,SAAS,QAAQ,QAAQ,QAAQ,SAAS,GAAG;AACpE,IAAAA,QAAO,SAAS,KAAK;AAErB,SAAKA,MAAK,cAAeA,MAAK,eACzB,CAACA,MAAK,cAAgB,OAAO,WAAW,YAAc,kBAAkBA,MAAK,gBAC7E,CAACA,MAAK,aAAcA,MAAK,UAAU,MAAM,IAAI;AAEhD,UAAI,UAAU;AACZ,YAAIA,MAAK,SAASA,MAAK,eAAe;AACpC,gBAAM,MAAMA,MAAK,cAAc,MAAM;AAAA,QACvC,OAAO;AACL,gBAAM,MAAMA,MAAK;AAAA,QACnB;AAAA,MACF,OAAO;AACL,cAAM,MAAM;AAAA,MACd;AAEA,UAAIA,MAAK,WAAW;AAClB,gBAAQ,MAAM,SAASA,MAAK,GAAG,KAAKA,MAAK;AAEzC,YAAI,UAAU,KAAKA,MAAK,SAAS,MAAM,qBAAqB;AAC1D,oBAAUA,MAAK,UAAU,QAAQ,KAAK;AAAA,QACxC,WAAW,gBAAgB,KAAKA,MAAK,WAAW,KAAK,GAAG;AACtD,oBAAUA,MAAK,UAAU,KAAK,EAAE,QAAQ,KAAK;AAAA,QAC/C,OAAO;AACL,gBAAM,IAAI,UAAU,OAAOA,MAAK,MAAM,iCAAiC,QAAQ,SAAS;AAAA,QAC1F;AAEA,cAAM,OAAO;AAAA,MACf;AAEA,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AAKA,SAAS,UAAU,OAAO,OAAO,QAAQ,OAAO,SAAS,OAAO,YAAY;AAC1E,QAAM,MAAM;AACZ,QAAM,OAAO;AAEb,MAAI,CAAC,WAAW,OAAO,QAAQ,KAAK,GAAG;AACrC,eAAW,OAAO,QAAQ,IAAI;AAAA,EAChC;AAEA,MAAIA,QAAO,UAAU,KAAK,MAAM,IAAI;AACpC,MAAI,UAAU;AACd,MAAI;AAEJ,MAAI,OAAO;AACT,YAAS,MAAM,YAAY,KAAK,MAAM,YAAY;AAAA,EACpD;AAEA,MAAI,gBAAgBA,UAAS,qBAAqBA,UAAS,kBACvD,gBACA;AAEJ,MAAI,eAAe;AACjB,qBAAiB,MAAM,WAAW,QAAQ,MAAM;AAChD,gBAAY,mBAAmB;AAAA,EACjC;AAEA,MAAK,MAAM,QAAQ,QAAQ,MAAM,QAAQ,OAAQ,aAAc,MAAM,WAAW,KAAK,QAAQ,GAAI;AAC/F,cAAU;AAAA,EACZ;AAEA,MAAI,aAAa,MAAM,eAAe,cAAc,GAAG;AACrD,UAAM,OAAO,UAAU;AAAA,EACzB,OAAO;AACL,QAAI,iBAAiB,aAAa,CAAC,MAAM,eAAe,cAAc,GAAG;AACvE,YAAM,eAAe,cAAc,IAAI;AAAA,IACzC;AACA,QAAIA,UAAS,mBAAmB;AAC9B,UAAI,SAAU,OAAO,KAAK,MAAM,IAAI,EAAE,WAAW,GAAI;AACnD,0BAAkB,OAAO,OAAO,MAAM,MAAM,OAAO;AACnD,YAAI,WAAW;AACb,gBAAM,OAAO,UAAU,iBAAiB,MAAM;AAAA,QAChD;AAAA,MACF,OAAO;AACL,yBAAiB,OAAO,OAAO,MAAM,IAAI;AACzC,YAAI,WAAW;AACb,gBAAM,OAAO,UAAU,iBAAiB,MAAM,MAAM;AAAA,QACtD;AAAA,MACF;AAAA,IACF,WAAWA,UAAS,kBAAkB;AACpC,UAAI,SAAU,MAAM,KAAK,WAAW,GAAI;AACtC,YAAI,MAAM,iBAAiB,CAAC,cAAc,QAAQ,GAAG;AACnD,6BAAmB,OAAO,QAAQ,GAAG,MAAM,MAAM,OAAO;AAAA,QAC1D,OAAO;AACL,6BAAmB,OAAO,OAAO,MAAM,MAAM,OAAO;AAAA,QACtD;AACA,YAAI,WAAW;AACb,gBAAM,OAAO,UAAU,iBAAiB,MAAM;AAAA,QAChD;AAAA,MACF,OAAO;AACL,0BAAkB,OAAO,OAAO,MAAM,IAAI;AAC1C,YAAI,WAAW;AACb,gBAAM,OAAO,UAAU,iBAAiB,MAAM,MAAM;AAAA,QACtD;AAAA,MACF;AAAA,IACF,WAAWA,UAAS,mBAAmB;AACrC,UAAI,MAAM,QAAQ,KAAK;AACrB,oBAAY,OAAO,MAAM,MAAM,OAAO,OAAO,OAAO;AAAA,MACtD;AAAA,IACF,WAAWA,UAAS,sBAAsB;AACxC,aAAO;AAAA,IACT,OAAO;AACL,UAAI,MAAM,YAAa,QAAO;AAC9B,YAAM,IAAI,UAAU,4CAA4CA,KAAI;AAAA,IACtE;AAEA,QAAI,MAAM,QAAQ,QAAQ,MAAM,QAAQ,KAAK;AAc3C,eAAS;AAAA,QACP,MAAM,IAAI,CAAC,MAAM,MAAM,MAAM,IAAI,MAAM,CAAC,IAAI,MAAM;AAAA,MACpD,EAAE,QAAQ,MAAM,KAAK;AAErB,UAAI,MAAM,IAAI,CAAC,MAAM,KAAK;AACxB,iBAAS,MAAM;AAAA,MACjB,WAAW,OAAO,MAAM,GAAG,EAAE,MAAM,sBAAsB;AACvD,iBAAS,OAAO,OAAO,MAAM,EAAE;AAAA,MACjC,OAAO;AACL,iBAAS,OAAO,SAAS;AAAA,MAC3B;AAEA,YAAM,OAAO,SAAS,MAAM,MAAM;AAAA,IACpC;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,uBAAuB,QAAQ,OAAO;AAC7C,MAAI,UAAU,CAAC,GACX,oBAAoB,CAAC,GACrB,OACA;AAEJ,cAAY,QAAQ,SAAS,iBAAiB;AAE9C,OAAK,QAAQ,GAAG,SAAS,kBAAkB,QAAQ,QAAQ,QAAQ,SAAS,GAAG;AAC7E,UAAM,WAAW,KAAK,QAAQ,kBAAkB,KAAK,CAAC,CAAC;AAAA,EACzD;AACA,QAAM,iBAAiB,IAAI,MAAM,MAAM;AACzC;AAEA,SAAS,YAAY,QAAQ,SAAS,mBAAmB;AACvD,MAAI,eACA,OACA;AAEJ,MAAI,WAAW,QAAQ,OAAO,WAAW,UAAU;AACjD,YAAQ,QAAQ,QAAQ,MAAM;AAC9B,QAAI,UAAU,IAAI;AAChB,UAAI,kBAAkB,QAAQ,KAAK,MAAM,IAAI;AAC3C,0BAAkB,KAAK,KAAK;AAAA,MAC9B;AAAA,IACF,OAAO;AACL,cAAQ,KAAK,MAAM;AAEnB,UAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,aAAK,QAAQ,GAAG,SAAS,OAAO,QAAQ,QAAQ,QAAQ,SAAS,GAAG;AAClE,sBAAY,OAAO,KAAK,GAAG,SAAS,iBAAiB;AAAA,QACvD;AAAA,MACF,OAAO;AACL,wBAAgB,OAAO,KAAK,MAAM;AAElC,aAAK,QAAQ,GAAG,SAAS,cAAc,QAAQ,QAAQ,QAAQ,SAAS,GAAG;AACzE,sBAAY,OAAO,cAAc,KAAK,CAAC,GAAG,SAAS,iBAAiB;AAAA,QACtE;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,OAAO,OAAO,SAAS;AAC9B,YAAU,WAAW,CAAC;AAEtB,MAAI,QAAQ,IAAI,MAAM,OAAO;AAE7B,MAAI,CAAC,MAAM,OAAQ,wBAAuB,OAAO,KAAK;AAEtD,MAAI,QAAQ;AAEZ,MAAI,MAAM,UAAU;AAClB,YAAQ,MAAM,SAAS,KAAK,EAAE,IAAI,MAAM,GAAG,IAAI,KAAK;AAAA,EACtD;AAEA,MAAI,UAAU,OAAO,GAAG,OAAO,MAAM,IAAI,EAAG,QAAO,MAAM,OAAO;AAEhE,SAAO;AACT;AAEA,IAAI,SAAS;AAEb,IAAI,SAAS;AAAA,EACZ,MAAM;AACP;AAEA,SAAS,QAAQ,MAAM,IAAI;AACzB,SAAO,WAAY;AACjB,UAAM,IAAI,MAAM,mBAAmB,OAAO,wCAC1B,KAAK,yCAAyC;AAAA,EAChE;AACF;AAGA,IAAI,OAAsB;AAC1B,IAAI,SAAsB;AAC1B,IAAI,kBAAsB;AAC1B,IAAI,cAAsB;AAC1B,IAAI,cAAsB;AAC1B,IAAI,iBAAsB;AAC1B,IAAI,OAAsB,OAAO;AACjC,IAAI,UAAsB,OAAO;AACjC,IAAI,OAAsB,OAAO;AACjC,IAAI,gBAAsB;AAG1B,IAAI,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,EACA;AAAA,EACA,MAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAGA,IAAI,WAAsB,QAAQ,YAAY,MAAM;AACpD,IAAI,cAAsB,QAAQ,eAAe,SAAS;AAC1D,IAAI,WAAsB,QAAQ,YAAY,MAAM;AAEpD,IAAI,SAAS;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AAEA,IAAO,kBAAQ;", "names": ["exception", "map", "schema", "type", "extend", "str", "string"]}