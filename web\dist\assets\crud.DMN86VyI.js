import{r as u,v as w,X as r}from"./index.BHZI5pdK.js";import{h as b,I as g}from"./vue.BNx9QYep.js";function y(a){return a.authorized=0,u({url:"/api/system/role/get_role_users/",method:"get",params:a})}function x(a,i){return u({url:`/api/system/role/${a}/add_role_users/`,method:"post",data:{users_id:i}})}const v=function({crudExpose:a,context:i}){const c=async e=>await y(e),d=async({form:e,row:o})=>{},f=async({row:e})=>{},m=async({form:e})=>{},s=b([]),p=e=>{const n=a.getTableData().filter(t=>!e.includes(t));r.arrayEach(e,t=>{r.pluck(s.value,"id").includes(t.id)||(s.value=r.union(s.value,[t]))}),r.arrayEach(n,t=>{s.value=r.remove(s.value,l=>l.id!==t.id)})},h=()=>{const e=a.getBaseTableRef(),o=a.getTableData(),n=r.filter(o,t=>r.pluck(s.value,"id").includes(t.id));g(()=>{r.arrayEach(n,t=>{e.toggleRowSelection(t,!0)})})};return{selectedRows:s,crudOptions:{request:{pageRequest:c,addRequest:m,editRequest:d,delRequest:f},actionbar:{show:!1,buttons:{add:{show:!1}}},rowHandle:{show:!1,fixed:"left",width:150,buttons:{view:{show:!1},edit:{show:!1},remove:{show:!1}}},table:{rowKey:"id",onSelectionChange:p,onRefreshed:()=>h()},columns:{$checked:{title:"选择",form:{show:!1},column:{show:!0,type:"selection",align:"center",width:"55px",columnSetDisabled:!0}},_index:{title:"序号",form:{show:!1},column:{align:"center",width:"70px",columnSetDisabled:!0,formatter:e=>{let o=e.index??1,n=a.crudBinding.value.pagination;return((n.currentPage??1)-1)*n.pageSize+o+1}}},name:{title:"用户名",search:{show:!0,component:{props:{clearable:!0}}},type:"text",form:{show:!1}},dept:{title:"部门",show:!0,type:"dict-tree",column:{name:"text",formatter({value:e,row:o,index:n}){return o.dept__name}},search:{show:!0,disabled:!0,col:{span:6},component:{multiple:!1,props:{checkStrictly:!0,clearable:!0,filterable:!0}}},form:{show:!1},dict:w({isTree:!0,url:"/api/system/dept/all_dept/",value:"id",label:"name"})}}}}},S=Object.freeze(Object.defineProperty({__proto__:null,createCrudOptions:v},Symbol.toStringTag,{value:"Module"}));export{x as a,S as b,v as c};
