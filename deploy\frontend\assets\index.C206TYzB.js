const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.ivKRa8yL.js","assets/index.BHZI5pdK.js","assets/vue.BNx9QYep.js","assets/index.Dg-OhEXY.css","assets/crud.ChqF5Hog.js","assets/api.CkJHuGUE.js","assets/index.DfXQLLqY.js","assets/_plugin-vue_export-helper.DlAUqK2U.js","assets/index.C-cAzbwf.css"])))=>i.map(i=>d[i]);
import{ac as te,ad as ne,a3 as N,b as le,_ as ae,s as U}from"./index.BHZI5pdK.js";import{createCrudOptions as se}from"./crud.BkbaeLOc.js";import{h as F}from"./result.C72K_tqh.js";import re from"./index.BwIuo1jW.js";import{J as de}from"./index.DfXQLLqY.js";import{UpdateTask as ie,RunTask as ue}from"./api.bW-LTLO_.js";import{d as _e,h as C,j as ce,k as a,b as u,o as i,w as o,l as t,x as me,u as d,m as f,a as b,F as pe,p as fe,e as n,s as _,q as k,P as ke,t as V}from"./vue.BNx9QYep.js";import{_ as ve}from"./_plugin-vue_export-helper.DlAUqK2U.js";import"./authFunction.D3Be3hRy.js";import"./min.vue_vue_type_script_setup_true_lang.BB0XUCge.js";import"./hour.vue_vue_type_script_setup_true_lang.DaLalTYu.js";import"./day.vue_vue_type_script_setup_true_lang.BBzKVgRu.js";import"./month.vue_vue_type_script_setup_true_lang.BwG6FKWX.js";import"./week.vue_vue_type_script_setup_true_lang.D1XoAIP2.js";import"./normal.vue_vue_type_style_index_0_lang.CXBdsnF8.js";const ge={key:0},we={style:{height:"260px"}},ye={key:0,style:{width:"100%"}},Ce={class:"bottom w-full"},xe={class:"state flex flex-wrap items-center"},he={class:"ml-2"},be={class:"taskName"},Ve={style:{height:"600px",position:"relative"}},Re=_e({__name:"index",setup(Te){const L=ke(()=>ae(()=>import("./index.ivKRa8yL.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8]))),{crudRef:R,crudBinding:c,crudExpose:v,context:M}=te(),m=C(!1),x=C({cron:""}),g=C(!1);let T=C({});const P=l=>{T=V(l),g.value=!0},D=l=>{l.enabled=!l.enabled,ie({enabled:l.enabled,id:l.id}).then(e=>{if(e.code===2e3)return U(e.msg)})};function S(l){m.value=!1,x.value.cron=l}ce(async()=>{await ne({crudBinding:c,crudRef:R,crudExpose:v,context:M,createCrudOptions:se}),await v.doRefresh()});function q(l){v.openEdit({row:V(l)})}function A(l){v.doRemove({row:V(l)})}function J(l){x.value=l,m.value=!0}const O=l=>{ue(l).then(e=>{if(e.code===2e3)return U(e.msg)})};return(l,e)=>{const w=a("el-button"),j=a("el-form-item"),G=a("el-empty"),E=a("el-tag"),$=a("el-popconfirm"),H=a("CaretRight"),h=a("el-icon"),z=a("el-dropdown-item"),K=a("el-dropdown-menu"),Q=a("el-dropdown"),W=a("Monitor"),X=a("el-card"),Y=a("el-col"),Z=a("el-row"),ee=a("fs-crud"),B=a("el-dialog"),oe=a("fs-page");return i(),u(oe,null,{default:o(()=>[t(ee,me({ref_key:"crudRef",ref:R},d(c)),{form_cron:o(s=>[t(j,{style:{"margin-bottom":"20px"},rules:{required:!0,message:"请输入",trigger:"blur"}},{default:o(()=>[n("div",null,[n("div",null,[t(w,{type:"primary",onClick:p=>J(s.form),size:"small"},{default:o(()=>e[2]||(e[2]=[k(" Cron表达式设置 ")])),_:2,__:[2]},1032,["onClick"])]),s.form.cron?(i(),b("span",ge,[n("div",null,_(s.form.cron),1),n("div",null,_(s.form.cron?d(F)(s.form.cron):""),1)])):f("",!0)])]),_:2},1024)]),form_kwargs:o(s=>[n("div",we,[t(d(de),{class:"editor",style:{height:"250px"},modelValue:s.form.kwargs,"onUpdate:modelValue":p=>s.form.kwargs=p},null,8,["modelValue","onUpdate:modelValue"])])]),default:o(()=>{var s;return[(s=d(c))!=null&&s.data?(i(),u(Z,{key:0,gutter:15,style:{height:"100%",width:"100%",overflow:"auto"}},{default:o(()=>{var p,I;return[((p=d(c))==null?void 0:p.data.length)===0?(i(),b("span",ye,[t(G,{description:"暂无数据请添加"})])):f("",!0),(i(!0),b(pe,null,fe((I=d(c))==null?void 0:I.data,(r,Ee)=>(i(),u(Y,{key:r.id,xl:4,lg:6,md:8,sm:12,xs:24,span:6,style:{"margin-bottom":"10px"}},{default:o(()=>[t(X,{class:"task task-item",shadow:"hover"},{default:o(()=>[n("h2",null,_(r.name),1),n("ul",null,[n("li",null,[e[3]||(e[3]=n("h4",null,"执行任务",-1)),n("p",null,_(r.task),1)]),n("li",null,[e[4]||(e[4]=n("h4",null,"定时规则",-1)),n("p",null,_(r.cron?d(F)(r.cron):"--"),1)]),n("li",null,[e[5]||(e[5]=n("h4",null,"最后运行时间",-1)),n("p",null,_(r.last_run_at||"--"),1)])]),n("div",Ce,[n("div",xe,[t($,{width:"180","confirm-button-text":"确定",onConfirm:y=>D(r),"cancel-button-text":"取消",title:r.enabled?"确认停用该任务？":"确认启用该任务？"},{reference:o(()=>[r.enabled==!0?(i(),u(E,{key:0,type:"success",effect:"dark"},{default:o(()=>e[6]||(e[6]=[k("已启用")])),_:1,__:[6]})):(i(),u(E,{key:1,type:"danger",effect:"dark"},{default:o(()=>e[7]||(e[7]=[k("已停用")])),_:1,__:[7]}))]),_:2},1032,["onConfirm","title"]),n("div",he,[t($,{width:"180","confirm-button-text":"确定",onConfirm:y=>O(r),"cancel-button-text":"取消",title:"立即运行该任务？"},{reference:o(()=>[t(w,{type:"primary",size:"small",circle:"",plain:""},{default:o(()=>[t(h,null,{default:o(()=>[t(H)]),_:1})]),_:1})]),_:2},1032,["onConfirm"])])]),n("div",be,[t(Q,{trigger:"hover",class:"ml-2"},{dropdown:o(()=>[t(K,null,{default:o(()=>[t(z,{icon:d(N),onClick:y=>q(r)},{default:o(()=>e[8]||(e[8]=[k("编辑")])),_:2,__:[8]},1032,["icon","onClick"]),t(z,{icon:d(le),onClick:y=>A(r),divided:""},{default:o(()=>e[9]||(e[9]=[k("删除")])),_:2,__:[9]},1032,["icon","onClick"])]),_:2},1024)]),default:o(()=>[t(w,{type:"primary",size:"small",circle:"",effect:""},{default:o(()=>[t(h,null,{default:o(()=>[t(d(N))]),_:1})]),_:1})]),_:2},1024),t(w,{type:"primary",size:"small",circle:"",plain:"",onClick:y=>P(r),class:"ml-2"},{default:o(()=>[t(h,null,{default:o(()=>[t(W)]),_:1})]),_:2},1032,["onClick"])])])]),_:2},1024)]),_:2},1024))),128))]}),_:1})):f("",!0)]}),_:1},16),t(B,{modelValue:m.value,"onUpdate:modelValue":e[0]||(e[0]=s=>m.value=s),title:"Cron表达式选择器",width:"800"},{default:o(()=>[m.value?(i(),u(re,{key:0,onFill:S,expression:x.value.cron},null,8,["expression"])):f("",!0)]),_:1},8,["modelValue"]),t(B,{modelValue:g.value,"onUpdate:modelValue":e[1]||(e[1]=s=>g.value=s),title:"任务运行日志",width:"1200",class:"rounded-lg"},{default:o(()=>[n("div",Ve,[g.value?(i(),u(d(L),{key:0,taskItem:d(T)},null,8,["taskItem"])):f("",!0)])]),_:1},8,["modelValue"])]),_:1})}}}),Oe=ve(Re,[["__scopeId","data-v-630ec0ff"]]);export{Oe as default};
