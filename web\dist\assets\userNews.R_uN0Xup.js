import{d as i,g as p,R as g,j as h,k as b,a as n,o as t,e,m as v,s as a,b as w,F as k,p as y}from"./vue.BNx9QYep.js";import{r as f}from"./index.BHZI5pdK.js";import{_ as L}from"./_plugin-vue_export-helper.DlAUqK2U.js";const x={class:"layout-navbars-breadcrumb-user-news"},C={class:"head-box"},B={class:"head-box-title"},M={class:"content-box"},N={class:"content-box-msg"},T=["innerHTML"],G={class:"content-box-time"},$=i({name:"layoutBreadcrumbUserNews"}),D=i({...$,setup(F){const o=p({newsList:[]}),d=g(),l=()=>{d.push("/messageCenter")},m=()=>{f({url:"/api/system/message_center/get_newest_msg/",method:"get",params:{}}).then(s=>{const{data:c}=s;o.newsList=[c]})};return h(()=>{m()}),(s,c)=>{const _=b("el-empty");return t(),n("div",x,[e("div",C,[e("div",B,a(s.$t("message.user.newTitle")),1)]),e("div",M,[o.newsList.length>0?(t(!0),n(k,{key:0},y(o.newsList,(r,u)=>(t(),n("div",{class:"content-box-item",key:u},[e("div",null,a(r.title),1),e("div",N,[e("div",{innerHTML:r.content},null,8,T)]),e("div",G,a(r.create_datetime),1)]))),128)):(t(),w(_,{key:1,description:s.$t("message.user.newDesc")},null,8,["description"]))]),o.newsList.length>0?(t(),n("div",{key:0,class:"foot-box",onClick:l},a(s.$t("message.user.newGo")),1)):v("",!0)])}}}),j=L(D,[["__scopeId","data-v-eeb5a33b"]]);export{j as default};
