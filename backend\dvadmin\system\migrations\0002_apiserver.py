# Generated by Django 4.2.14 on 2025-05-21 08:25

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('system', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ApiServer',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('name', models.CharField(help_text='服务器名称', max_length=100, verbose_name='服务器名称')),
                ('url', models.CharField(help_text='服务器地址，例如：http://***********:8000', max_length=255, verbose_name='服务器地址')),
                ('description', models.TextField(blank=True, help_text='描述', null=True, verbose_name='描述')),
                ('is_default', models.BooleanField(default=False, help_text='是否默认', verbose_name='是否默认')),
                ('is_active', models.BooleanField(default=True, help_text='是否启用', verbose_name='是否启用')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': 'API服务器配置',
                'verbose_name_plural': 'API服务器配置',
                'db_table': 'dvadmin_api_server',
                'ordering': ('-create_datetime',),
            },
        ),
    ]
