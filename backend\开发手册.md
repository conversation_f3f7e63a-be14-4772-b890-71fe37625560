# Django-Vue3-Admin 新界面添加指南

## 一、后端开发流程

### 1. 创建新的Django应用
```bash
# 在backend目录下执行
python manage.py startapp 你的应用名称
```

### 2. 注册应用到settings.py
在`backend/application/settings.py`的`INSTALLED_APPS`列表中添加你的应用名称：
```python
INSTALLED_APPS = [
    # 其他已有应用...
    "你的应用名称",
]
```

### 3. 编写模型(models.py)
在你的应用目录下编辑`models.py`文件：
```python
from django.db import models
from dvadmin.utils.models import CoreModel

class 你的模型名(CoreModel):
    """
    说明: 这里继承了CoreModel，会自动包含id、创建时间、更新时间等基础字段
    """
    # 根据需要定义字段
    字段1 = models.CharField(max_length=255, verbose_name="字段1说明")
    字段2 = models.IntegerField(verbose_name="字段2说明")
    字段3 = models.FloatField(verbose_name="字段3说明")
    字段4 = models.DateField(verbose_name="字段4说明")

    # 可以根据需要添加更多字段

    class Meta:
        db_table = "你的数据表名"  # 数据库表名
        verbose_name = '表显示名称'  # 在admin中显示的名称
        verbose_name_plural = verbose_name
        ordering = ('-create_datetime',)  # 默认排序字段
```

### 4. 创建视图集和序列化器(views.py)
在你的应用目录下创建或编辑`views.py`文件：
```python
from 你的应用名称.models import 你的模型名
from dvadmin.utils.serializers import CustomModelSerializer
from dvadmin.utils.viewset import CustomModelViewSet

# 序列化器 - 用于查询操作
class 你的模型名Serializer(CustomModelSerializer):
    """
    序列化器
    """
    class Meta:
        model = 你的模型名
        fields = "__all__"  # 可以指定具体字段，如 ['id', '字段1', '字段2']

# 序列化器 - 用于创建和更新操作
class 你的模型名CreateUpdateSerializer(CustomModelSerializer):
    """
    创建/更新时的序列化器
    """
    class Meta:
        model = 你的模型名
        fields = '__all__'

# 视图集 - 处理CRUD操作
class 你的模型名ViewSet(CustomModelViewSet):
    """
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    """
    queryset = 你的模型名.objects.all()
    serializer_class = 你的模型名Serializer  # 查询时使用的序列化器
    create_serializer_class = 你的模型名CreateUpdateSerializer  # 创建时使用的序列化器
    update_serializer_class = 你的模型名CreateUpdateSerializer  # 更新时使用的序列化器

    # 可过滤的字段
    filter_fields = ['字段1', '字段2']
    # 可搜索的字段
    search_fields = ['字段1']
```

### 5. 配置URL路由
在你的应用目录下创建`urls.py`文件：
```python
from rest_framework.routers import SimpleRouter
from .views import 你的模型名ViewSet

# 创建路由器
router = SimpleRouter()
# 注册视图集，第一个参数是API路径前缀
router.register("api/你的路由路径", 你的模型名ViewSet)

# 配置URL模式
urlpatterns = [
    # 可以添加其他自定义URL
]
# 将路由器中的URL添加到urlpatterns
urlpatterns += router.urls
```

### 6. 将应用路由挂载到主路由
在`backend/application/urls.py`的`urlpatterns`列表中添加：
```python
path('', include('你的应用名称.urls')),
```

### 7. 数据库迁移
```bash
# 生成迁移文件
python manage.py makemigrations

# 应用迁移
python manage.py migrate
```

### 8. 验证API
启动服务器后，访问`http://127.0.0.1:8000`查看Swagger文档，确认API是否正常工作。

## 二、前端开发流程

### 1. 创建页面目录和文件
在前端项目目录下创建新的页面目录和必要文件：
```bash
# 在web/src/views/目录下创建新的文件夹
mkdir -p web/src/views/你的页面目录名

# 创建必要的三个文件
touch web/src/views/你的页面目录名/api.js
touch web/src/views/你的页面目录名/crud.js
touch web/src/views/你的页面目录名/index.vue
```

目录结构应如下所示：
```
web/src/views/
  |--你的页面目录名/
    |--api.js    # 定义API接口
    |--crud.js   # 配置CRUD界面
    |--index.vue # Vue组件
```

### 2. 编写api.js
这个文件用于定义与后端API的交互函数：
```javascript
import { request } from '@/api/service'

// API路径前缀，需要与后端路由一致
export const urlPrefix = '/api/你的路由路径/'

// 获取列表数据
export function GetList(query) {
  return request({
    url: urlPrefix,
    method: 'get',
    params: query
  })
}

// 添加数据
export function AddObj(obj) {
  return request({
    url: urlPrefix,
    method: 'post',
    data: obj
  })
}

// 更新数据
export function UpdateObj(obj) {
  return request({
    url: urlPrefix + obj.id + '/',
    method: 'put',
    data: obj
  })
}

// 删除数据
export function DelObj(id) {
  return request({
    url: urlPrefix + id + '/',
    method: 'delete',
    data: { id }
  })
}
```

### 3. 编写index.vue
这个文件是Vue组件，定义页面的主体结构：
```vue
<template>
  <d2-container :class="{ 'page-compact': crud.pageOptions.compact }">
    <template slot="header">你的页面标题</template>
    <d2-crud-x ref="d2Crud" v-bind="_crudProps" v-on="_crudListeners">
      <!-- 自动绑定参数与事件 -->
      <div slot="header">
        <crud-search
          ref="search"
          :options="crud.searchOptions"
          @submit="handleSearch"
        />
        <el-button-group>
          <el-button size="small" type="primary" @click="addRow"
            ><i class="el-icon-plus" /> 新增</el-button
          >
        </el-button-group>
        <crud-toolbar v-bind="_crudToolbarProps" v-on="_crudToolbarListeners" />
      </div>
    </d2-crud-x>
  </d2-container>
</template>

<script>
import { crudOptions } from './crud' // 引入crud配置
import { d2CrudPlus } from 'd2-crud-plus'
import { AddObj, GetList, UpdateObj, DelObj } from './api' // 引入API函数

export default {
  name: '你的组件名',
  mixins: [d2CrudPlus.crud], // 继承d2CrudPlus.crud
  methods: {
    // 获取CRUD配置
    getCrudOptions () {
      return crudOptions(this)
    },
    // 数据请求方法
    pageRequest (query) {
      return GetList(query)
    },
    // 添加请求方法
    addRequest (row) {
      return AddObj(row)
    },
    // 更新请求方法
    updateRequest (row) {
      return UpdateObj(row)
    },
    // 删除请求方法
    delRequest (row) {
      return DelObj(row.id)
    }
  }
}
</script>
```

### 4. 编写crud.js
这个文件用于配置CRUD界面的表格、表单等：
```javascript
import { request } from "@/api/service";
import { BUTTON_STATUS_NUMBER } from "@/config/button";
import { urlPrefix as bookPrefix } from "./api";

export const crudOptions = vm => {
    return {
        // 页面配置选项
        pageOptions: {
            compact: true // 是否紧凑模式
        },
        // 表格配置选项
        options: {
            tableType: "vxe-table", // 表格类型
            rowKey: true, // 必须设置，true or false
            rowId: "id", // 行数据的唯一标识字段
            height: "100%", // 表格高度，使用toolbar必须设置
            highlightCurrentRow: false // 是否高亮当前行
        },
        // 行操作按钮配置
        rowHandle: {
            width: 140, // 操作列宽度
            // 查看按钮
            view: {
                thin: true, // 是否使用细按钮
                text: "", // 按钮文本
                disabled() {
                    // 根据权限控制按钮是否禁用
                    return !vm.hasPermissions("Retrieve");
                }
            },
            // 编辑按钮
            edit: {
                thin: true,
                text: "",
                disabled() {
                    return !vm.hasPermissions("Update");
                }
            },
            // 删除按钮
            remove: {
                thin: true,
                text: "",
                disabled() {
                    return !vm.hasPermissions("Delete");
                }
            }
        },
        // 序号列配置
        indexRow: {
            title: "序号",
            align: "center",
            width: 100
        },
        // 查看表单配置
        viewOptions: {
            componentType: "form"
        },
        // 表单配置
        formOptions: {
            defaultSpan: 24, // 默认的表单项宽度
            width: "35%" // 表单弹窗宽度
        },
        // 表格列配置
        columns: [
            // ID列
            {
                title: "ID", // 列标题
                key: "id", // 字段名
                show: false, // 是否显示
                disabled: true, // 是否禁用
                width: 90, // 列宽
                form: {
                    disabled: true // 表单中禁用
                }
            },
            // 字段1列配置示例
            {
                title: "字段1名称",
                key: "字段1", // 与后端模型字段对应
                sortable: true, // 是否可排序
                type: "input", // 字段类型
                form: {
                    editDisabled: true, // 编辑时是否禁用
                    rules: [
                        // 表单校验规则
                        { required: true, message: "字段1必填" }
                    ],
                    component: {
                        props: {
                            clearable: true // 是否可清空
                        },
                        placeholder: "请输入字段1"
                    }
                }
            },
            // 字段2列配置示例
            {
                title: "字段2名称",
                key: "字段2",
                sortable: true,
                type: "number", // 数字类型
                form: {
                    rules: [
                        { required: true, message: "字段2必填" }
                    ],
                    component: {
                        props: {
                            clearable: true
                        },
                        placeholder: "请输入字段2"
                    }
                }
            },
            // 字段3列配置示例
            {
                title: "字段3名称",
                key: "字段3",
                sortable: true,
                type: "number",
                // 搜索配置
                search: {
                    component: {
                        props: {
                            clearable: true
                        }
                    }
                },
                form: {
                    rules: [
                        { required: true, message: "字段3必填" }
                    ],
                    component: {
                        props: {
                            clearable: true
                        },
                        placeholder: "请输入字段3"
                    }
                }
            },
            // 日期字段配置示例
            {
                title: "字段4名称",
                key: "字段4",
                sortable: true,
                type: "date", // 日期类型
                form: {
                    rules: [
                        { required: true, message: "字段4必填" }
                    ],
                    component: {
                        props: {
                            clearable: true,
                            format: 'yyyy-MM-dd', // 日期格式
                            valueFormat: 'yyyy-MM-dd' // 值格式
                        },
                        placeholder: "请选择日期"
                    }
                }
            }
            // 可以根据需要添加更多字段
        ].concat(vm.commonEndColumns()) // 添加公共结尾列
    };
};
```

### 5. 添加菜单配置
完成前端文件编写后，需要在系统的菜单管理中添加新页面：

1. 登录系统后，进入"系统管理" -> "菜单管理"
2. 点击"新增"按钮，填写菜单信息：
   - 菜单标题：填写你的页面名称
   - 路由名称：与前端组件名一致
   - 路由地址：填写前端目录名，如`/你的页面目录名`
   - 组件地址：填写前端组件路径，如`你的页面目录名/index`
   - 菜单图标：选择合适的图标
   - 排序：设置菜单显示顺序
   - 是否显示：选择"是"
   - 是否缓存：根据需要选择
   - 上级菜单：选择合适的上级菜单

3. 保存菜单配置后，刷新页面即可在左侧菜单栏看到新添加的菜单

### 6. 测试页面功能
完成上述步骤后，点击新添加的菜单，测试页面的各项功能：
- 列表数据显示
- 搜索功能
- 新增数据
- 编辑数据
- 删除数据

## 三、开发注意事项

### 1. 命名规范
- 后端应用名、模型名使用驼峰命名法，如`SalesManagement`
- 数据库表名使用小写下划线命名法，如`sales_management`
- 前端目录名使用小写下划线或短横线命名法，如`sales_management`或`sales-management`
- API路径使用小写下划线命名法，如`api/sales_management`

### 2. 权限控制
- 在视图集中可以通过`permission_classes`设置权限
- 在前端可以通过`hasPermissions`方法控制按钮的显示与禁用

### 3. 字段类型对应
后端字段类型与前端表单类型对应关系：
- CharField -> input
- IntegerField -> number
- FloatField -> number
- DateField -> date
- DateTimeField -> datetime
- BooleanField -> switch
- ForeignKey -> select
- ManyToManyField -> select (multiple)

### 4. 常见问题排查
- API接口404：检查URL路由配置是否正确
- 数据无法保存：检查序列化器字段定义是否完整
- 前端页面无法访问：检查菜单配置和路由名称是否一致
- 按钮权限问题：检查角色权限配置

## 四、完整示例
参考本文档中的示例代码，可以快速创建一个完整的CRUD页面。根据实际需求，可以对模型字段、表单配置、API接口等进行调整和扩展。

祝您开发顺利！





# Django-Vue3-Admin 数据传输问题修复指南

## 问题描述

在Django+Vue3+SQLServer的项目中，前端无法正确显示后端返回的数据。具体表现为：

- 后端API成功返回数据（状态码200）
- 日志显示SQL Server查询到记录
- 前端页面提示"获取部门数据失败"
- 前端控制台显示code=500而不是预期的code=0

## 诊断过程

1. **观察后端日志**：
   ```
   ===== API响应JSON内容 =====
   {
     "code": 0,
     "msg": "获取部门列表成功",
     "data": {
       "records": [{"id": 1, "name": "111"}],
       "total": 1,
       "currentPage": 1,
       "pageSize": 20
     }
   }
   ===== API响应JSON结束 =====
   ```

2. **观察前端控制台**：
   ```
   == transformRes开始分析响应 ==
   1. 原始响应类型: object
   2. 响应是否为null或undefined: 有值
   3. 响应对象结构: ['code', 'msg', 'data']
   4. 响应code值: 500 类型: number
   ...
   == 分析结束 ==
   
   code!==0或data不存在, code值: 500 类型: number
   请求返回错误: 获取部门列表失败
   ```

3. **关键发现**：
   - 后端返回`code: 0`作为成功状态码
   - 前端收到的却是`code: 500`
   - 响应内容被篡改，可能是请求拦截器在处理

## 问题根源

经检查`web/src/utils/service.ts`文件中的响应拦截器，发现问题：

```javascript
// 根据 code 进行判断
if (code === undefined) {
    // ...
} else {
    // 有 code 代表这是一个后端接口 可以进行进一步的判断
    switch (code) {
        case 400:
            // ...
        case 401:
            // ...
        case 2000:  // <-- 只接受2000作为成功码
            // @ts-ignore
            if (response.config.unpack === false) {
                //如果不需要解包
                return dataAxios;
            }
            return dataAxios;
        default:
            // 不是正确的 code
            errorCreate(`${dataAxios.msg}: ${response.config.url}`);
            break;
    }
    return Promise.reject(dataAxios);
}
```

**问题原因**：前端拦截器只接受`code: 2000`作为成功状态码，而我们自己添加的hl_web应用返回的是`code: 0`，导致前端将成功响应当作错误处理。

## 解决方案

修改前端响应拦截器，增加对`code: 0`的处理：

```javascript
switch (code) {
    case 400:
        // ...
    case 401:
        // ...
    case 0:  // 添加对code=0的处理，匹配hl_web返回的格式
        // @ts-ignore
        if (response.config.unpack === false) {
            //如果不需要解包
            return dataAxios;
        }
        return dataAxios;
    case 2000:
        // ...
}
```

## 操作步骤

1. 定位问题：
   - 检查后端API响应格式和前端收到的格式是否一致
   - 查看前端请求拦截器代码，特别是状态码处理部分

2. 修改前端代码：
   - 打开`web/src/utils/service.ts`文件
   - 在响应拦截器的switch语句中添加对`code: 0`的处理
   - 保存文件并重启前端服务

3. 验证修复：
   - 重新访问页面，检查是否正确显示数据
   - 查看控制台输出，确认前端正确处理了响应

## 预防措施

1. **统一API响应格式**：在项目中统一前后端API约定，特别是成功/失败状态码

2. **添加详细日志**：
   - 后端记录完整的响应内容
   - 前端记录收到的原始响应和处理后的状态

3. **文档化API约定**：记录项目中使用的状态码约定和响应格式，便于新开发者理解

## 其他可能的解决方案

1. 修改后端返回码：将hl_web应用的返回码从`code: 0`改为`code: 2000`以匹配项目约定

2. 自定义API调用：对特定API使用不经过拦截器的请求方法

3. 添加特殊标记：在API请求中添加标记，让拦截器对特定请求使用不同的处理逻辑

## 总结

这个问题是由于前后端接口约定不一致导致的。前端框架期望`code: 2000`作为成功状态码，而我们新添加的后端API使用了`code: 0`，使得前端将成功响应当作错误处理。

通过修改前端响应拦截器，使其同时接受`code: 0`和`code: 2000`作为成功状态码，成功解决了问题。

这种问题提醒我们在扩展现有系统时，需要仔细了解和遵循项目中已有的约定和标准。 