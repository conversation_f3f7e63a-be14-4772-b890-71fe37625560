import{r as a,v as n,A as d,s as c}from"./index.BHZI5pdK.js";import{d as m}from"./dictionary.DNsEqk19.js";import{a as o}from"./authFunction.D3Be3hRy.js";import{c as h,I as p}from"./vue.BNx9QYep.js";const i="/api/system/role/";function w(t){return a({url:i,method:"get",params:t})}function f(t){return a({url:i,method:"post",data:t})}function l(t){return a({url:i+t.id+"/",method:"put",data:t})}function y(t){return a({url:i+t+"/",method:"delete",data:{id:t}})}const O=function({crudExpose:t,context:s}){return{crudOptions:{request:{pageRequest:async e=>await w(e),addRequest:async({form:e})=>await f(e),editRequest:async({form:e,row:r})=>(e.id=r.id,await l(e)),delRequest:async({row:e})=>await y(e.id)},pagination:{show:!0},actionbar:{buttons:{add:{show:o("role:Create")}}},rowHandle:{fixed:"right",width:h(()=>o("role:AuthorizedAdd")||o("role:AuthorizedSearch")?420:320),buttons:{view:{show:!0},edit:{show:o("role:Update")},remove:{show:o("role:Delete")},assignment:{type:"primary",text:"授权用户",show:o("role:AuthorizedAdd")||o("role:AuthorizedSearch"),click:e=>{const{row:r}=e;s.RoleUserDrawer.handleDrawerOpen(r),p(()=>{s.RoleUserRef.value.setSearchFormData({form:{role_id:r.id}}),s.RoleUserRef.value.doRefresh()})}},permission:{type:"primary",text:"权限配置",show:o("role:Permission"),click:e=>{const{row:r}=e;s.RoleDrawer.handleDrawerOpen(r),s.RoleMenuBtn.setState([]),s.RoleMenuField.setState([])}}}},form:{col:{span:24},labelWidth:"100px",wrapper:{is:"el-dialog",width:"600px"}},columns:{_index:{title:"序号",form:{show:!1},column:{type:"index",align:"center",width:"70px",columnSetDisabled:!0}},id:{title:"ID",column:{show:!1},search:{show:!1},form:{show:!1}},name:{title:"角色名称",search:{show:!0},column:{minWidth:120,sortable:"custom"},form:{rules:[{required:!0,message:"角色名称必填"}],component:{placeholder:"请输入角色名称"}}},key:{title:"权限标识",search:{show:!1},column:{minWidth:120,sortable:"custom",columnSetDisabled:!0},form:{rules:[{required:!0,message:"权限标识必填"}],component:{placeholder:"输入权限标识"}},valueBuilder(e){const{row:r,key:u}=e;return r[u]}},sort:{title:"排序",search:{show:!1},type:"number",column:{minWidth:90,sortable:"custom"},form:{rules:[{required:!0,message:"排序必填"}],value:1}},status:{title:"状态",search:{show:!0},type:"dict-radio",column:{width:100,component:{name:"fs-dict-switch",activeText:"",inactiveText:"",style:"--el-switch-on-color: var(--el-color-primary); --el-switch-off-color: #dcdfe6",onChange:d(e=>()=>{l(e.row).then(r=>{c(r.msg)})})}},dict:n({value:m("button_status_bool")})}}}}};export{O as createCrudOptions};
