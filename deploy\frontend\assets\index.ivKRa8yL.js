import{a as $}from"./index.BHZI5pdK.js";import{createCrudOptions as I}from"./crud.ChqF5Hog.js";import{J as k}from"./index.DfXQLLqY.js";import{d as h,h as n,j as R,k as l,b as c,o as p,w as t,l as s,x as E,u as m,e as v,m as g,q as V}from"./vue.BNx9QYep.js";import"./api.CkJHuGUE.js";import"./_plugin-vue_export-helper.DlAUqK2U.js";const H={style:{height:"260px"}},J={style:{height:"260px"}},K=h({name:"taskLog"}),P=h({...K,props:{taskItem:{}},setup(w){const a=n({}),r=n(!1),u=n({}),i=n(!1),x=w,{crudBinding:y,crudRef:C,crudExpose:b}=$({createCrudOptions:I,context:{taskItem:x.taskItem}});return R(()=>{b.doRefresh()}),(S,e)=>{const f=l("el-button"),_=l("el-popover"),B=l("fs-crud"),N=l("fs-page");return p(),c(N,null,{default:t(()=>[s(B,E({ref_key:"crudRef",ref:C},m(y)),{cell_task_kwargs:t(d=>[s(_,{placement:"bottom",width:600,trigger:"click",content:"this is content, this is content, this is content",onHide:e[1]||(e[1]=o=>r.value=!1)},{reference:t(()=>[s(f,{class:"m-2",type:"primary",onClick:o=>{a.value=d.row.task_kwargs,r.value=!0}},{default:t(()=>e[4]||(e[4]=[V("查看")])),_:2,__:[4]},1032,["onClick"])]),default:t(()=>[v("div",H,[r.value?(p(),c(m(k),{key:0,class:"editor",style:{height:"250px"},modelValue:a.value,"onUpdate:modelValue":e[0]||(e[0]=o=>a.value=o)},null,8,["modelValue"])):g("",!0)])]),_:2},1024)]),cell_result:t(d=>[s(_,{placement:"bottom",width:600,trigger:"click",content:"this is content, this is content, this is content",onHide:e[3]||(e[3]=o=>i.value=!1)},{reference:t(()=>[s(f,{class:"m-2",type:"success",onClick:o=>{u.value=d.row.result,i.value=!0}},{default:t(()=>e[5]||(e[5]=[V("查看")])),_:2,__:[5]},1032,["onClick"])]),default:t(()=>[v("div",J,[i.value?(p(),c(m(k),{key:0,class:"editor",style:{height:"250px"},modelValue:u.value,"onUpdate:modelValue":e[2]||(e[2]=o=>u.value=o)},null,8,["modelValue"])):g("",!0)])]),_:2},1024)]),_:1},16)]),_:1})}}});export{P as default};
