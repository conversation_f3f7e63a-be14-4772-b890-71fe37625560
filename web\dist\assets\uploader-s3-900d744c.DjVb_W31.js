import{C as Ja,m as ec,f as tc,i as rc,h as nc}from"./index.BHZI5pdK.js";import"./vue.BNx9QYep.js";var Zn={},sc=Object.defineProperty,ic=(e,t,r)=>t in e?sc(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,N=(e,t,r)=>(ic(e,typeof t!="symbol"?t+"":t,r),r);const oc=e=>{let t=e.httpHandler;return{setHttpHandler(r){t=r},httpHandler(){return t},updateHttpClientConfig(r,n){t.updateHttpClientConfig(r,n)},httpHandlerConfigs(){return t.httpHandlerConfigs()}}},ac=e=>({httpHandler:e.httpHandler()});var Xn;(function(e){e.HEADER="header",e.QUERY="query"})(Xn||(Xn={}));var Qn;(function(e){e.HEADER="header",e.QUERY="query"})(Qn||(Qn={}));var Ct;(function(e){e.HTTP="http",e.HTTPS="https"})(Ct||(Ct={}));var Wt;(function(e){e.MD5="md5",e.CRC32="crc32",e.CRC32C="crc32c",e.SHA1="sha1",e.SHA256="sha256"})(Wt||(Wt={}));var Yn;(function(e){e[e.HEADER=0]="HEADER",e[e.TRAILER=1]="TRAILER"})(Yn||(Yn={}));const Jr="__smithy_context";var Jn;(function(e){e.PROFILE="profile",e.SSO_SESSION="sso-session",e.SERVICES="services"})(Jn||(Jn={}));var es;(function(e){e.HTTP_0_9="http/0.9",e.HTTP_1_0="http/1.0",e.TDS_8_0="tds/8.0"})(es||(es={}));class ee{constructor(t){this.method=t.method||"GET",this.hostname=t.hostname||"localhost",this.port=t.port,this.query=t.query||{},this.headers=t.headers||{},this.body=t.body,this.protocol=t.protocol?t.protocol.slice(-1)!==":"?`${t.protocol}:`:t.protocol:"https:",this.path=t.path?t.path.charAt(0)!=="/"?`/${t.path}`:t.path:"/",this.username=t.username,this.password=t.password,this.fragment=t.fragment}static clone(t){const r=new ee({...t,headers:{...t.headers}});return r.query&&(r.query=cc(r.query)),r}static isInstance(t){if(!t)return!1;const r=t;return"method"in r&&"protocol"in r&&"hostname"in r&&"path"in r&&typeof r.query=="object"&&typeof r.headers=="object"}clone(){return ee.clone(this)}}function cc(e){return Object.keys(e).reduce((t,r)=>{const n=e[r];return{...t,[r]:Array.isArray(n)?[...n]:n}},{})}class Pt{constructor(t){this.statusCode=t.statusCode,this.reason=t.reason,this.headers=t.headers||{},this.body=t.body}static isInstance(t){if(!t)return!1;const r=t;return typeof r.statusCode=="number"&&typeof r.headers=="object"}}function uc(e){return t=>async r=>{var n,s;const{request:i}=r;return ee.isInstance(i)&&i.body&&e.runtime==="node"&&((s=(n=e.requestHandler)==null?void 0:n.constructor)==null?void 0:s.name)!=="FetchHttpHandler"&&(i.headers={...i.headers,Expect:"100-continue"}),t({...r,request:i})}}const lc={step:"build",tags:["SET_EXPECT_HEADER","EXPECT_HEADER"],name:"addExpectContinueMiddleware",override:!0},dc=e=>({applyToStack:t=>{t.add(uc(e),lc)}}),ct={WHEN_SUPPORTED:"WHEN_SUPPORTED",WHEN_REQUIRED:"WHEN_REQUIRED"},hc=ct.WHEN_SUPPORTED,pr={WHEN_SUPPORTED:"WHEN_SUPPORTED",WHEN_REQUIRED:"WHEN_REQUIRED"},fc=ct.WHEN_SUPPORTED;var F;(function(e){e.MD5="MD5",e.CRC32="CRC32",e.CRC32C="CRC32C",e.CRC64NVME="CRC64NVME",e.SHA1="SHA1",e.SHA256="SHA256"})(F||(F={}));var ts;(function(e){e.HEADER="header",e.TRAILER="trailer"})(ts||(ts={}));const en=F.CRC32;var rs;(function(e){e.ENV="env",e.CONFIG="shared config entry"})(rs||(rs={}));function pc(e,t,r){return e.$source||(e.$source={}),e.$source[t]=r,e}function Y(e,t,r){e.__aws_sdk_context?e.__aws_sdk_context.features||(e.__aws_sdk_context.features={}):e.__aws_sdk_context={features:{}},e.__aws_sdk_context.features[t]=r}const ns=e=>{var t,r;return Pt.isInstance(e)?((t=e.headers)==null?void 0:t.date)??((r=e.headers)==null?void 0:r.Date):void 0},pn=e=>new Date(Date.now()+e),gc=(e,t)=>Math.abs(pn(t).getTime()-e)>=3e5,ss=(e,t)=>{const r=Date.parse(e);return gc(r,t)?r-Date.now():t},St=(e,t)=>{if(!t)throw new Error(`Property \`${e}\` is not resolved for AWS SDK SigV4Auth`);return t},Ji=async e=>{var t,r,n;const s=St("context",e.context),i=St("config",e.config),o=(n=(r=(t=s.endpointV2)==null?void 0:t.properties)==null?void 0:r.authSchemes)==null?void 0:n[0],a=await St("signer",i.signer)(o),c=e==null?void 0:e.signingRegion,u=e==null?void 0:e.signingRegionSet,h=e==null?void 0:e.signingName;return{config:i,signer:a,signingRegion:c,signingRegionSet:u,signingName:h}};class eo{async sign(t,r,n){var s;if(!ee.isInstance(t))throw new Error("The request is not an instance of `HttpRequest` and cannot be signed");const i=await Ji(n),{config:o,signer:a}=i;let{signingRegion:c,signingName:u}=i;const h=n.context;if(((s=h==null?void 0:h.authSchemes)==null?void 0:s.length)??!1){const[d,p]=h.authSchemes;(d==null?void 0:d.name)==="sigv4a"&&(p==null?void 0:p.name)==="sigv4"&&(c=(p==null?void 0:p.signingRegion)??c,u=(p==null?void 0:p.signingName)??u)}return await a.sign(t,{signingDate:pn(o.systemClockOffset),signingRegion:c,signingService:u})}errorHandler(t){return r=>{const n=r.ServerTime??ns(r.$response);if(n){const s=St("config",t.config),i=s.systemClockOffset;s.systemClockOffset=ss(n,s.systemClockOffset),s.systemClockOffset!==i&&r.$metadata&&(r.$metadata.clockSkewCorrected=!0)}throw r}}successHandler(t,r){const n=ns(t);if(n){const s=St("config",r.config);s.systemClockOffset=ss(n,s.systemClockOffset)}}}class mc extends eo{async sign(t,r,n){var s;if(!ee.isInstance(t))throw new Error("The request is not an instance of `HttpRequest` and cannot be signed");const{config:i,signer:o,signingRegion:a,signingRegionSet:c,signingName:u}=await Ji(n),h=(await((s=i.sigv4aSigningRegionSet)==null?void 0:s.call(i))??c??[a]).join(",");return await o.sign(t,{signingDate:pn(i.systemClockOffset),signingRegion:h,signingService:u})}}const ft=e=>e[Jr]||(e[Jr]={}),Ne=e=>{if(typeof e=="function")return e;const t=Promise.resolve(e);return()=>t};function yc(e){const t=new Map;for(const r of e)t.set(r.schemeId,r);return t}const wc=(e,t)=>(r,n)=>async s=>{var i;const o=e.httpAuthSchemeProvider(await t.httpAuthSchemeParametersProvider(e,n,s.input)),a=yc(e.httpAuthSchemes),c=ft(n),u=[];for(const h of o){const d=a.get(h.schemeId);if(!d){u.push(`HttpAuthScheme \`${h.schemeId}\` was not enabled for this service.`);continue}const p=d.identityProvider(await t.identityProviderConfigProvider(e));if(!p){u.push(`HttpAuthScheme \`${h.schemeId}\` did not have an IdentityProvider configured.`);continue}const{identityProperties:g={},signingProperties:S={}}=((i=h.propertiesExtractor)==null?void 0:i.call(h,e,n))||{};h.identityProperties=Object.assign(h.identityProperties||{},g),h.signingProperties=Object.assign(h.signingProperties||{},S),c.selectedHttpAuthScheme={httpAuthOption:h,identity:await p(h.identityProperties),signer:d.signer};break}if(!c.selectedHttpAuthScheme)throw new Error(u.join(`
`));return r(s)},bc={step:"serialize",tags:["HTTP_AUTH_SCHEME"],name:"httpAuthSchemeMiddleware",override:!0,relation:"before",toMiddleware:"endpointV2Middleware"},vc=(e,{httpAuthSchemeParametersProvider:t,identityProviderConfigProvider:r})=>({applyToStack:n=>{n.addRelativeTo(wc(e,{httpAuthSchemeParametersProvider:t,identityProviderConfigProvider:r}),bc)}}),Sc=(e,t)=>(r,n)=>async s=>{var i,o,a,c;const{response:u}=await r(s);try{const h=await t(u,e);return{response:u,output:h}}catch(h){if(Object.defineProperty(h,"$response",{value:u}),!("$metadata"in h)){const d="Deserialization error: to see the raw response, inspect the hidden field {error}.$response on this object.";try{h.message+=`
  `+d}catch{!n.logger||((o=(i=n.logger)==null?void 0:i.constructor)==null?void 0:o.name)==="NoOpLogger"||(c=(a=n.logger)==null?void 0:a.warn)==null||c.call(a,d)}typeof h.$responseBodyText<"u"&&h.$response&&(h.$response.body=h.$responseBodyText)}throw h}},Ec=(e,t)=>(r,n)=>async s=>{var i;const o=(i=n.endpointV2)!=null&&i.url&&e.urlParser?async()=>e.urlParser(n.endpointV2.url):e.endpoint;if(!o)throw new Error("No valid endpoint provider available.");const a=await t(s.input,{...e,endpoint:o});return r({...s,request:a})},Ac={name:"deserializerMiddleware",step:"deserialize",tags:["DESERIALIZER"],override:!0},gn={name:"serializerMiddleware",step:"serialize",tags:["SERIALIZER"],override:!0};function to(e,t,r){return{applyToStack:n=>{n.add(Sc(e,r),Ac),n.add(Ec(e,t),gn)}}}gn.name;const kc=e=>t=>{throw t},xc=(e,t)=>{},Rc=e=>(t,r)=>async n=>{if(!ee.isInstance(n.request))return t(n);const s=ft(r).selectedHttpAuthScheme;if(!s)throw new Error("No HttpAuthScheme was selected: unable to sign request");const{httpAuthOption:{signingProperties:i={}},identity:o,signer:a}=s,c=await t({...n,request:await a.sign(n.request,o,i)}).catch((a.errorHandler||kc)(i));return(a.successHandler||xc)(c.response,i),c},ro={step:"finalizeRequest",tags:["HTTP_SIGNING"],name:"httpSigningMiddleware",aliases:["apiKeyMiddleware","tokenMiddleware","awsAuthMiddleware"],override:!0,relation:"after",toMiddleware:"retryMiddleware"},Cc=e=>({applyToStack:t=>{t.addRelativeTo(Rc(),ro)}}),st=e=>{if(typeof e=="function")return e;const t=Promise.resolve(e);return()=>t},ze={},pt=new Array(64);for(let e=0,t=65,r=90;e+t<=r;e++){const n=String.fromCharCode(e+t);ze[n]=e,pt[e]=n}for(let e=0,t=97,r=122;e+t<=r;e++){const n=String.fromCharCode(e+t),s=e+26;ze[n]=s,pt[s]=n}for(let e=0;e<10;e++){ze[e.toString(10)]=e+52;const t=e.toString(10),r=e+52;ze[t]=r,pt[r]=t}ze["+"]=62;pt[62]="+";ze["/"]=63;pt[63]="/";const ot=6,Et=8,Pc=63,mn=e=>{let t=e.length/4*3;e.slice(-2)==="=="?t-=2:e.slice(-1)==="="&&t--;const r=new ArrayBuffer(t),n=new DataView(r);for(let s=0;s<e.length;s+=4){let i=0,o=0;for(let u=s,h=s+3;u<=h;u++)if(e[u]!=="="){if(!(e[u]in ze))throw new TypeError(`Invalid character ${e[u]} in base64 string.`);i|=ze[e[u]]<<(h-u)*ot,o+=ot}else i>>=ot;const a=s/4*3;i>>=o%Et;const c=Math.floor(o/Et);for(let u=0;u<c;u++){const h=(c-u-1)*Et;n.setUint8(a+u,(i&255<<h)>>h)}}return new Uint8Array(r)},Je=e=>new TextEncoder().encode(e),at=e=>typeof e=="string"?Je(e):ArrayBuffer.isView(e)?new Uint8Array(e.buffer,e.byteOffset,e.byteLength/Uint8Array.BYTES_PER_ELEMENT):new Uint8Array(e),yn=e=>{if(typeof e=="string")return e;if(typeof e!="object"||typeof e.byteOffset!="number"||typeof e.byteLength!="number")throw new Error("@smithy/util-utf8: toUtf8 encoder function only accepts string | Uint8Array.");return new TextDecoder("utf-8").decode(e)};function tr(e){let t;typeof e=="string"?t=Je(e):t=e;const r=typeof t=="object"&&typeof t.length=="number",n=typeof t=="object"&&typeof t.byteOffset=="number"&&typeof t.byteLength=="number";if(!r&&!n)throw new Error("@smithy/util-base64: toBase64 encoder function only accepts string | Uint8Array.");let s="";for(let i=0;i<t.length;i+=3){let o=0,a=0;for(let u=i,h=Math.min(i+3,t.length);u<h;u++)o|=t[u]<<(h-u-1)*Et,a+=Et;const c=Math.ceil(a/ot);o<<=c*ot-a;for(let u=1;u<=c;u++){const h=(c-u)*ot;s+=pt[(o&Pc<<h)>>h]}s+="==".slice(0,4-c)}return s}function Tc(e,t="utf-8"){return t==="base64"?tr(e):yn(e)}function Ic(e,t){return t==="base64"?Qe.mutate(mn(e)):Qe.mutate(Je(e))}class Qe extends Uint8Array{static fromString(t,r="utf-8"){switch(typeof t){case"string":return Ic(t,r);default:throw new Error(`Unsupported conversion from ${typeof t} to Uint8ArrayBlobAdapter.`)}}static mutate(t){return Object.setPrototypeOf(t,Qe.prototype),t}transformToString(t="utf-8"){return Tc(this,t)}}const Nc=typeof ReadableStream=="function"?ReadableStream:function(){};class Oc extends Nc{}const tn=e=>{var t;return typeof ReadableStream=="function"&&(((t=e==null?void 0:e.constructor)==null?void 0:t.name)===ReadableStream.name||e instanceof ReadableStream)},Mc=({expectedChecksum:e,checksum:t,source:r,checksumSourceLocation:n,base64Encoder:s})=>{var i;if(!tn(r))throw new Error(`@smithy/util-stream: unsupported source type ${((i=r==null?void 0:r.constructor)==null?void 0:i.name)??r} in ChecksumStream.`);const o=s??tr;if(typeof TransformStream!="function")throw new Error("@smithy/util-stream: unable to instantiate ChecksumStream because API unavailable: ReadableStream/TransformStream.");const a=new TransformStream({start(){},async transform(u,h){t.update(u),h.enqueue(u)},async flush(u){const h=await t.digest(),d=o(h);if(e!==d){const p=new Error(`Checksum mismatch: expected "${e}" but received "${d}" in response header "${n}".`);u.error(p)}else u.terminate()}});r.pipeThrough(a);const c=a.readable;return Object.setPrototypeOf(c,Oc.prototype),c};class Uc{constructor(t){this.allocByteArray=t,this.byteLength=0,this.byteArrays=[]}push(t){this.byteArrays.push(t),this.byteLength+=t.byteLength}flush(){if(this.byteArrays.length===1){const n=this.byteArrays[0];return this.reset(),n}const t=this.allocByteArray(this.byteLength);let r=0;for(let n=0;n<this.byteArrays.length;++n){const s=this.byteArrays[n];t.set(s,r),r+=s.byteLength}return this.reset(),t}reset(){this.byteArrays=[],this.byteLength=0}}function $c(e,t,r){const n=e.getReader();let s=!1,i=0;const o=["",new Uc(u=>new Uint8Array(u))];let a=-1;const c=async u=>{const{value:h,done:d}=await n.read(),p=h;if(d){if(a!==-1){const g=gr(o,a);At(g)>0&&u.enqueue(g)}u.close()}else{const g=Bc(p);if(a!==g&&(a>=0&&u.enqueue(gr(o,a)),a=g),a===-1){u.enqueue(p);return}const S=At(p);i+=S;const R=At(o[a]);if(S>=t&&R===0)u.enqueue(p);else{const x=Dc(o,a,p);!s&&i>t*2&&(s=!0,r==null||r.warn(`@smithy/util-stream - stream chunk size ${S} is below threshold of ${t}, automatically buffering.`)),x>=t?u.enqueue(gr(o,a)):await c(u)}}};return new ReadableStream({pull:c})}const _c=$c;function Dc(e,t,r){switch(t){case 0:return e[0]+=r,At(e[0]);case 1:case 2:return e[t].push(r),At(e[t])}}function gr(e,t){switch(t){case 0:const r=e[0];return e[0]="",r;case 1:case 2:return e[t].flush()}throw new Error(`@smithy/util-stream - invalid index ${t} given to flush()`)}function At(e){return(e==null?void 0:e.byteLength)??(e==null?void 0:e.length)??0}function Bc(e){return typeof Buffer<"u"&&e instanceof Buffer?2:e instanceof Uint8Array?1:typeof e=="string"?0:-1}const Lc=(e,t)=>{const{base64Encoder:r,bodyLengthChecker:n,checksumAlgorithmFn:s,checksumLocationName:i,streamHasher:o}=t,a=r!==void 0&&n!==void 0&&s!==void 0&&i!==void 0&&o!==void 0,c=a?o(s,e):void 0,u=e.getReader();return new ReadableStream({async pull(h){const{value:d,done:p}=await u.read();if(p){if(h.enqueue(`0\r
`),a){const g=r(await c);h.enqueue(`${i}:${g}\r
`),h.enqueue(`\r
`)}h.close()}else h.enqueue(`${(n(d)||0).toString(16)}\r
${d}\r
`)}})};async function Fc(e,t){let r=0;const n=[],s=e.getReader();let i=!1;for(;!i;){const{done:c,value:u}=await s.read();if(u&&(n.push(u),r+=(u==null?void 0:u.byteLength)??0),r>=t)break;i=c}s.releaseLock();const o=new Uint8Array(Math.min(t,r));let a=0;for(const c of n){if(c.byteLength>o.byteLength-a){o.set(c.subarray(0,o.byteLength-a),a);break}else o.set(c,a);a+=c.length}return o}const Ye=e=>encodeURIComponent(e).replace(/[!'()*]/g,Hc),Hc=e=>`%${e.charCodeAt(0).toString(16).toUpperCase()}`;function qc(e){const t=[];for(let r of Object.keys(e).sort()){const n=e[r];if(r=Ye(r),Array.isArray(n))for(let s=0,i=n.length;s<i;s++)t.push(`${r}=${Ye(n[s])}`);else{let s=r;(n||typeof n=="string")&&(s+=`=${Ye(n)}`),t.push(s)}}return t.join("&")}function is(e,t){return new Request(e,t)}function zc(e=0){return new Promise((t,r)=>{e&&setTimeout(()=>{const n=new Error(`Request did not complete within ${e} ms`);n.name="TimeoutError",r(n)},e)})}const mr={supported:void 0};class wn{static create(t){return typeof(t==null?void 0:t.handle)=="function"?t:new wn(t)}constructor(t){typeof t=="function"?this.configProvider=t().then(r=>r||{}):(this.config=t??{},this.configProvider=Promise.resolve(this.config)),mr.supported===void 0&&(mr.supported=typeof Request<"u"&&"keepalive"in is("https://[::1]"))}destroy(){}async handle(t,{abortSignal:r}={}){var n;this.config||(this.config=await this.configProvider);const s=this.config.requestTimeout,i=this.config.keepAlive===!0,o=this.config.credentials;if(r!=null&&r.aborted){const M=new Error("Request aborted");return M.name="AbortError",Promise.reject(M)}let a=t.path;const c=qc(t.query||{});c&&(a+=`?${c}`),t.fragment&&(a+=`#${t.fragment}`);let u="";if(t.username!=null||t.password!=null){const M=t.username??"",j=t.password??"";u=`${M}:${j}@`}const{port:h,method:d}=t,p=`${t.protocol}//${u}${t.hostname}${h?`:${h}`:""}${a}`,g=d==="GET"||d==="HEAD"?void 0:t.body,S={body:g,headers:new Headers(t.headers),method:d,credentials:o};(n=this.config)!=null&&n.cache&&(S.cache=this.config.cache),g&&(S.duplex="half"),typeof AbortController<"u"&&(S.signal=r),mr.supported&&(S.keepalive=i),typeof this.config.requestInit=="function"&&Object.assign(S,this.config.requestInit(t));let R=()=>{};const x=is(p,S),_=[fetch(x).then(M=>{const j=M.headers,z={};for(const Z of j.entries())z[Z[0]]=Z[1];return M.body!=null?{response:new Pt({headers:z,reason:M.statusText,statusCode:M.status,body:M.body})}:M.blob().then(Z=>({response:new Pt({headers:z,reason:M.statusText,statusCode:M.status,body:Z})}))}),zc(s)];return r&&_.push(new Promise((M,j)=>{const z=()=>{const Z=new Error("Request aborted");Z.name="AbortError",j(Z)};if(typeof r.addEventListener=="function"){const Z=r;Z.addEventListener("abort",z,{once:!0}),R=()=>Z.removeEventListener("abort",z)}else r.onabort=z})),Promise.race(_).finally(R)}updateHttpClientConfig(t,r){this.config=void 0,this.configProvider=this.configProvider.then(n=>(n[t]=r,n))}httpHandlerConfigs(){return this.config??{}}}const no=async e=>{var t;return typeof Blob=="function"&&e instanceof Blob||((t=e.constructor)==null?void 0:t.name)==="Blob"?Blob.prototype.arrayBuffer!==void 0?new Uint8Array(await e.arrayBuffer()):jc(e):Vc(e)};async function jc(e){const t=await Wc(e),r=mn(t);return new Uint8Array(r)}async function Vc(e){const t=[],r=e.getReader();let n=!1,s=0;for(;!n;){const{done:a,value:c}=await r.read();c&&(t.push(c),s+=c.length),n=a}const i=new Uint8Array(s);let o=0;for(const a of t)i.set(a,o),o+=a.length;return i}function Wc(e){return new Promise((t,r)=>{const n=new FileReader;n.onloadend=()=>{if(n.readyState!==2)return r(new Error("Reader aborted too early"));const s=n.result??"",i=s.indexOf(","),o=i>-1?i+1:s.length;t(s.substring(o))},n.onabort=()=>r(new Error("Read aborted")),n.onerror=()=>r(n.error),n.readAsDataURL(e)})}const so={},rn={};for(let e=0;e<256;e++){let t=e.toString(16).toLowerCase();t.length===1&&(t=`0${t}`),so[e]=t,rn[t]=e}function io(e){if(e.length%2!==0)throw new Error("Hex encoded strings must have an even number length");const t=new Uint8Array(e.length/2);for(let r=0;r<e.length;r+=2){const n=e.slice(r,r+2).toLowerCase();if(n in rn)t[r/2]=rn[n];else throw new Error(`Cannot decode unrecognized sequence ${n} as hexadecimal`)}return t}function be(e){let t="";for(let r=0;r<e.byteLength;r++)t+=so[e[r]];return t}const os="The stream has already been transformed.",Kc=e=>{var t,r;if(!as(e)&&!tn(e)){const o=((r=(t=e==null?void 0:e.__proto__)==null?void 0:t.constructor)==null?void 0:r.name)||e;throw new Error(`Unexpected stream implementation, expect Blob or ReadableStream, got ${o}`)}let n=!1;const s=async()=>{if(n)throw new Error(os);return n=!0,await no(e)},i=o=>{if(typeof o.stream!="function")throw new Error(`Cannot transform payload Blob to web stream. Please make sure the Blob.stream() is polyfilled.
If you are using React Native, this API is not yet supported, see: https://react-native.canny.io/feature-requests/p/fetch-streaming-body`);return o.stream()};return Object.assign(e,{transformToByteArray:s,transformToString:async o=>{const a=await s();if(o==="base64")return tr(a);if(o==="hex")return be(a);if(o===void 0||o==="utf8"||o==="utf-8")return yn(a);if(typeof TextDecoder=="function")return new TextDecoder(o).decode(a);throw new Error("TextDecoder is not available, please make sure polyfill is provided.")},transformToWebStream:()=>{if(n)throw new Error(os);if(n=!0,as(e))return i(e);if(tn(e))return e;throw new Error(`Cannot transform payload to web stream, got ${e}`)}})},as=e=>typeof Blob=="function"&&e instanceof Blob;async function Gc(e){return typeof e.stream=="function"&&(e=e.stream()),e.tee()}const oo=async(e=new Uint8Array,t)=>{if(e instanceof Uint8Array)return Qe.mutate(e);if(!e)return Qe.mutate(new Uint8Array);const r=t.streamCollector(e);return Qe.mutate(await r)};function cs(e){return encodeURIComponent(e).replace(/[!'()*]/g,function(t){return"%"+t.charCodeAt(0).toString(16).toUpperCase()})}const Zc=(e,t,r,n,s,i)=>{if(t!=null&&t[r]!==void 0){const o=n();if(o.length<=0)throw new Error("Empty value provided for input HTTP label: "+r+".");e=e.replace(s,i?o.split("/").map(a=>cs(a)).join("/"):cs(o))}else throw new Error("No value provided for input HTTP label: "+r+".");return e};function ao(e,t){return new Xc(e,t)}class Xc{constructor(t,r){this.input=t,this.context=r,this.query={},this.method="",this.headers={},this.path="",this.body=null,this.hostname="",this.resolvePathStack=[]}async build(){const{hostname:t,protocol:r="https",port:n,path:s}=await this.context.endpoint();this.path=s;for(const i of this.resolvePathStack)i(this.path);return new ee({protocol:r,hostname:this.hostname||t,port:n,method:this.method,path:this.path,query:this.query,body:this.body,headers:this.headers})}hn(t){return this.hostname=t,this}bp(t){return this.resolvePathStack.push(r=>{this.path=`${r!=null&&r.endsWith("/")?r.slice(0,-1):r||""}`+t}),this}p(t,r,n,s){return this.resolvePathStack.push(i=>{this.path=Zc(i,this.input,t,r,n,s)}),this}h(t){return this.headers=t,this}q(t){return this.query=t,this}b(t){return this.body=t,this}m(t){return this.method=t,this}}function Qc(e,t,r){e.__smithy_context?e.__smithy_context.features||(e.__smithy_context.features={}):e.__smithy_context={features:{}},e.__smithy_context.features[t]=r}class Yc{constructor(t){this.authSchemes=new Map;for(const[r,n]of Object.entries(t))n!==void 0&&this.authSchemes.set(r,n)}getIdentityProvider(t){return this.authSchemes.get(t)}}const Jc=e=>t=>co(t)&&t.expiration.getTime()-Date.now()<e,eu=3e5,tu=Jc(eu),co=e=>e.expiration!==void 0,ru=(e,t,r)=>{if(e===void 0)return;const n=typeof e!="function"?async()=>Promise.resolve(e):e;let s,i,o,a=!1;const c=async u=>{i||(i=n(u));try{s=await i,o=!0,a=!1}finally{i=void 0}return s};return t===void 0?async u=>((!o||u!=null&&u.forceRefresh)&&(s=await c(u)),s):async u=>((!o||u!=null&&u.forceRefresh)&&(s=await c(u)),a?s:r(s)?(t(s)&&await c(u),s):(a=!0,s))},nu=(e,t,r)=>{let n,s,i,o=!1;const a=async()=>{s||(s=e());try{n=await s,i=!0,o=!1}finally{s=void 0}return n};return t===void 0?async c=>((!i||c!=null&&c.forceRefresh)&&(n=await a()),n):async c=>((!i||c!=null&&c.forceRefresh)&&(n=await a()),o?n:r&&!r(n)?(o=!0,n):(t(n)&&await a(),n))},su=e=>(e.sigv4aSigningRegionSet=st(e.sigv4aSigningRegionSet),e),iu="X-Amz-Algorithm",ou="X-Amz-Credential",uo="X-Amz-Date",au="X-Amz-SignedHeaders",cu="X-Amz-Expires",lo="X-Amz-Signature",ho="X-Amz-Security-Token",fo="authorization",po=uo.toLowerCase(),uu="date",lu=[fo,po,uu],du=lo.toLowerCase(),nn="x-amz-content-sha256",hu=ho.toLowerCase(),fu={authorization:!0,"cache-control":!0,connection:!0,expect:!0,from:!0,"keep-alive":!0,"max-forwards":!0,pragma:!0,referer:!0,te:!0,trailer:!0,"transfer-encoding":!0,upgrade:!0,"user-agent":!0,"x-amzn-trace-id":!0},pu=/^proxy-/,gu=/^sec-/,yr="AWS4-HMAC-SHA256",mu="AWS4-HMAC-SHA256-PAYLOAD",yu="UNSIGNED-PAYLOAD",wu=50,go="aws4_request",bu=60*60*24*7,Ot={},wr=[],br=(e,t,r)=>`${e}/${t}/${r}/${go}`,vu=async(e,t,r,n,s)=>{const i=await us(e,t.secretAccessKey,t.accessKeyId),o=`${r}:${n}:${s}:${be(i)}:${t.sessionToken}`;if(o in Ot)return Ot[o];for(wr.push(o);wr.length>wu;)delete Ot[wr.shift()];let a=`AWS4${t.secretAccessKey}`;for(const c of[r,n,s,go])a=await us(e,a,c);return Ot[o]=a},us=(e,t,r)=>{const n=new e(t);return n.update(at(r)),n.digest()},ls=({headers:e},t,r)=>{const n={};for(const s of Object.keys(e).sort()){if(e[s]==null)continue;const i=s.toLowerCase();(i in fu||t!=null&&t.has(i)||pu.test(i)||gu.test(i))&&(!r||r&&!r.has(i))||(n[i]=e[s].trim().replace(/\s+/g," "))}return n},Su=({query:e={}})=>{const t=[],r={};for(const n of Object.keys(e)){if(n.toLowerCase()===du)continue;const s=Ye(n);t.push(s);const i=e[n];typeof i=="string"?r[s]=`${s}=${Ye(i)}`:Array.isArray(i)&&(r[s]=i.slice(0).reduce((o,a)=>o.concat([`${s}=${Ye(a)}`]),[]).sort().join("&"))}return t.sort().map(n=>r[n]).filter(n=>n).join("&")},mo=e=>typeof ArrayBuffer=="function"&&e instanceof ArrayBuffer||Object.prototype.toString.call(e)==="[object ArrayBuffer]",vr=async({headers:e,body:t},r)=>{for(const n of Object.keys(e))if(n.toLowerCase()===nn)return e[n];if(t==null)return"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855";if(typeof t=="string"||ArrayBuffer.isView(t)||mo(t)){const n=new r;return n.update(at(t)),be(await n.digest())}return yu};class Eu{format(t){const r=[];for(const i of Object.keys(t)){const o=Je(i);r.push(Uint8Array.from([o.byteLength]),o,this.formatHeaderValue(t[i]))}const n=new Uint8Array(r.reduce((i,o)=>i+o.byteLength,0));let s=0;for(const i of r)n.set(i,s),s+=i.byteLength;return n}formatHeaderValue(t){switch(t.type){case"boolean":return Uint8Array.from([t.value?0:1]);case"byte":return Uint8Array.from([2,t.value]);case"short":const r=new DataView(new ArrayBuffer(3));return r.setUint8(0,3),r.setInt16(1,t.value,!1),new Uint8Array(r.buffer);case"integer":const n=new DataView(new ArrayBuffer(5));return n.setUint8(0,4),n.setInt32(1,t.value,!1),new Uint8Array(n.buffer);case"long":const s=new Uint8Array(9);return s[0]=5,s.set(t.value.bytes,1),s;case"binary":const i=new DataView(new ArrayBuffer(3+t.value.byteLength));i.setUint8(0,6),i.setUint16(1,t.value.byteLength,!1);const o=new Uint8Array(i.buffer);return o.set(t.value,3),o;case"string":const a=Je(t.value),c=new DataView(new ArrayBuffer(3+a.byteLength));c.setUint8(0,7),c.setUint16(1,a.byteLength,!1);const u=new Uint8Array(c.buffer);return u.set(a,3),u;case"timestamp":const h=new Uint8Array(9);return h[0]=8,h.set(ku.fromNumber(t.value.valueOf()).bytes,1),h;case"uuid":if(!Au.test(t.value))throw new Error(`Invalid UUID received: ${t.value}`);const d=new Uint8Array(17);return d[0]=9,d.set(io(t.value.replace(/\-/g,"")),1),d}}}var ds;(function(e){e[e.boolTrue=0]="boolTrue",e[e.boolFalse=1]="boolFalse",e[e.byte=2]="byte",e[e.short=3]="short",e[e.integer=4]="integer",e[e.long=5]="long",e[e.byteArray=6]="byteArray",e[e.string=7]="string",e[e.timestamp=8]="timestamp",e[e.uuid=9]="uuid"})(ds||(ds={}));const Au=/^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/;let ku=class yo{constructor(t){if(this.bytes=t,t.byteLength!==8)throw new Error("Int64 buffers must be exactly 8 bytes")}static fromNumber(t){if(t>9223372036854776e3||t<-9223372036854776e3)throw new Error(`${t} is too large (or, if negative, too small) to represent as an Int64`);const r=new Uint8Array(8);for(let n=7,s=Math.abs(Math.round(t));n>-1&&s>0;n--,s/=256)r[n]=s;return t<0&&hs(r),new yo(r)}valueOf(){const t=this.bytes.slice(0),r=t[0]&128;return r&&hs(t),parseInt(be(t),16)*(r?-1:1)}toString(){return String(this.valueOf())}};function hs(e){for(let t=0;t<8;t++)e[t]^=255;for(let t=7;t>-1&&(e[t]++,e[t]===0);t--);}const xu=(e,t)=>{e=e.toLowerCase();for(const r of Object.keys(t))if(e===r.toLowerCase())return!0;return!1},Ru=(e,t={})=>{var r,n;const{headers:s,query:i={}}=ee.clone(e);for(const o of Object.keys(s)){const a=o.toLowerCase();(a.slice(0,6)==="x-amz-"&&!((r=t.unhoistableHeaders)!=null&&r.has(a))||(n=t.hoistableHeaders)!=null&&n.has(a))&&(i[o]=s[o],delete s[o])}return{...e,headers:s,query:i}},fs=e=>{e=ee.clone(e);for(const t of Object.keys(e.headers))lu.indexOf(t.toLowerCase())>-1&&delete e.headers[t];return e},Cu=e=>Pu(e).toISOString().replace(/\.\d{3}Z$/,"Z"),Pu=e=>typeof e=="number"?new Date(e*1e3):typeof e=="string"?Number(e)?new Date(Number(e)*1e3):new Date(e):e;class sn{constructor({applyChecksum:t,credentials:r,region:n,service:s,sha256:i,uriEscapePath:o=!0}){this.headerFormatter=new Eu,this.service=s,this.sha256=i,this.uriEscapePath=o,this.applyChecksum=typeof t=="boolean"?t:!0,this.regionProvider=Ne(n),this.credentialProvider=Ne(r)}async presign(t,r={}){const{signingDate:n=new Date,expiresIn:s=3600,unsignableHeaders:i,unhoistableHeaders:o,signableHeaders:a,hoistableHeaders:c,signingRegion:u,signingService:h}=r,d=await this.credentialProvider();this.validateResolvedCredentials(d);const p=u??await this.regionProvider(),{longDate:g,shortDate:S}=Mt(n);if(s>bu)return Promise.reject("Signature version 4 presigned URLs must have an expiration date less than one week in the future");const R=br(S,p,h??this.service),x=Ru(fs(t),{unhoistableHeaders:o,hoistableHeaders:c});d.sessionToken&&(x.query[ho]=d.sessionToken),x.query[iu]=yr,x.query[ou]=`${d.accessKeyId}/${R}`,x.query[uo]=g,x.query[cu]=s.toString(10);const _=ls(x,i,a);return x.query[au]=ps(_),x.query[lo]=await this.getSignature(g,R,this.getSigningKey(d,p,S,h),this.createCanonicalRequest(x,_,await vr(t,this.sha256))),x}async sign(t,r){return typeof t=="string"?this.signString(t,r):t.headers&&t.payload?this.signEvent(t,r):t.message?this.signMessage(t,r):this.signRequest(t,r)}async signEvent({headers:t,payload:r},{signingDate:n=new Date,priorSignature:s,signingRegion:i,signingService:o}){const a=i??await this.regionProvider(),{shortDate:c,longDate:u}=Mt(n),h=br(c,a,o??this.service),d=await vr({headers:{},body:r},this.sha256),p=new this.sha256;p.update(t);const g=be(await p.digest()),S=[mu,u,h,s,g,d].join(`
`);return this.signString(S,{signingDate:n,signingRegion:a,signingService:o})}async signMessage(t,{signingDate:r=new Date,signingRegion:n,signingService:s}){return this.signEvent({headers:this.headerFormatter.format(t.message.headers),payload:t.message.body},{signingDate:r,signingRegion:n,signingService:s,priorSignature:t.priorSignature}).then(i=>({message:t.message,signature:i}))}async signString(t,{signingDate:r=new Date,signingRegion:n,signingService:s}={}){const i=await this.credentialProvider();this.validateResolvedCredentials(i);const o=n??await this.regionProvider(),{shortDate:a}=Mt(r),c=new this.sha256(await this.getSigningKey(i,o,a,s));return c.update(at(t)),be(await c.digest())}async signRequest(t,{signingDate:r=new Date,signableHeaders:n,unsignableHeaders:s,signingRegion:i,signingService:o}={}){const a=await this.credentialProvider();this.validateResolvedCredentials(a);const c=i??await this.regionProvider(),u=fs(t),{longDate:h,shortDate:d}=Mt(r),p=br(d,c,o??this.service);u.headers[po]=h,a.sessionToken&&(u.headers[hu]=a.sessionToken);const g=await vr(u,this.sha256);!xu(nn,u.headers)&&this.applyChecksum&&(u.headers[nn]=g);const S=ls(u,s,n),R=await this.getSignature(h,p,this.getSigningKey(a,c,d,o),this.createCanonicalRequest(u,S,g));return u.headers[fo]=`${yr} Credential=${a.accessKeyId}/${p}, SignedHeaders=${ps(S)}, Signature=${R}`,u}createCanonicalRequest(t,r,n){const s=Object.keys(r).sort();return`${t.method}
${this.getCanonicalPath(t)}
${Su(t)}
${s.map(i=>`${i}:${r[i]}`).join(`
`)}

${s.join(";")}
${n}`}async createStringToSign(t,r,n){const s=new this.sha256;s.update(at(n));const i=await s.digest();return`${yr}
${t}
${r}
${be(i)}`}getCanonicalPath({path:t}){if(this.uriEscapePath){const r=[];for(const s of t.split("/"))(s==null?void 0:s.length)!==0&&s!=="."&&(s===".."?r.pop():r.push(s));const n=`${t!=null&&t.startsWith("/")?"/":""}${r.join("/")}${r.length>0&&t!=null&&t.endsWith("/")?"/":""}`;return Ye(n).replace(/%2F/g,"/")}return t}async getSignature(t,r,n,s){const i=await this.createStringToSign(t,r,s),o=new this.sha256(await n);return o.update(at(i)),be(await o.digest())}getSigningKey(t,r,n,s){return vu(this.sha256,t,n,r,s||this.service)}validateResolvedCredentials(t){if(typeof t!="object"||typeof t.accessKeyId!="string"||typeof t.secretAccessKey!="string")throw new Error("Resolved credential object is not valid")}}const Mt=e=>{const t=Cu(e).replace(/[\-:]/g,"");return{longDate:t,shortDate:t.slice(0,8)}},ps=e=>Object.keys(e).sort().join(";"),Tu=e=>{let t=!1,r;e.credentials&&(t=!0,r=ru(e.credentials,tu,co)),r||(e.credentialDefaultProvider?r=st(e.credentialDefaultProvider(Object.assign({},e,{parentClientConfig:e}))):r=async()=>{throw new Error("`credentials` is missing")});const n=async()=>r({callerClientConfig:e}),{signingEscapePath:s=!0,systemClockOffset:i=e.systemClockOffset||0,sha256:o}=e;let a;return e.signer?a=st(e.signer):e.regionInfoProvider?a=()=>st(e.region)().then(async c=>[await e.regionInfoProvider(c,{useFipsEndpoint:await e.useFipsEndpoint(),useDualstackEndpoint:await e.useDualstackEndpoint()})||{},c]).then(([c,u])=>{const{signingRegion:h,signingService:d}=c;e.signingRegion=e.signingRegion||h||u,e.signingName=e.signingName||d||e.serviceId;const p={...e,credentials:n,region:e.signingRegion,service:e.signingName,sha256:o,uriEscapePath:s},g=e.signerConstructor||sn;return new g(p)}):a=async c=>{c=Object.assign({},{name:"sigv4",signingName:e.signingName||e.defaultSigningName,signingRegion:await st(e.region)(),properties:{}},c);const u=c.signingRegion,h=c.signingName;e.signingRegion=e.signingRegion||u,e.signingName=e.signingName||h||e.serviceId;const d={...e,credentials:n,region:e.signingRegion,service:e.signingName,sha256:o,uriEscapePath:s},p=e.signerConstructor||sn;return new p(d)},{...e,systemClockOffset:i,signingEscapePath:s,credentials:t?async()=>n().then(c=>pc(c,"CREDENTIALS_CODE","e")):n,signer:a}},Ke=(e,t)=>{const r=[];if(e&&r.push(e),t)for(const n of t)r.push(n);return r},$e=(e,t)=>`${e||"anonymous"}${t&&t.length>0?` (a.k.a. ${t.join(",")})`:""}`,Kt=()=>{let e=[],t=[],r=!1;const n=new Set,s=d=>d.sort((p,g)=>gs[g.step]-gs[p.step]||ms[g.priority||"normal"]-ms[p.priority||"normal"]),i=d=>{let p=!1;const g=S=>{const R=Ke(S.name,S.aliases);if(R.includes(d)){p=!0;for(const x of R)n.delete(x);return!1}return!0};return e=e.filter(g),t=t.filter(g),p},o=d=>{let p=!1;const g=S=>{if(S.middleware===d){p=!0;for(const R of Ke(S.name,S.aliases))n.delete(R);return!1}return!0};return e=e.filter(g),t=t.filter(g),p},a=d=>{var p;return e.forEach(g=>{d.add(g.middleware,{...g})}),t.forEach(g=>{d.addRelativeTo(g.middleware,{...g})}),(p=d.identifyOnResolve)==null||p.call(d,h.identifyOnResolve()),d},c=d=>{const p=[];return d.before.forEach(g=>{g.before.length===0&&g.after.length===0?p.push(g):p.push(...c(g))}),p.push(d),d.after.reverse().forEach(g=>{g.before.length===0&&g.after.length===0?p.push(g):p.push(...c(g))}),p},u=(d=!1)=>{const p=[],g=[],S={};return e.forEach(R=>{const x={...R,before:[],after:[]};for(const _ of Ke(x.name,x.aliases))S[_]=x;p.push(x)}),t.forEach(R=>{const x={...R,before:[],after:[]};for(const _ of Ke(x.name,x.aliases))S[_]=x;g.push(x)}),g.forEach(R=>{if(R.toMiddleware){const x=S[R.toMiddleware];if(x===void 0){if(d)return;throw new Error(`${R.toMiddleware} is not found when adding ${$e(R.name,R.aliases)} middleware ${R.relation} ${R.toMiddleware}`)}R.relation==="after"&&x.after.push(R),R.relation==="before"&&x.before.push(R)}}),s(p).map(c).reduce((R,x)=>(R.push(...x),R),[])},h={add:(d,p={})=>{const{name:g,override:S,aliases:R}=p,x={step:"initialize",priority:"normal",middleware:d,...p},_=Ke(g,R);if(_.length>0){if(_.some(M=>n.has(M))){if(!S)throw new Error(`Duplicate middleware name '${$e(g,R)}'`);for(const M of _){const j=e.findIndex(Z=>{var We;return Z.name===M||((We=Z.aliases)==null?void 0:We.some(fr=>fr===M))});if(j===-1)continue;const z=e[j];if(z.step!==x.step||x.priority!==z.priority)throw new Error(`"${$e(z.name,z.aliases)}" middleware with ${z.priority} priority in ${z.step} step cannot be overridden by "${$e(g,R)}" middleware with ${x.priority} priority in ${x.step} step.`);e.splice(j,1)}}for(const M of _)n.add(M)}e.push(x)},addRelativeTo:(d,p)=>{const{name:g,override:S,aliases:R}=p,x={middleware:d,...p},_=Ke(g,R);if(_.length>0){if(_.some(M=>n.has(M))){if(!S)throw new Error(`Duplicate middleware name '${$e(g,R)}'`);for(const M of _){const j=t.findIndex(Z=>{var We;return Z.name===M||((We=Z.aliases)==null?void 0:We.some(fr=>fr===M))});if(j===-1)continue;const z=t[j];if(z.toMiddleware!==x.toMiddleware||z.relation!==x.relation)throw new Error(`"${$e(z.name,z.aliases)}" middleware ${z.relation} "${z.toMiddleware}" middleware cannot be overridden by "${$e(g,R)}" middleware ${x.relation} "${x.toMiddleware}" middleware.`);t.splice(j,1)}}for(const M of _)n.add(M)}t.push(x)},clone:()=>a(Kt()),use:d=>{d.applyToStack(h)},remove:d=>typeof d=="string"?i(d):o(d),removeByTag:d=>{let p=!1;const g=S=>{const{tags:R,name:x,aliases:_}=S;if(R&&R.includes(d)){const M=Ke(x,_);for(const j of M)n.delete(j);return p=!0,!1}return!0};return e=e.filter(g),t=t.filter(g),p},concat:d=>{var p;const g=a(Kt());return g.use(d),g.identifyOnResolve(r||g.identifyOnResolve()||(((p=d.identifyOnResolve)==null?void 0:p.call(d))??!1)),g},applyToStack:a,identify:()=>u(!0).map(d=>{const p=d.step??d.relation+" "+d.toMiddleware;return $e(d.name,d.aliases)+" - "+p}),identifyOnResolve(d){return typeof d=="boolean"&&(r=d),r},resolve:(d,p)=>{for(const g of u().map(S=>S.middleware).reverse())d=g(d,p);return d}};return h},gs={initialize:5,serialize:4,build:3,finalizeRequest:2,deserialize:1},ms={high:3,normal:2,low:1};class Iu{constructor(t){this.config=t,this.middlewareStack=Kt()}send(t,r,n){const s=typeof r!="function"?r:void 0,i=typeof r=="function"?r:n,o=s===void 0&&this.config.cacheMiddleware===!0;let a;if(o){this.handlers||(this.handlers=new WeakMap);const c=this.handlers;c.has(t.constructor)?a=c.get(t.constructor):(a=t.resolveMiddleware(this.middlewareStack,this.config,s),c.set(t.constructor,a))}else delete this.handlers,a=t.resolveMiddleware(this.middlewareStack,this.config,s);if(i)a(t).then(c=>i(null,c.output),c=>i(c)).catch(()=>{});else return a(t).then(c=>c.output)}destroy(){var t,r,n;(n=(r=(t=this.config)==null?void 0:t.requestHandler)==null?void 0:r.destroy)==null||n.call(r),delete this.handlers}}class bn{constructor(){this.middlewareStack=Kt()}static classBuilder(){return new Nu}resolveMiddlewareWithContext(t,r,n,{middlewareFn:s,clientName:i,commandName:o,inputFilterSensitiveLog:a,outputFilterSensitiveLog:c,smithyContext:u,additionalContext:h,CommandCtor:d}){for(const x of s.bind(this)(d,t,r,n))this.middlewareStack.use(x);const p=t.concat(this.middlewareStack),{logger:g}=r,S={logger:g,clientName:i,commandName:o,inputFilterSensitiveLog:a,outputFilterSensitiveLog:c,[Jr]:{commandInstance:this,...u},...h},{requestHandler:R}=r;return p.resolve(x=>R.handle(x.request,n||{}),S)}}class Nu{constructor(){this._init=()=>{},this._ep={},this._middlewareFn=()=>[],this._commandName="",this._clientName="",this._additionalContext={},this._smithyContext={},this._inputFilterSensitiveLog=t=>t,this._outputFilterSensitiveLog=t=>t,this._serializer=null,this._deserializer=null}init(t){this._init=t}ep(t){return this._ep=t,this}m(t){return this._middlewareFn=t,this}s(t,r,n={}){return this._smithyContext={service:t,operation:r,...n},this}c(t={}){return this._additionalContext=t,this}n(t,r){return this._clientName=t,this._commandName=r,this}f(t=n=>n,r=n=>n){return this._inputFilterSensitiveLog=t,this._outputFilterSensitiveLog=r,this}ser(t){return this._serializer=t,this}de(t){return this._deserializer=t,this}build(){const t=this;let r;return r=class extends bn{static getEndpointParameterInstructions(){return t._ep}constructor(...[n]){super(),this.serialize=t._serializer,this.deserialize=t._deserializer,this.input=n??{},t._init(this)}resolveMiddleware(n,s,i){return this.resolveMiddlewareWithContext(n,s,i,{CommandCtor:r,middlewareFn:t._middlewareFn,clientName:t._clientName,commandName:t._commandName,inputFilterSensitiveLog:t._inputFilterSensitiveLog,outputFilterSensitiveLog:t._outputFilterSensitiveLog,smithyContext:t._smithyContext,additionalContext:t._additionalContext})}}}}const Te="***SensitiveInformation***",wo=e=>{switch(e){case"true":return!0;case"false":return!1;default:throw new Error(`Unable to parse boolean value "${e}"`)}},Ou=e=>{if(e!=null){if(typeof e=="string"){const t=parseFloat(e);if(!Number.isNaN(t))return String(t)!==String(e)&&Ao.warn(Eo(`Expected number but observed string: ${e}`)),t}if(typeof e=="number")return e;throw new TypeError(`Expected number, got ${typeof e}: ${e}`)}},Mu=Math.ceil(2**127*(2-2**-23)),Uu=e=>{const t=Ou(e);if(t!==void 0&&!Number.isNaN(t)&&t!==1/0&&t!==-1/0&&Math.abs(t)>Mu)throw new TypeError(`Expected 32-bit float, got ${e}`);return t},bo=e=>{if(e!=null){if(Number.isInteger(e)&&!Number.isNaN(e))return e;throw new TypeError(`Expected integer, got ${typeof e}: ${e}`)}},$u=e=>vo(e,16),_u=e=>vo(e,8),vo=(e,t)=>{const r=bo(e);if(r!==void 0&&Du(r,t)!==r)throw new TypeError(`Expected ${t}-bit integer, got ${e}`);return r},Du=(e,t)=>{switch(t){case 32:return Int32Array.of(e)[0];case 16:return Int16Array.of(e)[0];case 8:return Int8Array.of(e)[0]}},So=(e,t)=>{if(e==null)throw t?new TypeError(`Expected a non-null value for ${t}`):new TypeError("Expected a non-null value");return e},Bu=e=>{if(e==null)return;if(typeof e=="object"&&!Array.isArray(e))return e;const t=Array.isArray(e)?"array":typeof e;throw new TypeError(`Expected object, got ${t}: ${e}`)},kt=e=>{if(e!=null){if(typeof e=="string")return e;if(["boolean","number","bigint"].includes(typeof e))return Ao.warn(Eo(`Expected string, got ${typeof e}: ${e}`)),String(e);throw new TypeError(`Expected string, got ${typeof e}: ${e}`)}},Lu=e=>Uu(typeof e=="string"?rr(e):e),Fu=/(-?(?:0|[1-9]\d*)(?:\.\d+)?(?:[eE][+-]?\d+)?)|(-?Infinity)|(NaN)/g,rr=e=>{const t=e.match(Fu);if(t===null||t[0].length!==e.length)throw new TypeError("Expected real number, got implicit NaN");return parseFloat(e)},Hu=e=>bo(typeof e=="string"?rr(e):e),qu=e=>$u(typeof e=="string"?rr(e):e),zu=e=>_u(typeof e=="string"?rr(e):e),Eo=e=>String(new TypeError(e).stack||e).split(`
`).slice(0,5).filter(t=>!t.includes("stackTraceWarning")).join(`
`),Ao={warn:console.warn},ju=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],ko=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function Vu(e){const t=e.getUTCFullYear(),r=e.getUTCMonth(),n=e.getUTCDay(),s=e.getUTCDate(),i=e.getUTCHours(),o=e.getUTCMinutes(),a=e.getUTCSeconds(),c=s<10?`0${s}`:`${s}`,u=i<10?`0${i}`:`${i}`,h=o<10?`0${o}`:`${o}`,d=a<10?`0${a}`:`${a}`;return`${ju[n]}, ${c} ${ko[r]} ${t} ${u}:${h}:${d} GMT`}const Wu=new RegExp(/^(\d{4})-(\d{2})-(\d{2})[tT](\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?(([-+]\d{2}\:\d{2})|[zZ])$/),Ku=e=>{if(e==null)return;if(typeof e!="string")throw new TypeError("RFC-3339 date-times must be expressed as strings");const t=Wu.exec(e);if(!t)throw new TypeError("Invalid RFC-3339 date-time value");const[r,n,s,i,o,a,c,u,h]=t,d=qu(xo(n)),p=xt(s,"month",1,12),g=xt(i,"day",1,31),S=Gu(d,p,g,{hours:o,minutes:a,seconds:c,fractionalMilliseconds:u});return h.toUpperCase()!="Z"&&S.setTime(S.getTime()-Ju(h)),S},Gu=(e,t,r,n)=>{const s=t-1;return Xu(e,s,r),new Date(Date.UTC(e,s,r,xt(n.hours,"hour",0,23),xt(n.minutes,"minute",0,59),xt(n.seconds,"seconds",0,60),Yu(n.fractionalMilliseconds)))},Zu=[31,28,31,30,31,30,31,31,30,31,30,31],Xu=(e,t,r)=>{let n=Zu[t];if(t===1&&Qu(e)&&(n=29),r>n)throw new TypeError(`Invalid day for ${ko[t]} in ${e}: ${r}`)},Qu=e=>e%4===0&&(e%100!==0||e%400===0),xt=(e,t,r,n)=>{const s=zu(xo(e));if(s<r||s>n)throw new TypeError(`${t} must be between ${r} and ${n}, inclusive`);return s},Yu=e=>e==null?0:Lu("0."+e)*1e3,Ju=e=>{const t=e[0];let r=1;if(t=="+")r=1;else if(t=="-")r=-1;else throw new TypeError(`Offset direction, ${t}, must be "+" or "-"`);const n=Number(e.substring(1,3)),s=Number(e.substring(4,6));return r*(n*60+s)*60*1e3},xo=e=>{let t=0;for(;t<e.length-1&&e.charAt(t)==="0";)t++;return t===0?e:e.slice(t)};class it extends Error{constructor(t){super(t.message),Object.setPrototypeOf(this,Object.getPrototypeOf(this).constructor.prototype),this.name=t.name,this.$fault=t.$fault,this.$metadata=t.$metadata}static isInstance(t){if(!t)return!1;const r=t;return it.prototype.isPrototypeOf(r)||!!r.$fault&&!!r.$metadata&&(r.$fault==="client"||r.$fault==="server")}static[Symbol.hasInstance](t){if(!t)return!1;const r=t;return this===it?it.isInstance(t):it.isInstance(t)?r.name&&this.name?this.prototype.isPrototypeOf(t)||r.name===this.name:this.prototype.isPrototypeOf(t):!1}}const Se=(e,t={})=>{Object.entries(t).filter(([,n])=>n!==void 0).forEach(([n,s])=>{(e[n]==null||e[n]==="")&&(e[n]=s)});const r=e.message||e.Message||"UnknownError";return e.message=r,delete e.Message,e},el=({output:e,parsedBody:t,exceptionCtor:r,errorCode:n})=>{const s=rl(e),i=s.httpStatusCode?s.httpStatusCode+"":void 0,o=new r({name:(t==null?void 0:t.code)||(t==null?void 0:t.Code)||n||i||"UnknownError",$fault:"client",$metadata:s});throw Se(o,t)},tl=e=>({output:t,parsedBody:r,errorCode:n})=>{el({output:t,parsedBody:r,exceptionCtor:e,errorCode:n})},rl=e=>({httpStatusCode:e.statusCode,requestId:e.headers["x-amzn-requestid"]??e.headers["x-amzn-request-id"]??e.headers["x-amz-request-id"],extendedRequestId:e.headers["x-amz-id-2"],cfId:e.headers["x-amz-cf-id"]}),nl=e=>{switch(e){case"standard":return{retryMode:"standard",connectionTimeout:3100};case"in-region":return{retryMode:"standard",connectionTimeout:1100};case"cross-region":return{retryMode:"standard",connectionTimeout:3100};case"mobile":return{retryMode:"standard",connectionTimeout:3e4};default:return{}}},sl=e=>{const t=[];for(const r in Wt){const n=Wt[r];e[n]!==void 0&&t.push({algorithmId:()=>n,checksumConstructor:()=>e[n]})}return{_checksumAlgorithms:t,addChecksumAlgorithm(r){this._checksumAlgorithms.push(r)},checksumAlgorithms(){return this._checksumAlgorithms}}},il=e=>{const t={};return e.checksumAlgorithms().forEach(r=>{t[r.algorithmId()]=r.checksumConstructor()}),t},ol=e=>{let t=e.retryStrategy;return{setRetryStrategy(r){t=r},retryStrategy(){return t}}},al=e=>{const t={};return t.retryStrategy=e.retryStrategy(),t},cl=e=>({...sl(e),...ol(e)}),ul=e=>({...il(e),...al(e)}),Ro=e=>{const t="#text";for(const r in e)e.hasOwnProperty(r)&&e[r][t]!==void 0?e[r]=e[r][t]:typeof e[r]=="object"&&e[r]!==null&&(e[r]=Ro(e[r]));return e},He=e=>e!=null;class vn{trace(){}debug(){}info(){}warn(){}error(){}}function te(e,t,r){let n,s,i;if(typeof t>"u"&&typeof r>"u")n={},i=e;else{if(n=e,typeof t=="function")return s=t,i=r,ll(n,s,i);i=t}for(const o of Object.keys(i)){if(!Array.isArray(i[o])){n[o]=i[o];continue}dl(n,null,i,o)}return n}const ll=(e,t,r)=>te(e,Object.entries(r).reduce((n,[s,i])=>(Array.isArray(i)?n[s]=i:typeof i=="function"?n[s]=[t,i()]:n[s]=[t,i],n),{})),dl=(e,t,r,n)=>{let[s,i]=r[n];if(typeof i=="function"){let o;const a=s===void 0&&(o=i())!=null,c=typeof s=="function"&&!!s(void 0)||typeof s!="function"&&!!s;a?e[n]=o:c&&(e[n]=i())}else{const o=s===void 0&&i!=null,a=typeof s=="function"&&!!s(i)||typeof s!="function"&&!!s;(o||a)&&(e[n]=i)}},hl=e=>e.toISOString().replace(".000Z","Z"),fl=(e,t)=>oo(e,t).then(r=>t.utf8Encoder(r));var Co={},nr={};(function(e){const t=":A-Za-z_\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD",r=t+"\\-.\\d\\u00B7\\u0300-\\u036F\\u203F-\\u2040",n="["+t+"]["+r+"]*",s=new RegExp("^"+n+"$"),i=function(a,c){const u=[];let h=c.exec(a);for(;h;){const d=[];d.startIndex=c.lastIndex-h[0].length;const p=h.length;for(let g=0;g<p;g++)d.push(h[g]);u.push(d),h=c.exec(a)}return u},o=function(a){const c=s.exec(a);return!(c===null||typeof c>"u")};e.isExist=function(a){return typeof a<"u"},e.isEmptyObject=function(a){return Object.keys(a).length===0},e.merge=function(a,c,u){if(c){const h=Object.keys(c),d=h.length;for(let p=0;p<d;p++)u==="strict"?a[h[p]]=[c[h[p]]]:a[h[p]]=c[h[p]]}},e.getValue=function(a){return e.isExist(a)?a:""},e.isName=o,e.getAllMatches=i,e.nameRegexp=n})(nr);const Sn=nr,pl={allowBooleanAttributes:!1,unpairedTags:[]};Co.validate=function(e,t){t=Object.assign({},pl,t);const r=[];let n=!1,s=!1;e[0]==="\uFEFF"&&(e=e.substr(1));for(let i=0;i<e.length;i++)if(e[i]==="<"&&e[i+1]==="?"){if(i+=2,i=ws(e,i),i.err)return i}else if(e[i]==="<"){let o=i;if(i++,e[i]==="!"){i=bs(e,i);continue}else{let a=!1;e[i]==="/"&&(a=!0,i++);let c="";for(;i<e.length&&e[i]!==">"&&e[i]!==" "&&e[i]!=="	"&&e[i]!==`
`&&e[i]!=="\r";i++)c+=e[i];if(c=c.trim(),c[c.length-1]==="/"&&(c=c.substring(0,c.length-1),i--),!El(c)){let d;return c.trim().length===0?d="Invalid space after '<'.":d="Tag '"+c+"' is an invalid name.",W("InvalidTag",d,ge(e,i))}const u=yl(e,i);if(u===!1)return W("InvalidAttr","Attributes for '"+c+"' have open quote.",ge(e,i));let h=u.value;if(i=u.index,h[h.length-1]==="/"){const d=i-h.length;h=h.substring(0,h.length-1);const p=vs(h,t);if(p===!0)n=!0;else return W(p.err.code,p.err.msg,ge(e,d+p.err.line))}else if(a)if(u.tagClosed){if(h.trim().length>0)return W("InvalidTag","Closing tag '"+c+"' can't have attributes or invalid starting.",ge(e,o));if(r.length===0)return W("InvalidTag","Closing tag '"+c+"' has not been opened.",ge(e,o));{const d=r.pop();if(c!==d.tagName){let p=ge(e,d.tagStartPos);return W("InvalidTag","Expected closing tag '"+d.tagName+"' (opened in line "+p.line+", col "+p.col+") instead of closing tag '"+c+"'.",ge(e,o))}r.length==0&&(s=!0)}}else return W("InvalidTag","Closing tag '"+c+"' doesn't have proper closing.",ge(e,i));else{const d=vs(h,t);if(d!==!0)return W(d.err.code,d.err.msg,ge(e,i-h.length+d.err.line));if(s===!0)return W("InvalidXml","Multiple possible root nodes found.",ge(e,i));t.unpairedTags.indexOf(c)!==-1||r.push({tagName:c,tagStartPos:o}),n=!0}for(i++;i<e.length;i++)if(e[i]==="<")if(e[i+1]==="!"){i++,i=bs(e,i);continue}else if(e[i+1]==="?"){if(i=ws(e,++i),i.err)return i}else break;else if(e[i]==="&"){const d=vl(e,i);if(d==-1)return W("InvalidChar","char '&' is not expected.",ge(e,i));i=d}else if(s===!0&&!ys(e[i]))return W("InvalidXml","Extra text at the end",ge(e,i));e[i]==="<"&&i--}}else{if(ys(e[i]))continue;return W("InvalidChar","char '"+e[i]+"' is not expected.",ge(e,i))}if(n){if(r.length==1)return W("InvalidTag","Unclosed tag '"+r[0].tagName+"'.",ge(e,r[0].tagStartPos));if(r.length>0)return W("InvalidXml","Invalid '"+JSON.stringify(r.map(i=>i.tagName),null,4).replace(/\r?\n/g,"")+"' found.",{line:1,col:1})}else return W("InvalidXml","Start tag expected.",1);return!0};function ys(e){return e===" "||e==="	"||e===`
`||e==="\r"}function ws(e,t){const r=t;for(;t<e.length;t++)if(e[t]=="?"||e[t]==" "){const n=e.substr(r,t-r);if(t>5&&n==="xml")return W("InvalidXml","XML declaration allowed only at the start of the document.",ge(e,t));if(e[t]=="?"&&e[t+1]==">"){t++;break}else continue}return t}function bs(e,t){if(e.length>t+5&&e[t+1]==="-"&&e[t+2]==="-"){for(t+=3;t<e.length;t++)if(e[t]==="-"&&e[t+1]==="-"&&e[t+2]===">"){t+=2;break}}else if(e.length>t+8&&e[t+1]==="D"&&e[t+2]==="O"&&e[t+3]==="C"&&e[t+4]==="T"&&e[t+5]==="Y"&&e[t+6]==="P"&&e[t+7]==="E"){let r=1;for(t+=8;t<e.length;t++)if(e[t]==="<")r++;else if(e[t]===">"&&(r--,r===0))break}else if(e.length>t+9&&e[t+1]==="["&&e[t+2]==="C"&&e[t+3]==="D"&&e[t+4]==="A"&&e[t+5]==="T"&&e[t+6]==="A"&&e[t+7]==="["){for(t+=8;t<e.length;t++)if(e[t]==="]"&&e[t+1]==="]"&&e[t+2]===">"){t+=2;break}}return t}const gl='"',ml="'";function yl(e,t){let r="",n="",s=!1;for(;t<e.length;t++){if(e[t]===gl||e[t]===ml)n===""?n=e[t]:n!==e[t]||(n="");else if(e[t]===">"&&n===""){s=!0;break}r+=e[t]}return n!==""?!1:{value:r,index:t,tagClosed:s}}const wl=new RegExp(`(\\s*)([^\\s=]+)(\\s*=)?(\\s*(['"])(([\\s\\S])*?)\\5)?`,"g");function vs(e,t){const r=Sn.getAllMatches(e,wl),n={};for(let s=0;s<r.length;s++){if(r[s][1].length===0)return W("InvalidAttr","Attribute '"+r[s][2]+"' has no space in starting.",gt(r[s]));if(r[s][3]!==void 0&&r[s][4]===void 0)return W("InvalidAttr","Attribute '"+r[s][2]+"' is without value.",gt(r[s]));if(r[s][3]===void 0&&!t.allowBooleanAttributes)return W("InvalidAttr","boolean attribute '"+r[s][2]+"' is not allowed.",gt(r[s]));const i=r[s][2];if(!Sl(i))return W("InvalidAttr","Attribute '"+i+"' is an invalid name.",gt(r[s]));if(!n.hasOwnProperty(i))n[i]=1;else return W("InvalidAttr","Attribute '"+i+"' is repeated.",gt(r[s]))}return!0}function bl(e,t){let r=/\d/;for(e[t]==="x"&&(t++,r=/[\da-fA-F]/);t<e.length;t++){if(e[t]===";")return t;if(!e[t].match(r))break}return-1}function vl(e,t){if(t++,e[t]===";")return-1;if(e[t]==="#")return t++,bl(e,t);let r=0;for(;t<e.length;t++,r++)if(!(e[t].match(/\w/)&&r<20)){if(e[t]===";")break;return-1}return t}function W(e,t,r){return{err:{code:e,msg:t,line:r.line||r,col:r.col}}}function Sl(e){return Sn.isName(e)}function El(e){return Sn.isName(e)}function ge(e,t){const r=e.substring(0,t).split(/\r?\n/);return{line:r.length,col:r[r.length-1].length+1}}function gt(e){return e.startIndex+e[1].length}var En={};const Po={preserveOrder:!1,attributeNamePrefix:"@_",attributesGroupName:!1,textNodeName:"#text",ignoreAttributes:!0,removeNSPrefix:!1,allowBooleanAttributes:!1,parseTagValue:!0,parseAttributeValue:!1,trimValues:!0,cdataPropName:!1,numberParseOptions:{hex:!0,leadingZeros:!0,eNotation:!0},tagValueProcessor:function(e,t){return t},attributeValueProcessor:function(e,t){return t},stopNodes:[],alwaysCreateTextNode:!1,isArray:()=>!1,commentPropName:!1,unpairedTags:[],processEntities:!0,htmlEntities:!1,ignoreDeclaration:!1,ignorePiTags:!1,transformTagName:!1,transformAttributeName:!1,updateTag:function(e,t,r){return e}},Al=function(e){return Object.assign({},Po,e)};En.buildOptions=Al;En.defaultOptions=Po;class kl{constructor(t){this.tagname=t,this.child=[],this[":@"]={}}add(t,r){t==="__proto__"&&(t="#__proto__"),this.child.push({[t]:r})}addChild(t){t.tagname==="__proto__"&&(t.tagname="#__proto__"),t[":@"]&&Object.keys(t[":@"]).length>0?this.child.push({[t.tagname]:t.child,":@":t[":@"]}):this.child.push({[t.tagname]:t.child})}}var xl=kl;const Rl=nr;function Cl(e,t){const r={};if(e[t+3]==="O"&&e[t+4]==="C"&&e[t+5]==="T"&&e[t+6]==="Y"&&e[t+7]==="P"&&e[t+8]==="E"){t=t+9;let n=1,s=!1,i=!1,o="";for(;t<e.length;t++)if(e[t]==="<"&&!i){if(s&&Il(e,t))t+=7,[entityName,val,t]=Pl(e,t+1),val.indexOf("&")===-1&&(r[Ul(entityName)]={regx:RegExp(`&${entityName};`,"g"),val});else if(s&&Nl(e,t))t+=8;else if(s&&Ol(e,t))t+=8;else if(s&&Ml(e,t))t+=9;else if(Tl)i=!0;else throw new Error("Invalid DOCTYPE");n++,o=""}else if(e[t]===">"){if(i?e[t-1]==="-"&&e[t-2]==="-"&&(i=!1,n--):n--,n===0)break}else e[t]==="["?s=!0:o+=e[t];if(n!==0)throw new Error("Unclosed DOCTYPE")}else throw new Error("Invalid Tag instead of DOCTYPE");return{entities:r,i:t}}function Pl(e,t){let r="";for(;t<e.length&&e[t]!=="'"&&e[t]!=='"';t++)r+=e[t];if(r=r.trim(),r.indexOf(" ")!==-1)throw new Error("External entites are not supported");const n=e[t++];let s="";for(;t<e.length&&e[t]!==n;t++)s+=e[t];return[r,s,t]}function Tl(e,t){return e[t+1]==="!"&&e[t+2]==="-"&&e[t+3]==="-"}function Il(e,t){return e[t+1]==="!"&&e[t+2]==="E"&&e[t+3]==="N"&&e[t+4]==="T"&&e[t+5]==="I"&&e[t+6]==="T"&&e[t+7]==="Y"}function Nl(e,t){return e[t+1]==="!"&&e[t+2]==="E"&&e[t+3]==="L"&&e[t+4]==="E"&&e[t+5]==="M"&&e[t+6]==="E"&&e[t+7]==="N"&&e[t+8]==="T"}function Ol(e,t){return e[t+1]==="!"&&e[t+2]==="A"&&e[t+3]==="T"&&e[t+4]==="T"&&e[t+5]==="L"&&e[t+6]==="I"&&e[t+7]==="S"&&e[t+8]==="T"}function Ml(e,t){return e[t+1]==="!"&&e[t+2]==="N"&&e[t+3]==="O"&&e[t+4]==="T"&&e[t+5]==="A"&&e[t+6]==="T"&&e[t+7]==="I"&&e[t+8]==="O"&&e[t+9]==="N"}function Ul(e){if(Rl.isName(e))return e;throw new Error(`Invalid entity name ${e}`)}var $l=Cl;const _l=/^[-+]?0x[a-fA-F0-9]+$/,Dl=/^([\-\+])?(0*)([0-9]*(\.[0-9]*)?)$/,Bl={hex:!0,leadingZeros:!0,decimalPoint:".",eNotation:!0};function Ll(e,t={}){if(t=Object.assign({},Bl,t),!e||typeof e!="string")return e;let r=e.trim();if(t.skipLike!==void 0&&t.skipLike.test(r))return e;if(e==="0")return 0;if(t.hex&&_l.test(r))return Hl(r,16);if(r.search(/[eE]/)!==-1){const n=r.match(/^([-\+])?(0*)([0-9]*(\.[0-9]*)?[eE][-\+]?[0-9]+)$/);if(n){if(t.leadingZeros)r=(n[1]||"")+n[3];else if(!(n[2]==="0"&&n[3][0]==="."))return e;return t.eNotation?Number(r):e}else return e}else{const n=Dl.exec(r);if(n){const s=n[1],i=n[2];let o=Fl(n[3]);if(!t.leadingZeros&&i.length>0&&s&&r[2]!=="."||!t.leadingZeros&&i.length>0&&!s&&r[1]!==".")return e;if(t.leadingZeros&&i===e)return 0;{const a=Number(r),c=""+a;return c.search(/[eE]/)!==-1?t.eNotation?a:e:r.indexOf(".")!==-1?c==="0"&&o===""||c===o||s&&c==="-"+o?a:e:i?o===c||s+o===c?a:e:r===c||r===s+c?a:e}}else return e}}function Fl(e){return e&&e.indexOf(".")!==-1&&(e=e.replace(/0+$/,""),e==="."?e="0":e[0]==="."?e="0"+e:e[e.length-1]==="."&&(e=e.substr(0,e.length-1))),e}function Hl(e,t){if(parseInt)return parseInt(e,t);if(Number.parseInt)return Number.parseInt(e,t);if(window&&window.parseInt)return window.parseInt(e,t);throw new Error("parseInt, Number.parseInt, window.parseInt are not supported")}var ql=Ll;const To=nr,mt=xl,zl=$l,jl=ql;let Vl=class{constructor(e){this.options=e,this.currentNode=null,this.tagsNodeStack=[],this.docTypeEntities={},this.lastEntities={apos:{regex:/&(apos|#39|#x27);/g,val:"'"},gt:{regex:/&(gt|#62|#x3E);/g,val:">"},lt:{regex:/&(lt|#60|#x3C);/g,val:"<"},quot:{regex:/&(quot|#34|#x22);/g,val:'"'}},this.ampEntity={regex:/&(amp|#38|#x26);/g,val:"&"},this.htmlEntities={space:{regex:/&(nbsp|#160);/g,val:" "},cent:{regex:/&(cent|#162);/g,val:"¢"},pound:{regex:/&(pound|#163);/g,val:"£"},yen:{regex:/&(yen|#165);/g,val:"¥"},euro:{regex:/&(euro|#8364);/g,val:"€"},copyright:{regex:/&(copy|#169);/g,val:"©"},reg:{regex:/&(reg|#174);/g,val:"®"},inr:{regex:/&(inr|#8377);/g,val:"₹"},num_dec:{regex:/&#([0-9]{1,7});/g,val:(t,r)=>String.fromCharCode(Number.parseInt(r,10))},num_hex:{regex:/&#x([0-9a-fA-F]{1,6});/g,val:(t,r)=>String.fromCharCode(Number.parseInt(r,16))}},this.addExternalEntities=Wl,this.parseXml=Ql,this.parseTextData=Kl,this.resolveNameSpace=Gl,this.buildAttributesMap=Xl,this.isItStopNode=td,this.replaceEntitiesValue=Jl,this.readStopNodeData=nd,this.saveTextToParentTag=ed,this.addChild=Yl}};function Wl(e){const t=Object.keys(e);for(let r=0;r<t.length;r++){const n=t[r];this.lastEntities[n]={regex:new RegExp("&"+n+";","g"),val:e[n]}}}function Kl(e,t,r,n,s,i,o){if(e!==void 0&&(this.options.trimValues&&!n&&(e=e.trim()),e.length>0)){o||(e=this.replaceEntitiesValue(e));const a=this.options.tagValueProcessor(t,e,r,s,i);return a==null?e:typeof a!=typeof e||a!==e?a:this.options.trimValues?an(e,this.options.parseTagValue,this.options.numberParseOptions):e.trim()===e?an(e,this.options.parseTagValue,this.options.numberParseOptions):e}}function Gl(e){if(this.options.removeNSPrefix){const t=e.split(":"),r=e.charAt(0)==="/"?"/":"";if(t[0]==="xmlns")return"";t.length===2&&(e=r+t[1])}return e}const Zl=new RegExp(`([^\\s=]+)\\s*(=\\s*(['"])([\\s\\S]*?)\\3)?`,"gm");function Xl(e,t,r){if(!this.options.ignoreAttributes&&typeof e=="string"){const n=To.getAllMatches(e,Zl),s=n.length,i={};for(let o=0;o<s;o++){const a=this.resolveNameSpace(n[o][1]);let c=n[o][4],u=this.options.attributeNamePrefix+a;if(a.length)if(this.options.transformAttributeName&&(u=this.options.transformAttributeName(u)),u==="__proto__"&&(u="#__proto__"),c!==void 0){this.options.trimValues&&(c=c.trim()),c=this.replaceEntitiesValue(c);const h=this.options.attributeValueProcessor(a,c,t);h==null?i[u]=c:typeof h!=typeof c||h!==c?i[u]=h:i[u]=an(c,this.options.parseAttributeValue,this.options.numberParseOptions)}else this.options.allowBooleanAttributes&&(i[u]=!0)}if(!Object.keys(i).length)return;if(this.options.attributesGroupName){const o={};return o[this.options.attributesGroupName]=i,o}return i}}const Ql=function(e){e=e.replace(/\r\n?/g,`
`);const t=new mt("!xml");let r=t,n="",s="";for(let i=0;i<e.length;i++)if(e[i]==="<")if(e[i+1]==="/"){const o=Xe(e,">",i,"Closing Tag is not closed.");let a=e.substring(i+2,o).trim();if(this.options.removeNSPrefix){const h=a.indexOf(":");h!==-1&&(a=a.substr(h+1))}this.options.transformTagName&&(a=this.options.transformTagName(a)),r&&(n=this.saveTextToParentTag(n,r,s));const c=s.substring(s.lastIndexOf(".")+1);if(a&&this.options.unpairedTags.indexOf(a)!==-1)throw new Error(`Unpaired tag can not be used as closing tag: </${a}>`);let u=0;c&&this.options.unpairedTags.indexOf(c)!==-1?(u=s.lastIndexOf(".",s.lastIndexOf(".")-1),this.tagsNodeStack.pop()):u=s.lastIndexOf("."),s=s.substring(0,u),r=this.tagsNodeStack.pop(),n="",i=o}else if(e[i+1]==="?"){let o=on(e,i,!1,"?>");if(!o)throw new Error("Pi Tag is not closed.");if(n=this.saveTextToParentTag(n,r,s),!(this.options.ignoreDeclaration&&o.tagName==="?xml"||this.options.ignorePiTags)){const a=new mt(o.tagName);a.add(this.options.textNodeName,""),o.tagName!==o.tagExp&&o.attrExpPresent&&(a[":@"]=this.buildAttributesMap(o.tagExp,s,o.tagName)),this.addChild(r,a,s)}i=o.closeIndex+1}else if(e.substr(i+1,3)==="!--"){const o=Xe(e,"-->",i+4,"Comment is not closed.");if(this.options.commentPropName){const a=e.substring(i+4,o-2);n=this.saveTextToParentTag(n,r,s),r.add(this.options.commentPropName,[{[this.options.textNodeName]:a}])}i=o}else if(e.substr(i+1,2)==="!D"){const o=zl(e,i);this.docTypeEntities=o.entities,i=o.i}else if(e.substr(i+1,2)==="!["){const o=Xe(e,"]]>",i,"CDATA is not closed.")-2,a=e.substring(i+9,o);n=this.saveTextToParentTag(n,r,s);let c=this.parseTextData(a,r.tagname,s,!0,!1,!0,!0);c==null&&(c=""),this.options.cdataPropName?r.add(this.options.cdataPropName,[{[this.options.textNodeName]:a}]):r.add(this.options.textNodeName,c),i=o+2}else{let o=on(e,i,this.options.removeNSPrefix),a=o.tagName;const c=o.rawTagName;let u=o.tagExp,h=o.attrExpPresent,d=o.closeIndex;this.options.transformTagName&&(a=this.options.transformTagName(a)),r&&n&&r.tagname!=="!xml"&&(n=this.saveTextToParentTag(n,r,s,!1));const p=r;if(p&&this.options.unpairedTags.indexOf(p.tagname)!==-1&&(r=this.tagsNodeStack.pop(),s=s.substring(0,s.lastIndexOf("."))),a!==t.tagname&&(s+=s?"."+a:a),this.isItStopNode(this.options.stopNodes,s,a)){let g="";if(u.length>0&&u.lastIndexOf("/")===u.length-1)a[a.length-1]==="/"?(a=a.substr(0,a.length-1),s=s.substr(0,s.length-1),u=a):u=u.substr(0,u.length-1),i=o.closeIndex;else if(this.options.unpairedTags.indexOf(a)!==-1)i=o.closeIndex;else{const R=this.readStopNodeData(e,c,d+1);if(!R)throw new Error(`Unexpected end of ${c}`);i=R.i,g=R.tagContent}const S=new mt(a);a!==u&&h&&(S[":@"]=this.buildAttributesMap(u,s,a)),g&&(g=this.parseTextData(g,a,s,!0,h,!0,!0)),s=s.substr(0,s.lastIndexOf(".")),S.add(this.options.textNodeName,g),this.addChild(r,S,s)}else{if(u.length>0&&u.lastIndexOf("/")===u.length-1){a[a.length-1]==="/"?(a=a.substr(0,a.length-1),s=s.substr(0,s.length-1),u=a):u=u.substr(0,u.length-1),this.options.transformTagName&&(a=this.options.transformTagName(a));const g=new mt(a);a!==u&&h&&(g[":@"]=this.buildAttributesMap(u,s,a)),this.addChild(r,g,s),s=s.substr(0,s.lastIndexOf("."))}else{const g=new mt(a);this.tagsNodeStack.push(r),a!==u&&h&&(g[":@"]=this.buildAttributesMap(u,s,a)),this.addChild(r,g,s),r=g}n="",i=d}}else n+=e[i];return t.child};function Yl(e,t,r){const n=this.options.updateTag(t.tagname,r,t[":@"]);n===!1||(typeof n=="string"&&(t.tagname=n),e.addChild(t))}const Jl=function(e){if(this.options.processEntities){for(let t in this.docTypeEntities){const r=this.docTypeEntities[t];e=e.replace(r.regx,r.val)}for(let t in this.lastEntities){const r=this.lastEntities[t];e=e.replace(r.regex,r.val)}if(this.options.htmlEntities)for(let t in this.htmlEntities){const r=this.htmlEntities[t];e=e.replace(r.regex,r.val)}e=e.replace(this.ampEntity.regex,this.ampEntity.val)}return e};function ed(e,t,r,n){return e&&(n===void 0&&(n=Object.keys(t.child).length===0),e=this.parseTextData(e,t.tagname,r,!1,t[":@"]?Object.keys(t[":@"]).length!==0:!1,n),e!==void 0&&e!==""&&t.add(this.options.textNodeName,e),e=""),e}function td(e,t,r){const n="*."+r;for(const s in e){const i=e[s];if(n===i||t===i)return!0}return!1}function rd(e,t,r=">"){let n,s="";for(let i=t;i<e.length;i++){let o=e[i];if(n)o===n&&(n="");else if(o==='"'||o==="'")n=o;else if(o===r[0])if(r[1]){if(e[i+1]===r[1])return{data:s,index:i}}else return{data:s,index:i};else o==="	"&&(o=" ");s+=o}}function Xe(e,t,r,n){const s=e.indexOf(t,r);if(s===-1)throw new Error(n);return s+t.length-1}function on(e,t,r,n=">"){const s=rd(e,t+1,n);if(!s)return;let i=s.data;const o=s.index,a=i.search(/\s/);let c=i,u=!0;a!==-1&&(c=i.substring(0,a),i=i.substring(a+1).trimStart());const h=c;if(r){const d=c.indexOf(":");d!==-1&&(c=c.substr(d+1),u=c!==s.data.substr(d+1))}return{tagName:c,tagExp:i,closeIndex:o,attrExpPresent:u,rawTagName:h}}function nd(e,t,r){const n=r;let s=1;for(;r<e.length;r++)if(e[r]==="<")if(e[r+1]==="/"){const i=Xe(e,">",r,`${t} is not closed`);if(e.substring(r+2,i).trim()===t&&(s--,s===0))return{tagContent:e.substring(n,r),i};r=i}else if(e[r+1]==="?")r=Xe(e,"?>",r+1,"StopNode is not closed.");else if(e.substr(r+1,3)==="!--")r=Xe(e,"-->",r+3,"StopNode is not closed.");else if(e.substr(r+1,2)==="![")r=Xe(e,"]]>",r,"StopNode is not closed.")-2;else{const i=on(e,r,">");i&&((i&&i.tagName)===t&&i.tagExp[i.tagExp.length-1]!=="/"&&s++,r=i.closeIndex)}}function an(e,t,r){if(t&&typeof e=="string"){const n=e.trim();return n==="true"?!0:n==="false"?!1:jl(e,r)}else return To.isExist(e)?e:""}var sd=Vl,Io={};function id(e,t){return No(e,t)}function No(e,t,r){let n;const s={};for(let i=0;i<e.length;i++){const o=e[i],a=od(o);let c="";if(r===void 0?c=a:c=r+"."+a,a===t.textNodeName)n===void 0?n=o[a]:n+=""+o[a];else{if(a===void 0)continue;if(o[a]){let u=No(o[a],t,c);const h=cd(u,t);o[":@"]?ad(u,o[":@"],c,t):Object.keys(u).length===1&&u[t.textNodeName]!==void 0&&!t.alwaysCreateTextNode?u=u[t.textNodeName]:Object.keys(u).length===0&&(t.alwaysCreateTextNode?u[t.textNodeName]="":u=""),s[a]!==void 0&&s.hasOwnProperty(a)?(Array.isArray(s[a])||(s[a]=[s[a]]),s[a].push(u)):t.isArray(a,c,h)?s[a]=[u]:s[a]=u}}}return typeof n=="string"?n.length>0&&(s[t.textNodeName]=n):n!==void 0&&(s[t.textNodeName]=n),s}function od(e){const t=Object.keys(e);for(let r=0;r<t.length;r++){const n=t[r];if(n!==":@")return n}}function ad(e,t,r,n){if(t){const s=Object.keys(t),i=s.length;for(let o=0;o<i;o++){const a=s[o];n.isArray(a,r+"."+a,!0,!0)?e[a]=[t[a]]:e[a]=t[a]}}}function cd(e,t){const{textNodeName:r}=t,n=Object.keys(e).length;return!!(n===0||n===1&&(e[r]||typeof e[r]=="boolean"||e[r]===0))}Io.prettify=id;const{buildOptions:ud}=En,ld=sd,{prettify:dd}=Io,hd=Co;let fd=class{constructor(e){this.externalEntities={},this.options=ud(e)}parse(e,t){if(typeof e!="string")if(e.toString)e=e.toString();else throw new Error("XML data is accepted in String or Bytes[] form.");if(t){t===!0&&(t={});const s=hd.validate(e,t);if(s!==!0)throw Error(`${s.err.msg}:${s.err.line}:${s.err.col}`)}const r=new ld(this.options);r.addExternalEntities(this.externalEntities);const n=r.parseXml(e);return this.options.preserveOrder||n===void 0?n:dd(n,this.options)}addEntity(e,t){if(t.indexOf("&")!==-1)throw new Error("Entity value can't have '&'");if(e.indexOf("&")!==-1||e.indexOf(";")!==-1)throw new Error("An entity must be set without '&' and ';'. Eg. use '#xD' for '&#xD;'");if(t==="&")throw new Error("An entity with value '&' is not permitted");this.externalEntities[e]=t}};var pd=fd;const gd=pd;var md={XMLParser:gd};const Oo=(e,t)=>fl(e,t).then(r=>{if(r.length){const n=new md.XMLParser({attributeNamePrefix:"",htmlEntities:!0,ignoreAttributes:!1,ignoreDeclaration:!0,parseTagValue:!1,trimValues:!1,tagValueProcessor:(c,u)=>u.trim()===""&&u.includes(`
`)?"":void 0});n.addEntity("#xD","\r"),n.addEntity("#10",`
`);let s;try{s=n.parse(r,!0)}catch(c){throw c&&typeof c=="object"&&Object.defineProperty(c,"$responseBodyText",{value:r}),c}const i="#text",o=Object.keys(s)[0],a=s[o];return a[i]&&(a[o]=a[i],delete a[i]),Ro(a)}return{}}),yd=async(e,t)=>{const r=await Oo(e,t);return r.Error&&(r.Error.message=r.Error.message??r.Error.Message),r},wd=(e,t)=>{var r;if(((r=t==null?void 0:t.Error)==null?void 0:r.Code)!==void 0)return t.Error.Code;if((t==null?void 0:t.Code)!==void 0)return t.Code;if(e.statusCode==404)return"NotFound"},cn=[F.CRC32,F.CRC32C,F.CRC64NVME,F.SHA1,F.SHA256],bd=[F.SHA256,F.SHA1,F.CRC32,F.CRC32C,F.CRC64NVME],vd=(e,{requestChecksumRequired:t,requestAlgorithmMember:r,requestChecksumCalculation:n})=>{if(!r)return n===ct.WHEN_SUPPORTED||t?en:void 0;if(!e[r])return;const s=e[r];if(!cn.includes(s))throw new Error(`The checksum algorithm "${s}" is not supported by the client. Select one of ${cn}.`);return s},An=e=>e===F.MD5?"content-md5":`x-amz-checksum-${e.toLowerCase()}`,Sd=(e,t)=>{const r=e.toLowerCase();for(const n of Object.keys(t))if(r===n.toLowerCase())return!0;return!1},Ed=(e,t)=>{const r=e.toLowerCase();for(const n of Object.keys(t))if(n.toLowerCase().startsWith(r))return!0;return!1},Mo=e=>e!==void 0&&typeof e!="string"&&!ArrayBuffer.isView(e)&&!mo(e);function kn(e,t,r,n){function s(i){return i instanceof r?i:new r(function(o){o(i)})}return new(r||(r=Promise))(function(i,o){function a(h){try{u(n.next(h))}catch(d){o(d)}}function c(h){try{u(n.throw(h))}catch(d){o(d)}}function u(h){h.done?i(h.value):s(h.value).then(a,c)}u((n=n.apply(e,[])).next())})}function xn(e,t){var r={label:0,sent:function(){if(i[0]&1)throw i[1];return i[1]},trys:[],ops:[]},n,s,i,o=Object.create((typeof Iterator=="function"?Iterator:Object).prototype);return o.next=a(0),o.throw=a(1),o.return=a(2),typeof Symbol=="function"&&(o[Symbol.iterator]=function(){return this}),o;function a(u){return function(h){return c([u,h])}}function c(u){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,u[0]&&(r=0)),r;)try{if(n=1,s&&(i=u[0]&2?s.return:u[0]?s.throw||((i=s.return)&&i.call(s),0):s.next)&&!(i=i.call(s,u[1])).done)return i;switch(s=0,i&&(u=[u[0]&2,i.value]),u[0]){case 0:case 1:i=u;break;case 4:return r.label++,{value:u[1],done:!1};case 5:r.label++,s=u[1],u=[0];continue;case 7:u=r.ops.pop(),r.trys.pop();continue;default:if(i=r.trys,!(i=i.length>0&&i[i.length-1])&&(u[0]===6||u[0]===2)){r=0;continue}if(u[0]===3&&(!i||u[1]>i[0]&&u[1]<i[3])){r.label=u[1];break}if(u[0]===6&&r.label<i[1]){r.label=i[1],i=u;break}if(i&&r.label<i[2]){r.label=i[2],r.ops.push(u);break}i[2]&&r.ops.pop(),r.trys.pop();continue}u=t.call(e,r)}catch(h){u=[6,h],s=0}finally{n=i=0}if(u[0]&5)throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}function Uo(e){var t=typeof Symbol=="function"&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}const $o=e=>new TextEncoder().encode(e);var Ad=typeof Buffer<"u"&&Buffer.from?function(e){return Buffer.from(e,"utf8")}:$o;function je(e){return e instanceof Uint8Array?e:typeof e=="string"?Ad(e):ArrayBuffer.isView(e)?new Uint8Array(e.buffer,e.byteOffset,e.byteLength/Uint8Array.BYTES_PER_ELEMENT):new Uint8Array(e)}function Tt(e){return typeof e=="string"?e.length===0:e.byteLength===0}function _o(e){return new Uint8Array([(e&4278190080)>>24,(e&16711680)>>16,(e&65280)>>8,e&255])}function Do(e){if(!Uint32Array.from){for(var t=new Uint32Array(e.length),r=0;r<e.length;)t[r]=e[r],r+=1;return t}return Uint32Array.from(e)}var kd=function(){function e(){this.crc32c=new Ss}return e.prototype.update=function(t){Tt(t)||this.crc32c.update(je(t))},e.prototype.digest=function(){return kn(this,void 0,void 0,function(){return xn(this,function(t){return[2,_o(this.crc32c.digest())]})})},e.prototype.reset=function(){this.crc32c=new Ss},e}(),Ss=function(){function e(){this.checksum=4294967295}return e.prototype.update=function(t){var r,n;try{for(var s=Uo(t),i=s.next();!i.done;i=s.next()){var o=i.value;this.checksum=this.checksum>>>8^Rd[(this.checksum^o)&255]}}catch(a){r={error:a}}finally{try{i&&!i.done&&(n=s.return)&&n.call(s)}finally{if(r)throw r.error}}return this},e.prototype.digest=function(){return(this.checksum^4294967295)>>>0},e}(),xd=[0,4067132163,3778769143,324072436,3348797215,904991772,648144872,3570033899,2329499855,2024987596,1809983544,2575936315,1296289744,3207089363,2893594407,1578318884,274646895,3795141740,4049975192,51262619,3619967088,632279923,922689671,3298075524,2592579488,1760304291,2075979607,2312596564,1562183871,2943781820,3156637768,1313733451,549293790,3537243613,3246849577,871202090,3878099393,357341890,102525238,4101499445,2858735121,1477399826,1264559846,3107202533,1845379342,2677391885,2361733625,2125378298,820201905,3263744690,3520608582,598981189,4151959214,85089709,373468761,3827903834,3124367742,1213305469,1526817161,2842354314,2107672161,2412447074,2627466902,1861252501,1098587580,3004210879,2688576843,1378610760,2262928035,1955203488,1742404180,2511436119,3416409459,969524848,714683780,3639785095,205050476,4266873199,3976438427,526918040,1361435347,2739821008,2954799652,1114974503,2529119692,1691668175,2005155131,2247081528,3690758684,697762079,986182379,3366744552,476452099,3993867776,4250756596,255256311,1640403810,2477592673,2164122517,1922457750,2791048317,1412925310,1197962378,3037525897,3944729517,427051182,170179418,4165941337,746937522,3740196785,3451792453,1070968646,1905808397,2213795598,2426610938,1657317369,3053634322,1147748369,1463399397,2773627110,4215344322,153784257,444234805,3893493558,1021025245,3467647198,3722505002,797665321,2197175160,1889384571,1674398607,2443626636,1164749927,3070701412,2757221520,1446797203,137323447,4198817972,3910406976,461344835,3484808360,1037989803,781091935,3705997148,2460548119,1623424788,1939049696,2180517859,1429367560,2807687179,3020495871,1180866812,410100952,3927582683,4182430767,186734380,3756733383,763408580,1053836080,3434856499,2722870694,1344288421,1131464017,2971354706,1708204729,2545590714,2229949006,1988219213,680717673,3673779818,3383336350,1002577565,4010310262,493091189,238226049,4233660802,2987750089,1082061258,1395524158,2705686845,1972364758,2279892693,2494862625,1725896226,952904198,3399985413,3656866545,731699698,4283874585,222117402,510512622,3959836397,3280807620,837199303,582374963,3504198960,68661723,4135334616,3844915500,390545967,1230274059,3141532936,2825850620,1510247935,2395924756,2091215383,1878366691,2644384480,3553878443,565732008,854102364,3229815391,340358836,3861050807,4117890627,119113024,1493875044,2875275879,3090270611,1247431312,2660249211,1828433272,2141937292,2378227087,3811616794,291187481,34330861,4032846830,615137029,3603020806,3314634738,939183345,1776939221,2609017814,2295496738,2058945313,2926798794,1545135305,1330124605,3173225534,4084100981,17165430,307568514,3762199681,888469610,3332340585,3587147933,665062302,2042050490,2346497209,2559330125,1793573966,3190661285,1279665062,1595330642,2910671697],Rd=Do(xd),Cd=function(){function e(){this.crc32=new Gt}return e.prototype.update=function(t){Tt(t)||this.crc32.update(je(t))},e.prototype.digest=function(){return kn(this,void 0,void 0,function(){return xn(this,function(t){return[2,_o(this.crc32.digest())]})})},e.prototype.reset=function(){this.crc32=new Gt},e}(),Gt=function(){function e(){this.checksum=4294967295}return e.prototype.update=function(t){var r,n;try{for(var s=Uo(t),i=s.next();!i.done;i=s.next()){var o=i.value;this.checksum=this.checksum>>>8^Td[(this.checksum^o)&255]}}catch(a){r={error:a}}finally{try{i&&!i.done&&(n=s.return)&&n.call(s)}finally{if(r)throw r.error}}return this},e.prototype.digest=function(){return(this.checksum^4294967295)>>>0},e}(),Pd=[0,1996959894,3993919788,2567524794,124634137,1886057615,3915621685,2657392035,249268274,2044508324,3772115230,2547177864,162941995,2125561021,3887607047,2428444049,498536548,1789927666,4089016648,2227061214,450548861,1843258603,4107580753,2211677639,325883990,1684777152,4251122042,2321926636,335633487,1661365465,4195302755,2366115317,997073096,1281953886,3579855332,2724688242,1006888145,1258607687,3524101629,2768942443,901097722,1119000684,3686517206,2898065728,853044451,1172266101,3705015759,2882616665,651767980,1373503546,3369554304,3218104598,565507253,1454621731,3485111705,3099436303,671266974,1594198024,3322730930,2970347812,795835527,1483230225,3244367275,3060149565,1994146192,31158534,2563907772,4023717930,1907459465,112637215,2680153253,3904427059,2013776290,251722036,2517215374,3775830040,2137656763,141376813,2439277719,3865271297,1802195444,476864866,2238001368,4066508878,1812370925,453092731,2181625025,4111451223,1706088902,314042704,2344532202,4240017532,1658658271,366619977,2362670323,4224994405,1303535960,984961486,2747007092,3569037538,1256170817,1037604311,2765210733,3554079995,1131014506,879679996,2909243462,3663771856,1141124467,855842277,2852801631,3708648649,1342533948,654459306,3188396048,3373015174,1466479909,544179635,3110523913,3462522015,1591671054,702138776,2966460450,3352799412,1504918807,783551873,3082640443,3233442989,3988292384,2596254646,62317068,1957810842,3939845945,2647816111,81470997,1943803523,3814918930,2489596804,225274430,2053790376,3826175755,2466906013,167816743,2097651377,4027552580,2265490386,503444072,1762050814,4150417245,2154129355,426522225,1852507879,4275313526,2312317920,282753626,1742555852,4189708143,2394877945,397917763,1622183637,3604390888,2714866558,953729732,1340076626,3518719985,2797360999,1068828381,1219638859,3624741850,2936675148,906185462,1090812512,3747672003,2825379669,829329135,1181335161,3412177804,3160834842,628085408,1382605366,3423369109,3138078467,570562233,1426400815,3317316542,2998733608,733239954,1555261956,3268935591,3050360625,752459403,1541320221,2607071920,3965973030,1969922972,40735498,2617837225,3943577151,1913087877,83908371,2512341634,3803740692,2075208622,213261112,2463272603,3855990285,2094854071,198958881,2262029012,4057260610,1759359992,534414190,2176718541,4139329115,1873836001,414664567,2282248934,4279200368,1711684554,285281116,2405801727,4167216745,1634467795,376229701,2685067896,3608007406,1308918612,956543938,2808555105,3495958263,1231636301,1047427035,2932959818,3654703836,1088359270,936918e3,2847714899,3736837829,1202900863,817233897,3183342108,3401237130,1404277552,615818150,3134207493,3453421203,1423857449,601450431,3009837614,3294710456,1567103746,711928724,3020668471,3272380065,1510334235,755167117],Td=Do(Pd);const Id=()=>Cd,Bo=(e,t)=>{switch(e){case F.MD5:return t.md5;case F.CRC32:return Id();case F.CRC32C:return kd;case F.CRC64NVME:throw new Error(`Please check whether you have installed the "@aws-sdk/crc64-nvme-crt" package explicitly. 
You must also register the package by calling [require("@aws-sdk/crc64-nvme-crt");] or an ESM equivalent such as [import "@aws-sdk/crc64-nvme-crt";]. 
For more information please go to https://github.com/aws/aws-sdk-js-v3#functionality-requiring-aws-common-runtime-crt`);case F.SHA1:return t.sha1;case F.SHA256:return t.sha256;default:throw new Error(`Unsupported checksum algorithm: ${e}`)}},Lo=(e,t)=>{const r=new e;return r.update(at(t||"")),r.digest()},Nd={name:"flexibleChecksumsMiddleware",step:"build",tags:["BODY_CHECKSUM"],override:!0},Od=(e,t)=>(r,n)=>async s=>{if(!ee.isInstance(s.request)||Ed("x-amz-checksum-",s.request.headers))return r(s);const{request:i,input:o}=s,{body:a,headers:c}=i,{base64Encoder:u,streamHasher:h}=e,{requestChecksumRequired:d,requestAlgorithmMember:p}=t,g=await e.requestChecksumCalculation(),S=p==null?void 0:p.name,R=p==null?void 0:p.httpHeader;S&&!o[S]&&(g===ct.WHEN_SUPPORTED||d)&&(o[S]=en,R&&(c[R]=en));const x=vd(o,{requestChecksumRequired:d,requestAlgorithmMember:p==null?void 0:p.name,requestChecksumCalculation:g});let _=a,M=c;if(x){switch(x){case F.CRC32:Y(n,"FLEXIBLE_CHECKSUMS_REQ_CRC32","U");break;case F.CRC32C:Y(n,"FLEXIBLE_CHECKSUMS_REQ_CRC32C","V");break;case F.CRC64NVME:Y(n,"FLEXIBLE_CHECKSUMS_REQ_CRC64","W");break;case F.SHA1:Y(n,"FLEXIBLE_CHECKSUMS_REQ_SHA1","X");break;case F.SHA256:Y(n,"FLEXIBLE_CHECKSUMS_REQ_SHA256","Y");break}const j=An(x),z=Bo(x,e);if(Mo(a)){const{getAwsChunkedEncodingStream:Z,bodyLengthChecker:We}=e;_=Z(typeof e.requestStreamBufferSize=="number"&&e.requestStreamBufferSize>=8*1024?_c(a,e.requestStreamBufferSize,n.logger):a,{base64Encoder:u,bodyLengthChecker:We,checksumLocationName:j,checksumAlgorithmFn:z,streamHasher:h}),M={...c,"content-encoding":c["content-encoding"]?`${c["content-encoding"]},aws-chunked`:"aws-chunked","transfer-encoding":"chunked","x-amz-decoded-content-length":c["content-length"],"x-amz-content-sha256":"STREAMING-UNSIGNED-PAYLOAD-TRAILER","x-amz-trailer":j},delete M["content-length"]}else if(!Sd(j,c)){const Z=await Lo(z,a);M={...c,[j]:u(Z)}}}return await r({...s,request:{...i,headers:M,body:_}})},Md={name:"flexibleChecksumsInputMiddleware",toMiddleware:"serializerMiddleware",relation:"before",tags:["BODY_CHECKSUM"],override:!0},Ud=(e,t)=>(r,n)=>async s=>{const i=s.input,{requestValidationModeMember:o}=t,a=await e.requestChecksumCalculation(),c=await e.responseChecksumValidation();switch(a){case ct.WHEN_REQUIRED:Y(n,"FLEXIBLE_CHECKSUMS_REQ_WHEN_REQUIRED","a");break;case ct.WHEN_SUPPORTED:Y(n,"FLEXIBLE_CHECKSUMS_REQ_WHEN_SUPPORTED","Z");break}switch(c){case pr.WHEN_REQUIRED:Y(n,"FLEXIBLE_CHECKSUMS_RES_WHEN_REQUIRED","c");break;case pr.WHEN_SUPPORTED:Y(n,"FLEXIBLE_CHECKSUMS_RES_WHEN_SUPPORTED","b");break}return o&&!i[o]&&c===pr.WHEN_SUPPORTED&&(i[o]="ENABLED"),r(s)},Fo=(e=[])=>{const t=[];for(const r of bd)!e.includes(r)||!cn.includes(r)||t.push(r);return t},$d=e=>{const t=e.lastIndexOf("-");if(t!==-1){const r=e.slice(t+1);if(!r.startsWith("0")){const n=parseInt(r,10);if(!isNaN(n)&&n>=1&&n<=1e4)return!0}}return!1},_d=async(e,{checksumAlgorithmFn:t,base64Encoder:r})=>r(await Lo(t,e)),Dd=async(e,{config:t,responseAlgorithms:r,logger:n})=>{const s=Fo(r),{body:i,headers:o}=e;for(const a of s){const c=An(a),u=o[c];if(u){let h;try{h=Bo(a,t)}catch(g){if(a===F.CRC64NVME){n==null||n.warn(`Skipping ${F.CRC64NVME} checksum validation: ${g.message}`);continue}throw g}const{base64Encoder:d}=t;if(Mo(i)){e.body=Mc({expectedChecksum:u,checksumSourceLocation:c,checksum:new h,source:i,base64Encoder:d});return}const p=await _d(i,{checksumAlgorithmFn:h,base64Encoder:d});if(p===u)break;throw new Error(`Checksum mismatch: expected "${p}" but received "${u}" in response header "${c}".`)}}},Bd={name:"flexibleChecksumsResponseMiddleware",toMiddleware:"deserializerMiddleware",relation:"after",tags:["BODY_CHECKSUM"],override:!0},Ld=(e,t)=>(r,n)=>async s=>{if(!ee.isInstance(s.request))return r(s);const i=s.input,o=await r(s),a=o.response,{requestValidationModeMember:c,responseAlgorithms:u}=t;if(c&&i[c]==="ENABLED"){const{clientName:h,commandName:d}=n;if(h==="S3Client"&&d==="GetObjectCommand"&&Fo(u).every(p=>{const g=An(p),S=a.headers[g];return!S||$d(S)}))return o;await Dd(a,{config:e,responseAlgorithms:u,logger:n.logger})}return o},Fd=(e,t)=>({applyToStack:r=>{r.add(Od(e,t),Nd),r.addRelativeTo(Ud(e,t),Md),r.addRelativeTo(Ld(e,t),Bd)}}),Hd=e=>({...e,requestChecksumCalculation:Ne(e.requestChecksumCalculation??hc),responseChecksumValidation:Ne(e.responseChecksumValidation??fc),requestStreamBufferSize:Number(e.requestStreamBufferSize??0)}),qd=e=>t=>async r=>{if(!ee.isInstance(r.request))return t(r);const{request:n}=r,{handlerProtocol:s=""}=e.requestHandler.metadata||{};if(s.indexOf("h2")>=0&&!n.headers[":authority"])delete n.headers.host,n.headers[":authority"]=n.hostname+(n.port?":"+n.port:"");else if(!n.headers.host){let i=n.hostname;n.port!=null&&(i+=`:${n.port}`),n.headers.host=i}return t(r)},zd={name:"hostHeaderMiddleware",step:"build",priority:"low",tags:["HOST"],override:!0},jd=e=>({applyToStack:t=>{t.add(qd(e),zd)}}),Vd=()=>(e,t)=>async r=>{var n,s;try{const i=await e(r),{clientName:o,commandName:a,logger:c,dynamoDbDocumentClientOptions:u={}}=t,{overrideInputFilterSensitiveLog:h,overrideOutputFilterSensitiveLog:d}=u,p=h??t.inputFilterSensitiveLog,g=d??t.outputFilterSensitiveLog,{$metadata:S,...R}=i.output;return(n=c==null?void 0:c.info)==null||n.call(c,{clientName:o,commandName:a,input:p(r.input),output:g(R),metadata:S}),i}catch(i){const{clientName:o,commandName:a,logger:c,dynamoDbDocumentClientOptions:u={}}=t,{overrideInputFilterSensitiveLog:h}=u,d=h??t.inputFilterSensitiveLog;throw(s=c==null?void 0:c.error)==null||s.call(c,{clientName:o,commandName:a,input:d(r.input),error:i,metadata:i.$metadata}),i}},Wd={name:"loggerMiddleware",tags:["LOGGER"],step:"initialize",override:!0},Kd=e=>({applyToStack:t=>{t.add(Vd(),Wd)}}),Es="X-Amzn-Trace-Id",Gd="AWS_LAMBDA_FUNCTION_NAME",Zd="_X_AMZN_TRACE_ID",Xd=e=>t=>async r=>{const{request:n}=r;if(!ee.isInstance(n)||e.runtime!=="node"||n.headers.hasOwnProperty(Es))return t(r);const s=Zn[Gd],i=Zn[Zd],o=a=>typeof a=="string"&&a.length>0;return o(s)&&o(i)&&(n.headers[Es]=i),t({...r,request:n})},Qd={step:"build",tags:["RECURSION_DETECTION"],name:"recursionDetectionMiddleware",override:!0,priority:"low"},Yd=e=>({applyToStack:t=>{t.add(Xd(e),Qd)}}),Jd="content-length",eh="x-amz-decoded-content-length";function th(){return(e,t)=>async r=>{var n;const{request:s}=r;return ee.isInstance(s)&&!(Jd in s.headers)&&!(eh in s.headers)&&typeof((n=t==null?void 0:t.logger)==null?void 0:n.warn)=="function"&&!(t.logger instanceof vn)&&t.logger.warn("Are you using a Stream of unknown length as the Body of a PutObject request? Consider using Upload instead from @aws-sdk/lib-storage."),e({...r})}}const rh={step:"finalizeRequest",tags:["CHECK_CONTENT_LENGTH_HEADER"],name:"getCheckContentLengthHeaderPlugin",override:!0},nh=e=>({applyToStack:t=>{t.add(th(),rh)}}),sh=e=>(t,r)=>async n=>{const s=await e.region(),i=e.region;let o=()=>{};r.__s3RegionRedirect&&(Object.defineProperty(e,"region",{writable:!1,value:async()=>r.__s3RegionRedirect}),o=()=>Object.defineProperty(e,"region",{writable:!0,value:i}));try{const a=await t(n);if(r.__s3RegionRedirect){o();const c=await e.region();if(s!==c)throw new Error("Region was not restored following S3 region redirect.")}return a}catch(a){throw o(),a}},ih={tags:["REGION_REDIRECT","S3"],name:"regionRedirectEndpointMiddleware",override:!0,relation:"before",toMiddleware:"endpointV2Middleware"};function oh(e){return(t,r)=>async n=>{var s,i,o;try{return await t(n)}catch(a){if(e.followRegionRedirects&&(((s=a==null?void 0:a.$metadata)==null?void 0:s.httpStatusCode)===301||((i=a==null?void 0:a.$metadata)==null?void 0:i.httpStatusCode)===400&&(a==null?void 0:a.name)==="IllegalLocationConstraintException")){try{const c=a.$response.headers["x-amz-bucket-region"];(o=r.logger)==null||o.debug(`Redirecting from ${await e.region()} to ${c}`),r.__s3RegionRedirect=c}catch(c){throw new Error("Region redirect failed: "+c)}return t(n)}throw a}}}const ah={step:"initialize",tags:["REGION_REDIRECT","S3"],name:"regionRedirectMiddleware",override:!0},ch=e=>({applyToStack:t=>{t.add(oh(e),ah),t.addRelativeTo(sh(e),ih)}}),Ho=class qo{constructor(t={}){N(this,"data"),N(this,"lastPurgeTime",Date.now()),this.data=t}get(t){const r=this.data[t];if(r)return r}set(t,r){return this.data[t]=r,r}delete(t){delete this.data[t]}async purgeExpired(){const t=Date.now();if(!(this.lastPurgeTime+qo.EXPIRED_CREDENTIAL_PURGE_INTERVAL_MS>t))for(const r in this.data){const n=this.data[r];if(!n.isRefreshing){const s=await n.identity;s.expiration&&s.expiration.getTime()<t&&delete this.data[r]}}}};N(Ho,"EXPIRED_CREDENTIAL_PURGE_INTERVAL_MS",3e4);let uh=Ho;class Sr{constructor(t,r=!1,n=Date.now()){N(this,"_identity"),N(this,"isRefreshing"),N(this,"accessed"),this._identity=t,this.isRefreshing=r,this.accessed=n}get identity(){return this.accessed=Date.now(),this._identity}}const zo=class jo{constructor(t,r=new uh){N(this,"createSessionFn"),N(this,"cache"),this.createSessionFn=t,this.cache=r}async getS3ExpressIdentity(t,r){const n=r.Bucket,{cache:s}=this,i=s.get(n);return i?i.identity.then(o=>{var a,c;return(((a=o.expiration)==null?void 0:a.getTime())??0)<Date.now()?s.set(n,new Sr(this.getIdentity(n))).identity:((((c=o.expiration)==null?void 0:c.getTime())??0)<Date.now()+jo.REFRESH_WINDOW_MS&&!i.isRefreshing&&(i.isRefreshing=!0,this.getIdentity(n).then(u=>{s.set(n,new Sr(Promise.resolve(u)))})),o)}):s.set(n,new Sr(this.getIdentity(n))).identity}async getIdentity(t){var r,n;await this.cache.purgeExpired().catch(i=>{});const s=await this.createSessionFn(t);if(!((r=s.Credentials)!=null&&r.AccessKeyId)||!((n=s.Credentials)!=null&&n.SecretAccessKey))throw new Error("s3#createSession response credential missing AccessKeyId or SecretAccessKey.");return{accessKeyId:s.Credentials.AccessKeyId,secretAccessKey:s.Credentials.SecretAccessKey,sessionToken:s.Credentials.SessionToken,expiration:s.Credentials.Expiration?new Date(s.Credentials.Expiration):void 0}}};N(zo,"REFRESH_WINDOW_MS",6e4);let lh=zo;var As;(function(e){e.ENV="env",e.CONFIG="shared config entry"})(As||(As={}));const dh="Directory",hh="S3Express",fh="sigv4-s3express",un="X-Amz-S3session-Token",ln=un.toLowerCase();class ph extends sn{async signWithCredentials(t,r,n){const s=ks(r);t.headers[ln]=r.sessionToken;const i=this;return xs(i,s),i.signRequest(t,n??{})}async presignWithCredentials(t,r,n){const s=ks(r);return delete t.headers[ln],t.headers[un]=r.sessionToken,t.query=t.query??{},t.query[un]=r.sessionToken,xs(this,s),this.presign(t,n)}}function ks(e){return{accessKeyId:e.accessKeyId,secretAccessKey:e.secretAccessKey,expiration:e.expiration}}function xs(e,t){const r=setTimeout(()=>{throw new Error("SignatureV4S3Express credential override was created but not called.")},10),n=e.credentialProvider,s=()=>(clearTimeout(r),e.credentialProvider=n,Promise.resolve(t));e.credentialProvider=s}const gh=e=>(t,r)=>async n=>{var s,i,o,a,c;if(r.endpointV2){const u=r.endpointV2,h=((o=(i=(s=u.properties)==null?void 0:s.authSchemes)==null?void 0:i[0])==null?void 0:o.name)===fh;if((((a=u.properties)==null?void 0:a.backend)===hh||((c=u.properties)==null?void 0:c.bucketType)===dh)&&(Y(r,"S3_EXPRESS_BUCKET","J"),r.isS3ExpressBucket=!0),h){const d=n.input.Bucket;if(d){const p=await e.s3ExpressIdentityProvider.getS3ExpressIdentity(await e.credentials(),{Bucket:d});r.s3ExpressIdentity=p,ee.isInstance(n.request)&&p.sessionToken&&(n.request.headers[ln]=p.sessionToken)}}}return t(n)},mh={name:"s3ExpressMiddleware",step:"build",tags:["S3","S3_EXPRESS"],override:!0},yh=e=>({applyToStack:t=>{t.add(gh(e),mh)}}),wh=async(e,t,r,n)=>{const s=await n.signWithCredentials(r,e,{});if(s.headers["X-Amz-Security-Token"]||s.headers["x-amz-security-token"])throw new Error("X-Amz-Security-Token must not be set for s3-express requests.");return s},bh=e=>t=>{throw t},vh=(e,t)=>{},Sh=e=>(t,r)=>async n=>{if(!ee.isInstance(n.request))return t(n);const s=ft(r).selectedHttpAuthScheme;if(!s)throw new Error("No HttpAuthScheme was selected: unable to sign request");const{httpAuthOption:{signingProperties:i={}},identity:o,signer:a}=s;let c;r.s3ExpressIdentity?c=await wh(r.s3ExpressIdentity,i,n.request,await e.signer()):c=await a.sign(n.request,o,i);const u=await t({...n,request:c}).catch((a.errorHandler||bh)(i));return(a.successHandler||vh)(u.response,i),u},Eh=e=>({applyToStack:t=>{t.addRelativeTo(Sh(e),ro)}}),Ah=(e,{session:t})=>{const[r,n]=t;return{...e,forcePathStyle:e.forcePathStyle??!1,useAccelerateEndpoint:e.useAccelerateEndpoint??!1,disableMultiregionAccessPoints:e.disableMultiregionAccessPoints??!1,followRegionRedirects:e.followRegionRedirects??!1,s3ExpressIdentityProvider:e.s3ExpressIdentityProvider??new lh(async s=>r().send(new n({Bucket:s}))),bucketEndpoint:e.bucketEndpoint??!1}},kh={CopyObjectCommand:!0,UploadPartCopyCommand:!0,CompleteMultipartUploadCommand:!0},xh=3e3,Rh=e=>(t,r)=>async n=>{const s=await t(n),{response:i}=s;if(!Pt.isInstance(i))return s;const{statusCode:o,body:a}=i;if(o<200||o>=300||!(typeof(a==null?void 0:a.stream)=="function"||typeof(a==null?void 0:a.pipe)=="function"||typeof(a==null?void 0:a.tee)=="function"))return s;let c=a,u=a;a&&typeof a=="object"&&!(a instanceof Uint8Array)&&([c,u]=await Gc(a)),i.body=u;const h=await Ch(c,{streamCollector:async p=>Fc(p,xh)});typeof(c==null?void 0:c.destroy)=="function"&&c.destroy();const d=e.utf8Encoder(h.subarray(h.length-16));if(h.length===0&&kh[r.commandName]){const p=new Error("S3 aborted request");throw p.name="InternalError",p}return d&&d.endsWith("</Error>")&&(i.statusCode=400),s},Ch=(e=new Uint8Array,t)=>e instanceof Uint8Array?Promise.resolve(e):t.streamCollector(e)||Promise.resolve(new Uint8Array),Ph={relation:"after",toMiddleware:"deserializerMiddleware",tags:["THROW_200_EXCEPTIONS","S3"],name:"throw200ExceptionsMiddleware",override:!0},Vo=e=>({applyToStack:t=>{t.addRelativeTo(Rh(e),Ph)}}),Th=e=>typeof e=="string"&&e.indexOf("arn:")===0&&e.split(":").length>=6;function Ih(e){return(t,r)=>async n=>{var s,i,o,a;if(e.bucketEndpoint){const c=r.endpointV2;if(c){const u=n.input.Bucket;if(typeof u=="string")try{const h=new URL(u);r.endpointV2={...c,url:h}}catch(h){const d=`@aws-sdk/middleware-sdk-s3: bucketEndpoint=true was set but Bucket=${u} could not be parsed as URL.`;throw((i=(s=r.logger)==null?void 0:s.constructor)==null?void 0:i.name)==="NoOpLogger"||(a=(o=r.logger)==null?void 0:o.warn)==null||a.call(o,d),h}}}return t(n)}}const Nh={name:"bucketEndpointMiddleware",override:!0,relation:"after",toMiddleware:"endpointV2Middleware"};function Oh({bucketEndpoint:e}){return t=>async r=>{const{input:{Bucket:n}}=r;if(!e&&typeof n=="string"&&!Th(n)&&n.indexOf("/")>=0){const s=new Error(`Bucket name shouldn't contain '/', received '${n}'`);throw s.name="InvalidBucketName",s}return t({...r})}}const Mh={step:"initialize",tags:["VALIDATE_BUCKET_NAME"],name:"validateBucketNameMiddleware",override:!0},Uh=e=>({applyToStack:t=>{t.add(Oh(e),Mh),t.addRelativeTo(Ih(e),Nh)}}),$h=void 0;function _h(e){return e===void 0?!0:typeof e=="string"&&e.length<=50}function Dh(e){const t=st(e.userAgentAppId??$h);return{...e,customUserAgent:typeof e.customUserAgent=="string"?[[e.customUserAgent]]:e.customUserAgent,userAgentAppId:async()=>{var r,n;const s=await t();if(!_h(s)){const i=((n=(r=e.logger)==null?void 0:r.constructor)==null?void 0:n.name)==="NoOpLogger"||!e.logger?console:e.logger;typeof s!="string"?i==null||i.warn("userAgentAppId must be a string or undefined."):s.length>50&&(i==null||i.warn("The provided userAgentAppId exceeds the maximum length of 50 characters."))}return s}}}class Bh{constructor({size:t,params:r}){this.data=new Map,this.parameters=[],this.capacity=t??50,r&&(this.parameters=r)}get(t,r){const n=this.hash(t);if(n===!1)return r();if(!this.data.has(n)){if(this.data.size>this.capacity+10){const s=this.data.keys();let i=0;for(;;){const{value:o,done:a}=s.next();if(this.data.delete(o),a||++i>10)break}}this.data.set(n,r())}return this.data.get(n)}size(){return this.data.size}hash(t){let r="";const{parameters:n}=this;if(n.length===0)return!1;for(const s of n){const i=String(t[s]??"");if(i.includes("|;"))return!1;r+=i+"|;"}return r}}const Lh=new RegExp("^(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}$"),Wo=e=>Lh.test(e)||e.startsWith("[")&&e.endsWith("]"),Fh=new RegExp("^(?!.*-$)(?!-)[a-zA-Z0-9-]{1,63}$"),Rn=(e,t=!1)=>{if(!t)return Fh.test(e);const r=e.split(".");for(const n of r)if(!Rn(n))return!1;return!0},Zt={},It="endpoints";function Ve(e){return typeof e!="object"||e==null?e:"ref"in e?`$${Ve(e.ref)}`:"fn"in e?`${e.fn}(${(e.argv||[]).map(Ve).join(", ")})`:JSON.stringify(e,null,2)}class Ee extends Error{constructor(t){super(t),this.name="EndpointError"}}const Hh=(e,t)=>e===t,qh=e=>{const t=e.split("."),r=[];for(const n of t){const s=n.indexOf("[");if(s!==-1){if(n.indexOf("]")!==n.length-1)throw new Ee(`Path: '${e}' does not end with ']'`);const i=n.slice(s+1,-1);if(Number.isNaN(parseInt(i)))throw new Ee(`Invalid array index: '${i}' in path: '${e}'`);s!==0&&r.push(n.slice(0,s)),r.push(i)}else r.push(n)}return r},Ko=(e,t)=>qh(t).reduce((r,n)=>{if(typeof r!="object")throw new Ee(`Index '${n}' in '${t}' not found in '${JSON.stringify(e)}'`);return Array.isArray(r)?r[parseInt(n)]:r[n]},e),zh=e=>e!=null,jh=e=>!e,Er={[Ct.HTTP]:80,[Ct.HTTPS]:443},Vh=e=>{const t=(()=>{try{if(e instanceof URL)return e;if(typeof e=="object"&&"hostname"in e){const{hostname:p,port:g,protocol:S="",path:R="",query:x={}}=e,_=new URL(`${S}//${p}${g?`:${g}`:""}${R}`);return _.search=Object.entries(x).map(([M,j])=>`${M}=${j}`).join("&"),_}return new URL(e)}catch{return null}})();if(!t)return null;const r=t.href,{host:n,hostname:s,pathname:i,protocol:o,search:a}=t;if(a)return null;const c=o.slice(0,-1);if(!Object.values(Ct).includes(c))return null;const u=Wo(s),h=r.includes(`${n}:${Er[c]}`)||typeof e=="string"&&e.includes(`${n}:${Er[c]}`),d=`${n}${h?`:${Er[c]}`:""}`;return{scheme:c,authority:d,path:i,normalizedPath:i.endsWith("/")?i:`${i}/`,isIp:u}},Wh=(e,t)=>e===t,Kh=(e,t,r,n)=>t>=r||e.length<r?null:n?e.substring(e.length-r,e.length-t):e.substring(t,r),Gh=e=>encodeURIComponent(e).replace(/[!*'()]/g,t=>`%${t.charCodeAt(0).toString(16).toUpperCase()}`),Zh={booleanEquals:Hh,getAttr:Ko,isSet:zh,isValidHostLabel:Rn,not:jh,parseURL:Vh,stringEquals:Wh,substring:Kh,uriEncode:Gh},Go=(e,t)=>{const r=[],n={...t.endpointParams,...t.referenceRecord};let s=0;for(;s<e.length;){const i=e.indexOf("{",s);if(i===-1){r.push(e.slice(s));break}r.push(e.slice(s,i));const o=e.indexOf("}",i);if(o===-1){r.push(e.slice(i));break}e[i+1]==="{"&&e[o+1]==="}"&&(r.push(e.slice(i+1,o)),s=o+2);const a=e.substring(i+1,o);if(a.includes("#")){const[c,u]=a.split("#");r.push(Ko(n[c],u))}else r.push(n[a]);s=o+1}return r.join("")},Xh=({ref:e},t)=>({...t.endpointParams,...t.referenceRecord})[e],sr=(e,t,r)=>{if(typeof e=="string")return Go(e,r);if(e.fn)return Zo(e,r);if(e.ref)return Xh(e,r);throw new Ee(`'${t}': ${String(e)} is not a string, function or reference.`)},Zo=({fn:e,argv:t},r)=>{const n=t.map(i=>["boolean","number"].includes(typeof i)?i:sr(i,"arg",r)),s=e.split(".");return s[0]in Zt&&s[1]!=null?Zt[s[0]][s[1]](...n):Zh[e](...n)},Qh=({assign:e,...t},r)=>{var n,s;if(e&&e in r.referenceRecord)throw new Ee(`'${e}' is already defined in Reference Record.`);const i=Zo(t,r);return(s=(n=r.logger)==null?void 0:n.debug)==null||s.call(n,`${It} evaluateCondition: ${Ve(t)} = ${Ve(i)}`),{result:i===""?!0:!!i,...e!=null&&{toAssign:{name:e,value:i}}}},Cn=(e=[],t)=>{var r,n;const s={};for(const i of e){const{result:o,toAssign:a}=Qh(i,{...t,referenceRecord:{...t.referenceRecord,...s}});if(!o)return{result:o};a&&(s[a.name]=a.value,(n=(r=t.logger)==null?void 0:r.debug)==null||n.call(r,`${It} assign: ${a.name} := ${Ve(a.value)}`))}return{result:!0,referenceRecord:s}},Yh=(e,t)=>Object.entries(e).reduce((r,[n,s])=>({...r,[n]:s.map(i=>{const o=sr(i,"Header value entry",t);if(typeof o!="string")throw new Ee(`Header '${n}' value '${o}' is not a string`);return o})}),{}),Xo=(e,t)=>{if(Array.isArray(e))return e.map(r=>Xo(r,t));switch(typeof e){case"string":return Go(e,t);case"object":if(e===null)throw new Ee(`Unexpected endpoint property: ${e}`);return Qo(e,t);case"boolean":return e;default:throw new Ee(`Unexpected endpoint property type: ${typeof e}`)}},Qo=(e,t)=>Object.entries(e).reduce((r,[n,s])=>({...r,[n]:Xo(s,t)}),{}),Jh=(e,t)=>{const r=sr(e,"Endpoint URL",t);if(typeof r=="string")try{return new URL(r)}catch(n){throw n}throw new Ee(`Endpoint URL must be a string, got ${typeof r}`)},ef=(e,t)=>{var r,n;const{conditions:s,endpoint:i}=e,{result:o,referenceRecord:a}=Cn(s,t);if(!o)return;const c={...t,referenceRecord:{...t.referenceRecord,...a}},{url:u,properties:h,headers:d}=i;return(n=(r=t.logger)==null?void 0:r.debug)==null||n.call(r,`${It} Resolving endpoint from template: ${Ve(i)}`),{...d!=null&&{headers:Yh(d,c)},...h!=null&&{properties:Qo(h,c)},url:Jh(u,c)}},tf=(e,t)=>{const{conditions:r,error:n}=e,{result:s,referenceRecord:i}=Cn(r,t);if(s)throw new Ee(sr(n,"Error",{...t,referenceRecord:{...t.referenceRecord,...i}}))},rf=(e,t)=>{const{conditions:r,rules:n}=e,{result:s,referenceRecord:i}=Cn(r,t);if(s)return Yo(n,{...t,referenceRecord:{...t.referenceRecord,...i}})},Yo=(e,t)=>{for(const r of e)if(r.type==="endpoint"){const n=ef(r,t);if(n)return n}else if(r.type==="error")tf(r,t);else if(r.type==="tree"){const n=rf(r,t);if(n)return n}else throw new Ee(`Unknown endpoint rule: ${r}`);throw new Ee("Rules evaluation failed")},nf=(e,t)=>{var r,n,s,i;const{endpointParams:o,logger:a}=t,{parameters:c,rules:u}=e;(n=(r=t.logger)==null?void 0:r.debug)==null||n.call(r,`${It} Initial EndpointParams: ${Ve(o)}`);const h=Object.entries(c).filter(([,g])=>g.default!=null).map(([g,S])=>[g,S.default]);if(h.length>0)for(const[g,S]of h)o[g]=o[g]??S;const d=Object.entries(c).filter(([,g])=>g.required).map(([g])=>g);for(const g of d)if(o[g]==null)throw new Ee(`Missing required parameter: '${g}'`);const p=Yo(u,{endpointParams:o,logger:a,referenceRecord:{}});return(i=(s=t.logger)==null?void 0:s.debug)==null||i.call(s,`${It} Resolved endpoint: ${Ve(p)}`),p},Jo=(e,t=!1)=>{if(t){for(const r of e.split("."))if(!Jo(r))return!1;return!0}return!(!Rn(e)||e.length<3||e.length>63||e!==e.toLowerCase()||Wo(e))},Rs=":",sf="/",of=e=>{const t=e.split(Rs);if(t.length<6)return null;const[r,n,s,i,o,...a]=t;if(r!=="arn"||n===""||s===""||a.join(Rs)==="")return null;const c=a.map(u=>u.split(sf)).flat();return{partition:n,service:s,region:i,accountId:o,resourceId:c}},af=[{id:"aws",outputs:{dnsSuffix:"amazonaws.com",dualStackDnsSuffix:"api.aws",implicitGlobalRegion:"us-east-1",name:"aws",supportsDualStack:!0,supportsFIPS:!0},regionRegex:"^(us|eu|ap|sa|ca|me|af|il|mx)\\-\\w+\\-\\d+$",regions:{"af-south-1":{description:"Africa (Cape Town)"},"ap-east-1":{description:"Asia Pacific (Hong Kong)"},"ap-northeast-1":{description:"Asia Pacific (Tokyo)"},"ap-northeast-2":{description:"Asia Pacific (Seoul)"},"ap-northeast-3":{description:"Asia Pacific (Osaka)"},"ap-south-1":{description:"Asia Pacific (Mumbai)"},"ap-south-2":{description:"Asia Pacific (Hyderabad)"},"ap-southeast-1":{description:"Asia Pacific (Singapore)"},"ap-southeast-2":{description:"Asia Pacific (Sydney)"},"ap-southeast-3":{description:"Asia Pacific (Jakarta)"},"ap-southeast-4":{description:"Asia Pacific (Melbourne)"},"ap-southeast-5":{description:"Asia Pacific (Malaysia)"},"ap-southeast-7":{description:"Asia Pacific (Thailand)"},"aws-global":{description:"AWS Standard global region"},"ca-central-1":{description:"Canada (Central)"},"ca-west-1":{description:"Canada West (Calgary)"},"eu-central-1":{description:"Europe (Frankfurt)"},"eu-central-2":{description:"Europe (Zurich)"},"eu-north-1":{description:"Europe (Stockholm)"},"eu-south-1":{description:"Europe (Milan)"},"eu-south-2":{description:"Europe (Spain)"},"eu-west-1":{description:"Europe (Ireland)"},"eu-west-2":{description:"Europe (London)"},"eu-west-3":{description:"Europe (Paris)"},"il-central-1":{description:"Israel (Tel Aviv)"},"me-central-1":{description:"Middle East (UAE)"},"me-south-1":{description:"Middle East (Bahrain)"},"mx-central-1":{description:"Mexico (Central)"},"sa-east-1":{description:"South America (Sao Paulo)"},"us-east-1":{description:"US East (N. Virginia)"},"us-east-2":{description:"US East (Ohio)"},"us-west-1":{description:"US West (N. California)"},"us-west-2":{description:"US West (Oregon)"}}},{id:"aws-cn",outputs:{dnsSuffix:"amazonaws.com.cn",dualStackDnsSuffix:"api.amazonwebservices.com.cn",implicitGlobalRegion:"cn-northwest-1",name:"aws-cn",supportsDualStack:!0,supportsFIPS:!0},regionRegex:"^cn\\-\\w+\\-\\d+$",regions:{"aws-cn-global":{description:"AWS China global region"},"cn-north-1":{description:"China (Beijing)"},"cn-northwest-1":{description:"China (Ningxia)"}}},{id:"aws-us-gov",outputs:{dnsSuffix:"amazonaws.com",dualStackDnsSuffix:"api.aws",implicitGlobalRegion:"us-gov-west-1",name:"aws-us-gov",supportsDualStack:!0,supportsFIPS:!0},regionRegex:"^us\\-gov\\-\\w+\\-\\d+$",regions:{"aws-us-gov-global":{description:"AWS GovCloud (US) global region"},"us-gov-east-1":{description:"AWS GovCloud (US-East)"},"us-gov-west-1":{description:"AWS GovCloud (US-West)"}}},{id:"aws-iso",outputs:{dnsSuffix:"c2s.ic.gov",dualStackDnsSuffix:"c2s.ic.gov",implicitGlobalRegion:"us-iso-east-1",name:"aws-iso",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^us\\-iso\\-\\w+\\-\\d+$",regions:{"aws-iso-global":{description:"AWS ISO (US) global region"},"us-iso-east-1":{description:"US ISO East"},"us-iso-west-1":{description:"US ISO WEST"}}},{id:"aws-iso-b",outputs:{dnsSuffix:"sc2s.sgov.gov",dualStackDnsSuffix:"sc2s.sgov.gov",implicitGlobalRegion:"us-isob-east-1",name:"aws-iso-b",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^us\\-isob\\-\\w+\\-\\d+$",regions:{"aws-iso-b-global":{description:"AWS ISOB (US) global region"},"us-isob-east-1":{description:"US ISOB East (Ohio)"}}},{id:"aws-iso-e",outputs:{dnsSuffix:"cloud.adc-e.uk",dualStackDnsSuffix:"cloud.adc-e.uk",implicitGlobalRegion:"eu-isoe-west-1",name:"aws-iso-e",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^eu\\-isoe\\-\\w+\\-\\d+$",regions:{"eu-isoe-west-1":{description:"EU ISOE West"}}},{id:"aws-iso-f",outputs:{dnsSuffix:"csp.hci.ic.gov",dualStackDnsSuffix:"csp.hci.ic.gov",implicitGlobalRegion:"us-isof-south-1",name:"aws-iso-f",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^us\\-isof\\-\\w+\\-\\d+$",regions:{"aws-iso-f-global":{description:"AWS ISOF global region"},"us-isof-east-1":{description:"US ISOF EAST"},"us-isof-south-1":{description:"US ISOF SOUTH"}}}],cf={partitions:af};let uf=cf;const lf=e=>{const{partitions:t}=uf;for(const n of t){const{regions:s,outputs:i}=n;for(const[o,a]of Object.entries(s))if(o===e)return{...i,...a}}for(const n of t){const{regionRegex:s,outputs:i}=n;if(new RegExp(s).test(e))return{...i}}const r=t.find(n=>n.id==="aws");if(!r)throw new Error("Provided region was not found in the partition array or regex, and default partition with id 'aws' doesn't exist.");return{...r.outputs}},ea={isVirtualHostableS3Bucket:Jo,parseArn:of,partition:lf};Zt.aws=ea;const df=/\d{12}\.ddb/;async function hf(e,t,r){var n,s,i,o,a,c,u;const h=r.request;if(((n=h==null?void 0:h.headers)==null?void 0:n["smithy-protocol"])==="rpc-v2-cbor"&&Y(e,"PROTOCOL_RPC_V2_CBOR","M"),typeof t.retryStrategy=="function"){const p=await t.retryStrategy();typeof p.acquireInitialRetryToken=="function"?(i=(s=p.constructor)==null?void 0:s.name)!=null&&i.includes("Adaptive")?Y(e,"RETRY_MODE_ADAPTIVE","F"):Y(e,"RETRY_MODE_STANDARD","E"):Y(e,"RETRY_MODE_LEGACY","D")}if(typeof t.accountIdEndpointMode=="function"){const p=e.endpointV2;switch(String((o=p==null?void 0:p.url)==null?void 0:o.hostname).match(df)&&Y(e,"ACCOUNT_ID_ENDPOINT","O"),await((a=t.accountIdEndpointMode)==null?void 0:a.call(t))){case"disabled":Y(e,"ACCOUNT_ID_MODE_DISABLED","Q");break;case"preferred":Y(e,"ACCOUNT_ID_MODE_PREFERRED","P");break;case"required":Y(e,"ACCOUNT_ID_MODE_REQUIRED","R");break}}const d=(u=(c=e.__smithy_context)==null?void 0:c.selectedHttpAuthScheme)==null?void 0:u.identity;if(d!=null&&d.$source){const p=d;p.accountId&&Y(e,"RESOLVED_ACCOUNT_ID","T");for(const[g,S]of Object.entries(p.$source??{}))Y(e,g,S)}}const Cs="user-agent",Ar="x-amz-user-agent",Ps=" ",kr="/",ff=/[^\!\$\%\&\'\*\+\-\.\^\_\`\|\~\d\w]/g,pf=/[^\!\$\%\&\'\*\+\-\.\^\_\`\|\~\d\w\#]/g,Ts="-",gf=1024;function mf(e){let t="";for(const r in e){const n=e[r];if(t.length+n.length+1<=gf){t.length?t+=","+n:t+=n;continue}break}return t}const yf=e=>(t,r)=>async n=>{var s,i,o,a;const{request:c}=n;if(!ee.isInstance(c))return t(n);const{headers:u}=c,h=((s=r==null?void 0:r.userAgent)==null?void 0:s.map(Ut))||[],d=(await e.defaultUserAgentProvider()).map(Ut);await hf(r,e,n);const p=r;d.push(`m/${mf(Object.assign({},(i=r.__smithy_context)==null?void 0:i.features,(o=p.__aws_sdk_context)==null?void 0:o.features))}`);const g=((a=e==null?void 0:e.customUserAgent)==null?void 0:a.map(Ut))||[],S=await e.userAgentAppId();S&&d.push(Ut([`app/${S}`]));const R=[].concat([...d,...h,...g]).join(Ps),x=[...d.filter(_=>_.startsWith("aws-sdk-")),...g].join(Ps);return e.runtime!=="browser"?(x&&(u[Ar]=u[Ar]?`${u[Cs]} ${x}`:x),u[Cs]=R):u[Ar]=R,t({...n,request:c})},Ut=e=>{var t;const r=e[0].split(kr).map(a=>a.replace(ff,Ts)).join(kr),n=(t=e[1])==null?void 0:t.replace(pf,Ts),s=r.indexOf(kr),i=r.substring(0,s);let o=r.substring(s+1);return i==="api"&&(o=o.toLowerCase()),[i,o,n].filter(a=>a&&a.length>0).reduce((a,c,u)=>{switch(u){case 0:return c;case 1:return`${a}/${c}`;default:return`${a}#${c}`}},"")},wf={name:"getUserAgentMiddleware",step:"build",priority:"low",tags:["SET_USER_AGENT","USER_AGENT"],override:!0},bf=e=>({applyToStack:t=>{t.add(yf(e),wf)}}),vf=!1,Sf=!1,ta=e=>typeof e=="string"&&(e.startsWith("fips-")||e.endsWith("-fips")),Is=e=>ta(e)?["fips-aws-global","aws-fips"].includes(e)?"us-east-1":e.replace(/fips-(dkr-|prod-)?|-fips/,""):e,Ef=e=>{const{region:t,useFipsEndpoint:r}=e;if(!t)throw new Error("Region is missing");return{...e,region:async()=>{if(typeof t=="string")return Is(t);const n=await t();return Is(n)},useFipsEndpoint:async()=>{const n=typeof t=="string"?t:await t();return ta(n)?!0:typeof r!="function"?Promise.resolve(!!r):r()}}},Af=e=>({...e,eventStreamMarshaller:e.eventStreamSerdeProvider(e)}),Ns="content-length";function kf(e){return t=>async r=>{const n=r.request;if(ee.isInstance(n)){const{body:s,headers:i}=n;if(s&&Object.keys(i).map(o=>o.toLowerCase()).indexOf(Ns)===-1)try{const o=e(s);n.headers={...n.headers,[Ns]:String(o)}}catch{}}return t({...r,request:n})}}const xf={step:"build",tags:["SET_CONTENT_LENGTH","CONTENT_LENGTH"],name:"contentLengthMiddleware",override:!0},Rf=e=>({applyToStack:t=>{t.add(kf(e.bodyLengthChecker),xf)}}),Cf=async e=>{const t=(e==null?void 0:e.Bucket)||"";if(typeof e.Bucket=="string"&&(e.Bucket=t.replace(/#/g,encodeURIComponent("#")).replace(/\?/g,encodeURIComponent("?"))),Of(t)){if(e.ForcePathStyle===!0)throw new Error("Path-style addressing cannot be used with ARN buckets")}else(!Nf(t)||t.indexOf(".")!==-1&&!String(e.Endpoint).startsWith("http:")||t.toLowerCase()!==t||t.length<3)&&(e.ForcePathStyle=!0);return e.DisableMultiRegionAccessPoints&&(e.disableMultiRegionAccessPoints=!0,e.DisableMRAP=!0),e},Pf=/^[a-z0-9][a-z0-9\.\-]{1,61}[a-z0-9]$/,Tf=/(\d+\.){3}\d+/,If=/\.\./,Nf=e=>Pf.test(e)&&!Tf.test(e)&&!If.test(e),Of=e=>{const[t,r,n,,,s]=e.split(":"),i=t==="arn"&&e.split(":").length>=6,o=!!(i&&r&&n&&s);if(i&&!o)throw new Error(`Invalid ARN: ${e} was an invalid ARN.`);return o},Mf=(e,t,r)=>{const n=async()=>{const s=r[e]??r[t];return typeof s=="function"?s():s};return e==="credentialScope"||t==="CredentialScope"?async()=>{const s=typeof r.credentials=="function"?await r.credentials():r.credentials;return(s==null?void 0:s.credentialScope)??(s==null?void 0:s.CredentialScope)}:e==="accountId"||t==="AccountId"?async()=>{const s=typeof r.credentials=="function"?await r.credentials():r.credentials;return(s==null?void 0:s.accountId)??(s==null?void 0:s.AccountId)}:e==="endpoint"||t==="endpoint"?async()=>{const s=await n();if(s&&typeof s=="object"){if("url"in s)return s.url.href;if("hostname"in s){const{protocol:i,hostname:o,port:a,path:c}=s;return`${i}//${o}${a?":"+a:""}${c}`}}return s}:n},ra=async e=>{};function Uf(e){const t={};if(e=e.replace(/^\?/,""),e)for(const r of e.split("&")){let[n,s=null]=r.split("=");n=decodeURIComponent(n),s&&(s=decodeURIComponent(s)),n in t?Array.isArray(t[n])?t[n].push(s):t[n]=[t[n],s]:t[n]=s}return t}const Xt=e=>{if(typeof e=="string")return Xt(new URL(e));const{hostname:t,pathname:r,port:n,protocol:s,search:i}=e;let o;return i&&(o=Uf(i)),{hostname:t,port:n?parseInt(n):void 0,protocol:s,path:r,query:o}},na=e=>typeof e=="object"?"url"in e?Xt(e.url):e:Xt(e),$f=async(e,t,r,n)=>{if(!r.endpoint){let i;r.serviceConfiguredEndpoint?i=await r.serviceConfiguredEndpoint():i=await ra(r.serviceId),i&&(r.endpoint=()=>Promise.resolve(na(i)))}const s=await sa(e,t,r);if(typeof r.endpointProvider!="function")throw new Error("config.endpointProvider is not set.");return r.endpointProvider(s,n)},sa=async(e,t,r)=>{var n;const s={},i=((n=t==null?void 0:t.getEndpointParameterInstructions)==null?void 0:n.call(t))||{};for(const[o,a]of Object.entries(i))switch(a.type){case"staticContextParams":s[o]=a.value;break;case"contextParams":s[o]=e[a.name];break;case"clientContextParams":case"builtInParams":s[o]=await Mf(a.name,o,r)();break;case"operationContextParams":s[o]=a.get(e);break;default:throw new Error("Unrecognized endpoint parameter instruction: "+JSON.stringify(a))}return Object.keys(i).length===0&&Object.assign(s,r),String(r.serviceId).toLowerCase()==="s3"&&await Cf(s),s},_f=({config:e,instructions:t})=>(r,n)=>async s=>{var i,o,a;e.endpoint&&Qc(n,"ENDPOINT_OVERRIDE","N");const c=await $f(s.input,{getEndpointParameterInstructions(){return t}},{...e},n);n.endpointV2=c,n.authSchemes=(i=c.properties)==null?void 0:i.authSchemes;const u=(o=n.authSchemes)==null?void 0:o[0];if(u){n.signing_region=u.signingRegion,n.signing_service=u.signingName;const h=ft(n),d=(a=h==null?void 0:h.selectedHttpAuthScheme)==null?void 0:a.httpAuthOption;d&&(d.signingProperties=Object.assign(d.signingProperties||{},{signing_region:u.signingRegion,signingRegion:u.signingRegion,signing_service:u.signingName,signingName:u.signingName,signingRegionSet:u.signingRegionSet},u.properties))}return r({...s})},Df={step:"serialize",tags:["ENDPOINT_PARAMETERS","ENDPOINT_V2","ENDPOINT"],name:"endpointV2Middleware",override:!0,relation:"before",toMiddleware:gn.name},ia=(e,t)=>({applyToStack:r=>{r.addRelativeTo(_f({config:e,instructions:t}),Df)}}),Bf=e=>{const t=e.tls??!0,{endpoint:r}=e,n=r!=null?async()=>na(await Ne(r)()):void 0,s={...e,endpoint:n,tls:t,isCustomEndpoint:!!r,useDualstackEndpoint:Ne(e.useDualstackEndpoint??!1),useFipsEndpoint:Ne(e.useFipsEndpoint??!1)};let i;return s.serviceConfiguredEndpoint=async()=>(e.serviceId&&!i&&(i=ra(e.serviceId)),i),s};var ut;(function(e){e.STANDARD="standard",e.ADAPTIVE="adaptive"})(ut||(ut={}));const Pn=3,Lf=ut.STANDARD,Ff=["BandwidthLimitExceeded","EC2ThrottledException","LimitExceededException","PriorRequestNotComplete","ProvisionedThroughputExceededException","RequestLimitExceeded","RequestThrottled","RequestThrottledException","SlowDown","ThrottledException","Throttling","ThrottlingException","TooManyRequestsException","TransactionInProgressException"],Hf=["TimeoutError","RequestTimeout","RequestTimeoutException"],qf=[500,502,503,504],zf=["ECONNRESET","ECONNREFUSED","EPIPE","ETIMEDOUT"],jf=e=>{var t;return(t=e.$metadata)==null?void 0:t.clockSkewCorrected},oa=e=>{var t,r;return((t=e.$metadata)==null?void 0:t.httpStatusCode)===429||Ff.includes(e.name)||((r=e.$retryable)==null?void 0:r.throttling)==!0},Tn=(e,t=0)=>{var r;return jf(e)||Hf.includes(e.name)||zf.includes((e==null?void 0:e.code)||"")||qf.includes(((r=e.$metadata)==null?void 0:r.httpStatusCode)||0)||e.cause!==void 0&&t<=10&&Tn(e.cause,t+1)},Vf=e=>{var t;if(((t=e.$metadata)==null?void 0:t.httpStatusCode)!==void 0){const r=e.$metadata.httpStatusCode;return 500<=r&&r<=599&&!Tn(e)}return!1};class ir{constructor(t){this.currentCapacity=0,this.enabled=!1,this.lastMaxRate=0,this.measuredTxRate=0,this.requestCount=0,this.lastTimestamp=0,this.timeWindow=0,this.beta=(t==null?void 0:t.beta)??.7,this.minCapacity=(t==null?void 0:t.minCapacity)??1,this.minFillRate=(t==null?void 0:t.minFillRate)??.5,this.scaleConstant=(t==null?void 0:t.scaleConstant)??.4,this.smooth=(t==null?void 0:t.smooth)??.8;const r=this.getCurrentTimeInSeconds();this.lastThrottleTime=r,this.lastTxRateBucket=Math.floor(this.getCurrentTimeInSeconds()),this.fillRate=this.minFillRate,this.maxCapacity=this.minCapacity}getCurrentTimeInSeconds(){return Date.now()/1e3}async getSendToken(){return this.acquireTokenBucket(1)}async acquireTokenBucket(t){if(this.enabled){if(this.refillTokenBucket(),t>this.currentCapacity){const r=(t-this.currentCapacity)/this.fillRate*1e3;await new Promise(n=>ir.setTimeoutFn(n,r))}this.currentCapacity=this.currentCapacity-t}}refillTokenBucket(){const t=this.getCurrentTimeInSeconds();if(!this.lastTimestamp){this.lastTimestamp=t;return}const r=(t-this.lastTimestamp)*this.fillRate;this.currentCapacity=Math.min(this.maxCapacity,this.currentCapacity+r),this.lastTimestamp=t}updateClientSendingRate(t){let r;if(this.updateMeasuredRate(),oa(t)){const s=this.enabled?Math.min(this.measuredTxRate,this.fillRate):this.measuredTxRate;this.lastMaxRate=s,this.calculateTimeWindow(),this.lastThrottleTime=this.getCurrentTimeInSeconds(),r=this.cubicThrottle(s),this.enableTokenBucket()}else this.calculateTimeWindow(),r=this.cubicSuccess(this.getCurrentTimeInSeconds());const n=Math.min(r,2*this.measuredTxRate);this.updateTokenBucketRate(n)}calculateTimeWindow(){this.timeWindow=this.getPrecise(Math.pow(this.lastMaxRate*(1-this.beta)/this.scaleConstant,1/3))}cubicThrottle(t){return this.getPrecise(t*this.beta)}cubicSuccess(t){return this.getPrecise(this.scaleConstant*Math.pow(t-this.lastThrottleTime-this.timeWindow,3)+this.lastMaxRate)}enableTokenBucket(){this.enabled=!0}updateTokenBucketRate(t){this.refillTokenBucket(),this.fillRate=Math.max(t,this.minFillRate),this.maxCapacity=Math.max(t,this.minCapacity),this.currentCapacity=Math.min(this.currentCapacity,this.maxCapacity)}updateMeasuredRate(){const t=this.getCurrentTimeInSeconds(),r=Math.floor(t*2)/2;if(this.requestCount++,r>this.lastTxRateBucket){const n=this.requestCount/(r-this.lastTxRateBucket);this.measuredTxRate=this.getPrecise(n*this.smooth+this.measuredTxRate*(1-this.smooth)),this.requestCount=0,this.lastTxRateBucket=r}}getPrecise(t){return parseFloat(t.toFixed(8))}}ir.setTimeoutFn=setTimeout;const dn=100,aa=20*1e3,Wf=500,Os=500,Kf=5,Gf=10,Zf=1,Xf="amz-sdk-invocation-id",Qf="amz-sdk-request",Yf=()=>{let e=dn;return{computeNextBackoffDelay:t=>Math.floor(Math.min(aa,Math.random()*2**t*e)),setDelayBase:t=>{e=t}}},Ms=({retryDelay:e,retryCount:t,retryCost:r})=>({getRetryCount:()=>t,getRetryDelay:()=>Math.min(aa,e),getRetryCost:()=>r});class ca{constructor(t){this.maxAttempts=t,this.mode=ut.STANDARD,this.capacity=Os,this.retryBackoffStrategy=Yf(),this.maxAttemptsProvider=typeof t=="function"?t:async()=>t}async acquireInitialRetryToken(t){return Ms({retryDelay:dn,retryCount:0})}async refreshRetryTokenForRetry(t,r){const n=await this.getMaxAttempts();if(this.shouldRetry(t,r,n)){const s=r.errorType;this.retryBackoffStrategy.setDelayBase(s==="THROTTLING"?Wf:dn);const i=this.retryBackoffStrategy.computeNextBackoffDelay(t.getRetryCount()),o=r.retryAfterHint?Math.max(r.retryAfterHint.getTime()-Date.now()||0,i):i,a=this.getCapacityCost(s);return this.capacity-=a,Ms({retryDelay:o,retryCount:t.getRetryCount()+1,retryCost:a})}throw new Error("No retry token available")}recordSuccess(t){this.capacity=Math.max(Os,this.capacity+(t.getRetryCost()??Zf))}getCapacity(){return this.capacity}async getMaxAttempts(){try{return await this.maxAttemptsProvider()}catch{return Pn}}shouldRetry(t,r,n){return t.getRetryCount()+1<n&&this.capacity>=this.getCapacityCost(r.errorType)&&this.isRetryableError(r.errorType)}getCapacityCost(t){return t==="TRANSIENT"?Gf:Kf}isRetryableError(t){return t==="THROTTLING"||t==="TRANSIENT"}}class Jf{constructor(t,r){this.maxAttemptsProvider=t,this.mode=ut.ADAPTIVE;const{rateLimiter:n}=r??{};this.rateLimiter=n??new ir,this.standardRetryStrategy=new ca(t)}async acquireInitialRetryToken(t){return await this.rateLimiter.getSendToken(),this.standardRetryStrategy.acquireInitialRetryToken(t)}async refreshRetryTokenForRetry(t,r){return this.rateLimiter.updateClientSendingRate(r),this.standardRetryStrategy.refreshRetryTokenForRetry(t,r)}recordSuccess(t){this.rateLimiter.updateClientSendingRate({}),this.standardRetryStrategy.recordSuccess(t)}}let $t;const ep=new Uint8Array(16);function tp(){if(!$t&&($t=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!$t))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return $t(ep)}const ae=[];for(let e=0;e<256;++e)ae.push((e+256).toString(16).slice(1));function rp(e,t=0){return ae[e[t+0]]+ae[e[t+1]]+ae[e[t+2]]+ae[e[t+3]]+"-"+ae[e[t+4]]+ae[e[t+5]]+"-"+ae[e[t+6]]+ae[e[t+7]]+"-"+ae[e[t+8]]+ae[e[t+9]]+"-"+ae[e[t+10]]+ae[e[t+11]]+ae[e[t+12]]+ae[e[t+13]]+ae[e[t+14]]+ae[e[t+15]]}const np=typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto),Us={randomUUID:np};function sp(e,t,r){if(Us.randomUUID&&!e)return Us.randomUUID();e=e||{};const n=e.random||(e.rng||tp)();return n[6]=n[6]&15|64,n[8]=n[8]&63|128,rp(n)}const ip=e=>e instanceof Error?e:e instanceof Object?Object.assign(new Error,e):typeof e=="string"?new Error(e):new Error(`AWS SDK error wrapper for ${e}`),op=e=>{const{retryStrategy:t}=e,r=Ne(e.maxAttempts??Pn);return{...e,maxAttempts:r,retryStrategy:async()=>t||(await Ne(e.retryMode)()===ut.ADAPTIVE?new Jf(r):new ca(r))}},ap=e=>(e==null?void 0:e.body)instanceof ReadableStream,cp=e=>(t,r)=>async n=>{var s;let i=await e.retryStrategy();const o=await e.maxAttempts();if(up(i)){i=i;let a=await i.acquireInitialRetryToken(r.partition_id),c=new Error,u=0,h=0;const{request:d}=n,p=ee.isInstance(d);for(p&&(d.headers[Xf]=sp());;)try{p&&(d.headers[Qf]=`attempt=${u+1}; max=${o}`);const{response:g,output:S}=await t(n);return i.recordSuccess(a),S.$metadata.attempts=u+1,S.$metadata.totalRetryDelay=h,{response:g,output:S}}catch(g){const S=lp(g);if(c=ip(g),p&&ap(d))throw(s=r.logger instanceof vn?console:r.logger)==null||s.warn("An error was encountered in a non-retryable streaming request."),c;try{a=await i.refreshRetryTokenForRetry(a,S)}catch{throw c.$metadata||(c.$metadata={}),c.$metadata.attempts=u+1,c.$metadata.totalRetryDelay=h,c}u=a.getRetryCount();const R=a.getRetryDelay();h+=R,await new Promise(x=>setTimeout(x,R))}}else return i=i,i!=null&&i.mode&&(r.userAgent=[...r.userAgent||[],["cfg/retry-mode",i.mode]]),i.retry(t,n)},up=e=>typeof e.acquireInitialRetryToken<"u"&&typeof e.refreshRetryTokenForRetry<"u"&&typeof e.recordSuccess<"u",lp=e=>{const t={error:e,errorType:dp(e)},r=pp(e.$response);return r&&(t.retryAfterHint=r),t},dp=e=>oa(e)?"THROTTLING":Tn(e)?"TRANSIENT":Vf(e)?"SERVER_ERROR":"CLIENT_ERROR",hp={name:"retryMiddleware",tags:["RETRY"],step:"finalizeRequest",priority:"high",override:!0},fp=e=>({applyToStack:t=>{t.add(cp(e),hp)}}),pp=e=>{if(!Pt.isInstance(e))return;const t=Object.keys(e.headers).find(s=>s.toLowerCase()==="retry-after");if(!t)return;const r=e.headers[t],n=Number(r);return Number.isNaN(n)?new Date(r):new Date(n*1e3)},gp={CrtSignerV4:null};class mp{constructor(t){N(this,"sigv4aSigner"),N(this,"sigv4Signer"),N(this,"signerOptions"),this.sigv4Signer=new ph(t),this.signerOptions=t}async sign(t,r={}){if(r.signingRegion==="*"){if(this.signerOptions.runtime!=="node")throw new Error("This request requires signing with SigV4Asymmetric algorithm. It's only available in Node.js");return this.getSigv4aSigner().sign(t,r)}return this.sigv4Signer.sign(t,r)}async signWithCredentials(t,r,n={}){if(n.signingRegion==="*"){if(this.signerOptions.runtime!=="node")throw new Error("This request requires signing with SigV4Asymmetric algorithm. It's only available in Node.js");return this.getSigv4aSigner().signWithCredentials(t,r,n)}return this.sigv4Signer.signWithCredentials(t,r,n)}async presign(t,r={}){if(r.signingRegion==="*"){if(this.signerOptions.runtime!=="node")throw new Error("This request requires signing with SigV4Asymmetric algorithm. It's only available in Node.js");return this.getSigv4aSigner().presign(t,r)}return this.sigv4Signer.presign(t,r)}async presignWithCredentials(t,r,n={}){if(n.signingRegion==="*")throw new Error("Method presignWithCredentials is not supported for [signingRegion=*].");return this.sigv4Signer.presignWithCredentials(t,r,n)}getSigv4aSigner(){if(!this.sigv4aSigner){let t=null;try{if(t=gp.CrtSignerV4,typeof t!="function")throw new Error}catch(r){throw r.message=`${r.message}
Please check whether you have installed the "@aws-sdk/signature-v4-crt" package explicitly. 
You must also register the package by calling [require("@aws-sdk/signature-v4-crt");] or an ESM equivalent such as [import "@aws-sdk/signature-v4-crt";]. 
For more information please go to https://github.com/aws/aws-sdk-js-v3#functionality-requiring-aws-common-runtime-crt`,r}this.sigv4aSigner=new t({...this.signerOptions,signingAlgorithm:1})}return this.sigv4aSigner}}const In="required",l="type",f="conditions",m="fn",y="argv",P="ref",B="assign",E="url",A="properties",ua="backend",Ie="authSchemes",Ae="disableDoubleEncoding",ke="signingName",Me="signingRegion",k="headers",Nn="signingRegionSet",yp=6,wp=!1,Ce=!0,Re="isSet",oe="booleanEquals",C="error",Qt="aws.partition",D="stringEquals",K="getAttr",fe="name",he="substring",$s="bucketSuffix",la="parseURL",_s="{url#scheme}://{url#authority}/{uri_encoded_bucket}{url#path}",v="endpoint",b="tree",da="aws.isVirtualHostableS3Bucket",Ft="{url#scheme}://{Bucket}.{url#authority}{url#path}",Oe="not",Ht="{url#scheme}://{url#authority}{url#path}",ha="hardwareType",fa="regionPrefix",Ds="bucketAliasSuffix",hn="outpostId",et="isValidHostLabel",On="sigv4a",Nt="s3-outposts",lt="s3",pa="{url#scheme}://{url#authority}{url#normalizedPath}{Bucket}",ga="https://{Bucket}.s3-accelerate.{partitionResult#dnsSuffix}",Bs="https://{Bucket}.s3.{partitionResult#dnsSuffix}",ma="aws.parseArn",ya="bucketArn",wa="arnType",Yt="",Mn="s3-object-lambda",ba="accesspoint",Un="accessPointName",Ls="{url#scheme}://{accessPointName}-{bucketArn#accountId}.{url#authority}{url#path}",Fs="mrapPartition",Hs="outpostType",qs="arnPrefix",va="{url#scheme}://{url#authority}{url#normalizedPath}{uri_encoded_bucket}",zs="https://s3.{partitionResult#dnsSuffix}/{uri_encoded_bucket}",js="https://s3.{partitionResult#dnsSuffix}",tt={[In]:!1,[l]:"String"},rt={[In]:!0,default:!1,[l]:"Boolean"},yt={[In]:!1,[l]:"Boolean"},Pe={[m]:oe,[y]:[{[P]:"Accelerate"},!0]},q={[m]:oe,[y]:[{[P]:"UseFIPS"},!0]},H={[m]:oe,[y]:[{[P]:"UseDualStack"},!0]},Q={[m]:Re,[y]:[{[P]:"Endpoint"}]},Sa={[m]:Qt,[y]:[{[P]:"Region"}],[B]:"partitionResult"},Vs={[m]:D,[y]:[{[m]:K,[y]:[{[P]:"partitionResult"},fe]},"aws-cn"]},Jt={[m]:Re,[y]:[{[P]:"Bucket"}]},G={[P]:"Bucket"},me={[m]:la,[y]:[{[P]:"Endpoint"}],[B]:"url"},qt={[m]:oe,[y]:[{[m]:K,[y]:[{[P]:"url"},"isIp"]},!0]},Ea={[P]:"url"},Aa={[m]:"uriEncode",[y]:[G],[B]:"uri_encoded_bucket"},Ue={[ua]:"S3Express",[Ie]:[{[Ae]:!0,[fe]:"sigv4",[ke]:"s3express",[Me]:"{Region}"}]},T={},ka={[m]:da,[y]:[G,!1]},xr={[C]:"S3Express bucket name is not a valid virtual hostable name.",[l]:C},er={[ua]:"S3Express",[Ie]:[{[Ae]:!0,[fe]:"sigv4-s3express",[ke]:"s3express",[Me]:"{Region}"}]},Ws={[m]:Re,[y]:[{[P]:"UseS3ExpressControlEndpoint"}]},Ks={[m]:oe,[y]:[{[P]:"UseS3ExpressControlEndpoint"},!0]},I={[m]:Oe,[y]:[Q]},Gs={[C]:"Unrecognized S3Express bucket name format.",[l]:C},Zs={[m]:Oe,[y]:[Jt]},Xs={[P]:ha},Qs={[f]:[I],[C]:"Expected a endpoint to be specified but no endpoint was found",[l]:C},_t={[Ie]:[{[Ae]:!0,[fe]:On,[ke]:Nt,[Nn]:["*"]},{[Ae]:!0,[fe]:"sigv4",[ke]:Nt,[Me]:"{Region}"}]},Rr={[m]:oe,[y]:[{[P]:"ForcePathStyle"},!1]},bp={[P]:"ForcePathStyle"},X={[m]:oe,[y]:[{[P]:"Accelerate"},!1]},re={[m]:D,[y]:[{[P]:"Region"},"aws-global"]},ne={[Ie]:[{[Ae]:!0,[fe]:"sigv4",[ke]:lt,[Me]:"us-east-1"}]},$={[m]:Oe,[y]:[re]},se={[m]:oe,[y]:[{[P]:"UseGlobalEndpoint"},!0]},Ys={[E]:"https://{Bucket}.s3-fips.dualstack.{Region}.{partitionResult#dnsSuffix}",[A]:{[Ie]:[{[Ae]:!0,[fe]:"sigv4",[ke]:lt,[Me]:"{Region}"}]},[k]:{}},J={[Ie]:[{[Ae]:!0,[fe]:"sigv4",[ke]:lt,[Me]:"{Region}"}]},ie={[m]:oe,[y]:[{[P]:"UseGlobalEndpoint"},!1]},U={[m]:oe,[y]:[{[P]:"UseDualStack"},!1]},Js={[E]:"https://{Bucket}.s3-fips.{Region}.{partitionResult#dnsSuffix}",[A]:J,[k]:{}},O={[m]:oe,[y]:[{[P]:"UseFIPS"},!1]},ei={[E]:"https://{Bucket}.s3-accelerate.dualstack.{partitionResult#dnsSuffix}",[A]:J,[k]:{}},ti={[E]:"https://{Bucket}.s3.dualstack.{Region}.{partitionResult#dnsSuffix}",[A]:J,[k]:{}},Cr={[m]:oe,[y]:[{[m]:K,[y]:[Ea,"isIp"]},!1]},Pr={[E]:pa,[A]:J,[k]:{}},fn={[E]:Ft,[A]:J,[k]:{}},ri={[v]:fn,[l]:v},Tr={[E]:ga,[A]:J,[k]:{}},ni={[E]:"https://{Bucket}.s3.{Region}.{partitionResult#dnsSuffix}",[A]:J,[k]:{}},Dt={[C]:"Invalid region: region was not a valid DNS name.",[l]:C},ve={[P]:ya},xa={[P]:wa},Ir={[m]:K,[y]:[ve,"service"]},$n={[P]:Un},si={[f]:[H],[C]:"S3 Object Lambda does not support Dual-stack",[l]:C},ii={[f]:[Pe],[C]:"S3 Object Lambda does not support S3 Accelerate",[l]:C},oi={[f]:[{[m]:Re,[y]:[{[P]:"DisableAccessPoints"}]},{[m]:oe,[y]:[{[P]:"DisableAccessPoints"},!0]}],[C]:"Access points are not supported for this operation",[l]:C},Nr={[f]:[{[m]:Re,[y]:[{[P]:"UseArnRegion"}]},{[m]:oe,[y]:[{[P]:"UseArnRegion"},!1]},{[m]:Oe,[y]:[{[m]:D,[y]:[{[m]:K,[y]:[ve,"region"]},"{Region}"]}]}],[C]:"Invalid configuration: region from ARN `{bucketArn#region}` does not match client region `{Region}` and UseArnRegion is `false`",[l]:C},Ra={[m]:K,[y]:[{[P]:"bucketPartition"},fe]},Ca={[m]:K,[y]:[ve,"accountId"]},Or={[Ie]:[{[Ae]:!0,[fe]:"sigv4",[ke]:Mn,[Me]:"{bucketArn#region}"}]},ai={[C]:"Invalid ARN: The access point name may only contain a-z, A-Z, 0-9 and `-`. Found: `{accessPointName}`",[l]:C},Mr={[C]:"Invalid ARN: The account id may only contain a-z, A-Z, 0-9 and `-`. Found: `{bucketArn#accountId}`",[l]:C},Ur={[C]:"Invalid region in ARN: `{bucketArn#region}` (invalid DNS name)",[l]:C},$r={[C]:"Client was configured for partition `{partitionResult#name}` but ARN (`{Bucket}`) has `{bucketPartition#name}`",[l]:C},ci={[C]:"Invalid ARN: The ARN may only contain a single resource component after `accesspoint`.",[l]:C},ui={[C]:"Invalid ARN: Expected a resource of the format `accesspoint:<accesspoint name>` but no name was provided",[l]:C},wt={[Ie]:[{[Ae]:!0,[fe]:"sigv4",[ke]:lt,[Me]:"{bucketArn#region}"}]},li={[Ie]:[{[Ae]:!0,[fe]:On,[ke]:Nt,[Nn]:["*"]},{[Ae]:!0,[fe]:"sigv4",[ke]:Nt,[Me]:"{bucketArn#region}"}]},di={[m]:ma,[y]:[G]},hi={[E]:"https://s3-fips.dualstack.{Region}.{partitionResult#dnsSuffix}/{uri_encoded_bucket}",[A]:J,[k]:{}},fi={[E]:"https://s3-fips.{Region}.{partitionResult#dnsSuffix}/{uri_encoded_bucket}",[A]:J,[k]:{}},pi={[E]:"https://s3.dualstack.{Region}.{partitionResult#dnsSuffix}/{uri_encoded_bucket}",[A]:J,[k]:{}},_r={[E]:va,[A]:J,[k]:{}},gi={[E]:"https://s3.{Region}.{partitionResult#dnsSuffix}/{uri_encoded_bucket}",[A]:J,[k]:{}},mi={[P]:"UseObjectLambdaEndpoint"},Dr={[Ie]:[{[Ae]:!0,[fe]:"sigv4",[ke]:Mn,[Me]:"{Region}"}]},yi={[E]:"https://s3-fips.dualstack.{Region}.{partitionResult#dnsSuffix}",[A]:J,[k]:{}},wi={[E]:"https://s3-fips.{Region}.{partitionResult#dnsSuffix}",[A]:J,[k]:{}},bi={[E]:"https://s3.dualstack.{Region}.{partitionResult#dnsSuffix}",[A]:J,[k]:{}},Br={[E]:Ht,[A]:J,[k]:{}},vi={[E]:"https://s3.{Region}.{partitionResult#dnsSuffix}",[A]:J,[k]:{}},Lr=[{[P]:"Region"}],vp=[{[P]:"Endpoint"}],Sp=[G],Fr=[H],Bt=[Pe],Ge=[Q,me],Si=[{[m]:Re,[y]:[{[P]:"DisableS3ExpressSessionAuth"}]},{[m]:oe,[y]:[{[P]:"DisableS3ExpressSessionAuth"},!0]}],Ei=[qt],Hr=[Aa],qr=[ka],nt=[q],Ai=[{[m]:he,[y]:[G,6,14,!0],[B]:"s3expressAvailabilityZoneId"},{[m]:he,[y]:[G,14,16,!0],[B]:"s3expressAvailabilityZoneDelim"},{[m]:D,[y]:[{[P]:"s3expressAvailabilityZoneDelim"},"--"]}],bt=[{[f]:[q],[v]:{[E]:"https://{Bucket}.s3express-fips-{s3expressAvailabilityZoneId}.{Region}.amazonaws.com",[A]:Ue,[k]:{}},[l]:v},{[v]:{[E]:"https://{Bucket}.s3express-{s3expressAvailabilityZoneId}.{Region}.amazonaws.com",[A]:Ue,[k]:{}},[l]:v}],ki=[{[m]:he,[y]:[G,6,15,!0],[B]:"s3expressAvailabilityZoneId"},{[m]:he,[y]:[G,15,17,!0],[B]:"s3expressAvailabilityZoneDelim"},{[m]:D,[y]:[{[P]:"s3expressAvailabilityZoneDelim"},"--"]}],xi=[{[m]:he,[y]:[G,6,19,!0],[B]:"s3expressAvailabilityZoneId"},{[m]:he,[y]:[G,19,21,!0],[B]:"s3expressAvailabilityZoneDelim"},{[m]:D,[y]:[{[P]:"s3expressAvailabilityZoneDelim"},"--"]}],Ri=[{[m]:he,[y]:[G,6,20,!0],[B]:"s3expressAvailabilityZoneId"},{[m]:he,[y]:[G,20,22,!0],[B]:"s3expressAvailabilityZoneDelim"},{[m]:D,[y]:[{[P]:"s3expressAvailabilityZoneDelim"},"--"]}],Ci=[{[m]:he,[y]:[G,6,26,!0],[B]:"s3expressAvailabilityZoneId"},{[m]:he,[y]:[G,26,28,!0],[B]:"s3expressAvailabilityZoneDelim"},{[m]:D,[y]:[{[P]:"s3expressAvailabilityZoneDelim"},"--"]}],vt=[{[f]:[q],[v]:{[E]:"https://{Bucket}.s3express-fips-{s3expressAvailabilityZoneId}.{Region}.amazonaws.com",[A]:er,[k]:{}},[l]:v},{[v]:{[E]:"https://{Bucket}.s3express-{s3expressAvailabilityZoneId}.{Region}.amazonaws.com",[A]:er,[k]:{}},[l]:v}],Ep=[Jt],Pi=[{[m]:et,[y]:[{[P]:hn},!1]}],Ti=[{[m]:D,[y]:[{[P]:fa},"beta"]}],Ap=["*"],_e=[Sa],Ii=[{[m]:et,[y]:[{[P]:"Region"},!1]}],De=[{[m]:D,[y]:[{[P]:"Region"},"us-east-1"]}],zr=[{[m]:D,[y]:[xa,ba]}],Ni=[{[m]:K,[y]:[ve,"resourceId[1]"],[B]:Un},{[m]:Oe,[y]:[{[m]:D,[y]:[$n,Yt]}]}],kp=[ve,"resourceId[1]"],jr=[{[m]:Oe,[y]:[{[m]:D,[y]:[{[m]:K,[y]:[ve,"region"]},Yt]}]}],Oi=[{[m]:Oe,[y]:[{[m]:Re,[y]:[{[m]:K,[y]:[ve,"resourceId[2]"]}]}]}],xp=[ve,"resourceId[2]"],Vr=[{[m]:Qt,[y]:[{[m]:K,[y]:[ve,"region"]}],[B]:"bucketPartition"}],Mi=[{[m]:D,[y]:[Ra,{[m]:K,[y]:[{[P]:"partitionResult"},fe]}]}],Wr=[{[m]:et,[y]:[{[m]:K,[y]:[ve,"region"]},!0]}],Kr=[{[m]:et,[y]:[Ca,!1]}],Ui=[{[m]:et,[y]:[$n,!1]}],$i=[{[m]:et,[y]:[{[P]:"Region"},!0]}],Rp={parameters:{Bucket:tt,Region:tt,UseFIPS:rt,UseDualStack:rt,Endpoint:tt,ForcePathStyle:rt,Accelerate:rt,UseGlobalEndpoint:rt,UseObjectLambdaEndpoint:yt,Key:tt,Prefix:tt,CopySource:tt,DisableAccessPoints:yt,DisableMultiRegionAccessPoints:rt,UseArnRegion:yt,UseS3ExpressControlEndpoint:yt,DisableS3ExpressSessionAuth:yt},rules:[{[f]:[{[m]:Re,[y]:Lr}],rules:[{[f]:[Pe,q],error:"Accelerate cannot be used with FIPS",[l]:C},{[f]:[H,Q],error:"Cannot set dual-stack in combination with a custom endpoint.",[l]:C},{[f]:[Q,q],error:"A custom endpoint cannot be combined with FIPS",[l]:C},{[f]:[Q,Pe],error:"A custom endpoint cannot be combined with S3 Accelerate",[l]:C},{[f]:[q,Sa,Vs],error:"Partition does not support FIPS",[l]:C},{[f]:[Jt,{[m]:he,[y]:[G,0,yp,Ce],[B]:$s},{[m]:D,[y]:[{[P]:$s},"--x-s3"]}],rules:[{[f]:Fr,error:"S3Express does not support Dual-stack.",[l]:C},{[f]:Bt,error:"S3Express does not support S3 Accelerate.",[l]:C},{[f]:Ge,rules:[{[f]:Si,rules:[{[f]:Ei,rules:[{[f]:Hr,rules:[{endpoint:{[E]:_s,[A]:Ue,[k]:T},[l]:v}],[l]:b}],[l]:b},{[f]:qr,rules:[{endpoint:{[E]:Ft,[A]:Ue,[k]:T},[l]:v}],[l]:b},xr],[l]:b},{[f]:Ei,rules:[{[f]:Hr,rules:[{endpoint:{[E]:_s,[A]:er,[k]:T},[l]:v}],[l]:b}],[l]:b},{[f]:qr,rules:[{endpoint:{[E]:Ft,[A]:er,[k]:T},[l]:v}],[l]:b},xr],[l]:b},{[f]:[Ws,Ks],rules:[{[f]:[Aa,I],rules:[{[f]:nt,endpoint:{[E]:"https://s3express-control-fips.{Region}.amazonaws.com/{uri_encoded_bucket}",[A]:Ue,[k]:T},[l]:v},{endpoint:{[E]:"https://s3express-control.{Region}.amazonaws.com/{uri_encoded_bucket}",[A]:Ue,[k]:T},[l]:v}],[l]:b}],[l]:b},{[f]:qr,rules:[{[f]:Si,rules:[{[f]:Ai,rules:bt,[l]:b},{[f]:ki,rules:bt,[l]:b},{[f]:xi,rules:bt,[l]:b},{[f]:Ri,rules:bt,[l]:b},{[f]:Ci,rules:bt,[l]:b},Gs],[l]:b},{[f]:Ai,rules:vt,[l]:b},{[f]:ki,rules:vt,[l]:b},{[f]:xi,rules:vt,[l]:b},{[f]:Ri,rules:vt,[l]:b},{[f]:Ci,rules:vt,[l]:b},Gs],[l]:b},xr],[l]:b},{[f]:[Zs,Ws,Ks],rules:[{[f]:Ge,endpoint:{[E]:Ht,[A]:Ue,[k]:T},[l]:v},{[f]:nt,endpoint:{[E]:"https://s3express-control-fips.{Region}.amazonaws.com",[A]:Ue,[k]:T},[l]:v},{endpoint:{[E]:"https://s3express-control.{Region}.amazonaws.com",[A]:Ue,[k]:T},[l]:v}],[l]:b},{[f]:[Jt,{[m]:he,[y]:[G,49,50,Ce],[B]:ha},{[m]:he,[y]:[G,8,12,Ce],[B]:fa},{[m]:he,[y]:[G,0,7,Ce],[B]:Ds},{[m]:he,[y]:[G,32,49,Ce],[B]:hn},{[m]:Qt,[y]:Lr,[B]:"regionPartition"},{[m]:D,[y]:[{[P]:Ds},"--op-s3"]}],rules:[{[f]:Pi,rules:[{[f]:[{[m]:D,[y]:[Xs,"e"]}],rules:[{[f]:Ti,rules:[Qs,{[f]:Ge,endpoint:{[E]:"https://{Bucket}.ec2.{url#authority}",[A]:_t,[k]:T},[l]:v}],[l]:b},{endpoint:{[E]:"https://{Bucket}.ec2.s3-outposts.{Region}.{regionPartition#dnsSuffix}",[A]:_t,[k]:T},[l]:v}],[l]:b},{[f]:[{[m]:D,[y]:[Xs,"o"]}],rules:[{[f]:Ti,rules:[Qs,{[f]:Ge,endpoint:{[E]:"https://{Bucket}.op-{outpostId}.{url#authority}",[A]:_t,[k]:T},[l]:v}],[l]:b},{endpoint:{[E]:"https://{Bucket}.op-{outpostId}.s3-outposts.{Region}.{regionPartition#dnsSuffix}",[A]:_t,[k]:T},[l]:v}],[l]:b},{error:'Unrecognized hardware type: "Expected hardware type o or e but got {hardwareType}"',[l]:C}],[l]:b},{error:"Invalid ARN: The outpost Id must only contain a-z, A-Z, 0-9 and `-`.",[l]:C}],[l]:b},{[f]:Ep,rules:[{[f]:[Q,{[m]:Oe,[y]:[{[m]:Re,[y]:[{[m]:la,[y]:vp}]}]}],error:"Custom endpoint `{Endpoint}` was not a valid URI",[l]:C},{[f]:[Rr,ka],rules:[{[f]:_e,rules:[{[f]:Ii,rules:[{[f]:[Pe,Vs],error:"S3 Accelerate cannot be used in this region",[l]:C},{[f]:[H,q,X,I,re],endpoint:{[E]:"https://{Bucket}.s3-fips.dualstack.us-east-1.{partitionResult#dnsSuffix}",[A]:ne,[k]:T},[l]:v},{[f]:[H,q,X,I,$,se],rules:[{endpoint:Ys,[l]:v}],[l]:b},{[f]:[H,q,X,I,$,ie],endpoint:Ys,[l]:v},{[f]:[U,q,X,I,re],endpoint:{[E]:"https://{Bucket}.s3-fips.us-east-1.{partitionResult#dnsSuffix}",[A]:ne,[k]:T},[l]:v},{[f]:[U,q,X,I,$,se],rules:[{endpoint:Js,[l]:v}],[l]:b},{[f]:[U,q,X,I,$,ie],endpoint:Js,[l]:v},{[f]:[H,O,Pe,I,re],endpoint:{[E]:"https://{Bucket}.s3-accelerate.dualstack.us-east-1.{partitionResult#dnsSuffix}",[A]:ne,[k]:T},[l]:v},{[f]:[H,O,Pe,I,$,se],rules:[{endpoint:ei,[l]:v}],[l]:b},{[f]:[H,O,Pe,I,$,ie],endpoint:ei,[l]:v},{[f]:[H,O,X,I,re],endpoint:{[E]:"https://{Bucket}.s3.dualstack.us-east-1.{partitionResult#dnsSuffix}",[A]:ne,[k]:T},[l]:v},{[f]:[H,O,X,I,$,se],rules:[{endpoint:ti,[l]:v}],[l]:b},{[f]:[H,O,X,I,$,ie],endpoint:ti,[l]:v},{[f]:[U,O,X,Q,me,qt,re],endpoint:{[E]:pa,[A]:ne,[k]:T},[l]:v},{[f]:[U,O,X,Q,me,Cr,re],endpoint:{[E]:Ft,[A]:ne,[k]:T},[l]:v},{[f]:[U,O,X,Q,me,qt,$,se],rules:[{[f]:De,endpoint:Pr,[l]:v},{endpoint:Pr,[l]:v}],[l]:b},{[f]:[U,O,X,Q,me,Cr,$,se],rules:[{[f]:De,endpoint:fn,[l]:v},ri],[l]:b},{[f]:[U,O,X,Q,me,qt,$,ie],endpoint:Pr,[l]:v},{[f]:[U,O,X,Q,me,Cr,$,ie],endpoint:fn,[l]:v},{[f]:[U,O,Pe,I,re],endpoint:{[E]:ga,[A]:ne,[k]:T},[l]:v},{[f]:[U,O,Pe,I,$,se],rules:[{[f]:De,endpoint:Tr,[l]:v},{endpoint:Tr,[l]:v}],[l]:b},{[f]:[U,O,Pe,I,$,ie],endpoint:Tr,[l]:v},{[f]:[U,O,X,I,re],endpoint:{[E]:Bs,[A]:ne,[k]:T},[l]:v},{[f]:[U,O,X,I,$,se],rules:[{[f]:De,endpoint:{[E]:Bs,[A]:J,[k]:T},[l]:v},{endpoint:ni,[l]:v}],[l]:b},{[f]:[U,O,X,I,$,ie],endpoint:ni,[l]:v}],[l]:b},Dt],[l]:b}],[l]:b},{[f]:[Q,me,{[m]:D,[y]:[{[m]:K,[y]:[Ea,"scheme"]},"http"]},{[m]:da,[y]:[G,Ce]},Rr,O,U,X],rules:[{[f]:_e,rules:[{[f]:Ii,rules:[ri],[l]:b},Dt],[l]:b}],[l]:b},{[f]:[Rr,{[m]:ma,[y]:Sp,[B]:ya}],rules:[{[f]:[{[m]:K,[y]:[ve,"resourceId[0]"],[B]:wa},{[m]:Oe,[y]:[{[m]:D,[y]:[xa,Yt]}]}],rules:[{[f]:[{[m]:D,[y]:[Ir,Mn]}],rules:[{[f]:zr,rules:[{[f]:Ni,rules:[si,ii,{[f]:jr,rules:[oi,{[f]:Oi,rules:[Nr,{[f]:Vr,rules:[{[f]:_e,rules:[{[f]:Mi,rules:[{[f]:Wr,rules:[{[f]:[{[m]:D,[y]:[Ca,Yt]}],error:"Invalid ARN: Missing account id",[l]:C},{[f]:Kr,rules:[{[f]:Ui,rules:[{[f]:Ge,endpoint:{[E]:Ls,[A]:Or,[k]:T},[l]:v},{[f]:nt,endpoint:{[E]:"https://{accessPointName}-{bucketArn#accountId}.s3-object-lambda-fips.{bucketArn#region}.{bucketPartition#dnsSuffix}",[A]:Or,[k]:T},[l]:v},{endpoint:{[E]:"https://{accessPointName}-{bucketArn#accountId}.s3-object-lambda.{bucketArn#region}.{bucketPartition#dnsSuffix}",[A]:Or,[k]:T},[l]:v}],[l]:b},ai],[l]:b},Mr],[l]:b},Ur],[l]:b},$r],[l]:b}],[l]:b}],[l]:b},ci],[l]:b},{error:"Invalid ARN: bucket ARN is missing a region",[l]:C}],[l]:b},ui],[l]:b},{error:"Invalid ARN: Object Lambda ARNs only support `accesspoint` arn types, but found: `{arnType}`",[l]:C}],[l]:b},{[f]:zr,rules:[{[f]:Ni,rules:[{[f]:jr,rules:[{[f]:zr,rules:[{[f]:jr,rules:[oi,{[f]:Oi,rules:[Nr,{[f]:Vr,rules:[{[f]:_e,rules:[{[f]:[{[m]:D,[y]:[Ra,"{partitionResult#name}"]}],rules:[{[f]:Wr,rules:[{[f]:[{[m]:D,[y]:[Ir,lt]}],rules:[{[f]:Kr,rules:[{[f]:Ui,rules:[{[f]:Bt,error:"Access Points do not support S3 Accelerate",[l]:C},{[f]:[q,H],endpoint:{[E]:"https://{accessPointName}-{bucketArn#accountId}.s3-accesspoint-fips.dualstack.{bucketArn#region}.{bucketPartition#dnsSuffix}",[A]:wt,[k]:T},[l]:v},{[f]:[q,U],endpoint:{[E]:"https://{accessPointName}-{bucketArn#accountId}.s3-accesspoint-fips.{bucketArn#region}.{bucketPartition#dnsSuffix}",[A]:wt,[k]:T},[l]:v},{[f]:[O,H],endpoint:{[E]:"https://{accessPointName}-{bucketArn#accountId}.s3-accesspoint.dualstack.{bucketArn#region}.{bucketPartition#dnsSuffix}",[A]:wt,[k]:T},[l]:v},{[f]:[O,U,Q,me],endpoint:{[E]:Ls,[A]:wt,[k]:T},[l]:v},{[f]:[O,U],endpoint:{[E]:"https://{accessPointName}-{bucketArn#accountId}.s3-accesspoint.{bucketArn#region}.{bucketPartition#dnsSuffix}",[A]:wt,[k]:T},[l]:v}],[l]:b},ai],[l]:b},Mr],[l]:b},{error:"Invalid ARN: The ARN was not for the S3 service, found: {bucketArn#service}",[l]:C}],[l]:b},Ur],[l]:b},$r],[l]:b}],[l]:b}],[l]:b},ci],[l]:b}],[l]:b}],[l]:b},{[f]:[{[m]:et,[y]:[$n,Ce]}],rules:[{[f]:Fr,error:"S3 MRAP does not support dual-stack",[l]:C},{[f]:nt,error:"S3 MRAP does not support FIPS",[l]:C},{[f]:Bt,error:"S3 MRAP does not support S3 Accelerate",[l]:C},{[f]:[{[m]:oe,[y]:[{[P]:"DisableMultiRegionAccessPoints"},Ce]}],error:"Invalid configuration: Multi-Region Access Point ARNs are disabled.",[l]:C},{[f]:[{[m]:Qt,[y]:Lr,[B]:Fs}],rules:[{[f]:[{[m]:D,[y]:[{[m]:K,[y]:[{[P]:Fs},fe]},{[m]:K,[y]:[ve,"partition"]}]}],rules:[{endpoint:{[E]:"https://{accessPointName}.accesspoint.s3-global.{mrapPartition#dnsSuffix}",[A]:{[Ie]:[{[Ae]:Ce,name:On,[ke]:lt,[Nn]:Ap}]},[k]:T},[l]:v}],[l]:b},{error:"Client was configured for partition `{mrapPartition#name}` but bucket referred to partition `{bucketArn#partition}`",[l]:C}],[l]:b}],[l]:b},{error:"Invalid Access Point Name",[l]:C}],[l]:b},ui],[l]:b},{[f]:[{[m]:D,[y]:[Ir,Nt]}],rules:[{[f]:Fr,error:"S3 Outposts does not support Dual-stack",[l]:C},{[f]:nt,error:"S3 Outposts does not support FIPS",[l]:C},{[f]:Bt,error:"S3 Outposts does not support S3 Accelerate",[l]:C},{[f]:[{[m]:Re,[y]:[{[m]:K,[y]:[ve,"resourceId[4]"]}]}],error:"Invalid Arn: Outpost Access Point ARN contains sub resources",[l]:C},{[f]:[{[m]:K,[y]:kp,[B]:hn}],rules:[{[f]:Pi,rules:[Nr,{[f]:Vr,rules:[{[f]:_e,rules:[{[f]:Mi,rules:[{[f]:Wr,rules:[{[f]:Kr,rules:[{[f]:[{[m]:K,[y]:xp,[B]:Hs}],rules:[{[f]:[{[m]:K,[y]:[ve,"resourceId[3]"],[B]:Un}],rules:[{[f]:[{[m]:D,[y]:[{[P]:Hs},ba]}],rules:[{[f]:Ge,endpoint:{[E]:"https://{accessPointName}-{bucketArn#accountId}.{outpostId}.{url#authority}",[A]:li,[k]:T},[l]:v},{endpoint:{[E]:"https://{accessPointName}-{bucketArn#accountId}.{outpostId}.s3-outposts.{bucketArn#region}.{bucketPartition#dnsSuffix}",[A]:li,[k]:T},[l]:v}],[l]:b},{error:"Expected an outpost type `accesspoint`, found {outpostType}",[l]:C}],[l]:b},{error:"Invalid ARN: expected an access point name",[l]:C}],[l]:b},{error:"Invalid ARN: Expected a 4-component resource",[l]:C}],[l]:b},Mr],[l]:b},Ur],[l]:b},$r],[l]:b}],[l]:b}],[l]:b},{error:"Invalid ARN: The outpost Id may only contain a-z, A-Z, 0-9 and `-`. Found: `{outpostId}`",[l]:C}],[l]:b},{error:"Invalid ARN: The Outpost Id was not set",[l]:C}],[l]:b},{error:"Invalid ARN: Unrecognized format: {Bucket} (type: {arnType})",[l]:C}],[l]:b},{error:"Invalid ARN: No ARN type specified",[l]:C}],[l]:b},{[f]:[{[m]:he,[y]:[G,0,4,wp],[B]:qs},{[m]:D,[y]:[{[P]:qs},"arn:"]},{[m]:Oe,[y]:[{[m]:Re,[y]:[di]}]}],error:"Invalid ARN: `{Bucket}` was not a valid ARN",[l]:C},{[f]:[{[m]:oe,[y]:[bp,Ce]},di],error:"Path-style addressing cannot be used with ARN buckets",[l]:C},{[f]:Hr,rules:[{[f]:_e,rules:[{[f]:[X],rules:[{[f]:[H,I,q,re],endpoint:{[E]:"https://s3-fips.dualstack.us-east-1.{partitionResult#dnsSuffix}/{uri_encoded_bucket}",[A]:ne,[k]:T},[l]:v},{[f]:[H,I,q,$,se],rules:[{endpoint:hi,[l]:v}],[l]:b},{[f]:[H,I,q,$,ie],endpoint:hi,[l]:v},{[f]:[U,I,q,re],endpoint:{[E]:"https://s3-fips.us-east-1.{partitionResult#dnsSuffix}/{uri_encoded_bucket}",[A]:ne,[k]:T},[l]:v},{[f]:[U,I,q,$,se],rules:[{endpoint:fi,[l]:v}],[l]:b},{[f]:[U,I,q,$,ie],endpoint:fi,[l]:v},{[f]:[H,I,O,re],endpoint:{[E]:"https://s3.dualstack.us-east-1.{partitionResult#dnsSuffix}/{uri_encoded_bucket}",[A]:ne,[k]:T},[l]:v},{[f]:[H,I,O,$,se],rules:[{endpoint:pi,[l]:v}],[l]:b},{[f]:[H,I,O,$,ie],endpoint:pi,[l]:v},{[f]:[U,Q,me,O,re],endpoint:{[E]:va,[A]:ne,[k]:T},[l]:v},{[f]:[U,Q,me,O,$,se],rules:[{[f]:De,endpoint:_r,[l]:v},{endpoint:_r,[l]:v}],[l]:b},{[f]:[U,Q,me,O,$,ie],endpoint:_r,[l]:v},{[f]:[U,I,O,re],endpoint:{[E]:zs,[A]:ne,[k]:T},[l]:v},{[f]:[U,I,O,$,se],rules:[{[f]:De,endpoint:{[E]:zs,[A]:J,[k]:T},[l]:v},{endpoint:gi,[l]:v}],[l]:b},{[f]:[U,I,O,$,ie],endpoint:gi,[l]:v}],[l]:b},{error:"Path-style addressing cannot be used with S3 Accelerate",[l]:C}],[l]:b}],[l]:b}],[l]:b},{[f]:[{[m]:Re,[y]:[mi]},{[m]:oe,[y]:[mi,Ce]}],rules:[{[f]:_e,rules:[{[f]:$i,rules:[si,ii,{[f]:Ge,endpoint:{[E]:Ht,[A]:Dr,[k]:T},[l]:v},{[f]:nt,endpoint:{[E]:"https://s3-object-lambda-fips.{Region}.{partitionResult#dnsSuffix}",[A]:Dr,[k]:T},[l]:v},{endpoint:{[E]:"https://s3-object-lambda.{Region}.{partitionResult#dnsSuffix}",[A]:Dr,[k]:T},[l]:v}],[l]:b},Dt],[l]:b}],[l]:b},{[f]:[Zs],rules:[{[f]:_e,rules:[{[f]:$i,rules:[{[f]:[q,H,I,re],endpoint:{[E]:"https://s3-fips.dualstack.us-east-1.{partitionResult#dnsSuffix}",[A]:ne,[k]:T},[l]:v},{[f]:[q,H,I,$,se],rules:[{endpoint:yi,[l]:v}],[l]:b},{[f]:[q,H,I,$,ie],endpoint:yi,[l]:v},{[f]:[q,U,I,re],endpoint:{[E]:"https://s3-fips.us-east-1.{partitionResult#dnsSuffix}",[A]:ne,[k]:T},[l]:v},{[f]:[q,U,I,$,se],rules:[{endpoint:wi,[l]:v}],[l]:b},{[f]:[q,U,I,$,ie],endpoint:wi,[l]:v},{[f]:[O,H,I,re],endpoint:{[E]:"https://s3.dualstack.us-east-1.{partitionResult#dnsSuffix}",[A]:ne,[k]:T},[l]:v},{[f]:[O,H,I,$,se],rules:[{endpoint:bi,[l]:v}],[l]:b},{[f]:[O,H,I,$,ie],endpoint:bi,[l]:v},{[f]:[O,U,Q,me,re],endpoint:{[E]:Ht,[A]:ne,[k]:T},[l]:v},{[f]:[O,U,Q,me,$,se],rules:[{[f]:De,endpoint:Br,[l]:v},{endpoint:Br,[l]:v}],[l]:b},{[f]:[O,U,Q,me,$,ie],endpoint:Br,[l]:v},{[f]:[O,U,I,re],endpoint:{[E]:js,[A]:ne,[k]:T},[l]:v},{[f]:[O,U,I,$,se],rules:[{[f]:De,endpoint:{[E]:js,[A]:J,[k]:T},[l]:v},{endpoint:vi,[l]:v}],[l]:b},{[f]:[O,U,I,$,ie],endpoint:vi,[l]:v}],[l]:b},Dt],[l]:b}],[l]:b}],[l]:b},{error:"A region must be set when sending requests to S3.",[l]:C}]},Cp=Rp,Pp=new Bh({size:50,params:["Accelerate","Bucket","DisableAccessPoints","DisableMultiRegionAccessPoints","DisableS3ExpressSessionAuth","Endpoint","ForcePathStyle","Region","UseArnRegion","UseDualStack","UseFIPS","UseGlobalEndpoint","UseObjectLambdaEndpoint","UseS3ExpressControlEndpoint"]}),Pa=(e,t={})=>Pp.get(e,()=>nf(Cp,{endpointParams:e,logger:t.logger}));Zt.aws=ea;const Tp=e=>async(t,r,n)=>{var s,i,o;if(!n)throw new Error("Could not find `input` for `defaultEndpointRuleSetHttpAuthSchemeParametersProvider`");const a=await e(t,r,n),c=(o=(i=(s=ft(r))==null?void 0:s.commandInstance)==null?void 0:i.constructor)==null?void 0:o.getEndpointParameterInstructions;if(!c)throw new Error(`getEndpointParameterInstructions() is not defined on \`${r.commandName}\``);const u=await sa(n,{getEndpointParameterInstructions:c},t);return Object.assign(a,u)},Ip=async(e,t,r)=>({operation:ft(t).operation,region:await Ne(e.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()}),Np=Tp(Ip);function Ta(e){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"s3",region:e.region},propertiesExtractor:(t,r)=>({signingProperties:{config:t,context:r}})}}function Ia(e){return{schemeId:"aws.auth#sigv4a",signingProperties:{name:"s3",region:e.region},propertiesExtractor:(t,r)=>({signingProperties:{config:t,context:r}})}}const Op=(e,t,r)=>n=>{var s;const i=(s=e(n).properties)==null?void 0:s.authSchemes;if(!i)return t(n);const o=[];for(const a of i){const{name:c,properties:u={},...h}=a,d=c.toLowerCase();let p;if(d==="sigv4a"){if(p="aws.auth#sigv4a",i.find(R=>{const x=R.name.toLowerCase();return x!=="sigv4a"&&x.startsWith("sigv4")}))continue}else if(d.startsWith("sigv4"))p="aws.auth#sigv4";else throw new Error(`Unknown HttpAuthScheme found in \`@smithy.rules#endpointRuleSet\`: \`${d}\``);const g=r[p];if(!g)throw new Error(`Could not find HttpAuthOption create function for \`${p}\``);const S=g(n);S.schemeId=p,S.signingProperties={...S.signingProperties||{},...h,...u},o.push(S)}return o},Mp=e=>{const t=[];switch(e.operation){default:t.push(Ta(e)),t.push(Ia(e))}return t},Up=Op(Pa,Mp,{"aws.auth#sigv4":Ta,"aws.auth#sigv4a":Ia}),$p=e=>{const t=Tu(e);return{...su(t)}},_p=e=>({...e,useFipsEndpoint:e.useFipsEndpoint??!1,useDualstackEndpoint:e.useDualstackEndpoint??!1,forcePathStyle:e.forcePathStyle??!1,useAccelerateEndpoint:e.useAccelerateEndpoint??!1,useGlobalEndpoint:e.useGlobalEndpoint??!1,disableMultiregionAccessPoints:e.disableMultiregionAccessPoints??!1,defaultSigningName:"s3"}),Na={ForcePathStyle:{type:"clientContextParams",name:"forcePathStyle"},UseArnRegion:{type:"clientContextParams",name:"useArnRegion"},DisableMultiRegionAccessPoints:{type:"clientContextParams",name:"disableMultiregionAccessPoints"},Accelerate:{type:"clientContextParams",name:"useAccelerateEndpoint"},DisableS3ExpressSessionAuth:{type:"clientContextParams",name:"disableS3ExpressSessionAuth"},UseGlobalEndpoint:{type:"builtInParams",name:"useGlobalEndpoint"},UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}};class pe extends it{constructor(t){super(t),Object.setPrototypeOf(this,pe.prototype)}}class _n extends pe{constructor(t){super({name:"NoSuchUpload",$fault:"client",...t}),N(this,"name","NoSuchUpload"),N(this,"$fault","client"),Object.setPrototypeOf(this,_n.prototype)}}class Dn extends pe{constructor(t){super({name:"ObjectNotInActiveTierError",$fault:"client",...t}),N(this,"name","ObjectNotInActiveTierError"),N(this,"$fault","client"),Object.setPrototypeOf(this,Dn.prototype)}}class Bn extends pe{constructor(t){super({name:"BucketAlreadyExists",$fault:"client",...t}),N(this,"name","BucketAlreadyExists"),N(this,"$fault","client"),Object.setPrototypeOf(this,Bn.prototype)}}class Ln extends pe{constructor(t){super({name:"BucketAlreadyOwnedByYou",$fault:"client",...t}),N(this,"name","BucketAlreadyOwnedByYou"),N(this,"$fault","client"),Object.setPrototypeOf(this,Ln.prototype)}}class Fn extends pe{constructor(t){super({name:"NoSuchBucket",$fault:"client",...t}),N(this,"name","NoSuchBucket"),N(this,"$fault","client"),Object.setPrototypeOf(this,Fn.prototype)}}var _i;(function(e){e.visit=(t,r)=>t.Prefix!==void 0?r.Prefix(t.Prefix):t.Tag!==void 0?r.Tag(t.Tag):t.And!==void 0?r.And(t.And):r._(t.$unknown[0],t.$unknown[1])})(_i||(_i={}));var Di;(function(e){e.visit=(t,r)=>t.Prefix!==void 0?r.Prefix(t.Prefix):t.Tag!==void 0?r.Tag(t.Tag):t.AccessPointArn!==void 0?r.AccessPointArn(t.AccessPointArn):t.And!==void 0?r.And(t.And):r._(t.$unknown[0],t.$unknown[1])})(Di||(Di={}));class Hn extends pe{constructor(t){super({name:"InvalidObjectState",$fault:"client",...t}),N(this,"name","InvalidObjectState"),N(this,"$fault","client"),N(this,"StorageClass"),N(this,"AccessTier"),Object.setPrototypeOf(this,Hn.prototype),this.StorageClass=t.StorageClass,this.AccessTier=t.AccessTier}}class qn extends pe{constructor(t){super({name:"NoSuchKey",$fault:"client",...t}),N(this,"name","NoSuchKey"),N(this,"$fault","client"),Object.setPrototypeOf(this,qn.prototype)}}class zn extends pe{constructor(t){super({name:"NotFound",$fault:"client",...t}),N(this,"name","NotFound"),N(this,"$fault","client"),Object.setPrototypeOf(this,zn.prototype)}}const Dp=e=>({...e,...e.SecretAccessKey&&{SecretAccessKey:Te},...e.SessionToken&&{SessionToken:Te}}),Bp=e=>({...e,...e.SSEKMSKeyId&&{SSEKMSKeyId:Te},...e.SSEKMSEncryptionContext&&{SSEKMSEncryptionContext:Te},...e.Credentials&&{Credentials:Dp(e.Credentials)}}),Lp=e=>({...e,...e.SSEKMSKeyId&&{SSEKMSKeyId:Te},...e.SSEKMSEncryptionContext&&{SSEKMSEncryptionContext:Te}});class jn extends pe{constructor(t){super({name:"EncryptionTypeMismatch",$fault:"client",...t}),N(this,"name","EncryptionTypeMismatch"),N(this,"$fault","client"),Object.setPrototypeOf(this,jn.prototype)}}class Vn extends pe{constructor(t){super({name:"InvalidRequest",$fault:"client",...t}),N(this,"name","InvalidRequest"),N(this,"$fault","client"),Object.setPrototypeOf(this,Vn.prototype)}}class Wn extends pe{constructor(t){super({name:"InvalidWriteOffset",$fault:"client",...t}),N(this,"name","InvalidWriteOffset"),N(this,"$fault","client"),Object.setPrototypeOf(this,Wn.prototype)}}class Kn extends pe{constructor(t){super({name:"TooManyParts",$fault:"client",...t}),N(this,"name","TooManyParts"),N(this,"$fault","client"),Object.setPrototypeOf(this,Kn.prototype)}}class Gn extends pe{constructor(t){super({name:"ObjectAlreadyInActiveTierError",$fault:"client",...t}),N(this,"name","ObjectAlreadyInActiveTierError"),N(this,"$fault","client"),Object.setPrototypeOf(this,Gn.prototype)}}var Bi;(function(e){e.visit=(t,r)=>t.Records!==void 0?r.Records(t.Records):t.Stats!==void 0?r.Stats(t.Stats):t.Progress!==void 0?r.Progress(t.Progress):t.Cont!==void 0?r.Cont(t.Cont):t.End!==void 0?r.End(t.End):r._(t.$unknown[0],t.$unknown[1])})(Bi||(Bi={}));const Fp=e=>({...e,...e.SSEKMSKeyId&&{SSEKMSKeyId:Te},...e.SSEKMSEncryptionContext&&{SSEKMSEncryptionContext:Te}}),Hp=e=>({...e,...e.SSECustomerKey&&{SSECustomerKey:Te},...e.SSEKMSKeyId&&{SSEKMSKeyId:Te},...e.SSEKMSEncryptionContext&&{SSEKMSEncryptionContext:Te}}),qp=async(e,t)=>{const r=ao(e,t),n=te({},He,{[Kg]:e[Pg],[ur]:e[or],[lr]:e[cr],[dr]:e[ar],[ht]:[()=>He(e[dt]),()=>e[dt].toString()]});r.bp("/"),r.p("Bucket",()=>e.Bucket,"{Bucket}",!1);const s=te({[Vg]:[,""]});return r.m("GET").h(n).q(s).b(void 0),r.build()},zp=async(e,t)=>{const r=ao(e,t),n=te({},He,{[Fg]:e[gg]||"application/octet-stream",[Wg]:e[ag],[Ug]:e[ug],[$g]:e[lg],[_g]:e[dg],[Dg]:e[hg],[Bg]:[()=>He(e[Li]),()=>e[Li].toString()],[Lg]:e[fg],[am]:e[cg],[Fa]:e[Ma],[Ha]:e[Ua],[qa]:e[$a],[za]:e[_a],[ja]:e[Da],[Hg]:[()=>He(e[Fi]),()=>Vu(e[Fi]).toString()],[zg]:e[Eg],[jg]:e[Ag],[Qg]:e[wg],[Yg]:e[bg],[Jg]:e[vg],[em]:e[Sg],[dm]:[()=>He(e[qi]),()=>e[qi].toString()],[ur]:e[or],[om]:e[jt],[hm]:e[Mg],[Va]:e[Ba],[cm]:e[Tg],[Wa]:e[La],[lr]:e[cr],[dr]:e[ar],[ht]:[()=>He(e[dt]),()=>e[dt].toString()],[im]:e[Cg],[um]:e[Ng],[rm]:e[xg],[nm]:[()=>He(e[Hi]),()=>hl(e[Hi]).toString()],[tm]:e[kg],[Xg]:e[mg],...e.Metadata!==void 0&&Object.keys(e.Metadata).reduce((a,c)=>(a[`x-amz-meta-${c.toLowerCase()}`]=e.Metadata[c],a),{})});r.bp("/{Key+}"),r.p("Bucket",()=>e.Bucket,"{Bucket}",!1),r.p("Key",()=>e.Key,"{Key+}",!0);const s=te({[fm]:[,"PutObject"]});let i,o;return e.Body!==void 0&&(o=e.Body,i=o),r.m("PUT").h(n).q(s).b(i),r.build()},jp=async(e,t)=>{if(e.statusCode!==200&&e.statusCode>=300)return Oa(e,t);const r=te({$metadata:we(e),[or]:[,e.headers[ur]],[cr]:[,e.headers[lr]],[ar]:[,e.headers[dr]],[dt]:[()=>e.headers[ht]!==void 0,()=>wo(e.headers[ht])]}),n=So(Bu(await Oo(e.body,t)),"body");return n[Xr]!=null&&(r[Xr]=og(n[Xr])),r},Vp=async(e,t)=>{if(e.statusCode!==200&&e.statusCode>=300)return Oa(e,t);const r=te({$metadata:we(e),[zt]:[,e.headers[Zg]],[yg]:[,e.headers[qg]],[Ma]:[,e.headers[Fa]],[Ua]:[,e.headers[Ha]],[$a]:[,e.headers[qa]],[_a]:[,e.headers[za]],[Da]:[,e.headers[ja]],[pg]:[,e.headers[Gg]],[or]:[,e.headers[ur]],[Og]:[,e.headers[lm]],[Ba]:[,e.headers[Va]],[La]:[,e.headers[Wa]],[cr]:[,e.headers[lr]],[ar]:[,e.headers[dr]],[dt]:[()=>e.headers[ht]!==void 0,()=>wo(e.headers[ht])],[Ig]:[()=>e.headers[zi]!==void 0,()=>Hu(e.headers[zi])],[Rg]:[,e.headers[sm]]});return await oo(e.body,t),r},Oa=async(e,t)=>{const r={...e,body:await yd(e.body,t)},n=wd(e,r.body);switch(n){case"NoSuchUpload":case"com.amazonaws.s3#NoSuchUpload":throw await tg(r);case"ObjectNotInActiveTierError":case"com.amazonaws.s3#ObjectNotInActiveTierError":throw await sg(r);case"BucketAlreadyExists":case"com.amazonaws.s3#BucketAlreadyExists":throw await Kp(r);case"BucketAlreadyOwnedByYou":case"com.amazonaws.s3#BucketAlreadyOwnedByYou":throw await Gp(r);case"NoSuchBucket":case"com.amazonaws.s3#NoSuchBucket":throw await Jp(r);case"InvalidObjectState":case"com.amazonaws.s3#InvalidObjectState":throw await Xp(r);case"NoSuchKey":case"com.amazonaws.s3#NoSuchKey":throw await eg(r);case"NotFound":case"com.amazonaws.s3#NotFound":throw await rg(r);case"EncryptionTypeMismatch":case"com.amazonaws.s3#EncryptionTypeMismatch":throw await Zp(r);case"InvalidRequest":case"com.amazonaws.s3#InvalidRequest":throw await Qp(r);case"InvalidWriteOffset":case"com.amazonaws.s3#InvalidWriteOffset":throw await Yp(r);case"TooManyParts":case"com.amazonaws.s3#TooManyParts":throw await ig(r);case"ObjectAlreadyInActiveTierError":case"com.amazonaws.s3#ObjectAlreadyInActiveTierError":throw await ng(r);default:const s=r.body;return Wp({output:e,parsedBody:s,errorCode:n})}},Wp=tl(pe),Kp=async(e,t)=>{const r=te({});e.body;const n=new Bn({$metadata:we(e),...r});return Se(n,e.body)},Gp=async(e,t)=>{const r=te({});e.body;const n=new Ln({$metadata:we(e),...r});return Se(n,e.body)},Zp=async(e,t)=>{const r=te({});e.body;const n=new jn({$metadata:we(e),...r});return Se(n,e.body)},Xp=async(e,t)=>{const r=te({}),n=e.body;n[Zr]!=null&&(r[Zr]=kt(n[Zr])),n[jt]!=null&&(r[jt]=kt(n[jt]));const s=new Hn({$metadata:we(e),...r});return Se(s,e.body)},Qp=async(e,t)=>{const r=te({});e.body;const n=new Vn({$metadata:we(e),...r});return Se(n,e.body)},Yp=async(e,t)=>{const r=te({});e.body;const n=new Wn({$metadata:we(e),...r});return Se(n,e.body)},Jp=async(e,t)=>{const r=te({});e.body;const n=new Fn({$metadata:we(e),...r});return Se(n,e.body)},eg=async(e,t)=>{const r=te({});e.body;const n=new qn({$metadata:we(e),...r});return Se(n,e.body)},tg=async(e,t)=>{const r=te({});e.body;const n=new _n({$metadata:we(e),...r});return Se(n,e.body)},rg=async(e,t)=>{const r=te({});e.body;const n=new zn({$metadata:we(e),...r});return Se(n,e.body)},ng=async(e,t)=>{const r=te({});e.body;const n=new Gn({$metadata:we(e),...r});return Se(n,e.body)},sg=async(e,t)=>{const r=te({});e.body;const n=new Dn({$metadata:we(e),...r});return Se(n,e.body)},ig=async(e,t)=>{const r=te({});e.body;const n=new Kn({$metadata:we(e),...r});return Se(n,e.body)},og=(e,t)=>{const r={};return e[Gr]!=null&&(r[Gr]=kt(e[Gr])),e[Qr]!=null&&(r[Qr]=kt(e[Qr])),e[Yr]!=null&&(r[Yr]=kt(e[Yr])),e[zt]!=null&&(r[zt]=So(Ku(e[zt]))),r},we=e=>({httpStatusCode:e.statusCode,requestId:e.headers["x-amzn-requestid"]??e.headers["x-amzn-request-id"]??e.headers["x-amz-request-id"],extendedRequestId:e.headers["x-amz-id-2"],cfId:e.headers["x-amz-cf-id"]}),ag="ACL",Gr="AccessKeyId",Zr="AccessTier",dt="BucketKeyEnabled",Xr="Credentials",cg="ChecksumAlgorithm",ug="CacheControl",Ma="ChecksumCRC32",Ua="ChecksumCRC32C",$a="ChecksumCRC64NVME",lg="ContentDisposition",dg="ContentEncoding",hg="ContentLanguage",Li="ContentLength",fg="ContentMD5",_a="ChecksumSHA1",Da="ChecksumSHA256",pg="ChecksumType",gg="ContentType",Fi="Expires",mg="ExpectedBucketOwner",yg="ETag",zt="Expiration",wg="GrantFullControl",bg="GrantRead",vg="GrantReadACP",Sg="GrantWriteACP",Eg="IfMatch",Ag="IfNoneMatch",kg="ObjectLockLegalHoldStatus",xg="ObjectLockMode",Hi="ObjectLockRetainUntilDate",Rg="RequestCharged",Cg="RequestPayer",Qr="SecretAccessKey",jt="StorageClass",Pg="SessionMode",or="ServerSideEncryption",Ba="SSECustomerAlgorithm",Tg="SSECustomerKey",La="SSECustomerKeyMD5",ar="SSEKMSEncryptionContext",cr="SSEKMSKeyId",Yr="SessionToken",Ig="Size",Ng="Tagging",Og="VersionId",qi="WriteOffsetBytes",Mg="WebsiteRedirectLocation",Ug="cache-control",$g="content-disposition",_g="content-encoding",Dg="content-language",Bg="content-length",Lg="content-md5",Fg="content-type",Hg="expires",qg="etag",zg="if-match",jg="if-none-match",Vg="session",Wg="x-amz-acl",Fa="x-amz-checksum-crc32",Ha="x-amz-checksum-crc32c",qa="x-amz-checksum-crc64nvme",za="x-amz-checksum-sha1",ja="x-amz-checksum-sha256",Kg="x-amz-create-session-mode",Gg="x-amz-checksum-type",Zg="x-amz-expiration",Xg="x-amz-expected-bucket-owner",Qg="x-amz-grant-full-control",Yg="x-amz-grant-read",Jg="x-amz-grant-read-acp",em="x-amz-grant-write-acp",tm="x-amz-object-lock-legal-hold",rm="x-amz-object-lock-mode",nm="x-amz-object-lock-retain-until-date",zi="x-amz-object-size",sm="x-amz-request-charged",im="x-amz-request-payer",om="x-amz-storage-class",am="x-amz-sdk-checksum-algorithm",ur="x-amz-server-side-encryption",lr="x-amz-server-side-encryption-aws-kms-key-id",ht="x-amz-server-side-encryption-bucket-key-enabled",dr="x-amz-server-side-encryption-context",Va="x-amz-server-side-encryption-customer-algorithm",cm="x-amz-server-side-encryption-customer-key",Wa="x-amz-server-side-encryption-customer-key-md5",um="x-amz-tagging",lm="x-amz-version-id",dm="x-amz-write-offset-bytes",hm="x-amz-website-redirect-location",fm="x-id";class pm extends bn.classBuilder().ep({...Na,DisableS3ExpressSessionAuth:{type:"staticContextParams",value:!0},Bucket:{type:"contextParams",name:"Bucket"}}).m(function(t,r,n,s){return[to(n,this.serialize,this.deserialize),ia(n,t.getEndpointParameterInstructions()),Vo(n)]}).s("AmazonS3","CreateSession",{}).n("S3Client","CreateSessionCommand").f(Lp,Bp).ser(qp).de(jp).build(){}const gm="3.758.0",mm={version:gm};function ji(e){return typeof e=="string"?e.length===0:e.byteLength===0}var Ka={name:"SHA-1"},Vi={name:"HMAC",hash:Ka},ym=new Uint8Array([218,57,163,238,94,107,75,13,50,85,191,239,149,96,24,144,175,216,7,9]);const wm={};function qe(){return typeof window<"u"?window:typeof self<"u"?self:wm}var bm=function(){function e(t){this.toHash=new Uint8Array(0),t!==void 0&&(this.key=new Promise(function(r,n){qe().crypto.subtle.importKey("raw",Wi(t),Vi,!1,["sign"]).then(r,n)}),this.key.catch(function(){}))}return e.prototype.update=function(t){if(!ji(t)){var r=Wi(t),n=new Uint8Array(this.toHash.byteLength+r.byteLength);n.set(this.toHash,0),n.set(r,this.toHash.byteLength),this.toHash=n}},e.prototype.digest=function(){var t=this;return this.key?this.key.then(function(r){return qe().crypto.subtle.sign(Vi,r,t.toHash).then(function(n){return new Uint8Array(n)})}):ji(this.toHash)?Promise.resolve(ym):Promise.resolve().then(function(){return qe().crypto.subtle.digest(Ka,t.toHash)}).then(function(r){return Promise.resolve(new Uint8Array(r))})},e.prototype.reset=function(){this.toHash=new Uint8Array(0)},e}();function Wi(e){return typeof e=="string"?$o(e):ArrayBuffer.isView(e)?new Uint8Array(e.buffer,e.byteOffset,e.byteLength/Uint8Array.BYTES_PER_ELEMENT):new Uint8Array(e)}var vm=["decrypt","digest","encrypt","exportKey","generateKey","importKey","sign","verify"];function Ga(e){if(Sm(e)&&typeof e.crypto.subtle=="object"){var t=e.crypto.subtle;return Em(t)}return!1}function Sm(e){if(typeof e=="object"&&typeof e.crypto=="object"){var t=e.crypto.getRandomValues;return typeof t=="function"}return!1}function Em(e){return e&&vm.every(function(t){return typeof e[t]=="function"})}var Am=function(){function e(t){if(Ga(qe()))this.hash=new bm(t);else throw new Error("SHA1 not supported")}return e.prototype.update=function(t,r){this.hash.update(je(t))},e.prototype.digest=function(){return this.hash.digest()},e.prototype.reset=function(){this.hash.reset()},e}(),Za={name:"SHA-256"},Ki={name:"HMAC",hash:Za},km=new Uint8Array([227,176,196,66,152,252,28,20,154,251,244,200,153,111,185,36,39,174,65,228,100,155,147,76,164,149,153,27,120,82,184,85]),xm=function(){function e(t){this.toHash=new Uint8Array(0),this.secret=t,this.reset()}return e.prototype.update=function(t){if(!Tt(t)){var r=je(t),n=new Uint8Array(this.toHash.byteLength+r.byteLength);n.set(this.toHash,0),n.set(r,this.toHash.byteLength),this.toHash=n}},e.prototype.digest=function(){var t=this;return this.key?this.key.then(function(r){return qe().crypto.subtle.sign(Ki,r,t.toHash).then(function(n){return new Uint8Array(n)})}):Tt(this.toHash)?Promise.resolve(km):Promise.resolve().then(function(){return qe().crypto.subtle.digest(Za,t.toHash)}).then(function(r){return Promise.resolve(new Uint8Array(r))})},e.prototype.reset=function(){var t=this;this.toHash=new Uint8Array(0),this.secret&&this.secret!==void 0&&(this.key=new Promise(function(r,n){qe().crypto.subtle.importKey("raw",je(t.secret),Ki,!1,["sign"]).then(r,n)}),this.key.catch(function(){}))},e}(),xe=64,Rm=32,Cm=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),Pm=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225],Tm=Math.pow(2,53)-1,Vt=function(){function e(){this.state=Int32Array.from(Pm),this.temp=new Int32Array(64),this.buffer=new Uint8Array(64),this.bufferLength=0,this.bytesHashed=0,this.finished=!1}return e.prototype.update=function(t){if(this.finished)throw new Error("Attempted to update an already finished hash.");var r=0,n=t.byteLength;if(this.bytesHashed+=n,this.bytesHashed*8>Tm)throw new Error("Cannot hash more than 2^53 - 1 bits");for(;n>0;)this.buffer[this.bufferLength++]=t[r++],n--,this.bufferLength===xe&&(this.hashBuffer(),this.bufferLength=0)},e.prototype.digest=function(){if(!this.finished){var t=this.bytesHashed*8,r=new DataView(this.buffer.buffer,this.buffer.byteOffset,this.buffer.byteLength),n=this.bufferLength;if(r.setUint8(this.bufferLength++,128),n%xe>=xe-8){for(var s=this.bufferLength;s<xe;s++)r.setUint8(s,0);this.hashBuffer(),this.bufferLength=0}for(var s=this.bufferLength;s<xe-8;s++)r.setUint8(s,0);r.setUint32(xe-8,Math.floor(t/4294967296),!0),r.setUint32(xe-4,t),this.hashBuffer(),this.finished=!0}for(var i=new Uint8Array(Rm),s=0;s<8;s++)i[s*4]=this.state[s]>>>24&255,i[s*4+1]=this.state[s]>>>16&255,i[s*4+2]=this.state[s]>>>8&255,i[s*4+3]=this.state[s]>>>0&255;return i},e.prototype.hashBuffer=function(){for(var t=this,r=t.buffer,n=t.state,s=n[0],i=n[1],o=n[2],a=n[3],c=n[4],u=n[5],h=n[6],d=n[7],p=0;p<xe;p++){if(p<16)this.temp[p]=(r[p*4]&255)<<24|(r[p*4+1]&255)<<16|(r[p*4+2]&255)<<8|r[p*4+3]&255;else{var g=this.temp[p-2],S=(g>>>17|g<<15)^(g>>>19|g<<13)^g>>>10;g=this.temp[p-15];var R=(g>>>7|g<<25)^(g>>>18|g<<14)^g>>>3;this.temp[p]=(S+this.temp[p-7]|0)+(R+this.temp[p-16]|0)}var x=(((c>>>6|c<<26)^(c>>>11|c<<21)^(c>>>25|c<<7))+(c&u^~c&h)|0)+(d+(Cm[p]+this.temp[p]|0)|0)|0,_=((s>>>2|s<<30)^(s>>>13|s<<19)^(s>>>22|s<<10))+(s&i^s&o^i&o)|0;d=h,h=u,u=c,c=a+x|0,a=o,o=i,i=s,s=x+_|0}n[0]+=s,n[1]+=i,n[2]+=o,n[3]+=a,n[4]+=c,n[5]+=u,n[6]+=h,n[7]+=d},e}(),Im=function(){function e(t){this.secret=t,this.hash=new Vt,this.reset()}return e.prototype.update=function(t){if(!(Tt(t)||this.error))try{this.hash.update(je(t))}catch(r){this.error=r}},e.prototype.digestSync=function(){if(this.error)throw this.error;return this.outer?(this.outer.finished||this.outer.update(this.hash.digest()),this.outer.digest()):this.hash.digest()},e.prototype.digest=function(){return kn(this,void 0,void 0,function(){return xn(this,function(t){return[2,this.digestSync()]})})},e.prototype.reset=function(){if(this.hash=new Vt,this.secret){this.outer=new Vt;var t=Nm(this.secret),r=new Uint8Array(xe);r.set(t);for(var n=0;n<xe;n++)t[n]^=54,r[n]^=92;this.hash.update(t),this.outer.update(r);for(var n=0;n<t.byteLength;n++)t[n]=0}},e}();function Nm(e){var t=je(e);if(t.byteLength>xe){var r=new Vt;r.update(t),t=r.digest()}var n=new Uint8Array(xe);return n.set(t),n}var Om=function(){function e(t){Ga(qe())?this.hash=new xm(t):this.hash=new Im(t)}return e.prototype.update=function(t,r){this.hash.update(je(t))},e.prototype.digest=function(){return this.hash.digest()},e.prototype.reset=function(){this.hash.reset()},e}();const Mm={"Amazon Silk":"amazon_silk","Android Browser":"android",Bada:"bada",BlackBerry:"blackberry",Chrome:"chrome",Chromium:"chromium",Electron:"electron",Epiphany:"epiphany",Firefox:"firefox",Focus:"focus",Generic:"generic","Google Search":"google_search",Googlebot:"googlebot","Internet Explorer":"ie","K-Meleon":"k_meleon",Maxthon:"maxthon","Microsoft Edge":"edge","MZ Browser":"mz","NAVER Whale Browser":"naver",Opera:"opera","Opera Coast":"opera_coast",PhantomJS:"phantomjs",Puffin:"puffin",QupZilla:"qupzilla",QQ:"qq",QQLite:"qqlite",Safari:"safari",Sailfish:"sailfish","Samsung Internet for Android":"samsung_internet",SeaMonkey:"seamonkey",Sleipnir:"sleipnir",Swing:"swing",Tizen:"tizen","UC Browser":"uc",Vivaldi:"vivaldi","WebOS Browser":"webos",WeChat:"wechat","Yandex Browser":"yandex",Roku:"roku"},Xa={amazon_silk:"Amazon Silk",android:"Android Browser",bada:"Bada",blackberry:"BlackBerry",chrome:"Chrome",chromium:"Chromium",electron:"Electron",epiphany:"Epiphany",firefox:"Firefox",focus:"Focus",generic:"Generic",googlebot:"Googlebot",google_search:"Google Search",ie:"Internet Explorer",k_meleon:"K-Meleon",maxthon:"Maxthon",edge:"Microsoft Edge",mz:"MZ Browser",naver:"NAVER Whale Browser",opera:"Opera",opera_coast:"Opera Coast",phantomjs:"PhantomJS",puffin:"Puffin",qupzilla:"QupZilla",qq:"QQ Browser",qqlite:"QQ Browser Lite",safari:"Safari",sailfish:"Sailfish",samsung_internet:"Samsung Internet for Android",seamonkey:"SeaMonkey",sleipnir:"Sleipnir",swing:"Swing",tizen:"Tizen",uc:"UC Browser",vivaldi:"Vivaldi",webos:"WebOS Browser",wechat:"WeChat",yandex:"Yandex Browser"},V={tablet:"tablet",mobile:"mobile",desktop:"desktop",tv:"tv"},ye={WindowsPhone:"Windows Phone",Windows:"Windows",MacOS:"macOS",iOS:"iOS",Android:"Android",WebOS:"WebOS",BlackBerry:"BlackBerry",Bada:"Bada",Tizen:"Tizen",Linux:"Linux",ChromeOS:"Chrome OS",PlayStation4:"PlayStation 4",Roku:"Roku"},Le={EdgeHTML:"EdgeHTML",Blink:"Blink",Trident:"Trident",Presto:"Presto",Gecko:"Gecko",WebKit:"WebKit"};class w{static getFirstMatch(t,r){const n=r.match(t);return n&&n.length>0&&n[1]||""}static getSecondMatch(t,r){const n=r.match(t);return n&&n.length>1&&n[2]||""}static matchAndReturnConst(t,r,n){if(t.test(r))return n}static getWindowsVersionName(t){switch(t){case"NT":return"NT";case"XP":return"XP";case"NT 5.0":return"2000";case"NT 5.1":return"XP";case"NT 5.2":return"2003";case"NT 6.0":return"Vista";case"NT 6.1":return"7";case"NT 6.2":return"8";case"NT 6.3":return"8.1";case"NT 10.0":return"10";default:return}}static getMacOSVersionName(t){const r=t.split(".").splice(0,2).map(n=>parseInt(n,10)||0);if(r.push(0),r[0]===10)switch(r[1]){case 5:return"Leopard";case 6:return"Snow Leopard";case 7:return"Lion";case 8:return"Mountain Lion";case 9:return"Mavericks";case 10:return"Yosemite";case 11:return"El Capitan";case 12:return"Sierra";case 13:return"High Sierra";case 14:return"Mojave";case 15:return"Catalina";default:return}}static getAndroidVersionName(t){const r=t.split(".").splice(0,2).map(n=>parseInt(n,10)||0);if(r.push(0),!(r[0]===1&&r[1]<5)){if(r[0]===1&&r[1]<6)return"Cupcake";if(r[0]===1&&r[1]>=6)return"Donut";if(r[0]===2&&r[1]<2)return"Eclair";if(r[0]===2&&r[1]===2)return"Froyo";if(r[0]===2&&r[1]>2)return"Gingerbread";if(r[0]===3)return"Honeycomb";if(r[0]===4&&r[1]<1)return"Ice Cream Sandwich";if(r[0]===4&&r[1]<4)return"Jelly Bean";if(r[0]===4&&r[1]>=4)return"KitKat";if(r[0]===5)return"Lollipop";if(r[0]===6)return"Marshmallow";if(r[0]===7)return"Nougat";if(r[0]===8)return"Oreo";if(r[0]===9)return"Pie"}}static getVersionPrecision(t){return t.split(".").length}static compareVersions(t,r,n=!1){const s=w.getVersionPrecision(t),i=w.getVersionPrecision(r);let o=Math.max(s,i),a=0;const c=w.map([t,r],u=>{const h=o-w.getVersionPrecision(u),d=u+new Array(h+1).join(".0");return w.map(d.split("."),p=>new Array(20-p.length).join("0")+p).reverse()});for(n&&(a=o-Math.min(s,i)),o-=1;o>=a;){if(c[0][o]>c[1][o])return 1;if(c[0][o]===c[1][o]){if(o===a)return 0;o-=1}else if(c[0][o]<c[1][o])return-1}}static map(t,r){const n=[];let s;if(Array.prototype.map)return Array.prototype.map.call(t,r);for(s=0;s<t.length;s+=1)n.push(r(t[s]));return n}static find(t,r){let n,s;if(Array.prototype.find)return Array.prototype.find.call(t,r);for(n=0,s=t.length;n<s;n+=1){const i=t[n];if(r(i,n))return i}}static assign(t,...r){const n=t;let s,i;if(Object.assign)return Object.assign(t,...r);for(s=0,i=r.length;s<i;s+=1){const o=r[s];typeof o=="object"&&o!==null&&Object.keys(o).forEach(a=>{n[a]=o[a]})}return t}static getBrowserAlias(t){return Mm[t]}static getBrowserTypeByAlias(t){return Xa[t]||""}}const L=/version\/(\d+(\.?_?\d+)+)/i,Um=[{test:[/googlebot/i],describe(e){const t={name:"Googlebot"},r=w.getFirstMatch(/googlebot\/(\d+(\.\d+))/i,e)||w.getFirstMatch(L,e);return r&&(t.version=r),t}},{test:[/opera/i],describe(e){const t={name:"Opera"},r=w.getFirstMatch(L,e)||w.getFirstMatch(/(?:opera)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/opr\/|opios/i],describe(e){const t={name:"Opera"},r=w.getFirstMatch(/(?:opr|opios)[\s/](\S+)/i,e)||w.getFirstMatch(L,e);return r&&(t.version=r),t}},{test:[/SamsungBrowser/i],describe(e){const t={name:"Samsung Internet for Android"},r=w.getFirstMatch(L,e)||w.getFirstMatch(/(?:SamsungBrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/Whale/i],describe(e){const t={name:"NAVER Whale Browser"},r=w.getFirstMatch(L,e)||w.getFirstMatch(/(?:whale)[\s/](\d+(?:\.\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/MZBrowser/i],describe(e){const t={name:"MZ Browser"},r=w.getFirstMatch(/(?:MZBrowser)[\s/](\d+(?:\.\d+)+)/i,e)||w.getFirstMatch(L,e);return r&&(t.version=r),t}},{test:[/focus/i],describe(e){const t={name:"Focus"},r=w.getFirstMatch(/(?:focus)[\s/](\d+(?:\.\d+)+)/i,e)||w.getFirstMatch(L,e);return r&&(t.version=r),t}},{test:[/swing/i],describe(e){const t={name:"Swing"},r=w.getFirstMatch(/(?:swing)[\s/](\d+(?:\.\d+)+)/i,e)||w.getFirstMatch(L,e);return r&&(t.version=r),t}},{test:[/coast/i],describe(e){const t={name:"Opera Coast"},r=w.getFirstMatch(L,e)||w.getFirstMatch(/(?:coast)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/opt\/\d+(?:.?_?\d+)+/i],describe(e){const t={name:"Opera Touch"},r=w.getFirstMatch(/(?:opt)[\s/](\d+(\.?_?\d+)+)/i,e)||w.getFirstMatch(L,e);return r&&(t.version=r),t}},{test:[/yabrowser/i],describe(e){const t={name:"Yandex Browser"},r=w.getFirstMatch(/(?:yabrowser)[\s/](\d+(\.?_?\d+)+)/i,e)||w.getFirstMatch(L,e);return r&&(t.version=r),t}},{test:[/ucbrowser/i],describe(e){const t={name:"UC Browser"},r=w.getFirstMatch(L,e)||w.getFirstMatch(/(?:ucbrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/Maxthon|mxios/i],describe(e){const t={name:"Maxthon"},r=w.getFirstMatch(L,e)||w.getFirstMatch(/(?:Maxthon|mxios)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/epiphany/i],describe(e){const t={name:"Epiphany"},r=w.getFirstMatch(L,e)||w.getFirstMatch(/(?:epiphany)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/puffin/i],describe(e){const t={name:"Puffin"},r=w.getFirstMatch(L,e)||w.getFirstMatch(/(?:puffin)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/sleipnir/i],describe(e){const t={name:"Sleipnir"},r=w.getFirstMatch(L,e)||w.getFirstMatch(/(?:sleipnir)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/k-meleon/i],describe(e){const t={name:"K-Meleon"},r=w.getFirstMatch(L,e)||w.getFirstMatch(/(?:k-meleon)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/micromessenger/i],describe(e){const t={name:"WeChat"},r=w.getFirstMatch(/(?:micromessenger)[\s/](\d+(\.?_?\d+)+)/i,e)||w.getFirstMatch(L,e);return r&&(t.version=r),t}},{test:[/qqbrowser/i],describe(e){const t={name:/qqbrowserlite/i.test(e)?"QQ Browser Lite":"QQ Browser"},r=w.getFirstMatch(/(?:qqbrowserlite|qqbrowser)[/](\d+(\.?_?\d+)+)/i,e)||w.getFirstMatch(L,e);return r&&(t.version=r),t}},{test:[/msie|trident/i],describe(e){const t={name:"Internet Explorer"},r=w.getFirstMatch(/(?:msie |rv:)(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/\sedg\//i],describe(e){const t={name:"Microsoft Edge"},r=w.getFirstMatch(/\sedg\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/edg([ea]|ios)/i],describe(e){const t={name:"Microsoft Edge"},r=w.getSecondMatch(/edg([ea]|ios)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/vivaldi/i],describe(e){const t={name:"Vivaldi"},r=w.getFirstMatch(/vivaldi\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/seamonkey/i],describe(e){const t={name:"SeaMonkey"},r=w.getFirstMatch(/seamonkey\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/sailfish/i],describe(e){const t={name:"Sailfish"},r=w.getFirstMatch(/sailfish\s?browser\/(\d+(\.\d+)?)/i,e);return r&&(t.version=r),t}},{test:[/silk/i],describe(e){const t={name:"Amazon Silk"},r=w.getFirstMatch(/silk\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/phantom/i],describe(e){const t={name:"PhantomJS"},r=w.getFirstMatch(/phantomjs\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/slimerjs/i],describe(e){const t={name:"SlimerJS"},r=w.getFirstMatch(/slimerjs\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe(e){const t={name:"BlackBerry"},r=w.getFirstMatch(L,e)||w.getFirstMatch(/blackberry[\d]+\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/(web|hpw)[o0]s/i],describe(e){const t={name:"WebOS Browser"},r=w.getFirstMatch(L,e)||w.getFirstMatch(/w(?:eb)?[o0]sbrowser\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/bada/i],describe(e){const t={name:"Bada"},r=w.getFirstMatch(/dolfin\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/tizen/i],describe(e){const t={name:"Tizen"},r=w.getFirstMatch(/(?:tizen\s?)?browser\/(\d+(\.?_?\d+)+)/i,e)||w.getFirstMatch(L,e);return r&&(t.version=r),t}},{test:[/qupzilla/i],describe(e){const t={name:"QupZilla"},r=w.getFirstMatch(/(?:qupzilla)[\s/](\d+(\.?_?\d+)+)/i,e)||w.getFirstMatch(L,e);return r&&(t.version=r),t}},{test:[/firefox|iceweasel|fxios/i],describe(e){const t={name:"Firefox"},r=w.getFirstMatch(/(?:firefox|iceweasel|fxios)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/electron/i],describe(e){const t={name:"Electron"},r=w.getFirstMatch(/(?:electron)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/MiuiBrowser/i],describe(e){const t={name:"Miui"},r=w.getFirstMatch(/(?:MiuiBrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/chromium/i],describe(e){const t={name:"Chromium"},r=w.getFirstMatch(/(?:chromium)[\s/](\d+(\.?_?\d+)+)/i,e)||w.getFirstMatch(L,e);return r&&(t.version=r),t}},{test:[/chrome|crios|crmo/i],describe(e){const t={name:"Chrome"},r=w.getFirstMatch(/(?:chrome|crios|crmo)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/GSA/i],describe(e){const t={name:"Google Search"},r=w.getFirstMatch(/(?:GSA)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test(e){const t=!e.test(/like android/i),r=e.test(/android/i);return t&&r},describe(e){const t={name:"Android Browser"},r=w.getFirstMatch(L,e);return r&&(t.version=r),t}},{test:[/playstation 4/i],describe(e){const t={name:"PlayStation 4"},r=w.getFirstMatch(L,e);return r&&(t.version=r),t}},{test:[/safari|applewebkit/i],describe(e){const t={name:"Safari"},r=w.getFirstMatch(L,e);return r&&(t.version=r),t}},{test:[/.*/i],describe(e){const t=/^(.*)\/(.*) /,r=/^(.*)\/(.*)[ \t]\((.*)/,n=e.search("\\(")!==-1?r:t;return{name:w.getFirstMatch(n,e),version:w.getSecondMatch(n,e)}}}],$m=[{test:[/Roku\/DVP/],describe(e){const t=w.getFirstMatch(/Roku\/DVP-(\d+\.\d+)/i,e);return{name:ye.Roku,version:t}}},{test:[/windows phone/i],describe(e){const t=w.getFirstMatch(/windows phone (?:os)?\s?(\d+(\.\d+)*)/i,e);return{name:ye.WindowsPhone,version:t}}},{test:[/windows /i],describe(e){const t=w.getFirstMatch(/Windows ((NT|XP)( \d\d?.\d)?)/i,e),r=w.getWindowsVersionName(t);return{name:ye.Windows,version:t,versionName:r}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe(e){const t={name:ye.iOS},r=w.getSecondMatch(/(Version\/)(\d[\d.]+)/,e);return r&&(t.version=r),t}},{test:[/macintosh/i],describe(e){const t=w.getFirstMatch(/mac os x (\d+(\.?_?\d+)+)/i,e).replace(/[_\s]/g,"."),r=w.getMacOSVersionName(t),n={name:ye.MacOS,version:t};return r&&(n.versionName=r),n}},{test:[/(ipod|iphone|ipad)/i],describe(e){const t=w.getFirstMatch(/os (\d+([_\s]\d+)*) like mac os x/i,e).replace(/[_\s]/g,".");return{name:ye.iOS,version:t}}},{test(e){const t=!e.test(/like android/i),r=e.test(/android/i);return t&&r},describe(e){const t=w.getFirstMatch(/android[\s/-](\d+(\.\d+)*)/i,e),r=w.getAndroidVersionName(t),n={name:ye.Android,version:t};return r&&(n.versionName=r),n}},{test:[/(web|hpw)[o0]s/i],describe(e){const t=w.getFirstMatch(/(?:web|hpw)[o0]s\/(\d+(\.\d+)*)/i,e),r={name:ye.WebOS};return t&&t.length&&(r.version=t),r}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe(e){const t=w.getFirstMatch(/rim\stablet\sos\s(\d+(\.\d+)*)/i,e)||w.getFirstMatch(/blackberry\d+\/(\d+([_\s]\d+)*)/i,e)||w.getFirstMatch(/\bbb(\d+)/i,e);return{name:ye.BlackBerry,version:t}}},{test:[/bada/i],describe(e){const t=w.getFirstMatch(/bada\/(\d+(\.\d+)*)/i,e);return{name:ye.Bada,version:t}}},{test:[/tizen/i],describe(e){const t=w.getFirstMatch(/tizen[/\s](\d+(\.\d+)*)/i,e);return{name:ye.Tizen,version:t}}},{test:[/linux/i],describe(){return{name:ye.Linux}}},{test:[/CrOS/],describe(){return{name:ye.ChromeOS}}},{test:[/PlayStation 4/],describe(e){const t=w.getFirstMatch(/PlayStation 4[/\s](\d+(\.\d+)*)/i,e);return{name:ye.PlayStation4,version:t}}}],_m=[{test:[/googlebot/i],describe(){return{type:"bot",vendor:"Google"}}},{test:[/huawei/i],describe(e){const t=w.getFirstMatch(/(can-l01)/i,e)&&"Nova",r={type:V.mobile,vendor:"Huawei"};return t&&(r.model=t),r}},{test:[/nexus\s*(?:7|8|9|10).*/i],describe(){return{type:V.tablet,vendor:"Nexus"}}},{test:[/ipad/i],describe(){return{type:V.tablet,vendor:"Apple",model:"iPad"}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe(){return{type:V.tablet,vendor:"Apple",model:"iPad"}}},{test:[/kftt build/i],describe(){return{type:V.tablet,vendor:"Amazon",model:"Kindle Fire HD 7"}}},{test:[/silk/i],describe(){return{type:V.tablet,vendor:"Amazon"}}},{test:[/tablet(?! pc)/i],describe(){return{type:V.tablet}}},{test(e){const t=e.test(/ipod|iphone/i),r=e.test(/like (ipod|iphone)/i);return t&&!r},describe(e){const t=w.getFirstMatch(/(ipod|iphone)/i,e);return{type:V.mobile,vendor:"Apple",model:t}}},{test:[/nexus\s*[0-6].*/i,/galaxy nexus/i],describe(){return{type:V.mobile,vendor:"Nexus"}}},{test:[/[^-]mobi/i],describe(){return{type:V.mobile}}},{test(e){return e.getBrowserName(!0)==="blackberry"},describe(){return{type:V.mobile,vendor:"BlackBerry"}}},{test(e){return e.getBrowserName(!0)==="bada"},describe(){return{type:V.mobile}}},{test(e){return e.getBrowserName()==="windows phone"},describe(){return{type:V.mobile,vendor:"Microsoft"}}},{test(e){const t=Number(String(e.getOSVersion()).split(".")[0]);return e.getOSName(!0)==="android"&&t>=3},describe(){return{type:V.tablet}}},{test(e){return e.getOSName(!0)==="android"},describe(){return{type:V.mobile}}},{test(e){return e.getOSName(!0)==="macos"},describe(){return{type:V.desktop,vendor:"Apple"}}},{test(e){return e.getOSName(!0)==="windows"},describe(){return{type:V.desktop}}},{test(e){return e.getOSName(!0)==="linux"},describe(){return{type:V.desktop}}},{test(e){return e.getOSName(!0)==="playstation 4"},describe(){return{type:V.tv}}},{test(e){return e.getOSName(!0)==="roku"},describe(){return{type:V.tv}}}],Dm=[{test(e){return e.getBrowserName(!0)==="microsoft edge"},describe(e){if(/\sedg\//i.test(e))return{name:Le.Blink};const t=w.getFirstMatch(/edge\/(\d+(\.?_?\d+)+)/i,e);return{name:Le.EdgeHTML,version:t}}},{test:[/trident/i],describe(e){const t={name:Le.Trident},r=w.getFirstMatch(/trident\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test(e){return e.test(/presto/i)},describe(e){const t={name:Le.Presto},r=w.getFirstMatch(/presto\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test(e){const t=e.test(/gecko/i),r=e.test(/like gecko/i);return t&&!r},describe(e){const t={name:Le.Gecko},r=w.getFirstMatch(/gecko\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/(apple)?webkit\/537\.36/i],describe(){return{name:Le.Blink}}},{test:[/(apple)?webkit/i],describe(e){const t={name:Le.WebKit},r=w.getFirstMatch(/webkit\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}}];class Gi{constructor(t,r=!1){if(t==null||t==="")throw new Error("UserAgent parameter can't be empty");this._ua=t,this.parsedResult={},r!==!0&&this.parse()}getUA(){return this._ua}test(t){return t.test(this._ua)}parseBrowser(){this.parsedResult.browser={};const t=w.find(Um,r=>{if(typeof r.test=="function")return r.test(this);if(r.test instanceof Array)return r.test.some(n=>this.test(n));throw new Error("Browser's test function is not valid")});return t&&(this.parsedResult.browser=t.describe(this.getUA())),this.parsedResult.browser}getBrowser(){return this.parsedResult.browser?this.parsedResult.browser:this.parseBrowser()}getBrowserName(t){return t?String(this.getBrowser().name).toLowerCase()||"":this.getBrowser().name||""}getBrowserVersion(){return this.getBrowser().version}getOS(){return this.parsedResult.os?this.parsedResult.os:this.parseOS()}parseOS(){this.parsedResult.os={};const t=w.find($m,r=>{if(typeof r.test=="function")return r.test(this);if(r.test instanceof Array)return r.test.some(n=>this.test(n));throw new Error("Browser's test function is not valid")});return t&&(this.parsedResult.os=t.describe(this.getUA())),this.parsedResult.os}getOSName(t){const{name:r}=this.getOS();return t?String(r).toLowerCase()||"":r||""}getOSVersion(){return this.getOS().version}getPlatform(){return this.parsedResult.platform?this.parsedResult.platform:this.parsePlatform()}getPlatformType(t=!1){const{type:r}=this.getPlatform();return t?String(r).toLowerCase()||"":r||""}parsePlatform(){this.parsedResult.platform={};const t=w.find(_m,r=>{if(typeof r.test=="function")return r.test(this);if(r.test instanceof Array)return r.test.some(n=>this.test(n));throw new Error("Browser's test function is not valid")});return t&&(this.parsedResult.platform=t.describe(this.getUA())),this.parsedResult.platform}getEngine(){return this.parsedResult.engine?this.parsedResult.engine:this.parseEngine()}getEngineName(t){return t?String(this.getEngine().name).toLowerCase()||"":this.getEngine().name||""}parseEngine(){this.parsedResult.engine={};const t=w.find(Dm,r=>{if(typeof r.test=="function")return r.test(this);if(r.test instanceof Array)return r.test.some(n=>this.test(n));throw new Error("Browser's test function is not valid")});return t&&(this.parsedResult.engine=t.describe(this.getUA())),this.parsedResult.engine}parse(){return this.parseBrowser(),this.parseOS(),this.parsePlatform(),this.parseEngine(),this}getResult(){return w.assign({},this.parsedResult)}satisfies(t){const r={};let n=0;const s={};let i=0;if(Object.keys(t).forEach(o=>{const a=t[o];typeof a=="string"?(s[o]=a,i+=1):typeof a=="object"&&(r[o]=a,n+=1)}),n>0){const o=Object.keys(r),a=w.find(o,u=>this.isOS(u));if(a){const u=this.satisfies(r[a]);if(u!==void 0)return u}const c=w.find(o,u=>this.isPlatform(u));if(c){const u=this.satisfies(r[c]);if(u!==void 0)return u}}if(i>0){const o=Object.keys(s),a=w.find(o,c=>this.isBrowser(c,!0));if(a!==void 0)return this.compareVersion(s[a])}}isBrowser(t,r=!1){const n=this.getBrowserName().toLowerCase();let s=t.toLowerCase();const i=w.getBrowserTypeByAlias(s);return r&&i&&(s=i.toLowerCase()),s===n}compareVersion(t){let r=[0],n=t,s=!1;const i=this.getBrowserVersion();if(typeof i=="string")return t[0]===">"||t[0]==="<"?(n=t.substr(1),t[1]==="="?(s=!0,n=t.substr(2)):r=[],t[0]===">"?r.push(1):r.push(-1)):t[0]==="="?n=t.substr(1):t[0]==="~"&&(s=!0,n=t.substr(1)),r.indexOf(w.compareVersions(i,n,s))>-1}isOS(t){return this.getOSName(!0)===String(t).toLowerCase()}isPlatform(t){return this.getPlatformType(!0)===String(t).toLowerCase()}isEngine(t){return this.getEngineName(!0)===String(t).toLowerCase()}is(t,r=!1){return this.isBrowser(t,r)||this.isOS(t)||this.isPlatform(t)}some(t=[]){return t.some(r=>this.is(r))}}/*!
 * Bowser - a browser detector
 * https://github.com/lancedikson/bowser
 * MIT License | (c) Dustin Diaz 2012-2015
 * MIT License | (c) Denis Demchenko 2015-2019
 */class Qa{static getParser(t,r=!1){if(typeof t!="string")throw new Error("UserAgent should be a string");return new Gi(t,r)}static parse(t){return new Gi(t).getResult()}static get BROWSER_MAP(){return Xa}static get ENGINE_MAP(){return Le}static get OS_MAP(){return ye}static get PLATFORMS_MAP(){return V}}const Bm=({serviceId:e,clientVersion:t})=>async r=>{var n,s,i,o,a,c;const u=typeof window<"u"&&(n=window==null?void 0:window.navigator)!=null&&n.userAgent?Qa.parse(window.navigator.userAgent):void 0,h=[["aws-sdk-js",t],["ua","2.1"],[`os/${((s=u==null?void 0:u.os)==null?void 0:s.name)||"other"}`,(i=u==null?void 0:u.os)==null?void 0:i.version],["lang/js"],["md/browser",`${((o=u==null?void 0:u.browser)==null?void 0:o.name)??"unknown"}_${((a=u==null?void 0:u.browser)==null?void 0:a.version)??"unknown"}`]];e&&h.push([`api/${e}`,t]);const d=await((c=r==null?void 0:r.userAgentAppId)==null?void 0:c.call(r));return d&&h.push([`app/${d}`]),h};class Rt{constructor(t){if(this.bytes=t,t.byteLength!==8)throw new Error("Int64 buffers must be exactly 8 bytes")}static fromNumber(t){if(t>9223372036854776e3||t<-9223372036854776e3)throw new Error(`${t} is too large (or, if negative, too small) to represent as an Int64`);const r=new Uint8Array(8);for(let n=7,s=Math.abs(Math.round(t));n>-1&&s>0;n--,s/=256)r[n]=s;return t<0&&Zi(r),new Rt(r)}valueOf(){const t=this.bytes.slice(0),r=t[0]&128;return r&&Zi(t),parseInt(be(t),16)*(r?-1:1)}toString(){return String(this.valueOf())}}function Zi(e){for(let t=0;t<8;t++)e[t]^=255;for(let t=7;t>-1&&(e[t]++,e[t]===0);t--);}class Lm{constructor(t,r){this.toUtf8=t,this.fromUtf8=r}format(t){const r=[];for(const i of Object.keys(t)){const o=this.fromUtf8(i);r.push(Uint8Array.from([o.byteLength]),o,this.formatHeaderValue(t[i]))}const n=new Uint8Array(r.reduce((i,o)=>i+o.byteLength,0));let s=0;for(const i of r)n.set(i,s),s+=i.byteLength;return n}formatHeaderValue(t){switch(t.type){case"boolean":return Uint8Array.from([t.value?0:1]);case"byte":return Uint8Array.from([2,t.value]);case"short":const r=new DataView(new ArrayBuffer(3));return r.setUint8(0,3),r.setInt16(1,t.value,!1),new Uint8Array(r.buffer);case"integer":const n=new DataView(new ArrayBuffer(5));return n.setUint8(0,4),n.setInt32(1,t.value,!1),new Uint8Array(n.buffer);case"long":const s=new Uint8Array(9);return s[0]=5,s.set(t.value.bytes,1),s;case"binary":const i=new DataView(new ArrayBuffer(3+t.value.byteLength));i.setUint8(0,6),i.setUint16(1,t.value.byteLength,!1);const o=new Uint8Array(i.buffer);return o.set(t.value,3),o;case"string":const a=this.fromUtf8(t.value),c=new DataView(new ArrayBuffer(3+a.byteLength));c.setUint8(0,7),c.setUint16(1,a.byteLength,!1);const u=new Uint8Array(c.buffer);return u.set(a,3),u;case"timestamp":const h=new Uint8Array(9);return h[0]=8,h.set(Rt.fromNumber(t.value.valueOf()).bytes,1),h;case"uuid":if(!Gm.test(t.value))throw new Error(`Invalid UUID received: ${t.value}`);const d=new Uint8Array(17);return d[0]=9,d.set(io(t.value.replace(/\-/g,"")),1),d}}parse(t){const r={};let n=0;for(;n<t.byteLength;){const s=t.getUint8(n++),i=this.toUtf8(new Uint8Array(t.buffer,t.byteOffset+n,s));switch(n+=s,t.getUint8(n++)){case 0:r[i]={type:Qi,value:!0};break;case 1:r[i]={type:Qi,value:!1};break;case 2:r[i]={type:Fm,value:t.getInt8(n++)};break;case 3:r[i]={type:Hm,value:t.getInt16(n,!1)},n+=2;break;case 4:r[i]={type:qm,value:t.getInt32(n,!1)},n+=4;break;case 5:r[i]={type:zm,value:new Rt(new Uint8Array(t.buffer,t.byteOffset+n,8))},n+=8;break;case 6:const o=t.getUint16(n,!1);n+=2,r[i]={type:jm,value:new Uint8Array(t.buffer,t.byteOffset+n,o)},n+=o;break;case 7:const a=t.getUint16(n,!1);n+=2,r[i]={type:Vm,value:this.toUtf8(new Uint8Array(t.buffer,t.byteOffset+n,a))},n+=a;break;case 8:r[i]={type:Wm,value:new Date(new Rt(new Uint8Array(t.buffer,t.byteOffset+n,8)).valueOf())},n+=8;break;case 9:const c=new Uint8Array(t.buffer,t.byteOffset+n,16);n+=16,r[i]={type:Km,value:`${be(c.subarray(0,4))}-${be(c.subarray(4,6))}-${be(c.subarray(6,8))}-${be(c.subarray(8,10))}-${be(c.subarray(10))}`};break;default:throw new Error("Unrecognized header type tag")}}return r}}var Xi;(function(e){e[e.boolTrue=0]="boolTrue",e[e.boolFalse=1]="boolFalse",e[e.byte=2]="byte",e[e.short=3]="short",e[e.integer=4]="integer",e[e.long=5]="long",e[e.byteArray=6]="byteArray",e[e.string=7]="string",e[e.timestamp=8]="timestamp",e[e.uuid=9]="uuid"})(Xi||(Xi={}));const Qi="boolean",Fm="byte",Hm="short",qm="integer",zm="long",jm="binary",Vm="string",Wm="timestamp",Km="uuid",Gm=/^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/,Ya=4,Fe=Ya*2,Ze=4,Zm=Fe+Ze*2;function Xm({byteLength:e,byteOffset:t,buffer:r}){if(e<Zm)throw new Error("Provided message too short to accommodate event stream message overhead");const n=new DataView(r,t,e),s=n.getUint32(0,!1);if(e!==s)throw new Error("Reported message length does not match received message length");const i=n.getUint32(Ya,!1),o=n.getUint32(Fe,!1),a=n.getUint32(e-Ze,!1),c=new Gt().update(new Uint8Array(r,t,Fe));if(o!==c.digest())throw new Error(`The prelude checksum specified in the message (${o}) does not match the calculated CRC32 checksum (${c.digest()})`);if(c.update(new Uint8Array(r,t+Fe,e-(Fe+Ze))),a!==c.digest())throw new Error(`The message checksum (${c.digest()}) did not match the expected value of ${a}`);return{headers:new DataView(r,t+Fe+Ze,i),body:new Uint8Array(r,t+Fe+Ze+i,s-i-(Fe+Ze+Ze))}}class Qm{constructor(t,r){this.headerMarshaller=new Lm(t,r),this.messageBuffer=[],this.isEndOfStream=!1}feed(t){this.messageBuffer.push(this.decode(t))}endOfStream(){this.isEndOfStream=!0}getMessage(){const t=this.messageBuffer.pop(),r=this.isEndOfStream;return{getMessage(){return t},isEndOfStream(){return r}}}getAvailableMessages(){const t=this.messageBuffer;this.messageBuffer=[];const r=this.isEndOfStream;return{getMessages(){return t},isEndOfStream(){return r}}}encode({headers:t,body:r}){const n=this.headerMarshaller.format(t),s=n.byteLength+r.byteLength+16,i=new Uint8Array(s),o=new DataView(i.buffer,i.byteOffset,i.byteLength),a=new Gt;return o.setUint32(0,s,!1),o.setUint32(4,n.byteLength,!1),o.setUint32(8,a.update(i.subarray(0,8)).digest(),!1),i.set(n,12),i.set(r,n.byteLength+12),o.setUint32(s-4,a.update(i.subarray(8,s-4)).digest(),!1),i}decode(t){const{headers:r,body:n}=Xm(t);return{headers:this.headerMarshaller.parse(r),body:n}}formatHeaders(t){return this.headerMarshaller.format(t)}}class Ym{constructor(t){this.options=t}[Symbol.asyncIterator](){return this.asyncIterator()}async*asyncIterator(){for await(const t of this.options.inputStream)yield this.options.decoder.decode(t)}}class Jm{constructor(t){this.options=t}[Symbol.asyncIterator](){return this.asyncIterator()}async*asyncIterator(){for await(const t of this.options.messageStream)yield this.options.encoder.encode(t);this.options.includeEndFrame&&(yield new Uint8Array(0))}}class ey{constructor(t){this.options=t}[Symbol.asyncIterator](){return this.asyncIterator()}async*asyncIterator(){for await(const t of this.options.messageStream){const r=await this.options.deserializer(t);r!==void 0&&(yield r)}}}class ty{constructor(t){this.options=t}[Symbol.asyncIterator](){return this.asyncIterator()}async*asyncIterator(){for await(const t of this.options.inputStream)yield this.options.serializer(t)}}function ry(e){let t=0,r=0,n=null,s=null;const i=a=>{if(typeof a!="number")throw new Error("Attempted to allocate an event message where size was not a number: "+a);t=a,r=4,n=new Uint8Array(a),new DataView(n.buffer).setUint32(0,a,!1)},o=async function*(){const a=e[Symbol.asyncIterator]();for(;;){const{value:c,done:u}=await a.next();if(u){if(t)if(t===r)yield n;else throw new Error("Truncated event message received.");else return;return}const h=c.length;let d=0;for(;d<h;){if(!n){const g=h-d;s||(s=new Uint8Array(4));const S=Math.min(4-r,g);if(s.set(c.slice(d,d+S),r),r+=S,d+=S,r<4)break;i(new DataView(s.buffer).getUint32(0,!1)),s=null}const p=Math.min(t-r,h-d);n.set(c.slice(d,d+p),r),r+=p,d+=p,t&&t===r&&(yield n,n=null,t=0,r=0)}}};return{[Symbol.asyncIterator]:o}}function ny(e,t){return async function(r){const{value:n}=r.headers[":message-type"];if(n==="error"){const s=new Error(r.headers[":error-message"].value||"UnknownError");throw s.name=r.headers[":error-code"].value,s}else if(n==="exception"){const s=r.headers[":exception-type"].value,i={[s]:r},o=await e(i);if(o.$unknown){const a=new Error(t(r.body));throw a.name=s,a}throw o[s]}else if(n==="event"){const s={[r.headers[":event-type"].value]:r},i=await e(s);return i.$unknown?void 0:i}else throw Error(`Unrecognizable event type: ${r.headers[":event-type"].value}`)}}let sy=class{constructor({utf8Encoder:e,utf8Decoder:t}){this.eventStreamCodec=new Qm(e,t),this.utfEncoder=e}deserialize(e,t){const r=ry(e);return new ey({messageStream:new Ym({inputStream:r,decoder:this.eventStreamCodec}),deserializer:ny(t,this.utfEncoder)})}serialize(e,t){return new Jm({messageStream:new ty({inputStream:e,serializer:t}),encoder:this.eventStreamCodec,includeEndFrame:!0})}};const iy=e=>({[Symbol.asyncIterator]:async function*(){const t=e.getReader();try{for(;;){const{done:r,value:n}=await t.read();if(r)return;yield n}}finally{t.releaseLock()}}}),oy=e=>{const t=e[Symbol.asyncIterator]();return new ReadableStream({async pull(r){const{done:n,value:s}=await t.next();if(n)return r.close();r.enqueue(s)}})};class ay{constructor({utf8Encoder:t,utf8Decoder:r}){this.universalMarshaller=new sy({utf8Decoder:r,utf8Encoder:t})}deserialize(t,r){const n=cy(t)?iy(t):t;return this.universalMarshaller.deserialize(n,r)}serialize(t,r){const n=this.universalMarshaller.serialize(t,r);return typeof ReadableStream=="function"?oy(n):n}}const cy=e=>typeof ReadableStream=="function"&&e instanceof ReadableStream,uy=e=>new ay(e);async function ly(e,t,r=1024*1024){const n=e.size;let s=0;for(;s<n;){const i=e.slice(s,Math.min(n,s+r));t(new Uint8Array(await i.arrayBuffer())),s+=i.size}}const dy=async function(e,t){const r=new e;return await ly(t,n=>{r.update(n)}),r.digest()},hy=e=>()=>Promise.reject(e),Be=64,fy=16,py=[1732584193,4023233417,2562383102,271733878];class gy{constructor(){this.reset()}update(t){if(my(t))return;if(this.finished)throw new Error("Attempted to update an already finished hash.");const r=yy(t);let n=0,{byteLength:s}=r;for(this.bytesHashed+=s;s>0;)this.buffer.setUint8(this.bufferLength++,r[n++]),s--,this.bufferLength===Be&&(this.hashBuffer(),this.bufferLength=0)}async digest(){if(!this.finished){const{buffer:r,bufferLength:n,bytesHashed:s}=this,i=s*8;if(r.setUint8(this.bufferLength++,128),n%Be>=Be-8){for(let o=this.bufferLength;o<Be;o++)r.setUint8(o,0);this.hashBuffer(),this.bufferLength=0}for(let o=this.bufferLength;o<Be-8;o++)r.setUint8(o,0);r.setUint32(Be-8,i>>>0,!0),r.setUint32(Be-4,Math.floor(i/4294967296),!0),this.hashBuffer(),this.finished=!0}const t=new DataView(new ArrayBuffer(fy));for(let r=0;r<4;r++)t.setUint32(r*4,this.state[r],!0);return new Uint8Array(t.buffer,t.byteOffset,t.byteLength)}hashBuffer(){const{buffer:t,state:r}=this;let n=r[0],s=r[1],i=r[2],o=r[3];n=ce(n,s,i,o,t.getUint32(0,!0),7,3614090360),o=ce(o,n,s,i,t.getUint32(4,!0),12,3905402710),i=ce(i,o,n,s,t.getUint32(8,!0),17,606105819),s=ce(s,i,o,n,t.getUint32(12,!0),22,3250441966),n=ce(n,s,i,o,t.getUint32(16,!0),7,4118548399),o=ce(o,n,s,i,t.getUint32(20,!0),12,1200080426),i=ce(i,o,n,s,t.getUint32(24,!0),17,2821735955),s=ce(s,i,o,n,t.getUint32(28,!0),22,4249261313),n=ce(n,s,i,o,t.getUint32(32,!0),7,1770035416),o=ce(o,n,s,i,t.getUint32(36,!0),12,2336552879),i=ce(i,o,n,s,t.getUint32(40,!0),17,4294925233),s=ce(s,i,o,n,t.getUint32(44,!0),22,2304563134),n=ce(n,s,i,o,t.getUint32(48,!0),7,1804603682),o=ce(o,n,s,i,t.getUint32(52,!0),12,4254626195),i=ce(i,o,n,s,t.getUint32(56,!0),17,2792965006),s=ce(s,i,o,n,t.getUint32(60,!0),22,1236535329),n=ue(n,s,i,o,t.getUint32(4,!0),5,4129170786),o=ue(o,n,s,i,t.getUint32(24,!0),9,3225465664),i=ue(i,o,n,s,t.getUint32(44,!0),14,643717713),s=ue(s,i,o,n,t.getUint32(0,!0),20,3921069994),n=ue(n,s,i,o,t.getUint32(20,!0),5,3593408605),o=ue(o,n,s,i,t.getUint32(40,!0),9,38016083),i=ue(i,o,n,s,t.getUint32(60,!0),14,3634488961),s=ue(s,i,o,n,t.getUint32(16,!0),20,3889429448),n=ue(n,s,i,o,t.getUint32(36,!0),5,568446438),o=ue(o,n,s,i,t.getUint32(56,!0),9,3275163606),i=ue(i,o,n,s,t.getUint32(12,!0),14,4107603335),s=ue(s,i,o,n,t.getUint32(32,!0),20,1163531501),n=ue(n,s,i,o,t.getUint32(52,!0),5,2850285829),o=ue(o,n,s,i,t.getUint32(8,!0),9,4243563512),i=ue(i,o,n,s,t.getUint32(28,!0),14,1735328473),s=ue(s,i,o,n,t.getUint32(48,!0),20,2368359562),n=le(n,s,i,o,t.getUint32(20,!0),4,4294588738),o=le(o,n,s,i,t.getUint32(32,!0),11,2272392833),i=le(i,o,n,s,t.getUint32(44,!0),16,1839030562),s=le(s,i,o,n,t.getUint32(56,!0),23,4259657740),n=le(n,s,i,o,t.getUint32(4,!0),4,2763975236),o=le(o,n,s,i,t.getUint32(16,!0),11,1272893353),i=le(i,o,n,s,t.getUint32(28,!0),16,4139469664),s=le(s,i,o,n,t.getUint32(40,!0),23,3200236656),n=le(n,s,i,o,t.getUint32(52,!0),4,681279174),o=le(o,n,s,i,t.getUint32(0,!0),11,3936430074),i=le(i,o,n,s,t.getUint32(12,!0),16,3572445317),s=le(s,i,o,n,t.getUint32(24,!0),23,76029189),n=le(n,s,i,o,t.getUint32(36,!0),4,3654602809),o=le(o,n,s,i,t.getUint32(48,!0),11,3873151461),i=le(i,o,n,s,t.getUint32(60,!0),16,530742520),s=le(s,i,o,n,t.getUint32(8,!0),23,3299628645),n=de(n,s,i,o,t.getUint32(0,!0),6,4096336452),o=de(o,n,s,i,t.getUint32(28,!0),10,1126891415),i=de(i,o,n,s,t.getUint32(56,!0),15,2878612391),s=de(s,i,o,n,t.getUint32(20,!0),21,4237533241),n=de(n,s,i,o,t.getUint32(48,!0),6,1700485571),o=de(o,n,s,i,t.getUint32(12,!0),10,2399980690),i=de(i,o,n,s,t.getUint32(40,!0),15,4293915773),s=de(s,i,o,n,t.getUint32(4,!0),21,2240044497),n=de(n,s,i,o,t.getUint32(32,!0),6,1873313359),o=de(o,n,s,i,t.getUint32(60,!0),10,4264355552),i=de(i,o,n,s,t.getUint32(24,!0),15,2734768916),s=de(s,i,o,n,t.getUint32(52,!0),21,1309151649),n=de(n,s,i,o,t.getUint32(16,!0),6,4149444226),o=de(o,n,s,i,t.getUint32(44,!0),10,3174756917),i=de(i,o,n,s,t.getUint32(8,!0),15,718787259),s=de(s,i,o,n,t.getUint32(36,!0),21,3951481745),r[0]=n+r[0]&4294967295,r[1]=s+r[1]&4294967295,r[2]=i+r[2]&4294967295,r[3]=o+r[3]&4294967295}reset(){this.state=Uint32Array.from(py),this.buffer=new DataView(new ArrayBuffer(Be)),this.bufferLength=0,this.bytesHashed=0,this.finished=!1}}function hr(e,t,r,n,s,i){return t=(t+e&4294967295)+(n+i&4294967295)&4294967295,(t<<s|t>>>32-s)+r&4294967295}function ce(e,t,r,n,s,i,o){return hr(t&r|~t&n,e,t,s,i,o)}function ue(e,t,r,n,s,i,o){return hr(t&n|r&~n,e,t,s,i,o)}function le(e,t,r,n,s,i,o){return hr(t^r^n,e,t,s,i,o)}function de(e,t,r,n,s,i,o){return hr(r^(t|~n),e,t,s,i,o)}function my(e){return typeof e=="string"?e.length===0:e.byteLength===0}function yy(e){return typeof e=="string"?Je(e):ArrayBuffer.isView(e)?new Uint8Array(e.buffer,e.byteOffset,e.byteLength/Uint8Array.BYTES_PER_ELEMENT):new Uint8Array(e)}const Yi=typeof TextEncoder=="function"?new TextEncoder:null,wy=e=>{if(typeof e=="string"){if(Yi)return Yi.encode(e).byteLength;let t=e.length;for(let r=t-1;r>=0;r--){const n=e.charCodeAt(r);n>127&&n<=2047?t++:n>2047&&n<=65535&&(t+=2),n>=56320&&n<=57343&&r--}return t}else{if(typeof e.byteLength=="number")return e.byteLength;if(typeof e.size=="number")return e.size}throw new Error(`Body Length computation failed for ${e}`)},by=e=>({apiVersion:"2006-03-01",base64Decoder:(e==null?void 0:e.base64Decoder)??mn,base64Encoder:(e==null?void 0:e.base64Encoder)??tr,disableHostPrefix:(e==null?void 0:e.disableHostPrefix)??!1,endpointProvider:(e==null?void 0:e.endpointProvider)??Pa,extensions:(e==null?void 0:e.extensions)??[],getAwsChunkedEncodingStream:(e==null?void 0:e.getAwsChunkedEncodingStream)??Lc,httpAuthSchemeProvider:(e==null?void 0:e.httpAuthSchemeProvider)??Up,httpAuthSchemes:(e==null?void 0:e.httpAuthSchemes)??[{schemeId:"aws.auth#sigv4",identityProvider:t=>t.getIdentityProvider("aws.auth#sigv4"),signer:new eo},{schemeId:"aws.auth#sigv4a",identityProvider:t=>t.getIdentityProvider("aws.auth#sigv4a"),signer:new mc}],logger:(e==null?void 0:e.logger)??new vn,sdkStreamMixin:(e==null?void 0:e.sdkStreamMixin)??Kc,serviceId:(e==null?void 0:e.serviceId)??"S3",signerConstructor:(e==null?void 0:e.signerConstructor)??mp,signingEscapePath:(e==null?void 0:e.signingEscapePath)??!1,urlParser:(e==null?void 0:e.urlParser)??Xt,useArnRegion:(e==null?void 0:e.useArnRegion)??!1,utf8Decoder:(e==null?void 0:e.utf8Decoder)??Je,utf8Encoder:(e==null?void 0:e.utf8Encoder)??yn}),vy=["in-region","cross-region","mobile","standard","legacy"],Sy=({defaultsMode:e}={})=>nu(async()=>{const t=typeof e=="function"?await e():e;switch(t==null?void 0:t.toLowerCase()){case"auto":return Promise.resolve(Ey()?"mobile":"standard");case"mobile":case"in-region":case"cross-region":case"standard":case"legacy":return Promise.resolve(t==null?void 0:t.toLocaleLowerCase());case void 0:return Promise.resolve("legacy");default:throw new Error(`Invalid parameter for "defaultsMode", expect ${vy.join(", ")}, got ${t}`)}}),Ey=()=>{var e,t;const r=typeof window<"u"&&(e=window==null?void 0:window.navigator)!=null&&e.userAgent?Qa.parse(window.navigator.userAgent):void 0,n=(t=r==null?void 0:r.platform)==null?void 0:t.type;return n==="tablet"||n==="mobile"},Ay=e=>{const t=Sy(e),r=()=>t().then(nl),n=by(e);return{...n,...e,runtime:"browser",defaultsMode:t,bodyLengthChecker:(e==null?void 0:e.bodyLengthChecker)??wy,credentialDefaultProvider:(e==null?void 0:e.credentialDefaultProvider)??(s=>()=>Promise.reject(new Error("Credential is missing"))),defaultUserAgentProvider:(e==null?void 0:e.defaultUserAgentProvider)??Bm({serviceId:n.serviceId,clientVersion:mm.version}),eventStreamSerdeProvider:(e==null?void 0:e.eventStreamSerdeProvider)??uy,maxAttempts:(e==null?void 0:e.maxAttempts)??Pn,md5:(e==null?void 0:e.md5)??gy,region:(e==null?void 0:e.region)??hy("Region is missing"),requestHandler:wn.create((e==null?void 0:e.requestHandler)??r),retryMode:(e==null?void 0:e.retryMode)??(async()=>(await r()).retryMode||Lf),sha1:(e==null?void 0:e.sha1)??Am,sha256:(e==null?void 0:e.sha256)??Om,streamCollector:(e==null?void 0:e.streamCollector)??no,streamHasher:(e==null?void 0:e.streamHasher)??dy,useDualstackEndpoint:(e==null?void 0:e.useDualstackEndpoint)??(()=>Promise.resolve(vf)),useFipsEndpoint:(e==null?void 0:e.useFipsEndpoint)??(()=>Promise.resolve(Sf))}},ky=e=>{let t=async()=>{if(e.region===void 0)throw new Error("Region is missing from runtimeConfig");const r=e.region;return typeof r=="string"?r:r()};return{setRegion(r){t=r},region(){return t}}},xy=e=>({region:e.region()}),Ry=e=>{const t=e.httpAuthSchemes;let r=e.httpAuthSchemeProvider,n=e.credentials;return{setHttpAuthScheme(s){const i=t.findIndex(o=>o.schemeId===s.schemeId);i===-1?t.push(s):t.splice(i,1,s)},httpAuthSchemes(){return t},setHttpAuthSchemeProvider(s){r=s},httpAuthSchemeProvider(){return r},setCredentials(s){n=s},credentials(){return n}}},Cy=e=>({httpAuthSchemes:e.httpAuthSchemes(),httpAuthSchemeProvider:e.httpAuthSchemeProvider(),credentials:e.credentials()}),Lt=e=>e,Py=(e,t)=>{const r={...Lt(ky(e)),...Lt(cl(e)),...Lt(oc(e)),...Lt(Ry(e))};return t.forEach(n=>n.configure(r)),{...e,...xy(r),...ul(r),...ac(r),...Cy(r)}};class Ty extends Iu{constructor(...[t]){const r=Ay(t||{}),n=_p(r),s=Dh(n),i=Hd(s),o=op(i),a=Ef(o),c=a,u=Bf(c),h=Af(u),d=$p(h),p=Ah(d,{session:[()=>this,pm]}),g=Py(p,(t==null?void 0:t.extensions)||[]);super(g),N(this,"config"),this.config=g,this.middlewareStack.use(bf(this.config)),this.middlewareStack.use(fp(this.config)),this.middlewareStack.use(Rf(this.config)),this.middlewareStack.use(jd(this.config)),this.middlewareStack.use(Kd(this.config)),this.middlewareStack.use(Yd(this.config)),this.middlewareStack.use(vc(this.config,{httpAuthSchemeParametersProvider:Np,identityProviderConfigProvider:async S=>new Yc({"aws.auth#sigv4":S.credentials,"aws.auth#sigv4a":S.credentials})})),this.middlewareStack.use(Cc(this.config)),this.middlewareStack.use(Uh(this.config)),this.middlewareStack.use(dc(this.config)),this.middlewareStack.use(ch(this.config)),this.middlewareStack.use(yh(this.config)),this.middlewareStack.use(Eh(this.config))}destroy(){super.destroy()}}function Iy(e){return t=>async r=>{const n={...r.input},s=[{target:"SSECustomerKey",hash:"SSECustomerKeyMD5"},{target:"CopySourceSSECustomerKey",hash:"CopySourceSSECustomerKeyMD5"}];for(const i of s){const o=n[i.target];if(o){let a;typeof o=="string"?My(o,e)?a=e.base64Decoder(o):(a=e.utf8Decoder(o),n[i.target]=e.base64Encoder(a)):(a=ArrayBuffer.isView(o)?new Uint8Array(o.buffer,o.byteOffset,o.byteLength):new Uint8Array(o),n[i.target]=e.base64Encoder(a));const c=new e.md5;c.update(a),n[i.hash]=e.base64Encoder(await c.digest())}}return t({...r,input:n})}}const Ny={name:"ssecMiddleware",step:"initialize",tags:["SSE"],override:!0},Oy=e=>({applyToStack:t=>{t.add(Iy(e),Ny)}});function My(e,t){if(!/^(?:[A-Za-z0-9+/]{4})*([A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(e))return!1;try{return t.base64Decoder(e).length===32}catch{return!1}}class Uy extends bn.classBuilder().ep({...Na,Bucket:{type:"contextParams",name:"Bucket"},Key:{type:"contextParams",name:"Key"}}).m(function(t,r,n,s){return[to(n,this.serialize,this.deserialize),ia(n,t.getEndpointParameterInstructions()),Fd(n,{requestAlgorithmMember:{httpHeader:"x-amz-sdk-checksum-algorithm",name:"ChecksumAlgorithm"},requestChecksumRequired:!1}),nh(),Vo(n),Oy(n)]}).s("AmazonS3","PutObject",{}).n("S3Client","PutObjectCommand").f(Hp,Fp).ser(zp).de(Vp).build(){}const $y=rc.create({});async function _y(e,t,r){return e.getSignedUrl||console.error("请先配置uploader.getSignedUrl，该方法应该从后端获取签名url"),await e.getSignedUrl(e.bucket,t,e,r)}async function Dy(e,t){const{file:r,onProgress:n,options:s}=e,i=await _y(s,t,"put"),o=decodeURIComponent(i);return await $y.put(o,r,{onUploadProgress:a=>{const{loaded:c,total:u}=a;n({percent:Math.round(c*100/u)})}})}async function By(e){const{file:t,fileName:r,onProgress:n,options:s}=e,i=s,o=new Ty({...(i==null?void 0:i.sdkOpts)||{}}),a=await nc(t,r,i);async function c(){const u={url:i.sdkOpts.endpoint+"/"+i.bucket+"/"+a,key:a};return i.successHandle?await i.successHandle(u):u}if(i.getSignedUrl)await Dy(e,a);else{const u={Bucket:i.bucket,Key:a};await o.send(new Uy({Body:t,...u}))}return await c()}async function Hy(e){const{getConfig:t}=Ja(),r=t("s3"),n=e.options,s=ec(tc(r),n);return e.options=s,await By(e)}export{_y as buildSignedUrl,Hy as upload,Dy as uploadUsingSignedUrl};
