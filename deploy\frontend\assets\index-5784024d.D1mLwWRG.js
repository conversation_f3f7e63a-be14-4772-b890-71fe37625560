import{Y as Nl,K as ua}from"./index.BHZI5pdK.js";import"./vue.BNx9QYep.js";function Rn(e){const t=navigator.userAgent;return t.indexOf("compatible")>-1&&t.indexOf("MSIE")>-1?(new RegExp("MSIE (\\d+\\.\\d+);").test(t),parseFloat(RegExp.$1)):!1}const Il={_isIE11(){let e=0;const t=/MSIE (\d+\.\d+);/.test(navigator.userAgent),r=!!navigator.userAgent.match(/Trident\/7.0/),a=navigator.userAgent.indexOf("rv:11.0");return t&&(e=Number(RegExp.$1)),navigator.appVersion.indexOf("MSIE 10")!==-1&&(e=10),r&&a!==-1&&(e=11),e===11},_isEdge(){return/Edge/.test(navigator.userAgent)},_getDownloadUrl(e){const t="\uFEFF";if(window.Blob&&window.URL&&window.URL.createObjectURL){const r=new Blob([t+e],{type:"text/csv"});return URL.createObjectURL(r)}else return"data:attachment/csv;charset=utf-8,"+t+encodeURIComponent(e)},download(e,t){if(Rn()&&Rn()<10){const r=window.top.open("about:blank","_blank");r.document.charset="utf-8",r.document.write(t),r.document.close(),r.document.execCommand("SaveAs",e+".csv"),r.close()}else if(Rn()===10||this._isIE11()||this._isEdge()){const r="\uFEFF",a=new Blob([r+t],{type:"text/csv"});navigator.msSaveBlob(a,e+".csv")}else{const r=document.createElement("a");r.download=e+".csv",r.href=this._getDownloadUrl(t),document.body.appendChild(r),r.click(),document.body.removeChild(r)}}},Fl=`\r
`,On=(e,t,{separator:r,quoted:a})=>{const n=t.map(s=>a?(s=typeof s=="string"?s.replace(/"/g,'"'):s,`"${s}"`):s);e.push(n.join(r))},Pl={separator:",",quoted:!1};function Dl(e,t,r,a=!1){r=Object.assign({},Pl,r);let n;const s=[],i=[];return e?(n=e.map(o=>typeof o=="string"?o:(a||i.push(typeof o.label<"u"?o.label:o.prop),o.prop)),i.length>0&&On(s,i,r)):(n=[],t.forEach(o=>{Array.isArray(o)||(n=n.concat(Object.keys(o)))}),n.length>0&&(n=n.filter((o,c,l)=>l.indexOf(o)===c),a||On(s,n,r))),Array.isArray(t)&&t.forEach(o=>{Array.isArray(o)||(o=n.map(c=>typeof o[c]<"u"?o[c]:"")),On(s,o,r)}),s.join(Fl)}/*! @source http://purl.eligrey.com/github/Blob.js/blob/master/Blob.js */(function(e){if(e.URL=e.URL||e.webkitURL,e.Blob&&e.URL)try{new Blob;return}catch{}var t=e.BlobBuilder||e.WebKitBlobBuilder||e.MozBlobBuilder||function(r){var a=function(x){return Object.prototype.toString.call(x).match(/^\[object\s(.*)\]$/)[1]},n=function(){this.data=[]},s=function(x,A,P){this.data=x,this.size=x.length,this.type=A,this.encoding=P},i=n.prototype,o=s.prototype,c=r.FileReaderSync,l=function(x){this.code=this[this.name=x]},f="NOT_FOUND_ERR SECURITY_ERR ABORT_ERR NOT_READABLE_ERR ENCODING_ERR NO_MODIFICATION_ALLOWED_ERR INVALID_STATE_ERR SYNTAX_ERR".split(" "),p=f.length,h=r.URL||r.webkitURL||r,d=h.createObjectURL,m=h.revokeObjectURL,u=h,b=r.btoa,T=r.atob,S=r.ArrayBuffer,g=r.Uint8Array;for(s.fake=o.fake=!0;p--;)l.prototype[f[p]]=p+1;return h.createObjectURL||(u=r.URL={}),u.createObjectURL=function(x){var A=x.type,P;if(A===null&&(A="application/octet-stream"),x instanceof s)return P="data:"+A,x.encoding==="base64"?P+";base64,"+x.data:x.encoding==="URI"?P+","+decodeURIComponent(x.data):b?P+";base64,"+b(x.data):P+","+encodeURIComponent(x.data);if(d)return d.call(h,x)},u.revokeObjectURL=function(x){x.substring(0,5)!=="data:"&&m&&m.call(h,x)},i.append=function(x){var A=this.data;if(g&&(x instanceof S||x instanceof g)){for(var P="",R=new g(x),U=0,I=R.length;U<I;U++)P+=String.fromCharCode(R[U]);A.push(P)}else if(a(x)==="Blob"||a(x)==="File")if(c){var G=new c;A.push(G.readAsBinaryString(x))}else throw new l("NOT_READABLE_ERR");else x instanceof s?x.encoding==="base64"&&T?A.push(T(x.data)):x.encoding==="URI"?A.push(decodeURIComponent(x.data)):x.encoding==="raw"&&A.push(x.data):(typeof x!="string"&&(x+=""),A.push(unescape(encodeURIComponent(x))))},i.getBlob=function(x){return arguments.length||(x=null),new s(this.data.join(""),x,"raw")},i.toString=function(){return"[object BlobBuilder]"},o.slice=function(x,A,P){var R=arguments.length;return R<3&&(P=null),new s(this.data.slice(x,R>1?A:this.data.length),P,this.encoding)},o.toString=function(){return"[object Blob]"},o.close=function(){this.size=this.data.length=0},n}(e);e.Blob=function(r,a){var n=a&&a.type||"",s=new t;if(r)for(var i=0,o=r.length;i<o;i++)s.append(r[i]);return s.getBlob(n)}})(typeof self<"u"&&self||typeof window<"u"&&window||globalThis.content||globalThis);var no={exports:{}};(function(e,t){(function(r,a){a()})(ua,function(){function r(l,f){return typeof f>"u"?f={autoBom:!1}:typeof f!="object"&&(console.warn("Deprecated: Expected third argument to be a object"),f={autoBom:!f}),f.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(l.type)?new Blob(["\uFEFF",l],{type:l.type}):l}function a(l,f,p){var h=new XMLHttpRequest;h.open("GET",l),h.responseType="blob",h.onload=function(){c(h.response,f,p)},h.onerror=function(){console.error("could not download file")},h.send()}function n(l){var f=new XMLHttpRequest;f.open("HEAD",l,!1);try{f.send()}catch{}return 200<=f.status&&299>=f.status}function s(l){try{l.dispatchEvent(new MouseEvent("click"))}catch{var f=document.createEvent("MouseEvents");f.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),l.dispatchEvent(f)}}var i=typeof window=="object"&&window.window===window?window:typeof self=="object"&&self.self===self?self:typeof ua=="object"&&ua.global===ua?ua:void 0,o=i.navigator&&/Macintosh/.test(navigator.userAgent)&&/AppleWebKit/.test(navigator.userAgent)&&!/Safari/.test(navigator.userAgent),c=i.saveAs||(typeof window!="object"||window!==i?function(){}:"download"in HTMLAnchorElement.prototype&&!o?function(l,f,p){var h=i.URL||i.webkitURL,d=document.createElement("a");f=f||l.name||"download",d.download=f,d.rel="noopener",typeof l=="string"?(d.href=l,d.origin===location.origin?s(d):n(d.href)?a(l,f,p):s(d,d.target="_blank")):(d.href=h.createObjectURL(l),setTimeout(function(){h.revokeObjectURL(d.href)},4e4),setTimeout(function(){s(d)},0))}:"msSaveOrOpenBlob"in navigator?function(l,f,p){if(f=f||l.name||"download",typeof l!="string")navigator.msSaveOrOpenBlob(r(l,p),f);else if(n(l))a(l,f,p);else{var h=document.createElement("a");h.href=l,h.target="_blank",setTimeout(function(){s(h)})}}:function(l,f,p,h){if(h=h||open("","_blank"),h&&(h.document.title=h.document.body.innerText="downloading..."),typeof l=="string")return a(l,f,p);var d=l.type==="application/octet-stream",m=/constructor/i.test(i.HTMLElement)||i.safari,u=/CriOS\/[\d]+/.test(navigator.userAgent);if((u||d&&m||o)&&typeof FileReader<"u"){var b=new FileReader;b.onloadend=function(){var g=b.result;g=u?g:g.replace(/^data:[^;]*;/,"data:attachment/file;"),h?h.location.href=g:location=g,h=null},b.readAsDataURL(l)}else{var T=i.URL||i.webkitURL,S=T.createObjectURL(l);h?h.location=S:location.href=S,h=null,setTimeout(function(){T.revokeObjectURL(S)},4e4)}});i.saveAs=c.saveAs=c,e.exports=c})})(no);var Ll=no.exports;const so=Nl(Ll);/*! xlsx.js (C) 2013-present SheetJS -- http://sheetjs.com */var an={};an.version="0.18.5";var io=1252,Ml=[874,932,936,949,950,1250,1251,1252,1253,1254,1255,1256,1257,1258,1e4],is={0:1252,1:65001,2:65001,77:1e4,128:932,129:949,130:1361,134:936,136:950,161:1253,162:1254,163:1258,177:1255,178:1256,186:1257,204:1251,222:874,238:1250,255:1252,69:6969},os=function(e){Ml.indexOf(e)!=-1&&(io=is[0]=e)};function Ul(){os(1252)}var jr=function(e){os(e)};function cs(){jr(1200),Ul()}function Zs(e){for(var t=[],r=0,a=e.length;r<a;++r)t[r]=e.charCodeAt(r);return t}function Bl(e){for(var t=[],r=0;r<e.length>>1;++r)t[r]=String.fromCharCode(e.charCodeAt(2*r)+(e.charCodeAt(2*r+1)<<8));return t.join("")}function oo(e){for(var t=[],r=0;r<e.length>>1;++r)t[r]=String.fromCharCode(e.charCodeAt(2*r+1)+(e.charCodeAt(2*r)<<8));return t.join("")}var pa=function(e){var t=e.charCodeAt(0),r=e.charCodeAt(1);return t==255&&r==254?Bl(e.slice(2)):t==254&&r==255?oo(e.slice(2)):t==65279?e.slice(1):e},$a=function(e){return String.fromCharCode(e)},qs=function(e){return String.fromCharCode(e)},jn,mt="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function _a(e){for(var t="",r=0,a=0,n=0,s=0,i=0,o=0,c=0,l=0;l<e.length;)r=e.charCodeAt(l++),s=r>>2,a=e.charCodeAt(l++),i=(r&3)<<4|a>>4,n=e.charCodeAt(l++),o=(a&15)<<2|n>>6,c=n&63,isNaN(a)?o=c=64:isNaN(n)&&(c=64),t+=mt.charAt(s)+mt.charAt(i)+mt.charAt(o)+mt.charAt(c);return t}function Ir(e){var t="",r=0,a=0,n=0,s=0,i=0,o=0,c=0;e=e.replace(/[^\w\+\/\=]/g,"");for(var l=0;l<e.length;)s=mt.indexOf(e.charAt(l++)),i=mt.indexOf(e.charAt(l++)),r=s<<2|i>>4,t+=String.fromCharCode(r),o=mt.indexOf(e.charAt(l++)),a=(i&15)<<4|o>>2,o!==64&&(t+=String.fromCharCode(a)),c=mt.indexOf(e.charAt(l++)),n=(o&3)<<6|c,c!==64&&(t+=String.fromCharCode(n));return t}var Se=function(){return typeof Buffer<"u"&&typeof process<"u"&&typeof process.versions<"u"&&!!process.versions.node}(),lt=function(){if(typeof Buffer<"u"){var e=!Buffer.from;if(!e)try{Buffer.from("foo","utf8")}catch{e=!0}return e?function(t,r){return r?new Buffer(t,r):new Buffer(t)}:Buffer.from.bind(Buffer)}return function(){}}();function vt(e){return Se?Buffer.alloc?Buffer.alloc(e):new Buffer(e):typeof Uint8Array<"u"?new Uint8Array(e):new Array(e)}function Qs(e){return Se?Buffer.allocUnsafe?Buffer.allocUnsafe(e):new Buffer(e):typeof Uint8Array<"u"?new Uint8Array(e):new Array(e)}var Or=function(e){return Se?lt(e,"binary"):e.split("").map(function(t){return t.charCodeAt(0)&255})};function vn(e){if(typeof ArrayBuffer>"u")return Or(e);for(var t=new ArrayBuffer(e.length),r=new Uint8Array(t),a=0;a!=e.length;++a)r[a]=e.charCodeAt(a)&255;return t}function Tt(e){if(Array.isArray(e))return e.map(function(a){return String.fromCharCode(a)}).join("");for(var t=[],r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}function Wl(e){if(typeof Uint8Array>"u")throw new Error("Unsupported");return new Uint8Array(e)}function ls(e){if(typeof ArrayBuffer>"u")throw new Error("Unsupported");if(e instanceof ArrayBuffer)return ls(new Uint8Array(e));for(var t=new Array(e.length),r=0;r<e.length;++r)t[r]=e[r];return t}var ir=Se?function(e){return Buffer.concat(e.map(function(t){return Buffer.isBuffer(t)?t:lt(t)}))}:function(e){if(typeof Uint8Array<"u"){var t=0,r=0;for(t=0;t<e.length;++t)r+=e[t].length;var a=new Uint8Array(r),n=0;for(t=0,r=0;t<e.length;r+=n,++t)if(n=e[t].length,e[t]instanceof Uint8Array)a.set(e[t],r);else{if(typeof e[t]=="string")throw"wtf";a.set(new Uint8Array(e[t]),r)}return a}return[].concat.apply([],e.map(function(s){return Array.isArray(s)?s:[].slice.call(s)}))};function zl(e){for(var t=[],r=0,a=e.length+250,n=vt(e.length+255),s=0;s<e.length;++s){var i=e.charCodeAt(s);if(i<128)n[r++]=i;else if(i<2048)n[r++]=192|i>>6&31,n[r++]=128|i&63;else if(i>=55296&&i<57344){i=(i&1023)+64;var o=e.charCodeAt(++s)&1023;n[r++]=240|i>>8&7,n[r++]=128|i>>2&63,n[r++]=128|o>>6&15|(i&3)<<4,n[r++]=128|o&63}else n[r++]=224|i>>12&15,n[r++]=128|i>>6&63,n[r++]=128|i&63;r>a&&(t.push(n.slice(0,r)),r=0,n=vt(65535),a=65530)}return t.push(n.slice(0,r)),ir(t)}var yr=/\u0000/g,ma=/[\u0001-\u0006]/g;function Kt(e){for(var t="",r=e.length-1;r>=0;)t+=e.charAt(r--);return t}function $r(e,t){var r=""+e;return r.length>=t?r:$e("0",t-r.length)+r}function fs(e,t){var r=""+e;return r.length>=t?r:$e(" ",t-r.length)+r}function nn(e,t){var r=""+e;return r.length>=t?r:r+$e(" ",t-r.length)}function Hl(e,t){var r=""+Math.round(e);return r.length>=t?r:$e("0",t-r.length)+r}function Gl(e,t){var r=""+e;return r.length>=t?r:$e("0",t-r.length)+r}var ei=Math.pow(2,32);function Vt(e,t){if(e>ei||e<-ei)return Hl(e,t);var r=Math.round(e);return Gl(r,t)}function sn(e,t){return t=t||0,e.length>=7+t&&(e.charCodeAt(t)|32)===103&&(e.charCodeAt(t+1)|32)===101&&(e.charCodeAt(t+2)|32)===110&&(e.charCodeAt(t+3)|32)===101&&(e.charCodeAt(t+4)|32)===114&&(e.charCodeAt(t+5)|32)===97&&(e.charCodeAt(t+6)|32)===108}var ri=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]],Nn=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]];function Vl(e){return e||(e={}),e[0]="General",e[1]="0",e[2]="0.00",e[3]="#,##0",e[4]="#,##0.00",e[9]="0%",e[10]="0.00%",e[11]="0.00E+00",e[12]="# ?/?",e[13]="# ??/??",e[14]="m/d/yy",e[15]="d-mmm-yy",e[16]="d-mmm",e[17]="mmm-yy",e[18]="h:mm AM/PM",e[19]="h:mm:ss AM/PM",e[20]="h:mm",e[21]="h:mm:ss",e[22]="m/d/yy h:mm",e[37]="#,##0 ;(#,##0)",e[38]="#,##0 ;[Red](#,##0)",e[39]="#,##0.00;(#,##0.00)",e[40]="#,##0.00;[Red](#,##0.00)",e[45]="mm:ss",e[46]="[h]:mm:ss",e[47]="mmss.0",e[48]="##0.0E+0",e[49]="@",e[56]='"上午/下午 "hh"時"mm"分"ss"秒 "',e}var be={0:"General",1:"0",2:"0.00",3:"#,##0",4:"#,##0.00",9:"0%",10:"0.00%",11:"0.00E+00",12:"# ?/?",13:"# ??/??",14:"m/d/yy",15:"d-mmm-yy",16:"d-mmm",17:"mmm-yy",18:"h:mm AM/PM",19:"h:mm:ss AM/PM",20:"h:mm",21:"h:mm:ss",22:"m/d/yy h:mm",37:"#,##0 ;(#,##0)",38:"#,##0 ;[Red](#,##0)",39:"#,##0.00;(#,##0.00)",40:"#,##0.00;[Red](#,##0.00)",45:"mm:ss",46:"[h]:mm:ss",47:"mmss.0",48:"##0.0E+0",49:"@",56:'"上午/下午 "hh"時"mm"分"ss"秒 "'},ti={5:37,6:38,7:39,8:40,23:0,24:0,25:0,26:0,27:14,28:14,29:14,30:14,31:14,50:14,51:14,52:14,53:14,54:14,55:14,56:14,57:14,58:14,59:1,60:2,61:3,62:4,67:9,68:10,69:12,70:13,71:14,72:14,73:15,74:16,75:17,76:20,77:21,78:22,79:45,80:46,81:47,82:0},jl={5:'"$"#,##0_);\\("$"#,##0\\)',63:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',41:'_(* #,##0_);_(* \\(#,##0\\);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* \\(#,##0\\);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* \\(#,##0.00\\);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* \\(#,##0.00\\);_("$"* "-"??_);_(@_)'};function on(e,t,r){for(var a=e<0?-1:1,n=e*a,s=0,i=1,o=0,c=1,l=0,f=0,p=Math.floor(n);l<t&&(p=Math.floor(n),o=p*i+s,f=p*l+c,!(n-p<5e-8));)n=1/(n-p),s=i,i=o,c=l,l=f;if(f>t&&(l>t?(f=c,o=s):(f=l,o=i)),!r)return[0,a*o,f];var h=Math.floor(a*o/f);return[h,a*o-h*f,f]}function Ct(e,t,r){if(e>2958465||e<0)return null;var a=e|0,n=Math.floor(86400*(e-a)),s=0,i=[],o={D:a,T:n,u:86400*(e-a)-n,y:0,m:0,d:0,H:0,M:0,S:0,q:0};if(Math.abs(o.u)<1e-6&&(o.u=0),t&&t.date1904&&(a+=1462),o.u>.9999&&(o.u=0,++n==86400&&(o.T=n=0,++a,++o.D)),a===60)i=r?[1317,10,29]:[1900,2,29],s=3;else if(a===0)i=r?[1317,8,29]:[1900,1,0],s=6;else{a>60&&--a;var c=new Date(1900,0,1);c.setDate(c.getDate()+a-1),i=[c.getFullYear(),c.getMonth()+1,c.getDate()],s=c.getDay(),a<60&&(s=(s+6)%7),r&&(s=Zl(c,i))}return o.y=i[0],o.m=i[1],o.d=i[2],o.S=n%60,n=Math.floor(n/60),o.M=n%60,n=Math.floor(n/60),o.H=n,o.q=s,o}var co=new Date(1899,11,31,0,0,0),$l=co.getTime(),Xl=new Date(1900,2,1,0,0,0);function lo(e,t){var r=e.getTime();return t?r-=1461*24*60*60*1e3:e>=Xl&&(r+=24*60*60*1e3),(r-($l+(e.getTimezoneOffset()-co.getTimezoneOffset())*6e4))/(24*60*60*1e3)}function hs(e){return e.indexOf(".")==-1?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)$/,"$1")}function Yl(e){return e.indexOf("E")==-1?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)[Ee]/,"$1E").replace(/(E[+-])(\d)$/,"$10$2")}function Kl(e){var t=e<0?12:11,r=hs(e.toFixed(12));return r.length<=t||(r=e.toPrecision(10),r.length<=t)?r:e.toExponential(5)}function Jl(e){var t=hs(e.toFixed(11));return t.length>(e<0?12:11)||t==="0"||t==="-0"?e.toPrecision(6):t}function xa(e){var t=Math.floor(Math.log(Math.abs(e))*Math.LOG10E),r;return t>=-4&&t<=-1?r=e.toPrecision(10+t):Math.abs(t)<=9?r=Kl(e):t===10?r=e.toFixed(10).substr(0,12):r=Jl(e),hs(Yl(r.toUpperCase()))}function Nt(e,t){switch(typeof e){case"string":return e;case"boolean":return e?"TRUE":"FALSE";case"number":return(e|0)===e?e.toString(10):xa(e);case"undefined":return"";case"object":if(e==null)return"";if(e instanceof Date)return zr(14,lo(e,t&&t.date1904),t)}throw new Error("unsupported value in General format: "+e)}function Zl(e,t){t[0]-=581;var r=e.getDay();return e<60&&(r=(r+6)%7),r}function ql(e,t,r,a){var n="",s=0,i=0,o=r.y,c,l=0;switch(e){case 98:o=r.y+543;case 121:switch(t.length){case 1:case 2:c=o%100,l=2;break;default:c=o%1e4,l=4;break}break;case 109:switch(t.length){case 1:case 2:c=r.m,l=t.length;break;case 3:return Nn[r.m-1][1];case 5:return Nn[r.m-1][0];default:return Nn[r.m-1][2]}break;case 100:switch(t.length){case 1:case 2:c=r.d,l=t.length;break;case 3:return ri[r.q][0];default:return ri[r.q][1]}break;case 104:switch(t.length){case 1:case 2:c=1+(r.H+11)%12,l=t.length;break;default:throw"bad hour format: "+t}break;case 72:switch(t.length){case 1:case 2:c=r.H,l=t.length;break;default:throw"bad hour format: "+t}break;case 77:switch(t.length){case 1:case 2:c=r.M,l=t.length;break;default:throw"bad minute format: "+t}break;case 115:if(t!="s"&&t!="ss"&&t!=".0"&&t!=".00"&&t!=".000")throw"bad second format: "+t;return r.u===0&&(t=="s"||t=="ss")?$r(r.S,t.length):(a>=2?i=a===3?1e3:100:i=a===1?10:1,s=Math.round(i*(r.S+r.u)),s>=60*i&&(s=0),t==="s"?s===0?"0":""+s/i:(n=$r(s,2+a),t==="ss"?n.substr(0,2):"."+n.substr(2,t.length-1)));case 90:switch(t){case"[h]":case"[hh]":c=r.D*24+r.H;break;case"[m]":case"[mm]":c=(r.D*24+r.H)*60+r.M;break;case"[s]":case"[ss]":c=((r.D*24+r.H)*60+r.M)*60+Math.round(r.S+r.u);break;default:throw"bad abstime format: "+t}l=t.length===3?1:2;break;case 101:c=o,l=1;break}var f=l>0?$r(c,l):"";return f}function bt(e){var t=3;if(e.length<=t)return e;for(var r=e.length%t,a=e.substr(0,r);r!=e.length;r+=t)a+=(a.length>0?",":"")+e.substr(r,t);return a}var fo=/%/g;function Ql(e,t,r){var a=t.replace(fo,""),n=t.length-a.length;return st(e,a,r*Math.pow(10,2*n))+$e("%",n)}function ef(e,t,r){for(var a=t.length-1;t.charCodeAt(a-1)===44;)--a;return st(e,t.substr(0,a),r/Math.pow(10,3*(t.length-a)))}function ho(e,t){var r,a=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(t==0)return"0.0E+0";if(t<0)return"-"+ho(e,-t);var n=e.indexOf(".");n===-1&&(n=e.indexOf("E"));var s=Math.floor(Math.log(t)*Math.LOG10E)%n;if(s<0&&(s+=n),r=(t/Math.pow(10,s)).toPrecision(a+1+(n+s)%n),r.indexOf("e")===-1){var i=Math.floor(Math.log(t)*Math.LOG10E);for(r.indexOf(".")===-1?r=r.charAt(0)+"."+r.substr(1)+"E+"+(i-r.length+s):r+="E+"+(i-s);r.substr(0,2)==="0.";)r=r.charAt(0)+r.substr(2,n)+"."+r.substr(2+n),r=r.replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.");r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(o,c,l,f){return c+l+f.substr(0,(n+s)%n)+"."+f.substr(s)+"E"})}else r=t.toExponential(a);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}var uo=/# (\?+)( ?)\/( ?)(\d+)/;function rf(e,t,r){var a=parseInt(e[4],10),n=Math.round(t*a),s=Math.floor(n/a),i=n-s*a,o=a;return r+(s===0?"":""+s)+" "+(i===0?$e(" ",e[1].length+1+e[4].length):fs(i,e[1].length)+e[2]+"/"+e[3]+$r(o,e[4].length))}function tf(e,t,r){return r+(t===0?"":""+t)+$e(" ",e[1].length+2+e[4].length)}var po=/^#*0*\.([0#]+)/,mo=/\).*[0#]/,bo=/\(###\) ###\\?-####/;function Tr(e){for(var t="",r,a=0;a!=e.length;++a)switch(r=e.charCodeAt(a)){case 35:break;case 63:t+=" ";break;case 48:t+="0";break;default:t+=String.fromCharCode(r)}return t}function ai(e,t){var r=Math.pow(10,t);return""+Math.round(e*r)/r}function ni(e,t){var r=e-Math.floor(e),a=Math.pow(10,t);return t<(""+Math.round(r*a)).length?0:Math.round(r*a)}function af(e,t){return t<(""+Math.round((e-Math.floor(e))*Math.pow(10,t))).length?1:0}function nf(e){return e<2147483647&&e>-2147483648?""+(e>=0?e|0:e-1|0):""+Math.floor(e)}function Dr(e,t,r){if(e.charCodeAt(0)===40&&!t.match(mo)){var a=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?Dr("n",a,r):"("+Dr("n",a,-r)+")"}if(t.charCodeAt(t.length-1)===44)return ef(e,t,r);if(t.indexOf("%")!==-1)return Ql(e,t,r);if(t.indexOf("E")!==-1)return ho(t,r);if(t.charCodeAt(0)===36)return"$"+Dr(e,t.substr(t.charAt(1)==" "?2:1),r);var n,s,i,o,c=Math.abs(r),l=r<0?"-":"";if(t.match(/^00+$/))return l+Vt(c,t.length);if(t.match(/^[#?]+$/))return n=Vt(r,0),n==="0"&&(n=""),n.length>t.length?n:Tr(t.substr(0,t.length-n.length))+n;if(s=t.match(uo))return rf(s,c,l);if(t.match(/^#+0+$/))return l+Vt(c,t.length-t.indexOf("0"));if(s=t.match(po))return n=ai(r,s[1].length).replace(/^([^\.]+)$/,"$1."+Tr(s[1])).replace(/\.$/,"."+Tr(s[1])).replace(/\.(\d*)$/,function(m,u){return"."+u+$e("0",Tr(s[1]).length-u.length)}),t.indexOf("0.")!==-1?n:n.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),s=t.match(/^(0*)\.(#*)$/))return l+ai(c,s[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,s[1].length?"0.":".");if(s=t.match(/^#{1,3},##0(\.?)$/))return l+bt(Vt(c,0));if(s=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+Dr(e,t,-r):bt(""+(Math.floor(r)+af(r,s[1].length)))+"."+$r(ni(r,s[1].length),s[1].length);if(s=t.match(/^#,#*,#0/))return Dr(e,t.replace(/^#,#*,/,""),r);if(s=t.match(/^([0#]+)(\\?-([0#]+))+$/))return n=Kt(Dr(e,t.replace(/[\\-]/g,""),r)),i=0,Kt(Kt(t.replace(/\\/g,"")).replace(/[0#]/g,function(m){return i<n.length?n.charAt(i++):m==="0"?"0":""}));if(t.match(bo))return n=Dr(e,"##########",r),"("+n.substr(0,3)+") "+n.substr(3,3)+"-"+n.substr(6);var f="";if(s=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(s[4].length,7),o=on(c,Math.pow(10,i)-1,!1),n=""+l,f=st("n",s[1],o[1]),f.charAt(f.length-1)==" "&&(f=f.substr(0,f.length-1)+"0"),n+=f+s[2]+"/"+s[3],f=nn(o[2],i),f.length<s[4].length&&(f=Tr(s[4].substr(s[4].length-f.length))+f),n+=f,n;if(s=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(Math.max(s[1].length,s[4].length),7),o=on(c,Math.pow(10,i)-1,!0),l+(o[0]||(o[1]?"":"0"))+" "+(o[1]?fs(o[1],i)+s[2]+"/"+s[3]+nn(o[2],i):$e(" ",2*i+1+s[2].length+s[3].length));if(s=t.match(/^[#0?]+$/))return n=Vt(r,0),t.length<=n.length?n:Tr(t.substr(0,t.length-n.length))+n;if(s=t.match(/^([#0?]+)\.([#0]+)$/)){n=""+r.toFixed(Math.min(s[2].length,10)).replace(/([^0])0+$/,"$1"),i=n.indexOf(".");var p=t.indexOf(".")-i,h=t.length-n.length-p;return Tr(t.substr(0,p)+n+t.substr(t.length-h))}if(s=t.match(/^00,000\.([#0]*0)$/))return i=ni(r,s[1].length),r<0?"-"+Dr(e,t,-r):bt(nf(r)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(m){return"00,"+(m.length<3?$r(0,3-m.length):"")+m})+"."+$r(i,s[1].length);switch(t){case"###,##0.00":return Dr(e,"#,##0.00",r);case"###,###":case"##,###":case"#,###":var d=bt(Vt(c,0));return d!=="0"?l+d:"";case"###,###.00":return Dr(e,"###,##0.00",r).replace(/^0\./,".");case"#,###.00":return Dr(e,"#,##0.00",r).replace(/^0\./,".")}throw new Error("unsupported format |"+t+"|")}function sf(e,t,r){for(var a=t.length-1;t.charCodeAt(a-1)===44;)--a;return st(e,t.substr(0,a),r/Math.pow(10,3*(t.length-a)))}function of(e,t,r){var a=t.replace(fo,""),n=t.length-a.length;return st(e,a,r*Math.pow(10,2*n))+$e("%",n)}function vo(e,t){var r,a=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(t==0)return"0.0E+0";if(t<0)return"-"+vo(e,-t);var n=e.indexOf(".");n===-1&&(n=e.indexOf("E"));var s=Math.floor(Math.log(t)*Math.LOG10E)%n;if(s<0&&(s+=n),r=(t/Math.pow(10,s)).toPrecision(a+1+(n+s)%n),!r.match(/[Ee]/)){var i=Math.floor(Math.log(t)*Math.LOG10E);r.indexOf(".")===-1?r=r.charAt(0)+"."+r.substr(1)+"E+"+(i-r.length+s):r+="E+"+(i-s),r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(o,c,l,f){return c+l+f.substr(0,(n+s)%n)+"."+f.substr(s)+"E"})}else r=t.toExponential(a);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}function Jr(e,t,r){if(e.charCodeAt(0)===40&&!t.match(mo)){var a=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?Jr("n",a,r):"("+Jr("n",a,-r)+")"}if(t.charCodeAt(t.length-1)===44)return sf(e,t,r);if(t.indexOf("%")!==-1)return of(e,t,r);if(t.indexOf("E")!==-1)return vo(t,r);if(t.charCodeAt(0)===36)return"$"+Jr(e,t.substr(t.charAt(1)==" "?2:1),r);var n,s,i,o,c=Math.abs(r),l=r<0?"-":"";if(t.match(/^00+$/))return l+$r(c,t.length);if(t.match(/^[#?]+$/))return n=""+r,r===0&&(n=""),n.length>t.length?n:Tr(t.substr(0,t.length-n.length))+n;if(s=t.match(uo))return tf(s,c,l);if(t.match(/^#+0+$/))return l+$r(c,t.length-t.indexOf("0"));if(s=t.match(po))return n=(""+r).replace(/^([^\.]+)$/,"$1."+Tr(s[1])).replace(/\.$/,"."+Tr(s[1])),n=n.replace(/\.(\d*)$/,function(m,u){return"."+u+$e("0",Tr(s[1]).length-u.length)}),t.indexOf("0.")!==-1?n:n.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),s=t.match(/^(0*)\.(#*)$/))return l+(""+c).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,s[1].length?"0.":".");if(s=t.match(/^#{1,3},##0(\.?)$/))return l+bt(""+c);if(s=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+Jr(e,t,-r):bt(""+r)+"."+$e("0",s[1].length);if(s=t.match(/^#,#*,#0/))return Jr(e,t.replace(/^#,#*,/,""),r);if(s=t.match(/^([0#]+)(\\?-([0#]+))+$/))return n=Kt(Jr(e,t.replace(/[\\-]/g,""),r)),i=0,Kt(Kt(t.replace(/\\/g,"")).replace(/[0#]/g,function(m){return i<n.length?n.charAt(i++):m==="0"?"0":""}));if(t.match(bo))return n=Jr(e,"##########",r),"("+n.substr(0,3)+") "+n.substr(3,3)+"-"+n.substr(6);var f="";if(s=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(s[4].length,7),o=on(c,Math.pow(10,i)-1,!1),n=""+l,f=st("n",s[1],o[1]),f.charAt(f.length-1)==" "&&(f=f.substr(0,f.length-1)+"0"),n+=f+s[2]+"/"+s[3],f=nn(o[2],i),f.length<s[4].length&&(f=Tr(s[4].substr(s[4].length-f.length))+f),n+=f,n;if(s=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(Math.max(s[1].length,s[4].length),7),o=on(c,Math.pow(10,i)-1,!0),l+(o[0]||(o[1]?"":"0"))+" "+(o[1]?fs(o[1],i)+s[2]+"/"+s[3]+nn(o[2],i):$e(" ",2*i+1+s[2].length+s[3].length));if(s=t.match(/^[#0?]+$/))return n=""+r,t.length<=n.length?n:Tr(t.substr(0,t.length-n.length))+n;if(s=t.match(/^([#0]+)\.([#0]+)$/)){n=""+r.toFixed(Math.min(s[2].length,10)).replace(/([^0])0+$/,"$1"),i=n.indexOf(".");var p=t.indexOf(".")-i,h=t.length-n.length-p;return Tr(t.substr(0,p)+n+t.substr(t.length-h))}if(s=t.match(/^00,000\.([#0]*0)$/))return r<0?"-"+Jr(e,t,-r):bt(""+r).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(m){return"00,"+(m.length<3?$r(0,3-m.length):"")+m})+"."+$r(0,s[1].length);switch(t){case"###,###":case"##,###":case"#,###":var d=bt(""+c);return d!=="0"?l+d:"";default:if(t.match(/\.[0#?]*$/))return Jr(e,t.slice(0,t.lastIndexOf(".")),r)+Tr(t.slice(t.lastIndexOf(".")))}throw new Error("unsupported format |"+t+"|")}function st(e,t,r){return(r|0)===r?Jr(e,t,r):Dr(e,t,r)}function cf(e){for(var t=[],r=!1,a=0,n=0;a<e.length;++a)switch(e.charCodeAt(a)){case 34:r=!r;break;case 95:case 42:case 92:++a;break;case 59:t[t.length]=e.substr(n,a-n),n=a+1}if(t[t.length]=e.substr(n),r===!0)throw new Error("Format |"+e+"| unterminated string ");return t}var go=/\[[HhMmSs\u0E0A\u0E19\u0E17]*\]/;function na(e){for(var t=0,r="",a="";t<e.length;)switch(r=e.charAt(t)){case"G":sn(e,t)&&(t+=6),t++;break;case'"':for(;e.charCodeAt(++t)!==34&&t<e.length;);++t;break;case"\\":t+=2;break;case"_":t+=2;break;case"@":++t;break;case"B":case"b":if(e.charAt(t+1)==="1"||e.charAt(t+1)==="2")return!0;case"M":case"D":case"Y":case"H":case"S":case"E":case"m":case"d":case"y":case"h":case"s":case"e":case"g":return!0;case"A":case"a":case"上":if(e.substr(t,3).toUpperCase()==="A/P"||e.substr(t,5).toUpperCase()==="AM/PM"||e.substr(t,5).toUpperCase()==="上午/下午")return!0;++t;break;case"[":for(a=r;e.charAt(t++)!=="]"&&t<e.length;)a+=e.charAt(t);if(a.match(go))return!0;break;case".":case"0":case"#":for(;t<e.length&&("0#?.,E+-%".indexOf(r=e.charAt(++t))>-1||r=="\\"&&e.charAt(t+1)=="-"&&"0#".indexOf(e.charAt(t+2))>-1););break;case"?":for(;e.charAt(++t)===r;);break;case"*":++t,(e.charAt(t)==" "||e.charAt(t)=="*")&&++t;break;case"(":case")":++t;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(;t<e.length&&"0123456789".indexOf(e.charAt(++t))>-1;);break;case" ":++t;break;default:++t;break}return!1}function lf(e,t,r,a){for(var n=[],s="",i=0,o="",c="t",l,f,p,h="H";i<e.length;)switch(o=e.charAt(i)){case"G":if(!sn(e,i))throw new Error("unrecognized character "+o+" in "+e);n[n.length]={t:"G",v:"General"},i+=7;break;case'"':for(s="";(p=e.charCodeAt(++i))!==34&&i<e.length;)s+=String.fromCharCode(p);n[n.length]={t:"t",v:s},++i;break;case"\\":var d=e.charAt(++i),m=d==="("||d===")"?d:"t";n[n.length]={t:m,v:d},++i;break;case"_":n[n.length]={t:"t",v:" "},i+=2;break;case"@":n[n.length]={t:"T",v:t},++i;break;case"B":case"b":if(e.charAt(i+1)==="1"||e.charAt(i+1)==="2"){if(l==null&&(l=Ct(t,r,e.charAt(i+1)==="2"),l==null))return"";n[n.length]={t:"X",v:e.substr(i,2)},c=o,i+=2;break}case"M":case"D":case"Y":case"H":case"S":case"E":o=o.toLowerCase();case"m":case"d":case"y":case"h":case"s":case"e":case"g":if(t<0||l==null&&(l=Ct(t,r),l==null))return"";for(s=o;++i<e.length&&e.charAt(i).toLowerCase()===o;)s+=o;o==="m"&&c.toLowerCase()==="h"&&(o="M"),o==="h"&&(o=h),n[n.length]={t:o,v:s},c=o;break;case"A":case"a":case"上":var u={t:o,v:o};if(l==null&&(l=Ct(t,r)),e.substr(i,3).toUpperCase()==="A/P"?(l!=null&&(u.v=l.H>=12?"P":"A"),u.t="T",h="h",i+=3):e.substr(i,5).toUpperCase()==="AM/PM"?(l!=null&&(u.v=l.H>=12?"PM":"AM"),u.t="T",i+=5,h="h"):e.substr(i,5).toUpperCase()==="上午/下午"?(l!=null&&(u.v=l.H>=12?"下午":"上午"),u.t="T",i+=5,h="h"):(u.t="t",++i),l==null&&u.t==="T")return"";n[n.length]=u,c=o;break;case"[":for(s=o;e.charAt(i++)!=="]"&&i<e.length;)s+=e.charAt(i);if(s.slice(-1)!=="]")throw'unterminated "[" block: |'+s+"|";if(s.match(go)){if(l==null&&(l=Ct(t,r),l==null))return"";n[n.length]={t:"Z",v:s.toLowerCase()},c=s.charAt(1)}else s.indexOf("$")>-1&&(s=(s.match(/\$([^-\[\]]*)/)||[])[1]||"$",na(e)||(n[n.length]={t:"t",v:s}));break;case".":if(l!=null){for(s=o;++i<e.length&&(o=e.charAt(i))==="0";)s+=o;n[n.length]={t:"s",v:s};break}case"0":case"#":for(s=o;++i<e.length&&"0#?.,E+-%".indexOf(o=e.charAt(i))>-1;)s+=o;n[n.length]={t:"n",v:s};break;case"?":for(s=o;e.charAt(++i)===o;)s+=o;n[n.length]={t:o,v:s},c=o;break;case"*":++i,(e.charAt(i)==" "||e.charAt(i)=="*")&&++i;break;case"(":case")":n[n.length]={t:a===1?"t":o,v:o},++i;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(s=o;i<e.length&&"0123456789".indexOf(e.charAt(++i))>-1;)s+=e.charAt(i);n[n.length]={t:"D",v:s};break;case" ":n[n.length]={t:o,v:o},++i;break;case"$":n[n.length]={t:"t",v:"$"},++i;break;default:if(",$-+/():!^&'~{}<>=€acfijklopqrtuvwxzP".indexOf(o)===-1)throw new Error("unrecognized character "+o+" in "+e);n[n.length]={t:"t",v:o},++i;break}var b=0,T=0,S;for(i=n.length-1,c="t";i>=0;--i)switch(n[i].t){case"h":case"H":n[i].t=h,c="h",b<1&&(b=1);break;case"s":(S=n[i].v.match(/\.0+$/))&&(T=Math.max(T,S[0].length-1)),b<3&&(b=3);case"d":case"y":case"M":case"e":c=n[i].t;break;case"m":c==="s"&&(n[i].t="M",b<2&&(b=2));break;case"X":break;case"Z":b<1&&n[i].v.match(/[Hh]/)&&(b=1),b<2&&n[i].v.match(/[Mm]/)&&(b=2),b<3&&n[i].v.match(/[Ss]/)&&(b=3)}switch(b){case 0:break;case 1:l.u>=.5&&(l.u=0,++l.S),l.S>=60&&(l.S=0,++l.M),l.M>=60&&(l.M=0,++l.H);break;case 2:l.u>=.5&&(l.u=0,++l.S),l.S>=60&&(l.S=0,++l.M);break}var g="",x;for(i=0;i<n.length;++i)switch(n[i].t){case"t":case"T":case" ":case"D":break;case"X":n[i].v="",n[i].t=";";break;case"d":case"m":case"y":case"h":case"H":case"M":case"s":case"e":case"b":case"Z":n[i].v=ql(n[i].t.charCodeAt(0),n[i].v,l,T),n[i].t="t";break;case"n":case"?":for(x=i+1;n[x]!=null&&((o=n[x].t)==="?"||o==="D"||(o===" "||o==="t")&&n[x+1]!=null&&(n[x+1].t==="?"||n[x+1].t==="t"&&n[x+1].v==="/")||n[i].t==="("&&(o===" "||o==="n"||o===")")||o==="t"&&(n[x].v==="/"||n[x].v===" "&&n[x+1]!=null&&n[x+1].t=="?"));)n[i].v+=n[x].v,n[x]={v:"",t:";"},++x;g+=n[i].v,i=x-1;break;case"G":n[i].t="t",n[i].v=Nt(t,r);break}var A="",P,R;if(g.length>0){g.charCodeAt(0)==40?(P=t<0&&g.charCodeAt(0)===45?-t:t,R=st("n",g,P)):(P=t<0&&a>1?-t:t,R=st("n",g,P),P<0&&n[0]&&n[0].t=="t"&&(R=R.substr(1),n[0].v="-"+n[0].v)),x=R.length-1;var U=n.length;for(i=0;i<n.length;++i)if(n[i]!=null&&n[i].t!="t"&&n[i].v.indexOf(".")>-1){U=i;break}var I=n.length;if(U===n.length&&R.indexOf("E")===-1){for(i=n.length-1;i>=0;--i)n[i]==null||"n?".indexOf(n[i].t)===-1||(x>=n[i].v.length-1?(x-=n[i].v.length,n[i].v=R.substr(x+1,n[i].v.length)):x<0?n[i].v="":(n[i].v=R.substr(0,x+1),x=-1),n[i].t="t",I=i);x>=0&&I<n.length&&(n[I].v=R.substr(0,x+1)+n[I].v)}else if(U!==n.length&&R.indexOf("E")===-1){for(x=R.indexOf(".")-1,i=U;i>=0;--i)if(!(n[i]==null||"n?".indexOf(n[i].t)===-1)){for(f=n[i].v.indexOf(".")>-1&&i===U?n[i].v.indexOf(".")-1:n[i].v.length-1,A=n[i].v.substr(f+1);f>=0;--f)x>=0&&(n[i].v.charAt(f)==="0"||n[i].v.charAt(f)==="#")&&(A=R.charAt(x--)+A);n[i].v=A,n[i].t="t",I=i}for(x>=0&&I<n.length&&(n[I].v=R.substr(0,x+1)+n[I].v),x=R.indexOf(".")+1,i=U;i<n.length;++i)if(!(n[i]==null||"n?(".indexOf(n[i].t)===-1&&i!==U)){for(f=n[i].v.indexOf(".")>-1&&i===U?n[i].v.indexOf(".")+1:0,A=n[i].v.substr(0,f);f<n[i].v.length;++f)x<R.length&&(A+=R.charAt(x++));n[i].v=A,n[i].t="t",I=i}}}for(i=0;i<n.length;++i)n[i]!=null&&"n?".indexOf(n[i].t)>-1&&(P=a>1&&t<0&&i>0&&n[i-1].v==="-"?-t:t,n[i].v=st(n[i].t,n[i].v,P),n[i].t="t");var G="";for(i=0;i!==n.length;++i)n[i]!=null&&(G+=n[i].v);return G}var si=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function ii(e,t){if(t==null)return!1;var r=parseFloat(t[2]);switch(t[1]){case"=":if(e==r)return!0;break;case">":if(e>r)return!0;break;case"<":if(e<r)return!0;break;case"<>":if(e!=r)return!0;break;case">=":if(e>=r)return!0;break;case"<=":if(e<=r)return!0;break}return!1}function ff(e,t){var r=cf(e),a=r.length,n=r[a-1].indexOf("@");if(a<4&&n>-1&&--a,r.length>4)throw new Error("cannot find right format for |"+r.join("|")+"|");if(typeof t!="number")return[4,r.length===4||n>-1?r[r.length-1]:"@"];switch(r.length){case 1:r=n>-1?["General","General","General",r[0]]:[r[0],r[0],r[0],"@"];break;case 2:r=n>-1?[r[0],r[0],r[0],r[1]]:[r[0],r[1],r[0],"@"];break;case 3:r=n>-1?[r[0],r[1],r[0],r[2]]:[r[0],r[1],r[2],"@"];break}var s=t>0?r[0]:t<0?r[1]:r[2];if(r[0].indexOf("[")===-1&&r[1].indexOf("[")===-1)return[a,s];if(r[0].match(/\[[=<>]/)!=null||r[1].match(/\[[=<>]/)!=null){var i=r[0].match(si),o=r[1].match(si);return ii(t,i)?[a,r[0]]:ii(t,o)?[a,r[1]]:[a,r[i!=null&&o!=null?2:1]]}return[a,s]}function zr(e,t,r){r==null&&(r={});var a="";switch(typeof e){case"string":e=="m/d/yy"&&r.dateNF?a=r.dateNF:a=e;break;case"number":e==14&&r.dateNF?a=r.dateNF:a=(r.table!=null?r.table:be)[e],a==null&&(a=r.table&&r.table[ti[e]]||be[ti[e]]),a==null&&(a=jl[e]||"General");break}if(sn(a,0))return Nt(t,r);t instanceof Date&&(t=lo(t,r.date1904));var n=ff(a,t);if(sn(n[1]))return Nt(t,r);if(t===!0)t="TRUE";else if(t===!1)t="FALSE";else if(t===""||t==null)return"";return lf(n[1],t,r,n[0])}function it(e,t){if(typeof t!="number"){t=+t||-1;for(var r=0;r<392;++r){if(be[r]==null){t<0&&(t=r);continue}if(be[r]==e){t=r;break}}t<0&&(t=391)}return be[t]=e,t}function gn(e){for(var t=0;t!=392;++t)e[t]!==void 0&&it(e[t],t)}function sa(){be=Vl()}var hf={_table:be},uf={5:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',23:"General",24:"General",25:"General",26:"General",27:"m/d/yy",28:"m/d/yy",29:"m/d/yy",30:"m/d/yy",31:"m/d/yy",32:"h:mm:ss",33:"h:mm:ss",34:"h:mm:ss",35:"h:mm:ss",36:"m/d/yy",41:'_(* #,##0_);_(* (#,##0);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* (#,##0);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* (#,##0.00);_("$"* "-"??_);_(@_)',50:"m/d/yy",51:"m/d/yy",52:"m/d/yy",53:"m/d/yy",54:"m/d/yy",55:"m/d/yy",56:"m/d/yy",57:"m/d/yy",58:"m/d/yy",59:"0",60:"0.00",61:"#,##0",62:"#,##0.00",63:'"$"#,##0_);\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',67:"0%",68:"0.00%",69:"# ?/?",70:"# ??/??",71:"m/d/yy",72:"m/d/yy",73:"d-mmm-yy",74:"d-mmm",75:"mmm-yy",76:"h:mm",77:"h:mm:ss",78:"m/d/yy h:mm",79:"mm:ss",80:"[h]:mm:ss",81:"mmss.0"},wo=/[dD]+|[mM]+|[yYeE]+|[Hh]+|[Ss]+/g;function df(e){var t=typeof e=="number"?be[e]:e;return t=t.replace(wo,"(\\d+)"),new RegExp("^"+t+"$")}function pf(e,t,r){var a=-1,n=-1,s=-1,i=-1,o=-1,c=-1;(t.match(wo)||[]).forEach(function(p,h){var d=parseInt(r[h+1],10);switch(p.toLowerCase().charAt(0)){case"y":a=d;break;case"d":s=d;break;case"h":i=d;break;case"s":c=d;break;case"m":i>=0?o=d:n=d;break}}),c>=0&&o==-1&&n>=0&&(o=n,n=-1);var l=(""+(a>=0?a:new Date().getFullYear())).slice(-4)+"-"+("00"+(n>=1?n:1)).slice(-2)+"-"+("00"+(s>=1?s:1)).slice(-2);l.length==7&&(l="0"+l),l.length==8&&(l="20"+l);var f=("00"+(i>=0?i:0)).slice(-2)+":"+("00"+(o>=0?o:0)).slice(-2)+":"+("00"+(c>=0?c:0)).slice(-2);return i==-1&&o==-1&&c==-1?l:a==-1&&n==-1&&s==-1?f:l+"T"+f}var mf=function(){var e={};e.version="1.2.0";function t(){for(var R=0,U=new Array(256),I=0;I!=256;++I)R=I,R=R&1?-306674912^R>>>1:R>>>1,R=R&1?-306674912^R>>>1:R>>>1,R=R&1?-306674912^R>>>1:R>>>1,R=R&1?-306674912^R>>>1:R>>>1,R=R&1?-306674912^R>>>1:R>>>1,R=R&1?-306674912^R>>>1:R>>>1,R=R&1?-306674912^R>>>1:R>>>1,R=R&1?-306674912^R>>>1:R>>>1,U[I]=R;return typeof Int32Array<"u"?new Int32Array(U):U}var r=t();function a(R){var U=0,I=0,G=0,W=typeof Int32Array<"u"?new Int32Array(4096):new Array(4096);for(G=0;G!=256;++G)W[G]=R[G];for(G=0;G!=256;++G)for(I=R[G],U=256+G;U<4096;U+=256)I=W[U]=I>>>8^R[I&255];var D=[];for(G=1;G!=16;++G)D[G-1]=typeof Int32Array<"u"?W.subarray(G*256,G*256+256):W.slice(G*256,G*256+256);return D}var n=a(r),s=n[0],i=n[1],o=n[2],c=n[3],l=n[4],f=n[5],p=n[6],h=n[7],d=n[8],m=n[9],u=n[10],b=n[11],T=n[12],S=n[13],g=n[14];function x(R,U){for(var I=U^-1,G=0,W=R.length;G<W;)I=I>>>8^r[(I^R.charCodeAt(G++))&255];return~I}function A(R,U){for(var I=U^-1,G=R.length-15,W=0;W<G;)I=g[R[W++]^I&255]^S[R[W++]^I>>8&255]^T[R[W++]^I>>16&255]^b[R[W++]^I>>>24]^u[R[W++]]^m[R[W++]]^d[R[W++]]^h[R[W++]]^p[R[W++]]^f[R[W++]]^l[R[W++]]^c[R[W++]]^o[R[W++]]^i[R[W++]]^s[R[W++]]^r[R[W++]];for(G+=15;W<G;)I=I>>>8^r[(I^R[W++])&255];return~I}function P(R,U){for(var I=U^-1,G=0,W=R.length,D=0,te=0;G<W;)D=R.charCodeAt(G++),D<128?I=I>>>8^r[(I^D)&255]:D<2048?(I=I>>>8^r[(I^(192|D>>6&31))&255],I=I>>>8^r[(I^(128|D&63))&255]):D>=55296&&D<57344?(D=(D&1023)+64,te=R.charCodeAt(G++)&1023,I=I>>>8^r[(I^(240|D>>8&7))&255],I=I>>>8^r[(I^(128|D>>2&63))&255],I=I>>>8^r[(I^(128|te>>6&15|(D&3)<<4))&255],I=I>>>8^r[(I^(128|te&63))&255]):(I=I>>>8^r[(I^(224|D>>12&15))&255],I=I>>>8^r[(I^(128|D>>6&63))&255],I=I>>>8^r[(I^(128|D&63))&255]);return~I}return e.table=r,e.bstr=x,e.buf=A,e.str=P,e}(),pe=function(){var e={};e.version="1.2.1";function t(v,E){for(var w=v.split("/"),k=E.split("/"),y=0,_=0,M=Math.min(w.length,k.length);y<M;++y){if(_=w[y].length-k[y].length)return _;if(w[y]!=k[y])return w[y]<k[y]?-1:1}return w.length-k.length}function r(v){if(v.charAt(v.length-1)=="/")return v.slice(0,-1).indexOf("/")===-1?v:r(v.slice(0,-1));var E=v.lastIndexOf("/");return E===-1?v:v.slice(0,E+1)}function a(v){if(v.charAt(v.length-1)=="/")return a(v.slice(0,-1));var E=v.lastIndexOf("/");return E===-1?v:v.slice(E+1)}function n(v,E){typeof E=="string"&&(E=new Date(E));var w=E.getHours();w=w<<6|E.getMinutes(),w=w<<5|E.getSeconds()>>>1,v.write_shift(2,w);var k=E.getFullYear()-1980;k=k<<4|E.getMonth()+1,k=k<<5|E.getDate(),v.write_shift(2,k)}function s(v){var E=v.read_shift(2)&65535,w=v.read_shift(2)&65535,k=new Date,y=w&31;w>>>=5;var _=w&15;w>>>=4,k.setMilliseconds(0),k.setFullYear(w+1980),k.setMonth(_-1),k.setDate(y);var M=E&31;E>>>=5;var z=E&63;return E>>>=6,k.setHours(E),k.setMinutes(z),k.setSeconds(M<<1),k}function i(v){lr(v,0);for(var E={},w=0;v.l<=v.length-4;){var k=v.read_shift(2),y=v.read_shift(2),_=v.l+y,M={};switch(k){case 21589:w=v.read_shift(1),w&1&&(M.mtime=v.read_shift(4)),y>5&&(w&2&&(M.atime=v.read_shift(4)),w&4&&(M.ctime=v.read_shift(4))),M.mtime&&(M.mt=new Date(M.mtime*1e3));break}v.l=_,E[k]=M}return E}var o;function c(){return o||(o={})}function l(v,E){if(v[0]==80&&v[1]==75)return Js(v,E);if((v[0]|32)==109&&(v[1]|32)==105)return _l(v,E);if(v.length<512)throw new Error("CFB file size "+v.length+" < 512");var w=3,k=512,y=0,_=0,M=0,z=0,B=0,H=[],V=v.slice(0,512);lr(V,0);var Z=f(V);switch(w=Z[0],w){case 3:k=512;break;case 4:k=4096;break;case 0:if(Z[1]==0)return Js(v,E);default:throw new Error("Major Version: Expected 3 or 4 saw "+w)}k!==512&&(V=v.slice(0,k),lr(V,28));var oe=v.slice(0,k);p(V,w);var ce=V.read_shift(4,"i");if(w===3&&ce!==0)throw new Error("# Directory Sectors: Expected 0 saw "+ce);V.l+=4,M=V.read_shift(4,"i"),V.l+=4,V.chk("00100000","Mini Stream Cutoff Size: "),z=V.read_shift(4,"i"),y=V.read_shift(4,"i"),B=V.read_shift(4,"i"),_=V.read_shift(4,"i");for(var ie=-1,he=0;he<109&&(ie=V.read_shift(4,"i"),!(ie<0));++he)H[he]=ie;var Ae=h(v,k);u(B,_,Ae,k,H);var He=T(Ae,M,H,k);He[M].name="!Directory",y>0&&z!==D&&(He[z].name="!MiniFAT"),He[H[0]].name="!FAT",He.fat_addrs=H,He.ssz=k;var Ge={},Ar=[],la=[],fa=[];S(M,He,Ae,Ar,y,Ge,la,z),d(la,fa,Ar),Ar.shift();var ha={FileIndex:la,FullPaths:fa};return E&&E.raw&&(ha.raw={header:oe,sectors:Ae}),ha}function f(v){if(v[v.l]==80&&v[v.l+1]==75)return[0,0];v.chk(te,"Header Signature: "),v.l+=16;var E=v.read_shift(2,"u");return[v.read_shift(2,"u"),E]}function p(v,E){var w=9;switch(v.l+=2,w=v.read_shift(2)){case 9:if(E!=3)throw new Error("Sector Shift: Expected 9 saw "+w);break;case 12:if(E!=4)throw new Error("Sector Shift: Expected 12 saw "+w);break;default:throw new Error("Sector Shift: Expected 9 or 12 saw "+w)}v.chk("0600","Mini Sector Shift: "),v.chk("000000000000","Reserved: ")}function h(v,E){for(var w=Math.ceil(v.length/E)-1,k=[],y=1;y<w;++y)k[y-1]=v.slice(y*E,(y+1)*E);return k[w-1]=v.slice(w*E),k}function d(v,E,w){for(var k=0,y=0,_=0,M=0,z=0,B=w.length,H=[],V=[];k<B;++k)H[k]=V[k]=k,E[k]=w[k];for(;z<V.length;++z)k=V[z],y=v[k].L,_=v[k].R,M=v[k].C,H[k]===k&&(y!==-1&&H[y]!==y&&(H[k]=H[y]),_!==-1&&H[_]!==_&&(H[k]=H[_])),M!==-1&&(H[M]=k),y!==-1&&k!=H[k]&&(H[y]=H[k],V.lastIndexOf(y)<z&&V.push(y)),_!==-1&&k!=H[k]&&(H[_]=H[k],V.lastIndexOf(_)<z&&V.push(_));for(k=1;k<B;++k)H[k]===k&&(_!==-1&&H[_]!==_?H[k]=H[_]:y!==-1&&H[y]!==y&&(H[k]=H[y]));for(k=1;k<B;++k)if(v[k].type!==0){if(z=k,z!=H[z])do z=H[z],E[k]=E[z]+"/"+E[k];while(z!==0&&H[z]!==-1&&z!=H[z]);H[k]=-1}for(E[0]+="/",k=1;k<B;++k)v[k].type!==2&&(E[k]+="/")}function m(v,E,w){for(var k=v.start,y=v.size,_=[],M=k;w&&y>0&&M>=0;)_.push(E.slice(M*W,M*W+W)),y-=W,M=At(w,M*4);return _.length===0?j(0):ir(_).slice(0,v.size)}function u(v,E,w,k,y){var _=D;if(v===D){if(E!==0)throw new Error("DIFAT chain shorter than expected")}else if(v!==-1){var M=w[v],z=(k>>>2)-1;if(!M)return;for(var B=0;B<z&&(_=At(M,B*4))!==D;++B)y.push(_);u(At(M,k-4),E-1,w,k,y)}}function b(v,E,w,k,y){var _=[],M=[];y||(y=[]);var z=k-1,B=0,H=0;for(B=E;B>=0;){y[B]=!0,_[_.length]=B,M.push(v[B]);var V=w[Math.floor(B*4/k)];if(H=B*4&z,k<4+H)throw new Error("FAT boundary crossed: "+B+" 4 "+k);if(!v[V])break;B=At(v[V],H)}return{nodes:_,data:gi([M])}}function T(v,E,w,k){var y=v.length,_=[],M=[],z=[],B=[],H=k-1,V=0,Z=0,oe=0,ce=0;for(V=0;V<y;++V)if(z=[],oe=V+E,oe>=y&&(oe-=y),!M[oe]){B=[];var ie=[];for(Z=oe;Z>=0;){ie[Z]=!0,M[Z]=!0,z[z.length]=Z,B.push(v[Z]);var he=w[Math.floor(Z*4/k)];if(ce=Z*4&H,k<4+ce)throw new Error("FAT boundary crossed: "+Z+" 4 "+k);if(!v[he]||(Z=At(v[he],ce),ie[Z]))break}_[oe]={nodes:z,data:gi([B])}}return _}function S(v,E,w,k,y,_,M,z){for(var B=0,H=k.length?2:0,V=E[v].data,Z=0,oe=0,ce;Z<V.length;Z+=128){var ie=V.slice(Z,Z+128);lr(ie,64),oe=ie.read_shift(2),ce=gs(ie,0,oe-H),k.push(ce);var he={name:ce,type:ie.read_shift(1),color:ie.read_shift(1),L:ie.read_shift(4,"i"),R:ie.read_shift(4,"i"),C:ie.read_shift(4,"i"),clsid:ie.read_shift(16),state:ie.read_shift(4,"i"),start:0,size:0},Ae=ie.read_shift(2)+ie.read_shift(2)+ie.read_shift(2)+ie.read_shift(2);Ae!==0&&(he.ct=g(ie,ie.l-8));var He=ie.read_shift(2)+ie.read_shift(2)+ie.read_shift(2)+ie.read_shift(2);He!==0&&(he.mt=g(ie,ie.l-8)),he.start=ie.read_shift(4,"i"),he.size=ie.read_shift(4,"i"),he.size<0&&he.start<0&&(he.size=he.type=0,he.start=D,he.name=""),he.type===5?(B=he.start,y>0&&B!==D&&(E[B].name="!StreamData")):he.size>=4096?(he.storage="fat",E[he.start]===void 0&&(E[he.start]=b(w,he.start,E.fat_addrs,E.ssz)),E[he.start].name=he.name,he.content=E[he.start].data.slice(0,he.size)):(he.storage="minifat",he.size<0?he.size=0:B!==D&&he.start!==D&&E[B]&&(he.content=m(he,E[B].data,(E[z]||{}).data))),he.content&&lr(he.content,0),_[ce]=he,M.push(he)}}function g(v,E){return new Date((Rr(v,E+4)/1e7*Math.pow(2,32)+Rr(v,E)/1e7-11644473600)*1e3)}function x(v,E){return c(),l(o.readFileSync(v),E)}function A(v,E){var w=E&&E.type;switch(w||Se&&Buffer.isBuffer(v)&&(w="buffer"),w||"base64"){case"file":return x(v,E);case"base64":return l(Or(Ir(v)),E);case"binary":return l(Or(v),E)}return l(v,E)}function P(v,E){var w=E||{},k=w.root||"Root Entry";if(v.FullPaths||(v.FullPaths=[]),v.FileIndex||(v.FileIndex=[]),v.FullPaths.length!==v.FileIndex.length)throw new Error("inconsistent CFB structure");v.FullPaths.length===0&&(v.FullPaths[0]=k+"/",v.FileIndex[0]={name:k,type:5}),w.CLSID&&(v.FileIndex[0].clsid=w.CLSID),R(v)}function R(v){var E="Sh33tJ5";if(!pe.find(v,"/"+E)){var w=j(4);w[0]=55,w[1]=w[3]=50,w[2]=54,v.FileIndex.push({name:E,type:2,content:w,size:4,L:69,R:69,C:69}),v.FullPaths.push(v.FullPaths[0]+E),U(v)}}function U(v,E){P(v);for(var w=!1,k=!1,y=v.FullPaths.length-1;y>=0;--y){var _=v.FileIndex[y];switch(_.type){case 0:k?w=!0:(v.FileIndex.pop(),v.FullPaths.pop());break;case 1:case 2:case 5:k=!0,isNaN(_.R*_.L*_.C)&&(w=!0),_.R>-1&&_.L>-1&&_.R==_.L&&(w=!0);break;default:w=!0;break}}if(!(!w&&!E)){var M=new Date(1987,1,19),z=0,B=Object.create?Object.create(null):{},H=[];for(y=0;y<v.FullPaths.length;++y)B[v.FullPaths[y]]=!0,v.FileIndex[y].type!==0&&H.push([v.FullPaths[y],v.FileIndex[y]]);for(y=0;y<H.length;++y){var V=r(H[y][0]);k=B[V],k||(H.push([V,{name:a(V).replace("/",""),type:1,clsid:se,ct:M,mt:M,content:null}]),B[V]=!0)}for(H.sort(function(ce,ie){return t(ce[0],ie[0])}),v.FullPaths=[],v.FileIndex=[],y=0;y<H.length;++y)v.FullPaths[y]=H[y][0],v.FileIndex[y]=H[y][1];for(y=0;y<H.length;++y){var Z=v.FileIndex[y],oe=v.FullPaths[y];if(Z.name=a(oe).replace("/",""),Z.L=Z.R=Z.C=-(Z.color=1),Z.size=Z.content?Z.content.length:0,Z.start=0,Z.clsid=Z.clsid||se,y===0)Z.C=H.length>1?1:-1,Z.size=0,Z.type=5;else if(oe.slice(-1)=="/"){for(z=y+1;z<H.length&&r(v.FullPaths[z])!=oe;++z);for(Z.C=z>=H.length?-1:z,z=y+1;z<H.length&&r(v.FullPaths[z])!=r(oe);++z);Z.R=z>=H.length?-1:z,Z.type=1}else r(v.FullPaths[y+1]||"")==r(oe)&&(Z.R=y+1),Z.type=2}}}function I(v,E){var w=E||{};if(w.fileType=="mad")return xl(v,w);switch(U(v),w.fileType){case"zip":return wl(v,w)}var k=function(ce){for(var ie=0,he=0,Ae=0;Ae<ce.FileIndex.length;++Ae){var He=ce.FileIndex[Ae];if(He.content){var Ge=He.content.length;Ge>0&&(Ge<4096?ie+=Ge+63>>6:he+=Ge+511>>9)}}for(var Ar=ce.FullPaths.length+3>>2,la=ie+7>>3,fa=ie+127>>7,ha=la+he+Ar+fa,xt=ha+127>>7,Cn=xt<=109?0:Math.ceil((xt-109)/127);ha+xt+Cn+127>>7>xt;)Cn=++xt<=109?0:Math.ceil((xt-109)/127);var at=[1,Cn,xt,fa,Ar,he,ie,0];return ce.FileIndex[0].size=ie<<6,at[7]=(ce.FileIndex[0].start=at[0]+at[1]+at[2]+at[3]+at[4]+at[5])+(at[6]+7>>3),at}(v),y=j(k[7]<<9),_=0,M=0;{for(_=0;_<8;++_)y.write_shift(1,fe[_]);for(_=0;_<8;++_)y.write_shift(2,0);for(y.write_shift(2,62),y.write_shift(2,3),y.write_shift(2,65534),y.write_shift(2,9),y.write_shift(2,6),_=0;_<3;++_)y.write_shift(2,0);for(y.write_shift(4,0),y.write_shift(4,k[2]),y.write_shift(4,k[0]+k[1]+k[2]+k[3]-1),y.write_shift(4,0),y.write_shift(4,4096),y.write_shift(4,k[3]?k[0]+k[1]+k[2]-1:D),y.write_shift(4,k[3]),y.write_shift(-4,k[1]?k[0]-1:D),y.write_shift(4,k[1]),_=0;_<109;++_)y.write_shift(-4,_<k[2]?k[1]+_:-1)}if(k[1])for(M=0;M<k[1];++M){for(;_<236+M*127;++_)y.write_shift(-4,_<k[2]?k[1]+_:-1);y.write_shift(-4,M===k[1]-1?D:M+1)}var z=function(ce){for(M+=ce;_<M-1;++_)y.write_shift(-4,_+1);ce&&(++_,y.write_shift(-4,D))};for(M=_=0,M+=k[1];_<M;++_)y.write_shift(-4,de.DIFSECT);for(M+=k[2];_<M;++_)y.write_shift(-4,de.FATSECT);z(k[3]),z(k[4]);for(var B=0,H=0,V=v.FileIndex[0];B<v.FileIndex.length;++B)V=v.FileIndex[B],V.content&&(H=V.content.length,!(H<4096)&&(V.start=M,z(H+511>>9)));for(z(k[6]+7>>3);y.l&511;)y.write_shift(-4,de.ENDOFCHAIN);for(M=_=0,B=0;B<v.FileIndex.length;++B)V=v.FileIndex[B],V.content&&(H=V.content.length,!(!H||H>=4096)&&(V.start=M,z(H+63>>6)));for(;y.l&511;)y.write_shift(-4,de.ENDOFCHAIN);for(_=0;_<k[4]<<2;++_){var Z=v.FullPaths[_];if(!Z||Z.length===0){for(B=0;B<17;++B)y.write_shift(4,0);for(B=0;B<3;++B)y.write_shift(4,-1);for(B=0;B<12;++B)y.write_shift(4,0);continue}V=v.FileIndex[_],_===0&&(V.start=V.size?V.start-1:D);var oe=_===0&&w.root||V.name;if(H=2*(oe.length+1),y.write_shift(64,oe,"utf16le"),y.write_shift(2,H),y.write_shift(1,V.type),y.write_shift(1,V.color),y.write_shift(-4,V.L),y.write_shift(-4,V.R),y.write_shift(-4,V.C),V.clsid)y.write_shift(16,V.clsid,"hex");else for(B=0;B<4;++B)y.write_shift(4,0);y.write_shift(4,V.state||0),y.write_shift(4,0),y.write_shift(4,0),y.write_shift(4,0),y.write_shift(4,0),y.write_shift(4,V.start),y.write_shift(4,V.size),y.write_shift(4,0)}for(_=1;_<v.FileIndex.length;++_)if(V=v.FileIndex[_],V.size>=4096)if(y.l=V.start+1<<9,Se&&Buffer.isBuffer(V.content))V.content.copy(y,y.l,0,V.size),y.l+=V.size+511&-512;else{for(B=0;B<V.size;++B)y.write_shift(1,V.content[B]);for(;B&511;++B)y.write_shift(1,0)}for(_=1;_<v.FileIndex.length;++_)if(V=v.FileIndex[_],V.size>0&&V.size<4096)if(Se&&Buffer.isBuffer(V.content))V.content.copy(y,y.l,0,V.size),y.l+=V.size+63&-64;else{for(B=0;B<V.size;++B)y.write_shift(1,V.content[B]);for(;B&63;++B)y.write_shift(1,0)}if(Se)y.l=y.length;else for(;y.l<y.length;)y.write_shift(1,0);return y}function G(v,E){var w=v.FullPaths.map(function(B){return B.toUpperCase()}),k=w.map(function(B){var H=B.split("/");return H[H.length-(B.slice(-1)=="/"?2:1)]}),y=!1;E.charCodeAt(0)===47?(y=!0,E=w[0].slice(0,-1)+E):y=E.indexOf("/")!==-1;var _=E.toUpperCase(),M=y===!0?w.indexOf(_):k.indexOf(_);if(M!==-1)return v.FileIndex[M];var z=!_.match(ma);for(_=_.replace(yr,""),z&&(_=_.replace(ma,"!")),M=0;M<w.length;++M)if((z?w[M].replace(ma,"!"):w[M]).replace(yr,"")==_||(z?k[M].replace(ma,"!"):k[M]).replace(yr,"")==_)return v.FileIndex[M];return null}var W=64,D=-2,te="d0cf11e0a1b11ae1",fe=[208,207,17,224,161,177,26,225],se="00000000000000000000000000000000",de={MAXREGSECT:-6,DIFSECT:-4,FATSECT:-3,ENDOFCHAIN:D,FREESECT:-1,HEADER_SIGNATURE:te,HEADER_MINOR_VERSION:"3e00",MAXREGSID:-6,NOSTREAM:-1,HEADER_CLSID:se,EntryTypes:["unknown","storage","stream","lockbytes","property","root"]};function ue(v,E,w){c();var k=I(v,w);o.writeFileSync(E,k)}function K(v){for(var E=new Array(v.length),w=0;w<v.length;++w)E[w]=String.fromCharCode(v[w]);return E.join("")}function ee(v,E){var w=I(v,E);switch(E&&E.type||"buffer"){case"file":return c(),o.writeFileSync(E.filename,w),w;case"binary":return typeof w=="string"?w:K(w);case"base64":return _a(typeof w=="string"?w:K(w));case"buffer":if(Se)return Buffer.isBuffer(w)?w:lt(w);case"array":return typeof w=="string"?Or(w):w}return w}var me;function Te(v){try{var E=v.InflateRaw,w=new E;if(w._processChunk(new Uint8Array([3,0]),w._finishFlushFlag),w.bytesRead)me=v;else throw new Error("zlib does not expose bytesRead")}catch(k){console.error("cannot use native zlib: "+(k.message||k))}}function C(v,E){if(!me)return Ys(v,E);var w=me.InflateRaw,k=new w,y=k._processChunk(v.slice(v.l),k._finishFlushFlag);return v.l+=k.bytesRead,y}function L(v){return me?me.deflateRawSync(v):ye(v)}var F=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],N=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258],X=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577];function ne(v){var E=(v<<1|v<<11)&139536|(v<<5|v<<15)&558144;return(E>>16|E>>8|E)&255}for(var J=typeof Uint8Array<"u",Q=J?new Uint8Array(256):[],q=0;q<256;++q)Q[q]=ne(q);function Ee(v,E){var w=Q[v&255];return E<=8?w>>>8-E:(w=w<<8|Q[v>>8&255],E<=16?w>>>16-E:(w=w<<8|Q[v>>16&255],w>>>24-E))}function O(v,E){var w=E&7,k=E>>>3;return(v[k]|(w<=6?0:v[k+1]<<8))>>>w&3}function Me(v,E){var w=E&7,k=E>>>3;return(v[k]|(w<=5?0:v[k+1]<<8))>>>w&7}function Oe(v,E){var w=E&7,k=E>>>3;return(v[k]|(w<=4?0:v[k+1]<<8))>>>w&15}function De(v,E){var w=E&7,k=E>>>3;return(v[k]|(w<=3?0:v[k+1]<<8))>>>w&31}function xe(v,E){var w=E&7,k=E>>>3;return(v[k]|(w<=1?0:v[k+1]<<8))>>>w&127}function le(v,E,w){var k=E&7,y=E>>>3,_=(1<<w)-1,M=v[y]>>>k;return w<8-k||(M|=v[y+1]<<8-k,w<16-k)||(M|=v[y+2]<<16-k,w<24-k)||(M|=v[y+3]<<24-k),M&_}function rr(v,E,w){var k=E&7,y=E>>>3;return k<=5?v[y]|=(w&7)<<k:(v[y]|=w<<k&255,v[y+1]=(w&7)>>8-k),E+3}function Gr(v,E,w){var k=E&7,y=E>>>3;return w=(w&1)<<k,v[y]|=w,E+1}function wr(v,E,w){var k=E&7,y=E>>>3;return w<<=k,v[y]|=w&255,w>>>=8,v[y+1]=w,E+8}function ca(v,E,w){var k=E&7,y=E>>>3;return w<<=k,v[y]|=w&255,w>>>=8,v[y+1]=w&255,v[y+2]=w>>>8,E+16}function Gt(v,E){var w=v.length,k=2*w>E?2*w:E+5,y=0;if(w>=E)return v;if(Se){var _=Qs(k);if(v.copy)v.copy(_);else for(;y<v.length;++y)_[y]=v[y];return _}else if(J){var M=new Uint8Array(k);if(M.set)M.set(v);else for(;y<w;++y)M[y]=v[y];return M}return v.length=k,v}function kr(v){for(var E=new Array(v),w=0;w<v;++w)E[w]=0;return E}function St(v,E,w){var k=1,y=0,_=0,M=0,z=0,B=v.length,H=J?new Uint16Array(32):kr(32);for(_=0;_<32;++_)H[_]=0;for(_=B;_<w;++_)v[_]=0;B=v.length;var V=J?new Uint16Array(B):kr(B);for(_=0;_<B;++_)H[y=v[_]]++,k<y&&(k=y),V[_]=0;for(H[0]=0,_=1;_<=k;++_)H[_+16]=z=z+H[_-1]<<1;for(_=0;_<B;++_)z=v[_],z!=0&&(V[_]=H[z+16]++);var Z=0;for(_=0;_<B;++_)if(Z=v[_],Z!=0)for(z=Ee(V[_],k)>>k-Z,M=(1<<k+4-Z)-1;M>=0;--M)E[z|M<<Z]=Z&15|_<<4;return k}var ut=J?new Uint16Array(512):kr(512),dt=J?new Uint16Array(32):kr(32);if(!J){for(var rt=0;rt<512;++rt)ut[rt]=0;for(rt=0;rt<32;++rt)dt[rt]=0}(function(){for(var v=[],E=0;E<32;E++)v.push(5);St(v,dt,32);var w=[];for(E=0;E<=143;E++)w.push(8);for(;E<=255;E++)w.push(9);for(;E<=279;E++)w.push(7);for(;E<=287;E++)w.push(8);St(w,ut,288)})();var tt=function(){for(var v=J?new Uint8Array(32768):[],E=0,w=0;E<X.length-1;++E)for(;w<X[E+1];++w)v[w]=E;for(;w<32768;++w)v[w]=29;var k=J?new Uint8Array(259):[];for(E=0,w=0;E<N.length-1;++E)for(;w<N[E+1];++w)k[w]=E;function y(M,z){for(var B=0;B<M.length;){var H=Math.min(65535,M.length-B),V=B+H==M.length;for(z.write_shift(1,+V),z.write_shift(2,H),z.write_shift(2,~H&65535);H-- >0;)z[z.l++]=M[B++]}return z.l}function _(M,z){for(var B=0,H=0,V=J?new Uint16Array(32768):[];H<M.length;){var Z=Math.min(65535,M.length-H);if(Z<10){for(B=rr(z,B,+(H+Z==M.length)),B&7&&(B+=8-(B&7)),z.l=B/8|0,z.write_shift(2,Z),z.write_shift(2,~Z&65535);Z-- >0;)z[z.l++]=M[H++];B=z.l*8;continue}B=rr(z,B,+(H+Z==M.length)+2);for(var oe=0;Z-- >0;){var ce=M[H];oe=(oe<<5^ce)&32767;var ie=-1,he=0;if((ie=V[oe])&&(ie|=H&-32768,ie>H&&(ie-=32768),ie<H))for(;M[ie+he]==M[H+he]&&he<250;)++he;if(he>2){ce=k[he],ce<=22?B=wr(z,B,Q[ce+1]>>1)-1:(wr(z,B,3),B+=5,wr(z,B,Q[ce-23]>>5),B+=3);var Ae=ce<8?0:ce-4>>2;Ae>0&&(ca(z,B,he-N[ce]),B+=Ae),ce=v[H-ie],B=wr(z,B,Q[ce]>>3),B-=3;var He=ce<4?0:ce-2>>1;He>0&&(ca(z,B,H-ie-X[ce]),B+=He);for(var Ge=0;Ge<he;++Ge)V[oe]=H&32767,oe=(oe<<5^M[H])&32767,++H;Z-=he-1}else ce<=143?ce=ce+48:B=Gr(z,B,1),B=wr(z,B,Q[ce]),V[oe]=H&32767,++H}B=wr(z,B,0)-1}return z.l=(B+7)/8|0,z.l}return function(M,z){return M.length<8?y(M,z):_(M,z)}}();function ye(v){var E=j(50+Math.floor(v.length*1.1)),w=tt(v,E);return E.slice(0,w)}var Ue=J?new Uint16Array(32768):kr(32768),Fr=J?new Uint16Array(32768):kr(32768),tr=J?new Uint16Array(128):kr(128),_t=1,Xs=1;function bl(v,E){var w=De(v,E)+257;E+=5;var k=De(v,E)+1;E+=5;var y=Oe(v,E)+4;E+=4;for(var _=0,M=J?new Uint8Array(19):kr(19),z=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],B=1,H=J?new Uint8Array(8):kr(8),V=J?new Uint8Array(8):kr(8),Z=M.length,oe=0;oe<y;++oe)M[F[oe]]=_=Me(v,E),B<_&&(B=_),H[_]++,E+=3;var ce=0;for(H[0]=0,oe=1;oe<=B;++oe)V[oe]=ce=ce+H[oe-1]<<1;for(oe=0;oe<Z;++oe)(ce=M[oe])!=0&&(z[oe]=V[ce]++);var ie=0;for(oe=0;oe<Z;++oe)if(ie=M[oe],ie!=0){ce=Q[z[oe]]>>8-ie;for(var he=(1<<7-ie)-1;he>=0;--he)tr[ce|he<<ie]=ie&7|oe<<3}var Ae=[];for(B=1;Ae.length<w+k;)switch(ce=tr[xe(v,E)],E+=ce&7,ce>>>=3){case 16:for(_=3+O(v,E),E+=2,ce=Ae[Ae.length-1];_-- >0;)Ae.push(ce);break;case 17:for(_=3+Me(v,E),E+=3;_-- >0;)Ae.push(0);break;case 18:for(_=11+xe(v,E),E+=7;_-- >0;)Ae.push(0);break;default:Ae.push(ce),B<ce&&(B=ce);break}var He=Ae.slice(0,w),Ge=Ae.slice(w);for(oe=w;oe<286;++oe)He[oe]=0;for(oe=k;oe<30;++oe)Ge[oe]=0;return _t=St(He,Ue,286),Xs=St(Ge,Fr,30),E}function vl(v,E){if(v[0]==3&&!(v[1]&3))return[vt(E),2];for(var w=0,k=0,y=Qs(E||1<<18),_=0,M=y.length>>>0,z=0,B=0;!(k&1);){if(k=Me(v,w),w+=3,k>>>1)k>>1==1?(z=9,B=5):(w=bl(v,w),z=_t,B=Xs);else{w&7&&(w+=8-(w&7));var H=v[w>>>3]|v[(w>>>3)+1]<<8;if(w+=32,H>0)for(!E&&M<_+H&&(y=Gt(y,_+H),M=y.length);H-- >0;)y[_++]=v[w>>>3],w+=8;continue}for(;;){!E&&M<_+32767&&(y=Gt(y,_+32767),M=y.length);var V=le(v,w,z),Z=k>>>1==1?ut[V]:Ue[V];if(w+=Z&15,Z>>>=4,!(Z>>>8&255))y[_++]=Z;else{if(Z==256)break;Z-=257;var oe=Z<8?0:Z-4>>2;oe>5&&(oe=0);var ce=_+N[Z];oe>0&&(ce+=le(v,w,oe),w+=oe),V=le(v,w,B),Z=k>>>1==1?dt[V]:Fr[V],w+=Z&15,Z>>>=4;var ie=Z<4?0:Z-2>>1,he=X[Z];for(ie>0&&(he+=le(v,w,ie),w+=ie),!E&&M<ce&&(y=Gt(y,ce+100),M=y.length);_<ce;)y[_]=y[_-he],++_}}}return E?[y,w+7>>>3]:[y.slice(0,_),w+7>>>3]}function Ys(v,E){var w=v.slice(v.l||0),k=vl(w,E);return v.l+=k[1],k[0]}function Ks(v,E){if(v)typeof console<"u"&&console.error(E);else throw new Error(E)}function Js(v,E){var w=v;lr(w,0);var k=[],y=[],_={FileIndex:k,FullPaths:y};P(_,{root:E.root});for(var M=w.length-4;(w[M]!=80||w[M+1]!=75||w[M+2]!=5||w[M+3]!=6)&&M>=0;)--M;w.l=M+4,w.l+=4;var z=w.read_shift(2);w.l+=6;var B=w.read_shift(4);for(w.l=B,M=0;M<z;++M){w.l+=20;var H=w.read_shift(4),V=w.read_shift(4),Z=w.read_shift(2),oe=w.read_shift(2),ce=w.read_shift(2);w.l+=8;var ie=w.read_shift(4),he=i(w.slice(w.l+Z,w.l+Z+oe));w.l+=Z+oe+ce;var Ae=w.l;w.l=ie+4,gl(w,H,V,_,he),w.l=Ae}return _}function gl(v,E,w,k,y){v.l+=2;var _=v.read_shift(2),M=v.read_shift(2),z=s(v);if(_&8257)throw new Error("Unsupported ZIP encryption");for(var B=v.read_shift(4),H=v.read_shift(4),V=v.read_shift(4),Z=v.read_shift(2),oe=v.read_shift(2),ce="",ie=0;ie<Z;++ie)ce+=String.fromCharCode(v[v.l++]);if(oe){var he=i(v.slice(v.l,v.l+oe));(he[21589]||{}).mt&&(z=he[21589].mt),((y||{})[21589]||{}).mt&&(z=y[21589].mt)}v.l+=oe;var Ae=v.slice(v.l,v.l+H);switch(M){case 8:Ae=C(v,V);break;case 0:break;default:throw new Error("Unsupported ZIP Compression method "+M)}var He=!1;_&8&&(B=v.read_shift(4),B==134695760&&(B=v.read_shift(4),He=!0),H=v.read_shift(4),V=v.read_shift(4)),H!=E&&Ks(He,"Bad compressed size: "+E+" != "+H),V!=w&&Ks(He,"Bad uncompressed size: "+w+" != "+V),An(k,ce,Ae,{unsafe:!0,mt:z})}function wl(v,E){var w=E||{},k=[],y=[],_=j(1),M=w.compression?8:0,z=0,B=0,H=0,V=0,Z=0,oe=v.FullPaths[0],ce=oe,ie=v.FileIndex[0],he=[],Ae=0;for(B=1;B<v.FullPaths.length;++B)if(ce=v.FullPaths[B].slice(oe.length),ie=v.FileIndex[B],!(!ie.size||!ie.content||ce=="Sh33tJ5")){var He=V,Ge=j(ce.length);for(H=0;H<ce.length;++H)Ge.write_shift(1,ce.charCodeAt(H)&127);Ge=Ge.slice(0,Ge.l),he[Z]=mf.buf(ie.content,0);var Ar=ie.content;M==8&&(Ar=L(Ar)),_=j(30),_.write_shift(4,67324752),_.write_shift(2,20),_.write_shift(2,z),_.write_shift(2,M),ie.mt?n(_,ie.mt):_.write_shift(4,0),_.write_shift(-4,he[Z]),_.write_shift(4,Ar.length),_.write_shift(4,ie.content.length),_.write_shift(2,Ge.length),_.write_shift(2,0),V+=_.length,k.push(_),V+=Ge.length,k.push(Ge),V+=Ar.length,k.push(Ar),_=j(46),_.write_shift(4,33639248),_.write_shift(2,0),_.write_shift(2,20),_.write_shift(2,z),_.write_shift(2,M),_.write_shift(4,0),_.write_shift(-4,he[Z]),_.write_shift(4,Ar.length),_.write_shift(4,ie.content.length),_.write_shift(2,Ge.length),_.write_shift(2,0),_.write_shift(2,0),_.write_shift(2,0),_.write_shift(2,0),_.write_shift(4,0),_.write_shift(4,He),Ae+=_.l,y.push(_),Ae+=Ge.length,y.push(Ge),++Z}return _=j(22),_.write_shift(4,101010256),_.write_shift(2,0),_.write_shift(2,0),_.write_shift(2,Z),_.write_shift(2,Z),_.write_shift(4,Ae),_.write_shift(4,V),_.write_shift(2,0),ir([ir(k),ir(y),_])}var xn={htm:"text/html",xml:"text/xml",gif:"image/gif",jpg:"image/jpeg",png:"image/png",mso:"application/x-mso",thmx:"application/vnd.ms-officetheme",sh33tj5:"application/octet-stream"};function kl(v,E){if(v.ctype)return v.ctype;var w=v.name||"",k=w.match(/\.([^\.]+)$/);return k&&xn[k[1]]||E&&(k=(w=E).match(/[\.\\]([^\.\\])+$/),k&&xn[k[1]])?xn[k[1]]:"application/octet-stream"}function Tl(v){for(var E=_a(v),w=[],k=0;k<E.length;k+=76)w.push(E.slice(k,k+76));return w.join(`\r
`)+`\r
`}function El(v){var E=v.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF=]/g,function(H){var V=H.charCodeAt(0).toString(16).toUpperCase();return"="+(V.length==1?"0"+V:V)});E=E.replace(/ $/mg,"=20").replace(/\t$/mg,"=09"),E.charAt(0)==`
`&&(E="=0D"+E.slice(1)),E=E.replace(/\r(?!\n)/mg,"=0D").replace(/\n\n/mg,`
=0A`).replace(/([^\r\n])\n/mg,"$1=0A");for(var w=[],k=E.split(`\r
`),y=0;y<k.length;++y){var _=k[y];if(_.length==0){w.push("");continue}for(var M=0;M<_.length;){var z=76,B=_.slice(M,M+z);B.charAt(z-1)=="="?z--:B.charAt(z-2)=="="?z-=2:B.charAt(z-3)=="="&&(z-=3),B=_.slice(M,M+z),M+=z,M<_.length&&(B+="="),w.push(B)}}return w.join(`\r
`)}function yl(v){for(var E=[],w=0;w<v.length;++w){for(var k=v[w];w<=v.length&&k.charAt(k.length-1)=="=";)k=k.slice(0,k.length-1)+v[++w];E.push(k)}for(var y=0;y<E.length;++y)E[y]=E[y].replace(/[=][0-9A-Fa-f]{2}/g,function(_){return String.fromCharCode(parseInt(_.slice(1),16))});return Or(E.join(`\r
`))}function Sl(v,E,w){for(var k="",y="",_="",M,z=0;z<10;++z){var B=E[z];if(!B||B.match(/^\s*$/))break;var H=B.match(/^(.*?):\s*([^\s].*)$/);if(H)switch(H[1].toLowerCase()){case"content-location":k=H[2].trim();break;case"content-type":_=H[2].trim();break;case"content-transfer-encoding":y=H[2].trim();break}}switch(++z,y.toLowerCase()){case"base64":M=Or(Ir(E.slice(z).join("")));break;case"quoted-printable":M=yl(E.slice(z));break;default:throw new Error("Unsupported Content-Transfer-Encoding "+y)}var V=An(v,k.slice(w.length),M,{unsafe:!0});_&&(V.ctype=_)}function _l(v,E){if(K(v.slice(0,13)).toLowerCase()!="mime-version:")throw new Error("Unsupported MAD header");var w=E&&E.root||"",k=(Se&&Buffer.isBuffer(v)?v.toString("binary"):K(v)).split(`\r
`),y=0,_="";for(y=0;y<k.length;++y)if(_=k[y],!!/^Content-Location:/i.test(_)&&(_=_.slice(_.indexOf("file")),w||(w=_.slice(0,_.lastIndexOf("/")+1)),_.slice(0,w.length)!=w))for(;w.length>0&&(w=w.slice(0,w.length-1),w=w.slice(0,w.lastIndexOf("/")+1),_.slice(0,w.length)!=w););var M=(k[1]||"").match(/boundary="(.*?)"/);if(!M)throw new Error("MAD cannot find boundary");var z="--"+(M[1]||""),B=[],H=[],V={FileIndex:B,FullPaths:H};P(V);var Z,oe=0;for(y=0;y<k.length;++y){var ce=k[y];ce!==z&&ce!==z+"--"||(oe++&&Sl(V,k.slice(Z,y),w),Z=y)}return V}function xl(v,E){var w=E||{},k=w.boundary||"SheetJS";k="------="+k;for(var y=["MIME-Version: 1.0",'Content-Type: multipart/related; boundary="'+k.slice(2)+'"',"","",""],_=v.FullPaths[0],M=_,z=v.FileIndex[0],B=1;B<v.FullPaths.length;++B)if(M=v.FullPaths[B].slice(_.length),z=v.FileIndex[B],!(!z.size||!z.content||M=="Sh33tJ5")){M=M.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF]/g,function(Ae){return"_x"+Ae.charCodeAt(0).toString(16)+"_"}).replace(/[\u0080-\uFFFF]/g,function(Ae){return"_u"+Ae.charCodeAt(0).toString(16)+"_"});for(var H=z.content,V=Se&&Buffer.isBuffer(H)?H.toString("binary"):K(H),Z=0,oe=Math.min(1024,V.length),ce=0,ie=0;ie<=oe;++ie)(ce=V.charCodeAt(ie))>=32&&ce<128&&++Z;var he=Z>=oe*4/5;y.push(k),y.push("Content-Location: "+(w.root||"file:///C:/SheetJS/")+M),y.push("Content-Transfer-Encoding: "+(he?"quoted-printable":"base64")),y.push("Content-Type: "+kl(z,M)),y.push(""),y.push(he?El(V):Tl(V))}return y.push(k+`--\r
`),y.join(`\r
`)}function Al(v){var E={};return P(E,v),E}function An(v,E,w,k){var y=k&&k.unsafe;y||P(v);var _=!y&&pe.find(v,E);if(!_){var M=v.FullPaths[0];E.slice(0,M.length)==M?M=E:(M.slice(-1)!="/"&&(M+="/"),M=(M+E).replace("//","/")),_={name:a(E),type:2},v.FileIndex.push(_),v.FullPaths.push(M),y||pe.utils.cfb_gc(v)}return _.content=w,_.size=w?w.length:0,k&&(k.CLSID&&(_.clsid=k.CLSID),k.mt&&(_.mt=k.mt),k.ct&&(_.ct=k.ct)),_}function Cl(v,E){P(v);var w=pe.find(v,E);if(w){for(var k=0;k<v.FileIndex.length;++k)if(v.FileIndex[k]==w)return v.FileIndex.splice(k,1),v.FullPaths.splice(k,1),!0}return!1}function Rl(v,E,w){P(v);var k=pe.find(v,E);if(k){for(var y=0;y<v.FileIndex.length;++y)if(v.FileIndex[y]==k)return v.FileIndex[y].name=a(w),v.FullPaths[y]=w,!0}return!1}function Ol(v){U(v,!0)}return e.find=G,e.read=A,e.parse=l,e.write=ee,e.writeFile=ue,e.utils={cfb_new:Al,cfb_add:An,cfb_del:Cl,cfb_mov:Rl,cfb_gc:Ol,ReadShift:va,CheckField:zo,prep_blob:lr,bconcat:ir,use_zlib:Te,_deflateRaw:ye,_inflateRaw:Ys,consts:de},e}();function bf(e){return typeof e=="string"?vn(e):Array.isArray(e)?Wl(e):e}function Ua(e,t,r){if(typeof Deno<"u"){if(r&&typeof t=="string")switch(r){case"utf8":t=new TextEncoder(r).encode(t);break;case"binary":t=vn(t);break;default:throw new Error("Unsupported encoding "+r)}return Deno.writeFileSync(e,t)}var a=r=="utf8"?qr(t):t;if(typeof IE_SaveFile<"u")return IE_SaveFile(a,e);if(typeof Blob<"u"){var n=new Blob([bf(a)],{type:"application/octet-stream"});if(typeof navigator<"u"&&navigator.msSaveBlob)return navigator.msSaveBlob(n,e);if(typeof saveAs<"u")return saveAs(n,e);if(typeof URL<"u"&&typeof document<"u"&&document.createElement&&URL.createObjectURL){var s=URL.createObjectURL(n);if(typeof chrome=="object"&&typeof(chrome.downloads||{}).download=="function")return URL.revokeObjectURL&&typeof setTimeout<"u"&&setTimeout(function(){URL.revokeObjectURL(s)},6e4),chrome.downloads.download({url:s,filename:e,saveAs:!0});var i=document.createElement("a");if(i.download!=null)return i.download=e,i.href=s,document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL&&typeof setTimeout<"u"&&setTimeout(function(){URL.revokeObjectURL(s)},6e4),s}}if(typeof $<"u"&&typeof File<"u"&&typeof Folder<"u")try{var o=File(e);return o.open("w"),o.encoding="binary",Array.isArray(t)&&(t=Tt(t)),o.write(t),o.close(),t}catch(c){if(!c.message||!c.message.match(/onstruct/))throw c}throw new Error("cannot save file "+e)}function vf(e){if(typeof Deno<"u")return Deno.readFileSync(e);if(typeof $<"u"&&typeof File<"u"&&typeof Folder<"u")try{var t=File(e);t.open("r"),t.encoding="binary";var r=t.read();return t.close(),r}catch(a){if(!a.message||!a.message.match(/onstruct/))throw a}throw new Error("Cannot access file "+e)}function Ye(e){for(var t=Object.keys(e),r=[],a=0;a<t.length;++a)Object.prototype.hasOwnProperty.call(e,t[a])&&r.push(t[a]);return r}function oi(e,t){for(var r=[],a=Ye(e),n=0;n!==a.length;++n)r[e[a[n]][t]]==null&&(r[e[a[n]][t]]=a[n]);return r}function wn(e){for(var t=[],r=Ye(e),a=0;a!==r.length;++a)t[e[r[a]]]=r[a];return t}function kn(e){for(var t=[],r=Ye(e),a=0;a!==r.length;++a)t[e[r[a]]]=parseInt(r[a],10);return t}function gf(e){for(var t=[],r=Ye(e),a=0;a!==r.length;++a)t[e[r[a]]]==null&&(t[e[r[a]]]=[]),t[e[r[a]]].push(r[a]);return t}var cn=new Date(1899,11,30,0,0,0);function nr(e,t){var r=e.getTime(),a=cn.getTime()+(e.getTimezoneOffset()-cn.getTimezoneOffset())*6e4;return(r-a)/(24*60*60*1e3)}var ko=new Date,wf=cn.getTime()+(ko.getTimezoneOffset()-cn.getTimezoneOffset())*6e4,ci=ko.getTimezoneOffset();function Tn(e){var t=new Date;return t.setTime(e*24*60*60*1e3+wf),t.getTimezoneOffset()!==ci&&t.setTime(t.getTime()+(t.getTimezoneOffset()-ci)*6e4),t}function kf(e){var t=0,r=0,a=!1,n=e.match(/P([0-9\.]+Y)?([0-9\.]+M)?([0-9\.]+D)?T([0-9\.]+H)?([0-9\.]+M)?([0-9\.]+S)?/);if(!n)throw new Error("|"+e+"| is not an ISO8601 Duration");for(var s=1;s!=n.length;++s)if(n[s]){switch(r=1,s>3&&(a=!0),n[s].slice(n[s].length-1)){case"Y":throw new Error("Unsupported ISO Duration Field: "+n[s].slice(n[s].length-1));case"D":r*=24;case"H":r*=60;case"M":if(a)r*=60;else throw new Error("Unsupported ISO Duration Field: M")}t+=r*parseInt(n[s],10)}return t}var li=new Date("2017-02-19T19:06:09.000Z"),To=isNaN(li.getFullYear())?new Date("2/19/17"):li,Tf=To.getFullYear()==2017;function We(e,t){var r=new Date(e);if(Tf)return t>0?r.setTime(r.getTime()+r.getTimezoneOffset()*60*1e3):t<0&&r.setTime(r.getTime()-r.getTimezoneOffset()*60*1e3),r;if(e instanceof Date)return e;if(To.getFullYear()==1917&&!isNaN(r.getFullYear())){var a=r.getFullYear();return e.indexOf(""+a)>-1||r.setFullYear(r.getFullYear()+100),r}var n=e.match(/\d+/g)||["2017","2","19","0","0","0"],s=new Date(+n[0],+n[1]-1,+n[2],+n[3]||0,+n[4]||0,+n[5]||0);return e.indexOf("Z")>-1&&(s=new Date(s.getTime()-s.getTimezoneOffset()*60*1e3)),s}function It(e,t){if(Se&&Buffer.isBuffer(e)){if(t){if(e[0]==255&&e[1]==254)return qr(e.slice(2).toString("utf16le"));if(e[1]==254&&e[2]==255)return qr(oo(e.slice(2).toString("binary")))}return e.toString("binary")}if(typeof TextDecoder<"u")try{if(t){if(e[0]==255&&e[1]==254)return qr(new TextDecoder("utf-16le").decode(e.slice(2)));if(e[0]==254&&e[1]==255)return qr(new TextDecoder("utf-16be").decode(e.slice(2)))}var r={"€":"","‚":"",ƒ:"","„":"","…":"","†":"","‡":"","ˆ":"","‰":"",Š:"","‹":"",Œ:"",Ž:"","‘":"","’":"","“":"","”":"","•":"","–":"","—":"","˜":"","™":"",š:"","›":"",œ:"",ž:"",Ÿ:""};return Array.isArray(e)&&(e=new Uint8Array(e)),new TextDecoder("latin1").decode(e).replace(/[€‚ƒ„…†‡ˆ‰Š‹ŒŽ‘’“”•–—˜™š›œžŸ]/g,function(s){return r[s]||s})}catch{}for(var a=[],n=0;n!=e.length;++n)a.push(String.fromCharCode(e[n]));return a.join("")}function ze(e){if(typeof JSON<"u"&&!Array.isArray(e))return JSON.parse(JSON.stringify(e));if(typeof e!="object"||e==null)return e;if(e instanceof Date)return new Date(e.getTime());var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=ze(e[r]));return t}function $e(e,t){for(var r="";r.length<t;)r+=e;return r}function Xr(e){var t=Number(e);if(!isNaN(t))return isFinite(t)?t:NaN;if(!/\d/.test(e))return t;var r=1,a=e.replace(/([\d]),([\d])/g,"$1$2").replace(/[$]/g,"").replace(/[%]/g,function(){return r*=100,""});return!isNaN(t=Number(a))||(a=a.replace(/[(](.*)[)]/,function(n,s){return r=-r,s}),!isNaN(t=Number(a)))?t/r:t}var Ef=["january","february","march","april","may","june","july","august","september","october","november","december"];function ea(e){var t=new Date(e),r=new Date(NaN),a=t.getYear(),n=t.getMonth(),s=t.getDate();if(isNaN(s))return r;var i=e.toLowerCase();if(i.match(/jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec/)){if(i=i.replace(/[^a-z]/g,"").replace(/([^a-z]|^)[ap]m?([^a-z]|$)/,""),i.length>3&&Ef.indexOf(i)==-1)return r}else if(i.match(/[a-z]/))return r;return a<0||a>8099?r:(n>0||s>1)&&a!=101?t:e.match(/[^-0-9:,\/\\]/)?r:t}var yf=function(){var e="abacaba".split(/(:?b)/i).length==5;return function(t,r,a){if(e||typeof r=="string")return t.split(r);for(var n=t.split(r),s=[n[0]],i=1;i<n.length;++i)s.push(a),s.push(n[i]);return s}}();function Eo(e){return e?e.content&&e.type?It(e.content,!0):e.data?pa(e.data):e.asNodeBuffer&&Se?pa(e.asNodeBuffer().toString("binary")):e.asBinary?pa(e.asBinary()):e._data&&e._data.getContent?pa(It(Array.prototype.slice.call(e._data.getContent(),0))):null:null}function yo(e){if(!e)return null;if(e.data)return Zs(e.data);if(e.asNodeBuffer&&Se)return e.asNodeBuffer();if(e._data&&e._data.getContent){var t=e._data.getContent();return typeof t=="string"?Zs(t):Array.prototype.slice.call(t)}return e.content&&e.type?e.content:null}function Sf(e){return e&&e.name.slice(-4)===".bin"?yo(e):Eo(e)}function Ur(e,t){for(var r=e.FullPaths||Ye(e.files),a=t.toLowerCase().replace(/[\/]/g,"\\"),n=a.replace(/\\/g,"/"),s=0;s<r.length;++s){var i=r[s].replace(/^Root Entry[\/]/,"").toLowerCase();if(a==i||n==i)return e.files?e.files[r[s]]:e.FileIndex[s]}return null}function us(e,t){var r=Ur(e,t);if(r==null)throw new Error("Cannot find file "+t+" in zip");return r}function Qe(e,t,r){if(!r)return Sf(us(e,t));if(!t)return null;try{return Qe(e,t)}catch{return null}}function Nr(e,t,r){if(!r)return Eo(us(e,t));if(!t)return null;try{return Nr(e,t)}catch{return null}}function _f(e,t,r){return yo(us(e,t))}function fi(e){for(var t=e.FullPaths||Ye(e.files),r=[],a=0;a<t.length;++a)t[a].slice(-1)!="/"&&r.push(t[a].replace(/^Root Entry[\/]/,""));return r.sort()}function ke(e,t,r){if(e.FullPaths){if(typeof r=="string"){var a;return Se?a=lt(r):a=zl(r),pe.utils.cfb_add(e,t,a)}pe.utils.cfb_add(e,t,r)}else e.file(t,r)}function ds(){return pe.utils.cfb_new()}function So(e,t){switch(t.type){case"base64":return pe.read(e,{type:"base64"});case"binary":return pe.read(e,{type:"binary"});case"buffer":case"array":return pe.read(e,{type:"buffer"})}throw new Error("Unrecognized type "+t.type)}function ba(e,t){if(e.charAt(0)=="/")return e.slice(1);var r=t.split("/");t.slice(-1)!="/"&&r.pop();for(var a=e.split("/");a.length!==0;){var n=a.shift();n===".."?r.pop():n!=="."&&r.push(n)}return r.join("/")}var Ze=`<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r
`,xf=/([^"\s?>\/]+)\s*=\s*((?:")([^"]*)(?:")|(?:')([^']*)(?:')|([^'">\s]+))/g,hi=/<[\/\?]?[a-zA-Z0-9:_-]+(?:\s+[^"\s?>\/]+\s*=\s*(?:"[^"]*"|'[^']*'|[^'">\s=]+))*\s*[\/\?]?>/mg,Af=/<[^>]*>/g,gr=Ze.match(hi)?hi:Af,Cf=/<\w*:/,Rf=/<(\/?)\w+:/;function ge(e,t,r){for(var a={},n=0,s=0;n!==e.length&&!((s=e.charCodeAt(n))===32||s===10||s===13);++n);if(t||(a[0]=e.slice(0,n)),n===e.length)return a;var i=e.match(xf),o=0,c="",l=0,f="",p="",h=1;if(i)for(l=0;l!=i.length;++l){for(p=i[l],s=0;s!=p.length&&p.charCodeAt(s)!==61;++s);for(f=p.slice(0,s).trim();p.charCodeAt(s+1)==32;)++s;for(h=(n=p.charCodeAt(s+1))==34||n==39?1:0,c=p.slice(s+1+h,p.length-h),o=0;o!=f.length&&f.charCodeAt(o)!==58;++o);if(o===f.length)f.indexOf("_")>0&&(f=f.slice(0,f.indexOf("_"))),a[f]=c,a[f.toLowerCase()]=c;else{var d=(o===5&&f.slice(0,5)==="xmlns"?"xmlns":"")+f.slice(o+1);if(a[d]&&f.slice(o-3,o)=="ext")continue;a[d]=c,a[d.toLowerCase()]=c}}return a}function Qr(e){return e.replace(Rf,"<$1")}var _o={"&quot;":'"',"&apos;":"'","&gt;":">","&lt;":"<","&amp;":"&"},ps=wn(_o),Ce=function(){var e=/&(?:quot|apos|gt|lt|amp|#x?([\da-fA-F]+));/ig,t=/_x([\da-fA-F]{4})_/ig;return function r(a){var n=a+"",s=n.indexOf("<![CDATA[");if(s==-1)return n.replace(e,function(o,c){return _o[o]||String.fromCharCode(parseInt(c,o.indexOf("x")>-1?16:10))||o}).replace(t,function(o,c){return String.fromCharCode(parseInt(c,16))});var i=n.indexOf("]]>");return r(n.slice(0,s))+n.slice(s+9,i)+r(n.slice(i+3))}}(),ms=/[&<>'"]/g,Of=/[\u0000-\u0008\u000b-\u001f]/g;function Pe(e){var t=e+"";return t.replace(ms,function(r){return ps[r]}).replace(Of,function(r){return"_x"+("000"+r.charCodeAt(0).toString(16)).slice(-4)+"_"})}function ui(e){return Pe(e).replace(/ /g,"_x0020_")}var xo=/[\u0000-\u001f]/g;function bs(e){var t=e+"";return t.replace(ms,function(r){return ps[r]}).replace(/\n/g,"<br/>").replace(xo,function(r){return"&#x"+("000"+r.charCodeAt(0).toString(16)).slice(-4)+";"})}function Nf(e){var t=e+"";return t.replace(ms,function(r){return ps[r]}).replace(xo,function(r){return"&#x"+r.charCodeAt(0).toString(16).toUpperCase()+";"})}var di=function(){var e=/&#(\d+);/g;function t(r,a){return String.fromCharCode(parseInt(a,10))}return function(r){return r.replace(e,t)}}();function If(e){return e.replace(/(\r\n|[\r\n])/g,"&#10;")}function Be(e){switch(e){case 1:case!0:case"1":case"true":case"TRUE":return!0;default:return!1}}function In(e){for(var t="",r=0,a=0,n=0,s=0,i=0,o=0;r<e.length;){if(a=e.charCodeAt(r++),a<128){t+=String.fromCharCode(a);continue}if(n=e.charCodeAt(r++),a>191&&a<224){i=(a&31)<<6,i|=n&63,t+=String.fromCharCode(i);continue}if(s=e.charCodeAt(r++),a<240){t+=String.fromCharCode((a&15)<<12|(n&63)<<6|s&63);continue}i=e.charCodeAt(r++),o=((a&7)<<18|(n&63)<<12|(s&63)<<6|i&63)-65536,t+=String.fromCharCode(55296+(o>>>10&1023)),t+=String.fromCharCode(56320+(o&1023))}return t}function pi(e){var t=vt(2*e.length),r,a,n=1,s=0,i=0,o;for(a=0;a<e.length;a+=n)n=1,(o=e.charCodeAt(a))<128?r=o:o<224?(r=(o&31)*64+(e.charCodeAt(a+1)&63),n=2):o<240?(r=(o&15)*4096+(e.charCodeAt(a+1)&63)*64+(e.charCodeAt(a+2)&63),n=3):(n=4,r=(o&7)*262144+(e.charCodeAt(a+1)&63)*4096+(e.charCodeAt(a+2)&63)*64+(e.charCodeAt(a+3)&63),r-=65536,i=55296+(r>>>10&1023),r=56320+(r&1023)),i!==0&&(t[s++]=i&255,t[s++]=i>>>8,i=0),t[s++]=r%256,t[s++]=r>>>8;return t.slice(0,s).toString("ucs2")}function mi(e){return lt(e,"binary").toString("utf8")}var Xa="foo bar bazâð£",Le=Se&&(mi(Xa)==In(Xa)&&mi||pi(Xa)==In(Xa)&&pi)||In,qr=Se?function(e){return lt(e,"utf8").toString("binary")}:function(e){for(var t=[],r=0,a=0,n=0;r<e.length;)switch(a=e.charCodeAt(r++),!0){case a<128:t.push(String.fromCharCode(a));break;case a<2048:t.push(String.fromCharCode(192+(a>>6))),t.push(String.fromCharCode(128+(a&63)));break;case(a>=55296&&a<57344):a-=55296,n=e.charCodeAt(r++)-56320+(a<<10),t.push(String.fromCharCode(240+(n>>18&7))),t.push(String.fromCharCode(144+(n>>12&63))),t.push(String.fromCharCode(128+(n>>6&63))),t.push(String.fromCharCode(128+(n&63)));break;default:t.push(String.fromCharCode(224+(a>>12))),t.push(String.fromCharCode(128+(a>>6&63))),t.push(String.fromCharCode(128+(a&63)))}return t.join("")},Aa=function(){var e={};return function(t,r){var a=t+"|"+(r||"");return e[a]?e[a]:e[a]=new RegExp("<(?:\\w+:)?"+t+'(?: xml:space="preserve")?(?:[^>]*)>([\\s\\S]*?)</(?:\\w+:)?'+t+">",r||"")}}(),Ao=function(){var e=[["nbsp"," "],["middot","·"],["quot",'"'],["apos","'"],["gt",">"],["lt","<"],["amp","&"]].map(function(t){return[new RegExp("&"+t[0]+";","ig"),t[1]]});return function(t){for(var r=t.replace(/^[\t\n\r ]+/,"").replace(/[\t\n\r ]+$/,"").replace(/>\s+/g,">").replace(/\s+</g,"<").replace(/[\t\n\r ]+/g," ").replace(/<\s*[bB][rR]\s*\/?>/g,`
`).replace(/<[^>]*>/g,""),a=0;a<e.length;++a)r=r.replace(e[a][0],e[a][1]);return r}}(),Ff=function(){var e={};return function(t){return e[t]!==void 0?e[t]:e[t]=new RegExp("<(?:vt:)?"+t+">([\\s\\S]*?)</(?:vt:)?"+t+">","g")}}(),Pf=/<\/?(?:vt:)?variant>/g,Df=/<(?:vt:)([^>]*)>([\s\S]*)</;function bi(e,t){var r=ge(e),a=e.match(Ff(r.baseType))||[],n=[];if(a.length!=r.size){if(t.WTF)throw new Error("unexpected vector length "+a.length+" != "+r.size);return n}return a.forEach(function(s){var i=s.replace(Pf,"").match(Df);i&&n.push({v:Le(i[2]),t:i[1]})}),n}var Co=/(^\s|\s$|\n)/;function fr(e,t){return"<"+e+(t.match(Co)?' xml:space="preserve"':"")+">"+t+"</"+e+">"}function Ca(e){return Ye(e).map(function(t){return" "+t+'="'+e[t]+'"'}).join("")}function re(e,t,r){return"<"+e+(r!=null?Ca(r):"")+(t!=null?(t.match(Co)?' xml:space="preserve"':"")+">"+t+"</"+e:"/")+">"}function $n(e,t){try{return e.toISOString().replace(/\.\d*/,"")}catch(r){if(t)throw r}return""}function Lf(e,t){switch(typeof e){case"string":var r=re("vt:lpwstr",Pe(e));return r=r.replace(/&quot;/g,"_x0022_"),r;case"number":return re((e|0)==e?"vt:i4":"vt:r8",Pe(String(e)));case"boolean":return re("vt:bool",e?"true":"false")}if(e instanceof Date)return re("vt:filetime",$n(e));throw new Error("Unable to serialize "+e)}function vs(e){if(Se&&Buffer.isBuffer(e))return e.toString("utf8");if(typeof e=="string")return e;if(typeof Uint8Array<"u"&&e instanceof Uint8Array)return Le(Tt(ls(e)));throw new Error("Bad input format: expected Buffer or string")}var Ra=/<(\/?)([^\s?><!\/:]*:|)([^\s?<>:\/]+)(?:[\s?:\/][^>]*)?>/mg,ar={CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/metadata/core-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/custom-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/extended-properties",CT:"http://schemas.openxmlformats.org/package/2006/content-types",RELS:"http://schemas.openxmlformats.org/package/2006/relationships",TCMNT:"http://schemas.microsoft.com/office/spreadsheetml/2018/threadedcomments",dc:"http://purl.org/dc/elements/1.1/",dcterms:"http://purl.org/dc/terms/",dcmitype:"http://purl.org/dc/dcmitype/",r:"http://schemas.openxmlformats.org/officeDocument/2006/relationships",vt:"http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes",xsi:"http://www.w3.org/2001/XMLSchema-instance",xsd:"http://www.w3.org/2001/XMLSchema"},Mt=["http://schemas.openxmlformats.org/spreadsheetml/2006/main","http://purl.oclc.org/ooxml/spreadsheetml/main","http://schemas.microsoft.com/office/excel/2006/main","http://schemas.microsoft.com/office/excel/2006/2"],Cr={o:"urn:schemas-microsoft-com:office:office",x:"urn:schemas-microsoft-com:office:excel",ss:"urn:schemas-microsoft-com:office:spreadsheet",dt:"uuid:C2F41010-65B3-11d1-A29F-00AA00C14882",mv:"http://macVmlSchemaUri",v:"urn:schemas-microsoft-com:vml",html:"http://www.w3.org/TR/REC-html40"};function Mf(e,t){for(var r=1-2*(e[t+7]>>>7),a=((e[t+7]&127)<<4)+(e[t+6]>>>4&15),n=e[t+6]&15,s=5;s>=0;--s)n=n*256+e[t+s];return a==2047?n==0?r*(1/0):NaN:(a==0?a=-1022:(a-=1023,n+=Math.pow(2,52)),r*Math.pow(2,a-52)*n)}function Uf(e,t,r){var a=(t<0||1/t==-1/0?1:0)<<7,n=0,s=0,i=a?-t:t;isFinite(i)?i==0?n=s=0:(n=Math.floor(Math.log(i)/Math.LN2),s=i*Math.pow(2,52-n),n<=-1023&&(!isFinite(s)||s<Math.pow(2,52))?n=-1022:(s-=Math.pow(2,52),n+=1023)):(n=2047,s=isNaN(t)?26985:0);for(var o=0;o<=5;++o,s/=256)e[r+o]=s&255;e[r+6]=(n&15)<<4|s&15,e[r+7]=n>>4|a}var vi=function(e){for(var t=[],r=10240,a=0;a<e[0].length;++a)if(e[0][a])for(var n=0,s=e[0][a].length;n<s;n+=r)t.push.apply(t,e[0][a].slice(n,n+r));return t},gi=Se?function(e){return e[0].length>0&&Buffer.isBuffer(e[0][0])?Buffer.concat(e[0].map(function(t){return Buffer.isBuffer(t)?t:lt(t)})):vi(e)}:vi,wi=function(e,t,r){for(var a=[],n=t;n<r;n+=2)a.push(String.fromCharCode(nt(e,n)));return a.join("").replace(yr,"")},gs=Se?function(e,t,r){return Buffer.isBuffer(e)?e.toString("utf16le",t,r).replace(yr,""):wi(e,t,r)}:wi,ki=function(e,t,r){for(var a=[],n=t;n<t+r;++n)a.push(("0"+e[n].toString(16)).slice(-2));return a.join("")},Ro=Se?function(e,t,r){return Buffer.isBuffer(e)?e.toString("hex",t,t+r):ki(e,t,r)}:ki,Ti=function(e,t,r){for(var a=[],n=t;n<r;n++)a.push(String.fromCharCode(Xt(e,n)));return a.join("")},Ba=Se?function(e,t,r){return Buffer.isBuffer(e)?e.toString("utf8",t,r):Ti(e,t,r)}:Ti,Oo=function(e,t){var r=Rr(e,t);return r>0?Ba(e,t+4,t+4+r-1):""},No=Oo,Io=function(e,t){var r=Rr(e,t);return r>0?Ba(e,t+4,t+4+r-1):""},Fo=Io,Po=function(e,t){var r=2*Rr(e,t);return r>0?Ba(e,t+4,t+4+r-1):""},Do=Po,Lo=function(e,t){var r=Rr(e,t);return r>0?gs(e,t+4,t+4+r):""},Mo=Lo,Uo=function(e,t){var r=Rr(e,t);return r>0?Ba(e,t+4,t+4+r):""},Bo=Uo,Wo=function(e,t){return Mf(e,t)},ln=Wo,ws=function(e){return Array.isArray(e)||typeof Uint8Array<"u"&&e instanceof Uint8Array};Se&&(No=function(e,t){if(!Buffer.isBuffer(e))return Oo(e,t);var r=e.readUInt32LE(t);return r>0?e.toString("utf8",t+4,t+4+r-1):""},Fo=function(e,t){if(!Buffer.isBuffer(e))return Io(e,t);var r=e.readUInt32LE(t);return r>0?e.toString("utf8",t+4,t+4+r-1):""},Do=function(e,t){if(!Buffer.isBuffer(e))return Po(e,t);var r=2*e.readUInt32LE(t);return e.toString("utf16le",t+4,t+4+r-1)},Mo=function(e,t){if(!Buffer.isBuffer(e))return Lo(e,t);var r=e.readUInt32LE(t);return e.toString("utf16le",t+4,t+4+r)},Bo=function(e,t){if(!Buffer.isBuffer(e))return Uo(e,t);var r=e.readUInt32LE(t);return e.toString("utf8",t+4,t+4+r)},ln=function(e,t){return Buffer.isBuffer(e)?e.readDoubleLE(t):Wo(e,t)},ws=function(e){return Buffer.isBuffer(e)||Array.isArray(e)||typeof Uint8Array<"u"&&e instanceof Uint8Array});var Xt=function(e,t){return e[t]},nt=function(e,t){return e[t+1]*256+e[t]},Bf=function(e,t){var r=e[t+1]*256+e[t];return r<32768?r:(65535-r+1)*-1},Rr=function(e,t){return e[t+3]*(1<<24)+(e[t+2]<<16)+(e[t+1]<<8)+e[t]},At=function(e,t){return e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]},Wf=function(e,t){return e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3]};function va(e,t){var r="",a,n,s=[],i,o,c,l;switch(t){case"dbcs":if(l=this.l,Se&&Buffer.isBuffer(this))r=this.slice(this.l,this.l+2*e).toString("utf16le");else for(c=0;c<e;++c)r+=String.fromCharCode(nt(this,l)),l+=2;e*=2;break;case"utf8":r=Ba(this,this.l,this.l+e);break;case"utf16le":e*=2,r=gs(this,this.l,this.l+e);break;case"wstr":return va.call(this,e,"dbcs");case"lpstr-ansi":r=No(this,this.l),e=4+Rr(this,this.l);break;case"lpstr-cp":r=Fo(this,this.l),e=4+Rr(this,this.l);break;case"lpwstr":r=Do(this,this.l),e=4+2*Rr(this,this.l);break;case"lpp4":e=4+Rr(this,this.l),r=Mo(this,this.l),e&2&&(e+=2);break;case"8lpp4":e=4+Rr(this,this.l),r=Bo(this,this.l),e&3&&(e+=4-(e&3));break;case"cstr":for(e=0,r="";(i=Xt(this,this.l+e++))!==0;)s.push($a(i));r=s.join("");break;case"_wstr":for(e=0,r="";(i=nt(this,this.l+e))!==0;)s.push($a(i)),e+=2;e+=2,r=s.join("");break;case"dbcs-cont":for(r="",l=this.l,c=0;c<e;++c){if(this.lens&&this.lens.indexOf(l)!==-1)return i=Xt(this,l),this.l=l+1,o=va.call(this,e-c,i?"dbcs-cont":"sbcs-cont"),s.join("")+o;s.push($a(nt(this,l))),l+=2}r=s.join(""),e*=2;break;case"cpstr":case"sbcs-cont":for(r="",l=this.l,c=0;c!=e;++c){if(this.lens&&this.lens.indexOf(l)!==-1)return i=Xt(this,l),this.l=l+1,o=va.call(this,e-c,i?"dbcs-cont":"sbcs-cont"),s.join("")+o;s.push($a(Xt(this,l))),l+=1}r=s.join("");break;default:switch(e){case 1:return a=Xt(this,this.l),this.l++,a;case 2:return a=(t==="i"?Bf:nt)(this,this.l),this.l+=2,a;case 4:case-4:return t==="i"||!(this[this.l+3]&128)?(a=(e>0?At:Wf)(this,this.l),this.l+=4,a):(n=Rr(this,this.l),this.l+=4,n);case 8:case-8:if(t==="f")return e==8?n=ln(this,this.l):n=ln([this[this.l+7],this[this.l+6],this[this.l+5],this[this.l+4],this[this.l+3],this[this.l+2],this[this.l+1],this[this.l+0]],0),this.l+=8,n;e=8;case 16:r=Ro(this,this.l,e);break}}return this.l+=e,r}var zf=function(e,t,r){e[r]=t&255,e[r+1]=t>>>8&255,e[r+2]=t>>>16&255,e[r+3]=t>>>24&255},Hf=function(e,t,r){e[r]=t&255,e[r+1]=t>>8&255,e[r+2]=t>>16&255,e[r+3]=t>>24&255},Gf=function(e,t,r){e[r]=t&255,e[r+1]=t>>>8&255};function Vf(e,t,r){var a=0,n=0;if(r==="dbcs"){for(n=0;n!=t.length;++n)Gf(this,t.charCodeAt(n),this.l+2*n);a=2*t.length}else if(r==="sbcs"){for(t=t.replace(/[^\x00-\x7F]/g,"_"),n=0;n!=t.length;++n)this[this.l+n]=t.charCodeAt(n)&255;a=t.length}else if(r==="hex"){for(;n<e;++n)this[this.l++]=parseInt(t.slice(2*n,2*n+2),16)||0;return this}else if(r==="utf16le"){var s=Math.min(this.l+e,this.length);for(n=0;n<Math.min(t.length,e);++n){var i=t.charCodeAt(n);this[this.l++]=i&255,this[this.l++]=i>>8}for(;this.l<s;)this[this.l++]=0;return this}else switch(e){case 1:a=1,this[this.l]=t&255;break;case 2:a=2,this[this.l]=t&255,t>>>=8,this[this.l+1]=t&255;break;case 3:a=3,this[this.l]=t&255,t>>>=8,this[this.l+1]=t&255,t>>>=8,this[this.l+2]=t&255;break;case 4:a=4,zf(this,t,this.l);break;case 8:if(a=8,r==="f"){Uf(this,t,this.l);break}case 16:break;case-4:a=4,Hf(this,t,this.l);break}return this.l+=a,this}function zo(e,t){var r=Ro(this,this.l,e.length>>1);if(r!==e)throw new Error(t+"Expected "+e+" saw "+r);this.l+=e.length>>1}function lr(e,t){e.l=t,e.read_shift=va,e.chk=zo,e.write_shift=Vf}function vr(e,t){e.l+=t}function j(e){var t=vt(e);return lr(t,0),t}function ft(e,t,r){if(e){var a,n,s;lr(e,e.l||0);for(var i=e.length,o=0,c=0;e.l<i;){o=e.read_shift(1),o&128&&(o=(o&127)+((e.read_shift(1)&127)<<7));var l=Da[o]||Da[65535];for(a=e.read_shift(1),s=a&127,n=1;n<4&&a&128;++n)s+=((a=e.read_shift(1))&127)<<7*n;c=e.l+s;var f=l.f&&l.f(e,s,r);if(e.l=c,t(f,l,o))return}}}function _r(){var e=[],t=Se?256:2048,r=function(c){var l=j(c);return lr(l,0),l},a=r(t),n=function(){a&&(a.length>a.l&&(a=a.slice(0,a.l),a.l=a.length),a.length>0&&e.push(a),a=null)},s=function(c){return a&&c<a.length-a.l?a:(n(),a=r(Math.max(c+1,t)))},i=function(){return n(),ir(e)},o=function(c){n(),a=c,a.l==null&&(a.l=a.length),s(t)};return{next:s,push:o,end:i,_bufs:e}}function Y(e,t,r,a){var n=+t,s;if(!isNaN(n)){a||(a=Da[n].p||(r||[]).length||0),s=1+(n>=128?1:0)+1,a>=128&&++s,a>=16384&&++s,a>=2097152&&++s;var i=e.next(s);n<=127?i.write_shift(1,n):(i.write_shift(1,(n&127)+128),i.write_shift(1,n>>7));for(var o=0;o!=4;++o)if(a>=128)i.write_shift(1,(a&127)+128),a>>=7;else{i.write_shift(1,a);break}a>0&&ws(r)&&e.push(r)}}function ga(e,t,r){var a=ze(e);if(t.s?(a.cRel&&(a.c+=t.s.c),a.rRel&&(a.r+=t.s.r)):(a.cRel&&(a.c+=t.c),a.rRel&&(a.r+=t.r)),!r||r.biff<12){for(;a.c>=256;)a.c-=256;for(;a.r>=65536;)a.r-=65536}return a}function Ei(e,t,r){var a=ze(e);return a.s=ga(a.s,t.s,r),a.e=ga(a.e,t.s,r),a}function wa(e,t){if(e.cRel&&e.c<0)for(e=ze(e);e.c<0;)e.c+=t>8?16384:256;if(e.rRel&&e.r<0)for(e=ze(e);e.r<0;)e.r+=t>8?1048576:t>5?65536:16384;var r=ve(e);return!e.cRel&&e.cRel!=null&&(r=Xf(r)),!e.rRel&&e.rRel!=null&&(r=jf(r)),r}function Fn(e,t){return e.s.r==0&&!e.s.rRel&&e.e.r==(t.biff>=12?1048575:t.biff>=8?65536:16384)&&!e.e.rRel?(e.s.cRel?"":"$")+Ve(e.s.c)+":"+(e.e.cRel?"":"$")+Ve(e.e.c):e.s.c==0&&!e.s.cRel&&e.e.c==(t.biff>=12?16383:255)&&!e.e.cRel?(e.s.rRel?"":"$")+Je(e.s.r)+":"+(e.e.rRel?"":"$")+Je(e.e.r):wa(e.s,t.biff)+":"+wa(e.e,t.biff)}function ks(e){return parseInt($f(e),10)-1}function Je(e){return""+(e+1)}function jf(e){return e.replace(/([A-Z]|^)(\d+)$/,"$1$$$2")}function $f(e){return e.replace(/\$(\d+)$/,"$1")}function Ts(e){for(var t=Yf(e),r=0,a=0;a!==t.length;++a)r=26*r+t.charCodeAt(a)-64;return r-1}function Ve(e){if(e<0)throw new Error("invalid column "+e);var t="";for(++e;e;e=Math.floor((e-1)/26))t=String.fromCharCode((e-1)%26+65)+t;return t}function Xf(e){return e.replace(/^([A-Z])/,"$$$1")}function Yf(e){return e.replace(/^\$([A-Z])/,"$1")}function Kf(e){return e.replace(/(\$?[A-Z]*)(\$?\d*)/,"$1,$2").split(",")}function je(e){for(var t=0,r=0,a=0;a<e.length;++a){var n=e.charCodeAt(a);n>=48&&n<=57?t=10*t+(n-48):n>=65&&n<=90&&(r=26*r+(n-64))}return{c:r-1,r:t-1}}function ve(e){for(var t=e.c+1,r="";t;t=(t-1)/26|0)r=String.fromCharCode((t-1)%26+65)+r;return r+(e.r+1)}function xr(e){var t=e.indexOf(":");return t==-1?{s:je(e),e:je(e)}:{s:je(e.slice(0,t)),e:je(e.slice(t+1))}}function we(e,t){return typeof t>"u"||typeof t=="number"?we(e.s,e.e):(typeof e!="string"&&(e=ve(e)),typeof t!="string"&&(t=ve(t)),e==t?e:e+":"+t)}function Re(e){var t={s:{c:0,r:0},e:{c:0,r:0}},r=0,a=0,n=0,s=e.length;for(r=0;a<s&&!((n=e.charCodeAt(a)-64)<1||n>26);++a)r=26*r+n;for(t.s.c=--r,r=0;a<s&&!((n=e.charCodeAt(a)-48)<0||n>9);++a)r=10*r+n;if(t.s.r=--r,a===s||n!=10)return t.e.c=t.s.c,t.e.r=t.s.r,t;for(++a,r=0;a!=s&&!((n=e.charCodeAt(a)-64)<1||n>26);++a)r=26*r+n;for(t.e.c=--r,r=0;a!=s&&!((n=e.charCodeAt(a)-48)<0||n>9);++a)r=10*r+n;return t.e.r=--r,t}function yi(e,t){var r=e.t=="d"&&t instanceof Date;if(e.z!=null)try{return e.w=zr(e.z,r?nr(t):t)}catch{}try{return e.w=zr((e.XF||{}).numFmtId||(r?14:0),r?nr(t):t)}catch{return""+t}}function ct(e,t,r){return e==null||e.t==null||e.t=="z"?"":e.w!==void 0?e.w:(e.t=="d"&&!e.z&&r&&r.dateNF&&(e.z=r.dateNF),e.t=="e"?ht[e.v]||e.v:t==null?yi(e,e.v):yi(e,t))}function Et(e,t){var r=t&&t.sheet?t.sheet:"Sheet1",a={};return a[r]=e,{SheetNames:[r],Sheets:a}}function Ho(e,t,r){var a=r||{},n=e?Array.isArray(e):a.dense,s=e||(n?[]:{}),i=0,o=0;if(s&&a.origin!=null){if(typeof a.origin=="number")i=a.origin;else{var c=typeof a.origin=="string"?je(a.origin):a.origin;i=c.r,o=c.c}s["!ref"]||(s["!ref"]="A1:A1")}var l={s:{c:1e7,r:1e7},e:{c:0,r:0}};if(s["!ref"]){var f=Re(s["!ref"]);l.s.c=f.s.c,l.s.r=f.s.r,l.e.c=Math.max(l.e.c,f.e.c),l.e.r=Math.max(l.e.r,f.e.r),i==-1&&(l.e.r=i=f.e.r+1)}for(var p=0;p!=t.length;++p)if(t[p]){if(!Array.isArray(t[p]))throw new Error("aoa_to_sheet expects an array of arrays");for(var h=0;h!=t[p].length;++h)if(!(typeof t[p][h]>"u")){var d={v:t[p][h]},m=i+p,u=o+h;if(l.s.r>m&&(l.s.r=m),l.s.c>u&&(l.s.c=u),l.e.r<m&&(l.e.r=m),l.e.c<u&&(l.e.c=u),t[p][h]&&typeof t[p][h]=="object"&&!Array.isArray(t[p][h])&&!(t[p][h]instanceof Date))d=t[p][h];else if(Array.isArray(d.v)&&(d.f=t[p][h][1],d.v=d.v[0]),d.v===null)if(d.f)d.t="n";else if(a.nullError)d.t="e",d.v=0;else if(a.sheetStubs)d.t="z";else continue;else typeof d.v=="number"?d.t="n":typeof d.v=="boolean"?d.t="b":d.v instanceof Date?(d.z=a.dateNF||be[14],a.cellDates?(d.t="d",d.w=zr(d.z,nr(d.v))):(d.t="n",d.v=nr(d.v),d.w=zr(d.z,d.v))):d.t="s";if(n)s[m]||(s[m]=[]),s[m][u]&&s[m][u].z&&(d.z=s[m][u].z),s[m][u]=d;else{var b=ve({c:u,r:m});s[b]&&s[b].z&&(d.z=s[b].z),s[b]=d}}}return l.s.c<1e7&&(s["!ref"]=we(l)),s}function ia(e,t){return Ho(null,e,t)}function Jf(e){return e.read_shift(4,"i")}function Yr(e,t){return t||(t=j(4)),t.write_shift(4,e),t}function br(e){var t=e.read_shift(4);return t===0?"":e.read_shift(t,"dbcs")}function or(e,t){var r=!1;return t==null&&(r=!0,t=j(4+2*e.length)),t.write_shift(4,e.length),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}function Zf(e){return{ich:e.read_shift(2),ifnt:e.read_shift(2)}}function qf(e,t){return t||(t=j(4)),t.write_shift(2,0),t.write_shift(2,0),t}function Es(e,t){var r=e.l,a=e.read_shift(1),n=br(e),s=[],i={t:n,h:n};if(a&1){for(var o=e.read_shift(4),c=0;c!=o;++c)s.push(Zf(e));i.r=s}else i.r=[{ich:0,ifnt:0}];return e.l=r+t,i}function Qf(e,t){var r=!1;return t==null&&(r=!0,t=j(15+4*e.t.length)),t.write_shift(1,0),or(e.t,t),r?t.slice(0,t.l):t}var e1=Es;function r1(e,t){var r=!1;return t==null&&(r=!0,t=j(23+4*e.t.length)),t.write_shift(1,1),or(e.t,t),t.write_shift(4,1),qf({},t),r?t.slice(0,t.l):t}function Hr(e){var t=e.read_shift(4),r=e.read_shift(2);return r+=e.read_shift(1)<<16,e.l++,{c:t,iStyleRef:r}}function Ut(e,t){return t==null&&(t=j(8)),t.write_shift(-4,e.c),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}function Bt(e){var t=e.read_shift(2);return t+=e.read_shift(1)<<16,e.l++,{c:-1,iStyleRef:t}}function Wt(e,t){return t==null&&(t=j(4)),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}var t1=br,Go=or;function ys(e){var t=e.read_shift(4);return t===0||t===4294967295?"":e.read_shift(t,"dbcs")}function fn(e,t){var r=!1;return t==null&&(r=!0,t=j(127)),t.write_shift(4,e.length>0?e.length:4294967295),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}var a1=br,Xn=ys,Ss=fn;function _s(e){var t=e.slice(e.l,e.l+4),r=t[0]&1,a=t[0]&2;e.l+=4;var n=a===0?ln([0,0,0,0,t[0]&252,t[1],t[2],t[3]],0):At(t,0)>>2;return r?n/100:n}function Vo(e,t){t==null&&(t=j(4));var r=0,a=0,n=e*100;if(e==(e|0)&&e>=-536870912&&e<1<<29?a=1:n==(n|0)&&n>=-536870912&&n<1<<29&&(a=1,r=1),a)t.write_shift(-4,((r?n:e)<<2)+(r+2));else throw new Error("unsupported RkNumber "+e)}function jo(e){var t={s:{},e:{}};return t.s.r=e.read_shift(4),t.e.r=e.read_shift(4),t.s.c=e.read_shift(4),t.e.c=e.read_shift(4),t}function n1(e,t){return t||(t=j(16)),t.write_shift(4,e.s.r),t.write_shift(4,e.e.r),t.write_shift(4,e.s.c),t.write_shift(4,e.e.c),t}var zt=jo,oa=n1;function mr(e){if(e.length-e.l<8)throw"XLS Xnum Buffer underflow";return e.read_shift(8,"f")}function Ft(e,t){return(t||j(8)).write_shift(8,e,"f")}function s1(e){var t={},r=e.read_shift(1),a=r>>>1,n=e.read_shift(1),s=e.read_shift(2,"i"),i=e.read_shift(1),o=e.read_shift(1),c=e.read_shift(1);switch(e.l++,a){case 0:t.auto=1;break;case 1:t.index=n;var l=Jt[n];l&&(t.rgb=Na(l));break;case 2:t.rgb=Na([i,o,c]);break;case 3:t.theme=n;break}return s!=0&&(t.tint=s>0?s/32767:s/32768),t}function hn(e,t){if(t||(t=j(8)),!e||e.auto)return t.write_shift(4,0),t.write_shift(4,0),t;e.index!=null?(t.write_shift(1,2),t.write_shift(1,e.index)):e.theme!=null?(t.write_shift(1,6),t.write_shift(1,e.theme)):(t.write_shift(1,5),t.write_shift(1,0));var r=e.tint||0;if(r>0?r*=32767:r<0&&(r*=32768),t.write_shift(2,r),!e.rgb||e.theme!=null)t.write_shift(2,0),t.write_shift(1,0),t.write_shift(1,0);else{var a=e.rgb||"FFFFFF";typeof a=="number"&&(a=("000000"+a.toString(16)).slice(-6)),t.write_shift(1,parseInt(a.slice(0,2),16)),t.write_shift(1,parseInt(a.slice(2,4),16)),t.write_shift(1,parseInt(a.slice(4,6),16)),t.write_shift(1,255)}return t}function i1(e){var t=e.read_shift(1);e.l++;var r={fBold:t&1,fItalic:t&2,fUnderline:t&4,fStrikeout:t&8,fOutline:t&16,fShadow:t&32,fCondense:t&64,fExtend:t&128};return r}function o1(e,t){t||(t=j(2));var r=(e.italic?2:0)|(e.strike?8:0)|(e.outline?16:0)|(e.shadow?32:0)|(e.condense?64:0)|(e.extend?128:0);return t.write_shift(1,r),t.write_shift(1,0),t}function $o(e,t){var r={2:"BITMAP",3:"METAFILEPICT",8:"DIB",14:"ENHMETAFILE"},a=e.read_shift(4);switch(a){case 0:return"";case 4294967295:case 4294967294:return r[e.read_shift(4)]||""}if(a>400)throw new Error("Unsupported Clipboard: "+a.toString(16));return e.l-=4,e.read_shift(0,t==1?"lpstr":"lpwstr")}function c1(e){return $o(e,1)}function l1(e){return $o(e,2)}var xs=2,Sr=3,Ya=11,Si=12,un=19,Ka=64,f1=65,h1=71,u1=4108,d1=4126,sr=80,Xo=81,p1=[sr,Xo],Yn={1:{n:"CodePage",t:xs},2:{n:"Category",t:sr},3:{n:"PresentationFormat",t:sr},4:{n:"ByteCount",t:Sr},5:{n:"LineCount",t:Sr},6:{n:"ParagraphCount",t:Sr},7:{n:"SlideCount",t:Sr},8:{n:"NoteCount",t:Sr},9:{n:"HiddenCount",t:Sr},10:{n:"MultimediaClipCount",t:Sr},11:{n:"ScaleCrop",t:Ya},12:{n:"HeadingPairs",t:u1},13:{n:"TitlesOfParts",t:d1},14:{n:"Manager",t:sr},15:{n:"Company",t:sr},16:{n:"LinksUpToDate",t:Ya},17:{n:"CharacterCount",t:Sr},19:{n:"SharedDoc",t:Ya},22:{n:"HyperlinksChanged",t:Ya},23:{n:"AppVersion",t:Sr,p:"version"},24:{n:"DigSig",t:f1},26:{n:"ContentType",t:sr},27:{n:"ContentStatus",t:sr},28:{n:"Language",t:sr},29:{n:"Version",t:sr},255:{},2147483648:{n:"Locale",t:un},2147483651:{n:"Behavior",t:un},1919054434:{}},Kn={1:{n:"CodePage",t:xs},2:{n:"Title",t:sr},3:{n:"Subject",t:sr},4:{n:"Author",t:sr},5:{n:"Keywords",t:sr},6:{n:"Comments",t:sr},7:{n:"Template",t:sr},8:{n:"LastAuthor",t:sr},9:{n:"RevNumber",t:sr},10:{n:"EditTime",t:Ka},11:{n:"LastPrinted",t:Ka},12:{n:"CreatedDate",t:Ka},13:{n:"ModifiedDate",t:Ka},14:{n:"PageCount",t:Sr},15:{n:"WordCount",t:Sr},16:{n:"CharCount",t:Sr},17:{n:"Thumbnail",t:h1},18:{n:"Application",t:sr},19:{n:"DocSecurity",t:Sr},255:{},2147483648:{n:"Locale",t:un},2147483651:{n:"Behavior",t:un},1919054434:{}},_i={1:"US",2:"CA",3:"",7:"RU",20:"EG",30:"GR",31:"NL",32:"BE",33:"FR",34:"ES",36:"HU",39:"IT",41:"CH",43:"AT",44:"GB",45:"DK",46:"SE",47:"NO",48:"PL",49:"DE",52:"MX",55:"BR",61:"AU",64:"NZ",66:"TH",81:"JP",82:"KR",84:"VN",86:"CN",90:"TR",105:"JS",213:"DZ",216:"MA",218:"LY",351:"PT",354:"IS",358:"FI",420:"CZ",886:"TW",961:"LB",962:"JO",963:"SY",964:"IQ",965:"KW",966:"SA",971:"AE",972:"IL",974:"QA",981:"IR",65535:"US"},m1=[null,"solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"];function b1(e){return e.map(function(t){return[t>>16&255,t>>8&255,t&255]})}var v1=b1([0,16777215,16711680,65280,255,16776960,16711935,65535,0,16777215,16711680,65280,255,16776960,16711935,65535,8388608,32768,128,8421376,8388736,32896,12632256,8421504,10066431,10040166,16777164,13434879,6684774,16744576,26316,13421823,128,16711935,16776960,65535,8388736,8388608,32896,255,52479,13434879,13434828,16777113,10079487,16751052,13408767,16764057,3368703,3394764,10079232,16763904,16750848,16737792,6710937,9868950,13158,3381606,13056,3355392,10040064,10040166,3355545,3355443,16777215,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]),Jt=ze(v1),ht={0:"#NULL!",7:"#DIV/0!",15:"#VALUE!",23:"#REF!",29:"#NAME?",36:"#NUM!",42:"#N/A",43:"#GETTING_DATA",255:"#WTF?"},Yo={"#NULL!":0,"#DIV/0!":7,"#VALUE!":15,"#REF!":23,"#NAME?":29,"#NUM!":36,"#N/A":42,"#GETTING_DATA":43,"#WTF?":255},Jn={"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":"workbooks","application/vnd.ms-excel.sheet.macroEnabled.main+xml":"workbooks","application/vnd.ms-excel.sheet.binary.macroEnabled.main":"workbooks","application/vnd.ms-excel.addin.macroEnabled.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":"sheets","application/vnd.ms-excel.worksheet":"sheets","application/vnd.ms-excel.binIndexWs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":"charts","application/vnd.ms-excel.chartsheet":"charts","application/vnd.ms-excel.macrosheet+xml":"macros","application/vnd.ms-excel.macrosheet":"macros","application/vnd.ms-excel.intlmacrosheet":"TODO","application/vnd.ms-excel.binIndexMs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":"dialogs","application/vnd.ms-excel.dialogsheet":"dialogs","application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml":"strs","application/vnd.ms-excel.sharedStrings":"strs","application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":"styles","application/vnd.ms-excel.styles":"styles","application/vnd.openxmlformats-package.core-properties+xml":"coreprops","application/vnd.openxmlformats-officedocument.custom-properties+xml":"custprops","application/vnd.openxmlformats-officedocument.extended-properties+xml":"extprops","application/vnd.openxmlformats-officedocument.customXmlProperties+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.customProperty":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":"comments","application/vnd.ms-excel.comments":"comments","application/vnd.ms-excel.threadedcomments+xml":"threadedcomments","application/vnd.ms-excel.person+xml":"people","application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml":"metadata","application/vnd.ms-excel.sheetMetadata":"metadata","application/vnd.ms-excel.pivotTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.chart+xml":"TODO","application/vnd.ms-office.chartcolorstyle+xml":"TODO","application/vnd.ms-office.chartstyle+xml":"TODO","application/vnd.ms-office.chartex+xml":"TODO","application/vnd.ms-excel.calcChain":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.calcChain+xml":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.printerSettings":"TODO","application/vnd.ms-office.activeX":"TODO","application/vnd.ms-office.activeX+xml":"TODO","application/vnd.ms-excel.attachedToolbars":"TODO","application/vnd.ms-excel.connections":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":"TODO","application/vnd.ms-excel.externalLink":"links","application/vnd.openxmlformats-officedocument.spreadsheetml.externalLink+xml":"links","application/vnd.ms-excel.pivotCacheDefinition":"TODO","application/vnd.ms-excel.pivotCacheRecords":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheRecords+xml":"TODO","application/vnd.ms-excel.queryTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.queryTable+xml":"TODO","application/vnd.ms-excel.userNames":"TODO","application/vnd.ms-excel.revisionHeaders":"TODO","application/vnd.ms-excel.revisionLog":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionHeaders+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionLog+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.userNames+xml":"TODO","application/vnd.ms-excel.tableSingleCells":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.tableSingleCells+xml":"TODO","application/vnd.ms-excel.slicer":"TODO","application/vnd.ms-excel.slicerCache":"TODO","application/vnd.ms-excel.slicer+xml":"TODO","application/vnd.ms-excel.slicerCache+xml":"TODO","application/vnd.ms-excel.wsSortMap":"TODO","application/vnd.ms-excel.table":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":"TODO","application/vnd.openxmlformats-officedocument.theme+xml":"themes","application/vnd.openxmlformats-officedocument.themeOverride+xml":"TODO","application/vnd.ms-excel.Timeline+xml":"TODO","application/vnd.ms-excel.TimelineCache+xml":"TODO","application/vnd.ms-office.vbaProject":"vba","application/vnd.ms-office.vbaProjectSignature":"TODO","application/vnd.ms-office.volatileDependencies":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.volatileDependencies+xml":"TODO","application/vnd.ms-excel.controlproperties+xml":"TODO","application/vnd.openxmlformats-officedocument.model+data":"TODO","application/vnd.ms-excel.Survey+xml":"TODO","application/vnd.openxmlformats-officedocument.drawing+xml":"drawings","application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramColors+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramLayout+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramStyle+xml":"TODO","application/vnd.openxmlformats-officedocument.vmlDrawing":"TODO","application/vnd.openxmlformats-package.relationships+xml":"rels","application/vnd.openxmlformats-officedocument.oleObject":"TODO","image/png":"TODO",sheet:"js"},Ja={workbooks:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml",xlsm:"application/vnd.ms-excel.sheet.macroEnabled.main+xml",xlsb:"application/vnd.ms-excel.sheet.binary.macroEnabled.main",xlam:"application/vnd.ms-excel.addin.macroEnabled.main+xml",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml"},strs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml",xlsb:"application/vnd.ms-excel.sharedStrings"},comments:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml",xlsb:"application/vnd.ms-excel.comments"},sheets:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml",xlsb:"application/vnd.ms-excel.worksheet"},charts:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml",xlsb:"application/vnd.ms-excel.chartsheet"},dialogs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml",xlsb:"application/vnd.ms-excel.dialogsheet"},macros:{xlsx:"application/vnd.ms-excel.macrosheet+xml",xlsb:"application/vnd.ms-excel.macrosheet"},metadata:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml",xlsb:"application/vnd.ms-excel.sheetMetadata"},styles:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml",xlsb:"application/vnd.ms-excel.styles"}};function As(){return{workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""}}function g1(e){var t=As();if(!e||!e.match)return t;var r={};if((e.match(gr)||[]).forEach(function(a){var n=ge(a);switch(n[0].replace(Cf,"<")){case"<?xml":break;case"<Types":t.xmlns=n["xmlns"+(n[0].match(/<(\w+):/)||["",""])[1]];break;case"<Default":r[n.Extension]=n.ContentType;break;case"<Override":t[Jn[n.ContentType]]!==void 0&&t[Jn[n.ContentType]].push(n.PartName);break}}),t.xmlns!==ar.CT)throw new Error("Unknown Namespace: "+t.xmlns);return t.calcchain=t.calcchains.length>0?t.calcchains[0]:"",t.sst=t.strs.length>0?t.strs[0]:"",t.style=t.styles.length>0?t.styles[0]:"",t.defaults=r,delete t.calcchains,t}function Ko(e,t){var r=gf(Jn),a=[],n;a[a.length]=Ze,a[a.length]=re("Types",null,{xmlns:ar.CT,"xmlns:xsd":ar.xsd,"xmlns:xsi":ar.xsi}),a=a.concat([["xml","application/xml"],["bin","application/vnd.ms-excel.sheet.binary.macroEnabled.main"],["vml","application/vnd.openxmlformats-officedocument.vmlDrawing"],["data","application/vnd.openxmlformats-officedocument.model+data"],["bmp","image/bmp"],["png","image/png"],["gif","image/gif"],["emf","image/x-emf"],["wmf","image/x-wmf"],["jpg","image/jpeg"],["jpeg","image/jpeg"],["tif","image/tiff"],["tiff","image/tiff"],["pdf","application/pdf"],["rels","application/vnd.openxmlformats-package.relationships+xml"]].map(function(c){return re("Default",null,{Extension:c[0],ContentType:c[1]})}));var s=function(c){e[c]&&e[c].length>0&&(n=e[c][0],a[a.length]=re("Override",null,{PartName:(n[0]=="/"?"":"/")+n,ContentType:Ja[c][t.bookType]||Ja[c].xlsx}))},i=function(c){(e[c]||[]).forEach(function(l){a[a.length]=re("Override",null,{PartName:(l[0]=="/"?"":"/")+l,ContentType:Ja[c][t.bookType]||Ja[c].xlsx})})},o=function(c){(e[c]||[]).forEach(function(l){a[a.length]=re("Override",null,{PartName:(l[0]=="/"?"":"/")+l,ContentType:r[c][0]})})};return s("workbooks"),i("sheets"),i("charts"),o("themes"),["strs","styles"].forEach(s),["coreprops","extprops","custprops"].forEach(o),o("vba"),o("comments"),o("threadedcomments"),o("drawings"),i("metadata"),o("people"),a.length>2&&(a[a.length]="</Types>",a[1]=a[1].replace("/>",">")),a.join("")}var _e={WB:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",SHEET:"http://sheetjs.openxmlformats.org/officeDocument/2006/relationships/officeDocument",HLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",VML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing",XPATH:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLinkPath",XMISS:"http://schemas.microsoft.com/office/2006/relationships/xlExternalLinkPath/xlPathMissing",XLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLink",CXML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXml",CXMLP:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXmlProps",CMNT:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments",CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties",SST:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings",STY:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles",THEME:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme",CHART:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chart",CHARTEX:"http://schemas.microsoft.com/office/2014/relationships/chartEx",CS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chartsheet",WS:["http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet","http://purl.oclc.org/ooxml/officeDocument/relationships/worksheet"],DS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/dialogsheet",MS:"http://schemas.microsoft.com/office/2006/relationships/xlMacrosheet",IMG:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/image",DRAW:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing",XLMETA:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sheetMetadata",TCMNT:"http://schemas.microsoft.com/office/2017/10/relationships/threadedComment",PEOPLE:"http://schemas.microsoft.com/office/2017/10/relationships/person",VBA:"http://schemas.microsoft.com/office/2006/relationships/vbaProject"};function Oa(e){var t=e.lastIndexOf("/");return e.slice(0,t+1)+"_rels/"+e.slice(t+1)+".rels"}function ka(e,t){var r={"!id":{}};if(!e)return r;t.charAt(0)!=="/"&&(t="/"+t);var a={};return(e.match(gr)||[]).forEach(function(n){var s=ge(n);if(s[0]==="<Relationship"){var i={};i.Type=s.Type,i.Target=s.Target,i.Id=s.Id,s.TargetMode&&(i.TargetMode=s.TargetMode);var o=s.TargetMode==="External"?s.Target:ba(s.Target,t);r[o]=i,a[s.Id]=i}}),r["!id"]=a,r}function Zt(e){var t=[Ze,re("Relationships",null,{xmlns:ar.RELS})];return Ye(e["!id"]).forEach(function(r){t[t.length]=re("Relationship",null,e["!id"][r])}),t.length>2&&(t[t.length]="</Relationships>",t[1]=t[1].replace("/>",">")),t.join("")}function Fe(e,t,r,a,n,s){if(n||(n={}),e["!id"]||(e["!id"]={}),e["!idx"]||(e["!idx"]=1),t<0)for(t=e["!idx"];e["!id"]["rId"+t];++t);if(e["!idx"]=t+1,n.Id="rId"+t,n.Type=a,n.Target=r,[_e.HLINK,_e.XPATH,_e.XMISS].indexOf(n.Type)>-1&&(n.TargetMode="External"),e["!id"][n.Id])throw new Error("Cannot rewrite rId "+t);return e["!id"][n.Id]=n,e[("/"+n.Target).replace("//","/")]=n,t}var w1="application/vnd.oasis.opendocument.spreadsheet";function k1(e,t){for(var r=vs(e),a,n;a=Ra.exec(r);)switch(a[3]){case"manifest":break;case"file-entry":if(n=ge(a[0],!1),n.path=="/"&&n.type!==w1)throw new Error("This OpenDocument is not a spreadsheet");break;case"encryption-data":case"algorithm":case"start-key-generation":case"key-derivation":throw new Error("Unsupported ODS Encryption");default:if(t&&t.WTF)throw a}}function T1(e){var t=[Ze];t.push(`<manifest:manifest xmlns:manifest="urn:oasis:names:tc:opendocument:xmlns:manifest:1.0" manifest:version="1.2">
`),t.push(`  <manifest:file-entry manifest:full-path="/" manifest:version="1.2" manifest:media-type="application/vnd.oasis.opendocument.spreadsheet"/>
`);for(var r=0;r<e.length;++r)t.push('  <manifest:file-entry manifest:full-path="'+e[r][0]+'" manifest:media-type="'+e[r][1]+`"/>
`);return t.push("</manifest:manifest>"),t.join("")}function xi(e,t,r){return['  <rdf:Description rdf:about="'+e+`">
`,'    <rdf:type rdf:resource="http://docs.oasis-open.org/ns/office/1.2/meta/'+(r||"odf")+"#"+t+`"/>
`,`  </rdf:Description>
`].join("")}function E1(e,t){return['  <rdf:Description rdf:about="'+e+`">
`,'    <ns0:hasPart xmlns:ns0="http://docs.oasis-open.org/ns/office/1.2/meta/pkg#" rdf:resource="'+t+`"/>
`,`  </rdf:Description>
`].join("")}function y1(e){var t=[Ze];t.push(`<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
`);for(var r=0;r!=e.length;++r)t.push(xi(e[r][0],e[r][1])),t.push(E1("",e[r][0]));return t.push(xi("","Document","pkg")),t.push("</rdf:RDF>"),t.join("")}function Jo(){return'<office:document-meta xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:xlink="http://www.w3.org/1999/xlink" office:version="1.2"><office:meta><meta:generator>SheetJS '+an.version+"</meta:generator></office:meta></office:document-meta>"}var Wr=[["cp:category","Category"],["cp:contentStatus","ContentStatus"],["cp:keywords","Keywords"],["cp:lastModifiedBy","LastAuthor"],["cp:lastPrinted","LastPrinted"],["cp:revision","RevNumber"],["cp:version","Version"],["dc:creator","Author"],["dc:description","Comments"],["dc:identifier","Identifier"],["dc:language","Language"],["dc:subject","Subject"],["dc:title","Title"],["dcterms:created","CreatedDate","date"],["dcterms:modified","ModifiedDate","date"]],S1=function(){for(var e=new Array(Wr.length),t=0;t<Wr.length;++t){var r=Wr[t],a="(?:"+r[0].slice(0,r[0].indexOf(":"))+":)"+r[0].slice(r[0].indexOf(":")+1);e[t]=new RegExp("<"+a+"[^>]*>([\\s\\S]*?)</"+a+">")}return e}();function Zo(e){var t={};e=Le(e);for(var r=0;r<Wr.length;++r){var a=Wr[r],n=e.match(S1[r]);n!=null&&n.length>0&&(t[a[1]]=Ce(n[1])),a[2]==="date"&&t[a[1]]&&(t[a[1]]=We(t[a[1]]))}return t}function Pn(e,t,r,a,n){n[e]!=null||t==null||t===""||(n[e]=t,t=Pe(t),a[a.length]=r?re(e,t,r):fr(e,t))}function qo(e,t){var r=t||{},a=[Ze,re("cp:coreProperties",null,{"xmlns:cp":ar.CORE_PROPS,"xmlns:dc":ar.dc,"xmlns:dcterms":ar.dcterms,"xmlns:dcmitype":ar.dcmitype,"xmlns:xsi":ar.xsi})],n={};if(!e&&!r.Props)return a.join("");e&&(e.CreatedDate!=null&&Pn("dcterms:created",typeof e.CreatedDate=="string"?e.CreatedDate:$n(e.CreatedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},a,n),e.ModifiedDate!=null&&Pn("dcterms:modified",typeof e.ModifiedDate=="string"?e.ModifiedDate:$n(e.ModifiedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},a,n));for(var s=0;s!=Wr.length;++s){var i=Wr[s],o=r.Props&&r.Props[i[1]]!=null?r.Props[i[1]]:e?e[i[1]]:null;o===!0?o="1":o===!1?o="0":typeof o=="number"&&(o=String(o)),o!=null&&Pn(i[0],o,null,a,n)}return a.length>2&&(a[a.length]="</cp:coreProperties>",a[1]=a[1].replace("/>",">")),a.join("")}var Rt=[["Application","Application","string"],["AppVersion","AppVersion","string"],["Company","Company","string"],["DocSecurity","DocSecurity","string"],["Manager","Manager","string"],["HyperlinksChanged","HyperlinksChanged","bool"],["SharedDoc","SharedDoc","bool"],["LinksUpToDate","LinksUpToDate","bool"],["ScaleCrop","ScaleCrop","bool"],["HeadingPairs","HeadingPairs","raw"],["TitlesOfParts","TitlesOfParts","raw"]],Qo=["Worksheets","SheetNames","NamedRanges","DefinedNames","Chartsheets","ChartNames"];function ec(e,t,r,a){var n=[];if(typeof e=="string")n=bi(e,a);else for(var s=0;s<e.length;++s)n=n.concat(e[s].map(function(f){return{v:f}}));var i=typeof t=="string"?bi(t,a).map(function(f){return f.v}):t,o=0,c=0;if(i.length>0)for(var l=0;l!==n.length;l+=2){switch(c=+n[l+1].v,n[l].v){case"Worksheets":case"工作表":case"Листы":case"أوراق العمل":case"ワークシート":case"גליונות עבודה":case"Arbeitsblätter":case"Çalışma Sayfaları":case"Feuilles de calcul":case"Fogli di lavoro":case"Folhas de cálculo":case"Planilhas":case"Regneark":case"Hojas de cálculo":case"Werkbladen":r.Worksheets=c,r.SheetNames=i.slice(o,o+c);break;case"Named Ranges":case"Rangos con nombre":case"名前付き一覧":case"Benannte Bereiche":case"Navngivne områder":r.NamedRanges=c,r.DefinedNames=i.slice(o,o+c);break;case"Charts":case"Diagramme":r.Chartsheets=c,r.ChartNames=i.slice(o,o+c);break}o+=c}}function _1(e,t,r){var a={};return t||(t={}),e=Le(e),Rt.forEach(function(n){var s=(e.match(Aa(n[0]))||[])[1];switch(n[2]){case"string":s&&(t[n[1]]=Ce(s));break;case"bool":t[n[1]]=s==="true";break;case"raw":var i=e.match(new RegExp("<"+n[0]+"[^>]*>([\\s\\S]*?)</"+n[0]+">"));i&&i.length>0&&(a[n[1]]=i[1]);break}}),a.HeadingPairs&&a.TitlesOfParts&&ec(a.HeadingPairs,a.TitlesOfParts,t,r),t}function rc(e){var t=[],r=re;return e||(e={}),e.Application="SheetJS",t[t.length]=Ze,t[t.length]=re("Properties",null,{xmlns:ar.EXT_PROPS,"xmlns:vt":ar.vt}),Rt.forEach(function(a){if(e[a[1]]!==void 0){var n;switch(a[2]){case"string":n=Pe(String(e[a[1]]));break;case"bool":n=e[a[1]]?"true":"false";break}n!==void 0&&(t[t.length]=r(a[0],n))}}),t[t.length]=r("HeadingPairs",r("vt:vector",r("vt:variant","<vt:lpstr>Worksheets</vt:lpstr>")+r("vt:variant",r("vt:i4",String(e.Worksheets))),{size:2,baseType:"variant"})),t[t.length]=r("TitlesOfParts",r("vt:vector",e.SheetNames.map(function(a){return"<vt:lpstr>"+Pe(a)+"</vt:lpstr>"}).join(""),{size:e.Worksheets,baseType:"lpstr"})),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}var x1=/<[^>]+>[^<]*/g;function A1(e,t){var r={},a="",n=e.match(x1);if(n)for(var s=0;s!=n.length;++s){var i=n[s],o=ge(i);switch(o[0]){case"<?xml":break;case"<Properties":break;case"<property":a=Ce(o.name);break;case"</property>":a=null;break;default:if(i.indexOf("<vt:")===0){var c=i.split(">"),l=c[0].slice(4),f=c[1];switch(l){case"lpstr":case"bstr":case"lpwstr":r[a]=Ce(f);break;case"bool":r[a]=Be(f);break;case"i1":case"i2":case"i4":case"i8":case"int":case"uint":r[a]=parseInt(f,10);break;case"r4":case"r8":case"decimal":r[a]=parseFloat(f);break;case"filetime":case"date":r[a]=We(f);break;case"cy":case"error":r[a]=Ce(f);break;default:if(l.slice(-1)=="/")break;t.WTF&&typeof console<"u"&&console.warn("Unexpected",i,l,c)}}else if(i.slice(0,2)!=="</"&&t.WTF)throw new Error(i)}}return r}function tc(e){var t=[Ze,re("Properties",null,{xmlns:ar.CUST_PROPS,"xmlns:vt":ar.vt})];if(!e)return t.join("");var r=1;return Ye(e).forEach(function(a){++r,t[t.length]=re("property",Lf(e[a]),{fmtid:"{D5CDD505-2E9C-101B-9397-08002B2CF9AE}",pid:r,name:Pe(a)})}),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}var Zn={Title:"Title",Subject:"Subject",Author:"Author",Keywords:"Keywords",Comments:"Description",LastAuthor:"LastAuthor",RevNumber:"Revision",Application:"AppName",LastPrinted:"LastPrinted",CreatedDate:"Created",ModifiedDate:"LastSaved",Category:"Category",Manager:"Manager",Company:"Company",AppVersion:"Version",ContentStatus:"ContentStatus",Identifier:"Identifier",Language:"Language"},Dn;function C1(e,t,r){Dn||(Dn=wn(Zn)),t=Dn[t]||t,e[t]=r}function R1(e,t){var r=[];return Ye(Zn).map(function(a){for(var n=0;n<Wr.length;++n)if(Wr[n][1]==a)return Wr[n];for(n=0;n<Rt.length;++n)if(Rt[n][1]==a)return Rt[n];throw a}).forEach(function(a){if(e[a[1]]!=null){var n=t&&t.Props&&t.Props[a[1]]!=null?t.Props[a[1]]:e[a[1]];switch(a[2]){case"date":n=new Date(n).toISOString().replace(/\.\d*Z/,"Z");break}typeof n=="number"?n=String(n):n===!0||n===!1?n=n?"1":"0":n instanceof Date&&(n=new Date(n).toISOString().replace(/\.\d*Z/,"")),r.push(fr(Zn[a[1]]||a[1],n))}}),re("DocumentProperties",r.join(""),{xmlns:Cr.o})}function O1(e,t){var r=["Worksheets","SheetNames"],a="CustomDocumentProperties",n=[];return e&&Ye(e).forEach(function(s){if(Object.prototype.hasOwnProperty.call(e,s)){for(var i=0;i<Wr.length;++i)if(s==Wr[i][1])return;for(i=0;i<Rt.length;++i)if(s==Rt[i][1])return;for(i=0;i<r.length;++i)if(s==r[i])return;var o=e[s],c="string";typeof o=="number"?(c="float",o=String(o)):o===!0||o===!1?(c="boolean",o=o?"1":"0"):o=String(o),n.push(re(ui(s),o,{"dt:dt":c}))}}),t&&Ye(t).forEach(function(s){if(Object.prototype.hasOwnProperty.call(t,s)&&!(e&&Object.prototype.hasOwnProperty.call(e,s))){var i=t[s],o="string";typeof i=="number"?(o="float",i=String(i)):i===!0||i===!1?(o="boolean",i=i?"1":"0"):i instanceof Date?(o="dateTime.tz",i=i.toISOString()):i=String(i),n.push(re(ui(s),i,{"dt:dt":o}))}}),"<"+a+' xmlns="'+Cr.o+'">'+n.join("")+"</"+a+">"}function Cs(e){var t=e.read_shift(4),r=e.read_shift(4);return new Date((r/1e7*Math.pow(2,32)+t/1e7-11644473600)*1e3).toISOString().replace(/\.000/,"")}function N1(e){var t=typeof e=="string"?new Date(Date.parse(e)):e,r=t.getTime()/1e3+11644473600,a=r%Math.pow(2,32),n=(r-a)/Math.pow(2,32);a*=1e7,n*=1e7;var s=a/Math.pow(2,32)|0;s>0&&(a=a%Math.pow(2,32),n+=s);var i=j(8);return i.write_shift(4,a),i.write_shift(4,n),i}function ac(e,t,r){var a=e.l,n=e.read_shift(0,"lpstr-cp");if(r)for(;e.l-a&3;)++e.l;return n}function nc(e,t,r){var a=e.read_shift(0,"lpwstr");return a}function sc(e,t,r){return t===31?nc(e):ac(e,t,r)}function qn(e,t,r){return sc(e,t,r===!1?0:4)}function I1(e,t){if(!t)throw new Error("VtUnalignedString must have positive length");return sc(e,t,0)}function F1(e){for(var t=e.read_shift(4),r=[],a=0;a!=t;++a){var n=e.l;r[a]=e.read_shift(0,"lpwstr").replace(yr,""),e.l-n&2&&(e.l+=2)}return r}function P1(e){for(var t=e.read_shift(4),r=[],a=0;a!=t;++a)r[a]=e.read_shift(0,"lpstr-cp").replace(yr,"");return r}function D1(e){var t=e.l,r=dn(e,Xo);e[e.l]==0&&e[e.l+1]==0&&e.l-t&2&&(e.l+=2);var a=dn(e,Sr);return[r,a]}function L1(e){for(var t=e.read_shift(4),r=[],a=0;a<t/2;++a)r.push(D1(e));return r}function Ai(e,t){for(var r=e.read_shift(4),a={},n=0;n!=r;++n){var s=e.read_shift(4),i=e.read_shift(4);a[s]=e.read_shift(i,t===1200?"utf16le":"utf8").replace(yr,"").replace(ma,"!"),t===1200&&i%2&&(e.l+=2)}return e.l&3&&(e.l=e.l>>3<<2),a}function ic(e){var t=e.read_shift(4),r=e.slice(e.l,e.l+t);return e.l+=t,(t&3)>0&&(e.l+=4-(t&3)&3),r}function M1(e){var t={};return t.Size=e.read_shift(4),e.l+=t.Size+3-(t.Size-1)%4,t}function dn(e,t,r){var a=e.read_shift(2),n,s=r||{};if(e.l+=2,t!==Si&&a!==t&&p1.indexOf(t)===-1&&!((t&65534)==4126&&(a&65534)==4126))throw new Error("Expected type "+t+" saw "+a);switch(t===Si?a:t){case 2:return n=e.read_shift(2,"i"),s.raw||(e.l+=2),n;case 3:return n=e.read_shift(4,"i"),n;case 11:return e.read_shift(4)!==0;case 19:return n=e.read_shift(4),n;case 30:return ac(e,a,4).replace(yr,"");case 31:return nc(e);case 64:return Cs(e);case 65:return ic(e);case 71:return M1(e);case 80:return qn(e,a,!s.raw).replace(yr,"");case 81:return I1(e,a).replace(yr,"");case 4108:return L1(e);case 4126:case 4127:return a==4127?F1(e):P1(e);default:throw new Error("TypedPropertyValue unrecognized type "+t+" "+a)}}function Ci(e,t){var r=j(4),a=j(4);switch(r.write_shift(4,e==80?31:e),e){case 3:a.write_shift(-4,t);break;case 5:a=j(8),a.write_shift(8,t,"f");break;case 11:a.write_shift(4,t?1:0);break;case 64:a=N1(t);break;case 31:case 80:for(a=j(4+2*(t.length+1)+(t.length%2?0:2)),a.write_shift(4,t.length+1),a.write_shift(0,t,"dbcs");a.l!=a.length;)a.write_shift(1,0);break;default:throw new Error("TypedPropertyValue unrecognized type "+e+" "+t)}return ir([r,a])}function Ri(e,t){var r=e.l,a=e.read_shift(4),n=e.read_shift(4),s=[],i=0,o=0,c=-1,l={};for(i=0;i!=n;++i){var f=e.read_shift(4),p=e.read_shift(4);s[i]=[f,p+r]}s.sort(function(S,g){return S[1]-g[1]});var h={};for(i=0;i!=n;++i){if(e.l!==s[i][1]){var d=!0;if(i>0&&t)switch(t[s[i-1][0]].t){case 2:e.l+2===s[i][1]&&(e.l+=2,d=!1);break;case 80:e.l<=s[i][1]&&(e.l=s[i][1],d=!1);break;case 4108:e.l<=s[i][1]&&(e.l=s[i][1],d=!1);break}if((!t||i==0)&&e.l<=s[i][1]&&(d=!1,e.l=s[i][1]),d)throw new Error("Read Error: Expected address "+s[i][1]+" at "+e.l+" :"+i)}if(t){var m=t[s[i][0]];if(h[m.n]=dn(e,m.t,{raw:!0}),m.p==="version"&&(h[m.n]=String(h[m.n]>>16)+"."+("0000"+String(h[m.n]&65535)).slice(-4)),m.n=="CodePage")switch(h[m.n]){case 0:h[m.n]=1252;case 874:case 932:case 936:case 949:case 950:case 1250:case 1251:case 1253:case 1254:case 1255:case 1256:case 1257:case 1258:case 1e4:case 1200:case 1201:case 1252:case 65e3:case-536:case 65001:case-535:jr(o=h[m.n]>>>0&65535);break;default:throw new Error("Unsupported CodePage: "+h[m.n])}}else if(s[i][0]===1){if(o=h.CodePage=dn(e,xs),jr(o),c!==-1){var u=e.l;e.l=s[c][1],l=Ai(e,o),e.l=u}}else if(s[i][0]===0){if(o===0){c=i,e.l=s[i+1][1];continue}l=Ai(e,o)}else{var b=l[s[i][0]],T;switch(e[e.l]){case 65:e.l+=4,T=ic(e);break;case 30:e.l+=4,T=qn(e,e[e.l-4]).replace(/\u0000+$/,"");break;case 31:e.l+=4,T=qn(e,e[e.l-4]).replace(/\u0000+$/,"");break;case 3:e.l+=4,T=e.read_shift(4,"i");break;case 19:e.l+=4,T=e.read_shift(4);break;case 5:e.l+=4,T=e.read_shift(8,"f");break;case 11:e.l+=4,T=Ke(e,4);break;case 64:e.l+=4,T=We(Cs(e));break;default:throw new Error("unparsed value: "+e[e.l])}h[b]=T}}return e.l=r+a,h}var oc=["CodePage","Thumbnail","_PID_LINKBASE","_PID_HLINKS","SystemIdentifier","FMTID"];function U1(e){switch(typeof e){case"boolean":return 11;case"number":return(e|0)==e?3:5;case"string":return 31;case"object":if(e instanceof Date)return 64;break}return-1}function Oi(e,t,r){var a=j(8),n=[],s=[],i=8,o=0,c=j(8),l=j(8);if(c.write_shift(4,2),c.write_shift(4,1200),l.write_shift(4,1),s.push(c),n.push(l),i+=8+c.length,!t){l=j(8),l.write_shift(4,0),n.unshift(l);var f=[j(4)];for(f[0].write_shift(4,e.length),o=0;o<e.length;++o){var p=e[o][0];for(c=j(8+2*(p.length+1)+(p.length%2?0:2)),c.write_shift(4,o+2),c.write_shift(4,p.length+1),c.write_shift(0,p,"dbcs");c.l!=c.length;)c.write_shift(1,0);f.push(c)}c=ir(f),s.unshift(c),i+=8+c.length}for(o=0;o<e.length;++o)if(!(t&&!t[e[o][0]])&&!(oc.indexOf(e[o][0])>-1||Qo.indexOf(e[o][0])>-1)&&e[o][1]!=null){var h=e[o][1],d=0;if(t){d=+t[e[o][0]];var m=r[d];if(m.p=="version"&&typeof h=="string"){var u=h.split(".");h=(+u[0]<<16)+(+u[1]||0)}c=Ci(m.t,h)}else{var b=U1(h);b==-1&&(b=31,h=String(h)),c=Ci(b,h)}s.push(c),l=j(8),l.write_shift(4,t?d:2+o),n.push(l),i+=8+c.length}var T=8*(s.length+1);for(o=0;o<s.length;++o)n[o].write_shift(4,T),T+=s[o].length;return a.write_shift(4,i),a.write_shift(4,s.length),ir([a].concat(n).concat(s))}function Ni(e,t,r){var a=e.content;if(!a)return{};lr(a,0);var n,s,i,o,c=0;a.chk("feff","Byte Order: "),a.read_shift(2);var l=a.read_shift(4),f=a.read_shift(16);if(f!==pe.utils.consts.HEADER_CLSID&&f!==r)throw new Error("Bad PropertySet CLSID "+f);if(n=a.read_shift(4),n!==1&&n!==2)throw new Error("Unrecognized #Sets: "+n);if(s=a.read_shift(16),o=a.read_shift(4),n===1&&o!==a.l)throw new Error("Length mismatch: "+o+" !== "+a.l);n===2&&(i=a.read_shift(16),c=a.read_shift(4));var p=Ri(a,t),h={SystemIdentifier:l};for(var d in p)h[d]=p[d];if(h.FMTID=s,n===1)return h;if(c-a.l==2&&(a.l+=2),a.l!==c)throw new Error("Length mismatch 2: "+a.l+" !== "+c);var m;try{m=Ri(a,null)}catch{}for(d in m)h[d]=m[d];return h.FMTID=[s,i],h}function Ii(e,t,r,a,n,s){var i=j(n?68:48),o=[i];i.write_shift(2,65534),i.write_shift(2,0),i.write_shift(4,842412599),i.write_shift(16,pe.utils.consts.HEADER_CLSID,"hex"),i.write_shift(4,n?2:1),i.write_shift(16,t,"hex"),i.write_shift(4,n?68:48);var c=Oi(e,r,a);if(o.push(c),n){var l=Oi(n,null,null);i.write_shift(16,s,"hex"),i.write_shift(4,68+c.length),o.push(l)}return ir(o)}function pt(e,t){return e.read_shift(t),null}function B1(e,t){t||(t=j(e));for(var r=0;r<e;++r)t.write_shift(1,0);return t}function W1(e,t,r){for(var a=[],n=e.l+t;e.l<n;)a.push(r(e,n-e.l));if(n!==e.l)throw new Error("Slurp error");return a}function Ke(e,t){return e.read_shift(t)===1}function Er(e,t){return t||(t=j(2)),t.write_shift(2,+!!e),t}function er(e){return e.read_shift(2,"u")}function Br(e,t){return t||(t=j(2)),t.write_shift(2,e),t}function cc(e,t){return W1(e,t,er)}function z1(e){var t=e.read_shift(1),r=e.read_shift(1);return r===1?t:t===1}function lc(e,t,r){return r||(r=j(2)),r.write_shift(1,t=="e"?+e:+!!e),r.write_shift(1,t=="e"?1:0),r}function Wa(e,t,r){var a=e.read_shift(r&&r.biff>=12?2:1),n="sbcs-cont";if(r&&r.biff>=8,!r||r.biff==8){var s=e.read_shift(1);s&&(n="dbcs-cont")}else r.biff==12&&(n="wstr");r.biff>=2&&r.biff<=5&&(n="cpstr");var i=a?e.read_shift(a,n):"";return i}function H1(e){var t=e.read_shift(2),r=e.read_shift(1),a=r&4,n=r&8,s=1+(r&1),i=0,o,c={};n&&(i=e.read_shift(2)),a&&(o=e.read_shift(4));var l=s==2?"dbcs-cont":"sbcs-cont",f=t===0?"":e.read_shift(t,l);return n&&(e.l+=4*i),a&&(e.l+=o),c.t=f,n||(c.raw="<t>"+c.t+"</t>",c.r=c.t),c}function G1(e){var t=e.t||"",r=j(3);r.write_shift(2,t.length),r.write_shift(1,1);var a=j(2*t.length);a.write_shift(2*t.length,t,"utf16le");var n=[r,a];return ir(n)}function Pt(e,t,r){var a;if(r){if(r.biff>=2&&r.biff<=5)return e.read_shift(t,"cpstr");if(r.biff>=12)return e.read_shift(t,"dbcs-cont")}var n=e.read_shift(1);return n===0?a=e.read_shift(t,"sbcs-cont"):a=e.read_shift(t,"dbcs-cont"),a}function za(e,t,r){var a=e.read_shift(r&&r.biff==2?1:2);return a===0?(e.l++,""):Pt(e,a,r)}function Ht(e,t,r){if(r.biff>5)return za(e,t,r);var a=e.read_shift(1);return a===0?(e.l++,""):e.read_shift(a,r.biff<=4||!e.lens?"cpstr":"sbcs-cont")}function fc(e,t,r){return r||(r=j(3+2*e.length)),r.write_shift(2,e.length),r.write_shift(1,1),r.write_shift(31,e,"utf16le"),r}function V1(e){var t=e.read_shift(1);e.l++;var r=e.read_shift(2);return e.l+=2,[t,r]}function j1(e){var t=e.read_shift(4),r=e.l,a=!1;t>24&&(e.l+=t-24,e.read_shift(16)==="795881f43b1d7f48af2c825dc4852763"&&(a=!0),e.l=r);var n=e.read_shift((a?t-24:t)>>1,"utf16le").replace(yr,"");return a&&(e.l+=24),n}function $1(e){for(var t=e.read_shift(2),r="";t-- >0;)r+="../";var a=e.read_shift(0,"lpstr-ansi");if(e.l+=2,e.read_shift(2)!=57005)throw new Error("Bad FileMoniker");var n=e.read_shift(4);if(n===0)return r+a.replace(/\\/g,"/");var s=e.read_shift(4);if(e.read_shift(2)!=3)throw new Error("Bad FileMoniker");var i=e.read_shift(s>>1,"utf16le").replace(yr,"");return r+i}function X1(e,t){var r=e.read_shift(16);switch(r){case"e0c9ea79f9bace118c8200aa004ba90b":return j1(e);case"0303000000000000c000000000000046":return $1(e);default:throw new Error("Unsupported Moniker "+r)}}function Za(e){var t=e.read_shift(4),r=t>0?e.read_shift(t,"utf16le").replace(yr,""):"";return r}function Fi(e,t){t||(t=j(6+e.length*2)),t.write_shift(4,1+e.length);for(var r=0;r<e.length;++r)t.write_shift(2,e.charCodeAt(r));return t.write_shift(2,0),t}function Y1(e,t){var r=e.l+t,a=e.read_shift(4);if(a!==2)throw new Error("Unrecognized streamVersion: "+a);var n=e.read_shift(2);e.l+=2;var s,i,o,c,l="",f,p;n&16&&(s=Za(e,r-e.l)),n&128&&(i=Za(e,r-e.l)),(n&257)===257&&(o=Za(e,r-e.l)),(n&257)===1&&(c=X1(e,r-e.l)),n&8&&(l=Za(e,r-e.l)),n&32&&(f=e.read_shift(16)),n&64&&(p=Cs(e)),e.l=r;var h=i||o||c||"";h&&l&&(h+="#"+l),h||(h="#"+l),n&2&&h.charAt(0)=="/"&&h.charAt(1)!="/"&&(h="file://"+h);var d={Target:h};return f&&(d.guid=f),p&&(d.time=p),s&&(d.Tooltip=s),d}function K1(e){var t=j(512),r=0,a=e.Target;a.slice(0,7)=="file://"&&(a=a.slice(7));var n=a.indexOf("#"),s=n>-1?31:23;switch(a.charAt(0)){case"#":s=28;break;case".":s&=-3;break}t.write_shift(4,2),t.write_shift(4,s);var i=[8,6815827,6619237,4849780,83];for(r=0;r<i.length;++r)t.write_shift(4,i[r]);if(s==28)a=a.slice(1),Fi(a,t);else if(s&2){for(i="e0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),r=0;r<i.length;++r)t.write_shift(1,parseInt(i[r],16));var o=n>-1?a.slice(0,n):a;for(t.write_shift(4,2*(o.length+1)),r=0;r<o.length;++r)t.write_shift(2,o.charCodeAt(r));t.write_shift(2,0),s&8&&Fi(n>-1?a.slice(n+1):"",t)}else{for(i="03 03 00 00 00 00 00 00 c0 00 00 00 00 00 00 46".split(" "),r=0;r<i.length;++r)t.write_shift(1,parseInt(i[r],16));for(var c=0;a.slice(c*3,c*3+3)=="../"||a.slice(c*3,c*3+3)=="..\\";)++c;for(t.write_shift(2,c),t.write_shift(4,a.length-3*c+1),r=0;r<a.length-3*c;++r)t.write_shift(1,a.charCodeAt(r+3*c)&255);for(t.write_shift(1,0),t.write_shift(2,65535),t.write_shift(2,57005),r=0;r<6;++r)t.write_shift(4,0)}return t.slice(0,t.l)}function hc(e){var t=e.read_shift(1),r=e.read_shift(1),a=e.read_shift(1),n=e.read_shift(1);return[t,r,a,n]}function uc(e,t){var r=hc(e);return r[3]=0,r}function et(e){var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(2);return{r:t,c:r,ixfe:a}}function Dt(e,t,r,a){return a||(a=j(6)),a.write_shift(2,e),a.write_shift(2,t),a.write_shift(2,r||0),a}function J1(e){var t=e.read_shift(2),r=e.read_shift(2);return e.l+=8,{type:t,flags:r}}function Z1(e,t,r){return t===0?"":Ht(e,t,r)}function q1(e,t,r){var a=r.biff>8?4:2,n=e.read_shift(a),s=e.read_shift(a,"i"),i=e.read_shift(a,"i");return[n,s,i]}function dc(e){var t=e.read_shift(2),r=_s(e);return[t,r]}function Q1(e,t,r){e.l+=4,t-=4;var a=e.l+t,n=Wa(e,t,r),s=e.read_shift(2);if(a-=e.l,s!==a)throw new Error("Malformed AddinUdf: padding = "+a+" != "+s);return e.l+=s,n}function En(e){var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(2),n=e.read_shift(2);return{s:{c:a,r:t},e:{c:n,r}}}function pc(e,t){return t||(t=j(8)),t.write_shift(2,e.s.r),t.write_shift(2,e.e.r),t.write_shift(2,e.s.c),t.write_shift(2,e.e.c),t}function mc(e){var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(1),n=e.read_shift(1);return{s:{c:a,r:t},e:{c:n,r}}}var eh=mc;function bc(e){e.l+=4;var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(2);return e.l+=12,[r,t,a]}function rh(e){var t={};return e.l+=4,e.l+=16,t.fSharedNote=e.read_shift(2),e.l+=4,t}function th(e){var t={};return e.l+=4,e.cf=e.read_shift(2),t}function hr(e){e.l+=2,e.l+=e.read_shift(2)}var ah={0:hr,4:hr,5:hr,6:hr,7:th,8:hr,9:hr,10:hr,11:hr,12:hr,13:rh,14:hr,15:hr,16:hr,17:hr,18:hr,19:hr,20:hr,21:bc};function nh(e,t){for(var r=e.l+t,a=[];e.l<r;){var n=e.read_shift(2);e.l-=2;try{a.push(ah[n](e,r-e.l))}catch{return e.l=r,a}}return e.l!=r&&(e.l=r),a}function qa(e,t){var r={BIFFVer:0,dt:0};switch(r.BIFFVer=e.read_shift(2),t-=2,t>=2&&(r.dt=e.read_shift(2),e.l-=2),r.BIFFVer){case 1536:case 1280:case 1024:case 768:case 512:case 2:case 7:break;default:if(t>6)throw new Error("Unexpected BIFF Ver "+r.BIFFVer)}return e.read_shift(t),r}function Rs(e,t,r){var a=1536,n=16;switch(r.bookType){case"biff8":break;case"biff5":a=1280,n=8;break;case"biff4":a=4,n=6;break;case"biff3":a=3,n=6;break;case"biff2":a=2,n=4;break;case"xla":break;default:throw new Error("unsupported BIFF version")}var s=j(n);return s.write_shift(2,a),s.write_shift(2,t),n>4&&s.write_shift(2,29282),n>6&&s.write_shift(2,1997),n>8&&(s.write_shift(2,49161),s.write_shift(2,1),s.write_shift(2,1798),s.write_shift(2,0)),s}function sh(e,t){return t===0||e.read_shift(2),1200}function ih(e,t,r){if(r.enc)return e.l+=t,"";var a=e.l,n=Ht(e,0,r);return e.read_shift(t+a-e.l),n}function oh(e,t){var r=!t||t.biff==8,a=j(r?112:54);for(a.write_shift(t.biff==8?2:1,7),r&&a.write_shift(1,0),a.write_shift(4,859007059),a.write_shift(4,5458548|(r?0:536870912));a.l<a.length;)a.write_shift(1,r?0:32);return a}function ch(e,t,r){var a=r&&r.biff==8||t==2?e.read_shift(2):(e.l+=t,0);return{fDialog:a&16,fBelow:a&64,fRight:a&128}}function lh(e,t,r){var a=e.read_shift(4),n=e.read_shift(1)&3,s=e.read_shift(1);switch(s){case 0:s="Worksheet";break;case 1:s="Macrosheet";break;case 2:s="Chartsheet";break;case 6:s="VBAModule";break}var i=Wa(e,0,r);return i.length===0&&(i="Sheet1"),{pos:a,hs:n,dt:s,name:i}}function fh(e,t){var r=!t||t.biff>=8?2:1,a=j(8+r*e.name.length);a.write_shift(4,e.pos),a.write_shift(1,e.hs||0),a.write_shift(1,e.dt),a.write_shift(1,e.name.length),t.biff>=8&&a.write_shift(1,1),a.write_shift(r*e.name.length,e.name,t.biff<8?"sbcs":"utf16le");var n=a.slice(0,a.l);return n.l=a.l,n}function hh(e,t){for(var r=e.l+t,a=e.read_shift(4),n=e.read_shift(4),s=[],i=0;i!=n&&e.l<r;++i)s.push(H1(e));return s.Count=a,s.Unique=n,s}function uh(e,t){var r=j(8);r.write_shift(4,e.Count),r.write_shift(4,e.Unique);for(var a=[],n=0;n<e.length;++n)a[n]=G1(e[n]);var s=ir([r].concat(a));return s.parts=[r.length].concat(a.map(function(i){return i.length})),s}function dh(e,t){var r={};return r.dsst=e.read_shift(2),e.l+=t-2,r}function ph(e){var t={};t.r=e.read_shift(2),t.c=e.read_shift(2),t.cnt=e.read_shift(2)-t.c;var r=e.read_shift(2);e.l+=4;var a=e.read_shift(1);return e.l+=3,a&7&&(t.level=a&7),a&32&&(t.hidden=!0),a&64&&(t.hpt=r/20),t}function mh(e){var t=J1(e);if(t.type!=2211)throw new Error("Invalid Future Record "+t.type);var r=e.read_shift(4);return r!==0}function bh(e){return e.read_shift(2),e.read_shift(4)}function Pi(e,t,r){var a=0;r&&r.biff==2||(a=e.read_shift(2));var n=e.read_shift(2);r&&r.biff==2&&(a=1-(n>>15),n&=32767);var s={Unsynced:a&1,DyZero:(a&2)>>1,ExAsc:(a&4)>>2,ExDsc:(a&8)>>3};return[s,n]}function vh(e){var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(2),n=e.read_shift(2),s=e.read_shift(2),i=e.read_shift(2),o=e.read_shift(2),c=e.read_shift(2),l=e.read_shift(2);return{Pos:[t,r],Dim:[a,n],Flags:s,CurTab:i,FirstTab:o,Selected:c,TabRatio:l}}function gh(){var e=j(18);return e.write_shift(2,0),e.write_shift(2,0),e.write_shift(2,29280),e.write_shift(2,17600),e.write_shift(2,56),e.write_shift(2,0),e.write_shift(2,0),e.write_shift(2,1),e.write_shift(2,500),e}function wh(e,t,r){if(r&&r.biff>=2&&r.biff<5)return{};var a=e.read_shift(2);return{RTL:a&64}}function kh(e){var t=j(18),r=1718;return e&&e.RTL&&(r|=64),t.write_shift(2,r),t.write_shift(4,0),t.write_shift(4,64),t.write_shift(4,0),t.write_shift(4,0),t}function Th(){}function Eh(e,t,r){var a={dyHeight:e.read_shift(2),fl:e.read_shift(2)};switch(r&&r.biff||8){case 2:break;case 3:case 4:e.l+=2;break;default:e.l+=10;break}return a.name=Wa(e,0,r),a}function yh(e,t){var r=e.name||"Arial",a=t&&t.biff==5,n=a?15+r.length:16+2*r.length,s=j(n);return s.write_shift(2,e.sz*20),s.write_shift(4,0),s.write_shift(2,400),s.write_shift(4,0),s.write_shift(2,0),s.write_shift(1,r.length),a||s.write_shift(1,1),s.write_shift((a?1:2)*r.length,r,a?"sbcs":"utf16le"),s}function Sh(e){var t=et(e);return t.isst=e.read_shift(4),t}function _h(e,t,r,a){var n=j(10);return Dt(e,t,a,n),n.write_shift(4,r),n}function xh(e,t,r){r.biffguess&&r.biff==2&&(r.biff=5);var a=e.l+t,n=et(e);r.biff==2&&e.l++;var s=za(e,a-e.l,r);return n.val=s,n}function Ah(e,t,r,a,n){var s=!n||n.biff==8,i=j(8+ +s+(1+s)*r.length);return Dt(e,t,a,i),i.write_shift(2,r.length),s&&i.write_shift(1,1),i.write_shift((1+s)*r.length,r,s?"utf16le":"sbcs"),i}function Ch(e,t,r){var a=e.read_shift(2),n=Ht(e,0,r);return[a,n]}function Rh(e,t,r,a){var n=r&&r.biff==5;a||(a=j(n?3+t.length:5+2*t.length)),a.write_shift(2,e),a.write_shift(n?1:2,t.length),n||a.write_shift(1,1),a.write_shift((n?1:2)*t.length,t,n?"sbcs":"utf16le");var s=a.length>a.l?a.slice(0,a.l):a;return s.l==null&&(s.l=s.length),s}var Oh=Ht;function Di(e,t,r){var a=e.l+t,n=r.biff==8||!r.biff?4:2,s=e.read_shift(n),i=e.read_shift(n),o=e.read_shift(2),c=e.read_shift(2);return e.l=a,{s:{r:s,c:o},e:{r:i,c}}}function Nh(e,t){var r=t.biff==8||!t.biff?4:2,a=j(2*r+6);return a.write_shift(r,e.s.r),a.write_shift(r,e.e.r+1),a.write_shift(2,e.s.c),a.write_shift(2,e.e.c+1),a.write_shift(2,0),a}function Ih(e){var t=e.read_shift(2),r=e.read_shift(2),a=dc(e);return{r:t,c:r,ixfe:a[0],rknum:a[1]}}function Fh(e,t){for(var r=e.l+t-2,a=e.read_shift(2),n=e.read_shift(2),s=[];e.l<r;)s.push(dc(e));if(e.l!==r)throw new Error("MulRK read error");var i=e.read_shift(2);if(s.length!=i-n+1)throw new Error("MulRK length mismatch");return{r:a,c:n,C:i,rkrec:s}}function Ph(e,t){for(var r=e.l+t-2,a=e.read_shift(2),n=e.read_shift(2),s=[];e.l<r;)s.push(e.read_shift(2));if(e.l!==r)throw new Error("MulBlank read error");var i=e.read_shift(2);if(s.length!=i-n+1)throw new Error("MulBlank length mismatch");return{r:a,c:n,C:i,ixfe:s}}function Dh(e,t,r,a){var n={},s=e.read_shift(4),i=e.read_shift(4),o=e.read_shift(4),c=e.read_shift(2);return n.patternType=m1[o>>26],a.cellStyles&&(n.alc=s&7,n.fWrap=s>>3&1,n.alcV=s>>4&7,n.fJustLast=s>>7&1,n.trot=s>>8&255,n.cIndent=s>>16&15,n.fShrinkToFit=s>>20&1,n.iReadOrder=s>>22&2,n.fAtrNum=s>>26&1,n.fAtrFnt=s>>27&1,n.fAtrAlc=s>>28&1,n.fAtrBdr=s>>29&1,n.fAtrPat=s>>30&1,n.fAtrProt=s>>31&1,n.dgLeft=i&15,n.dgRight=i>>4&15,n.dgTop=i>>8&15,n.dgBottom=i>>12&15,n.icvLeft=i>>16&127,n.icvRight=i>>23&127,n.grbitDiag=i>>30&3,n.icvTop=o&127,n.icvBottom=o>>7&127,n.icvDiag=o>>14&127,n.dgDiag=o>>21&15,n.icvFore=c&127,n.icvBack=c>>7&127,n.fsxButton=c>>14&1),n}function Lh(e,t,r){var a={};return a.ifnt=e.read_shift(2),a.numFmtId=e.read_shift(2),a.flags=e.read_shift(2),a.fStyle=a.flags>>2&1,t-=6,a.data=Dh(e,t,a.fStyle,r),a}function Li(e,t,r,a){var n=r&&r.biff==5;a||(a=j(n?16:20)),a.write_shift(2,0),e.style?(a.write_shift(2,e.numFmtId||0),a.write_shift(2,65524)):(a.write_shift(2,e.numFmtId||0),a.write_shift(2,t<<4));var s=0;return e.numFmtId>0&&n&&(s|=1024),a.write_shift(4,s),a.write_shift(4,0),n||a.write_shift(4,0),a.write_shift(2,0),a}function Mh(e){e.l+=4;var t=[e.read_shift(2),e.read_shift(2)];if(t[0]!==0&&t[0]--,t[1]!==0&&t[1]--,t[0]>7||t[1]>7)throw new Error("Bad Gutters: "+t.join("|"));return t}function Uh(e){var t=j(8);return t.write_shift(4,0),t.write_shift(2,0),t.write_shift(2,0),t}function Mi(e,t,r){var a=et(e);(r.biff==2||t==9)&&++e.l;var n=z1(e);return a.val=n,a.t=n===!0||n===!1?"b":"e",a}function Bh(e,t,r,a,n,s){var i=j(8);return Dt(e,t,a,i),lc(r,s,i),i}function Wh(e,t,r){r.biffguess&&r.biff==2&&(r.biff=5);var a=et(e),n=mr(e);return a.val=n,a}function zh(e,t,r,a){var n=j(14);return Dt(e,t,a,n),Ft(r,n),n}var Ui=Z1;function Hh(e,t,r){var a=e.l+t,n=e.read_shift(2),s=e.read_shift(2);if(r.sbcch=s,s==1025||s==14849)return[s,n];if(s<1||s>255)throw new Error("Unexpected SupBook type: "+s);for(var i=Pt(e,s),o=[];a>e.l;)o.push(za(e));return[s,n,i,o]}function Bi(e,t,r){var a=e.read_shift(2),n,s={fBuiltIn:a&1,fWantAdvise:a>>>1&1,fWantPict:a>>>2&1,fOle:a>>>3&1,fOleLink:a>>>4&1,cf:a>>>5&1023,fIcon:a>>>15&1};return r.sbcch===14849&&(n=Q1(e,t-2,r)),s.body=n||e.read_shift(t-2),typeof n=="string"&&(s.Name=n),s}var Gh=["_xlnm.Consolidate_Area","_xlnm.Auto_Open","_xlnm.Auto_Close","_xlnm.Extract","_xlnm.Database","_xlnm.Criteria","_xlnm.Print_Area","_xlnm.Print_Titles","_xlnm.Recorder","_xlnm.Data_Form","_xlnm.Auto_Activate","_xlnm.Auto_Deactivate","_xlnm.Sheet_Title","_xlnm._FilterDatabase"];function Wi(e,t,r){var a=e.l+t,n=e.read_shift(2),s=e.read_shift(1),i=e.read_shift(1),o=e.read_shift(r&&r.biff==2?1:2),c=0;(!r||r.biff>=5)&&(r.biff!=5&&(e.l+=2),c=e.read_shift(2),r.biff==5&&(e.l+=2),e.l+=4);var l=Pt(e,i,r);n&32&&(l=Gh[l.charCodeAt(0)]);var f=a-e.l;r&&r.biff==2&&--f;var p=a==e.l||o===0||!(f>0)?[]:np(e,f,r,o);return{chKey:s,Name:l,itab:c,rgce:p}}function vc(e,t,r){if(r.biff<8)return Vh(e,t,r);for(var a=[],n=e.l+t,s=e.read_shift(r.biff>8?4:2);s--!==0;)a.push(q1(e,r.biff>8?12:6,r));if(e.l!=n)throw new Error("Bad ExternSheet: "+e.l+" != "+n);return a}function Vh(e,t,r){e[e.l+1]==3&&e[e.l]++;var a=Wa(e,t,r);return a.charCodeAt(0)==3?a.slice(1):a}function jh(e,t,r){if(r.biff<8){e.l+=t;return}var a=e.read_shift(2),n=e.read_shift(2),s=Pt(e,a,r),i=Pt(e,n,r);return[s,i]}function $h(e,t,r){var a=mc(e);e.l++;var n=e.read_shift(1);return t-=8,[sp(e,t,r),n,a]}function zi(e,t,r){var a=eh(e);switch(r.biff){case 2:e.l++,t-=7;break;case 3:case 4:e.l+=2,t-=8;break;default:e.l+=6,t-=12}return[a,tp(e,t,r)]}function Xh(e){var t=e.read_shift(4)!==0,r=e.read_shift(4)!==0,a=e.read_shift(4);return[t,r,a]}function Yh(e,t,r){if(!(r.biff<8)){var a=e.read_shift(2),n=e.read_shift(2),s=e.read_shift(2),i=e.read_shift(2),o=Ht(e,0,r);return r.biff<8&&e.read_shift(1),[{r:a,c:n},o,i,s]}}function Kh(e,t,r){return Yh(e,t,r)}function Jh(e,t){for(var r=[],a=e.read_shift(2);a--;)r.push(En(e));return r}function Zh(e){var t=j(2+e.length*8);t.write_shift(2,e.length);for(var r=0;r<e.length;++r)pc(e[r],t);return t}function qh(e,t,r){if(r&&r.biff<8)return eu(e,t,r);var a=bc(e),n=nh(e,t-22,a[1]);return{cmo:a,ft:n}}var Qh={8:function(e,t){var r=e.l+t;e.l+=10;var a=e.read_shift(2);e.l+=4,e.l+=2,e.l+=2,e.l+=2,e.l+=4;var n=e.read_shift(1);return e.l+=n,e.l=r,{fmt:a}}};function eu(e,t,r){e.l+=4;var a=e.read_shift(2),n=e.read_shift(2),s=e.read_shift(2);e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=6,t-=36;var i=[];return i.push((Qh[a]||vr)(e,t,r)),{cmo:[n,a,s],ft:i}}function ru(e,t,r){var a=e.l,n="";try{e.l+=4;var s=(r.lastobj||{cmo:[0,0]}).cmo[1],i;[0,5,7,11,12,14].indexOf(s)==-1?e.l+=6:i=V1(e,6,r);var o=e.read_shift(2);e.read_shift(2),er(e,2);var c=e.read_shift(2);e.l+=c;for(var l=1;l<e.lens.length-1;++l){if(e.l-a!=e.lens[l])throw new Error("TxO: bad continue record");var f=e[e.l],p=Pt(e,e.lens[l+1]-e.lens[l]-1);if(n+=p,n.length>=(f?o:2*o))break}if(n.length!==o&&n.length!==o*2)throw new Error("cchText: "+o+" != "+n.length);return e.l=a+t,{t:n}}catch{return e.l=a+t,{t:n}}}function tu(e,t){var r=En(e);e.l+=16;var a=Y1(e,t-24);return[r,a]}function au(e){var t=j(24),r=je(e[0]);t.write_shift(2,r.r),t.write_shift(2,r.r),t.write_shift(2,r.c),t.write_shift(2,r.c);for(var a="d0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),n=0;n<16;++n)t.write_shift(1,parseInt(a[n],16));return ir([t,K1(e[1])])}function nu(e,t){e.read_shift(2);var r=En(e),a=e.read_shift((t-10)/2,"dbcs-cont");return a=a.replace(yr,""),[r,a]}function su(e){var t=e[1].Tooltip,r=j(10+2*(t.length+1));r.write_shift(2,2048);var a=je(e[0]);r.write_shift(2,a.r),r.write_shift(2,a.r),r.write_shift(2,a.c),r.write_shift(2,a.c);for(var n=0;n<t.length;++n)r.write_shift(2,t.charCodeAt(n));return r.write_shift(2,0),r}function iu(e){var t=[0,0],r;return r=e.read_shift(2),t[0]=_i[r]||r,r=e.read_shift(2),t[1]=_i[r]||r,t}function ou(e){return e||(e=j(4)),e.write_shift(2,1),e.write_shift(2,1),e}function cu(e){for(var t=e.read_shift(2),r=[];t-- >0;)r.push(uc(e));return r}function lu(e){for(var t=e.read_shift(2),r=[];t-- >0;)r.push(uc(e));return r}function fu(e){e.l+=2;var t={cxfs:0,crc:0};return t.cxfs=e.read_shift(2),t.crc=e.read_shift(4),t}function gc(e,t,r){if(!r.cellStyles)return vr(e,t);var a=r&&r.biff>=12?4:2,n=e.read_shift(a),s=e.read_shift(a),i=e.read_shift(a),o=e.read_shift(a),c=e.read_shift(2);a==2&&(e.l+=2);var l={s:n,e:s,w:i,ixfe:o,flags:c};return(r.biff>=5||!r.biff)&&(l.level=c>>8&7),l}function hu(e,t){var r=j(12);r.write_shift(2,t),r.write_shift(2,t),r.write_shift(2,e.width*256),r.write_shift(2,0);var a=0;return e.hidden&&(a|=1),r.write_shift(1,a),a=e.level||0,r.write_shift(1,a),r.write_shift(2,0),r}function uu(e,t){var r={};return t<32||(e.l+=16,r.header=mr(e),r.footer=mr(e),e.l+=2),r}function du(e,t,r){var a={area:!1};if(r.biff!=5)return e.l+=t,a;var n=e.read_shift(1);return e.l+=3,n&16&&(a.area=!0),a}function pu(e){for(var t=j(2*e),r=0;r<e;++r)t.write_shift(2,r+1);return t}var mu=et,bu=cc,vu=za;function gu(e){var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(4),n={fmt:t,env:r,len:a,data:e.slice(e.l,e.l+a)};return e.l+=a,n}function wu(e,t,r){r.biffguess&&r.biff==5&&(r.biff=2);var a=et(e);++e.l;var n=Ht(e,t-7,r);return a.t="str",a.val=n,a}function ku(e){var t=et(e);++e.l;var r=mr(e);return t.t="n",t.val=r,t}function Tu(e,t,r){var a=j(15);return ja(a,e,t),a.write_shift(8,r,"f"),a}function Eu(e){var t=et(e);++e.l;var r=e.read_shift(2);return t.t="n",t.val=r,t}function yu(e,t,r){var a=j(9);return ja(a,e,t),a.write_shift(2,r),a}function Su(e){var t=e.read_shift(1);return t===0?(e.l++,""):e.read_shift(t,"sbcs-cont")}function _u(e,t){e.l+=6,e.l+=2,e.l+=1,e.l+=3,e.l+=1,e.l+=t-13}function xu(e,t,r){var a=e.l+t,n=et(e),s=e.read_shift(2),i=Pt(e,s,r);return e.l=a,n.t="str",n.val=i,n}var Au=[2,3,48,49,131,139,140,245],Qn=function(){var e={1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127,8:865,9:437,10:850,11:437,13:437,14:850,15:437,16:850,17:437,18:850,19:932,20:850,21:437,22:850,23:865,24:437,25:437,26:850,27:437,28:863,29:850,31:852,34:852,35:852,36:860,37:850,38:866,55:850,64:852,77:936,78:949,79:950,80:874,87:1252,88:1252,89:1252,108:863,134:737,135:852,136:857,204:1257,255:16969},t=wn({1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127});function r(o,c){var l=[],f=vt(1);switch(c.type){case"base64":f=Or(Ir(o));break;case"binary":f=Or(o);break;case"buffer":case"array":f=o;break}lr(f,0);var p=f.read_shift(1),h=!!(p&136),d=!1,m=!1;switch(p){case 2:break;case 3:break;case 48:d=!0,h=!0;break;case 49:d=!0,h=!0;break;case 131:break;case 139:break;case 140:m=!0;break;case 245:break;default:throw new Error("DBF Unsupported Version: "+p.toString(16))}var u=0,b=521;p==2&&(u=f.read_shift(2)),f.l+=3,p!=2&&(u=f.read_shift(4)),u>1048576&&(u=1e6),p!=2&&(b=f.read_shift(2));var T=f.read_shift(2),S=c.codepage||1252;p!=2&&(f.l+=16,f.read_shift(1),f[f.l]!==0&&(S=e[f[f.l]]),f.l+=1,f.l+=2),m&&(f.l+=36);for(var g=[],x={},A=Math.min(f.length,p==2?521:b-10-(d?264:0)),P=m?32:11;f.l<A&&f[f.l]!=13;)switch(x={},x.name=jn.utils.decode(S,f.slice(f.l,f.l+P)).replace(/[\u0000\r\n].*$/g,""),f.l+=P,x.type=String.fromCharCode(f.read_shift(1)),p!=2&&!m&&(x.offset=f.read_shift(4)),x.len=f.read_shift(1),p==2&&(x.offset=f.read_shift(2)),x.dec=f.read_shift(1),x.name.length&&g.push(x),p!=2&&(f.l+=m?13:14),x.type){case"B":(!d||x.len!=8)&&c.WTF&&""+x.name+x.type;break;case"G":case"P":c.WTF&&""+x.name+x.type;break;case"+":case"0":case"@":case"C":case"D":case"F":case"I":case"L":case"M":case"N":case"O":case"T":case"Y":break;default:throw new Error("Unknown Field Type: "+x.type)}if(f[f.l]!==13&&(f.l=b-1),f.read_shift(1)!==13)throw new Error("DBF Terminator not found "+f.l+" "+f[f.l]);f.l=b;var R=0,U=0;for(l[0]=[],U=0;U!=g.length;++U)l[0][U]=g[U].name;for(;u-- >0;){if(f[f.l]===42){f.l+=T;continue}for(++f.l,l[++R]=[],U=0,U=0;U!=g.length;++U){var I=f.slice(f.l,f.l+g[U].len);f.l+=g[U].len,lr(I,0);var G=jn.utils.decode(S,I);switch(g[U].type){case"C":G.trim().length&&(l[R][U]=G.replace(/\s+$/,""));break;case"D":G.length===8?l[R][U]=new Date(+G.slice(0,4),+G.slice(4,6)-1,+G.slice(6,8)):l[R][U]=G;break;case"F":l[R][U]=parseFloat(G.trim());break;case"+":case"I":l[R][U]=m?I.read_shift(-4,"i")^2147483648:I.read_shift(4,"i");break;case"L":switch(G.trim().toUpperCase()){case"Y":case"T":l[R][U]=!0;break;case"N":case"F":l[R][U]=!1;break;case"":case"?":break;default:throw new Error("DBF Unrecognized L:|"+G+"|")}break;case"M":if(!h)throw new Error("DBF Unexpected MEMO for type "+p.toString(16));l[R][U]="##MEMO##"+(m?parseInt(G.trim(),10):I.read_shift(4));break;case"N":G=G.replace(/\u0000/g,"").trim(),G&&G!="."&&(l[R][U]=+G||0);break;case"@":l[R][U]=new Date(I.read_shift(-8,"f")-621356832e5);break;case"T":l[R][U]=new Date((I.read_shift(4)-2440588)*864e5+I.read_shift(4));break;case"Y":l[R][U]=I.read_shift(4,"i")/1e4+I.read_shift(4,"i")/1e4*Math.pow(2,32);break;case"O":l[R][U]=-I.read_shift(-8,"f");break;case"B":if(d&&g[U].len==8){l[R][U]=I.read_shift(8,"f");break}case"G":case"P":I.l+=g[U].len;break;case"0":if(g[U].name==="_NullFlags")break;default:throw new Error("DBF Unsupported data type "+g[U].type)}}}if(p!=2&&f.l<f.length&&f[f.l++]!=26)throw new Error("DBF EOF Marker missing "+(f.l-1)+" of "+f.length+" "+f[f.l-1].toString(16));return c&&c.sheetRows&&(l=l.slice(0,c.sheetRows)),c.DBF=g,l}function a(o,c){var l=c||{};l.dateNF||(l.dateNF="yyyymmdd");var f=ia(r(o,l),l);return f["!cols"]=l.DBF.map(function(p){return{wch:p.len,DBF:p}}),delete l.DBF,f}function n(o,c){try{return Et(a(o,c),c)}catch(l){if(c&&c.WTF)throw l}return{SheetNames:[],Sheets:{}}}var s={B:8,C:250,L:1,D:8,"?":0,"":0};function i(o,c){var l=c||{};if(+l.codepage>=0&&jr(+l.codepage),l.type=="string")throw new Error("Cannot write DBF to JS string");var f=_r(),p=bn(o,{header:1,raw:!0,cellDates:!0}),h=p[0],d=p.slice(1),m=o["!cols"]||[],u=0,b=0,T=0,S=1;for(u=0;u<h.length;++u){if(((m[u]||{}).DBF||{}).name){h[u]=m[u].DBF.name,++T;continue}if(h[u]!=null){if(++T,typeof h[u]=="number"&&(h[u]=h[u].toString(10)),typeof h[u]!="string")throw new Error("DBF Invalid column name "+h[u]+" |"+typeof h[u]+"|");if(h.indexOf(h[u])!==u){for(b=0;b<1024;++b)if(h.indexOf(h[u]+"_"+b)==-1){h[u]+="_"+b;break}}}}var g=Re(o["!ref"]),x=[],A=[],P=[];for(u=0;u<=g.e.c-g.s.c;++u){var R="",U="",I=0,G=[];for(b=0;b<d.length;++b)d[b][u]!=null&&G.push(d[b][u]);if(G.length==0||h[u]==null){x[u]="?";continue}for(b=0;b<G.length;++b){switch(typeof G[b]){case"number":U="B";break;case"string":U="C";break;case"boolean":U="L";break;case"object":U=G[b]instanceof Date?"D":"C";break;default:U="C"}I=Math.max(I,String(G[b]).length),R=R&&R!=U?"C":U}I>250&&(I=250),U=((m[u]||{}).DBF||{}).type,U=="C"&&m[u].DBF.len>I&&(I=m[u].DBF.len),R=="B"&&U=="N"&&(R="N",P[u]=m[u].DBF.dec,I=m[u].DBF.len),A[u]=R=="C"||U=="N"?I:s[R]||0,S+=A[u],x[u]=R}var W=f.next(32);for(W.write_shift(4,318902576),W.write_shift(4,d.length),W.write_shift(2,296+32*T),W.write_shift(2,S),u=0;u<4;++u)W.write_shift(4,0);for(W.write_shift(4,0|(+t[io]||3)<<8),u=0,b=0;u<h.length;++u)if(h[u]!=null){var D=f.next(32),te=(h[u].slice(-10)+"\0\0\0\0\0\0\0\0\0\0\0").slice(0,11);D.write_shift(1,te,"sbcs"),D.write_shift(1,x[u]=="?"?"C":x[u],"sbcs"),D.write_shift(4,b),D.write_shift(1,A[u]||s[x[u]]||0),D.write_shift(1,P[u]||0),D.write_shift(1,2),D.write_shift(4,0),D.write_shift(1,0),D.write_shift(4,0),D.write_shift(4,0),b+=A[u]||s[x[u]]||0}var fe=f.next(264);for(fe.write_shift(4,13),u=0;u<65;++u)fe.write_shift(4,0);for(u=0;u<d.length;++u){var se=f.next(S);for(se.write_shift(1,0),b=0;b<h.length;++b)if(h[b]!=null)switch(x[b]){case"L":se.write_shift(1,d[u][b]==null?63:d[u][b]?84:70);break;case"B":se.write_shift(8,d[u][b]||0,"f");break;case"N":var de="0";for(typeof d[u][b]=="number"&&(de=d[u][b].toFixed(P[b]||0)),T=0;T<A[b]-de.length;++T)se.write_shift(1,32);se.write_shift(1,de,"sbcs");break;case"D":d[u][b]?(se.write_shift(4,("0000"+d[u][b].getFullYear()).slice(-4),"sbcs"),se.write_shift(2,("00"+(d[u][b].getMonth()+1)).slice(-2),"sbcs"),se.write_shift(2,("00"+d[u][b].getDate()).slice(-2),"sbcs")):se.write_shift(8,"00000000","sbcs");break;case"C":var ue=String(d[u][b]!=null?d[u][b]:"").slice(0,A[b]);for(se.write_shift(1,ue,"sbcs"),T=0;T<A[b]-ue.length;++T)se.write_shift(1,32);break}}return f.next(1).write_shift(1,26),f.end()}return{to_workbook:n,to_sheet:a,from_sheet:i}}(),wc=function(){var e={AA:"À",BA:"Á",CA:"Â",DA:195,HA:"Ä",JA:197,AE:"È",BE:"É",CE:"Ê",HE:"Ë",AI:"Ì",BI:"Í",CI:"Î",HI:"Ï",AO:"Ò",BO:"Ó",CO:"Ô",DO:213,HO:"Ö",AU:"Ù",BU:"Ú",CU:"Û",HU:"Ü",Aa:"à",Ba:"á",Ca:"â",Da:227,Ha:"ä",Ja:229,Ae:"è",Be:"é",Ce:"ê",He:"ë",Ai:"ì",Bi:"í",Ci:"î",Hi:"ï",Ao:"ò",Bo:"ó",Co:"ô",Do:245,Ho:"ö",Au:"ù",Bu:"ú",Cu:"û",Hu:"ü",KC:"Ç",Kc:"ç",q:"æ",z:"œ",a:"Æ",j:"Œ",DN:209,Dn:241,Hy:255,S:169,c:170,R:174,"B ":180,0:176,1:177,2:178,3:179,5:181,6:182,7:183,Q:185,k:186,b:208,i:216,l:222,s:240,y:248,"!":161,'"':162,"#":163,"(":164,"%":165,"'":167,"H ":168,"+":171,";":187,"<":188,"=":189,">":190,"?":191,"{":223},t=new RegExp("\x1BN("+Ye(e).join("|").replace(/\|\|\|/,"|\\||").replace(/([?()+])/g,"\\$1")+"|\\|)","gm"),r=function(h,d){var m=e[d];return typeof m=="number"?qs(m):m},a=function(h,d,m){var u=d.charCodeAt(0)-32<<4|m.charCodeAt(0)-48;return u==59?h:qs(u)};e["|"]=254;function n(h,d){switch(d.type){case"base64":return s(Ir(h),d);case"binary":return s(h,d);case"buffer":return s(Se&&Buffer.isBuffer(h)?h.toString("binary"):Tt(h),d);case"array":return s(It(h),d)}throw new Error("Unrecognized type "+d.type)}function s(h,d){var m=h.split(/[\n\r]+/),u=-1,b=-1,T=0,S=0,g=[],x=[],A=null,P={},R=[],U=[],I=[],G=0,W;for(+d.codepage>=0&&jr(+d.codepage);T!==m.length;++T){G=0;var D=m[T].trim().replace(/\x1B([\x20-\x2F])([\x30-\x3F])/g,a).replace(t,r),te=D.replace(/;;/g,"\0").split(";").map(function(N){return N.replace(/\u0000/g,";")}),fe=te[0],se;if(D.length>0)switch(fe){case"ID":break;case"E":break;case"B":break;case"O":break;case"W":break;case"P":te[1].charAt(0)=="P"&&x.push(D.slice(3).replace(/;;/g,";"));break;case"C":var de=!1,ue=!1,K=!1,ee=!1,me=-1,Te=-1;for(S=1;S<te.length;++S)switch(te[S].charAt(0)){case"A":break;case"X":b=parseInt(te[S].slice(1))-1,ue=!0;break;case"Y":for(u=parseInt(te[S].slice(1))-1,ue||(b=0),W=g.length;W<=u;++W)g[W]=[];break;case"K":se=te[S].slice(1),se.charAt(0)==='"'?se=se.slice(1,se.length-1):se==="TRUE"?se=!0:se==="FALSE"?se=!1:isNaN(Xr(se))?isNaN(ea(se).getDate())||(se=We(se)):(se=Xr(se),A!==null&&na(A)&&(se=Tn(se))),de=!0;break;case"E":ee=!0;var C=qt(te[S].slice(1),{r:u,c:b});g[u][b]=[g[u][b],C];break;case"S":K=!0,g[u][b]=[g[u][b],"S5S"];break;case"G":break;case"R":me=parseInt(te[S].slice(1))-1;break;case"C":Te=parseInt(te[S].slice(1))-1;break;default:if(d&&d.WTF)throw new Error("SYLK bad record "+D)}if(de&&(g[u][b]&&g[u][b].length==2?g[u][b][0]=se:g[u][b]=se,A=null),K){if(ee)throw new Error("SYLK shared formula cannot have own formula");var L=me>-1&&g[me][Te];if(!L||!L[1])throw new Error("SYLK shared formula cannot find base");g[u][b][1]=Mc(L[1],{r:u-me,c:b-Te})}break;case"F":var F=0;for(S=1;S<te.length;++S)switch(te[S].charAt(0)){case"X":b=parseInt(te[S].slice(1))-1,++F;break;case"Y":for(u=parseInt(te[S].slice(1))-1,W=g.length;W<=u;++W)g[W]=[];break;case"M":G=parseInt(te[S].slice(1))/20;break;case"F":break;case"G":break;case"P":A=x[parseInt(te[S].slice(1))];break;case"S":break;case"D":break;case"N":break;case"W":for(I=te[S].slice(1).split(" "),W=parseInt(I[0],10);W<=parseInt(I[1],10);++W)G=parseInt(I[2],10),U[W-1]=G===0?{hidden:!0}:{wch:G},gt(U[W-1]);break;case"C":b=parseInt(te[S].slice(1))-1,U[b]||(U[b]={});break;case"R":u=parseInt(te[S].slice(1))-1,R[u]||(R[u]={}),G>0?(R[u].hpt=G,R[u].hpx=aa(G)):G===0&&(R[u].hidden=!0);break;default:if(d&&d.WTF)throw new Error("SYLK bad record "+D)}F<1&&(A=null);break;default:if(d&&d.WTF)throw new Error("SYLK bad record "+D)}}return R.length>0&&(P["!rows"]=R),U.length>0&&(P["!cols"]=U),d&&d.sheetRows&&(g=g.slice(0,d.sheetRows)),[g,P]}function i(h,d){var m=n(h,d),u=m[0],b=m[1],T=ia(u,d);return Ye(b).forEach(function(S){T[S]=b[S]}),T}function o(h,d){return Et(i(h,d),d)}function c(h,d,m,u){var b="C;Y"+(m+1)+";X"+(u+1)+";K";switch(h.t){case"n":b+=h.v||0,h.f&&!h.F&&(b+=";E"+Ds(h.f,{r:m,c:u}));break;case"b":b+=h.v?"TRUE":"FALSE";break;case"e":b+=h.w||h.v;break;case"d":b+='"'+(h.w||h.v)+'"';break;case"s":b+='"'+h.v.replace(/"/g,"").replace(/;/g,";;")+'"';break}return b}function l(h,d){d.forEach(function(m,u){var b="F;W"+(u+1)+" "+(u+1)+" ";m.hidden?b+="0":(typeof m.width=="number"&&!m.wpx&&(m.wpx=Ia(m.width)),typeof m.wpx=="number"&&!m.wch&&(m.wch=Fa(m.wpx)),typeof m.wch=="number"&&(b+=Math.round(m.wch))),b.charAt(b.length-1)!=" "&&h.push(b)})}function f(h,d){d.forEach(function(m,u){var b="F;";m.hidden?b+="M0;":m.hpt?b+="M"+20*m.hpt+";":m.hpx&&(b+="M"+20*Pa(m.hpx)+";"),b.length>2&&h.push(b+"R"+(u+1))})}function p(h,d){var m=["ID;PWXL;N;E"],u=[],b=Re(h["!ref"]),T,S=Array.isArray(h),g=`\r
`;m.push("P;PGeneral"),m.push("F;P0;DG0G8;M255"),h["!cols"]&&l(m,h["!cols"]),h["!rows"]&&f(m,h["!rows"]),m.push("B;Y"+(b.e.r-b.s.r+1)+";X"+(b.e.c-b.s.c+1)+";D"+[b.s.c,b.s.r,b.e.c,b.e.r].join(" "));for(var x=b.s.r;x<=b.e.r;++x)for(var A=b.s.c;A<=b.e.c;++A){var P=ve({r:x,c:A});T=S?(h[x]||[])[A]:h[P],!(!T||T.v==null&&(!T.f||T.F))&&u.push(c(T,h,x,A))}return m.join(g)+g+u.join(g)+g+"E"+g}return{to_workbook:o,to_sheet:i,from_sheet:p}}(),kc=function(){function e(s,i){switch(i.type){case"base64":return t(Ir(s),i);case"binary":return t(s,i);case"buffer":return t(Se&&Buffer.isBuffer(s)?s.toString("binary"):Tt(s),i);case"array":return t(It(s),i)}throw new Error("Unrecognized type "+i.type)}function t(s,i){for(var o=s.split(`
`),c=-1,l=-1,f=0,p=[];f!==o.length;++f){if(o[f].trim()==="BOT"){p[++c]=[],l=0;continue}if(!(c<0)){var h=o[f].trim().split(","),d=h[0],m=h[1];++f;for(var u=o[f]||"";(u.match(/["]/g)||[]).length&1&&f<o.length-1;)u+=`
`+o[++f];switch(u=u.trim(),+d){case-1:if(u==="BOT"){p[++c]=[],l=0;continue}else if(u!=="EOD")throw new Error("Unrecognized DIF special command "+u);break;case 0:u==="TRUE"?p[c][l]=!0:u==="FALSE"?p[c][l]=!1:isNaN(Xr(m))?isNaN(ea(m).getDate())?p[c][l]=m:p[c][l]=We(m):p[c][l]=Xr(m),++l;break;case 1:u=u.slice(1,u.length-1),u=u.replace(/""/g,'"'),u&&u.match(/^=".*"$/)&&(u=u.slice(2,-1)),p[c][l++]=u!==""?u:null;break}if(u==="EOD")break}}return i&&i.sheetRows&&(p=p.slice(0,i.sheetRows)),p}function r(s,i){return ia(e(s,i),i)}function a(s,i){return Et(r(s,i),i)}var n=function(){var s=function(o,c,l,f,p){o.push(c),o.push(l+","+f),o.push('"'+p.replace(/"/g,'""')+'"')},i=function(o,c,l,f){o.push(c+","+l),o.push(c==1?'"'+f.replace(/"/g,'""')+'"':f)};return function(o){var c=[],l=Re(o["!ref"]),f,p=Array.isArray(o);s(c,"TABLE",0,1,"sheetjs"),s(c,"VECTORS",0,l.e.r-l.s.r+1,""),s(c,"TUPLES",0,l.e.c-l.s.c+1,""),s(c,"DATA",0,0,"");for(var h=l.s.r;h<=l.e.r;++h){i(c,-1,0,"BOT");for(var d=l.s.c;d<=l.e.c;++d){var m=ve({r:h,c:d});if(f=p?(o[h]||[])[d]:o[m],!f){i(c,1,0,"");continue}switch(f.t){case"n":var u=f.w;!u&&f.v!=null&&(u=f.v),u==null?f.f&&!f.F?i(c,1,0,"="+f.f):i(c,1,0,""):i(c,0,u,"V");break;case"b":i(c,0,f.v?1:0,f.v?"TRUE":"FALSE");break;case"s":i(c,1,0,isNaN(f.v)?f.v:'="'+f.v+'"');break;case"d":f.w||(f.w=zr(f.z||be[14],nr(We(f.v)))),i(c,0,f.w,"V");break;default:i(c,1,0,"")}}}i(c,-1,0,"EOD");var b=`\r
`,T=c.join(b);return T}}();return{to_workbook:a,to_sheet:r,from_sheet:n}}(),Tc=function(){function e(p){return p.replace(/\\b/g,"\\").replace(/\\c/g,":").replace(/\\n/g,`
`)}function t(p){return p.replace(/\\/g,"\\b").replace(/:/g,"\\c").replace(/\n/g,"\\n")}function r(p,h){for(var d=p.split(`
`),m=-1,u=-1,b=0,T=[];b!==d.length;++b){var S=d[b].trim().split(":");if(S[0]==="cell"){var g=je(S[1]);if(T.length<=g.r)for(m=T.length;m<=g.r;++m)T[m]||(T[m]=[]);switch(m=g.r,u=g.c,S[2]){case"t":T[m][u]=e(S[3]);break;case"v":T[m][u]=+S[3];break;case"vtf":var x=S[S.length-1];case"vtc":switch(S[3]){case"nl":T[m][u]=!!+S[4];break;default:T[m][u]=+S[4];break}S[2]=="vtf"&&(T[m][u]=[T[m][u],x])}}}return h&&h.sheetRows&&(T=T.slice(0,h.sheetRows)),T}function a(p,h){return ia(r(p,h),h)}function n(p,h){return Et(a(p,h),h)}var s=["socialcalc:version:1.5","MIME-Version: 1.0","Content-Type: multipart/mixed; boundary=SocialCalcSpreadsheetControlSave"].join(`
`),i=["--SocialCalcSpreadsheetControlSave","Content-type: text/plain; charset=UTF-8"].join(`
`)+`
`,o=["# SocialCalc Spreadsheet Control Save","part:sheet"].join(`
`),c="--SocialCalcSpreadsheetControlSave--";function l(p){if(!p||!p["!ref"])return"";for(var h=[],d=[],m,u="",b=xr(p["!ref"]),T=Array.isArray(p),S=b.s.r;S<=b.e.r;++S)for(var g=b.s.c;g<=b.e.c;++g)if(u=ve({r:S,c:g}),m=T?(p[S]||[])[g]:p[u],!(!m||m.v==null||m.t==="z")){switch(d=["cell",u,"t"],m.t){case"s":case"str":d.push(t(m.v));break;case"n":m.f?(d[2]="vtf",d[3]="n",d[4]=m.v,d[5]=t(m.f)):(d[2]="v",d[3]=m.v);break;case"b":d[2]="vt"+(m.f?"f":"c"),d[3]="nl",d[4]=m.v?"1":"0",d[5]=t(m.f||(m.v?"TRUE":"FALSE"));break;case"d":var x=nr(We(m.v));d[2]="vtc",d[3]="nd",d[4]=""+x,d[5]=m.w||zr(m.z||be[14],x);break;case"e":continue}h.push(d.join(":"))}return h.push("sheet:c:"+(b.e.c-b.s.c+1)+":r:"+(b.e.r-b.s.r+1)+":tvf:1"),h.push("valueformat:1:text-wiki"),h.join(`
`)}function f(p){return[s,i,o,i,l(p),c].join(`
`)}return{to_workbook:n,to_sheet:a,from_sheet:f}}(),ra=function(){function e(f,p,h,d,m){m.raw?p[h][d]=f:f===""||(f==="TRUE"?p[h][d]=!0:f==="FALSE"?p[h][d]=!1:isNaN(Xr(f))?isNaN(ea(f).getDate())?p[h][d]=f:p[h][d]=We(f):p[h][d]=Xr(f))}function t(f,p){var h=p||{},d=[];if(!f||f.length===0)return d;for(var m=f.split(/[\r\n]/),u=m.length-1;u>=0&&m[u].length===0;)--u;for(var b=10,T=0,S=0;S<=u;++S)T=m[S].indexOf(" "),T==-1?T=m[S].length:T++,b=Math.max(b,T);for(S=0;S<=u;++S){d[S]=[];var g=0;for(e(m[S].slice(0,b).trim(),d,S,g,h),g=1;g<=(m[S].length-b)/10+1;++g)e(m[S].slice(b+(g-1)*10,b+g*10).trim(),d,S,g,h)}return h.sheetRows&&(d=d.slice(0,h.sheetRows)),d}var r={44:",",9:"	",59:";",124:"|"},a={44:3,9:2,59:1,124:0};function n(f){for(var p={},h=!1,d=0,m=0;d<f.length;++d)(m=f.charCodeAt(d))==34?h=!h:!h&&m in r&&(p[m]=(p[m]||0)+1);m=[];for(d in p)Object.prototype.hasOwnProperty.call(p,d)&&m.push([p[d],d]);if(!m.length){p=a;for(d in p)Object.prototype.hasOwnProperty.call(p,d)&&m.push([p[d],d])}return m.sort(function(u,b){return u[0]-b[0]||a[u[1]]-a[b[1]]}),r[m.pop()[1]]||44}function s(f,p){var h=p||{},d="",m=h.dense?[]:{},u={s:{c:0,r:0},e:{c:0,r:0}};f.slice(0,4)=="sep="?f.charCodeAt(5)==13&&f.charCodeAt(6)==10?(d=f.charAt(4),f=f.slice(7)):f.charCodeAt(5)==13||f.charCodeAt(5)==10?(d=f.charAt(4),f=f.slice(6)):d=n(f.slice(0,1024)):h&&h.FS?d=h.FS:d=n(f.slice(0,1024));var b=0,T=0,S=0,g=0,x=0,A=d.charCodeAt(0),P=!1,R=0,U=f.charCodeAt(0);f=f.replace(/\r\n/mg,`
`);var I=h.dateNF!=null?df(h.dateNF):null;function G(){var W=f.slice(g,x),D={};if(W.charAt(0)=='"'&&W.charAt(W.length-1)=='"'&&(W=W.slice(1,-1).replace(/""/g,'"')),W.length===0)D.t="z";else if(h.raw)D.t="s",D.v=W;else if(W.trim().length===0)D.t="s",D.v=W;else if(W.charCodeAt(0)==61)W.charCodeAt(1)==34&&W.charCodeAt(W.length-1)==34?(D.t="s",D.v=W.slice(2,-1).replace(/""/g,'"')):Hd(W)?(D.t="n",D.f=W.slice(1)):(D.t="s",D.v=W);else if(W=="TRUE")D.t="b",D.v=!0;else if(W=="FALSE")D.t="b",D.v=!1;else if(!isNaN(S=Xr(W)))D.t="n",h.cellText!==!1&&(D.w=W),D.v=S;else if(!isNaN(ea(W).getDate())||I&&W.match(I)){D.z=h.dateNF||be[14];var te=0;I&&W.match(I)&&(W=pf(W,h.dateNF,W.match(I)||[]),te=1),h.cellDates?(D.t="d",D.v=We(W,te)):(D.t="n",D.v=nr(We(W,te))),h.cellText!==!1&&(D.w=zr(D.z,D.v instanceof Date?nr(D.v):D.v)),h.cellNF||delete D.z}else D.t="s",D.v=W;if(D.t=="z"||(h.dense?(m[b]||(m[b]=[]),m[b][T]=D):m[ve({c:T,r:b})]=D),g=x+1,U=f.charCodeAt(g),u.e.c<T&&(u.e.c=T),u.e.r<b&&(u.e.r=b),R==A)++T;else if(T=0,++b,h.sheetRows&&h.sheetRows<=b)return!0}e:for(;x<f.length;++x)switch(R=f.charCodeAt(x)){case 34:U===34&&(P=!P);break;case A:case 10:case 13:if(!P&&G())break e;break}return x-g>0&&G(),m["!ref"]=we(u),m}function i(f,p){return!(p&&p.PRN)||p.FS||f.slice(0,4)=="sep="||f.indexOf("	")>=0||f.indexOf(",")>=0||f.indexOf(";")>=0?s(f,p):ia(t(f,p),p)}function o(f,p){var h="",d=p.type=="string"?[0,0,0,0]:Gs(f,p);switch(p.type){case"base64":h=Ir(f);break;case"binary":h=f;break;case"buffer":p.codepage==65001?h=f.toString("utf8"):(p.codepage,h=Se&&Buffer.isBuffer(f)?f.toString("binary"):Tt(f));break;case"array":h=It(f);break;case"string":h=f;break;default:throw new Error("Unrecognized type "+p.type)}return d[0]==239&&d[1]==187&&d[2]==191?h=Le(h.slice(3)):p.type!="string"&&p.type!="buffer"&&p.codepage==65001?h=Le(h):p.type=="binary",h.slice(0,19)=="socialcalc:version:"?Tc.to_sheet(p.type=="string"?h:Le(h),p):i(h,p)}function c(f,p){return Et(o(f,p),p)}function l(f){for(var p=[],h=Re(f["!ref"]),d,m=Array.isArray(f),u=h.s.r;u<=h.e.r;++u){for(var b=[],T=h.s.c;T<=h.e.c;++T){var S=ve({r:u,c:T});if(d=m?(f[u]||[])[T]:f[S],!d||d.v==null){b.push("          ");continue}for(var g=(d.w||(ct(d),d.w)||"").slice(0,10);g.length<10;)g+=" ";b.push(g+(T===0?" ":""))}p.push(b.join(""))}return p.join(`
`)}return{to_workbook:c,to_sheet:o,from_sheet:l}}();function Cu(e,t){var r=t||{},a=!!r.WTF;r.WTF=!0;try{var n=wc.to_workbook(e,r);return r.WTF=a,n}catch(s){if(r.WTF=a,!s.message.match(/SYLK bad record ID/)&&a)throw s;return ra.to_workbook(e,t)}}var ta=function(){function e(C,L,F){if(C){lr(C,C.l||0);for(var N=F.Enum||me;C.l<C.length;){var X=C.read_shift(2),ne=N[X]||N[65535],J=C.read_shift(2),Q=C.l+J,q=ne.f&&ne.f(C,J,F);if(C.l=Q,L(q,ne,X))return}}}function t(C,L){switch(L.type){case"base64":return r(Or(Ir(C)),L);case"binary":return r(Or(C),L);case"buffer":case"array":return r(C,L)}throw"Unsupported type "+L.type}function r(C,L){if(!C)return C;var F=L||{},N=F.dense?[]:{},X="Sheet1",ne="",J=0,Q={},q=[],Ee=[],O={s:{r:0,c:0},e:{r:0,c:0}},Me=F.sheetRows||0;if(C[2]==0&&(C[3]==8||C[3]==9)&&C.length>=16&&C[14]==5&&C[15]===108)throw new Error("Unsupported Works 3 for Mac file");if(C[2]==2)F.Enum=me,e(C,function(le,rr,Gr){switch(Gr){case 0:F.vers=le,le>=4096&&(F.qpro=!0);break;case 6:O=le;break;case 204:le&&(ne=le);break;case 222:ne=le;break;case 15:case 51:F.qpro||(le[1].v=le[1].v.slice(1));case 13:case 14:case 16:Gr==14&&(le[2]&112)==112&&(le[2]&15)>1&&(le[2]&15)<15&&(le[1].z=F.dateNF||be[14],F.cellDates&&(le[1].t="d",le[1].v=Tn(le[1].v))),F.qpro&&le[3]>J&&(N["!ref"]=we(O),Q[X]=N,q.push(X),N=F.dense?[]:{},O={s:{r:0,c:0},e:{r:0,c:0}},J=le[3],X=ne||"Sheet"+(J+1),ne="");var wr=F.dense?(N[le[0].r]||[])[le[0].c]:N[ve(le[0])];if(wr){wr.t=le[1].t,wr.v=le[1].v,le[1].z!=null&&(wr.z=le[1].z),le[1].f!=null&&(wr.f=le[1].f);break}F.dense?(N[le[0].r]||(N[le[0].r]=[]),N[le[0].r][le[0].c]=le[1]):N[ve(le[0])]=le[1];break}},F);else if(C[2]==26||C[2]==14)F.Enum=Te,C[2]==14&&(F.qpro=!0,C.l=0),e(C,function(le,rr,Gr){switch(Gr){case 204:X=le;break;case 22:le[1].v=le[1].v.slice(1);case 23:case 24:case 25:case 37:case 39:case 40:if(le[3]>J&&(N["!ref"]=we(O),Q[X]=N,q.push(X),N=F.dense?[]:{},O={s:{r:0,c:0},e:{r:0,c:0}},J=le[3],X="Sheet"+(J+1)),Me>0&&le[0].r>=Me)break;F.dense?(N[le[0].r]||(N[le[0].r]=[]),N[le[0].r][le[0].c]=le[1]):N[ve(le[0])]=le[1],O.e.c<le[0].c&&(O.e.c=le[0].c),O.e.r<le[0].r&&(O.e.r=le[0].r);break;case 27:le[14e3]&&(Ee[le[14e3][0]]=le[14e3][1]);break;case 1537:Ee[le[0]]=le[1],le[0]==J&&(X=le[1]);break}},F);else throw new Error("Unrecognized LOTUS BOF "+C[2]);if(N["!ref"]=we(O),Q[ne||X]=N,q.push(ne||X),!Ee.length)return{SheetNames:q,Sheets:Q};for(var Oe={},De=[],xe=0;xe<Ee.length;++xe)Q[q[xe]]?(De.push(Ee[xe]||q[xe]),Oe[Ee[xe]]=Q[Ee[xe]]||Q[q[xe]]):(De.push(Ee[xe]),Oe[Ee[xe]]={"!ref":"A1"});return{SheetNames:De,Sheets:Oe}}function a(C,L){var F=L||{};if(+F.codepage>=0&&jr(+F.codepage),F.type=="string")throw new Error("Cannot write WK1 to JS string");var N=_r(),X=Re(C["!ref"]),ne=Array.isArray(C),J=[];ae(N,0,s(1030)),ae(N,6,c(X));for(var Q=Math.min(X.e.r,8191),q=X.s.r;q<=Q;++q)for(var Ee=Je(q),O=X.s.c;O<=X.e.c;++O){q===X.s.r&&(J[O]=Ve(O));var Me=J[O]+Ee,Oe=ne?(C[q]||[])[O]:C[Me];if(!(!Oe||Oe.t=="z"))if(Oe.t=="n")(Oe.v|0)==Oe.v&&Oe.v>=-32768&&Oe.v<=32767?ae(N,13,d(q,O,Oe.v)):ae(N,14,u(q,O,Oe.v));else{var De=ct(Oe);ae(N,15,p(q,O,De.slice(0,239)))}}return ae(N,1),N.end()}function n(C,L){var F=L||{};if(+F.codepage>=0&&jr(+F.codepage),F.type=="string")throw new Error("Cannot write WK3 to JS string");var N=_r();ae(N,0,i(C));for(var X=0,ne=0;X<C.SheetNames.length;++X)(C.Sheets[C.SheetNames[X]]||{})["!ref"]&&ae(N,27,ee(C.SheetNames[X],ne++));var J=0;for(X=0;X<C.SheetNames.length;++X){var Q=C.Sheets[C.SheetNames[X]];if(!(!Q||!Q["!ref"])){for(var q=Re(Q["!ref"]),Ee=Array.isArray(Q),O=[],Me=Math.min(q.e.r,8191),Oe=q.s.r;Oe<=Me;++Oe)for(var De=Je(Oe),xe=q.s.c;xe<=q.e.c;++xe){Oe===q.s.r&&(O[xe]=Ve(xe));var le=O[xe]+De,rr=Ee?(Q[Oe]||[])[xe]:Q[le];if(!(!rr||rr.t=="z"))if(rr.t=="n")ae(N,23,G(Oe,xe,J,rr.v));else{var Gr=ct(rr);ae(N,22,R(Oe,xe,J,Gr.slice(0,239)))}}++J}}return ae(N,1),N.end()}function s(C){var L=j(2);return L.write_shift(2,C),L}function i(C){var L=j(26);L.write_shift(2,4096),L.write_shift(2,4),L.write_shift(4,0);for(var F=0,N=0,X=0,ne=0;ne<C.SheetNames.length;++ne){var J=C.SheetNames[ne],Q=C.Sheets[J];if(!(!Q||!Q["!ref"])){++X;var q=xr(Q["!ref"]);F<q.e.r&&(F=q.e.r),N<q.e.c&&(N=q.e.c)}}return F>8191&&(F=8191),L.write_shift(2,F),L.write_shift(1,X),L.write_shift(1,N),L.write_shift(2,0),L.write_shift(2,0),L.write_shift(1,1),L.write_shift(1,2),L.write_shift(4,0),L.write_shift(4,0),L}function o(C,L,F){var N={s:{c:0,r:0},e:{c:0,r:0}};return L==8&&F.qpro?(N.s.c=C.read_shift(1),C.l++,N.s.r=C.read_shift(2),N.e.c=C.read_shift(1),C.l++,N.e.r=C.read_shift(2),N):(N.s.c=C.read_shift(2),N.s.r=C.read_shift(2),L==12&&F.qpro&&(C.l+=2),N.e.c=C.read_shift(2),N.e.r=C.read_shift(2),L==12&&F.qpro&&(C.l+=2),N.s.c==65535&&(N.s.c=N.e.c=N.s.r=N.e.r=0),N)}function c(C){var L=j(8);return L.write_shift(2,C.s.c),L.write_shift(2,C.s.r),L.write_shift(2,C.e.c),L.write_shift(2,C.e.r),L}function l(C,L,F){var N=[{c:0,r:0},{t:"n",v:0},0,0];return F.qpro&&F.vers!=20768?(N[0].c=C.read_shift(1),N[3]=C.read_shift(1),N[0].r=C.read_shift(2),C.l+=2):(N[2]=C.read_shift(1),N[0].c=C.read_shift(2),N[0].r=C.read_shift(2)),N}function f(C,L,F){var N=C.l+L,X=l(C,L,F);if(X[1].t="s",F.vers==20768){C.l++;var ne=C.read_shift(1);return X[1].v=C.read_shift(ne,"utf8"),X}return F.qpro&&C.l++,X[1].v=C.read_shift(N-C.l,"cstr"),X}function p(C,L,F){var N=j(7+F.length);N.write_shift(1,255),N.write_shift(2,L),N.write_shift(2,C),N.write_shift(1,39);for(var X=0;X<N.length;++X){var ne=F.charCodeAt(X);N.write_shift(1,ne>=128?95:ne)}return N.write_shift(1,0),N}function h(C,L,F){var N=l(C,L,F);return N[1].v=C.read_shift(2,"i"),N}function d(C,L,F){var N=j(7);return N.write_shift(1,255),N.write_shift(2,L),N.write_shift(2,C),N.write_shift(2,F,"i"),N}function m(C,L,F){var N=l(C,L,F);return N[1].v=C.read_shift(8,"f"),N}function u(C,L,F){var N=j(13);return N.write_shift(1,255),N.write_shift(2,L),N.write_shift(2,C),N.write_shift(8,F,"f"),N}function b(C,L,F){var N=C.l+L,X=l(C,L,F);if(X[1].v=C.read_shift(8,"f"),F.qpro)C.l=N;else{var ne=C.read_shift(2);x(C.slice(C.l,C.l+ne),X),C.l+=ne}return X}function T(C,L,F){var N=L&32768;return L&=-32769,L=(N?C:0)+(L>=8192?L-16384:L),(N?"":"$")+(F?Ve(L):Je(L))}var S={51:["FALSE",0],52:["TRUE",0],70:["LEN",1],80:["SUM",69],81:["AVERAGEA",69],82:["COUNTA",69],83:["MINA",69],84:["MAXA",69],111:["T",1]},g=["","","","","","","","","","+","-","*","/","^","=","<>","<=",">=","<",">","","","","","&","","","","","","",""];function x(C,L){lr(C,0);for(var F=[],N=0,X="",ne="",J="",Q="";C.l<C.length;){var q=C[C.l++];switch(q){case 0:F.push(C.read_shift(8,"f"));break;case 1:ne=T(L[0].c,C.read_shift(2),!0),X=T(L[0].r,C.read_shift(2),!1),F.push(ne+X);break;case 2:{var Ee=T(L[0].c,C.read_shift(2),!0),O=T(L[0].r,C.read_shift(2),!1);ne=T(L[0].c,C.read_shift(2),!0),X=T(L[0].r,C.read_shift(2),!1),F.push(Ee+O+":"+ne+X)}break;case 3:if(C.l<C.length){console.error("WK1 premature formula end");return}break;case 4:F.push("("+F.pop()+")");break;case 5:F.push(C.read_shift(2));break;case 6:{for(var Me="";q=C[C.l++];)Me+=String.fromCharCode(q);F.push('"'+Me.replace(/"/g,'""')+'"')}break;case 8:F.push("-"+F.pop());break;case 23:F.push("+"+F.pop());break;case 22:F.push("NOT("+F.pop()+")");break;case 20:case 21:Q=F.pop(),J=F.pop(),F.push(["AND","OR"][q-20]+"("+J+","+Q+")");break;default:if(q<32&&g[q])Q=F.pop(),J=F.pop(),F.push(J+g[q]+Q);else if(S[q]){if(N=S[q][1],N==69&&(N=C[C.l++]),N>F.length){console.error("WK1 bad formula parse 0x"+q.toString(16)+":|"+F.join("|")+"|");return}var Oe=F.slice(-N);F.length-=N,F.push(S[q][0]+"("+Oe.join(",")+")")}else return q<=7?console.error("WK1 invalid opcode "+q.toString(16)):q<=24?console.error("WK1 unsupported op "+q.toString(16)):q<=30?console.error("WK1 invalid opcode "+q.toString(16)):q<=115?console.error("WK1 unsupported function opcode "+q.toString(16)):console.error("WK1 unrecognized opcode "+q.toString(16))}}F.length==1?L[1].f=""+F[0]:console.error("WK1 bad formula parse |"+F.join("|")+"|")}function A(C){var L=[{c:0,r:0},{t:"n",v:0},0];return L[0].r=C.read_shift(2),L[3]=C[C.l++],L[0].c=C[C.l++],L}function P(C,L){var F=A(C);return F[1].t="s",F[1].v=C.read_shift(L-4,"cstr"),F}function R(C,L,F,N){var X=j(6+N.length);X.write_shift(2,C),X.write_shift(1,F),X.write_shift(1,L),X.write_shift(1,39);for(var ne=0;ne<N.length;++ne){var J=N.charCodeAt(ne);X.write_shift(1,J>=128?95:J)}return X.write_shift(1,0),X}function U(C,L){var F=A(C);F[1].v=C.read_shift(2);var N=F[1].v>>1;if(F[1].v&1)switch(N&7){case 0:N=(N>>3)*5e3;break;case 1:N=(N>>3)*500;break;case 2:N=(N>>3)/20;break;case 3:N=(N>>3)/200;break;case 4:N=(N>>3)/2e3;break;case 5:N=(N>>3)/2e4;break;case 6:N=(N>>3)/16;break;case 7:N=(N>>3)/64;break}return F[1].v=N,F}function I(C,L){var F=A(C),N=C.read_shift(4),X=C.read_shift(4),ne=C.read_shift(2);if(ne==65535)return N===0&&X===3221225472?(F[1].t="e",F[1].v=15):N===0&&X===3489660928?(F[1].t="e",F[1].v=42):F[1].v=0,F;var J=ne&32768;return ne=(ne&32767)-16446,F[1].v=(1-J*2)*(X*Math.pow(2,ne+32)+N*Math.pow(2,ne)),F}function G(C,L,F,N){var X=j(14);if(X.write_shift(2,C),X.write_shift(1,F),X.write_shift(1,L),N==0)return X.write_shift(4,0),X.write_shift(4,0),X.write_shift(2,65535),X;var ne=0,J=0,Q=0,q=0;return N<0&&(ne=1,N=-N),J=Math.log2(N)|0,N/=Math.pow(2,J-31),q=N>>>0,q&2147483648||(N/=2,++J,q=N>>>0),N-=q,q|=2147483648,q>>>=0,N*=Math.pow(2,32),Q=N>>>0,X.write_shift(4,Q),X.write_shift(4,q),J+=16383+(ne?32768:0),X.write_shift(2,J),X}function W(C,L){var F=I(C);return C.l+=L-14,F}function D(C,L){var F=A(C),N=C.read_shift(4);return F[1].v=N>>6,F}function te(C,L){var F=A(C),N=C.read_shift(8,"f");return F[1].v=N,F}function fe(C,L){var F=te(C);return C.l+=L-10,F}function se(C,L){return C[C.l+L-1]==0?C.read_shift(L,"cstr"):""}function de(C,L){var F=C[C.l++];F>L-1&&(F=L-1);for(var N="";N.length<F;)N+=String.fromCharCode(C[C.l++]);return N}function ue(C,L,F){if(!(!F.qpro||L<21)){var N=C.read_shift(1);C.l+=17,C.l+=1,C.l+=2;var X=C.read_shift(L-21,"cstr");return[N,X]}}function K(C,L){for(var F={},N=C.l+L;C.l<N;){var X=C.read_shift(2);if(X==14e3){for(F[X]=[0,""],F[X][0]=C.read_shift(2);C[C.l];)F[X][1]+=String.fromCharCode(C[C.l]),C.l++;C.l++}}return F}function ee(C,L){var F=j(5+C.length);F.write_shift(2,14e3),F.write_shift(2,L);for(var N=0;N<C.length;++N){var X=C.charCodeAt(N);F[F.l++]=X>127?95:X}return F[F.l++]=0,F}var me={0:{n:"BOF",f:er},1:{n:"EOF"},2:{n:"CALCMODE"},3:{n:"CALCORDER"},4:{n:"SPLIT"},5:{n:"SYNC"},6:{n:"RANGE",f:o},7:{n:"WINDOW1"},8:{n:"COLW1"},9:{n:"WINTWO"},10:{n:"COLW2"},11:{n:"NAME"},12:{n:"BLANK"},13:{n:"INTEGER",f:h},14:{n:"NUMBER",f:m},15:{n:"LABEL",f},16:{n:"FORMULA",f:b},24:{n:"TABLE"},25:{n:"ORANGE"},26:{n:"PRANGE"},27:{n:"SRANGE"},28:{n:"FRANGE"},29:{n:"KRANGE1"},32:{n:"HRANGE"},35:{n:"KRANGE2"},36:{n:"PROTEC"},37:{n:"FOOTER"},38:{n:"HEADER"},39:{n:"SETUP"},40:{n:"MARGINS"},41:{n:"LABELFMT"},42:{n:"TITLES"},43:{n:"SHEETJS"},45:{n:"GRAPH"},46:{n:"NGRAPH"},47:{n:"CALCCOUNT"},48:{n:"UNFORMATTED"},49:{n:"CURSORW12"},50:{n:"WINDOW"},51:{n:"STRING",f},55:{n:"PASSWORD"},56:{n:"LOCKED"},60:{n:"QUERY"},61:{n:"QUERYNAME"},62:{n:"PRINT"},63:{n:"PRINTNAME"},64:{n:"GRAPH2"},65:{n:"GRAPHNAME"},66:{n:"ZOOM"},67:{n:"SYMSPLIT"},68:{n:"NSROWS"},69:{n:"NSCOLS"},70:{n:"RULER"},71:{n:"NNAME"},72:{n:"ACOMM"},73:{n:"AMACRO"},74:{n:"PARSE"},102:{n:"PRANGES??"},103:{n:"RRANGES??"},104:{n:"FNAME??"},105:{n:"MRANGES??"},204:{n:"SHEETNAMECS",f:se},222:{n:"SHEETNAMELP",f:de},65535:{n:""}},Te={0:{n:"BOF"},1:{n:"EOF"},2:{n:"PASSWORD"},3:{n:"CALCSET"},4:{n:"WINDOWSET"},5:{n:"SHEETCELLPTR"},6:{n:"SHEETLAYOUT"},7:{n:"COLUMNWIDTH"},8:{n:"HIDDENCOLUMN"},9:{n:"USERRANGE"},10:{n:"SYSTEMRANGE"},11:{n:"ZEROFORCE"},12:{n:"SORTKEYDIR"},13:{n:"FILESEAL"},14:{n:"DATAFILLNUMS"},15:{n:"PRINTMAIN"},16:{n:"PRINTSTRING"},17:{n:"GRAPHMAIN"},18:{n:"GRAPHSTRING"},19:{n:"??"},20:{n:"ERRCELL"},21:{n:"NACELL"},22:{n:"LABEL16",f:P},23:{n:"NUMBER17",f:I},24:{n:"NUMBER18",f:U},25:{n:"FORMULA19",f:W},26:{n:"FORMULA1A"},27:{n:"XFORMAT",f:K},28:{n:"DTLABELMISC"},29:{n:"DTLABELCELL"},30:{n:"GRAPHWINDOW"},31:{n:"CPA"},32:{n:"LPLAUTO"},33:{n:"QUERY"},34:{n:"HIDDENSHEET"},35:{n:"??"},37:{n:"NUMBER25",f:D},38:{n:"??"},39:{n:"NUMBER27",f:te},40:{n:"FORMULA28",f:fe},142:{n:"??"},147:{n:"??"},150:{n:"??"},151:{n:"??"},152:{n:"??"},153:{n:"??"},154:{n:"??"},155:{n:"??"},156:{n:"??"},163:{n:"??"},174:{n:"??"},175:{n:"??"},176:{n:"??"},177:{n:"??"},184:{n:"??"},185:{n:"??"},186:{n:"??"},187:{n:"??"},188:{n:"??"},195:{n:"??"},201:{n:"??"},204:{n:"SHEETNAMECS",f:se},205:{n:"??"},206:{n:"??"},207:{n:"??"},208:{n:"??"},256:{n:"??"},259:{n:"??"},260:{n:"??"},261:{n:"??"},262:{n:"??"},263:{n:"??"},265:{n:"??"},266:{n:"??"},267:{n:"??"},268:{n:"??"},270:{n:"??"},271:{n:"??"},384:{n:"??"},389:{n:"??"},390:{n:"??"},393:{n:"??"},396:{n:"??"},512:{n:"??"},514:{n:"??"},513:{n:"??"},516:{n:"??"},517:{n:"??"},640:{n:"??"},641:{n:"??"},642:{n:"??"},643:{n:"??"},644:{n:"??"},645:{n:"??"},646:{n:"??"},647:{n:"??"},648:{n:"??"},658:{n:"??"},659:{n:"??"},660:{n:"??"},661:{n:"??"},662:{n:"??"},665:{n:"??"},666:{n:"??"},768:{n:"??"},772:{n:"??"},1537:{n:"SHEETINFOQP",f:ue},1600:{n:"??"},1602:{n:"??"},1793:{n:"??"},1794:{n:"??"},1795:{n:"??"},1796:{n:"??"},1920:{n:"??"},2048:{n:"??"},2049:{n:"??"},2052:{n:"??"},2688:{n:"??"},10998:{n:"??"},12849:{n:"??"},28233:{n:"??"},28484:{n:"??"},65535:{n:""}};return{sheet_to_wk1:a,book_to_wk3:n,to_workbook:t}}();function Ru(e){var t={},r=e.match(gr),a=0,n=!1;if(r)for(;a!=r.length;++a){var s=ge(r[a]);switch(s[0].replace(/\w*:/g,"")){case"<condense":break;case"<extend":break;case"<shadow":if(!s.val)break;case"<shadow>":case"<shadow/>":t.shadow=1;break;case"</shadow>":break;case"<charset":if(s.val=="1")break;t.cp=is[parseInt(s.val,10)];break;case"<outline":if(!s.val)break;case"<outline>":case"<outline/>":t.outline=1;break;case"</outline>":break;case"<rFont":t.name=s.val;break;case"<sz":t.sz=s.val;break;case"<strike":if(!s.val)break;case"<strike>":case"<strike/>":t.strike=1;break;case"</strike>":break;case"<u":if(!s.val)break;switch(s.val){case"double":t.uval="double";break;case"singleAccounting":t.uval="single-accounting";break;case"doubleAccounting":t.uval="double-accounting";break}case"<u>":case"<u/>":t.u=1;break;case"</u>":break;case"<b":if(s.val=="0")break;case"<b>":case"<b/>":t.b=1;break;case"</b>":break;case"<i":if(s.val=="0")break;case"<i>":case"<i/>":t.i=1;break;case"</i>":break;case"<color":s.rgb&&(t.color=s.rgb.slice(2,8));break;case"<color>":case"<color/>":case"</color>":break;case"<family":t.family=s.val;break;case"<family>":case"<family/>":case"</family>":break;case"<vertAlign":t.valign=s.val;break;case"<vertAlign>":case"<vertAlign/>":case"</vertAlign>":break;case"<scheme":break;case"<scheme>":case"<scheme/>":case"</scheme>":break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":n=!0;break;case"</ext>":n=!1;break;default:if(s[0].charCodeAt(1)!==47&&!n)throw new Error("Unrecognized rich format "+s[0])}}return t}var Ou=function(){var e=Aa("t"),t=Aa("rPr");function r(s){var i=s.match(e);if(!i)return{t:"s",v:""};var o={t:"s",v:Ce(i[1])},c=s.match(t);return c&&(o.s=Ru(c[1])),o}var a=/<(?:\w+:)?r>/g,n=/<\/(?:\w+:)?r>/;return function(s){return s.replace(a,"").split(n).map(r).filter(function(i){return i.v})}}(),Nu=function(){var e=/(\r\n|\n)/g;function t(a,n,s){var i=[];a.u&&i.push("text-decoration: underline;"),a.uval&&i.push("text-underline-style:"+a.uval+";"),a.sz&&i.push("font-size:"+a.sz+"pt;"),a.outline&&i.push("text-effect: outline;"),a.shadow&&i.push("text-shadow: auto;"),n.push('<span style="'+i.join("")+'">'),a.b&&(n.push("<b>"),s.push("</b>")),a.i&&(n.push("<i>"),s.push("</i>")),a.strike&&(n.push("<s>"),s.push("</s>"));var o=a.valign||"";return o=="superscript"||o=="super"?o="sup":o=="subscript"&&(o="sub"),o!=""&&(n.push("<"+o+">"),s.push("</"+o+">")),s.push("</span>"),a}function r(a){var n=[[],a.v,[]];return a.v?(a.s&&t(a.s,n[0],n[2]),n[0].join("")+n[1].replace(e,"<br/>")+n[2].join("")):""}return function(a){return a.map(r).join("")}}(),Iu=/<(?:\w+:)?t[^>]*>([^<]*)<\/(?:\w+:)?t>/g,Fu=/<(?:\w+:)?r>/,Pu=/<(?:\w+:)?rPh.*?>([\s\S]*?)<\/(?:\w+:)?rPh>/g;function Os(e,t){var r=t?t.cellHTML:!0,a={};return e?(e.match(/^\s*<(?:\w+:)?t[^>]*>/)?(a.t=Ce(Le(e.slice(e.indexOf(">")+1).split(/<\/(?:\w+:)?t>/)[0]||"")),a.r=Le(e),r&&(a.h=bs(a.t))):e.match(Fu)&&(a.r=Le(e),a.t=Ce(Le((e.replace(Pu,"").match(Iu)||[]).join("").replace(gr,""))),r&&(a.h=Nu(Ou(a.r)))),a):{t:""}}var Du=/<(?:\w+:)?sst([^>]*)>([\s\S]*)<\/(?:\w+:)?sst>/,Lu=/<(?:\w+:)?(?:si|sstItem)>/g,Mu=/<\/(?:\w+:)?(?:si|sstItem)>/;function Uu(e,t){var r=[],a="";if(!e)return r;var n=e.match(Du);if(n){a=n[2].replace(Lu,"").split(Mu);for(var s=0;s!=a.length;++s){var i=Os(a[s].trim(),t);i!=null&&(r[r.length]=i)}n=ge(n[1]),r.Count=n.count,r.Unique=n.uniqueCount}return r}var Bu=/^\s|\s$|[\t\n\r]/;function Ec(e,t){if(!t.bookSST)return"";var r=[Ze];r[r.length]=re("sst",null,{xmlns:Mt[0],count:e.Count,uniqueCount:e.Unique});for(var a=0;a!=e.length;++a)if(e[a]!=null){var n=e[a],s="<si>";n.r?s+=n.r:(s+="<t",n.t||(n.t=""),n.t.match(Bu)&&(s+=' xml:space="preserve"'),s+=">"+Pe(n.t)+"</t>"),s+="</si>",r[r.length]=s}return r.length>2&&(r[r.length]="</sst>",r[1]=r[1].replace("/>",">")),r.join("")}function Wu(e){return[e.read_shift(4),e.read_shift(4)]}function zu(e,t){var r=[],a=!1;return ft(e,function(n,s,i){switch(i){case 159:r.Count=n[0],r.Unique=n[1];break;case 19:r.push(n);break;case 160:return!0;case 35:a=!0;break;case 36:a=!1;break;default:if(s.T,!a||t.WTF)throw new Error("Unexpected record 0x"+i.toString(16))}}),r}function Hu(e,t){return t||(t=j(8)),t.write_shift(4,e.Count),t.write_shift(4,e.Unique),t}var Gu=Qf;function Vu(e){var t=_r();Y(t,159,Hu(e));for(var r=0;r<e.length;++r)Y(t,19,Gu(e[r]));return Y(t,160),t.end()}function yc(e){for(var t=[],r=e.split(""),a=0;a<r.length;++a)t[a]=r[a].charCodeAt(0);return t}function ot(e,t){var r={};return r.Major=e.read_shift(2),r.Minor=e.read_shift(2),t>=4&&(e.l+=t-4),r}function ju(e){var t={};return t.id=e.read_shift(0,"lpp4"),t.R=ot(e,4),t.U=ot(e,4),t.W=ot(e,4),t}function $u(e){for(var t=e.read_shift(4),r=e.l+t-4,a={},n=e.read_shift(4),s=[];n-- >0;)s.push({t:e.read_shift(4),v:e.read_shift(0,"lpp4")});if(a.name=e.read_shift(0,"lpp4"),a.comps=s,e.l!=r)throw new Error("Bad DataSpaceMapEntry: "+e.l+" != "+r);return a}function Xu(e){var t=[];e.l+=4;for(var r=e.read_shift(4);r-- >0;)t.push($u(e));return t}function Yu(e){var t=[];e.l+=4;for(var r=e.read_shift(4);r-- >0;)t.push(e.read_shift(0,"lpp4"));return t}function Ku(e){var t={};return e.read_shift(4),e.l+=4,t.id=e.read_shift(0,"lpp4"),t.name=e.read_shift(0,"lpp4"),t.R=ot(e,4),t.U=ot(e,4),t.W=ot(e,4),t}function Ju(e){var t=Ku(e);if(t.ename=e.read_shift(0,"8lpp4"),t.blksz=e.read_shift(4),t.cmode=e.read_shift(4),e.read_shift(4)!=4)throw new Error("Bad !Primary record");return t}function Sc(e,t){var r=e.l+t,a={};a.Flags=e.read_shift(4)&63,e.l+=4,a.AlgID=e.read_shift(4);var n=!1;switch(a.AlgID){case 26126:case 26127:case 26128:n=a.Flags==36;break;case 26625:n=a.Flags==4;break;case 0:n=a.Flags==16||a.Flags==4||a.Flags==36;break;default:throw"Unrecognized encryption algorithm: "+a.AlgID}if(!n)throw new Error("Encryption Flags/AlgID mismatch");return a.AlgIDHash=e.read_shift(4),a.KeySize=e.read_shift(4),a.ProviderType=e.read_shift(4),e.l+=8,a.CSPName=e.read_shift(r-e.l>>1,"utf16le"),e.l=r,a}function _c(e,t){var r={},a=e.l+t;return e.l+=4,r.Salt=e.slice(e.l,e.l+16),e.l+=16,r.Verifier=e.slice(e.l,e.l+16),e.l+=16,e.read_shift(4),r.VerifierHash=e.slice(e.l,a),e.l=a,r}function Zu(e){var t=ot(e);switch(t.Minor){case 2:return[t.Minor,qu(e)];case 3:return[t.Minor,Qu()];case 4:return[t.Minor,e0(e)]}throw new Error("ECMA-376 Encrypted file unrecognized Version: "+t.Minor)}function qu(e){var t=e.read_shift(4);if((t&63)!=36)throw new Error("EncryptionInfo mismatch");var r=e.read_shift(4),a=Sc(e,r),n=_c(e,e.length-e.l);return{t:"Std",h:a,v:n}}function Qu(){throw new Error("File is password-protected: ECMA-376 Extensible")}function e0(e){var t=["saltSize","blockSize","keyBits","hashSize","cipherAlgorithm","cipherChaining","hashAlgorithm","saltValue"];e.l+=4;var r=e.read_shift(e.length-e.l,"utf8"),a={};return r.replace(gr,function(n){var s=ge(n);switch(Qr(s[0])){case"<?xml":break;case"<encryption":case"</encryption>":break;case"<keyData":t.forEach(function(i){a[i]=s[i]});break;case"<dataIntegrity":a.encryptedHmacKey=s.encryptedHmacKey,a.encryptedHmacValue=s.encryptedHmacValue;break;case"<keyEncryptors>":case"<keyEncryptors":a.encs=[];break;case"</keyEncryptors>":break;case"<keyEncryptor":a.uri=s.uri;break;case"</keyEncryptor>":break;case"<encryptedKey":a.encs.push(s);break;default:throw s[0]}}),a}function r0(e,t){var r={},a=r.EncryptionVersionInfo=ot(e,4);if(t-=4,a.Minor!=2)throw new Error("unrecognized minor version code: "+a.Minor);if(a.Major>4||a.Major<2)throw new Error("unrecognized major version code: "+a.Major);r.Flags=e.read_shift(4),t-=4;var n=e.read_shift(4);return t-=4,r.EncryptionHeader=Sc(e,n),t-=n,r.EncryptionVerifier=_c(e,t),r}function t0(e){var t={},r=t.EncryptionVersionInfo=ot(e,4);if(r.Major!=1||r.Minor!=1)throw"unrecognized version code "+r.Major+" : "+r.Minor;return t.Salt=e.read_shift(16),t.EncryptedVerifier=e.read_shift(16),t.EncryptedVerifierHash=e.read_shift(16),t}function Ns(e){var t=0,r,a=yc(e),n=a.length+1,s,i,o,c,l;for(r=vt(n),r[0]=a.length,s=1;s!=n;++s)r[s]=a[s-1];for(s=n-1;s>=0;--s)i=r[s],o=t&16384?1:0,c=t<<1&32767,l=o|c,t=l^i;return t^52811}var xc=function(){var e=[187,255,255,186,255,255,185,128,0,190,15,0,191,15,0],t=[57840,7439,52380,33984,4364,3600,61902,12606,6258,57657,54287,34041,10252,43370,20163],r=[44796,19929,39858,10053,20106,40212,10761,31585,63170,64933,60267,50935,40399,11199,17763,35526,1453,2906,5812,11624,23248,885,1770,3540,7080,14160,28320,56640,55369,41139,20807,41614,21821,43642,17621,28485,56970,44341,19019,38038,14605,29210,60195,50791,40175,10751,21502,43004,24537,18387,36774,3949,7898,15796,31592,63184,47201,24803,49606,37805,14203,28406,56812,17824,35648,1697,3394,6788,13576,27152,43601,17539,35078,557,1114,2228,4456,30388,60776,51953,34243,7079,14158,28316,14128,28256,56512,43425,17251,34502,7597,13105,26210,52420,35241,883,1766,3532,4129,8258,16516,33032,4657,9314,18628],a=function(i){return(i/2|i*128)&255},n=function(i,o){return a(i^o)},s=function(i){for(var o=t[i.length-1],c=104,l=i.length-1;l>=0;--l)for(var f=i[l],p=0;p!=7;++p)f&64&&(o^=r[c]),f*=2,--c;return o};return function(i){for(var o=yc(i),c=s(o),l=o.length,f=vt(16),p=0;p!=16;++p)f[p]=0;var h,d,m;for((l&1)===1&&(h=c>>8,f[l]=n(e[0],h),--l,h=c&255,d=o[o.length-1],f[l]=n(d,h));l>0;)--l,h=c>>8,f[l]=n(o[l],h),--l,h=c&255,f[l]=n(o[l],h);for(l=15,m=15-o.length;m>0;)h=c>>8,f[l]=n(e[m],h),--l,--m,h=c&255,f[l]=n(o[l],h),--l,--m;return f}}(),a0=function(e,t,r,a,n){n||(n=t),a||(a=xc(e));var s,i;for(s=0;s!=t.length;++s)i=t[s],i^=a[r],i=(i>>5|i<<3)&255,n[s]=i,++r;return[n,r,a]},n0=function(e){var t=0,r=xc(e);return function(a){var n=a0("",a,t,r);return t=n[1],n[0]}};function s0(e,t,r,a){var n={key:er(e),verificationBytes:er(e)};return r.password&&(n.verifier=Ns(r.password)),a.valid=n.verificationBytes===n.verifier,a.valid&&(a.insitu=n0(r.password)),n}function i0(e,t,r){var a=r||{};return a.Info=e.read_shift(2),e.l-=2,a.Info===1?a.Data=t0(e):a.Data=r0(e,t),a}function o0(e,t,r){var a={Type:r.biff>=8?e.read_shift(2):0};return a.Type?i0(e,t-2,a):s0(e,r.biff>=8?t:t-2,r,a),a}var Ac=function(){function e(n,s){switch(s.type){case"base64":return t(Ir(n),s);case"binary":return t(n,s);case"buffer":return t(Se&&Buffer.isBuffer(n)?n.toString("binary"):Tt(n),s);case"array":return t(It(n),s)}throw new Error("Unrecognized type "+s.type)}function t(n,s){var i=s||{},o=i.dense?[]:{},c=n.match(/\\trowd.*?\\row\b/g);if(!c.length)throw new Error("RTF missing table");var l={s:{c:0,r:0},e:{c:0,r:c.length-1}};return c.forEach(function(f,p){Array.isArray(o)&&(o[p]=[]);for(var h=/\\\w+\b/g,d=0,m,u=-1;m=h.exec(f);){switch(m[0]){case"\\cell":var b=f.slice(d,h.lastIndex-m[0].length);if(b[0]==" "&&(b=b.slice(1)),++u,b.length){var T={v:b,t:"s"};Array.isArray(o)?o[p][u]=T:o[ve({r:p,c:u})]=T}break}d=h.lastIndex}u>l.e.c&&(l.e.c=u)}),o["!ref"]=we(l),o}function r(n,s){return Et(e(n,s),s)}function a(n){for(var s=["{\\rtf1\\ansi"],i=Re(n["!ref"]),o,c=Array.isArray(n),l=i.s.r;l<=i.e.r;++l){s.push("\\trowd\\trautofit1");for(var f=i.s.c;f<=i.e.c;++f)s.push("\\cellx"+(f+1));for(s.push("\\pard\\intbl"),f=i.s.c;f<=i.e.c;++f){var p=ve({r:l,c:f});o=c?(n[l]||[])[f]:n[p],!(!o||o.v==null&&(!o.f||o.F))&&(s.push(" "+(o.w||(ct(o),o.w))),s.push("\\cell"))}s.push("\\pard\\intbl\\row")}return s.join("")+"}"}return{to_workbook:r,to_sheet:e,from_sheet:a}}();function c0(e){var t=e.slice(e[0]==="#"?1:0).slice(0,6);return[parseInt(t.slice(0,2),16),parseInt(t.slice(2,4),16),parseInt(t.slice(4,6),16)]}function Na(e){for(var t=0,r=1;t!=3;++t)r=r*256+(e[t]>255?255:e[t]<0?0:e[t]);return r.toString(16).toUpperCase().slice(1)}function l0(e){var t=e[0]/255,r=e[1]/255,a=e[2]/255,n=Math.max(t,r,a),s=Math.min(t,r,a),i=n-s;if(i===0)return[0,0,t];var o=0,c=0,l=n+s;switch(c=i/(l>1?2-l:l),n){case t:o=((r-a)/i+6)%6;break;case r:o=(a-t)/i+2;break;case a:o=(t-r)/i+4;break}return[o/6,c,l/2]}function f0(e){var t=e[0],r=e[1],a=e[2],n=r*2*(a<.5?a:1-a),s=a-n/2,i=[s,s,s],o=6*t,c;if(r!==0)switch(o|0){case 0:case 6:c=n*o,i[0]+=n,i[1]+=c;break;case 1:c=n*(2-o),i[0]+=c,i[1]+=n;break;case 2:c=n*(o-2),i[1]+=n,i[2]+=c;break;case 3:c=n*(4-o),i[1]+=c,i[2]+=n;break;case 4:c=n*(o-4),i[2]+=n,i[0]+=c;break;case 5:c=n*(6-o),i[2]+=c,i[0]+=n;break}for(var l=0;l!=3;++l)i[l]=Math.round(i[l]*255);return i}function pn(e,t){if(t===0)return e;var r=l0(c0(e));return t<0?r[2]=r[2]*(1+t):r[2]=1-(1-r[2])*(1-t),Na(f0(r))}var Cc=6,h0=15,u0=1,pr=Cc;function Ia(e){return Math.floor((e+Math.round(128/pr)/256)*pr)}function Fa(e){return Math.floor((e-5)/pr*100+.5)/100}function mn(e){return Math.round((e*pr+5)/pr*256)/256}function Ln(e){return mn(Fa(Ia(e)))}function Is(e){var t=Math.abs(e-Ln(e)),r=pr;if(t>.005)for(pr=u0;pr<h0;++pr)Math.abs(e-Ln(e))<=t&&(t=Math.abs(e-Ln(e)),r=pr);pr=r}function gt(e){e.width?(e.wpx=Ia(e.width),e.wch=Fa(e.wpx),e.MDW=pr):e.wpx?(e.wch=Fa(e.wpx),e.width=mn(e.wch),e.MDW=pr):typeof e.wch=="number"&&(e.width=mn(e.wch),e.wpx=Ia(e.width),e.MDW=pr),e.customWidth&&delete e.customWidth}var d0=96,Rc=d0;function Pa(e){return e*96/Rc}function aa(e){return e*Rc/96}var p0={None:"none",Solid:"solid",Gray50:"mediumGray",Gray75:"darkGray",Gray25:"lightGray",HorzStripe:"darkHorizontal",VertStripe:"darkVertical",ReverseDiagStripe:"darkDown",DiagStripe:"darkUp",DiagCross:"darkGrid",ThickDiagCross:"darkTrellis",ThinHorzStripe:"lightHorizontal",ThinVertStripe:"lightVertical",ThinReverseDiagStripe:"lightDown",ThinHorzCross:"lightGrid"};function m0(e,t,r,a){t.Borders=[];var n={},s=!1;(e[0].match(gr)||[]).forEach(function(i){var o=ge(i);switch(Qr(o[0])){case"<borders":case"<borders>":case"</borders>":break;case"<border":case"<border>":case"<border/>":n={},o.diagonalUp&&(n.diagonalUp=Be(o.diagonalUp)),o.diagonalDown&&(n.diagonalDown=Be(o.diagonalDown)),t.Borders.push(n);break;case"</border>":break;case"<left/>":break;case"<left":case"<left>":break;case"</left>":break;case"<right/>":break;case"<right":case"<right>":break;case"</right>":break;case"<top/>":break;case"<top":case"<top>":break;case"</top>":break;case"<bottom/>":break;case"<bottom":case"<bottom>":break;case"</bottom>":break;case"<diagonal":case"<diagonal>":case"<diagonal/>":break;case"</diagonal>":break;case"<horizontal":case"<horizontal>":case"<horizontal/>":break;case"</horizontal>":break;case"<vertical":case"<vertical>":case"<vertical/>":break;case"</vertical>":break;case"<start":case"<start>":case"<start/>":break;case"</start>":break;case"<end":case"<end>":case"<end/>":break;case"</end>":break;case"<color":case"<color>":break;case"<color/>":case"</color>":break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":s=!0;break;case"</ext>":s=!1;break;default:if(a&&a.WTF&&!s)throw new Error("unrecognized "+o[0]+" in borders")}})}function b0(e,t,r,a){t.Fills=[];var n={},s=!1;(e[0].match(gr)||[]).forEach(function(i){var o=ge(i);switch(Qr(o[0])){case"<fills":case"<fills>":case"</fills>":break;case"<fill>":case"<fill":case"<fill/>":n={},t.Fills.push(n);break;case"</fill>":break;case"<gradientFill>":break;case"<gradientFill":case"</gradientFill>":t.Fills.push(n),n={};break;case"<patternFill":case"<patternFill>":o.patternType&&(n.patternType=o.patternType);break;case"<patternFill/>":case"</patternFill>":break;case"<bgColor":n.bgColor||(n.bgColor={}),o.indexed&&(n.bgColor.indexed=parseInt(o.indexed,10)),o.theme&&(n.bgColor.theme=parseInt(o.theme,10)),o.tint&&(n.bgColor.tint=parseFloat(o.tint)),o.rgb&&(n.bgColor.rgb=o.rgb.slice(-6));break;case"<bgColor/>":case"</bgColor>":break;case"<fgColor":n.fgColor||(n.fgColor={}),o.theme&&(n.fgColor.theme=parseInt(o.theme,10)),o.tint&&(n.fgColor.tint=parseFloat(o.tint)),o.rgb!=null&&(n.fgColor.rgb=o.rgb.slice(-6));break;case"<fgColor/>":case"</fgColor>":break;case"<stop":case"<stop/>":break;case"</stop>":break;case"<color":case"<color/>":break;case"</color>":break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":s=!0;break;case"</ext>":s=!1;break;default:if(a&&a.WTF&&!s)throw new Error("unrecognized "+o[0]+" in fills")}})}function v0(e,t,r,a){t.Fonts=[];var n={},s=!1;(e[0].match(gr)||[]).forEach(function(i){var o=ge(i);switch(Qr(o[0])){case"<fonts":case"<fonts>":case"</fonts>":break;case"<font":case"<font>":break;case"</font>":case"<font/>":t.Fonts.push(n),n={};break;case"<name":o.val&&(n.name=Le(o.val));break;case"<name/>":case"</name>":break;case"<b":n.bold=o.val?Be(o.val):1;break;case"<b/>":n.bold=1;break;case"<i":n.italic=o.val?Be(o.val):1;break;case"<i/>":n.italic=1;break;case"<u":switch(o.val){case"none":n.underline=0;break;case"single":n.underline=1;break;case"double":n.underline=2;break;case"singleAccounting":n.underline=33;break;case"doubleAccounting":n.underline=34;break}break;case"<u/>":n.underline=1;break;case"<strike":n.strike=o.val?Be(o.val):1;break;case"<strike/>":n.strike=1;break;case"<outline":n.outline=o.val?Be(o.val):1;break;case"<outline/>":n.outline=1;break;case"<shadow":n.shadow=o.val?Be(o.val):1;break;case"<shadow/>":n.shadow=1;break;case"<condense":n.condense=o.val?Be(o.val):1;break;case"<condense/>":n.condense=1;break;case"<extend":n.extend=o.val?Be(o.val):1;break;case"<extend/>":n.extend=1;break;case"<sz":o.val&&(n.sz=+o.val);break;case"<sz/>":case"</sz>":break;case"<vertAlign":o.val&&(n.vertAlign=o.val);break;case"<vertAlign/>":case"</vertAlign>":break;case"<family":o.val&&(n.family=parseInt(o.val,10));break;case"<family/>":case"</family>":break;case"<scheme":o.val&&(n.scheme=o.val);break;case"<scheme/>":case"</scheme>":break;case"<charset":if(o.val=="1")break;o.codepage=is[parseInt(o.val,10)];break;case"<color":if(n.color||(n.color={}),o.auto&&(n.color.auto=Be(o.auto)),o.rgb)n.color.rgb=o.rgb.slice(-6);else if(o.indexed){n.color.index=parseInt(o.indexed,10);var c=Jt[n.color.index];n.color.index==81&&(c=Jt[1]),c||(c=Jt[1]),n.color.rgb=c[0].toString(16)+c[1].toString(16)+c[2].toString(16)}else o.theme&&(n.color.theme=parseInt(o.theme,10),o.tint&&(n.color.tint=parseFloat(o.tint)),o.theme&&r.themeElements&&r.themeElements.clrScheme&&(n.color.rgb=pn(r.themeElements.clrScheme[n.color.theme].rgb,n.color.tint||0)));break;case"<color/>":case"</color>":break;case"<AlternateContent":s=!0;break;case"</AlternateContent>":s=!1;break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":s=!0;break;case"</ext>":s=!1;break;default:if(a&&a.WTF&&!s)throw new Error("unrecognized "+o[0]+" in fonts")}})}function g0(e,t,r){t.NumberFmt=[];for(var a=Ye(be),n=0;n<a.length;++n)t.NumberFmt[a[n]]=be[a[n]];var s=e[0].match(gr);if(s)for(n=0;n<s.length;++n){var i=ge(s[n]);switch(Qr(i[0])){case"<numFmts":case"</numFmts>":case"<numFmts/>":case"<numFmts>":break;case"<numFmt":{var o=Ce(Le(i.formatCode)),c=parseInt(i.numFmtId,10);if(t.NumberFmt[c]=o,c>0){if(c>392){for(c=392;c>60&&t.NumberFmt[c]!=null;--c);t.NumberFmt[c]=o}it(o,c)}}break;case"</numFmt>":break;default:if(r.WTF)throw new Error("unrecognized "+i[0]+" in numFmts")}}}function w0(e){var t=["<numFmts>"];return[[5,8],[23,26],[41,44],[50,392]].forEach(function(r){for(var a=r[0];a<=r[1];++a)e[a]!=null&&(t[t.length]=re("numFmt",null,{numFmtId:a,formatCode:Pe(e[a])}))}),t.length===1?"":(t[t.length]="</numFmts>",t[0]=re("numFmts",null,{count:t.length-2}).replace("/>",">"),t.join(""))}var Qa=["numFmtId","fillId","fontId","borderId","xfId"],en=["applyAlignment","applyBorder","applyFill","applyFont","applyNumberFormat","applyProtection","pivotButton","quotePrefix"];function k0(e,t,r){t.CellXf=[];var a,n=!1;(e[0].match(gr)||[]).forEach(function(s){var i=ge(s),o=0;switch(Qr(i[0])){case"<cellXfs":case"<cellXfs>":case"<cellXfs/>":case"</cellXfs>":break;case"<xf":case"<xf/>":for(a=i,delete a[0],o=0;o<Qa.length;++o)a[Qa[o]]&&(a[Qa[o]]=parseInt(a[Qa[o]],10));for(o=0;o<en.length;++o)a[en[o]]&&(a[en[o]]=Be(a[en[o]]));if(t.NumberFmt&&a.numFmtId>392){for(o=392;o>60;--o)if(t.NumberFmt[a.numFmtId]==t.NumberFmt[o]){a.numFmtId=o;break}}t.CellXf.push(a);break;case"</xf>":break;case"<alignment":case"<alignment/>":var c={};i.vertical&&(c.vertical=i.vertical),i.horizontal&&(c.horizontal=i.horizontal),i.textRotation!=null&&(c.textRotation=i.textRotation),i.indent&&(c.indent=i.indent),i.wrapText&&(c.wrapText=Be(i.wrapText)),a.alignment=c;break;case"</alignment>":break;case"<protection":break;case"</protection>":case"<protection/>":break;case"<AlternateContent":n=!0;break;case"</AlternateContent>":n=!1;break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":n=!0;break;case"</ext>":n=!1;break;default:if(r&&r.WTF&&!n)throw new Error("unrecognized "+i[0]+" in cellXfs")}})}function T0(e){var t=[];return t[t.length]=re("cellXfs",null),e.forEach(function(r){t[t.length]=re("xf",null,r)}),t[t.length]="</cellXfs>",t.length===2?"":(t[0]=re("cellXfs",null,{count:t.length-2}).replace("/>",">"),t.join(""))}var E0=function(){var e=/<(?:\w+:)?numFmts([^>]*)>[\S\s]*?<\/(?:\w+:)?numFmts>/,t=/<(?:\w+:)?cellXfs([^>]*)>[\S\s]*?<\/(?:\w+:)?cellXfs>/,r=/<(?:\w+:)?fills([^>]*)>[\S\s]*?<\/(?:\w+:)?fills>/,a=/<(?:\w+:)?fonts([^>]*)>[\S\s]*?<\/(?:\w+:)?fonts>/,n=/<(?:\w+:)?borders([^>]*)>[\S\s]*?<\/(?:\w+:)?borders>/;return function(s,i,o){var c={};if(!s)return c;s=s.replace(/<!--([\s\S]*?)-->/mg,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"");var l;return(l=s.match(e))&&g0(l,c,o),(l=s.match(a))&&v0(l,c,i,o),(l=s.match(r))&&b0(l,c,i,o),(l=s.match(n))&&m0(l,c,i,o),(l=s.match(t))&&k0(l,c,o),c}}();function Oc(e,t){var r=[Ze,re("styleSheet",null,{xmlns:Mt[0],"xmlns:vt":ar.vt})],a;return e.SSF&&(a=w0(e.SSF))!=null&&(r[r.length]=a),r[r.length]='<fonts count="1"><font><sz val="12"/><color theme="1"/><name val="Calibri"/><family val="2"/><scheme val="minor"/></font></fonts>',r[r.length]='<fills count="2"><fill><patternFill patternType="none"/></fill><fill><patternFill patternType="gray125"/></fill></fills>',r[r.length]='<borders count="1"><border><left/><right/><top/><bottom/><diagonal/></border></borders>',r[r.length]='<cellStyleXfs count="1"><xf numFmtId="0" fontId="0" fillId="0" borderId="0"/></cellStyleXfs>',(a=T0(t.cellXfs))&&(r[r.length]=a),r[r.length]='<cellStyles count="1"><cellStyle name="Normal" xfId="0" builtinId="0"/></cellStyles>',r[r.length]='<dxfs count="0"/>',r[r.length]='<tableStyles count="0" defaultTableStyle="TableStyleMedium9" defaultPivotStyle="PivotStyleMedium4"/>',r.length>2&&(r[r.length]="</styleSheet>",r[1]=r[1].replace("/>",">")),r.join("")}function y0(e,t){var r=e.read_shift(2),a=br(e);return[r,a]}function S0(e,t,r){r||(r=j(6+4*t.length)),r.write_shift(2,e),or(t,r);var a=r.length>r.l?r.slice(0,r.l):r;return r.l==null&&(r.l=r.length),a}function _0(e,t,r){var a={};a.sz=e.read_shift(2)/20;var n=i1(e);n.fItalic&&(a.italic=1),n.fCondense&&(a.condense=1),n.fExtend&&(a.extend=1),n.fShadow&&(a.shadow=1),n.fOutline&&(a.outline=1),n.fStrikeout&&(a.strike=1);var s=e.read_shift(2);switch(s===700&&(a.bold=1),e.read_shift(2)){case 1:a.vertAlign="superscript";break;case 2:a.vertAlign="subscript";break}var i=e.read_shift(1);i!=0&&(a.underline=i);var o=e.read_shift(1);o>0&&(a.family=o);var c=e.read_shift(1);switch(c>0&&(a.charset=c),e.l++,a.color=s1(e),e.read_shift(1)){case 1:a.scheme="major";break;case 2:a.scheme="minor";break}return a.name=br(e),a}function x0(e,t){t||(t=j(25+4*32)),t.write_shift(2,e.sz*20),o1(e,t),t.write_shift(2,e.bold?700:400);var r=0;e.vertAlign=="superscript"?r=1:e.vertAlign=="subscript"&&(r=2),t.write_shift(2,r),t.write_shift(1,e.underline||0),t.write_shift(1,e.family||0),t.write_shift(1,e.charset||0),t.write_shift(1,0),hn(e.color,t);var a=0;return a=2,t.write_shift(1,a),or(e.name,t),t.length>t.l?t.slice(0,t.l):t}var A0=["none","solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"],Mn,C0=vr;function Hi(e,t){t||(t=j(4*3+8*7+16*1)),Mn||(Mn=wn(A0));var r=Mn[e.patternType];r==null&&(r=40),t.write_shift(4,r);var a=0;if(r!=40)for(hn({auto:1},t),hn({auto:1},t);a<12;++a)t.write_shift(4,0);else{for(;a<4;++a)t.write_shift(4,0);for(;a<12;++a)t.write_shift(4,0)}return t.length>t.l?t.slice(0,t.l):t}function R0(e,t){var r=e.l+t,a=e.read_shift(2),n=e.read_shift(2);return e.l=r,{ixfe:a,numFmtId:n}}function Nc(e,t,r){r||(r=j(16)),r.write_shift(2,t||0),r.write_shift(2,e.numFmtId||0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(1,0),r.write_shift(1,0);var a=0;return r.write_shift(1,a),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r}function da(e,t){return t||(t=j(10)),t.write_shift(1,0),t.write_shift(1,0),t.write_shift(4,0),t.write_shift(4,0),t}var O0=vr;function N0(e,t){return t||(t=j(51)),t.write_shift(1,0),da(null,t),da(null,t),da(null,t),da(null,t),da(null,t),t.length>t.l?t.slice(0,t.l):t}function I0(e,t){return t||(t=j(12+4*10)),t.write_shift(4,e.xfId),t.write_shift(2,1),t.write_shift(1,0),t.write_shift(1,0),fn(e.name||"",t),t.length>t.l?t.slice(0,t.l):t}function F0(e,t,r){var a=j(2052);return a.write_shift(4,e),fn(t,a),fn(r,a),a.length>a.l?a.slice(0,a.l):a}function P0(e,t,r){var a={};a.NumberFmt=[];for(var n in be)a.NumberFmt[n]=be[n];a.CellXf=[],a.Fonts=[];var s=[],i=!1;return ft(e,function(o,c,l){switch(l){case 44:a.NumberFmt[o[0]]=o[1],it(o[1],o[0]);break;case 43:a.Fonts.push(o),o.color.theme!=null&&t&&t.themeElements&&t.themeElements.clrScheme&&(o.color.rgb=pn(t.themeElements.clrScheme[o.color.theme].rgb,o.color.tint||0));break;case 1025:break;case 45:break;case 46:break;case 47:s[s.length-1]==617&&a.CellXf.push(o);break;case 48:case 507:case 572:case 475:break;case 1171:case 2102:case 1130:case 512:case 2095:case 3072:break;case 35:i=!0;break;case 36:i=!1;break;case 37:s.push(l),i=!0;break;case 38:s.pop(),i=!1;break;default:if(c.T>0)s.push(l);else if(c.T<0)s.pop();else if(!i||r.WTF&&s[s.length-1]!=37)throw new Error("Unexpected record 0x"+l.toString(16))}}),a}function D0(e,t){if(t){var r=0;[[5,8],[23,26],[41,44],[50,392]].forEach(function(a){for(var n=a[0];n<=a[1];++n)t[n]!=null&&++r}),r!=0&&(Y(e,615,Yr(r)),[[5,8],[23,26],[41,44],[50,392]].forEach(function(a){for(var n=a[0];n<=a[1];++n)t[n]!=null&&Y(e,44,S0(n,t[n]))}),Y(e,616))}}function L0(e){var t=1;Y(e,611,Yr(t)),Y(e,43,x0({sz:12,color:{theme:1},name:"Calibri",family:2})),Y(e,612)}function M0(e){var t=2;Y(e,603,Yr(t)),Y(e,45,Hi({patternType:"none"})),Y(e,45,Hi({patternType:"gray125"})),Y(e,604)}function U0(e){var t=1;Y(e,613,Yr(t)),Y(e,46,N0()),Y(e,614)}function B0(e){var t=1;Y(e,626,Yr(t)),Y(e,47,Nc({numFmtId:0},65535)),Y(e,627)}function W0(e,t){Y(e,617,Yr(t.length)),t.forEach(function(r){Y(e,47,Nc(r,0))}),Y(e,618)}function z0(e){var t=1;Y(e,619,Yr(t)),Y(e,48,I0({xfId:0,name:"Normal"})),Y(e,620)}function H0(e){var t=0;Y(e,505,Yr(t)),Y(e,506)}function G0(e){var t=0;Y(e,508,F0(t,"TableStyleMedium9","PivotStyleMedium4")),Y(e,509)}function V0(e,t){var r=_r();return Y(r,278),D0(r,e.SSF),L0(r),M0(r),U0(r),B0(r),W0(r,t.cellXfs),z0(r),H0(r),G0(r),Y(r,279),r.end()}var j0=["</a:lt1>","</a:dk1>","</a:lt2>","</a:dk2>","</a:accent1>","</a:accent2>","</a:accent3>","</a:accent4>","</a:accent5>","</a:accent6>","</a:hlink>","</a:folHlink>"];function $0(e,t,r){t.themeElements.clrScheme=[];var a={};(e[0].match(gr)||[]).forEach(function(n){var s=ge(n);switch(s[0]){case"<a:clrScheme":case"</a:clrScheme>":break;case"<a:srgbClr":a.rgb=s.val;break;case"<a:sysClr":a.rgb=s.lastClr;break;case"<a:dk1>":case"</a:dk1>":case"<a:lt1>":case"</a:lt1>":case"<a:dk2>":case"</a:dk2>":case"<a:lt2>":case"</a:lt2>":case"<a:accent1>":case"</a:accent1>":case"<a:accent2>":case"</a:accent2>":case"<a:accent3>":case"</a:accent3>":case"<a:accent4>":case"</a:accent4>":case"<a:accent5>":case"</a:accent5>":case"<a:accent6>":case"</a:accent6>":case"<a:hlink>":case"</a:hlink>":case"<a:folHlink>":case"</a:folHlink>":s[0].charAt(1)==="/"?(t.themeElements.clrScheme[j0.indexOf(s[0])]=a,a={}):a.name=s[0].slice(3,s[0].length-1);break;default:if(r&&r.WTF)throw new Error("Unrecognized "+s[0]+" in clrScheme")}})}function X0(){}function Y0(){}var K0=/<a:clrScheme([^>]*)>[\s\S]*<\/a:clrScheme>/,J0=/<a:fontScheme([^>]*)>[\s\S]*<\/a:fontScheme>/,Z0=/<a:fmtScheme([^>]*)>[\s\S]*<\/a:fmtScheme>/;function q0(e,t,r){t.themeElements={};var a;[["clrScheme",K0,$0],["fontScheme",J0,X0],["fmtScheme",Z0,Y0]].forEach(function(n){if(!(a=e.match(n[1])))throw new Error(n[0]+" not found in themeElements");n[2](a,t,r)})}var Q0=/<a:themeElements([^>]*)>[\s\S]*<\/a:themeElements>/;function Ic(e,t){(!e||e.length===0)&&(e=Fs());var r,a={};if(!(r=e.match(Q0)))throw new Error("themeElements not found in theme");return q0(r[0],a,t),a.raw=e,a}function Fs(e,t){if(t&&t.themeXLSX)return t.themeXLSX;if(e&&typeof e.raw=="string")return e.raw;var r=[Ze];return r[r.length]='<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">',r[r.length]="<a:themeElements>",r[r.length]='<a:clrScheme name="Office">',r[r.length]='<a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1>',r[r.length]='<a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1>',r[r.length]='<a:dk2><a:srgbClr val="1F497D"/></a:dk2>',r[r.length]='<a:lt2><a:srgbClr val="EEECE1"/></a:lt2>',r[r.length]='<a:accent1><a:srgbClr val="4F81BD"/></a:accent1>',r[r.length]='<a:accent2><a:srgbClr val="C0504D"/></a:accent2>',r[r.length]='<a:accent3><a:srgbClr val="9BBB59"/></a:accent3>',r[r.length]='<a:accent4><a:srgbClr val="8064A2"/></a:accent4>',r[r.length]='<a:accent5><a:srgbClr val="4BACC6"/></a:accent5>',r[r.length]='<a:accent6><a:srgbClr val="F79646"/></a:accent6>',r[r.length]='<a:hlink><a:srgbClr val="0000FF"/></a:hlink>',r[r.length]='<a:folHlink><a:srgbClr val="800080"/></a:folHlink>',r[r.length]="</a:clrScheme>",r[r.length]='<a:fontScheme name="Office">',r[r.length]="<a:majorFont>",r[r.length]='<a:latin typeface="Cambria"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Times New Roman"/>',r[r.length]='<a:font script="Hebr" typeface="Times New Roman"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="MoolBoran"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Times New Roman"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:majorFont>",r[r.length]="<a:minorFont>",r[r.length]='<a:latin typeface="Calibri"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Arial"/>',r[r.length]='<a:font script="Hebr" typeface="Arial"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="DaunPenh"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Arial"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:minorFont>",r[r.length]="</a:fontScheme>",r[r.length]='<a:fmtScheme name="Office">',r[r.length]="<a:fillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="1"/>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="0"/>',r[r.length]="</a:gradFill>",r[r.length]="</a:fillStyleLst>",r[r.length]="<a:lnStyleLst>",r[r.length]='<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]="</a:lnStyleLst>",r[r.length]="<a:effectStyleLst>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]='<a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d>',r[r.length]='<a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d>',r[r.length]="</a:effectStyle>",r[r.length]="</a:effectStyleLst>",r[r.length]="<a:bgFillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]="</a:bgFillStyleLst>",r[r.length]="</a:fmtScheme>",r[r.length]="</a:themeElements>",r[r.length]="<a:objectDefaults>",r[r.length]="<a:spDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style>',r[r.length]="</a:spDef>",r[r.length]="<a:lnDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style>',r[r.length]="</a:lnDef>",r[r.length]="</a:objectDefaults>",r[r.length]="<a:extraClrSchemeLst/>",r[r.length]="</a:theme>",r.join("")}function ed(e,t,r){var a=e.l+t,n=e.read_shift(4);if(n!==124226){if(!r.cellStyles){e.l=a;return}var s=e.slice(e.l);e.l=a;var i;try{i=So(s,{type:"array"})}catch{return}var o=Nr(i,"theme/theme/theme1.xml",!0);if(o)return Ic(o,r)}}function rd(e){return e.read_shift(4)}function td(e){var t={};switch(t.xclrType=e.read_shift(2),t.nTintShade=e.read_shift(2),t.xclrType){case 0:e.l+=4;break;case 1:t.xclrValue=ad(e,4);break;case 2:t.xclrValue=hc(e);break;case 3:t.xclrValue=rd(e);break;case 4:e.l+=4;break}return e.l+=8,t}function ad(e,t){return vr(e,t)}function nd(e,t){return vr(e,t)}function sd(e){var t=e.read_shift(2),r=e.read_shift(2)-4,a=[t];switch(t){case 4:case 5:case 7:case 8:case 9:case 10:case 11:case 13:a[1]=td(e);break;case 6:a[1]=nd(e,r);break;case 14:case 15:a[1]=e.read_shift(r===1?1:2);break;default:throw new Error("Unrecognized ExtProp type: "+t+" "+r)}return a}function id(e,t){var r=e.l+t;e.l+=2;var a=e.read_shift(2);e.l+=2;for(var n=e.read_shift(2),s=[];n-- >0;)s.push(sd(e,r-e.l));return{ixfe:a,ext:s}}function od(e,t){t.forEach(function(r){switch(r[0]){}})}function cd(e,t){return{flags:e.read_shift(4),version:e.read_shift(4),name:br(e)}}function ld(e){var t=j(12+2*e.name.length);return t.write_shift(4,e.flags),t.write_shift(4,e.version),or(e.name,t),t.slice(0,t.l)}function fd(e){for(var t=[],r=e.read_shift(4);r-- >0;)t.push([e.read_shift(4),e.read_shift(4)]);return t}function hd(e){var t=j(4+8*e.length);t.write_shift(4,e.length);for(var r=0;r<e.length;++r)t.write_shift(4,e[r][0]),t.write_shift(4,e[r][1]);return t}function ud(e,t){var r=j(8+2*t.length);return r.write_shift(4,e),or(t,r),r.slice(0,r.l)}function dd(e){return e.l+=4,e.read_shift(4)!=0}function pd(e,t){var r=j(8);return r.write_shift(4,e),r.write_shift(4,1),r}function md(e,t,r){var a={Types:[],Cell:[],Value:[]},n=r||{},s=[],i=!1,o=2;return ft(e,function(c,l,f){switch(f){case 335:a.Types.push({name:c.name});break;case 51:c.forEach(function(p){o==1?a.Cell.push({type:a.Types[p[0]-1].name,index:p[1]}):o==0&&a.Value.push({type:a.Types[p[0]-1].name,index:p[1]})});break;case 337:o=c?1:0;break;case 338:o=2;break;case 35:s.push(f),i=!0;break;case 36:s.pop(),i=!1;break;default:if(!l.T&&(!i||n.WTF&&s[s.length-1]!=35))throw new Error("Unexpected record 0x"+f.toString(16))}}),a}function bd(){var e=_r();return Y(e,332),Y(e,334,Yr(1)),Y(e,335,ld({name:"XLDAPR",version:12e4,flags:3496657072})),Y(e,336),Y(e,339,ud(1,"XLDAPR")),Y(e,52),Y(e,35,Yr(514)),Y(e,4096,Yr(0)),Y(e,4097,Br(1)),Y(e,36),Y(e,53),Y(e,340),Y(e,337,pd(1)),Y(e,51,hd([[1,0]])),Y(e,338),Y(e,333),e.end()}function vd(e,t,r){var a={Types:[],Cell:[],Value:[]};if(!e)return a;var n=!1,s=2,i;return e.replace(gr,function(o){var c=ge(o);switch(Qr(c[0])){case"<?xml":break;case"<metadata":case"</metadata>":break;case"<metadataTypes":case"</metadataTypes>":break;case"<metadataType":a.Types.push({name:c.name});break;case"</metadataType>":break;case"<futureMetadata":for(var l=0;l<a.Types.length;++l)a.Types[l].name==c.name&&(i=a.Types[l]);break;case"</futureMetadata>":break;case"<bk>":break;case"</bk>":break;case"<rc":s==1?a.Cell.push({type:a.Types[c.t-1].name,index:+c.v}):s==0&&a.Value.push({type:a.Types[c.t-1].name,index:+c.v});break;case"</rc>":break;case"<cellMetadata":s=1;break;case"</cellMetadata>":s=2;break;case"<valueMetadata":s=0;break;case"</valueMetadata>":s=2;break;case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<ext":n=!0;break;case"</ext>":n=!1;break;case"<rvb":if(!i)break;i.offsets||(i.offsets=[]),i.offsets.push(+c.i);break;default:if(!n&&r.WTF)throw new Error("unrecognized "+c[0]+" in metadata")}return o}),a}function Fc(){var e=[Ze];return e.push(`<metadata xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:xlrd="http://schemas.microsoft.com/office/spreadsheetml/2017/richdata" xmlns:xda="http://schemas.microsoft.com/office/spreadsheetml/2017/dynamicarray">
  <metadataTypes count="1">
    <metadataType name="XLDAPR" minSupportedVersion="120000" copy="1" pasteAll="1" pasteValues="1" merge="1" splitFirst="1" rowColShift="1" clearFormats="1" clearComments="1" assign="1" coerce="1" cellMeta="1"/>
  </metadataTypes>
  <futureMetadata name="XLDAPR" count="1">
    <bk>
      <extLst>
        <ext uri="{bdbb8cdc-fa1e-496e-a857-3c3f30c029c3}">
          <xda:dynamicArrayProperties fDynamic="1" fCollapsed="0"/>
        </ext>
      </extLst>
    </bk>
  </futureMetadata>
  <cellMetadata count="1">
    <bk>
      <rc t="1" v="0"/>
    </bk>
  </cellMetadata>
</metadata>`),e.join("")}function gd(e){var t=[];if(!e)return t;var r=1;return(e.match(gr)||[]).forEach(function(a){var n=ge(a);switch(n[0]){case"<?xml":break;case"<calcChain":case"<calcChain>":case"</calcChain>":break;case"<c":delete n[0],n.i?r=n.i:n.i=r,t.push(n);break}}),t}function wd(e){var t={};t.i=e.read_shift(4);var r={};r.r=e.read_shift(4),r.c=e.read_shift(4),t.r=ve(r);var a=e.read_shift(1);return a&2&&(t.l="1"),a&8&&(t.a="1"),t}function kd(e,t,r){var a=[];return ft(e,function(n,s,i){switch(i){case 63:a.push(n);break;default:if(!s.T)throw new Error("Unexpected record 0x"+i.toString(16))}}),a}function Td(e,t,r,a){if(!e)return e;var n=a||{},s=!1;ft(e,function(i,o,c){switch(c){case 359:case 363:case 364:case 366:case 367:case 368:case 369:case 370:case 371:case 472:case 577:case 578:case 579:case 580:case 581:case 582:case 583:case 584:case 585:case 586:case 587:break;case 35:s=!0;break;case 36:s=!1;break;default:if(!o.T&&(!s||n.WTF))throw new Error("Unexpected record 0x"+c.toString(16))}},n)}function Ed(e,t){if(!e)return"??";var r=(e.match(/<c:chart [^>]*r:id="([^"]*)"/)||["",""])[1];return t["!id"][r].Target}var Yt=1024;function Pc(e,t){for(var r=[21600,21600],a=["m0,0l0",r[1],r[0],r[1],r[0],"0xe"].join(","),n=[re("xml",null,{"xmlns:v":Cr.v,"xmlns:o":Cr.o,"xmlns:x":Cr.x,"xmlns:mv":Cr.mv}).replace(/\/>/,">"),re("o:shapelayout",re("o:idmap",null,{"v:ext":"edit",data:e}),{"v:ext":"edit"}),re("v:shapetype",[re("v:stroke",null,{joinstyle:"miter"}),re("v:path",null,{gradientshapeok:"t","o:connecttype":"rect"})].join(""),{id:"_x0000_t202","o:spt":202,coordsize:r.join(","),path:a})];Yt<e*1e3;)Yt+=1e3;return t.forEach(function(s){var i=je(s[0]),o={color2:"#BEFF82",type:"gradient"};o.type=="gradient"&&(o.angle="-180");var c=o.type=="gradient"?re("o:fill",null,{type:"gradientUnscaled","v:ext":"view"}):null,l=re("v:fill",c,o),f={on:"t",obscured:"t"};++Yt,n=n.concat(["<v:shape"+Ca({id:"_x0000_s"+Yt,type:"#_x0000_t202",style:"position:absolute; margin-left:80pt;margin-top:5pt;width:104pt;height:64pt;z-index:10"+(s[1].hidden?";visibility:hidden":""),fillcolor:"#ECFAD4",strokecolor:"#edeaa1"})+">",l,re("v:shadow",null,f),re("v:path",null,{"o:connecttype":"none"}),'<v:textbox><div style="text-align:left"></div></v:textbox>','<x:ClientData ObjectType="Note">',"<x:MoveWithCells/>","<x:SizeWithCells/>",fr("x:Anchor",[i.c+1,0,i.r+1,0,i.c+3,20,i.r+5,20].join(",")),fr("x:AutoFill","False"),fr("x:Row",String(i.r)),fr("x:Column",String(i.c)),s[1].hidden?"":"<x:Visible/>","</x:ClientData>","</v:shape>"])}),n.push("</xml>"),n.join("")}function Gi(e,t,r,a){var n=Array.isArray(e),s;t.forEach(function(i){var o=je(i.ref);if(n?(e[o.r]||(e[o.r]=[]),s=e[o.r][o.c]):s=e[i.ref],!s){s={t:"z"},n?e[o.r][o.c]=s:e[i.ref]=s;var c=Re(e["!ref"]||"BDWGO1000001:A1");c.s.r>o.r&&(c.s.r=o.r),c.e.r<o.r&&(c.e.r=o.r),c.s.c>o.c&&(c.s.c=o.c),c.e.c<o.c&&(c.e.c=o.c);var l=we(c);l!==e["!ref"]&&(e["!ref"]=l)}s.c||(s.c=[]);var f={a:i.author,t:i.t,r:i.r,T:r};i.h&&(f.h=i.h);for(var p=s.c.length-1;p>=0;--p){if(!r&&s.c[p].T)return;r&&!s.c[p].T&&s.c.splice(p,1)}if(r&&a){for(p=0;p<a.length;++p)if(f.a==a[p].id){f.a=a[p].name||f.a;break}}s.c.push(f)})}function yd(e,t){if(e.match(/<(?:\w+:)?comments *\/>/))return[];var r=[],a=[],n=e.match(/<(?:\w+:)?authors>([\s\S]*)<\/(?:\w+:)?authors>/);n&&n[1]&&n[1].split(/<\/\w*:?author>/).forEach(function(i){if(!(i===""||i.trim()==="")){var o=i.match(/<(?:\w+:)?author[^>]*>(.*)/);o&&r.push(o[1])}});var s=e.match(/<(?:\w+:)?commentList>([\s\S]*)<\/(?:\w+:)?commentList>/);return s&&s[1]&&s[1].split(/<\/\w*:?comment>/).forEach(function(i){if(!(i===""||i.trim()==="")){var o=i.match(/<(?:\w+:)?comment[^>]*>/);if(o){var c=ge(o[0]),l={author:c.authorId&&r[c.authorId]||"sheetjsghost",ref:c.ref,guid:c.guid},f=je(c.ref);if(!(t.sheetRows&&t.sheetRows<=f.r)){var p=i.match(/<(?:\w+:)?text>([\s\S]*)<\/(?:\w+:)?text>/),h=!!p&&!!p[1]&&Os(p[1])||{r:"",t:"",h:""};l.r=h.r,h.r=="<t></t>"&&(h.t=h.h=""),l.t=(h.t||"").replace(/\r\n/g,`
`).replace(/\r/g,`
`),t.cellHTML&&(l.h=h.h),a.push(l)}}}}),a}function Dc(e){var t=[Ze,re("comments",null,{xmlns:Mt[0]})],r=[];return t.push("<authors>"),e.forEach(function(a){a[1].forEach(function(n){var s=Pe(n.a);r.indexOf(s)==-1&&(r.push(s),t.push("<author>"+s+"</author>")),n.T&&n.ID&&r.indexOf("tc="+n.ID)==-1&&(r.push("tc="+n.ID),t.push("<author>tc="+n.ID+"</author>"))})}),r.length==0&&(r.push("SheetJ5"),t.push("<author>SheetJ5</author>")),t.push("</authors>"),t.push("<commentList>"),e.forEach(function(a){var n=0,s=[];if(a[1][0]&&a[1][0].T&&a[1][0].ID?n=r.indexOf("tc="+a[1][0].ID):a[1].forEach(function(c){c.a&&(n=r.indexOf(Pe(c.a))),s.push(c.t||"")}),t.push('<comment ref="'+a[0]+'" authorId="'+n+'"><text>'),s.length<=1)t.push(fr("t",Pe(s[0]||"")));else{for(var i=`Comment:
    `+s[0]+`
`,o=1;o<s.length;++o)i+=`Reply:
    `+s[o]+`
`;t.push(fr("t",Pe(i)))}t.push("</text></comment>")}),t.push("</commentList>"),t.length>2&&(t[t.length]="</comments>",t[1]=t[1].replace("/>",">")),t.join("")}function Sd(e,t){var r=[],a=!1,n={},s=0;return e.replace(gr,function(i,o){var c=ge(i);switch(Qr(c[0])){case"<?xml":break;case"<ThreadedComments":break;case"</ThreadedComments>":break;case"<threadedComment":n={author:c.personId,guid:c.id,ref:c.ref,T:1};break;case"</threadedComment>":n.t!=null&&r.push(n);break;case"<text>":case"<text":s=o+i.length;break;case"</text>":n.t=e.slice(s,o).replace(/\r\n/g,`
`).replace(/\r/g,`
`);break;case"<mentions":case"<mentions>":a=!0;break;case"</mentions>":a=!1;break;case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<ext":a=!0;break;case"</ext>":a=!1;break;default:if(!a&&t.WTF)throw new Error("unrecognized "+c[0]+" in threaded comments")}return i}),r}function _d(e,t,r){var a=[Ze,re("ThreadedComments",null,{xmlns:ar.TCMNT}).replace(/[\/]>/,">")];return e.forEach(function(n){var s="";(n[1]||[]).forEach(function(i,o){if(!i.T){delete i.ID;return}i.a&&t.indexOf(i.a)==-1&&t.push(i.a);var c={ref:n[0],id:"{54EE7951-**************-"+("000000000000"+r.tcid++).slice(-12)+"}"};o==0?s=c.id:c.parentId=s,i.ID=c.id,i.a&&(c.personId="{54EE7950-**************-"+("000000000000"+t.indexOf(i.a)).slice(-12)+"}"),a.push(re("threadedComment",fr("text",i.t||""),c))})}),a.push("</ThreadedComments>"),a.join("")}function xd(e,t){var r=[],a=!1;return e.replace(gr,function(n){var s=ge(n);switch(Qr(s[0])){case"<?xml":break;case"<personList":break;case"</personList>":break;case"<person":r.push({name:s.displayname,id:s.id});break;case"</person>":break;case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<ext":a=!0;break;case"</ext>":a=!1;break;default:if(!a&&t.WTF)throw new Error("unrecognized "+s[0]+" in threaded comments")}return n}),r}function Ad(e){var t=[Ze,re("personList",null,{xmlns:ar.TCMNT,"xmlns:x":Mt[0]}).replace(/[\/]>/,">")];return e.forEach(function(r,a){t.push(re("person",null,{displayName:r,id:"{54EE7950-**************-"+("000000000000"+a).slice(-12)+"}",userId:r,providerId:"None"}))}),t.push("</personList>"),t.join("")}function Cd(e){var t={};t.iauthor=e.read_shift(4);var r=zt(e);return t.rfx=r.s,t.ref=ve(r.s),e.l+=16,t}function Rd(e,t){return t==null&&(t=j(36)),t.write_shift(4,e[1].iauthor),oa(e[0],t),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t}var Od=br;function Nd(e){return or(e.slice(0,54))}function Id(e,t){var r=[],a=[],n={},s=!1;return ft(e,function(i,o,c){switch(c){case 632:a.push(i);break;case 635:n=i;break;case 637:n.t=i.t,n.h=i.h,n.r=i.r;break;case 636:if(n.author=a[n.iauthor],delete n.iauthor,t.sheetRows&&n.rfx&&t.sheetRows<=n.rfx.r)break;n.t||(n.t=""),delete n.rfx,r.push(n);break;case 3072:break;case 35:s=!0;break;case 36:s=!1;break;case 37:break;case 38:break;default:if(!o.T&&(!s||t.WTF))throw new Error("Unexpected record 0x"+c.toString(16))}}),r}function Fd(e){var t=_r(),r=[];return Y(t,628),Y(t,630),e.forEach(function(a){a[1].forEach(function(n){r.indexOf(n.a)>-1||(r.push(n.a.slice(0,54)),Y(t,632,Nd(n.a)))})}),Y(t,631),Y(t,633),e.forEach(function(a){a[1].forEach(function(n){n.iauthor=r.indexOf(n.a);var s={s:je(a[0]),e:je(a[0])};Y(t,635,Rd([s,n])),n.t&&n.t.length>0&&Y(t,637,r1(n)),Y(t,636),delete n.iauthor})}),Y(t,634),Y(t,629),t.end()}var Pd="application/vnd.ms-office.vbaProject";function Dd(e){var t=pe.utils.cfb_new({root:"R"});return e.FullPaths.forEach(function(r,a){if(!(r.slice(-1)==="/"||!r.match(/_VBA_PROJECT_CUR/))){var n=r.replace(/^[^\/]*/,"R").replace(/\/_VBA_PROJECT_CUR\u0000*/,"");pe.utils.cfb_add(t,n,e.FileIndex[a].content)}}),pe.write(t)}function Ld(e,t){t.FullPaths.forEach(function(r,a){if(a!=0){var n=r.replace(/[^\/]*[\/]/,"/_VBA_PROJECT_CUR/");n.slice(-1)!=="/"&&pe.utils.cfb_add(e,n,t.FileIndex[a].content)}})}var Lc=["xlsb","xlsm","xlam","biff8","xla"];function Md(){return{"!type":"dialog"}}function Ud(){return{"!type":"dialog"}}function Bd(){return{"!type":"macro"}}function Wd(){return{"!type":"macro"}}var qt=function(){var e=/(^|[^A-Za-z_])R(\[?-?\d+\]|[1-9]\d*|)C(\[?-?\d+\]|[1-9]\d*|)(?![A-Za-z0-9_])/g,t={r:0,c:0};function r(a,n,s,i){var o=!1,c=!1;s.length==0?c=!0:s.charAt(0)=="["&&(c=!0,s=s.slice(1,-1)),i.length==0?o=!0:i.charAt(0)=="["&&(o=!0,i=i.slice(1,-1));var l=s.length>0?parseInt(s,10)|0:0,f=i.length>0?parseInt(i,10)|0:0;return o?f+=t.c:--f,c?l+=t.r:--l,n+(o?"":"$")+Ve(f)+(c?"":"$")+Je(l)}return function(a,n){return t=n,a.replace(e,r)}}(),Ps=/(^|[^._A-Z0-9])([$]?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])([$]?)(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})(?![_.\(A-Za-z0-9])/g,Ds=function(){return function(e,t){return e.replace(Ps,function(r,a,n,s,i,o){var c=Ts(s)-(n?0:t.c),l=ks(o)-(i?0:t.r),f=l==0?"":i?l+1:"["+l+"]",p=c==0?"":n?c+1:"["+c+"]";return a+"R"+f+"C"+p})}}();function Mc(e,t){return e.replace(Ps,function(r,a,n,s,i,o){return a+(n=="$"?n+s:Ve(Ts(s)+t.c))+(i=="$"?i+o:Je(ks(o)+t.r))})}function zd(e,t,r){var a=xr(t),n=a.s,s=je(r),i={r:s.r-n.r,c:s.c-n.c};return Mc(e,i)}function Hd(e){return e.length!=1}function Vi(e){return e.replace(/_xlfn\./g,"")}function qe(e){e.l+=1}function wt(e,t){var r=e.read_shift(2);return[r&16383,r>>14&1,r>>15&1]}function Uc(e,t,r){var a=2;if(r){if(r.biff>=2&&r.biff<=5)return Bc(e);r.biff==12&&(a=4)}var n=e.read_shift(a),s=e.read_shift(a),i=wt(e),o=wt(e);return{s:{r:n,c:i[0],cRel:i[1],rRel:i[2]},e:{r:s,c:o[0],cRel:o[1],rRel:o[2]}}}function Bc(e){var t=wt(e),r=wt(e),a=e.read_shift(1),n=e.read_shift(1);return{s:{r:t[0],c:a,cRel:t[1],rRel:t[2]},e:{r:r[0],c:n,cRel:r[1],rRel:r[2]}}}function Gd(e,t,r){if(r.biff<8)return Bc(e);var a=e.read_shift(r.biff==12?4:2),n=e.read_shift(r.biff==12?4:2),s=wt(e),i=wt(e);return{s:{r:a,c:s[0],cRel:s[1],rRel:s[2]},e:{r:n,c:i[0],cRel:i[1],rRel:i[2]}}}function Wc(e,t,r){if(r&&r.biff>=2&&r.biff<=5)return Vd(e);var a=e.read_shift(r&&r.biff==12?4:2),n=wt(e);return{r:a,c:n[0],cRel:n[1],rRel:n[2]}}function Vd(e){var t=wt(e),r=e.read_shift(1);return{r:t[0],c:r,cRel:t[1],rRel:t[2]}}function jd(e){var t=e.read_shift(2),r=e.read_shift(2);return{r:t,c:r&255,fQuoted:!!(r&16384),cRel:r>>15,rRel:r>>15}}function $d(e,t,r){var a=r&&r.biff?r.biff:8;if(a>=2&&a<=5)return Xd(e);var n=e.read_shift(a>=12?4:2),s=e.read_shift(2),i=(s&16384)>>14,o=(s&32768)>>15;if(s&=16383,o==1)for(;n>524287;)n-=1048576;if(i==1)for(;s>8191;)s=s-16384;return{r:n,c:s,cRel:i,rRel:o}}function Xd(e){var t=e.read_shift(2),r=e.read_shift(1),a=(t&32768)>>15,n=(t&16384)>>14;return t&=16383,a==1&&t>=8192&&(t=t-16384),n==1&&r>=128&&(r=r-256),{r:t,c:r,cRel:n,rRel:a}}function Yd(e,t,r){var a=(e[e.l++]&96)>>5,n=Uc(e,r.biff>=2&&r.biff<=5?6:8,r);return[a,n]}function Kd(e,t,r){var a=(e[e.l++]&96)>>5,n=e.read_shift(2,"i"),s=8;if(r)switch(r.biff){case 5:e.l+=12,s=6;break;case 12:s=12;break}var i=Uc(e,s,r);return[a,n,i]}function Jd(e,t,r){var a=(e[e.l++]&96)>>5;return e.l+=r&&r.biff>8?12:r.biff<8?6:8,[a]}function Zd(e,t,r){var a=(e[e.l++]&96)>>5,n=e.read_shift(2),s=8;if(r)switch(r.biff){case 5:e.l+=12,s=6;break;case 12:s=12;break}return e.l+=s,[a,n]}function qd(e,t,r){var a=(e[e.l++]&96)>>5,n=Gd(e,t-1,r);return[a,n]}function Qd(e,t,r){var a=(e[e.l++]&96)>>5;return e.l+=r.biff==2?6:r.biff==12?14:7,[a]}function ji(e){var t=e[e.l+1]&1,r=1;return e.l+=4,[t,r]}function e2(e,t,r){e.l+=2;for(var a=e.read_shift(r&&r.biff==2?1:2),n=[],s=0;s<=a;++s)n.push(e.read_shift(r&&r.biff==2?1:2));return n}function r2(e,t,r){var a=e[e.l+1]&255?1:0;return e.l+=2,[a,e.read_shift(r&&r.biff==2?1:2)]}function t2(e,t,r){var a=e[e.l+1]&255?1:0;return e.l+=2,[a,e.read_shift(r&&r.biff==2?1:2)]}function a2(e){var t=e[e.l+1]&255?1:0;return e.l+=2,[t,e.read_shift(2)]}function n2(e,t,r){var a=e[e.l+1]&255?1:0;return e.l+=r&&r.biff==2?3:4,[a]}function zc(e){var t=e.read_shift(1),r=e.read_shift(1);return[t,r]}function s2(e){return e.read_shift(2),zc(e)}function i2(e){return e.read_shift(2),zc(e)}function o2(e,t,r){var a=(e[e.l]&96)>>5;e.l+=1;var n=Wc(e,0,r);return[a,n]}function c2(e,t,r){var a=(e[e.l]&96)>>5;e.l+=1;var n=$d(e,0,r);return[a,n]}function l2(e,t,r){var a=(e[e.l]&96)>>5;e.l+=1;var n=e.read_shift(2);r&&r.biff==5&&(e.l+=12);var s=Wc(e,0,r);return[a,n,s]}function f2(e,t,r){var a=(e[e.l]&96)>>5;e.l+=1;var n=e.read_shift(r&&r.biff<=3?1:2);return[dp[n],Vc[n],a]}function h2(e,t,r){var a=e[e.l++],n=e.read_shift(1),s=r&&r.biff<=3?[a==88?-1:0,e.read_shift(1)]:u2(e);return[n,(s[0]===0?Vc:up)[s[1]]]}function u2(e){return[e[e.l+1]>>7,e.read_shift(2)&32767]}function d2(e,t,r){e.l+=r&&r.biff==2?3:4}function p2(e,t,r){if(e.l++,r&&r.biff==12)return[e.read_shift(4,"i"),0];var a=e.read_shift(2),n=e.read_shift(r&&r.biff==2?1:2);return[a,n]}function m2(e){return e.l++,ht[e.read_shift(1)]}function b2(e){return e.l++,e.read_shift(2)}function v2(e){return e.l++,e.read_shift(1)!==0}function g2(e){return e.l++,mr(e)}function w2(e,t,r){return e.l++,Wa(e,t-1,r)}function k2(e,t){var r=[e.read_shift(1)];if(t==12)switch(r[0]){case 2:r[0]=4;break;case 4:r[0]=16;break;case 0:r[0]=1;break;case 1:r[0]=2;break}switch(r[0]){case 4:r[1]=Ke(e,1)?"TRUE":"FALSE",t!=12&&(e.l+=7);break;case 37:case 16:r[1]=ht[e[e.l]],e.l+=t==12?4:8;break;case 0:e.l+=8;break;case 1:r[1]=mr(e);break;case 2:r[1]=Ht(e,0,{biff:t>0&&t<8?2:t});break;default:throw new Error("Bad SerAr: "+r[0])}return r}function T2(e,t,r){for(var a=e.read_shift(r.biff==12?4:2),n=[],s=0;s!=a;++s)n.push((r.biff==12?zt:En)(e));return n}function E2(e,t,r){var a=0,n=0;r.biff==12?(a=e.read_shift(4),n=e.read_shift(4)):(n=1+e.read_shift(1),a=1+e.read_shift(2)),r.biff>=2&&r.biff<8&&(--a,--n==0&&(n=256));for(var s=0,i=[];s!=a&&(i[s]=[]);++s)for(var o=0;o!=n;++o)i[s][o]=k2(e,r.biff);return i}function y2(e,t,r){var a=e.read_shift(1)>>>5&3,n=!r||r.biff>=8?4:2,s=e.read_shift(n);switch(r.biff){case 2:e.l+=5;break;case 3:case 4:e.l+=8;break;case 5:e.l+=12;break}return[a,0,s]}function S2(e,t,r){if(r.biff==5)return _2(e);var a=e.read_shift(1)>>>5&3,n=e.read_shift(2),s=e.read_shift(4);return[a,n,s]}function _2(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2,"i");e.l+=8;var a=e.read_shift(2);return e.l+=12,[t,r,a]}function x2(e,t,r){var a=e.read_shift(1)>>>5&3;e.l+=r&&r.biff==2?3:4;var n=e.read_shift(r&&r.biff==2?1:2);return[a,n]}function A2(e,t,r){var a=e.read_shift(1)>>>5&3,n=e.read_shift(r&&r.biff==2?1:2);return[a,n]}function C2(e,t,r){var a=e.read_shift(1)>>>5&3;return e.l+=4,r.biff<8&&e.l--,r.biff==12&&(e.l+=2),[a]}function R2(e,t,r){var a=(e[e.l++]&96)>>5,n=e.read_shift(2),s=4;if(r)switch(r.biff){case 5:s=15;break;case 12:s=6;break}return e.l+=s,[a,n]}var O2=vr,N2=vr,I2=vr;function Ha(e,t,r){return e.l+=2,[jd(e)]}function Ls(e){return e.l+=6,[]}var F2=Ha,P2=Ls,D2=Ls,L2=Ha;function Hc(e){return e.l+=2,[er(e),e.read_shift(2)&1]}var M2=Ha,U2=Hc,B2=Ls,W2=Ha,z2=Ha,H2=["Data","All","Headers","??","?Data2","??","?DataHeaders","??","Totals","??","??","??","?DataTotals","??","??","??","?Current"];function G2(e){e.l+=2;var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(4),n=e.read_shift(2),s=e.read_shift(2),i=H2[r>>2&31];return{ixti:t,coltype:r&3,rt:i,idx:a,c:n,C:s}}function V2(e){return e.l+=2,[e.read_shift(4)]}function j2(e,t,r){return e.l+=5,e.l+=2,e.l+=r.biff==2?1:4,["PTGSHEET"]}function $2(e,t,r){return e.l+=r.biff==2?4:5,["PTGENDSHEET"]}function X2(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2);return[t,r]}function Y2(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2);return[t,r]}function K2(e){return e.l+=4,[0,0]}var $i={1:{n:"PtgExp",f:p2},2:{n:"PtgTbl",f:I2},3:{n:"PtgAdd",f:qe},4:{n:"PtgSub",f:qe},5:{n:"PtgMul",f:qe},6:{n:"PtgDiv",f:qe},7:{n:"PtgPower",f:qe},8:{n:"PtgConcat",f:qe},9:{n:"PtgLt",f:qe},10:{n:"PtgLe",f:qe},11:{n:"PtgEq",f:qe},12:{n:"PtgGe",f:qe},13:{n:"PtgGt",f:qe},14:{n:"PtgNe",f:qe},15:{n:"PtgIsect",f:qe},16:{n:"PtgUnion",f:qe},17:{n:"PtgRange",f:qe},18:{n:"PtgUplus",f:qe},19:{n:"PtgUminus",f:qe},20:{n:"PtgPercent",f:qe},21:{n:"PtgParen",f:qe},22:{n:"PtgMissArg",f:qe},23:{n:"PtgStr",f:w2},26:{n:"PtgSheet",f:j2},27:{n:"PtgEndSheet",f:$2},28:{n:"PtgErr",f:m2},29:{n:"PtgBool",f:v2},30:{n:"PtgInt",f:b2},31:{n:"PtgNum",f:g2},32:{n:"PtgArray",f:Qd},33:{n:"PtgFunc",f:f2},34:{n:"PtgFuncVar",f:h2},35:{n:"PtgName",f:y2},36:{n:"PtgRef",f:o2},37:{n:"PtgArea",f:Yd},38:{n:"PtgMemArea",f:x2},39:{n:"PtgMemErr",f:O2},40:{n:"PtgMemNoMem",f:N2},41:{n:"PtgMemFunc",f:A2},42:{n:"PtgRefErr",f:C2},43:{n:"PtgAreaErr",f:Jd},44:{n:"PtgRefN",f:c2},45:{n:"PtgAreaN",f:qd},46:{n:"PtgMemAreaN",f:X2},47:{n:"PtgMemNoMemN",f:Y2},57:{n:"PtgNameX",f:S2},58:{n:"PtgRef3d",f:l2},59:{n:"PtgArea3d",f:Kd},60:{n:"PtgRefErr3d",f:R2},61:{n:"PtgAreaErr3d",f:Zd},255:{}},J2={64:32,96:32,65:33,97:33,66:34,98:34,67:35,99:35,68:36,100:36,69:37,101:37,70:38,102:38,71:39,103:39,72:40,104:40,73:41,105:41,74:42,106:42,75:43,107:43,76:44,108:44,77:45,109:45,78:46,110:46,79:47,111:47,88:34,120:34,89:57,121:57,90:58,122:58,91:59,123:59,92:60,124:60,93:61,125:61},Z2={1:{n:"PtgElfLel",f:Hc},2:{n:"PtgElfRw",f:W2},3:{n:"PtgElfCol",f:F2},6:{n:"PtgElfRwV",f:z2},7:{n:"PtgElfColV",f:L2},10:{n:"PtgElfRadical",f:M2},11:{n:"PtgElfRadicalS",f:B2},13:{n:"PtgElfColS",f:P2},15:{n:"PtgElfColSV",f:D2},16:{n:"PtgElfRadicalLel",f:U2},25:{n:"PtgList",f:G2},29:{n:"PtgSxName",f:V2},255:{}},q2={0:{n:"PtgAttrNoop",f:K2},1:{n:"PtgAttrSemi",f:n2},2:{n:"PtgAttrIf",f:t2},4:{n:"PtgAttrChoose",f:e2},8:{n:"PtgAttrGoto",f:r2},16:{n:"PtgAttrSum",f:d2},32:{n:"PtgAttrBaxcel",f:ji},33:{n:"PtgAttrBaxcel",f:ji},64:{n:"PtgAttrSpace",f:s2},65:{n:"PtgAttrSpaceSemi",f:i2},128:{n:"PtgAttrIfError",f:a2},255:{}};function Ga(e,t,r,a){if(a.biff<8)return vr(e,t);for(var n=e.l+t,s=[],i=0;i!==r.length;++i)switch(r[i][0]){case"PtgArray":r[i][1]=E2(e,0,a),s.push(r[i][1]);break;case"PtgMemArea":r[i][2]=T2(e,r[i][1],a),s.push(r[i][2]);break;case"PtgExp":a&&a.biff==12&&(r[i][1][1]=e.read_shift(4),s.push(r[i][1]));break;case"PtgList":case"PtgElfRadicalS":case"PtgElfColS":case"PtgElfColSV":throw"Unsupported "+r[i][0]}return t=n-e.l,t!==0&&s.push(vr(e,t)),s}function Va(e,t,r){for(var a=e.l+t,n,s,i=[];a!=e.l;)t=a-e.l,s=e[e.l],n=$i[s]||$i[J2[s]],(s===24||s===25)&&(n=(s===24?Z2:q2)[e[e.l+1]]),!n||!n.f?vr(e,t):i.push([n.n,n.f(e,t,r)]);return i}function Q2(e){for(var t=[],r=0;r<e.length;++r){for(var a=e[r],n=[],s=0;s<a.length;++s){var i=a[s];if(i)switch(i[0]){case 2:n.push('"'+i[1].replace(/"/g,'""')+'"');break;default:n.push(i[1])}else n.push("")}t.push(n.join(","))}return t.join(";")}var ep={PtgAdd:"+",PtgConcat:"&",PtgDiv:"/",PtgEq:"=",PtgGe:">=",PtgGt:">",PtgLe:"<=",PtgLt:"<",PtgMul:"*",PtgNe:"<>",PtgPower:"^",PtgSub:"-"};function rp(e,t){if(!e&&!(t&&t.biff<=5&&t.biff>=2))throw new Error("empty sheet name");return/[^\w\u4E00-\u9FFF\u3040-\u30FF]/.test(e)?"'"+e+"'":e}function Gc(e,t,r){if(!e)return"SH33TJSERR0";if(r.biff>8&&(!e.XTI||!e.XTI[t]))return e.SheetNames[t];if(!e.XTI)return"SH33TJSERR6";var a=e.XTI[t];if(r.biff<8)return t>1e4&&(t-=65536),t<0&&(t=-t),t==0?"":e.XTI[t-1];if(!a)return"SH33TJSERR1";var n="";if(r.biff>8)switch(e[a[0]][0]){case 357:return n=a[1]==-1?"#REF":e.SheetNames[a[1]],a[1]==a[2]?n:n+":"+e.SheetNames[a[2]];case 358:return r.SID!=null?e.SheetNames[r.SID]:"SH33TJSSAME"+e[a[0]][0];case 355:default:return"SH33TJSSRC"+e[a[0]][0]}switch(e[a[0]][0][0]){case 1025:return n=a[1]==-1?"#REF":e.SheetNames[a[1]]||"SH33TJSERR3",a[1]==a[2]?n:n+":"+e.SheetNames[a[2]];case 14849:return e[a[0]].slice(1).map(function(s){return s.Name}).join(";;");default:return e[a[0]][0][3]?(n=a[1]==-1?"#REF":e[a[0]][0][3][a[1]]||"SH33TJSERR4",a[1]==a[2]?n:n+":"+e[a[0]][0][3][a[2]]):"SH33TJSERR2"}}function Xi(e,t,r){var a=Gc(e,t,r);return a=="#REF"?a:rp(a,r)}function dr(e,t,r,a,n){var s=n&&n.biff||8,i={s:{c:0,r:0}},o=[],c,l,f,p=0,h=0,d,m="";if(!e[0]||!e[0][0])return"";for(var u=-1,b="",T=0,S=e[0].length;T<S;++T){var g=e[0][T];switch(g[0]){case"PtgUminus":o.push("-"+o.pop());break;case"PtgUplus":o.push("+"+o.pop());break;case"PtgPercent":o.push(o.pop()+"%");break;case"PtgAdd":case"PtgConcat":case"PtgDiv":case"PtgEq":case"PtgGe":case"PtgGt":case"PtgLe":case"PtgLt":case"PtgMul":case"PtgNe":case"PtgPower":case"PtgSub":if(c=o.pop(),l=o.pop(),u>=0){switch(e[0][u][1][0]){case 0:b=$e(" ",e[0][u][1][1]);break;case 1:b=$e("\r",e[0][u][1][1]);break;default:if(b="",n.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][u][1][0])}l=l+b,u=-1}o.push(l+ep[g[0]]+c);break;case"PtgIsect":c=o.pop(),l=o.pop(),o.push(l+" "+c);break;case"PtgUnion":c=o.pop(),l=o.pop(),o.push(l+","+c);break;case"PtgRange":c=o.pop(),l=o.pop(),o.push(l+":"+c);break;case"PtgAttrChoose":break;case"PtgAttrGoto":break;case"PtgAttrIf":break;case"PtgAttrIfError":break;case"PtgRef":f=ga(g[1][1],i,n),o.push(wa(f,s));break;case"PtgRefN":f=r?ga(g[1][1],r,n):g[1][1],o.push(wa(f,s));break;case"PtgRef3d":p=g[1][1],f=ga(g[1][2],i,n),m=Xi(a,p,n),o.push(m+"!"+wa(f,s));break;case"PtgFunc":case"PtgFuncVar":var x=g[1][0],A=g[1][1];x||(x=0),x&=127;var P=x==0?[]:o.slice(-x);o.length-=x,A==="User"&&(A=P.shift()),o.push(A+"("+P.join(",")+")");break;case"PtgBool":o.push(g[1]?"TRUE":"FALSE");break;case"PtgInt":o.push(g[1]);break;case"PtgNum":o.push(String(g[1]));break;case"PtgStr":o.push('"'+g[1].replace(/"/g,'""')+'"');break;case"PtgErr":o.push(g[1]);break;case"PtgAreaN":d=Ei(g[1][1],r?{s:r}:i,n),o.push(Fn(d,n));break;case"PtgArea":d=Ei(g[1][1],i,n),o.push(Fn(d,n));break;case"PtgArea3d":p=g[1][1],d=g[1][2],m=Xi(a,p,n),o.push(m+"!"+Fn(d,n));break;case"PtgAttrSum":o.push("SUM("+o.pop()+")");break;case"PtgAttrBaxcel":case"PtgAttrSemi":break;case"PtgName":h=g[1][2];var R=(a.names||[])[h-1]||(a[0]||[])[h],U=R?R.Name:"SH33TJSNAME"+String(h);U&&U.slice(0,6)=="_xlfn."&&!n.xlfn&&(U=U.slice(6)),o.push(U);break;case"PtgNameX":var I=g[1][1];h=g[1][2];var G;if(n.biff<=5)I<0&&(I=-I),a[I]&&(G=a[I][h]);else{var W="";if(((a[I]||[])[0]||[])[0]==14849||(((a[I]||[])[0]||[])[0]==1025?a[I][h]&&a[I][h].itab>0&&(W=a.SheetNames[a[I][h].itab-1]+"!"):W=a.SheetNames[h-1]+"!"),a[I]&&a[I][h])W+=a[I][h].Name;else if(a[0]&&a[0][h])W+=a[0][h].Name;else{var D=(Gc(a,I,n)||"").split(";;");D[h-1]?W=D[h-1]:W+="SH33TJSERRX"}o.push(W);break}G||(G={Name:"SH33TJSERRY"}),o.push(G.Name);break;case"PtgParen":var te="(",fe=")";if(u>=0){switch(b="",e[0][u][1][0]){case 2:te=$e(" ",e[0][u][1][1])+te;break;case 3:te=$e("\r",e[0][u][1][1])+te;break;case 4:fe=$e(" ",e[0][u][1][1])+fe;break;case 5:fe=$e("\r",e[0][u][1][1])+fe;break;default:if(n.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][u][1][0])}u=-1}o.push(te+o.pop()+fe);break;case"PtgRefErr":o.push("#REF!");break;case"PtgRefErr3d":o.push("#REF!");break;case"PtgExp":f={c:g[1][1],r:g[1][0]};var se={c:r.c,r:r.r};if(a.sharedf[ve(f)]){var de=a.sharedf[ve(f)];o.push(dr(de,i,se,a,n))}else{var ue=!1;for(c=0;c!=a.arrayf.length;++c)if(l=a.arrayf[c],!(f.c<l[0].s.c||f.c>l[0].e.c)&&!(f.r<l[0].s.r||f.r>l[0].e.r)){o.push(dr(l[1],i,se,a,n)),ue=!0;break}ue||o.push(g[1])}break;case"PtgArray":o.push("{"+Q2(g[1])+"}");break;case"PtgMemArea":break;case"PtgAttrSpace":case"PtgAttrSpaceSemi":u=T;break;case"PtgTbl":break;case"PtgMemErr":break;case"PtgMissArg":o.push("");break;case"PtgAreaErr":o.push("#REF!");break;case"PtgAreaErr3d":o.push("#REF!");break;case"PtgList":o.push("Table"+g[1].idx+"[#"+g[1].rt+"]");break;case"PtgMemAreaN":case"PtgMemNoMemN":case"PtgAttrNoop":case"PtgSheet":case"PtgEndSheet":break;case"PtgMemFunc":break;case"PtgMemNoMem":break;case"PtgElfCol":case"PtgElfColS":case"PtgElfColSV":case"PtgElfColV":case"PtgElfLel":case"PtgElfRadical":case"PtgElfRadicalLel":case"PtgElfRadicalS":case"PtgElfRw":case"PtgElfRwV":throw new Error("Unsupported ELFs");case"PtgSxName":throw new Error("Unrecognized Formula Token: "+String(g));default:throw new Error("Unrecognized Formula Token: "+String(g))}var K=["PtgAttrSpace","PtgAttrSpaceSemi","PtgAttrGoto"];if(n.biff!=3&&u>=0&&K.indexOf(e[0][T][0])==-1){g=e[0][u];var ee=!0;switch(g[1][0]){case 4:ee=!1;case 0:b=$e(" ",g[1][1]);break;case 5:ee=!1;case 1:b=$e("\r",g[1][1]);break;default:if(b="",n.WTF)throw new Error("Unexpected PtgAttrSpaceType "+g[1][0])}o.push((ee?b:"")+o.pop()+(ee?"":b)),u=-1}}if(o.length>1&&n.WTF)throw new Error("bad formula stack");return o[0]}function tp(e,t,r){var a=e.l+t,n=r.biff==2?1:2,s,i=e.read_shift(n);if(i==65535)return[[],vr(e,t-2)];var o=Va(e,i,r);return t!==i+n&&(s=Ga(e,t-i-n,o,r)),e.l=a,[o,s]}function ap(e,t,r){var a=e.l+t,n=r.biff==2?1:2,s,i=e.read_shift(n);if(i==65535)return[[],vr(e,t-2)];var o=Va(e,i,r);return t!==i+n&&(s=Ga(e,t-i-n,o,r)),e.l=a,[o,s]}function np(e,t,r,a){var n=e.l+t,s=Va(e,a,r),i;return n!==e.l&&(i=Ga(e,n-e.l,s,r)),[s,i]}function sp(e,t,r){var a=e.l+t,n,s=e.read_shift(2),i=Va(e,s,r);return s==65535?[[],vr(e,t-2)]:(t!==s+2&&(n=Ga(e,a-s-2,i,r)),[i,n])}function ip(e){var t;if(nt(e,e.l+6)!==65535)return[mr(e),"n"];switch(e[e.l]){case 0:return e.l+=8,["String","s"];case 1:return t=e[e.l+2]===1,e.l+=8,[t,"b"];case 2:return t=e[e.l+2],e.l+=8,[t,"e"];case 3:return e.l+=8,["","s"]}return[]}function op(e){if(e==null){var t=j(8);return t.write_shift(1,3),t.write_shift(1,0),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(2,65535),t}else if(typeof e=="number")return Ft(e);return Ft(0)}function Un(e,t,r){var a=e.l+t,n=et(e);r.biff==2&&++e.l;var s=ip(e),i=e.read_shift(1);r.biff!=2&&(e.read_shift(1),r.biff>=5&&e.read_shift(4));var o=ap(e,a-e.l,r);return{cell:n,val:s[0],formula:o,shared:i>>3&1,tt:s[1]}}function cp(e,t,r,a,n){var s=Dt(t,r,n),i=op(e.v),o=j(6),c=33;o.write_shift(2,c),o.write_shift(4,0);for(var l=j(e.bf.length),f=0;f<e.bf.length;++f)l[f]=e.bf[f];var p=ir([s,i,o,l]);return p}function yn(e,t,r){var a=e.read_shift(4),n=Va(e,a,r),s=e.read_shift(4),i=s>0?Ga(e,s,n,r):null;return[n,i]}var lp=yn,Sn=yn,fp=yn,hp=yn,up={0:"BEEP",1:"OPEN",2:"OPEN.LINKS",3:"CLOSE.ALL",4:"SAVE",5:"SAVE.AS",6:"FILE.DELETE",7:"PAGE.SETUP",8:"PRINT",9:"PRINTER.SETUP",10:"QUIT",11:"NEW.WINDOW",12:"ARRANGE.ALL",13:"WINDOW.SIZE",14:"WINDOW.MOVE",15:"FULL",16:"CLOSE",17:"RUN",22:"SET.PRINT.AREA",23:"SET.PRINT.TITLES",24:"SET.PAGE.BREAK",25:"REMOVE.PAGE.BREAK",26:"FONT",27:"DISPLAY",28:"PROTECT.DOCUMENT",29:"PRECISION",30:"A1.R1C1",31:"CALCULATE.NOW",32:"CALCULATION",34:"DATA.FIND",35:"EXTRACT",36:"DATA.DELETE",37:"SET.DATABASE",38:"SET.CRITERIA",39:"SORT",40:"DATA.SERIES",41:"TABLE",42:"FORMAT.NUMBER",43:"ALIGNMENT",44:"STYLE",45:"BORDER",46:"CELL.PROTECTION",47:"COLUMN.WIDTH",48:"UNDO",49:"CUT",50:"COPY",51:"PASTE",52:"CLEAR",53:"PASTE.SPECIAL",54:"EDIT.DELETE",55:"INSERT",56:"FILL.RIGHT",57:"FILL.DOWN",61:"DEFINE.NAME",62:"CREATE.NAMES",63:"FORMULA.GOTO",64:"FORMULA.FIND",65:"SELECT.LAST.CELL",66:"SHOW.ACTIVE.CELL",67:"GALLERY.AREA",68:"GALLERY.BAR",69:"GALLERY.COLUMN",70:"GALLERY.LINE",71:"GALLERY.PIE",72:"GALLERY.SCATTER",73:"COMBINATION",74:"PREFERRED",75:"ADD.OVERLAY",76:"GRIDLINES",77:"SET.PREFERRED",78:"AXES",79:"LEGEND",80:"ATTACH.TEXT",81:"ADD.ARROW",82:"SELECT.CHART",83:"SELECT.PLOT.AREA",84:"PATTERNS",85:"MAIN.CHART",86:"OVERLAY",87:"SCALE",88:"FORMAT.LEGEND",89:"FORMAT.TEXT",90:"EDIT.REPEAT",91:"PARSE",92:"JUSTIFY",93:"HIDE",94:"UNHIDE",95:"WORKSPACE",96:"FORMULA",97:"FORMULA.FILL",98:"FORMULA.ARRAY",99:"DATA.FIND.NEXT",100:"DATA.FIND.PREV",101:"FORMULA.FIND.NEXT",102:"FORMULA.FIND.PREV",103:"ACTIVATE",104:"ACTIVATE.NEXT",105:"ACTIVATE.PREV",106:"UNLOCKED.NEXT",107:"UNLOCKED.PREV",108:"COPY.PICTURE",109:"SELECT",110:"DELETE.NAME",111:"DELETE.FORMAT",112:"VLINE",113:"HLINE",114:"VPAGE",115:"HPAGE",116:"VSCROLL",117:"HSCROLL",118:"ALERT",119:"NEW",120:"CANCEL.COPY",121:"SHOW.CLIPBOARD",122:"MESSAGE",124:"PASTE.LINK",125:"APP.ACTIVATE",126:"DELETE.ARROW",127:"ROW.HEIGHT",128:"FORMAT.MOVE",129:"FORMAT.SIZE",130:"FORMULA.REPLACE",131:"SEND.KEYS",132:"SELECT.SPECIAL",133:"APPLY.NAMES",134:"REPLACE.FONT",135:"FREEZE.PANES",136:"SHOW.INFO",137:"SPLIT",138:"ON.WINDOW",139:"ON.DATA",140:"DISABLE.INPUT",142:"OUTLINE",143:"LIST.NAMES",144:"FILE.CLOSE",145:"SAVE.WORKBOOK",146:"DATA.FORM",147:"COPY.CHART",148:"ON.TIME",149:"WAIT",150:"FORMAT.FONT",151:"FILL.UP",152:"FILL.LEFT",153:"DELETE.OVERLAY",155:"SHORT.MENUS",159:"SET.UPDATE.STATUS",161:"COLOR.PALETTE",162:"DELETE.STYLE",163:"WINDOW.RESTORE",164:"WINDOW.MAXIMIZE",166:"CHANGE.LINK",167:"CALCULATE.DOCUMENT",168:"ON.KEY",169:"APP.RESTORE",170:"APP.MOVE",171:"APP.SIZE",172:"APP.MINIMIZE",173:"APP.MAXIMIZE",174:"BRING.TO.FRONT",175:"SEND.TO.BACK",185:"MAIN.CHART.TYPE",186:"OVERLAY.CHART.TYPE",187:"SELECT.END",188:"OPEN.MAIL",189:"SEND.MAIL",190:"STANDARD.FONT",191:"CONSOLIDATE",192:"SORT.SPECIAL",193:"GALLERY.3D.AREA",194:"GALLERY.3D.COLUMN",195:"GALLERY.3D.LINE",196:"GALLERY.3D.PIE",197:"VIEW.3D",198:"GOAL.SEEK",199:"WORKGROUP",200:"FILL.GROUP",201:"UPDATE.LINK",202:"PROMOTE",203:"DEMOTE",204:"SHOW.DETAIL",206:"UNGROUP",207:"OBJECT.PROPERTIES",208:"SAVE.NEW.OBJECT",209:"SHARE",210:"SHARE.NAME",211:"DUPLICATE",212:"APPLY.STYLE",213:"ASSIGN.TO.OBJECT",214:"OBJECT.PROTECTION",215:"HIDE.OBJECT",216:"SET.EXTRACT",217:"CREATE.PUBLISHER",218:"SUBSCRIBE.TO",219:"ATTRIBUTES",220:"SHOW.TOOLBAR",222:"PRINT.PREVIEW",223:"EDIT.COLOR",224:"SHOW.LEVELS",225:"FORMAT.MAIN",226:"FORMAT.OVERLAY",227:"ON.RECALC",228:"EDIT.SERIES",229:"DEFINE.STYLE",240:"LINE.PRINT",243:"ENTER.DATA",249:"GALLERY.RADAR",250:"MERGE.STYLES",251:"EDITION.OPTIONS",252:"PASTE.PICTURE",253:"PASTE.PICTURE.LINK",254:"SPELLING",256:"ZOOM",259:"INSERT.OBJECT",260:"WINDOW.MINIMIZE",265:"SOUND.NOTE",266:"SOUND.PLAY",267:"FORMAT.SHAPE",268:"EXTEND.POLYGON",269:"FORMAT.AUTO",272:"GALLERY.3D.BAR",273:"GALLERY.3D.SURFACE",274:"FILL.AUTO",276:"CUSTOMIZE.TOOLBAR",277:"ADD.TOOL",278:"EDIT.OBJECT",279:"ON.DOUBLECLICK",280:"ON.ENTRY",281:"WORKBOOK.ADD",282:"WORKBOOK.MOVE",283:"WORKBOOK.COPY",284:"WORKBOOK.OPTIONS",285:"SAVE.WORKSPACE",288:"CHART.WIZARD",289:"DELETE.TOOL",290:"MOVE.TOOL",291:"WORKBOOK.SELECT",292:"WORKBOOK.ACTIVATE",293:"ASSIGN.TO.TOOL",295:"COPY.TOOL",296:"RESET.TOOL",297:"CONSTRAIN.NUMERIC",298:"PASTE.TOOL",302:"WORKBOOK.NEW",305:"SCENARIO.CELLS",306:"SCENARIO.DELETE",307:"SCENARIO.ADD",308:"SCENARIO.EDIT",309:"SCENARIO.SHOW",310:"SCENARIO.SHOW.NEXT",311:"SCENARIO.SUMMARY",312:"PIVOT.TABLE.WIZARD",313:"PIVOT.FIELD.PROPERTIES",314:"PIVOT.FIELD",315:"PIVOT.ITEM",316:"PIVOT.ADD.FIELDS",318:"OPTIONS.CALCULATION",319:"OPTIONS.EDIT",320:"OPTIONS.VIEW",321:"ADDIN.MANAGER",322:"MENU.EDITOR",323:"ATTACH.TOOLBARS",324:"VBAActivate",325:"OPTIONS.CHART",328:"VBA.INSERT.FILE",330:"VBA.PROCEDURE.DEFINITION",336:"ROUTING.SLIP",338:"ROUTE.DOCUMENT",339:"MAIL.LOGON",342:"INSERT.PICTURE",343:"EDIT.TOOL",344:"GALLERY.DOUGHNUT",350:"CHART.TREND",352:"PIVOT.ITEM.PROPERTIES",354:"WORKBOOK.INSERT",355:"OPTIONS.TRANSITION",356:"OPTIONS.GENERAL",370:"FILTER.ADVANCED",373:"MAIL.ADD.MAILER",374:"MAIL.DELETE.MAILER",375:"MAIL.REPLY",376:"MAIL.REPLY.ALL",377:"MAIL.FORWARD",378:"MAIL.NEXT.LETTER",379:"DATA.LABEL",380:"INSERT.TITLE",381:"FONT.PROPERTIES",382:"MACRO.OPTIONS",383:"WORKBOOK.HIDE",384:"WORKBOOK.UNHIDE",385:"WORKBOOK.DELETE",386:"WORKBOOK.NAME",388:"GALLERY.CUSTOM",390:"ADD.CHART.AUTOFORMAT",391:"DELETE.CHART.AUTOFORMAT",392:"CHART.ADD.DATA",393:"AUTO.OUTLINE",394:"TAB.ORDER",395:"SHOW.DIALOG",396:"SELECT.ALL",397:"UNGROUP.SHEETS",398:"SUBTOTAL.CREATE",399:"SUBTOTAL.REMOVE",400:"RENAME.OBJECT",412:"WORKBOOK.SCROLL",413:"WORKBOOK.NEXT",414:"WORKBOOK.PREV",415:"WORKBOOK.TAB.SPLIT",416:"FULL.SCREEN",417:"WORKBOOK.PROTECT",420:"SCROLLBAR.PROPERTIES",421:"PIVOT.SHOW.PAGES",422:"TEXT.TO.COLUMNS",423:"FORMAT.CHARTTYPE",424:"LINK.FORMAT",425:"TRACER.DISPLAY",430:"TRACER.NAVIGATE",431:"TRACER.CLEAR",432:"TRACER.ERROR",433:"PIVOT.FIELD.GROUP",434:"PIVOT.FIELD.UNGROUP",435:"CHECKBOX.PROPERTIES",436:"LABEL.PROPERTIES",437:"LISTBOX.PROPERTIES",438:"EDITBOX.PROPERTIES",439:"PIVOT.REFRESH",440:"LINK.COMBO",441:"OPEN.TEXT",442:"HIDE.DIALOG",443:"SET.DIALOG.FOCUS",444:"ENABLE.OBJECT",445:"PUSHBUTTON.PROPERTIES",446:"SET.DIALOG.DEFAULT",447:"FILTER",448:"FILTER.SHOW.ALL",449:"CLEAR.OUTLINE",450:"FUNCTION.WIZARD",451:"ADD.LIST.ITEM",452:"SET.LIST.ITEM",453:"REMOVE.LIST.ITEM",454:"SELECT.LIST.ITEM",455:"SET.CONTROL.VALUE",456:"SAVE.COPY.AS",458:"OPTIONS.LISTS.ADD",459:"OPTIONS.LISTS.DELETE",460:"SERIES.AXES",461:"SERIES.X",462:"SERIES.Y",463:"ERRORBAR.X",464:"ERRORBAR.Y",465:"FORMAT.CHART",466:"SERIES.ORDER",467:"MAIL.LOGOFF",468:"CLEAR.ROUTING.SLIP",469:"APP.ACTIVATE.MICROSOFT",470:"MAIL.EDIT.MAILER",471:"ON.SHEET",472:"STANDARD.WIDTH",473:"SCENARIO.MERGE",474:"SUMMARY.INFO",475:"FIND.FILE",476:"ACTIVE.CELL.FONT",477:"ENABLE.TIPWIZARD",478:"VBA.MAKE.ADDIN",480:"INSERTDATATABLE",481:"WORKGROUP.OPTIONS",482:"MAIL.SEND.MAILER",485:"AUTOCORRECT",489:"POST.DOCUMENT",491:"PICKLIST",493:"VIEW.SHOW",494:"VIEW.DEFINE",495:"VIEW.DELETE",509:"SHEET.BACKGROUND",510:"INSERT.MAP.OBJECT",511:"OPTIONS.MENONO",517:"MSOCHECKS",518:"NORMAL",519:"LAYOUT",520:"RM.PRINT.AREA",521:"CLEAR.PRINT.AREA",522:"ADD.PRINT.AREA",523:"MOVE.BRK",545:"HIDECURR.NOTE",546:"HIDEALL.NOTES",547:"DELETE.NOTE",548:"TRAVERSE.NOTES",549:"ACTIVATE.NOTES",620:"PROTECT.REVISIONS",621:"UNPROTECT.REVISIONS",647:"OPTIONS.ME",653:"WEB.PUBLISH",667:"NEWWEBQUERY",673:"PIVOT.TABLE.CHART",753:"OPTIONS.SAVE",755:"OPTIONS.SPELL",808:"HIDEALL.INKANNOTS"},Vc={0:"COUNT",1:"IF",2:"ISNA",3:"ISERROR",4:"SUM",5:"AVERAGE",6:"MIN",7:"MAX",8:"ROW",9:"COLUMN",10:"NA",11:"NPV",12:"STDEV",13:"DOLLAR",14:"FIXED",15:"SIN",16:"COS",17:"TAN",18:"ATAN",19:"PI",20:"SQRT",21:"EXP",22:"LN",23:"LOG10",24:"ABS",25:"INT",26:"SIGN",27:"ROUND",28:"LOOKUP",29:"INDEX",30:"REPT",31:"MID",32:"LEN",33:"VALUE",34:"TRUE",35:"FALSE",36:"AND",37:"OR",38:"NOT",39:"MOD",40:"DCOUNT",41:"DSUM",42:"DAVERAGE",43:"DMIN",44:"DMAX",45:"DSTDEV",46:"VAR",47:"DVAR",48:"TEXT",49:"LINEST",50:"TREND",51:"LOGEST",52:"GROWTH",53:"GOTO",54:"HALT",55:"RETURN",56:"PV",57:"FV",58:"NPER",59:"PMT",60:"RATE",61:"MIRR",62:"IRR",63:"RAND",64:"MATCH",65:"DATE",66:"TIME",67:"DAY",68:"MONTH",69:"YEAR",70:"WEEKDAY",71:"HOUR",72:"MINUTE",73:"SECOND",74:"NOW",75:"AREAS",76:"ROWS",77:"COLUMNS",78:"OFFSET",79:"ABSREF",80:"RELREF",81:"ARGUMENT",82:"SEARCH",83:"TRANSPOSE",84:"ERROR",85:"STEP",86:"TYPE",87:"ECHO",88:"SET.NAME",89:"CALLER",90:"DEREF",91:"WINDOWS",92:"SERIES",93:"DOCUMENTS",94:"ACTIVE.CELL",95:"SELECTION",96:"RESULT",97:"ATAN2",98:"ASIN",99:"ACOS",100:"CHOOSE",101:"HLOOKUP",102:"VLOOKUP",103:"LINKS",104:"INPUT",105:"ISREF",106:"GET.FORMULA",107:"GET.NAME",108:"SET.VALUE",109:"LOG",110:"EXEC",111:"CHAR",112:"LOWER",113:"UPPER",114:"PROPER",115:"LEFT",116:"RIGHT",117:"EXACT",118:"TRIM",119:"REPLACE",120:"SUBSTITUTE",121:"CODE",122:"NAMES",123:"DIRECTORY",124:"FIND",125:"CELL",126:"ISERR",127:"ISTEXT",128:"ISNUMBER",129:"ISBLANK",130:"T",131:"N",132:"FOPEN",133:"FCLOSE",134:"FSIZE",135:"FREADLN",136:"FREAD",137:"FWRITELN",138:"FWRITE",139:"FPOS",140:"DATEVALUE",141:"TIMEVALUE",142:"SLN",143:"SYD",144:"DDB",145:"GET.DEF",146:"REFTEXT",147:"TEXTREF",148:"INDIRECT",149:"REGISTER",150:"CALL",151:"ADD.BAR",152:"ADD.MENU",153:"ADD.COMMAND",154:"ENABLE.COMMAND",155:"CHECK.COMMAND",156:"RENAME.COMMAND",157:"SHOW.BAR",158:"DELETE.MENU",159:"DELETE.COMMAND",160:"GET.CHART.ITEM",161:"DIALOG.BOX",162:"CLEAN",163:"MDETERM",164:"MINVERSE",165:"MMULT",166:"FILES",167:"IPMT",168:"PPMT",169:"COUNTA",170:"CANCEL.KEY",171:"FOR",172:"WHILE",173:"BREAK",174:"NEXT",175:"INITIATE",176:"REQUEST",177:"POKE",178:"EXECUTE",179:"TERMINATE",180:"RESTART",181:"HELP",182:"GET.BAR",183:"PRODUCT",184:"FACT",185:"GET.CELL",186:"GET.WORKSPACE",187:"GET.WINDOW",188:"GET.DOCUMENT",189:"DPRODUCT",190:"ISNONTEXT",191:"GET.NOTE",192:"NOTE",193:"STDEVP",194:"VARP",195:"DSTDEVP",196:"DVARP",197:"TRUNC",198:"ISLOGICAL",199:"DCOUNTA",200:"DELETE.BAR",201:"UNREGISTER",204:"USDOLLAR",205:"FINDB",206:"SEARCHB",207:"REPLACEB",208:"LEFTB",209:"RIGHTB",210:"MIDB",211:"LENB",212:"ROUNDUP",213:"ROUNDDOWN",214:"ASC",215:"DBCS",216:"RANK",219:"ADDRESS",220:"DAYS360",221:"TODAY",222:"VDB",223:"ELSE",224:"ELSE.IF",225:"END.IF",226:"FOR.CELL",227:"MEDIAN",228:"SUMPRODUCT",229:"SINH",230:"COSH",231:"TANH",232:"ASINH",233:"ACOSH",234:"ATANH",235:"DGET",236:"CREATE.OBJECT",237:"VOLATILE",238:"LAST.ERROR",239:"CUSTOM.UNDO",240:"CUSTOM.REPEAT",241:"FORMULA.CONVERT",242:"GET.LINK.INFO",243:"TEXT.BOX",244:"INFO",245:"GROUP",246:"GET.OBJECT",247:"DB",248:"PAUSE",251:"RESUME",252:"FREQUENCY",253:"ADD.TOOLBAR",254:"DELETE.TOOLBAR",255:"User",256:"RESET.TOOLBAR",257:"EVALUATE",258:"GET.TOOLBAR",259:"GET.TOOL",260:"SPELLING.CHECK",261:"ERROR.TYPE",262:"APP.TITLE",263:"WINDOW.TITLE",264:"SAVE.TOOLBAR",265:"ENABLE.TOOL",266:"PRESS.TOOL",267:"REGISTER.ID",268:"GET.WORKBOOK",269:"AVEDEV",270:"BETADIST",271:"GAMMALN",272:"BETAINV",273:"BINOMDIST",274:"CHIDIST",275:"CHIINV",276:"COMBIN",277:"CONFIDENCE",278:"CRITBINOM",279:"EVEN",280:"EXPONDIST",281:"FDIST",282:"FINV",283:"FISHER",284:"FISHERINV",285:"FLOOR",286:"GAMMADIST",287:"GAMMAINV",288:"CEILING",289:"HYPGEOMDIST",290:"LOGNORMDIST",291:"LOGINV",292:"NEGBINOMDIST",293:"NORMDIST",294:"NORMSDIST",295:"NORMINV",296:"NORMSINV",297:"STANDARDIZE",298:"ODD",299:"PERMUT",300:"POISSON",301:"TDIST",302:"WEIBULL",303:"SUMXMY2",304:"SUMX2MY2",305:"SUMX2PY2",306:"CHITEST",307:"CORREL",308:"COVAR",309:"FORECAST",310:"FTEST",311:"INTERCEPT",312:"PEARSON",313:"RSQ",314:"STEYX",315:"SLOPE",316:"TTEST",317:"PROB",318:"DEVSQ",319:"GEOMEAN",320:"HARMEAN",321:"SUMSQ",322:"KURT",323:"SKEW",324:"ZTEST",325:"LARGE",326:"SMALL",327:"QUARTILE",328:"PERCENTILE",329:"PERCENTRANK",330:"MODE",331:"TRIMMEAN",332:"TINV",334:"MOVIE.COMMAND",335:"GET.MOVIE",336:"CONCATENATE",337:"POWER",338:"PIVOT.ADD.DATA",339:"GET.PIVOT.TABLE",340:"GET.PIVOT.FIELD",341:"GET.PIVOT.ITEM",342:"RADIANS",343:"DEGREES",344:"SUBTOTAL",345:"SUMIF",346:"COUNTIF",347:"COUNTBLANK",348:"SCENARIO.GET",349:"OPTIONS.LISTS.GET",350:"ISPMT",351:"DATEDIF",352:"DATESTRING",353:"NUMBERSTRING",354:"ROMAN",355:"OPEN.DIALOG",356:"SAVE.DIALOG",357:"VIEW.GET",358:"GETPIVOTDATA",359:"HYPERLINK",360:"PHONETIC",361:"AVERAGEA",362:"MAXA",363:"MINA",364:"STDEVPA",365:"VARPA",366:"STDEVA",367:"VARA",368:"BAHTTEXT",369:"THAIDAYOFWEEK",370:"THAIDIGIT",371:"THAIMONTHOFYEAR",372:"THAINUMSOUND",373:"THAINUMSTRING",374:"THAISTRINGLENGTH",375:"ISTHAIDIGIT",376:"ROUNDBAHTDOWN",377:"ROUNDBAHTUP",378:"THAIYEAR",379:"RTD",380:"CUBEVALUE",381:"CUBEMEMBER",382:"CUBEMEMBERPROPERTY",383:"CUBERANKEDMEMBER",384:"HEX2BIN",385:"HEX2DEC",386:"HEX2OCT",387:"DEC2BIN",388:"DEC2HEX",389:"DEC2OCT",390:"OCT2BIN",391:"OCT2HEX",392:"OCT2DEC",393:"BIN2DEC",394:"BIN2OCT",395:"BIN2HEX",396:"IMSUB",397:"IMDIV",398:"IMPOWER",399:"IMABS",400:"IMSQRT",401:"IMLN",402:"IMLOG2",403:"IMLOG10",404:"IMSIN",405:"IMCOS",406:"IMEXP",407:"IMARGUMENT",408:"IMCONJUGATE",409:"IMAGINARY",410:"IMREAL",411:"COMPLEX",412:"IMSUM",413:"IMPRODUCT",414:"SERIESSUM",415:"FACTDOUBLE",416:"SQRTPI",417:"QUOTIENT",418:"DELTA",419:"GESTEP",420:"ISEVEN",421:"ISODD",422:"MROUND",423:"ERF",424:"ERFC",425:"BESSELJ",426:"BESSELK",427:"BESSELY",428:"BESSELI",429:"XIRR",430:"XNPV",431:"PRICEMAT",432:"YIELDMAT",433:"INTRATE",434:"RECEIVED",435:"DISC",436:"PRICEDISC",437:"YIELDDISC",438:"TBILLEQ",439:"TBILLPRICE",440:"TBILLYIELD",441:"PRICE",442:"YIELD",443:"DOLLARDE",444:"DOLLARFR",445:"NOMINAL",446:"EFFECT",447:"CUMPRINC",448:"CUMIPMT",449:"EDATE",450:"EOMONTH",451:"YEARFRAC",452:"COUPDAYBS",453:"COUPDAYS",454:"COUPDAYSNC",455:"COUPNCD",456:"COUPNUM",457:"COUPPCD",458:"DURATION",459:"MDURATION",460:"ODDLPRICE",461:"ODDLYIELD",462:"ODDFPRICE",463:"ODDFYIELD",464:"RANDBETWEEN",465:"WEEKNUM",466:"AMORDEGRC",467:"AMORLINC",468:"CONVERT",724:"SHEETJS",469:"ACCRINT",470:"ACCRINTM",471:"WORKDAY",472:"NETWORKDAYS",473:"GCD",474:"MULTINOMIAL",475:"LCM",476:"FVSCHEDULE",477:"CUBEKPIMEMBER",478:"CUBESET",479:"CUBESETCOUNT",480:"IFERROR",481:"COUNTIFS",482:"SUMIFS",483:"AVERAGEIF",484:"AVERAGEIFS"},dp={2:1,3:1,10:0,15:1,16:1,17:1,18:1,19:0,20:1,21:1,22:1,23:1,24:1,25:1,26:1,27:2,30:2,31:3,32:1,33:1,34:0,35:0,38:1,39:2,40:3,41:3,42:3,43:3,44:3,45:3,47:3,48:2,53:1,61:3,63:0,65:3,66:3,67:1,68:1,69:1,70:1,71:1,72:1,73:1,74:0,75:1,76:1,77:1,79:2,80:2,83:1,85:0,86:1,89:0,90:1,94:0,95:0,97:2,98:1,99:1,101:3,102:3,105:1,106:1,108:2,111:1,112:1,113:1,114:1,117:2,118:1,119:4,121:1,126:1,127:1,128:1,129:1,130:1,131:1,133:1,134:1,135:1,136:2,137:2,138:2,140:1,141:1,142:3,143:4,144:4,161:1,162:1,163:1,164:1,165:2,172:1,175:2,176:2,177:3,178:2,179:1,184:1,186:1,189:3,190:1,195:3,196:3,197:1,198:1,199:3,201:1,207:4,210:3,211:1,212:2,213:2,214:1,215:1,225:0,229:1,230:1,231:1,232:1,233:1,234:1,235:3,244:1,247:4,252:2,257:1,261:1,271:1,273:4,274:2,275:2,276:2,277:3,278:3,279:1,280:3,281:3,282:3,283:1,284:1,285:2,286:4,287:3,288:2,289:4,290:3,291:3,292:3,293:4,294:1,295:3,296:1,297:3,298:1,299:2,300:3,301:3,302:4,303:2,304:2,305:2,306:2,307:2,308:2,309:3,310:2,311:2,312:2,313:2,314:2,315:2,316:4,325:2,326:2,327:2,328:2,331:2,332:2,337:2,342:1,343:1,346:2,347:1,350:4,351:3,352:1,353:2,360:1,368:1,369:1,370:1,371:1,372:1,373:1,374:1,375:1,376:1,377:1,378:1,382:3,385:1,392:1,393:1,396:2,397:2,398:2,399:1,400:1,401:1,402:1,403:1,404:1,405:1,406:1,407:1,408:1,409:1,410:1,414:4,415:1,416:1,417:2,420:1,421:1,422:2,424:1,425:2,426:2,427:2,428:2,430:3,438:3,439:3,440:3,443:2,444:2,445:2,446:2,447:6,448:6,449:2,450:2,464:2,468:3,476:2,479:1,480:2,65535:0};function Yi(e){return e.slice(0,3)=="of:"&&(e=e.slice(3)),e.charCodeAt(0)==61&&(e=e.slice(1),e.charCodeAt(0)==61&&(e=e.slice(1))),e=e.replace(/COM\.MICROSOFT\./g,""),e=e.replace(/\[((?:\.[A-Z]+[0-9]+)(?::\.[A-Z]+[0-9]+)?)\]/g,function(t,r){return r.replace(/\./g,"")}),e=e.replace(/\[.(#[A-Z]*[?!])\]/g,"$1"),e.replace(/[;~]/g,",").replace(/\|/g,";")}function pp(e){var t="of:="+e.replace(Ps,"$1[.$2$3$4$5]").replace(/\]:\[/g,":");return t.replace(/;/g,"|").replace(/,/g,";")}function Bn(e){var t=e.split(":"),r=t[0].split(".")[0];return[r,t[0].split(".")[1]+(t.length>1?":"+(t[1].split(".")[1]||t[1].split(".")[0]):"")]}function mp(e){return e.replace(/\./,"!")}var Ta={},Qt={},Ea=typeof Map<"u";function Ms(e,t,r){var a=0,n=e.length;if(r){if(Ea?r.has(t):Object.prototype.hasOwnProperty.call(r,t)){for(var s=Ea?r.get(t):r[t];a<s.length;++a)if(e[s[a]].t===t)return e.Count++,s[a]}}else for(;a<n;++a)if(e[a].t===t)return e.Count++,a;return e[n]={t},e.Count++,e.Unique++,r&&(Ea?(r.has(t)||r.set(t,[]),r.get(t).push(n)):(Object.prototype.hasOwnProperty.call(r,t)||(r[t]=[]),r[t].push(n))),n}function _n(e,t){var r={min:e+1,max:e+1},a=-1;return t.MDW&&(pr=t.MDW),t.width!=null?r.customWidth=1:t.wpx!=null?a=Fa(t.wpx):t.wch!=null&&(a=t.wch),a>-1?(r.width=mn(a),r.customWidth=1):t.width!=null&&(r.width=t.width),t.hidden&&(r.hidden=!0),t.level!=null&&(r.outlineLevel=r.level=t.level),r}function Ot(e,t){if(e){var r=[.7,.7,.75,.75,.3,.3];t=="xlml"&&(r=[1,1,1,1,.5,.5]),e.left==null&&(e.left=r[0]),e.right==null&&(e.right=r[1]),e.top==null&&(e.top=r[2]),e.bottom==null&&(e.bottom=r[3]),e.header==null&&(e.header=r[4]),e.footer==null&&(e.footer=r[5])}}function yt(e,t,r){var a=r.revssf[t.z!=null?t.z:"General"],n=60,s=e.length;if(a==null&&r.ssf){for(;n<392;++n)if(r.ssf[n]==null){it(t.z,n),r.ssf[n]=t.z,r.revssf[t.z]=a=n;break}}for(n=0;n!=s;++n)if(e[n].numFmtId===a)return n;return e[s]={numFmtId:a,fontId:0,fillId:0,borderId:0,xfId:0,applyNumberFormat:1},s}function jc(e,t,r,a,n,s){try{a.cellNF&&(e.z=be[t])}catch(o){if(a.WTF)throw o}if(!(e.t==="z"&&!a.cellStyles)){if(e.t==="d"&&typeof e.v=="string"&&(e.v=We(e.v)),(!a||a.cellText!==!1)&&e.t!=="z")try{if(be[t]==null&&it(uf[t]||"General",t),e.t==="e")e.w=e.w||ht[e.v];else if(t===0)if(e.t==="n")(e.v|0)===e.v?e.w=e.v.toString(10):e.w=xa(e.v);else if(e.t==="d"){var i=nr(e.v);(i|0)===i?e.w=i.toString(10):e.w=xa(i)}else{if(e.v===void 0)return"";e.w=Nt(e.v,Qt)}else e.t==="d"?e.w=zr(t,nr(e.v),Qt):e.w=zr(t,e.v,Qt)}catch(o){if(a.WTF)throw o}if(a.cellStyles&&r!=null)try{e.s=s.Fills[r],e.s.fgColor&&e.s.fgColor.theme&&!e.s.fgColor.rgb&&(e.s.fgColor.rgb=pn(n.themeElements.clrScheme[e.s.fgColor.theme].rgb,e.s.fgColor.tint||0),a.WTF&&(e.s.fgColor.raw_rgb=n.themeElements.clrScheme[e.s.fgColor.theme].rgb)),e.s.bgColor&&e.s.bgColor.theme&&(e.s.bgColor.rgb=pn(n.themeElements.clrScheme[e.s.bgColor.theme].rgb,e.s.bgColor.tint||0),a.WTF&&(e.s.bgColor.raw_rgb=n.themeElements.clrScheme[e.s.bgColor.theme].rgb))}catch(o){if(a.WTF&&s.Fills)throw o}}}function bp(e,t,r){if(e&&e["!ref"]){var a=Re(e["!ref"]);if(a.e.c<a.s.c||a.e.r<a.s.r)throw new Error("Bad range ("+r+"): "+e["!ref"])}}function vp(e,t){var r=Re(t);r.s.r<=r.e.r&&r.s.c<=r.e.c&&r.s.r>=0&&r.s.c>=0&&(e["!ref"]=we(r))}var gp=/<(?:\w:)?mergeCell ref="[A-Z0-9:]+"\s*[\/]?>/g,wp=/<(?:\w+:)?sheetData[^>]*>([\s\S]*)<\/(?:\w+:)?sheetData>/,kp=/<(?:\w:)?hyperlink [^>]*>/mg,Tp=/"(\w*:\w*)"/,Ep=/<(?:\w:)?col\b[^>]*[\/]?>/g,yp=/<(?:\w:)?autoFilter[^>]*([\/]|>([\s\S]*)<\/(?:\w:)?autoFilter)>/g,Sp=/<(?:\w:)?pageMargins[^>]*\/>/g,$c=/<(?:\w:)?sheetPr\b(?:[^>a-z][^>]*)?\/>/,_p=/<(?:\w:)?sheetPr[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetPr)>/,xp=/<(?:\w:)?sheetViews[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetViews)>/;function Ap(e,t,r,a,n,s,i){if(!e)return e;a||(a={"!id":{}});var o=t.dense?[]:{},c={s:{r:2e6,c:2e6},e:{r:0,c:0}},l="",f="",p=e.match(wp);p?(l=e.slice(0,p.index),f=e.slice(p.index+p[0].length)):l=f=e;var h=l.match($c);h?Us(h[0],o,n,r):(h=l.match(_p))&&Rp(h[0],h[1]||"",o,n,r);var d=(l.match(/<(?:\w*:)?dimension/)||{index:-1}).index;if(d>0){var m=l.slice(d,d+50).match(Tp);m&&vp(o,m[1])}var u=l.match(xp);u&&u[1]&&Hp(u[1],n);var b=[];if(t.cellStyles){var T=l.match(Ep);T&&Mp(b,T)}p&&jp(p[1],o,t,c,s,i);var S=f.match(yp);S&&(o["!autofilter"]=Bp(S[0]));var g=[],x=f.match(gp);if(x)for(d=0;d!=x.length;++d)g[d]=Re(x[d].slice(x[d].indexOf('"')+1));var A=f.match(kp);A&&Pp(o,A,a);var P=f.match(Sp);if(P&&(o["!margins"]=Dp(ge(P[0]))),!o["!ref"]&&c.e.c>=c.s.c&&c.e.r>=c.s.r&&(o["!ref"]=we(c)),t.sheetRows>0&&o["!ref"]){var R=Re(o["!ref"]);t.sheetRows<=+R.e.r&&(R.e.r=t.sheetRows-1,R.e.r>c.e.r&&(R.e.r=c.e.r),R.e.r<R.s.r&&(R.s.r=R.e.r),R.e.c>c.e.c&&(R.e.c=c.e.c),R.e.c<R.s.c&&(R.s.c=R.e.c),o["!fullref"]=o["!ref"],o["!ref"]=we(R))}return b.length>0&&(o["!cols"]=b),g.length>0&&(o["!merges"]=g),o}function Cp(e){if(e.length===0)return"";for(var t='<mergeCells count="'+e.length+'">',r=0;r!=e.length;++r)t+='<mergeCell ref="'+we(e[r])+'"/>';return t+"</mergeCells>"}function Us(e,t,r,a){var n=ge(e);r.Sheets[a]||(r.Sheets[a]={}),n.codeName&&(r.Sheets[a].CodeName=Ce(Le(n.codeName)))}function Rp(e,t,r,a,n){Us(e.slice(0,e.indexOf(">")),r,a,n)}function Op(e,t,r,a,n){var s=!1,i={},o=null;if(a.bookType!=="xlsx"&&t.vbaraw){var c=t.SheetNames[r];try{t.Workbook&&(c=t.Workbook.Sheets[r].CodeName||c)}catch{}s=!0,i.codeName=qr(Pe(c))}if(e&&e["!outline"]){var l={summaryBelow:1,summaryRight:1};e["!outline"].above&&(l.summaryBelow=0),e["!outline"].left&&(l.summaryRight=0),o=(o||"")+re("outlinePr",null,l)}!s&&!o||(n[n.length]=re("sheetPr",o,i))}var Np=["objects","scenarios","selectLockedCells","selectUnlockedCells"],Ip=["formatColumns","formatRows","formatCells","insertColumns","insertRows","insertHyperlinks","deleteColumns","deleteRows","sort","autoFilter","pivotTables"];function Fp(e){var t={sheet:1};return Np.forEach(function(r){e[r]!=null&&e[r]&&(t[r]="1")}),Ip.forEach(function(r){e[r]!=null&&!e[r]&&(t[r]="0")}),e.password&&(t.password=Ns(e.password).toString(16).toUpperCase()),re("sheetProtection",null,t)}function Pp(e,t,r){for(var a=Array.isArray(e),n=0;n!=t.length;++n){var s=ge(Le(t[n]),!0);if(!s.ref)return;var i=((r||{})["!id"]||[])[s.id];i?(s.Target=i.Target,s.location&&(s.Target+="#"+Ce(s.location))):(s.Target="#"+Ce(s.location),i={Target:s.Target,TargetMode:"Internal"}),s.Rel=i,s.tooltip&&(s.Tooltip=s.tooltip,delete s.tooltip);for(var o=Re(s.ref),c=o.s.r;c<=o.e.r;++c)for(var l=o.s.c;l<=o.e.c;++l){var f=ve({c:l,r:c});a?(e[c]||(e[c]=[]),e[c][l]||(e[c][l]={t:"z",v:void 0}),e[c][l].l=s):(e[f]||(e[f]={t:"z",v:void 0}),e[f].l=s)}}}function Dp(e){var t={};return["left","right","top","bottom","header","footer"].forEach(function(r){e[r]&&(t[r]=parseFloat(e[r]))}),t}function Lp(e){return Ot(e),re("pageMargins",null,e)}function Mp(e,t){for(var r=!1,a=0;a!=t.length;++a){var n=ge(t[a],!0);n.hidden&&(n.hidden=Be(n.hidden));var s=parseInt(n.min,10)-1,i=parseInt(n.max,10)-1;for(n.outlineLevel&&(n.level=+n.outlineLevel||0),delete n.min,delete n.max,n.width=+n.width,!r&&n.width&&(r=!0,Is(n.width)),gt(n);s<=i;)e[s++]=ze(n)}}function Up(e,t){for(var r=["<cols>"],a,n=0;n!=t.length;++n)(a=t[n])&&(r[r.length]=re("col",null,_n(n,a)));return r[r.length]="</cols>",r.join("")}function Bp(e){var t={ref:(e.match(/ref="([^"]*)"/)||[])[1]};return t}function Wp(e,t,r,a){var n=typeof e.ref=="string"?e.ref:we(e.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var s=r.Workbook.Names,i=xr(n);i.s.r==i.e.r&&(i.e.r=xr(t["!ref"]).e.r,n=we(i));for(var o=0;o<s.length;++o){var c=s[o];if(c.Name=="_xlnm._FilterDatabase"&&c.Sheet==a){c.Ref="'"+r.SheetNames[a]+"'!"+n;break}}return o==s.length&&s.push({Name:"_xlnm._FilterDatabase",Sheet:a,Ref:"'"+r.SheetNames[a]+"'!"+n}),re("autoFilter",null,{ref:n})}var zp=/<(?:\w:)?sheetView(?:[^>a-z][^>]*)?\/?>/;function Hp(e,t){t.Views||(t.Views=[{}]),(e.match(zp)||[]).forEach(function(r,a){var n=ge(r);t.Views[a]||(t.Views[a]={}),+n.zoomScale&&(t.Views[a].zoom=+n.zoomScale),Be(n.rightToLeft)&&(t.Views[a].RTL=!0)})}function Gp(e,t,r,a){var n={workbookViewId:"0"};return(((a||{}).Workbook||{}).Views||[])[0]&&(n.rightToLeft=a.Workbook.Views[0].RTL?"1":"0"),re("sheetViews",re("sheetView",null,n),{})}function Vp(e,t,r,a){if(e.c&&r["!comments"].push([t,e.c]),e.v===void 0&&typeof e.f!="string"||e.t==="z"&&!e.f)return"";var n="",s=e.t,i=e.v;if(e.t!=="z")switch(e.t){case"b":n=e.v?"1":"0";break;case"n":n=""+e.v;break;case"e":n=ht[e.v];break;case"d":a&&a.cellDates?n=We(e.v,-1).toISOString():(e=ze(e),e.t="n",n=""+(e.v=nr(We(e.v)))),typeof e.z>"u"&&(e.z=be[14]);break;default:n=e.v;break}var o=fr("v",Pe(n)),c={r:t},l=yt(a.cellXfs,e,a);switch(l!==0&&(c.s=l),e.t){case"n":break;case"d":c.t="d";break;case"b":c.t="b";break;case"e":c.t="e";break;case"z":break;default:if(e.v==null){delete e.t;break}if(e.v.length>32767)throw new Error("Text length must not exceed 32767 characters");if(a&&a.bookSST){o=fr("v",""+Ms(a.Strings,e.v,a.revStrings)),c.t="s";break}c.t="str";break}if(e.t!=s&&(e.t=s,e.v=i),typeof e.f=="string"&&e.f){var f=e.F&&e.F.slice(0,t.length)==t?{t:"array",ref:e.F}:null;o=re("f",Pe(e.f),f)+(e.v!=null?o:"")}return e.l&&r["!links"].push([t,e.l]),e.D&&(c.cm=1),re("c",o,c)}var jp=function(){var e=/<(?:\w+:)?c[ \/>]/,t=/<\/(?:\w+:)?row>/,r=/r=["']([^"']*)["']/,a=/<(?:\w+:)?is>([\S\s]*?)<\/(?:\w+:)?is>/,n=/ref=["']([^"']*)["']/,s=Aa("v"),i=Aa("f");return function(o,c,l,f,p,h){for(var d=0,m="",u=[],b=[],T=0,S=0,g=0,x="",A,P,R=0,U=0,I,G,W=0,D=0,te=Array.isArray(h.CellXf),fe,se=[],de=[],ue=Array.isArray(c),K=[],ee={},me=!1,Te=!!l.sheetStubs,C=o.split(t),L=0,F=C.length;L!=F;++L){m=C[L].trim();var N=m.length;if(N!==0){var X=0;e:for(d=0;d<N;++d)switch(m[d]){case">":if(m[d-1]!="/"){++d;break e}if(l&&l.cellStyles){if(P=ge(m.slice(X,d),!0),R=P.r!=null?parseInt(P.r,10):R+1,U=-1,l.sheetRows&&l.sheetRows<R)continue;ee={},me=!1,P.ht&&(me=!0,ee.hpt=parseFloat(P.ht),ee.hpx=aa(ee.hpt)),P.hidden=="1"&&(me=!0,ee.hidden=!0),P.outlineLevel!=null&&(me=!0,ee.level=+P.outlineLevel),me&&(K[R-1]=ee)}break;case"<":X=d;break}if(X>=d)break;if(P=ge(m.slice(X,d),!0),R=P.r!=null?parseInt(P.r,10):R+1,U=-1,!(l.sheetRows&&l.sheetRows<R)){f.s.r>R-1&&(f.s.r=R-1),f.e.r<R-1&&(f.e.r=R-1),l&&l.cellStyles&&(ee={},me=!1,P.ht&&(me=!0,ee.hpt=parseFloat(P.ht),ee.hpx=aa(ee.hpt)),P.hidden=="1"&&(me=!0,ee.hidden=!0),P.outlineLevel!=null&&(me=!0,ee.level=+P.outlineLevel),me&&(K[R-1]=ee)),u=m.slice(d).split(e);for(var ne=0;ne!=u.length&&u[ne].trim().charAt(0)=="<";++ne);for(u=u.slice(ne),d=0;d!=u.length;++d)if(m=u[d].trim(),m.length!==0){if(b=m.match(r),T=d,S=0,g=0,m="<c "+(m.slice(0,1)=="<"?">":"")+m,b!=null&&b.length===2){for(T=0,x=b[1],S=0;S!=x.length&&!((g=x.charCodeAt(S)-64)<1||g>26);++S)T=26*T+g;--T,U=T}else++U;for(S=0;S!=m.length&&m.charCodeAt(S)!==62;++S);if(++S,P=ge(m.slice(0,S),!0),P.r||(P.r=ve({r:R-1,c:U})),x=m.slice(S),A={t:""},(b=x.match(s))!=null&&b[1]!==""&&(A.v=Ce(b[1])),l.cellFormula){if((b=x.match(i))!=null&&b[1]!==""){if(A.f=Ce(Le(b[1])).replace(/\r\n/g,`
`),l.xlfn||(A.f=Vi(A.f)),b[0].indexOf('t="array"')>-1)A.F=(x.match(n)||[])[1],A.F.indexOf(":")>-1&&se.push([Re(A.F),A.F]);else if(b[0].indexOf('t="shared"')>-1){G=ge(b[0]);var J=Ce(Le(b[1]));l.xlfn||(J=Vi(J)),de[parseInt(G.si,10)]=[G,J,P.r]}}else(b=x.match(/<f[^>]*\/>/))&&(G=ge(b[0]),de[G.si]&&(A.f=zd(de[G.si][1],de[G.si][2],P.r)));var Q=je(P.r);for(S=0;S<se.length;++S)Q.r>=se[S][0].s.r&&Q.r<=se[S][0].e.r&&Q.c>=se[S][0].s.c&&Q.c<=se[S][0].e.c&&(A.F=se[S][1])}if(P.t==null&&A.v===void 0)if(A.f||A.F)A.v=0,A.t="n";else if(Te)A.t="z";else continue;else A.t=P.t||"n";switch(f.s.c>U&&(f.s.c=U),f.e.c<U&&(f.e.c=U),A.t){case"n":if(A.v==""||A.v==null){if(!Te)continue;A.t="z"}else A.v=parseFloat(A.v);break;case"s":if(typeof A.v>"u"){if(!Te)continue;A.t="z"}else I=Ta[parseInt(A.v,10)],A.v=I.t,A.r=I.r,l.cellHTML&&(A.h=I.h);break;case"str":A.t="s",A.v=A.v!=null?Le(A.v):"",l.cellHTML&&(A.h=bs(A.v));break;case"inlineStr":b=x.match(a),A.t="s",b!=null&&(I=Os(b[1]))?(A.v=I.t,l.cellHTML&&(A.h=I.h)):A.v="";break;case"b":A.v=Be(A.v);break;case"d":l.cellDates?A.v=We(A.v,1):(A.v=nr(We(A.v,1)),A.t="n");break;case"e":(!l||l.cellText!==!1)&&(A.w=A.v),A.v=Yo[A.v];break}if(W=D=0,fe=null,te&&P.s!==void 0&&(fe=h.CellXf[P.s],fe!=null&&(fe.numFmtId!=null&&(W=fe.numFmtId),l.cellStyles&&fe.fillId!=null&&(D=fe.fillId))),jc(A,W,D,l,p,h),l.cellDates&&te&&A.t=="n"&&na(be[W])&&(A.t="d",A.v=Tn(A.v)),P.cm&&l.xlmeta){var q=(l.xlmeta.Cell||[])[+P.cm-1];q&&q.type=="XLDAPR"&&(A.D=!0)}if(ue){var Ee=je(P.r);c[Ee.r]||(c[Ee.r]=[]),c[Ee.r][Ee.c]=A}else c[P.r]=A}}}}K.length>0&&(c["!rows"]=K)}}();function $p(e,t,r,a){var n=[],s=[],i=Re(e["!ref"]),o="",c,l="",f=[],p=0,h=0,d=e["!rows"],m=Array.isArray(e),u={r:l},b,T=-1;for(h=i.s.c;h<=i.e.c;++h)f[h]=Ve(h);for(p=i.s.r;p<=i.e.r;++p){for(s=[],l=Je(p),h=i.s.c;h<=i.e.c;++h){c=f[h]+l;var S=m?(e[p]||[])[h]:e[c];S!==void 0&&(o=Vp(S,c,e,t))!=null&&s.push(o)}(s.length>0||d&&d[p])&&(u={r:l},d&&d[p]&&(b=d[p],b.hidden&&(u.hidden=1),T=-1,b.hpx?T=Pa(b.hpx):b.hpt&&(T=b.hpt),T>-1&&(u.ht=T,u.customHeight=1),b.level&&(u.outlineLevel=b.level)),n[n.length]=re("row",s.join(""),u))}if(d)for(;p<d.length;++p)d&&d[p]&&(u={r:p+1},b=d[p],b.hidden&&(u.hidden=1),T=-1,b.hpx?T=Pa(b.hpx):b.hpt&&(T=b.hpt),T>-1&&(u.ht=T,u.customHeight=1),b.level&&(u.outlineLevel=b.level),n[n.length]=re("row","",u));return n.join("")}function Xc(e,t,r,a){var n=[Ze,re("worksheet",null,{xmlns:Mt[0],"xmlns:r":ar.r})],s=r.SheetNames[e],i=0,o="",c=r.Sheets[s];c==null&&(c={});var l=c["!ref"]||"A1",f=Re(l);if(f.e.c>16383||f.e.r>1048575){if(t.WTF)throw new Error("Range "+l+" exceeds format limit A1:XFD1048576");f.e.c=Math.min(f.e.c,16383),f.e.r=Math.min(f.e.c,1048575),l=we(f)}a||(a={}),c["!comments"]=[];var p=[];Op(c,r,e,t,n),n[n.length]=re("dimension",null,{ref:l}),n[n.length]=Gp(c,t,e,r),t.sheetFormat&&(n[n.length]=re("sheetFormatPr",null,{defaultRowHeight:t.sheetFormat.defaultRowHeight||"16",baseColWidth:t.sheetFormat.baseColWidth||"10",outlineLevelRow:t.sheetFormat.outlineLevelRow||"7"})),c["!cols"]!=null&&c["!cols"].length>0&&(n[n.length]=Up(c,c["!cols"])),n[i=n.length]="<sheetData/>",c["!links"]=[],c["!ref"]!=null&&(o=$p(c,t),o.length>0&&(n[n.length]=o)),n.length>i+1&&(n[n.length]="</sheetData>",n[i]=n[i].replace("/>",">")),c["!protect"]&&(n[n.length]=Fp(c["!protect"])),c["!autofilter"]!=null&&(n[n.length]=Wp(c["!autofilter"],c,r,e)),c["!merges"]!=null&&c["!merges"].length>0&&(n[n.length]=Cp(c["!merges"]));var h=-1,d,m=-1;return c["!links"].length>0&&(n[n.length]="<hyperlinks>",c["!links"].forEach(function(u){u[1].Target&&(d={ref:u[0]},u[1].Target.charAt(0)!="#"&&(m=Fe(a,-1,Pe(u[1].Target).replace(/#.*$/,""),_e.HLINK),d["r:id"]="rId"+m),(h=u[1].Target.indexOf("#"))>-1&&(d.location=Pe(u[1].Target.slice(h+1))),u[1].Tooltip&&(d.tooltip=Pe(u[1].Tooltip)),n[n.length]=re("hyperlink",null,d))}),n[n.length]="</hyperlinks>"),delete c["!links"],c["!margins"]!=null&&(n[n.length]=Lp(c["!margins"])),(!t||t.ignoreEC||t.ignoreEC==null)&&(n[n.length]=fr("ignoredErrors",re("ignoredError",null,{numberStoredAsText:1,sqref:l}))),p.length>0&&(m=Fe(a,-1,"../drawings/drawing"+(e+1)+".xml",_e.DRAW),n[n.length]=re("drawing",null,{"r:id":"rId"+m}),c["!drawing"]=p),c["!comments"].length>0&&(m=Fe(a,-1,"../drawings/vmlDrawing"+(e+1)+".vml",_e.VML),n[n.length]=re("legacyDrawing",null,{"r:id":"rId"+m}),c["!legacy"]=m),n.length>1&&(n[n.length]="</worksheet>",n[1]=n[1].replace("/>",">")),n.join("")}function Xp(e,t){var r={},a=e.l+t;r.r=e.read_shift(4),e.l+=4;var n=e.read_shift(2);e.l+=1;var s=e.read_shift(1);return e.l=a,s&7&&(r.level=s&7),s&16&&(r.hidden=!0),s&32&&(r.hpt=n/20),r}function Yp(e,t,r){var a=j(145),n=(r["!rows"]||[])[e]||{};a.write_shift(4,e),a.write_shift(4,0);var s=320;n.hpx?s=Pa(n.hpx)*20:n.hpt&&(s=n.hpt*20),a.write_shift(2,s),a.write_shift(1,0);var i=0;n.level&&(i|=n.level),n.hidden&&(i|=16),(n.hpx||n.hpt)&&(i|=32),a.write_shift(1,i),a.write_shift(1,0);var o=0,c=a.l;a.l+=4;for(var l={r:e,c:0},f=0;f<16;++f)if(!(t.s.c>f+1<<10||t.e.c<f<<10)){for(var p=-1,h=-1,d=f<<10;d<f+1<<10;++d){l.c=d;var m=Array.isArray(r)?(r[l.r]||[])[l.c]:r[ve(l)];m&&(p<0&&(p=d),h=d)}p<0||(++o,a.write_shift(4,p),a.write_shift(4,h))}var u=a.l;return a.l=c,a.write_shift(4,o),a.l=u,a.length>a.l?a.slice(0,a.l):a}function Kp(e,t,r,a){var n=Yp(a,r,t);(n.length>17||(t["!rows"]||[])[a])&&Y(e,0,n)}var Jp=zt,Zp=oa;function qp(){}function Qp(e,t){var r={},a=e[e.l];return++e.l,r.above=!(a&64),r.left=!(a&128),e.l+=18,r.name=t1(e),r}function em(e,t,r){r==null&&(r=j(84+4*e.length));var a=192;t&&(t.above&&(a&=-65),t.left&&(a&=-129)),r.write_shift(1,a);for(var n=1;n<3;++n)r.write_shift(1,0);return hn({auto:1},r),r.write_shift(-4,-1),r.write_shift(-4,-1),Go(e,r),r.slice(0,r.l)}function rm(e){var t=Hr(e);return[t]}function tm(e,t,r){return r==null&&(r=j(8)),Ut(t,r)}function am(e){var t=Bt(e);return[t]}function nm(e,t,r){return r==null&&(r=j(4)),Wt(t,r)}function sm(e){var t=Hr(e),r=e.read_shift(1);return[t,r,"b"]}function im(e,t,r){return r==null&&(r=j(9)),Ut(t,r),r.write_shift(1,e.v?1:0),r}function om(e){var t=Bt(e),r=e.read_shift(1);return[t,r,"b"]}function cm(e,t,r){return r==null&&(r=j(5)),Wt(t,r),r.write_shift(1,e.v?1:0),r}function lm(e){var t=Hr(e),r=e.read_shift(1);return[t,r,"e"]}function fm(e,t,r){return r==null&&(r=j(9)),Ut(t,r),r.write_shift(1,e.v),r}function hm(e){var t=Bt(e),r=e.read_shift(1);return[t,r,"e"]}function um(e,t,r){return r==null&&(r=j(8)),Wt(t,r),r.write_shift(1,e.v),r.write_shift(2,0),r.write_shift(1,0),r}function dm(e){var t=Hr(e),r=e.read_shift(4);return[t,r,"s"]}function pm(e,t,r){return r==null&&(r=j(12)),Ut(t,r),r.write_shift(4,t.v),r}function mm(e){var t=Bt(e),r=e.read_shift(4);return[t,r,"s"]}function bm(e,t,r){return r==null&&(r=j(8)),Wt(t,r),r.write_shift(4,t.v),r}function vm(e){var t=Hr(e),r=mr(e);return[t,r,"n"]}function gm(e,t,r){return r==null&&(r=j(16)),Ut(t,r),Ft(e.v,r),r}function Yc(e){var t=Bt(e),r=mr(e);return[t,r,"n"]}function wm(e,t,r){return r==null&&(r=j(12)),Wt(t,r),Ft(e.v,r),r}function km(e){var t=Hr(e),r=_s(e);return[t,r,"n"]}function Tm(e,t,r){return r==null&&(r=j(12)),Ut(t,r),Vo(e.v,r),r}function Em(e){var t=Bt(e),r=_s(e);return[t,r,"n"]}function ym(e,t,r){return r==null&&(r=j(8)),Wt(t,r),Vo(e.v,r),r}function Sm(e){var t=Hr(e),r=Es(e);return[t,r,"is"]}function _m(e){var t=Hr(e),r=br(e);return[t,r,"str"]}function xm(e,t,r){return r==null&&(r=j(12+4*e.v.length)),Ut(t,r),or(e.v,r),r.length>r.l?r.slice(0,r.l):r}function Am(e){var t=Bt(e),r=br(e);return[t,r,"str"]}function Cm(e,t,r){return r==null&&(r=j(8+4*e.v.length)),Wt(t,r),or(e.v,r),r.length>r.l?r.slice(0,r.l):r}function Rm(e,t,r){var a=e.l+t,n=Hr(e);n.r=r["!row"];var s=e.read_shift(1),i=[n,s,"b"];if(r.cellFormula){e.l+=2;var o=Sn(e,a-e.l,r);i[3]=dr(o,null,n,r.supbooks,r)}else e.l=a;return i}function Om(e,t,r){var a=e.l+t,n=Hr(e);n.r=r["!row"];var s=e.read_shift(1),i=[n,s,"e"];if(r.cellFormula){e.l+=2;var o=Sn(e,a-e.l,r);i[3]=dr(o,null,n,r.supbooks,r)}else e.l=a;return i}function Nm(e,t,r){var a=e.l+t,n=Hr(e);n.r=r["!row"];var s=mr(e),i=[n,s,"n"];if(r.cellFormula){e.l+=2;var o=Sn(e,a-e.l,r);i[3]=dr(o,null,n,r.supbooks,r)}else e.l=a;return i}function Im(e,t,r){var a=e.l+t,n=Hr(e);n.r=r["!row"];var s=br(e),i=[n,s,"str"];if(r.cellFormula){e.l+=2;var o=Sn(e,a-e.l,r);i[3]=dr(o,null,n,r.supbooks,r)}else e.l=a;return i}var Fm=zt,Pm=oa;function Dm(e,t){return t==null&&(t=j(4)),t.write_shift(4,e),t}function Lm(e,t){var r=e.l+t,a=zt(e),n=ys(e),s=br(e),i=br(e),o=br(e);e.l=r;var c={rfx:a,relId:n,loc:s,display:o};return i&&(c.Tooltip=i),c}function Mm(e,t){var r=j(50+4*(e[1].Target.length+(e[1].Tooltip||"").length));oa({s:je(e[0]),e:je(e[0])},r),Ss("rId"+t,r);var a=e[1].Target.indexOf("#"),n=a==-1?"":e[1].Target.slice(a+1);return or(n||"",r),or(e[1].Tooltip||"",r),or("",r),r.slice(0,r.l)}function Um(){}function Bm(e,t,r){var a=e.l+t,n=jo(e),s=e.read_shift(1),i=[n];if(i[2]=s,r.cellFormula){var o=lp(e,a-e.l,r);i[1]=o}else e.l=a;return i}function Wm(e,t,r){var a=e.l+t,n=zt(e),s=[n];if(r.cellFormula){var i=hp(e,a-e.l,r);s[1]=i,e.l=a}else e.l=a;return s}function zm(e,t,r){r==null&&(r=j(18));var a=_n(e,t);r.write_shift(-4,e),r.write_shift(-4,e),r.write_shift(4,(a.width||10)*256),r.write_shift(4,0);var n=0;return t.hidden&&(n|=1),typeof a.width=="number"&&(n|=2),t.level&&(n|=t.level<<8),r.write_shift(2,n),r}var Kc=["left","right","top","bottom","header","footer"];function Hm(e){var t={};return Kc.forEach(function(r){t[r]=mr(e)}),t}function Gm(e,t){return t==null&&(t=j(6*8)),Ot(e),Kc.forEach(function(r){Ft(e[r],t)}),t}function Vm(e){var t=e.read_shift(2);return e.l+=28,{RTL:t&32}}function jm(e,t,r){r==null&&(r=j(30));var a=924;return(((t||{}).Views||[])[0]||{}).RTL&&(a|=32),r.write_shift(2,a),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(2,0),r.write_shift(2,100),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(4,0),r}function $m(e){var t=j(24);return t.write_shift(4,4),t.write_shift(4,1),oa(e,t),t}function Xm(e,t){return t==null&&(t=j(16*4+2)),t.write_shift(2,e.password?Ns(e.password):0),t.write_shift(4,1),[["objects",!1],["scenarios",!1],["formatCells",!0],["formatColumns",!0],["formatRows",!0],["insertColumns",!0],["insertRows",!0],["insertHyperlinks",!0],["deleteColumns",!0],["deleteRows",!0],["selectLockedCells",!1],["sort",!0],["autoFilter",!0],["pivotTables",!0],["selectUnlockedCells",!1]].forEach(function(r){r[1]?t.write_shift(4,e[r[0]]!=null&&!e[r[0]]?1:0):t.write_shift(4,e[r[0]]!=null&&e[r[0]]?0:1)}),t}function Ym(){}function Km(){}function Jm(e,t,r,a,n,s,i){if(!e)return e;var o=t||{};a||(a={"!id":{}});var c=o.dense?[]:{},l,f={s:{r:2e6,c:2e6},e:{r:0,c:0}},p=!1,h=!1,d,m,u,b,T,S,g,x,A,P=[];o.biff=12,o["!row"]=0;var R=0,U=!1,I=[],G={},W=o.supbooks||n.supbooks||[[]];if(W.sharedf=G,W.arrayf=I,W.SheetNames=n.SheetNames||n.Sheets.map(function(K){return K.name}),!o.supbooks&&(o.supbooks=W,n.Names))for(var D=0;D<n.Names.length;++D)W[0][D+1]=n.Names[D];var te=[],fe=[],se=!1;Da[16]={n:"BrtShortReal",f:Yc};var de;if(ft(e,function(K,ee,me){if(!h)switch(me){case 148:l=K;break;case 0:d=K,o.sheetRows&&o.sheetRows<=d.r&&(h=!0),x=Je(b=d.r),o["!row"]=d.r,(K.hidden||K.hpt||K.level!=null)&&(K.hpt&&(K.hpx=aa(K.hpt)),fe[K.r]=K);break;case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 9:case 10:case 11:case 13:case 14:case 15:case 16:case 17:case 18:case 62:switch(m={t:K[2]},K[2]){case"n":m.v=K[1];break;case"s":g=Ta[K[1]],m.v=g.t,m.r=g.r;break;case"b":m.v=!!K[1];break;case"e":m.v=K[1],o.cellText!==!1&&(m.w=ht[m.v]);break;case"str":m.t="s",m.v=K[1];break;case"is":m.t="s",m.v=K[1].t;break}if((u=i.CellXf[K[0].iStyleRef])&&jc(m,u.numFmtId,null,o,s,i),T=K[0].c==-1?T+1:K[0].c,o.dense?(c[b]||(c[b]=[]),c[b][T]=m):c[Ve(T)+x]=m,o.cellFormula){for(U=!1,R=0;R<I.length;++R){var Te=I[R];d.r>=Te[0].s.r&&d.r<=Te[0].e.r&&T>=Te[0].s.c&&T<=Te[0].e.c&&(m.F=we(Te[0]),U=!0)}!U&&K.length>3&&(m.f=K[3])}if(f.s.r>d.r&&(f.s.r=d.r),f.s.c>T&&(f.s.c=T),f.e.r<d.r&&(f.e.r=d.r),f.e.c<T&&(f.e.c=T),o.cellDates&&u&&m.t=="n"&&na(be[u.numFmtId])){var C=Ct(m.v);C&&(m.t="d",m.v=new Date(C.y,C.m-1,C.d,C.H,C.M,C.S,C.u))}de&&(de.type=="XLDAPR"&&(m.D=!0),de=void 0);break;case 1:case 12:if(!o.sheetStubs||p)break;m={t:"z",v:void 0},T=K[0].c==-1?T+1:K[0].c,o.dense?(c[b]||(c[b]=[]),c[b][T]=m):c[Ve(T)+x]=m,f.s.r>d.r&&(f.s.r=d.r),f.s.c>T&&(f.s.c=T),f.e.r<d.r&&(f.e.r=d.r),f.e.c<T&&(f.e.c=T),de&&(de.type=="XLDAPR"&&(m.D=!0),de=void 0);break;case 176:P.push(K);break;case 49:de=((o.xlmeta||{}).Cell||[])[K-1];break;case 494:var L=a["!id"][K.relId];for(L?(K.Target=L.Target,K.loc&&(K.Target+="#"+K.loc),K.Rel=L):K.relId==""&&(K.Target="#"+K.loc),b=K.rfx.s.r;b<=K.rfx.e.r;++b)for(T=K.rfx.s.c;T<=K.rfx.e.c;++T)o.dense?(c[b]||(c[b]=[]),c[b][T]||(c[b][T]={t:"z",v:void 0}),c[b][T].l=K):(S=ve({c:T,r:b}),c[S]||(c[S]={t:"z",v:void 0}),c[S].l=K);break;case 426:if(!o.cellFormula)break;I.push(K),A=o.dense?c[b][T]:c[Ve(T)+x],A.f=dr(K[1],f,{r:d.r,c:T},W,o),A.F=we(K[0]);break;case 427:if(!o.cellFormula)break;G[ve(K[0].s)]=K[1],A=o.dense?c[b][T]:c[Ve(T)+x],A.f=dr(K[1],f,{r:d.r,c:T},W,o);break;case 60:if(!o.cellStyles)break;for(;K.e>=K.s;)te[K.e--]={width:K.w/256,hidden:!!(K.flags&1),level:K.level},se||(se=!0,Is(K.w/256)),gt(te[K.e+1]);break;case 161:c["!autofilter"]={ref:we(K)};break;case 476:c["!margins"]=K;break;case 147:n.Sheets[r]||(n.Sheets[r]={}),K.name&&(n.Sheets[r].CodeName=K.name),(K.above||K.left)&&(c["!outline"]={above:K.above,left:K.left});break;case 137:n.Views||(n.Views=[{}]),n.Views[0]||(n.Views[0]={}),K.RTL&&(n.Views[0].RTL=!0);break;case 485:break;case 64:case 1053:break;case 151:break;case 152:case 175:case 644:case 625:case 562:case 396:case 1112:case 1146:case 471:case 1050:case 649:case 1105:case 589:case 607:case 564:case 1055:case 168:case 174:case 1180:case 499:case 507:case 550:case 171:case 167:case 1177:case 169:case 1181:case 551:case 552:case 661:case 639:case 478:case 537:case 477:case 536:case 1103:case 680:case 1104:case 1024:case 663:case 535:case 678:case 504:case 1043:case 428:case 170:case 3072:case 50:case 2070:case 1045:break;case 35:p=!0;break;case 36:p=!1;break;case 37:p=!0;break;case 38:p=!1;break;default:if(!ee.T&&(!p||o.WTF))throw new Error("Unexpected record 0x"+me.toString(16))}},o),delete o.supbooks,delete o["!row"],!c["!ref"]&&(f.s.r<2e6||l&&(l.e.r>0||l.e.c>0||l.s.r>0||l.s.c>0))&&(c["!ref"]=we(l||f)),o.sheetRows&&c["!ref"]){var ue=Re(c["!ref"]);o.sheetRows<=+ue.e.r&&(ue.e.r=o.sheetRows-1,ue.e.r>f.e.r&&(ue.e.r=f.e.r),ue.e.r<ue.s.r&&(ue.s.r=ue.e.r),ue.e.c>f.e.c&&(ue.e.c=f.e.c),ue.e.c<ue.s.c&&(ue.s.c=ue.e.c),c["!fullref"]=c["!ref"],c["!ref"]=we(ue))}return P.length>0&&(c["!merges"]=P),te.length>0&&(c["!cols"]=te),fe.length>0&&(c["!rows"]=fe),c}function Zm(e,t,r,a,n,s,i){if(t.v===void 0)return!1;var o="";switch(t.t){case"b":o=t.v?"1":"0";break;case"d":t=ze(t),t.z=t.z||be[14],t.v=nr(We(t.v)),t.t="n";break;case"n":case"e":o=""+t.v;break;default:o=t.v;break}var c={r,c:a};switch(c.s=yt(n.cellXfs,t,n),t.l&&s["!links"].push([ve(c),t.l]),t.c&&s["!comments"].push([ve(c),t.c]),t.t){case"s":case"str":return n.bookSST?(o=Ms(n.Strings,t.v,n.revStrings),c.t="s",c.v=o,i?Y(e,18,bm(t,c)):Y(e,7,pm(t,c))):(c.t="str",i?Y(e,17,Cm(t,c)):Y(e,6,xm(t,c))),!0;case"n":return t.v==(t.v|0)&&t.v>-1e3&&t.v<1e3?i?Y(e,13,ym(t,c)):Y(e,2,Tm(t,c)):i?Y(e,16,wm(t,c)):Y(e,5,gm(t,c)),!0;case"b":return c.t="b",i?Y(e,15,cm(t,c)):Y(e,4,im(t,c)),!0;case"e":return c.t="e",i?Y(e,14,um(t,c)):Y(e,3,fm(t,c)),!0}return i?Y(e,12,nm(t,c)):Y(e,1,tm(t,c)),!0}function qm(e,t,r,a){var n=Re(t["!ref"]||"A1"),s,i="",o=[];Y(e,145);var c=Array.isArray(t),l=n.e.r;t["!rows"]&&(l=Math.max(n.e.r,t["!rows"].length-1));for(var f=n.s.r;f<=l;++f){i=Je(f),Kp(e,t,n,f);var p=!1;if(f<=n.e.r)for(var h=n.s.c;h<=n.e.c;++h){f===n.s.r&&(o[h]=Ve(h)),s=o[h]+i;var d=c?(t[f]||[])[h]:t[s];if(!d){p=!1;continue}p=Zm(e,d,f,h,a,t,p)}}Y(e,146)}function Qm(e,t){!t||!t["!merges"]||(Y(e,177,Dm(t["!merges"].length)),t["!merges"].forEach(function(r){Y(e,176,Pm(r))}),Y(e,178))}function eb(e,t){!t||!t["!cols"]||(Y(e,390),t["!cols"].forEach(function(r,a){r&&Y(e,60,zm(a,r))}),Y(e,391))}function rb(e,t){!t||!t["!ref"]||(Y(e,648),Y(e,649,$m(Re(t["!ref"]))),Y(e,650))}function tb(e,t,r){t["!links"].forEach(function(a){if(a[1].Target){var n=Fe(r,-1,a[1].Target.replace(/#.*$/,""),_e.HLINK);Y(e,494,Mm(a,n))}}),delete t["!links"]}function ab(e,t,r,a){if(t["!comments"].length>0){var n=Fe(a,-1,"../drawings/vmlDrawing"+(r+1)+".vml",_e.VML);Y(e,551,Ss("rId"+n)),t["!legacy"]=n}}function nb(e,t,r,a){if(t["!autofilter"]){var n=t["!autofilter"],s=typeof n.ref=="string"?n.ref:we(n.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var i=r.Workbook.Names,o=xr(s);o.s.r==o.e.r&&(o.e.r=xr(t["!ref"]).e.r,s=we(o));for(var c=0;c<i.length;++c){var l=i[c];if(l.Name=="_xlnm._FilterDatabase"&&l.Sheet==a){l.Ref="'"+r.SheetNames[a]+"'!"+s;break}}c==i.length&&i.push({Name:"_xlnm._FilterDatabase",Sheet:a,Ref:"'"+r.SheetNames[a]+"'!"+s}),Y(e,161,oa(Re(s))),Y(e,162)}}function sb(e,t,r){Y(e,133),Y(e,137,jm(t,r)),Y(e,138),Y(e,134)}function ib(e,t){t["!protect"]&&Y(e,535,Xm(t["!protect"]))}function ob(e,t,r,a){var n=_r(),s=r.SheetNames[e],i=r.Sheets[s]||{},o=s;try{r&&r.Workbook&&(o=r.Workbook.Sheets[e].CodeName||o)}catch{}var c=Re(i["!ref"]||"A1");if(c.e.c>16383||c.e.r>1048575){if(t.WTF)throw new Error("Range "+(i["!ref"]||"A1")+" exceeds format limit A1:XFD1048576");c.e.c=Math.min(c.e.c,16383),c.e.r=Math.min(c.e.c,1048575)}return i["!links"]=[],i["!comments"]=[],Y(n,129),(r.vbaraw||i["!outline"])&&Y(n,147,em(o,i["!outline"])),Y(n,148,Zp(c)),sb(n,i,r.Workbook),eb(n,i),qm(n,i,e,t),ib(n,i),nb(n,i,r,e),Qm(n,i),tb(n,i,a),i["!margins"]&&Y(n,476,Gm(i["!margins"])),(!t||t.ignoreEC||t.ignoreEC==null)&&rb(n,i),ab(n,i,e,a),Y(n,130),n.end()}function cb(e){var t=[],r=e.match(/^<c:numCache>/),a;(e.match(/<c:pt idx="(\d*)">(.*?)<\/c:pt>/mg)||[]).forEach(function(s){var i=s.match(/<c:pt idx="(\d*?)"><c:v>(.*)<\/c:v><\/c:pt>/);i&&(t[+i[1]]=r?+i[2]:i[2])});var n=Ce((e.match(/<c:formatCode>([\s\S]*?)<\/c:formatCode>/)||["","General"])[1]);return(e.match(/<c:f>(.*?)<\/c:f>/mg)||[]).forEach(function(s){a=s.replace(/<.*?>/g,"")}),[t,n,a]}function lb(e,t,r,a,n,s){var i=s||{"!type":"chart"};if(!e)return s;var o=0,c=0,l="A",f={s:{r:2e6,c:2e6},e:{r:0,c:0}};return(e.match(/<c:numCache>[\s\S]*?<\/c:numCache>/gm)||[]).forEach(function(p){var h=cb(p);f.s.r=f.s.c=0,f.e.c=o,l=Ve(o),h[0].forEach(function(d,m){i[l+Je(m)]={t:"n",v:d,z:h[1]},c=m}),f.e.r<c&&(f.e.r=c),++o}),o>0&&(i["!ref"]=we(f)),i}function fb(e,t,r,a,n){if(!e)return e;a||(a={"!id":{}});var s={"!type":"chart","!drawel":null,"!rel":""},i,o=e.match($c);return o&&Us(o[0],s,n,r),(i=e.match(/drawing r:id="(.*?)"/))&&(s["!rel"]=i[1]),a["!id"][s["!rel"]]&&(s["!drawel"]=a["!id"][s["!rel"]]),s}function hb(e,t){e.l+=10;var r=br(e);return{name:r}}function ub(e,t,r,a,n){if(!e)return e;a||(a={"!id":{}});var s={"!type":"chart","!drawel":null,"!rel":""},i=!1;return ft(e,function(o,c,l){switch(l){case 550:s["!rel"]=o;break;case 651:n.Sheets[r]||(n.Sheets[r]={}),o.name&&(n.Sheets[r].CodeName=o.name);break;case 562:case 652:case 669:case 679:case 551:case 552:case 476:case 3072:break;case 35:i=!0;break;case 36:i=!1;break;case 37:break;case 38:break;default:if(!(c.T>0)&&!(c.T<0)&&(!i||t.WTF))throw new Error("Unexpected record 0x"+l.toString(16))}},t),a["!id"][s["!rel"]]&&(s["!drawel"]=a["!id"][s["!rel"]]),s}var Bs=[["allowRefreshQuery",!1,"bool"],["autoCompressPictures",!0,"bool"],["backupFile",!1,"bool"],["checkCompatibility",!1,"bool"],["CodeName",""],["date1904",!1,"bool"],["defaultThemeVersion",0,"int"],["filterPrivacy",!1,"bool"],["hidePivotFieldList",!1,"bool"],["promptedSolutions",!1,"bool"],["publishItems",!1,"bool"],["refreshAllConnections",!1,"bool"],["saveExternalLinkValues",!0,"bool"],["showBorderUnselectedTables",!0,"bool"],["showInkAnnotation",!0,"bool"],["showObjects","all"],["showPivotChartFilter",!1,"bool"],["updateLinks","userSet"]],db=[["activeTab",0,"int"],["autoFilterDateGrouping",!0,"bool"],["firstSheet",0,"int"],["minimized",!1,"bool"],["showHorizontalScroll",!0,"bool"],["showSheetTabs",!0,"bool"],["showVerticalScroll",!0,"bool"],["tabRatio",600,"int"],["visibility","visible"]],pb=[],mb=[["calcCompleted","true"],["calcMode","auto"],["calcOnSave","true"],["concurrentCalc","true"],["fullCalcOnLoad","false"],["fullPrecision","true"],["iterate","false"],["iterateCount","100"],["iterateDelta","0.001"],["refMode","A1"]];function Ki(e,t){for(var r=0;r!=e.length;++r)for(var a=e[r],n=0;n!=t.length;++n){var s=t[n];if(a[s[0]]==null)a[s[0]]=s[1];else switch(s[2]){case"bool":typeof a[s[0]]=="string"&&(a[s[0]]=Be(a[s[0]]));break;case"int":typeof a[s[0]]=="string"&&(a[s[0]]=parseInt(a[s[0]],10));break}}}function Ji(e,t){for(var r=0;r!=t.length;++r){var a=t[r];if(e[a[0]]==null)e[a[0]]=a[1];else switch(a[2]){case"bool":typeof e[a[0]]=="string"&&(e[a[0]]=Be(e[a[0]]));break;case"int":typeof e[a[0]]=="string"&&(e[a[0]]=parseInt(e[a[0]],10));break}}}function Jc(e){Ji(e.WBProps,Bs),Ji(e.CalcPr,mb),Ki(e.WBView,db),Ki(e.Sheets,pb),Qt.date1904=Be(e.WBProps.date1904)}function bb(e){return!e.Workbook||!e.Workbook.WBProps?"false":Be(e.Workbook.WBProps.date1904)?"true":"false"}var vb="][*?/\\".split("");function Zc(e,t){if(e.length>31)throw new Error("Sheet names cannot exceed 31 chars");var r=!0;return vb.forEach(function(a){if(e.indexOf(a)!=-1)throw new Error("Sheet name cannot contain : \\ / ? * [ ]")}),r}function gb(e,t,r){e.forEach(function(a,n){Zc(a);for(var s=0;s<n;++s)if(a==e[s])throw new Error("Duplicate Sheet Name: "+a);if(r){var i=t&&t[n]&&t[n].CodeName||a;if(i.charCodeAt(0)==95&&i.length>22)throw new Error("Bad Code Name: Worksheet"+i)}})}function wb(e){if(!e||!e.SheetNames||!e.Sheets)throw new Error("Invalid Workbook");if(!e.SheetNames.length)throw new Error("Workbook is empty");var t=e.Workbook&&e.Workbook.Sheets||[];gb(e.SheetNames,t,!!e.vbaraw);for(var r=0;r<e.SheetNames.length;++r)bp(e.Sheets[e.SheetNames[r]],e.SheetNames[r],r)}var kb=/<\w+:workbook/;function Tb(e,t){if(!e)throw new Error("Could not find file");var r={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},Names:[],xmlns:""},a=!1,n="xmlns",s={},i=0;if(e.replace(gr,function(o,c){var l=ge(o);switch(Qr(l[0])){case"<?xml":break;case"<workbook":o.match(kb)&&(n="xmlns"+o.match(/<(\w+):/)[1]),r.xmlns=l[n];break;case"</workbook>":break;case"<fileVersion":delete l[0],r.AppVersion=l;break;case"<fileVersion/>":case"</fileVersion>":break;case"<fileSharing":break;case"<fileSharing/>":break;case"<workbookPr":case"<workbookPr/>":Bs.forEach(function(f){if(l[f[0]]!=null)switch(f[2]){case"bool":r.WBProps[f[0]]=Be(l[f[0]]);break;case"int":r.WBProps[f[0]]=parseInt(l[f[0]],10);break;default:r.WBProps[f[0]]=l[f[0]]}}),l.codeName&&(r.WBProps.CodeName=Le(l.codeName));break;case"</workbookPr>":break;case"<workbookProtection":break;case"<workbookProtection/>":break;case"<bookViews":case"<bookViews>":case"</bookViews>":break;case"<workbookView":case"<workbookView/>":delete l[0],r.WBView.push(l);break;case"</workbookView>":break;case"<sheets":case"<sheets>":case"</sheets>":break;case"<sheet":switch(l.state){case"hidden":l.Hidden=1;break;case"veryHidden":l.Hidden=2;break;default:l.Hidden=0}delete l.state,l.name=Ce(Le(l.name)),delete l[0],r.Sheets.push(l);break;case"</sheet>":break;case"<functionGroups":case"<functionGroups/>":break;case"<functionGroup":break;case"<externalReferences":case"</externalReferences>":case"<externalReferences>":break;case"<externalReference":break;case"<definedNames/>":break;case"<definedNames>":case"<definedNames":a=!0;break;case"</definedNames>":a=!1;break;case"<definedName":s={},s.Name=Le(l.name),l.comment&&(s.Comment=l.comment),l.localSheetId&&(s.Sheet=+l.localSheetId),Be(l.hidden||"0")&&(s.Hidden=!0),i=c+o.length;break;case"</definedName>":s.Ref=Ce(Le(e.slice(i,c))),r.Names.push(s);break;case"<definedName/>":break;case"<calcPr":delete l[0],r.CalcPr=l;break;case"<calcPr/>":delete l[0],r.CalcPr=l;break;case"</calcPr>":break;case"<oleSize":break;case"<customWorkbookViews>":case"</customWorkbookViews>":case"<customWorkbookViews":break;case"<customWorkbookView":case"</customWorkbookView>":break;case"<pivotCaches>":case"</pivotCaches>":case"<pivotCaches":break;case"<pivotCache":break;case"<smartTagPr":case"<smartTagPr/>":break;case"<smartTagTypes":case"<smartTagTypes>":case"</smartTagTypes>":break;case"<smartTagType":break;case"<webPublishing":case"<webPublishing/>":break;case"<fileRecoveryPr":case"<fileRecoveryPr/>":break;case"<webPublishObjects>":case"<webPublishObjects":case"</webPublishObjects>":break;case"<webPublishObject":break;case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<ext":a=!0;break;case"</ext>":a=!1;break;case"<ArchID":break;case"<AlternateContent":case"<AlternateContent>":a=!0;break;case"</AlternateContent>":a=!1;break;case"<revisionPtr":break;default:if(!a&&t.WTF)throw new Error("unrecognized "+l[0]+" in workbook")}return o}),Mt.indexOf(r.xmlns)===-1)throw new Error("Unknown Namespace: "+r.xmlns);return Jc(r),r}function qc(e){var t=[Ze];t[t.length]=re("workbook",null,{xmlns:Mt[0],"xmlns:r":ar.r});var r=e.Workbook&&(e.Workbook.Names||[]).length>0,a={codeName:"ThisWorkbook"};e.Workbook&&e.Workbook.WBProps&&(Bs.forEach(function(o){e.Workbook.WBProps[o[0]]!=null&&e.Workbook.WBProps[o[0]]!=o[1]&&(a[o[0]]=e.Workbook.WBProps[o[0]])}),e.Workbook.WBProps.CodeName&&(a.codeName=e.Workbook.WBProps.CodeName,delete a.CodeName)),t[t.length]=re("workbookPr",null,a);var n=e.Workbook&&e.Workbook.Sheets||[],s=0;if(n&&n[0]&&n[0].Hidden){for(t[t.length]="<bookViews>",s=0;s!=e.SheetNames.length&&!(!n[s]||!n[s].Hidden);++s);s==e.SheetNames.length&&(s=0),t[t.length]='<workbookView firstSheet="'+s+'" activeTab="'+s+'"/>',t[t.length]="</bookViews>"}for(t[t.length]="<sheets>",s=0;s!=e.SheetNames.length;++s){var i={name:Pe(e.SheetNames[s].slice(0,31))};if(i.sheetId=""+(s+1),i["r:id"]="rId"+(s+1),n[s])switch(n[s].Hidden){case 1:i.state="hidden";break;case 2:i.state="veryHidden";break}t[t.length]=re("sheet",null,i)}return t[t.length]="</sheets>",r&&(t[t.length]="<definedNames>",e.Workbook&&e.Workbook.Names&&e.Workbook.Names.forEach(function(o){var c={name:o.Name};o.Comment&&(c.comment=o.Comment),o.Sheet!=null&&(c.localSheetId=""+o.Sheet),o.Hidden&&(c.hidden="1"),o.Ref&&(t[t.length]=re("definedName",Pe(o.Ref),c))}),t[t.length]="</definedNames>"),t.length>2&&(t[t.length]="</workbook>",t[1]=t[1].replace("/>",">")),t.join("")}function Eb(e,t){var r={};return r.Hidden=e.read_shift(4),r.iTabID=e.read_shift(4),r.strRelID=Xn(e),r.name=br(e),r}function yb(e,t){return t||(t=j(127)),t.write_shift(4,e.Hidden),t.write_shift(4,e.iTabID),Ss(e.strRelID,t),or(e.name.slice(0,31),t),t.length>t.l?t.slice(0,t.l):t}function Sb(e,t){var r={},a=e.read_shift(4);r.defaultThemeVersion=e.read_shift(4);var n=t>8?br(e):"";return n.length>0&&(r.CodeName=n),r.autoCompressPictures=!!(a&65536),r.backupFile=!!(a&64),r.checkCompatibility=!!(a&4096),r.date1904=!!(a&1),r.filterPrivacy=!!(a&8),r.hidePivotFieldList=!!(a&1024),r.promptedSolutions=!!(a&16),r.publishItems=!!(a&2048),r.refreshAllConnections=!!(a&262144),r.saveExternalLinkValues=!!(a&128),r.showBorderUnselectedTables=!!(a&4),r.showInkAnnotation=!!(a&32),r.showObjects=["all","placeholders","none"][a>>13&3],r.showPivotChartFilter=!!(a&32768),r.updateLinks=["userSet","never","always"][a>>8&3],r}function _b(e,t){t||(t=j(72));var r=0;return e&&e.filterPrivacy&&(r|=8),t.write_shift(4,r),t.write_shift(4,0),Go(e&&e.CodeName||"ThisWorkbook",t),t.slice(0,t.l)}function xb(e,t){var r={};return e.read_shift(4),r.ArchID=e.read_shift(4),e.l+=t-8,r}function Ab(e,t,r){var a=e.l+t;e.l+=4,e.l+=1;var n=e.read_shift(4),s=a1(e),i=fp(e,0,r),o=ys(e);e.l=a;var c={Name:s,Ptg:i};return n<268435455&&(c.Sheet=n),o&&(c.Comment=o),c}function Cb(e,t){var r={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},xmlns:""},a=[],n=!1;t||(t={}),t.biff=12;var s=[],i=[[]];return i.SheetNames=[],i.XTI=[],Da[16]={n:"BrtFRTArchID$",f:xb},ft(e,function(o,c,l){switch(l){case 156:i.SheetNames.push(o.name),r.Sheets.push(o);break;case 153:r.WBProps=o;break;case 39:o.Sheet!=null&&(t.SID=o.Sheet),o.Ref=dr(o.Ptg,null,null,i,t),delete t.SID,delete o.Ptg,s.push(o);break;case 1036:break;case 357:case 358:case 355:case 667:i[0].length?i.push([l,o]):i[0]=[l,o],i[i.length-1].XTI=[];break;case 362:i.length===0&&(i[0]=[],i[0].XTI=[]),i[i.length-1].XTI=i[i.length-1].XTI.concat(o),i.XTI=i.XTI.concat(o);break;case 361:break;case 2071:case 158:case 143:case 664:case 353:break;case 3072:case 3073:case 534:case 677:case 157:case 610:case 2050:case 155:case 548:case 676:case 128:case 665:case 2128:case 2125:case 549:case 2053:case 596:case 2076:case 2075:case 2082:case 397:case 154:case 1117:case 553:case 2091:break;case 35:a.push(l),n=!0;break;case 36:a.pop(),n=!1;break;case 37:a.push(l),n=!0;break;case 38:a.pop(),n=!1;break;case 16:break;default:if(!c.T&&(!n||t.WTF&&a[a.length-1]!=37&&a[a.length-1]!=35))throw new Error("Unexpected record 0x"+l.toString(16))}},t),Jc(r),r.Names=s,r.supbooks=i,r}function Rb(e,t){Y(e,143);for(var r=0;r!=t.SheetNames.length;++r){var a=t.Workbook&&t.Workbook.Sheets&&t.Workbook.Sheets[r]&&t.Workbook.Sheets[r].Hidden||0,n={Hidden:a,iTabID:r+1,strRelID:"rId"+(r+1),name:t.SheetNames[r]};Y(e,156,yb(n))}Y(e,144)}function Ob(e,t){t||(t=j(127));for(var r=0;r!=4;++r)t.write_shift(4,0);return or("SheetJS",t),or(an.version,t),or(an.version,t),or("7262",t),t.length>t.l?t.slice(0,t.l):t}function Nb(e,t){t||(t=j(29)),t.write_shift(-4,0),t.write_shift(-4,460),t.write_shift(4,28800),t.write_shift(4,17600),t.write_shift(4,500),t.write_shift(4,e),t.write_shift(4,e);var r=120;return t.write_shift(1,r),t.length>t.l?t.slice(0,t.l):t}function Ib(e,t){if(!(!t.Workbook||!t.Workbook.Sheets)){for(var r=t.Workbook.Sheets,a=0,n=-1,s=-1;a<r.length;++a)!r[a]||!r[a].Hidden&&n==-1?n=a:r[a].Hidden==1&&s==-1&&(s=a);s>n||(Y(e,135),Y(e,158,Nb(n)),Y(e,136))}}function Fb(e,t){var r=_r();return Y(r,131),Y(r,128,Ob()),Y(r,153,_b(e.Workbook&&e.Workbook.WBProps||null)),Ib(r,e),Rb(r,e),Y(r,132),r.end()}function Pb(e,t,r){return t.slice(-4)===".bin"?Cb(e,r):Tb(e,r)}function Db(e,t,r,a,n,s,i,o){return t.slice(-4)===".bin"?Jm(e,a,r,n,s,i,o):Ap(e,a,r,n,s,i,o)}function Lb(e,t,r,a,n,s,i,o){return t.slice(-4)===".bin"?ub(e,a,r,n,s):fb(e,a,r,n,s)}function Mb(e,t,r,a,n,s,i,o){return t.slice(-4)===".bin"?Bd():Wd()}function Ub(e,t,r,a,n,s,i,o){return t.slice(-4)===".bin"?Md():Ud()}function Bb(e,t,r,a){return t.slice(-4)===".bin"?P0(e,r,a):E0(e,r,a)}function Wb(e,t,r){return Ic(e,r)}function zb(e,t,r){return t.slice(-4)===".bin"?zu(e,r):Uu(e,r)}function Hb(e,t,r){return t.slice(-4)===".bin"?Id(e,r):yd(e,r)}function Gb(e,t,r){return t.slice(-4)===".bin"?kd(e):gd(e)}function Vb(e,t,r,a){return r.slice(-4)===".bin"?Td(e,t,r,a):void 0}function jb(e,t,r){return t.slice(-4)===".bin"?md(e,t,r):vd(e,t,r)}function $b(e,t,r){return(t.slice(-4)===".bin"?Fb:qc)(e)}function Xb(e,t,r,a,n){return(t.slice(-4)===".bin"?ob:Xc)(e,r,a,n)}function Yb(e,t,r){return(t.slice(-4)===".bin"?V0:Oc)(e,r)}function Kb(e,t,r){return(t.slice(-4)===".bin"?Vu:Ec)(e,r)}function Jb(e,t,r){return(t.slice(-4)===".bin"?Fd:Dc)(e)}function Zb(e){return(e.slice(-4)===".bin"?bd:Fc)()}var Qc=/([\w:]+)=((?:")([^"]*)(?:")|(?:')([^']*)(?:'))/g,el=/([\w:]+)=((?:")(?:[^"]*)(?:")|(?:')(?:[^']*)(?:'))/;function Vr(e,t){var r=e.split(/\s+/),a=[];if(a[0]=r[0],r.length===1)return a;var n=e.match(Qc),s,i,o,c;if(n)for(c=0;c!=n.length;++c)s=n[c].match(el),(i=s[1].indexOf(":"))===-1?a[s[1]]=s[2].slice(1,s[2].length-1):(s[1].slice(0,6)==="xmlns:"?o="xmlns"+s[1].slice(6):o=s[1].slice(i+1),a[o]=s[2].slice(1,s[2].length-1));return a}function qb(e){var t=e.split(/\s+/),r={};if(t.length===1)return r;var a=e.match(Qc),n,s,i,o;if(a)for(o=0;o!=a.length;++o)n=a[o].match(el),(s=n[1].indexOf(":"))===-1?r[n[1]]=n[2].slice(1,n[2].length-1):(n[1].slice(0,6)==="xmlns:"?i="xmlns"+n[1].slice(6):i=n[1].slice(s+1),r[i]=n[2].slice(1,n[2].length-1));return r}var ya;function Qb(e,t){var r=ya[e]||Ce(e);return r==="General"?Nt(t):zr(r,t)}function ev(e,t,r,a){var n=a;switch((r[0].match(/dt:dt="([\w.]+)"/)||["",""])[1]){case"boolean":n=Be(a);break;case"i2":case"int":n=parseInt(a,10);break;case"r4":case"float":n=parseFloat(a);break;case"date":case"dateTime.tz":n=We(a);break;case"i8":case"string":case"fixed":case"uuid":case"bin.base64":break;default:throw new Error("bad custprop:"+r[0])}e[Ce(t)]=n}function rv(e,t,r){if(e.t!=="z"){if(!r||r.cellText!==!1)try{e.t==="e"?e.w=e.w||ht[e.v]:t==="General"?e.t==="n"?(e.v|0)===e.v?e.w=e.v.toString(10):e.w=xa(e.v):e.w=Nt(e.v):e.w=Qb(t||"General",e.v)}catch(s){if(r.WTF)throw s}try{var a=ya[t]||t||"General";if(r.cellNF&&(e.z=a),r.cellDates&&e.t=="n"&&na(a)){var n=Ct(e.v);n&&(e.t="d",e.v=new Date(n.y,n.m-1,n.d,n.H,n.M,n.S,n.u))}}catch(s){if(r.WTF)throw s}}}function tv(e,t,r){if(r.cellStyles&&t.Interior){var a=t.Interior;a.Pattern&&(a.patternType=p0[a.Pattern]||a.Pattern)}e[t.ID]=t}function av(e,t,r,a,n,s,i,o,c,l){var f="General",p=a.StyleID,h={};l=l||{};var d=[],m=0;for(p===void 0&&o&&(p=o.StyleID),p===void 0&&i&&(p=i.StyleID);s[p]!==void 0&&(s[p].nf&&(f=s[p].nf),s[p].Interior&&d.push(s[p].Interior),!!s[p].Parent);)p=s[p].Parent;switch(r.Type){case"Boolean":a.t="b",a.v=Be(e);break;case"String":a.t="s",a.r=di(Ce(e)),a.v=e.indexOf("<")>-1?Ce(t||e).replace(/<.*?>/g,""):a.r;break;case"DateTime":e.slice(-1)!="Z"&&(e+="Z"),a.v=(We(e)-new Date(Date.UTC(1899,11,30)))/(24*60*60*1e3),a.v!==a.v?a.v=Ce(e):a.v<60&&(a.v=a.v-1),(!f||f=="General")&&(f="yyyy-mm-dd");case"Number":a.v===void 0&&(a.v=+e),a.t||(a.t="n");break;case"Error":a.t="e",a.v=Yo[e],l.cellText!==!1&&(a.w=e);break;default:e==""&&t==""?a.t="z":(a.t="s",a.v=di(t||e));break}if(rv(a,f,l),l.cellFormula!==!1)if(a.Formula){var u=Ce(a.Formula);u.charCodeAt(0)==61&&(u=u.slice(1)),a.f=qt(u,n),delete a.Formula,a.ArrayRange=="RC"?a.F=qt("RC:RC",n):a.ArrayRange&&(a.F=qt(a.ArrayRange,n),c.push([Re(a.F),a.F]))}else for(m=0;m<c.length;++m)n.r>=c[m][0].s.r&&n.r<=c[m][0].e.r&&n.c>=c[m][0].s.c&&n.c<=c[m][0].e.c&&(a.F=c[m][1]);l.cellStyles&&(d.forEach(function(b){!h.patternType&&b.patternType&&(h.patternType=b.patternType)}),a.s=h),a.StyleID!==void 0&&(a.ixfe=a.StyleID)}function nv(e){e.t=e.v||"",e.t=e.t.replace(/\r\n/g,`
`).replace(/\r/g,`
`),e.v=e.w=e.ixfe=void 0}function Wn(e,t){var r=t||{};sa();var a=pa(vs(e));(r.type=="binary"||r.type=="array"||r.type=="base64")&&(a=Le(a));var n=a.slice(0,1024).toLowerCase(),s=!1;if(n=n.replace(/".*?"/g,""),(n.indexOf(">")&1023)>Math.min(n.indexOf(",")&1023,n.indexOf(";")&1023)){var i=ze(r);return i.type="string",ra.to_workbook(a,i)}if(n.indexOf("<?xml")==-1&&["html","table","head","meta","script","style","div"].forEach(function(rr){n.indexOf("<"+rr)>=0&&(s=!0)}),s)return Gv(a,r);ya={"General Number":"General","General Date":be[22],"Long Date":"dddd, mmmm dd, yyyy","Medium Date":be[15],"Short Date":be[14],"Long Time":be[19],"Medium Time":be[18],"Short Time":be[20],Currency:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',Fixed:be[2],Standard:be[4],Percent:be[10],Scientific:be[11],"Yes/No":'"Yes";"Yes";"No";@',"True/False":'"True";"True";"False";@',"On/Off":'"Yes";"Yes";"No";@'};var o,c=[],l,f={},p=[],h=r.dense?[]:{},d="",m={},u={},b=Vr('<Data ss:Type="String">'),T=0,S=0,g=0,x={s:{r:2e6,c:2e6},e:{r:0,c:0}},A={},P={},R="",U=0,I=[],G={},W={},D=0,te=[],fe=[],se={},de=[],ue,K=!1,ee=[],me=[],Te={},C=0,L=0,F={Sheets:[],WBProps:{date1904:!1}},N={};Ra.lastIndex=0,a=a.replace(/<!--([\s\S]*?)-->/mg,"");for(var X="";o=Ra.exec(a);)switch(o[3]=(X=o[3]).toLowerCase()){case"data":if(X=="data"){if(o[1]==="/"){if((l=c.pop())[0]!==o[3])throw new Error("Bad state: "+l.join("|"))}else o[0].charAt(o[0].length-2)!=="/"&&c.push([o[3],!0]);break}if(c[c.length-1][1])break;o[1]==="/"?av(a.slice(T,o.index),R,b,c[c.length-1][0]=="comment"?se:m,{c:S,r:g},A,de[S],u,ee,r):(R="",b=Vr(o[0]),T=o.index+o[0].length);break;case"cell":if(o[1]==="/")if(fe.length>0&&(m.c=fe),(!r.sheetRows||r.sheetRows>g)&&m.v!==void 0&&(r.dense?(h[g]||(h[g]=[]),h[g][S]=m):h[Ve(S)+Je(g)]=m),m.HRef&&(m.l={Target:Ce(m.HRef)},m.HRefScreenTip&&(m.l.Tooltip=m.HRefScreenTip),delete m.HRef,delete m.HRefScreenTip),(m.MergeAcross||m.MergeDown)&&(C=S+(parseInt(m.MergeAcross,10)|0),L=g+(parseInt(m.MergeDown,10)|0),I.push({s:{c:S,r:g},e:{c:C,r:L}})),!r.sheetStubs)m.MergeAcross?S=C+1:++S;else if(m.MergeAcross||m.MergeDown){for(var ne=S;ne<=C;++ne)for(var J=g;J<=L;++J)(ne>S||J>g)&&(r.dense?(h[J]||(h[J]=[]),h[J][ne]={t:"z"}):h[Ve(ne)+Je(J)]={t:"z"});S=C+1}else++S;else m=qb(o[0]),m.Index&&(S=+m.Index-1),S<x.s.c&&(x.s.c=S),S>x.e.c&&(x.e.c=S),o[0].slice(-2)==="/>"&&++S,fe=[];break;case"row":o[1]==="/"||o[0].slice(-2)==="/>"?(g<x.s.r&&(x.s.r=g),g>x.e.r&&(x.e.r=g),o[0].slice(-2)==="/>"&&(u=Vr(o[0]),u.Index&&(g=+u.Index-1)),S=0,++g):(u=Vr(o[0]),u.Index&&(g=+u.Index-1),Te={},(u.AutoFitHeight=="0"||u.Height)&&(Te.hpx=parseInt(u.Height,10),Te.hpt=Pa(Te.hpx),me[g]=Te),u.Hidden=="1"&&(Te.hidden=!0,me[g]=Te));break;case"worksheet":if(o[1]==="/"){if((l=c.pop())[0]!==o[3])throw new Error("Bad state: "+l.join("|"));p.push(d),x.s.r<=x.e.r&&x.s.c<=x.e.c&&(h["!ref"]=we(x),r.sheetRows&&r.sheetRows<=x.e.r&&(h["!fullref"]=h["!ref"],x.e.r=r.sheetRows-1,h["!ref"]=we(x))),I.length&&(h["!merges"]=I),de.length>0&&(h["!cols"]=de),me.length>0&&(h["!rows"]=me),f[d]=h}else x={s:{r:2e6,c:2e6},e:{r:0,c:0}},g=S=0,c.push([o[3],!1]),l=Vr(o[0]),d=Ce(l.Name),h=r.dense?[]:{},I=[],ee=[],me=[],N={name:d,Hidden:0},F.Sheets.push(N);break;case"table":if(o[1]==="/"){if((l=c.pop())[0]!==o[3])throw new Error("Bad state: "+l.join("|"))}else{if(o[0].slice(-2)=="/>")break;c.push([o[3],!1]),de=[],K=!1}break;case"style":o[1]==="/"?tv(A,P,r):P=Vr(o[0]);break;case"numberformat":P.nf=Ce(Vr(o[0]).Format||"General"),ya[P.nf]&&(P.nf=ya[P.nf]);for(var Q=0;Q!=392&&be[Q]!=P.nf;++Q);if(Q==392){for(Q=57;Q!=392;++Q)if(be[Q]==null){it(P.nf,Q);break}}break;case"column":if(c[c.length-1][0]!=="table")break;if(ue=Vr(o[0]),ue.Hidden&&(ue.hidden=!0,delete ue.Hidden),ue.Width&&(ue.wpx=parseInt(ue.Width,10)),!K&&ue.wpx>10){K=!0,pr=Cc;for(var q=0;q<de.length;++q)de[q]&&gt(de[q])}K&&gt(ue),de[ue.Index-1||de.length]=ue;for(var Ee=0;Ee<+ue.Span;++Ee)de[de.length]=ze(ue);break;case"namedrange":if(o[1]==="/")break;F.Names||(F.Names=[]);var O=ge(o[0]),Me={Name:O.Name,Ref:qt(O.RefersTo.slice(1),{r:0,c:0})};F.Sheets.length>0&&(Me.Sheet=F.Sheets.length-1),F.Names.push(Me);break;case"namedcell":break;case"b":break;case"i":break;case"u":break;case"s":break;case"em":break;case"h2":break;case"h3":break;case"sub":break;case"sup":break;case"span":break;case"alignment":break;case"borders":break;case"border":break;case"font":if(o[0].slice(-2)==="/>")break;o[1]==="/"?R+=a.slice(U,o.index):U=o.index+o[0].length;break;case"interior":if(!r.cellStyles)break;P.Interior=Vr(o[0]);break;case"protection":break;case"author":case"title":case"description":case"created":case"keywords":case"subject":case"category":case"company":case"lastauthor":case"lastsaved":case"lastprinted":case"version":case"revision":case"totaltime":case"hyperlinkbase":case"manager":case"contentstatus":case"identifier":case"language":case"appname":if(o[0].slice(-2)==="/>")break;o[1]==="/"?C1(G,X,a.slice(D,o.index)):D=o.index+o[0].length;break;case"paragraphs":break;case"styles":case"workbook":if(o[1]==="/"){if((l=c.pop())[0]!==o[3])throw new Error("Bad state: "+l.join("|"))}else c.push([o[3],!1]);break;case"comment":if(o[1]==="/"){if((l=c.pop())[0]!==o[3])throw new Error("Bad state: "+l.join("|"));nv(se),fe.push(se)}else c.push([o[3],!1]),l=Vr(o[0]),se={a:l.Author};break;case"autofilter":if(o[1]==="/"){if((l=c.pop())[0]!==o[3])throw new Error("Bad state: "+l.join("|"))}else if(o[0].charAt(o[0].length-2)!=="/"){var Oe=Vr(o[0]);h["!autofilter"]={ref:qt(Oe.Range).replace(/\$/g,"")},c.push([o[3],!0])}break;case"name":break;case"datavalidation":if(o[1]==="/"){if((l=c.pop())[0]!==o[3])throw new Error("Bad state: "+l.join("|"))}else o[0].charAt(o[0].length-2)!=="/"&&c.push([o[3],!0]);break;case"pixelsperinch":break;case"componentoptions":case"documentproperties":case"customdocumentproperties":case"officedocumentsettings":case"pivottable":case"pivotcache":case"names":case"mapinfo":case"pagebreaks":case"querytable":case"sorting":case"schema":case"conditionalformatting":case"smarttagtype":case"smarttags":case"excelworkbook":case"workbookoptions":case"worksheetoptions":if(o[1]==="/"){if((l=c.pop())[0]!==o[3])throw new Error("Bad state: "+l.join("|"))}else o[0].charAt(o[0].length-2)!=="/"&&c.push([o[3],!0]);break;case"null":break;default:if(c.length==0&&o[3]=="document"||c.length==0&&o[3]=="uof")return Kv(a,r);var De=!0;switch(c[c.length-1][0]){case"officedocumentsettings":switch(o[3]){case"allowpng":break;case"removepersonalinformation":break;case"downloadcomponents":break;case"locationofcomponents":break;case"colors":break;case"color":break;case"index":break;case"rgb":break;case"targetscreensize":break;case"readonlyrecommended":break;default:De=!1}break;case"componentoptions":switch(o[3]){case"toolbar":break;case"hideofficelogo":break;case"spreadsheetautofit":break;case"label":break;case"caption":break;case"maxheight":break;case"maxwidth":break;case"nextsheetnumber":break;default:De=!1}break;case"excelworkbook":switch(o[3]){case"date1904":F.WBProps.date1904=!0;break;case"windowheight":break;case"windowwidth":break;case"windowtopx":break;case"windowtopy":break;case"tabratio":break;case"protectstructure":break;case"protectwindow":break;case"protectwindows":break;case"activesheet":break;case"displayinknotes":break;case"firstvisiblesheet":break;case"supbook":break;case"sheetname":break;case"sheetindex":break;case"sheetindexfirst":break;case"sheetindexlast":break;case"dll":break;case"acceptlabelsinformulas":break;case"donotsavelinkvalues":break;case"iteration":break;case"maxiterations":break;case"maxchange":break;case"path":break;case"xct":break;case"count":break;case"selectedsheets":break;case"calculation":break;case"uncalced":break;case"startupprompt":break;case"crn":break;case"externname":break;case"formula":break;case"colfirst":break;case"collast":break;case"wantadvise":break;case"boolean":break;case"error":break;case"text":break;case"ole":break;case"noautorecover":break;case"publishobjects":break;case"donotcalculatebeforesave":break;case"number":break;case"refmoder1c1":break;case"embedsavesmarttags":break;default:De=!1}break;case"workbookoptions":switch(o[3]){case"owcversion":break;case"height":break;case"width":break;default:De=!1}break;case"worksheetoptions":switch(o[3]){case"visible":if(o[0].slice(-2)!=="/>")if(o[1]==="/")switch(a.slice(D,o.index)){case"SheetHidden":N.Hidden=1;break;case"SheetVeryHidden":N.Hidden=2;break}else D=o.index+o[0].length;break;case"header":h["!margins"]||Ot(h["!margins"]={},"xlml"),isNaN(+ge(o[0]).Margin)||(h["!margins"].header=+ge(o[0]).Margin);break;case"footer":h["!margins"]||Ot(h["!margins"]={},"xlml"),isNaN(+ge(o[0]).Margin)||(h["!margins"].footer=+ge(o[0]).Margin);break;case"pagemargins":var xe=ge(o[0]);h["!margins"]||Ot(h["!margins"]={},"xlml"),isNaN(+xe.Top)||(h["!margins"].top=+xe.Top),isNaN(+xe.Left)||(h["!margins"].left=+xe.Left),isNaN(+xe.Right)||(h["!margins"].right=+xe.Right),isNaN(+xe.Bottom)||(h["!margins"].bottom=+xe.Bottom);break;case"displayrighttoleft":F.Views||(F.Views=[]),F.Views[0]||(F.Views[0]={}),F.Views[0].RTL=!0;break;case"freezepanes":break;case"frozennosplit":break;case"splithorizontal":case"splitvertical":break;case"donotdisplaygridlines":break;case"activerow":break;case"activecol":break;case"toprowbottompane":break;case"leftcolumnrightpane":break;case"unsynced":break;case"print":break;case"printerrors":break;case"panes":break;case"scale":break;case"pane":break;case"number":break;case"layout":break;case"pagesetup":break;case"selected":break;case"protectobjects":break;case"enableselection":break;case"protectscenarios":break;case"validprinterinfo":break;case"horizontalresolution":break;case"verticalresolution":break;case"numberofcopies":break;case"activepane":break;case"toprowvisible":break;case"leftcolumnvisible":break;case"fittopage":break;case"rangeselection":break;case"papersizeindex":break;case"pagelayoutzoom":break;case"pagebreakzoom":break;case"filteron":break;case"fitwidth":break;case"fitheight":break;case"commentslayout":break;case"zoom":break;case"lefttoright":break;case"gridlines":break;case"allowsort":break;case"allowfilter":break;case"allowinsertrows":break;case"allowdeleterows":break;case"allowinsertcols":break;case"allowdeletecols":break;case"allowinserthyperlinks":break;case"allowformatcells":break;case"allowsizecols":break;case"allowsizerows":break;case"nosummaryrowsbelowdetail":h["!outline"]||(h["!outline"]={}),h["!outline"].above=!0;break;case"tabcolorindex":break;case"donotdisplayheadings":break;case"showpagelayoutzoom":break;case"nosummarycolumnsrightdetail":h["!outline"]||(h["!outline"]={}),h["!outline"].left=!0;break;case"blackandwhite":break;case"donotdisplayzeros":break;case"displaypagebreak":break;case"rowcolheadings":break;case"donotdisplayoutline":break;case"noorientation":break;case"allowusepivottables":break;case"zeroheight":break;case"viewablerange":break;case"selection":break;case"protectcontents":break;default:De=!1}break;case"pivottable":case"pivotcache":switch(o[3]){case"immediateitemsondrop":break;case"showpagemultipleitemlabel":break;case"compactrowindent":break;case"location":break;case"pivotfield":break;case"orientation":break;case"layoutform":break;case"layoutsubtotallocation":break;case"layoutcompactrow":break;case"position":break;case"pivotitem":break;case"datatype":break;case"datafield":break;case"sourcename":break;case"parentfield":break;case"ptlineitems":break;case"ptlineitem":break;case"countofsameitems":break;case"item":break;case"itemtype":break;case"ptsource":break;case"cacheindex":break;case"consolidationreference":break;case"filename":break;case"reference":break;case"nocolumngrand":break;case"norowgrand":break;case"blanklineafteritems":break;case"hidden":break;case"subtotal":break;case"basefield":break;case"mapchilditems":break;case"function":break;case"refreshonfileopen":break;case"printsettitles":break;case"mergelabels":break;case"defaultversion":break;case"refreshname":break;case"refreshdate":break;case"refreshdatecopy":break;case"versionlastrefresh":break;case"versionlastupdate":break;case"versionupdateablemin":break;case"versionrefreshablemin":break;case"calculation":break;default:De=!1}break;case"pagebreaks":switch(o[3]){case"colbreaks":break;case"colbreak":break;case"rowbreaks":break;case"rowbreak":break;case"colstart":break;case"colend":break;case"rowend":break;default:De=!1}break;case"autofilter":switch(o[3]){case"autofiltercolumn":break;case"autofiltercondition":break;case"autofilterand":break;case"autofilteror":break;default:De=!1}break;case"querytable":switch(o[3]){case"id":break;case"autoformatfont":break;case"autoformatpattern":break;case"querysource":break;case"querytype":break;case"enableredirections":break;case"refreshedinxl9":break;case"urlstring":break;case"htmltables":break;case"connection":break;case"commandtext":break;case"refreshinfo":break;case"notitles":break;case"nextid":break;case"columninfo":break;case"overwritecells":break;case"donotpromptforfile":break;case"textwizardsettings":break;case"source":break;case"number":break;case"decimal":break;case"thousandseparator":break;case"trailingminusnumbers":break;case"formatsettings":break;case"fieldtype":break;case"delimiters":break;case"tab":break;case"comma":break;case"autoformatname":break;case"versionlastedit":break;case"versionlastrefresh":break;default:De=!1}break;case"datavalidation":switch(o[3]){case"range":break;case"type":break;case"min":break;case"max":break;case"sort":break;case"descending":break;case"order":break;case"casesensitive":break;case"value":break;case"errorstyle":break;case"errormessage":break;case"errortitle":break;case"inputmessage":break;case"inputtitle":break;case"combohide":break;case"inputhide":break;case"condition":break;case"qualifier":break;case"useblank":break;case"value1":break;case"value2":break;case"format":break;case"cellrangelist":break;default:De=!1}break;case"sorting":case"conditionalformatting":switch(o[3]){case"range":break;case"type":break;case"min":break;case"max":break;case"sort":break;case"descending":break;case"order":break;case"casesensitive":break;case"value":break;case"errorstyle":break;case"errormessage":break;case"errortitle":break;case"cellrangelist":break;case"inputmessage":break;case"inputtitle":break;case"combohide":break;case"inputhide":break;case"condition":break;case"qualifier":break;case"useblank":break;case"value1":break;case"value2":break;case"format":break;default:De=!1}break;case"mapinfo":case"schema":case"data":switch(o[3]){case"map":break;case"entry":break;case"range":break;case"xpath":break;case"field":break;case"xsdtype":break;case"filteron":break;case"aggregate":break;case"elementtype":break;case"attributetype":break;case"schema":case"element":case"complextype":case"datatype":case"all":case"attribute":case"extends":break;case"row":break;default:De=!1}break;case"smarttags":break;default:De=!1;break}if(De||o[3].match(/!\[CDATA/))break;if(!c[c.length-1][1])throw"Unrecognized tag: "+o[3]+"|"+c.join("|");if(c[c.length-1][0]==="customdocumentproperties"){if(o[0].slice(-2)==="/>")break;o[1]==="/"?ev(W,X,te,a.slice(D,o.index)):(te=o,D=o.index+o[0].length);break}if(r.WTF)throw"Unrecognized tag: "+o[3]+"|"+c.join("|")}var le={};return!r.bookSheets&&!r.bookProps&&(le.Sheets=f),le.SheetNames=p,le.Workbook=F,le.SSF=ze(be),le.Props=G,le.Custprops=W,le}function es(e,t){switch(zs(t=t||{}),t.type||"base64"){case"base64":return Wn(Ir(e),t);case"binary":case"buffer":case"file":return Wn(e,t);case"array":return Wn(Tt(e),t)}}function sv(e,t){var r=[];return e.Props&&r.push(R1(e.Props,t)),e.Custprops&&r.push(O1(e.Props,e.Custprops)),r.join("")}function iv(){return""}function ov(e,t){var r=['<Style ss:ID="Default" ss:Name="Normal"><NumberFormat/></Style>'];return t.cellXfs.forEach(function(a,n){var s=[];s.push(re("NumberFormat",null,{"ss:Format":Pe(be[a.numFmtId])}));var i={"ss:ID":"s"+(21+n)};r.push(re("Style",s.join(""),i))}),re("Styles",r.join(""))}function rl(e){return re("NamedRange",null,{"ss:Name":e.Name,"ss:RefersTo":"="+Ds(e.Ref,{r:0,c:0})})}function cv(e){if(!((e||{}).Workbook||{}).Names)return"";for(var t=e.Workbook.Names,r=[],a=0;a<t.length;++a){var n=t[a];n.Sheet==null&&(n.Name.match(/^_xlfn\./)||r.push(rl(n)))}return re("Names",r.join(""))}function lv(e,t,r,a){if(!e||!((a||{}).Workbook||{}).Names)return"";for(var n=a.Workbook.Names,s=[],i=0;i<n.length;++i){var o=n[i];o.Sheet==r&&(o.Name.match(/^_xlfn\./)||s.push(rl(o)))}return s.join("")}function fv(e,t,r,a){if(!e)return"";var n=[];if(e["!margins"]&&(n.push("<PageSetup>"),e["!margins"].header&&n.push(re("Header",null,{"x:Margin":e["!margins"].header})),e["!margins"].footer&&n.push(re("Footer",null,{"x:Margin":e["!margins"].footer})),n.push(re("PageMargins",null,{"x:Bottom":e["!margins"].bottom||"0.75","x:Left":e["!margins"].left||"0.7","x:Right":e["!margins"].right||"0.7","x:Top":e["!margins"].top||"0.75"})),n.push("</PageSetup>")),a&&a.Workbook&&a.Workbook.Sheets&&a.Workbook.Sheets[r])if(a.Workbook.Sheets[r].Hidden)n.push(re("Visible",a.Workbook.Sheets[r].Hidden==1?"SheetHidden":"SheetVeryHidden",{}));else{for(var s=0;s<r&&!(a.Workbook.Sheets[s]&&!a.Workbook.Sheets[s].Hidden);++s);s==r&&n.push("<Selected/>")}return((((a||{}).Workbook||{}).Views||[])[0]||{}).RTL&&n.push("<DisplayRightToLeft/>"),e["!protect"]&&(n.push(fr("ProtectContents","True")),e["!protect"].objects&&n.push(fr("ProtectObjects","True")),e["!protect"].scenarios&&n.push(fr("ProtectScenarios","True")),e["!protect"].selectLockedCells!=null&&!e["!protect"].selectLockedCells?n.push(fr("EnableSelection","NoSelection")):e["!protect"].selectUnlockedCells!=null&&!e["!protect"].selectUnlockedCells&&n.push(fr("EnableSelection","UnlockedCells")),[["formatCells","AllowFormatCells"],["formatColumns","AllowSizeCols"],["formatRows","AllowSizeRows"],["insertColumns","AllowInsertCols"],["insertRows","AllowInsertRows"],["insertHyperlinks","AllowInsertHyperlinks"],["deleteColumns","AllowDeleteCols"],["deleteRows","AllowDeleteRows"],["sort","AllowSort"],["autoFilter","AllowFilter"],["pivotTables","AllowUsePivotTables"]].forEach(function(i){e["!protect"][i[0]]&&n.push("<"+i[1]+"/>")})),n.length==0?"":re("WorksheetOptions",n.join(""),{xmlns:Cr.x})}function hv(e){return e.map(function(t){var r=If(t.t||""),a=re("ss:Data",r,{xmlns:"http://www.w3.org/TR/REC-html40"});return re("Comment",a,{"ss:Author":t.a})}).join("")}function uv(e,t,r,a,n,s,i){if(!e||e.v==null&&e.f==null)return"";var o={};if(e.f&&(o["ss:Formula"]="="+Pe(Ds(e.f,i))),e.F&&e.F.slice(0,t.length)==t){var c=je(e.F.slice(t.length+1));o["ss:ArrayRange"]="RC:R"+(c.r==i.r?"":"["+(c.r-i.r)+"]")+"C"+(c.c==i.c?"":"["+(c.c-i.c)+"]")}if(e.l&&e.l.Target&&(o["ss:HRef"]=Pe(e.l.Target),e.l.Tooltip&&(o["x:HRefScreenTip"]=Pe(e.l.Tooltip))),r["!merges"])for(var l=r["!merges"],f=0;f!=l.length;++f)l[f].s.c!=i.c||l[f].s.r!=i.r||(l[f].e.c>l[f].s.c&&(o["ss:MergeAcross"]=l[f].e.c-l[f].s.c),l[f].e.r>l[f].s.r&&(o["ss:MergeDown"]=l[f].e.r-l[f].s.r));var p="",h="";switch(e.t){case"z":if(!a.sheetStubs)return"";break;case"n":p="Number",h=String(e.v);break;case"b":p="Boolean",h=e.v?"1":"0";break;case"e":p="Error",h=ht[e.v];break;case"d":p="DateTime",h=new Date(e.v).toISOString(),e.z==null&&(e.z=e.z||be[14]);break;case"s":p="String",h=Nf(e.v||"");break}var d=yt(a.cellXfs,e,a);o["ss:StyleID"]="s"+(21+d),o["ss:Index"]=i.c+1;var m=e.v!=null?h:"",u=e.t=="z"?"":'<Data ss:Type="'+p+'">'+m+"</Data>";return(e.c||[]).length>0&&(u+=hv(e.c)),re("Cell",u,o)}function dv(e,t){var r='<Row ss:Index="'+(e+1)+'"';return t&&(t.hpt&&!t.hpx&&(t.hpx=aa(t.hpt)),t.hpx&&(r+=' ss:AutoFitHeight="0" ss:Height="'+t.hpx+'"'),t.hidden&&(r+=' ss:Hidden="1"')),r+">"}function pv(e,t,r,a){if(!e["!ref"])return"";var n=Re(e["!ref"]),s=e["!merges"]||[],i=0,o=[];e["!cols"]&&e["!cols"].forEach(function(b,T){gt(b);var S=!!b.width,g=_n(T,b),x={"ss:Index":T+1};S&&(x["ss:Width"]=Ia(g.width)),b.hidden&&(x["ss:Hidden"]="1"),o.push(re("Column",null,x))});for(var c=Array.isArray(e),l=n.s.r;l<=n.e.r;++l){for(var f=[dv(l,(e["!rows"]||[])[l])],p=n.s.c;p<=n.e.c;++p){var h=!1;for(i=0;i!=s.length;++i)if(!(s[i].s.c>p)&&!(s[i].s.r>l)&&!(s[i].e.c<p)&&!(s[i].e.r<l)){(s[i].s.c!=p||s[i].s.r!=l)&&(h=!0);break}if(!h){var d={r:l,c:p},m=ve(d),u=c?(e[l]||[])[p]:e[m];f.push(uv(u,m,e,t,r,a,d))}}f.push("</Row>"),f.length>2&&o.push(f.join(""))}return o.join("")}function mv(e,t,r){var a=[],n=r.SheetNames[e],s=r.Sheets[n],i=s?lv(s,t,e,r):"";return i.length>0&&a.push("<Names>"+i+"</Names>"),i=s?pv(s,t,e,r):"",i.length>0&&a.push("<Table>"+i+"</Table>"),a.push(fv(s,t,e,r)),a.join("")}function bv(e,t){t||(t={}),e.SSF||(e.SSF=ze(be)),e.SSF&&(sa(),gn(e.SSF),t.revssf=kn(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF,t.cellXfs=[],yt(t.cellXfs,{},{revssf:{General:0}}));var r=[];r.push(sv(e,t)),r.push(iv()),r.push(""),r.push("");for(var a=0;a<e.SheetNames.length;++a)r.push(re("Worksheet",mv(a,t,e),{"ss:Name":Pe(e.SheetNames[a])}));return r[2]=ov(e,t),r[3]=cv(e),Ze+re("Workbook",r.join(""),{xmlns:Cr.ss,"xmlns:o":Cr.o,"xmlns:x":Cr.x,"xmlns:ss":Cr.ss,"xmlns:dt":Cr.dt,"xmlns:html":Cr.html})}function vv(e){var t={},r=e.content;if(r.l=28,t.AnsiUserType=r.read_shift(0,"lpstr-ansi"),t.AnsiClipboardFormat=c1(r),r.length-r.l<=4)return t;var a=r.read_shift(4);if(a==0||a>40||(r.l-=4,t.Reserved1=r.read_shift(0,"lpstr-ansi"),r.length-r.l<=4)||(a=r.read_shift(4),a!==1907505652)||(t.UnicodeClipboardFormat=l1(r),a=r.read_shift(4),a==0||a>40))return t;r.l-=4,t.Reserved2=r.read_shift(0,"lpwstr")}var gv=[60,1084,2066,2165,2175];function wv(e,t,r,a,n){var s=a,i=[],o=r.slice(r.l,r.l+s);if(n&&n.enc&&n.enc.insitu&&o.length>0)switch(e){case 9:case 521:case 1033:case 2057:case 47:case 405:case 225:case 406:case 312:case 404:case 10:break;case 133:break;default:n.enc.insitu(o)}i.push(o),r.l+=s;for(var c=nt(r,r.l),l=rs[c],f=0;l!=null&&gv.indexOf(c)>-1;)s=nt(r,r.l+2),f=r.l+4,c==2066?f+=4:(c==2165||c==2175)&&(f+=12),o=r.slice(f,r.l+4+s),i.push(o),r.l+=4+s,l=rs[c=nt(r,r.l)];var p=ir(i);lr(p,0);var h=0;p.lens=[];for(var d=0;d<i.length;++d)p.lens.push(h),h+=i[d].length;if(p.length<a)throw"XLS Record 0x"+e.toString(16)+" Truncated: "+p.length+" < "+a;return t.f(p,p.length,n)}function Kr(e,t,r){if(e.t!=="z"&&e.XF){var a=0;try{a=e.z||e.XF.numFmtId||0,t.cellNF&&(e.z=be[a])}catch(s){if(t.WTF)throw s}if(!t||t.cellText!==!1)try{e.t==="e"?e.w=e.w||ht[e.v]:a===0||a=="General"?e.t==="n"?(e.v|0)===e.v?e.w=e.v.toString(10):e.w=xa(e.v):e.w=Nt(e.v):e.w=zr(a,e.v,{date1904:!!r,dateNF:t&&t.dateNF})}catch(s){if(t.WTF)throw s}if(t.cellDates&&a&&e.t=="n"&&na(be[a]||String(a))){var n=Ct(e.v);n&&(e.t="d",e.v=new Date(n.y,n.m-1,n.d,n.H,n.M,n.S,n.u))}}}function rn(e,t,r){return{v:e,ixfe:t,t:r}}function kv(e,t){var r={opts:{}},a={},n=t.dense?[]:{},s={},i={},o=null,c=[],l="",f={},p,h="",d,m,u,b,T={},S=[],g,x,A=[],P=[],R={Sheets:[],WBProps:{date1904:!1},Views:[{}]},U={},I=function(ye){return ye<8?Jt[ye]:ye<64&&P[ye-8]||Jt[ye]},G=function(ye,Ue,Fr){var tr=Ue.XF.data;if(!(!tr||!tr.patternType||!Fr||!Fr.cellStyles)){Ue.s={},Ue.s.patternType=tr.patternType;var _t;(_t=Na(I(tr.icvFore)))&&(Ue.s.fgColor={rgb:_t}),(_t=Na(I(tr.icvBack)))&&(Ue.s.bgColor={rgb:_t})}},W=function(ye,Ue,Fr){if(!(Te>1)&&!(Fr.sheetRows&&ye.r>=Fr.sheetRows)){if(Fr.cellStyles&&Ue.XF&&Ue.XF.data&&G(ye,Ue,Fr),delete Ue.ixfe,delete Ue.XF,p=ye,h=ve(ye),(!i||!i.s||!i.e)&&(i={s:{r:0,c:0},e:{r:0,c:0}}),ye.r<i.s.r&&(i.s.r=ye.r),ye.c<i.s.c&&(i.s.c=ye.c),ye.r+1>i.e.r&&(i.e.r=ye.r+1),ye.c+1>i.e.c&&(i.e.c=ye.c+1),Fr.cellFormula&&Ue.f){for(var tr=0;tr<S.length;++tr)if(!(S[tr][0].s.c>ye.c||S[tr][0].s.r>ye.r)&&!(S[tr][0].e.c<ye.c||S[tr][0].e.r<ye.r)){Ue.F=we(S[tr][0]),(S[tr][0].s.c!=ye.c||S[tr][0].s.r!=ye.r)&&delete Ue.f,Ue.f&&(Ue.f=""+dr(S[tr][1],i,ye,ee,D));break}}Fr.dense?(n[ye.r]||(n[ye.r]=[]),n[ye.r][ye.c]=Ue):n[h]=Ue}},D={enc:!1,sbcch:0,snames:[],sharedf:T,arrayf:S,rrtabid:[],lastuser:"",biff:8,codepage:0,winlocked:0,cellStyles:!!t&&!!t.cellStyles,WTF:!!t&&!!t.wtf};t.password&&(D.password=t.password);var te,fe=[],se=[],de=[],ue=[],K=!1,ee=[];ee.SheetNames=D.snames,ee.sharedf=D.sharedf,ee.arrayf=D.arrayf,ee.names=[],ee.XTI=[];var me=0,Te=0,C=0,L=[],F=[],N;D.codepage=1200,jr(1200);for(var X=!1;e.l<e.length-1;){var ne=e.l,J=e.read_shift(2);if(J===0&&me===10)break;var Q=e.l===e.length?0:e.read_shift(2),q=rs[J];if(q&&q.f){if(t.bookSheets&&me===133&&J!==133)break;if(me=J,q.r===2||q.r==12){var Ee=e.read_shift(2);if(Q-=2,!D.enc&&Ee!==J&&((Ee&255)<<8|Ee>>8)!==J)throw new Error("rt mismatch: "+Ee+"!="+J);q.r==12&&(e.l+=10,Q-=10)}var O={};if(J===10?O=q.f(e,Q,D):O=wv(J,q,e,Q,D),Te==0&&[9,521,1033,2057].indexOf(me)===-1)continue;switch(J){case 34:r.opts.Date1904=R.WBProps.date1904=O;break;case 134:r.opts.WriteProtect=!0;break;case 47:if(D.enc||(e.l=0),D.enc=O,!t.password)throw new Error("File is password-protected");if(O.valid==null)throw new Error("Encryption scheme unsupported");if(!O.valid)throw new Error("Password is incorrect");break;case 92:D.lastuser=O;break;case 66:var Me=Number(O);switch(Me){case 21010:Me=1200;break;case 32768:Me=1e4;break;case 32769:Me=1252;break}jr(D.codepage=Me),X=!0;break;case 317:D.rrtabid=O;break;case 25:D.winlocked=O;break;case 439:r.opts.RefreshAll=O;break;case 12:r.opts.CalcCount=O;break;case 16:r.opts.CalcDelta=O;break;case 17:r.opts.CalcIter=O;break;case 13:r.opts.CalcMode=O;break;case 14:r.opts.CalcPrecision=O;break;case 95:r.opts.CalcSaveRecalc=O;break;case 15:D.CalcRefMode=O;break;case 2211:r.opts.FullCalc=O;break;case 129:O.fDialog&&(n["!type"]="dialog"),O.fBelow||((n["!outline"]||(n["!outline"]={})).above=!0),O.fRight||((n["!outline"]||(n["!outline"]={})).left=!0);break;case 224:A.push(O);break;case 430:ee.push([O]),ee[ee.length-1].XTI=[];break;case 35:case 547:ee[ee.length-1].push(O);break;case 24:case 536:N={Name:O.Name,Ref:dr(O.rgce,i,null,ee,D)},O.itab>0&&(N.Sheet=O.itab-1),ee.names.push(N),ee[0]||(ee[0]=[],ee[0].XTI=[]),ee[ee.length-1].push(O),O.Name=="_xlnm._FilterDatabase"&&O.itab>0&&O.rgce&&O.rgce[0]&&O.rgce[0][0]&&O.rgce[0][0][0]=="PtgArea3d"&&(F[O.itab-1]={ref:we(O.rgce[0][0][1][2])});break;case 22:D.ExternCount=O;break;case 23:ee.length==0&&(ee[0]=[],ee[0].XTI=[]),ee[ee.length-1].XTI=ee[ee.length-1].XTI.concat(O),ee.XTI=ee.XTI.concat(O);break;case 2196:if(D.biff<8)break;N!=null&&(N.Comment=O[1]);break;case 18:n["!protect"]=O;break;case 19:O!==0&&D.WTF&&console.error("Password verifier: "+O);break;case 133:s[O.pos]=O,D.snames.push(O.name);break;case 10:{if(--Te)break;if(i.e){if(i.e.r>0&&i.e.c>0){if(i.e.r--,i.e.c--,n["!ref"]=we(i),t.sheetRows&&t.sheetRows<=i.e.r){var Oe=i.e.r;i.e.r=t.sheetRows-1,n["!fullref"]=n["!ref"],n["!ref"]=we(i),i.e.r=Oe}i.e.r++,i.e.c++}fe.length>0&&(n["!merges"]=fe),se.length>0&&(n["!objects"]=se),de.length>0&&(n["!cols"]=de),ue.length>0&&(n["!rows"]=ue),R.Sheets.push(U)}l===""?f=n:a[l]=n,n=t.dense?[]:{}}break;case 9:case 521:case 1033:case 2057:{if(D.biff===8&&(D.biff={9:2,521:3,1033:4}[J]||{512:2,768:3,1024:4,1280:5,1536:8,2:2,7:2}[O.BIFFVer]||8),D.biffguess=O.BIFFVer==0,O.BIFFVer==0&&O.dt==4096&&(D.biff=5,X=!0,jr(D.codepage=28591)),D.biff==8&&O.BIFFVer==0&&O.dt==16&&(D.biff=2),Te++)break;if(n=t.dense?[]:{},D.biff<8&&!X&&(X=!0,jr(D.codepage=t.codepage||1252)),D.biff<5||O.BIFFVer==0&&O.dt==4096){l===""&&(l="Sheet1"),i={s:{r:0,c:0},e:{r:0,c:0}};var De={pos:e.l-Q,name:l};s[De.pos]=De,D.snames.push(l)}else l=(s[ne]||{name:""}).name;O.dt==32&&(n["!type"]="chart"),O.dt==64&&(n["!type"]="macro"),fe=[],se=[],D.arrayf=S=[],de=[],ue=[],K=!1,U={Hidden:(s[ne]||{hs:0}).hs,name:l}}break;case 515:case 3:case 2:n["!type"]=="chart"&&(t.dense?(n[O.r]||[])[O.c]:n[ve({c:O.c,r:O.r})])&&++O.c,g={ixfe:O.ixfe,XF:A[O.ixfe]||{},v:O.val,t:"n"},C>0&&(g.z=L[g.ixfe>>8&63]),Kr(g,t,r.opts.Date1904),W({c:O.c,r:O.r},g,t);break;case 5:case 517:g={ixfe:O.ixfe,XF:A[O.ixfe],v:O.val,t:O.t},C>0&&(g.z=L[g.ixfe>>8&63]),Kr(g,t,r.opts.Date1904),W({c:O.c,r:O.r},g,t);break;case 638:g={ixfe:O.ixfe,XF:A[O.ixfe],v:O.rknum,t:"n"},C>0&&(g.z=L[g.ixfe>>8&63]),Kr(g,t,r.opts.Date1904),W({c:O.c,r:O.r},g,t);break;case 189:for(var xe=O.c;xe<=O.C;++xe){var le=O.rkrec[xe-O.c][0];g={ixfe:le,XF:A[le],v:O.rkrec[xe-O.c][1],t:"n"},C>0&&(g.z=L[g.ixfe>>8&63]),Kr(g,t,r.opts.Date1904),W({c:xe,r:O.r},g,t)}break;case 6:case 518:case 1030:{if(O.val=="String"){o=O;break}if(g=rn(O.val,O.cell.ixfe,O.tt),g.XF=A[g.ixfe],t.cellFormula){var rr=O.formula;if(rr&&rr[0]&&rr[0][0]&&rr[0][0][0]=="PtgExp"){var Gr=rr[0][0][1][0],wr=rr[0][0][1][1],ca=ve({r:Gr,c:wr});T[ca]?g.f=""+dr(O.formula,i,O.cell,ee,D):g.F=((t.dense?(n[Gr]||[])[wr]:n[ca])||{}).F}else g.f=""+dr(O.formula,i,O.cell,ee,D)}C>0&&(g.z=L[g.ixfe>>8&63]),Kr(g,t,r.opts.Date1904),W(O.cell,g,t),o=O}break;case 7:case 519:if(o)o.val=O,g=rn(O,o.cell.ixfe,"s"),g.XF=A[g.ixfe],t.cellFormula&&(g.f=""+dr(o.formula,i,o.cell,ee,D)),C>0&&(g.z=L[g.ixfe>>8&63]),Kr(g,t,r.opts.Date1904),W(o.cell,g,t),o=null;else throw new Error("String record expects Formula");break;case 33:case 545:{S.push(O);var Gt=ve(O[0].s);if(d=t.dense?(n[O[0].s.r]||[])[O[0].s.c]:n[Gt],t.cellFormula&&d){if(!o||!Gt||!d)break;d.f=""+dr(O[1],i,O[0],ee,D),d.F=we(O[0])}}break;case 1212:{if(!t.cellFormula)break;if(h){if(!o)break;T[ve(o.cell)]=O[0],d=t.dense?(n[o.cell.r]||[])[o.cell.c]:n[ve(o.cell)],(d||{}).f=""+dr(O[0],i,p,ee,D)}}break;case 253:g=rn(c[O.isst].t,O.ixfe,"s"),c[O.isst].h&&(g.h=c[O.isst].h),g.XF=A[g.ixfe],C>0&&(g.z=L[g.ixfe>>8&63]),Kr(g,t,r.opts.Date1904),W({c:O.c,r:O.r},g,t);break;case 513:t.sheetStubs&&(g={ixfe:O.ixfe,XF:A[O.ixfe],t:"z"},C>0&&(g.z=L[g.ixfe>>8&63]),Kr(g,t,r.opts.Date1904),W({c:O.c,r:O.r},g,t));break;case 190:if(t.sheetStubs)for(var kr=O.c;kr<=O.C;++kr){var St=O.ixfe[kr-O.c];g={ixfe:St,XF:A[St],t:"z"},C>0&&(g.z=L[g.ixfe>>8&63]),Kr(g,t,r.opts.Date1904),W({c:kr,r:O.r},g,t)}break;case 214:case 516:case 4:g=rn(O.val,O.ixfe,"s"),g.XF=A[g.ixfe],C>0&&(g.z=L[g.ixfe>>8&63]),Kr(g,t,r.opts.Date1904),W({c:O.c,r:O.r},g,t);break;case 0:case 512:Te===1&&(i=O);break;case 252:c=O;break;case 1054:if(D.biff==4){L[C++]=O[1];for(var ut=0;ut<C+163&&be[ut]!=O[1];++ut);ut>=163&&it(O[1],C+163)}else it(O[1],O[0]);break;case 30:{L[C++]=O;for(var dt=0;dt<C+163&&be[dt]!=O;++dt);dt>=163&&it(O,C+163)}break;case 229:fe=fe.concat(O);break;case 93:se[O.cmo[0]]=D.lastobj=O;break;case 438:D.lastobj.TxO=O;break;case 127:D.lastobj.ImData=O;break;case 440:for(b=O[0].s.r;b<=O[0].e.r;++b)for(u=O[0].s.c;u<=O[0].e.c;++u)d=t.dense?(n[b]||[])[u]:n[ve({c:u,r:b})],d&&(d.l=O[1]);break;case 2048:for(b=O[0].s.r;b<=O[0].e.r;++b)for(u=O[0].s.c;u<=O[0].e.c;++u)d=t.dense?(n[b]||[])[u]:n[ve({c:u,r:b})],d&&d.l&&(d.l.Tooltip=O[1]);break;case 28:{if(D.biff<=5&&D.biff>=2)break;d=t.dense?(n[O[0].r]||[])[O[0].c]:n[ve(O[0])];var rt=se[O[2]];d||(t.dense?(n[O[0].r]||(n[O[0].r]=[]),d=n[O[0].r][O[0].c]={t:"z"}):d=n[ve(O[0])]={t:"z"},i.e.r=Math.max(i.e.r,O[0].r),i.s.r=Math.min(i.s.r,O[0].r),i.e.c=Math.max(i.e.c,O[0].c),i.s.c=Math.min(i.s.c,O[0].c)),d.c||(d.c=[]),m={a:O[1],t:rt.TxO.t},d.c.push(m)}break;case 2173:od(A[O.ixfe],O.ext);break;case 125:{if(!D.cellStyles)break;for(;O.e>=O.s;)de[O.e--]={width:O.w/256,level:O.level||0,hidden:!!(O.flags&1)},K||(K=!0,Is(O.w/256)),gt(de[O.e+1])}break;case 520:{var tt={};O.level!=null&&(ue[O.r]=tt,tt.level=O.level),O.hidden&&(ue[O.r]=tt,tt.hidden=!0),O.hpt&&(ue[O.r]=tt,tt.hpt=O.hpt,tt.hpx=aa(O.hpt))}break;case 38:case 39:case 40:case 41:n["!margins"]||Ot(n["!margins"]={}),n["!margins"][{38:"left",39:"right",40:"top",41:"bottom"}[J]]=O;break;case 161:n["!margins"]||Ot(n["!margins"]={}),n["!margins"].header=O.header,n["!margins"].footer=O.footer;break;case 574:O.RTL&&(R.Views[0].RTL=!0);break;case 146:P=O;break;case 2198:te=O;break;case 140:x=O;break;case 442:l?U.CodeName=O||U.name:R.WBProps.CodeName=O||"ThisWorkbook";break}}else q||console.error("Missing Info for XLS Record 0x"+J.toString(16)),e.l+=Q}return r.SheetNames=Ye(s).sort(function(ye,Ue){return Number(ye)-Number(Ue)}).map(function(ye){return s[ye].name}),t.bookSheets||(r.Sheets=a),!r.SheetNames.length&&f["!ref"]?(r.SheetNames.push("Sheet1"),r.Sheets&&(r.Sheets.Sheet1=f)):r.Preamble=f,r.Sheets&&F.forEach(function(ye,Ue){r.Sheets[r.SheetNames[Ue]]["!autofilter"]=ye}),r.Strings=c,r.SSF=ze(be),D.enc&&(r.Encryption=D.enc),te&&(r.Themes=te),r.Metadata={},x!==void 0&&(r.Metadata.Country=x),ee.names.length>0&&(R.Names=ee.names),r.Workbook=R,r}var Sa={SI:"e0859ff2f94f6810ab9108002b27b3d9",DSI:"02d5cdd59c2e1b10939708002b2cf9ae",UDI:"05d5cdd59c2e1b10939708002b2cf9ae"};function Tv(e,t,r){var a=pe.find(e,"/!DocumentSummaryInformation");if(a&&a.size>0)try{var n=Ni(a,Yn,Sa.DSI);for(var s in n)t[s]=n[s]}catch(l){if(r.WTF)throw l}var i=pe.find(e,"/!SummaryInformation");if(i&&i.size>0)try{var o=Ni(i,Kn,Sa.SI);for(var c in o)t[c]==null&&(t[c]=o[c])}catch(l){if(r.WTF)throw l}t.HeadingPairs&&t.TitlesOfParts&&(ec(t.HeadingPairs,t.TitlesOfParts,t,r),delete t.HeadingPairs,delete t.TitlesOfParts)}function Ev(e,t){var r=[],a=[],n=[],s=0,i,o=oi(Yn,"n"),c=oi(Kn,"n");if(e.Props)for(i=Ye(e.Props),s=0;s<i.length;++s)(Object.prototype.hasOwnProperty.call(o,i[s])?r:Object.prototype.hasOwnProperty.call(c,i[s])?a:n).push([i[s],e.Props[i[s]]]);if(e.Custprops)for(i=Ye(e.Custprops),s=0;s<i.length;++s)Object.prototype.hasOwnProperty.call(e.Props||{},i[s])||(Object.prototype.hasOwnProperty.call(o,i[s])?r:Object.prototype.hasOwnProperty.call(c,i[s])?a:n).push([i[s],e.Custprops[i[s]]]);var l=[];for(s=0;s<n.length;++s)oc.indexOf(n[s][0])>-1||Qo.indexOf(n[s][0])>-1||n[s][1]!=null&&l.push(n[s]);a.length&&pe.utils.cfb_add(t,"/SummaryInformation",Ii(a,Sa.SI,c,Kn)),(r.length||l.length)&&pe.utils.cfb_add(t,"/DocumentSummaryInformation",Ii(r,Sa.DSI,o,Yn,l.length?l:null,Sa.UDI))}function tl(e,t){t||(t={}),zs(t),cs(),t.codepage&&os(t.codepage);var r,a;if(e.FullPaths){if(pe.find(e,"/encryption"))throw new Error("File is password-protected");r=pe.find(e,"!CompObj"),a=pe.find(e,"/Workbook")||pe.find(e,"/Book")}else{switch(t.type){case"base64":e=Or(Ir(e));break;case"binary":e=Or(e);break;case"buffer":break;case"array":Array.isArray(e)||(e=Array.prototype.slice.call(e));break}lr(e,0),a={content:e}}var n,s;if(r&&vv(r),t.bookProps&&!t.bookSheets)n={};else{var i=Se?"buffer":"array";if(a&&a.content)n=kv(a.content,t);else if((s=pe.find(e,"PerfectOffice_MAIN"))&&s.content)n=ta.to_workbook(s.content,(t.type=i,t));else if((s=pe.find(e,"NativeContent_MAIN"))&&s.content)n=ta.to_workbook(s.content,(t.type=i,t));else throw(s=pe.find(e,"MN0"))&&s.content?new Error("Unsupported Works 4 for Mac file"):new Error("Cannot find Workbook stream");t.bookVBA&&e.FullPaths&&pe.find(e,"/_VBA_PROJECT_CUR/VBA/dir")&&(n.vbaraw=Dd(e))}var o={};return e.FullPaths&&Tv(e,o,t),n.Props=n.Custprops=o,t.bookFiles&&(n.cfb=e),n}function yv(e,t){var r=t||{},a=pe.utils.cfb_new({root:"R"}),n="/Workbook";switch(r.bookType||"xls"){case"xls":r.bookType="biff8";case"xla":r.bookType||(r.bookType="xla");case"biff8":n="/Workbook",r.biff=8;break;case"biff5":n="/Book",r.biff=5;break;default:throw new Error("invalid type "+r.bookType+" for XLS CFB")}return pe.utils.cfb_add(a,n,al(e,r)),r.biff==8&&(e.Props||e.Custprops)&&Ev(e,a),r.biff==8&&e.vbaraw&&Ld(a,pe.read(e.vbaraw,{type:typeof e.vbaraw=="string"?"binary":"buffer"})),a}var Da={0:{f:Xp},1:{f:rm},2:{f:km},3:{f:lm},4:{f:sm},5:{f:vm},6:{f:_m},7:{f:dm},8:{f:Im},9:{f:Nm},10:{f:Rm},11:{f:Om},12:{f:am},13:{f:Em},14:{f:hm},15:{f:om},16:{f:Yc},17:{f:Am},18:{f:mm},19:{f:Es},20:{},21:{},22:{},23:{},24:{},25:{},26:{},27:{},28:{},29:{},30:{},31:{},32:{},33:{},34:{},35:{T:1},36:{T:-1},37:{T:1},38:{T:-1},39:{f:Ab},40:{},42:{},43:{f:_0},44:{f:y0},45:{f:C0},46:{f:O0},47:{f:R0},48:{},49:{f:Jf},50:{},51:{f:fd},52:{T:1},53:{T:-1},54:{T:1},55:{T:-1},56:{T:1},57:{T:-1},58:{},59:{},60:{f:gc},62:{f:Sm},63:{f:wd},64:{f:Ym},65:{},66:{},67:{},68:{},69:{},70:{},128:{},129:{T:1},130:{T:-1},131:{T:1,f:vr,p:0},132:{T:-1},133:{T:1},134:{T:-1},135:{T:1},136:{T:-1},137:{T:1,f:Vm},138:{T:-1},139:{T:1},140:{T:-1},141:{T:1},142:{T:-1},143:{T:1},144:{T:-1},145:{T:1},146:{T:-1},147:{f:Qp},148:{f:Jp,p:16},151:{f:Um},152:{},153:{f:Sb},154:{},155:{},156:{f:Eb},157:{},158:{},159:{T:1,f:Wu},160:{T:-1},161:{T:1,f:zt},162:{T:-1},163:{T:1},164:{T:-1},165:{T:1},166:{T:-1},167:{},168:{},169:{},170:{},171:{},172:{T:1},173:{T:-1},174:{},175:{},176:{f:Fm},177:{T:1},178:{T:-1},179:{T:1},180:{T:-1},181:{T:1},182:{T:-1},183:{T:1},184:{T:-1},185:{T:1},186:{T:-1},187:{T:1},188:{T:-1},189:{T:1},190:{T:-1},191:{T:1},192:{T:-1},193:{T:1},194:{T:-1},195:{T:1},196:{T:-1},197:{T:1},198:{T:-1},199:{T:1},200:{T:-1},201:{T:1},202:{T:-1},203:{T:1},204:{T:-1},205:{T:1},206:{T:-1},207:{T:1},208:{T:-1},209:{T:1},210:{T:-1},211:{T:1},212:{T:-1},213:{T:1},214:{T:-1},215:{T:1},216:{T:-1},217:{T:1},218:{T:-1},219:{T:1},220:{T:-1},221:{T:1},222:{T:-1},223:{T:1},224:{T:-1},225:{T:1},226:{T:-1},227:{T:1},228:{T:-1},229:{T:1},230:{T:-1},231:{T:1},232:{T:-1},233:{T:1},234:{T:-1},235:{T:1},236:{T:-1},237:{T:1},238:{T:-1},239:{T:1},240:{T:-1},241:{T:1},242:{T:-1},243:{T:1},244:{T:-1},245:{T:1},246:{T:-1},247:{T:1},248:{T:-1},249:{T:1},250:{T:-1},251:{T:1},252:{T:-1},253:{T:1},254:{T:-1},255:{T:1},256:{T:-1},257:{T:1},258:{T:-1},259:{T:1},260:{T:-1},261:{T:1},262:{T:-1},263:{T:1},264:{T:-1},265:{T:1},266:{T:-1},267:{T:1},268:{T:-1},269:{T:1},270:{T:-1},271:{T:1},272:{T:-1},273:{T:1},274:{T:-1},275:{T:1},276:{T:-1},277:{},278:{T:1},279:{T:-1},280:{T:1},281:{T:-1},282:{T:1},283:{T:1},284:{T:-1},285:{T:1},286:{T:-1},287:{T:1},288:{T:-1},289:{T:1},290:{T:-1},291:{T:1},292:{T:-1},293:{T:1},294:{T:-1},295:{T:1},296:{T:-1},297:{T:1},298:{T:-1},299:{T:1},300:{T:-1},301:{T:1},302:{T:-1},303:{T:1},304:{T:-1},305:{T:1},306:{T:-1},307:{T:1},308:{T:-1},309:{T:1},310:{T:-1},311:{T:1},312:{T:-1},313:{T:-1},314:{T:1},315:{T:-1},316:{T:1},317:{T:-1},318:{T:1},319:{T:-1},320:{T:1},321:{T:-1},322:{T:1},323:{T:-1},324:{T:1},325:{T:-1},326:{T:1},327:{T:-1},328:{T:1},329:{T:-1},330:{T:1},331:{T:-1},332:{T:1},333:{T:-1},334:{T:1},335:{f:cd},336:{T:-1},337:{f:dd,T:1},338:{T:-1},339:{T:1},340:{T:-1},341:{T:1},342:{T:-1},343:{T:1},344:{T:-1},345:{T:1},346:{T:-1},347:{T:1},348:{T:-1},349:{T:1},350:{T:-1},351:{},352:{},353:{T:1},354:{T:-1},355:{f:Xn},357:{},358:{},359:{},360:{T:1},361:{},362:{f:vc},363:{},364:{},366:{},367:{},368:{},369:{},370:{},371:{},372:{T:1},373:{T:-1},374:{T:1},375:{T:-1},376:{T:1},377:{T:-1},378:{T:1},379:{T:-1},380:{T:1},381:{T:-1},382:{T:1},383:{T:-1},384:{T:1},385:{T:-1},386:{T:1},387:{T:-1},388:{T:1},389:{T:-1},390:{T:1},391:{T:-1},392:{T:1},393:{T:-1},394:{T:1},395:{T:-1},396:{},397:{},398:{},399:{},400:{},401:{T:1},403:{},404:{},405:{},406:{},407:{},408:{},409:{},410:{},411:{},412:{},413:{},414:{},415:{},416:{},417:{},418:{},419:{},420:{},421:{},422:{T:1},423:{T:1},424:{T:-1},425:{T:-1},426:{f:Bm},427:{f:Wm},428:{},429:{T:1},430:{T:-1},431:{T:1},432:{T:-1},433:{T:1},434:{T:-1},435:{T:1},436:{T:-1},437:{T:1},438:{T:-1},439:{T:1},440:{T:-1},441:{T:1},442:{T:-1},443:{T:1},444:{T:-1},445:{T:1},446:{T:-1},447:{T:1},448:{T:-1},449:{T:1},450:{T:-1},451:{T:1},452:{T:-1},453:{T:1},454:{T:-1},455:{T:1},456:{T:-1},457:{T:1},458:{T:-1},459:{T:1},460:{T:-1},461:{T:1},462:{T:-1},463:{T:1},464:{T:-1},465:{T:1},466:{T:-1},467:{T:1},468:{T:-1},469:{T:1},470:{T:-1},471:{},472:{},473:{T:1},474:{T:-1},475:{},476:{f:Hm},477:{},478:{},479:{T:1},480:{T:-1},481:{T:1},482:{T:-1},483:{T:1},484:{T:-1},485:{f:qp},486:{T:1},487:{T:-1},488:{T:1},489:{T:-1},490:{T:1},491:{T:-1},492:{T:1},493:{T:-1},494:{f:Lm},495:{T:1},496:{T:-1},497:{T:1},498:{T:-1},499:{},500:{T:1},501:{T:-1},502:{T:1},503:{T:-1},504:{},505:{T:1},506:{T:-1},507:{},508:{T:1},509:{T:-1},510:{T:1},511:{T:-1},512:{},513:{},514:{T:1},515:{T:-1},516:{T:1},517:{T:-1},518:{T:1},519:{T:-1},520:{T:1},521:{T:-1},522:{},523:{},524:{},525:{},526:{},527:{},528:{T:1},529:{T:-1},530:{T:1},531:{T:-1},532:{T:1},533:{T:-1},534:{},535:{},536:{},537:{},538:{T:1},539:{T:-1},540:{T:1},541:{T:-1},542:{T:1},548:{},549:{},550:{f:Xn},551:{},552:{},553:{},554:{T:1},555:{T:-1},556:{T:1},557:{T:-1},558:{T:1},559:{T:-1},560:{T:1},561:{T:-1},562:{},564:{},565:{T:1},566:{T:-1},569:{T:1},570:{T:-1},572:{},573:{T:1},574:{T:-1},577:{},578:{},579:{},580:{},581:{},582:{},583:{},584:{},585:{},586:{},587:{},588:{T:-1},589:{},590:{T:1},591:{T:-1},592:{T:1},593:{T:-1},594:{T:1},595:{T:-1},596:{},597:{T:1},598:{T:-1},599:{T:1},600:{T:-1},601:{T:1},602:{T:-1},603:{T:1},604:{T:-1},605:{T:1},606:{T:-1},607:{},608:{T:1},609:{T:-1},610:{},611:{T:1},612:{T:-1},613:{T:1},614:{T:-1},615:{T:1},616:{T:-1},617:{T:1},618:{T:-1},619:{T:1},620:{T:-1},625:{},626:{T:1},627:{T:-1},628:{T:1},629:{T:-1},630:{T:1},631:{T:-1},632:{f:Od},633:{T:1},634:{T:-1},635:{T:1,f:Cd},636:{T:-1},637:{f:e1},638:{T:1},639:{},640:{T:-1},641:{T:1},642:{T:-1},643:{T:1},644:{},645:{T:-1},646:{T:1},648:{T:1},649:{},650:{T:-1},651:{f:hb},652:{},653:{T:1},654:{T:-1},655:{T:1},656:{T:-1},657:{T:1},658:{T:-1},659:{},660:{T:1},661:{},662:{T:-1},663:{},664:{T:1},665:{},666:{T:-1},667:{},668:{},669:{},671:{T:1},672:{T:-1},673:{T:1},674:{T:-1},675:{},676:{},677:{},678:{},679:{},680:{},681:{},1024:{},1025:{},1026:{T:1},1027:{T:-1},1028:{T:1},1029:{T:-1},1030:{},1031:{T:1},1032:{T:-1},1033:{T:1},1034:{T:-1},1035:{},1036:{},1037:{},1038:{T:1},1039:{T:-1},1040:{},1041:{T:1},1042:{T:-1},1043:{},1044:{},1045:{},1046:{T:1},1047:{T:-1},1048:{T:1},1049:{T:-1},1050:{},1051:{T:1},1052:{T:1},1053:{f:Km},1054:{T:1},1055:{},1056:{T:1},1057:{T:-1},1058:{T:1},1059:{T:-1},1061:{},1062:{T:1},1063:{T:-1},1064:{T:1},1065:{T:-1},1066:{T:1},1067:{T:-1},1068:{T:1},1069:{T:-1},1070:{T:1},1071:{T:-1},1072:{T:1},1073:{T:-1},1075:{T:1},1076:{T:-1},1077:{T:1},1078:{T:-1},1079:{T:1},1080:{T:-1},1081:{T:1},1082:{T:-1},1083:{T:1},1084:{T:-1},1085:{},1086:{T:1},1087:{T:-1},1088:{T:1},1089:{T:-1},1090:{T:1},1091:{T:-1},1092:{T:1},1093:{T:-1},1094:{T:1},1095:{T:-1},1096:{},1097:{T:1},1098:{},1099:{T:-1},1100:{T:1},1101:{T:-1},1102:{},1103:{},1104:{},1105:{},1111:{},1112:{},1113:{T:1},1114:{T:-1},1115:{T:1},1116:{T:-1},1117:{},1118:{T:1},1119:{T:-1},1120:{T:1},1121:{T:-1},1122:{T:1},1123:{T:-1},1124:{T:1},1125:{T:-1},1126:{},1128:{T:1},1129:{T:-1},1130:{},1131:{T:1},1132:{T:-1},1133:{T:1},1134:{T:-1},1135:{T:1},1136:{T:-1},1137:{T:1},1138:{T:-1},1139:{T:1},1140:{T:-1},1141:{},1142:{T:1},1143:{T:-1},1144:{T:1},1145:{T:-1},1146:{},1147:{T:1},1148:{T:-1},1149:{T:1},1150:{T:-1},1152:{T:1},1153:{T:-1},1154:{T:-1},1155:{T:-1},1156:{T:-1},1157:{T:1},1158:{T:-1},1159:{T:1},1160:{T:-1},1161:{T:1},1162:{T:-1},1163:{T:1},1164:{T:-1},1165:{T:1},1166:{T:-1},1167:{T:1},1168:{T:-1},1169:{T:1},1170:{T:-1},1171:{},1172:{T:1},1173:{T:-1},1177:{},1178:{T:1},1180:{},1181:{},1182:{},2048:{T:1},2049:{T:-1},2050:{},2051:{T:1},2052:{T:-1},2053:{},2054:{},2055:{T:1},2056:{T:-1},2057:{T:1},2058:{T:-1},2060:{},2067:{},2068:{T:1},2069:{T:-1},2070:{},2071:{},2072:{T:1},2073:{T:-1},2075:{},2076:{},2077:{T:1},2078:{T:-1},2079:{},2080:{T:1},2081:{T:-1},2082:{},2083:{T:1},2084:{T:-1},2085:{T:1},2086:{T:-1},2087:{T:1},2088:{T:-1},2089:{T:1},2090:{T:-1},2091:{},2092:{},2093:{T:1},2094:{T:-1},2095:{},2096:{T:1},2097:{T:-1},2098:{T:1},2099:{T:-1},2100:{T:1},2101:{T:-1},2102:{},2103:{T:1},2104:{T:-1},2105:{},2106:{T:1},2107:{T:-1},2108:{},2109:{T:1},2110:{T:-1},2111:{T:1},2112:{T:-1},2113:{T:1},2114:{T:-1},2115:{},2116:{},2117:{},2118:{T:1},2119:{T:-1},2120:{},2121:{T:1},2122:{T:-1},2123:{T:1},2124:{T:-1},2125:{},2126:{T:1},2127:{T:-1},2128:{},2129:{T:1},2130:{T:-1},2131:{T:1},2132:{T:-1},2133:{T:1},2134:{},2135:{},2136:{},2137:{T:1},2138:{T:-1},2139:{T:1},2140:{T:-1},2141:{},3072:{},3073:{},4096:{T:1},4097:{T:-1},5002:{T:1},5003:{T:-1},5081:{T:1},5082:{T:-1},5083:{},5084:{T:1},5085:{T:-1},5086:{T:1},5087:{T:-1},5088:{},5089:{},5090:{},5092:{T:1},5093:{T:-1},5094:{},5095:{T:1},5096:{T:-1},5097:{},5099:{},65535:{n:""}},rs={6:{f:Un},10:{f:pt},12:{f:er},13:{f:er},14:{f:Ke},15:{f:Ke},16:{f:mr},17:{f:Ke},18:{f:Ke},19:{f:er},20:{f:Ui},21:{f:Ui},23:{f:vc},24:{f:Wi},25:{f:Ke},26:{},27:{},28:{f:Kh},29:{},34:{f:Ke},35:{f:Bi},38:{f:mr},39:{f:mr},40:{f:mr},41:{f:mr},42:{f:Ke},43:{f:Ke},47:{f:o0},49:{f:Eh},51:{f:er},60:{},61:{f:vh},64:{f:Ke},65:{f:Th},66:{f:er},77:{},80:{},81:{},82:{},85:{f:er},89:{},90:{},91:{},92:{f:ih},93:{f:qh},94:{},95:{f:Ke},96:{},97:{},99:{f:Ke},125:{f:gc},128:{f:Mh},129:{f:ch},130:{f:er},131:{f:Ke},132:{f:Ke},133:{f:lh},134:{},140:{f:iu},141:{f:er},144:{},146:{f:lu},151:{},152:{},153:{},154:{},155:{},156:{f:er},157:{},158:{},160:{f:bu},161:{f:uu},174:{},175:{},176:{},177:{},178:{},180:{},181:{},182:{},184:{},185:{},189:{f:Fh},190:{f:Ph},193:{f:pt},197:{},198:{},199:{},200:{},201:{},202:{f:Ke},203:{},204:{},205:{},206:{},207:{},208:{},209:{},210:{},211:{},213:{},215:{},216:{},217:{},218:{f:er},220:{},221:{f:Ke},222:{},224:{f:Lh},225:{f:sh},226:{f:pt},227:{},229:{f:Jh},233:{},235:{},236:{},237:{},239:{},240:{},241:{},242:{},244:{},245:{},246:{},247:{},248:{},249:{},251:{},252:{f:hh},253:{f:Sh},255:{f:dh},256:{},259:{},290:{},311:{},312:{},315:{},317:{f:cc},318:{},319:{},320:{},330:{},331:{},333:{},334:{},335:{},336:{},337:{},338:{},339:{},340:{},351:{},352:{f:Ke},353:{f:pt},401:{},402:{},403:{},404:{},405:{},406:{},407:{},408:{},425:{},426:{},427:{},428:{},429:{},430:{f:Hh},431:{f:Ke},432:{},433:{},434:{},437:{},438:{f:ru},439:{f:Ke},440:{f:tu},441:{},442:{f:za},443:{},444:{f:er},445:{},446:{},448:{f:pt},449:{f:bh,r:2},450:{f:pt},512:{f:Di},513:{f:mu},515:{f:Wh},516:{f:xh},517:{f:Mi},519:{f:vu},520:{f:ph},523:{},545:{f:zi},549:{f:Pi},566:{},574:{f:wh},638:{f:Ih},659:{},1048:{},1054:{f:Ch},1084:{},1212:{f:$h},2048:{f:nu},2049:{},2050:{},2051:{},2052:{},2053:{},2054:{},2055:{},2056:{},2057:{f:qa},2058:{},2059:{},2060:{},2061:{},2062:{},2063:{},2064:{},2066:{},2067:{},2128:{},2129:{},2130:{},2131:{},2132:{},2133:{},2134:{},2135:{},2136:{},2137:{},2138:{},2146:{},2147:{r:12},2148:{},2149:{},2150:{},2151:{f:pt},2152:{},2154:{},2155:{},2156:{},2161:{},2162:{},2164:{},2165:{},2166:{},2167:{},2168:{},2169:{},2170:{},2171:{},2172:{f:fu,r:12},2173:{f:id,r:12},2174:{},2175:{},2180:{},2181:{},2182:{},2183:{},2184:{},2185:{},2186:{},2187:{},2188:{f:Ke,r:12},2189:{},2190:{r:12},2191:{},2192:{},2194:{},2195:{},2196:{f:jh,r:12},2197:{},2198:{f:ed,r:12},2199:{},2200:{},2201:{},2202:{f:Xh,r:12},2203:{f:pt},2204:{},2205:{},2206:{},2207:{},2211:{f:mh},2212:{},2213:{},2214:{},2215:{},4097:{},4098:{},4099:{},4102:{},4103:{},4105:{},4106:{},4107:{},4108:{},4109:{},4116:{},4117:{},4118:{},4119:{},4120:{},4121:{},4122:{},4123:{},4124:{},4125:{},4126:{},4127:{},4128:{},4129:{},4130:{},4132:{},4133:{},4134:{f:er},4135:{},4146:{},4147:{},4148:{},4149:{},4154:{},4156:{},4157:{},4158:{},4159:{},4160:{},4161:{},4163:{},4164:{f:du},4165:{},4166:{},4168:{},4170:{},4171:{},4174:{},4175:{},4176:{},4177:{},4187:{},4188:{f:cu},4189:{},4191:{},4192:{},4193:{},4194:{},4195:{},4196:{},4197:{},4198:{},4199:{},4200:{},0:{f:Di},1:{},2:{f:Eu},3:{f:ku},4:{f:wu},5:{f:Mi},7:{f:Su},8:{},9:{f:qa},11:{},22:{f:er},30:{f:Oh},31:{},32:{},33:{f:zi},36:{},37:{f:Pi},50:{f:_u},62:{},52:{},67:{},68:{f:er},69:{},86:{},126:{},127:{f:gu},135:{},136:{},137:{},145:{},148:{},149:{},150:{},169:{},171:{},188:{},191:{},192:{},194:{},195:{},214:{f:xu},223:{},234:{},354:{},421:{},518:{f:Un},521:{f:qa},536:{f:Wi},547:{f:Bi},561:{},579:{},1030:{f:Un},1033:{f:qa},1091:{},2157:{},2163:{},2177:{},2240:{},2241:{},2242:{},2243:{},2244:{},2245:{},2246:{},2247:{},2248:{},2249:{},2250:{},2251:{},2262:{r:12},29282:{}};function ae(e,t,r,a){var n=t;if(!isNaN(n)){var s=a||(r||[]).length||0,i=e.next(4);i.write_shift(2,n),i.write_shift(2,s),s>0&&ws(r)&&e.push(r)}}function Sv(e,t,r,a){var n=(r||[]).length||0;if(n<=8224)return ae(e,t,r,n);var s=t;if(!isNaN(s)){for(var i=r.parts||[],o=0,c=0,l=0;l+(i[o]||8224)<=8224;)l+=i[o]||8224,o++;var f=e.next(4);for(f.write_shift(2,s),f.write_shift(2,l),e.push(r.slice(c,c+l)),c+=l;c<n;){for(f=e.next(4),f.write_shift(2,60),l=0;l+(i[o]||8224)<=8224;)l+=i[o]||8224,o++;f.write_shift(2,l),e.push(r.slice(c,c+l)),c+=l}}}function ja(e,t,r){return e||(e=j(7)),e.write_shift(2,t),e.write_shift(2,r),e.write_shift(2,0),e.write_shift(1,0),e}function _v(e,t,r,a){var n=j(9);return ja(n,e,t),lc(r,a||"b",n),n}function xv(e,t,r){var a=j(8+2*r.length);return ja(a,e,t),a.write_shift(1,r.length),a.write_shift(r.length,r,"sbcs"),a.l<a.length?a.slice(0,a.l):a}function Av(e,t,r,a){if(t.v!=null)switch(t.t){case"d":case"n":var n=t.t=="d"?nr(We(t.v)):t.v;n==(n|0)&&n>=0&&n<65536?ae(e,2,yu(r,a,n)):ae(e,3,Tu(r,a,n));return;case"b":case"e":ae(e,5,_v(r,a,t.v,t.t));return;case"s":case"str":ae(e,4,xv(r,a,(t.v||"").slice(0,255)));return}ae(e,1,ja(null,r,a))}function Cv(e,t,r,a){var n=Array.isArray(t),s=Re(t["!ref"]||"A1"),i,o="",c=[];if(s.e.c>255||s.e.r>16383){if(a.WTF)throw new Error("Range "+(t["!ref"]||"A1")+" exceeds format limit A1:IV16384");s.e.c=Math.min(s.e.c,255),s.e.r=Math.min(s.e.c,16383),i=we(s)}for(var l=s.s.r;l<=s.e.r;++l){o=Je(l);for(var f=s.s.c;f<=s.e.c;++f){l===s.s.r&&(c[f]=Ve(f)),i=c[f]+o;var p=n?(t[l]||[])[f]:t[i];p&&Av(e,p,l,f)}}}function Rv(e,t){for(var r=t||{},a=_r(),n=0,s=0;s<e.SheetNames.length;++s)e.SheetNames[s]==r.sheet&&(n=s);if(n==0&&r.sheet&&e.SheetNames[0]!=r.sheet)throw new Error("Sheet not found: "+r.sheet);return ae(a,r.biff==4?1033:r.biff==3?521:9,Rs(e,16,r)),Cv(a,e.Sheets[e.SheetNames[n]],n,r),ae(a,10),a.end()}function Ov(e,t,r){ae(e,49,yh({sz:12,name:"Arial"},r))}function Nv(e,t,r){t&&[[5,8],[23,26],[41,44],[50,392]].forEach(function(a){for(var n=a[0];n<=a[1];++n)t[n]!=null&&ae(e,1054,Rh(n,t[n],r))})}function Iv(e,t){var r=j(19);r.write_shift(4,2151),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(2,3),r.write_shift(1,1),r.write_shift(4,0),ae(e,2151,r),r=j(39),r.write_shift(4,2152),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(2,3),r.write_shift(1,0),r.write_shift(4,0),r.write_shift(2,1),r.write_shift(4,4),r.write_shift(2,0),pc(Re(t["!ref"]||"A1"),r),r.write_shift(4,4),ae(e,2152,r)}function Fv(e,t){for(var r=0;r<16;++r)ae(e,224,Li({numFmtId:0,style:!0},0,t));t.cellXfs.forEach(function(a){ae(e,224,Li(a,0,t))})}function Pv(e,t){for(var r=0;r<t["!links"].length;++r){var a=t["!links"][r];ae(e,440,au(a)),a[1].Tooltip&&ae(e,2048,su(a))}delete t["!links"]}function Dv(e,t){if(t){var r=0;t.forEach(function(a,n){++r<=256&&a&&ae(e,125,hu(_n(n,a),n))})}}function Lv(e,t,r,a,n){var s=16+yt(n.cellXfs,t,n);if(t.v==null&&!t.bf){ae(e,513,Dt(r,a,s));return}if(t.bf)ae(e,6,cp(t,r,a,n,s));else switch(t.t){case"d":case"n":var i=t.t=="d"?nr(We(t.v)):t.v;ae(e,515,zh(r,a,i,s));break;case"b":case"e":ae(e,517,Bh(r,a,t.v,s,n,t.t));break;case"s":case"str":if(n.bookSST){var o=Ms(n.Strings,t.v,n.revStrings);ae(e,253,_h(r,a,o,s))}else ae(e,516,Ah(r,a,(t.v||"").slice(0,255),s,n));break;default:ae(e,513,Dt(r,a,s))}}function Mv(e,t,r){var a=_r(),n=r.SheetNames[e],s=r.Sheets[n]||{},i=(r||{}).Workbook||{},o=(i.Sheets||[])[e]||{},c=Array.isArray(s),l=t.biff==8,f,p="",h=[],d=Re(s["!ref"]||"A1"),m=l?65536:16384;if(d.e.c>255||d.e.r>=m){if(t.WTF)throw new Error("Range "+(s["!ref"]||"A1")+" exceeds format limit A1:IV16384");d.e.c=Math.min(d.e.c,255),d.e.r=Math.min(d.e.c,m-1)}ae(a,2057,Rs(r,16,t)),ae(a,13,Br(1)),ae(a,12,Br(100)),ae(a,15,Er(!0)),ae(a,17,Er(!1)),ae(a,16,Ft(.001)),ae(a,95,Er(!0)),ae(a,42,Er(!1)),ae(a,43,Er(!1)),ae(a,130,Br(1)),ae(a,128,Uh()),ae(a,131,Er(!1)),ae(a,132,Er(!1)),l&&Dv(a,s["!cols"]),ae(a,512,Nh(d,t)),l&&(s["!links"]=[]);for(var u=d.s.r;u<=d.e.r;++u){p=Je(u);for(var b=d.s.c;b<=d.e.c;++b){u===d.s.r&&(h[b]=Ve(b)),f=h[b]+p;var T=c?(s[u]||[])[b]:s[f];T&&(Lv(a,T,u,b,t),l&&T.l&&s["!links"].push([f,T.l]))}}var S=o.CodeName||o.name||n;return l&&ae(a,574,kh((i.Views||[])[0])),l&&(s["!merges"]||[]).length&&ae(a,229,Zh(s["!merges"])),l&&Pv(a,s),ae(a,442,fc(S)),l&&Iv(a,s),ae(a,10),a.end()}function Uv(e,t,r){var a=_r(),n=(e||{}).Workbook||{},s=n.Sheets||[],i=n.WBProps||{},o=r.biff==8,c=r.biff==5;if(ae(a,2057,Rs(e,5,r)),r.bookType=="xla"&&ae(a,135),ae(a,225,o?Br(1200):null),ae(a,193,B1(2)),c&&ae(a,191),c&&ae(a,192),ae(a,226),ae(a,92,oh("SheetJS",r)),ae(a,66,Br(o?1200:1252)),o&&ae(a,353,Br(0)),o&&ae(a,448),ae(a,317,pu(e.SheetNames.length)),o&&e.vbaraw&&ae(a,211),o&&e.vbaraw){var l=i.CodeName||"ThisWorkbook";ae(a,442,fc(l))}ae(a,156,Br(17)),ae(a,25,Er(!1)),ae(a,18,Er(!1)),ae(a,19,Br(0)),o&&ae(a,431,Er(!1)),o&&ae(a,444,Br(0)),ae(a,61,gh()),ae(a,64,Er(!1)),ae(a,141,Br(0)),ae(a,34,Er(bb(e)=="true")),ae(a,14,Er(!0)),o&&ae(a,439,Er(!1)),ae(a,218,Br(0)),Ov(a,e,r),Nv(a,e.SSF,r),Fv(a,r),o&&ae(a,352,Er(!1));var f=a.end(),p=_r();o&&ae(p,140,ou()),o&&r.Strings&&Sv(p,252,uh(r.Strings)),ae(p,10);var h=p.end(),d=_r(),m=0,u=0;for(u=0;u<e.SheetNames.length;++u)m+=(o?12:11)+(o?2:1)*e.SheetNames[u].length;var b=f.length+m+h.length;for(u=0;u<e.SheetNames.length;++u){var T=s[u]||{};ae(d,133,fh({pos:b,hs:T.Hidden||0,dt:0,name:e.SheetNames[u]},r)),b+=t[u].length}var S=d.end();if(m!=S.length)throw new Error("BS8 "+m+" != "+S.length);var g=[];return f.length&&g.push(f),S.length&&g.push(S),h.length&&g.push(h),ir(g)}function Bv(e,t){var r=t||{},a=[];e&&!e.SSF&&(e.SSF=ze(be)),e&&e.SSF&&(sa(),gn(e.SSF),r.revssf=kn(e.SSF),r.revssf[e.SSF[65535]]=0,r.ssf=e.SSF),r.Strings=[],r.Strings.Count=0,r.Strings.Unique=0,Hs(r),r.cellXfs=[],yt(r.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={});for(var n=0;n<e.SheetNames.length;++n)a[a.length]=Mv(n,r,e);return a.unshift(Uv(e,a,r)),ir(a)}function al(e,t){for(var r=0;r<=e.SheetNames.length;++r){var a=e.Sheets[e.SheetNames[r]];if(!(!a||!a["!ref"])){var n=xr(a["!ref"]);n.e.c>255&&typeof console<"u"&&console.error&&console.error("Worksheet '"+e.SheetNames[r]+"' extends beyond column IV (255).  Data may be lost.")}}var s=t||{};switch(s.biff||2){case 8:case 5:return Bv(e,t);case 4:case 3:case 2:return Rv(e,t)}throw new Error("invalid type "+s.bookType+" for BIFF")}function Zi(e,t){var r=t||{},a=r.dense?[]:{};e=e.replace(/<!--.*?-->/g,"");var n=e.match(/<table/i);if(!n)throw new Error("Invalid HTML: could not find <table>");var s=e.match(/<\/table/i),i=n.index,o=s&&s.index||e.length,c=yf(e.slice(i,o),/(:?<tr[^>]*>)/i,"<tr>"),l=-1,f=0,p=0,h=0,d={s:{r:1e7,c:1e7},e:{r:0,c:0}},m=[];for(i=0;i<c.length;++i){var u=c[i].trim(),b=u.slice(0,3).toLowerCase();if(b=="<tr"){if(++l,r.sheetRows&&r.sheetRows<=l){--l;break}f=0;continue}if(!(b!="<td"&&b!="<th")){var T=u.split(/<\/t[dh]>/i);for(o=0;o<T.length;++o){var S=T[o].trim();if(S.match(/<t[dh]/i)){for(var g=S,x=0;g.charAt(0)=="<"&&(x=g.indexOf(">"))>-1;)g=g.slice(x+1);for(var A=0;A<m.length;++A){var P=m[A];P.s.c==f&&P.s.r<l&&l<=P.e.r&&(f=P.e.c+1,A=-1)}var R=ge(S.slice(0,S.indexOf(">")));h=R.colspan?+R.colspan:1,((p=+R.rowspan)>1||h>1)&&m.push({s:{r:l,c:f},e:{r:l+(p||1)-1,c:f+h-1}});var U=R.t||R["data-t"]||"";if(!g.length){f+=h;continue}if(g=Ao(g),d.s.r>l&&(d.s.r=l),d.e.r<l&&(d.e.r=l),d.s.c>f&&(d.s.c=f),d.e.c<f&&(d.e.c=f),!g.length){f+=h;continue}var I={t:"s",v:g};r.raw||!g.trim().length||U=="s"||(g==="TRUE"?I={t:"b",v:!0}:g==="FALSE"?I={t:"b",v:!1}:isNaN(Xr(g))?isNaN(ea(g).getDate())||(I={t:"d",v:We(g)},r.cellDates||(I={t:"n",v:nr(I.v)}),I.z=r.dateNF||be[14]):I={t:"n",v:Xr(g)}),r.dense?(a[l]||(a[l]=[]),a[l][f]=I):a[ve({r:l,c:f})]=I,f+=h}}}}return a["!ref"]=we(d),m.length&&(a["!merges"]=m),a}function Wv(e,t,r,a){for(var n=e["!merges"]||[],s=[],i=t.s.c;i<=t.e.c;++i){for(var o=0,c=0,l=0;l<n.length;++l)if(!(n[l].s.r>r||n[l].s.c>i)&&!(n[l].e.r<r||n[l].e.c<i)){if(n[l].s.r<r||n[l].s.c<i){o=-1;break}o=n[l].e.r-n[l].s.r+1,c=n[l].e.c-n[l].s.c+1;break}if(!(o<0)){var f=ve({r,c:i}),p=a.dense?(e[r]||[])[i]:e[f],h=p&&p.v!=null&&(p.h||bs(p.w||(ct(p),p.w)||""))||"",d={};o>1&&(d.rowspan=o),c>1&&(d.colspan=c),a.editable?h='<span contenteditable="true">'+h+"</span>":p&&(d["data-t"]=p&&p.t||"z",p.v!=null&&(d["data-v"]=p.v),p.z!=null&&(d["data-z"]=p.z),p.l&&(p.l.Target||"#").charAt(0)!="#"&&(h='<a href="'+p.l.Target+'">'+h+"</a>")),d.id=(a.id||"sjs")+"-"+f,s.push(re("td",h,d))}}var m="<tr>";return m+s.join("")+"</tr>"}var zv='<html><head><meta charset="utf-8"/><title>SheetJS Table Export</title></head><body>',Hv="</body></html>";function Gv(e,t){var r=e.match(/<table[\s\S]*?>[\s\S]*?<\/table>/gi);if(!r||r.length==0)throw new Error("Invalid HTML: could not find <table>");if(r.length==1)return Et(Zi(r[0],t),t);var a=js();return r.forEach(function(n,s){$s(a,Zi(n,t),"Sheet"+(s+1))}),a}function Vv(e,t,r){var a=[];return a.join("")+"<table"+(r&&r.id?' id="'+r.id+'"':"")+">"}function nl(e,t){var r=t||{},a=r.header!=null?r.header:zv,n=r.footer!=null?r.footer:Hv,s=[a],i=xr(e["!ref"]);r.dense=Array.isArray(e),s.push(Vv(e,i,r));for(var o=i.s.r;o<=i.e.r;++o)s.push(Wv(e,i,o,r));return s.push("</table>"+n),s.join("")}function sl(e,t,r){var a=r||{},n=0,s=0;if(a.origin!=null)if(typeof a.origin=="number")n=a.origin;else{var i=typeof a.origin=="string"?je(a.origin):a.origin;n=i.r,s=i.c}var o=t.getElementsByTagName("tr"),c=Math.min(a.sheetRows||1e7,o.length),l={s:{r:0,c:0},e:{r:n,c:s}};if(e["!ref"]){var f=xr(e["!ref"]);l.s.r=Math.min(l.s.r,f.s.r),l.s.c=Math.min(l.s.c,f.s.c),l.e.r=Math.max(l.e.r,f.e.r),l.e.c=Math.max(l.e.c,f.e.c),n==-1&&(l.e.r=n=f.e.r+1)}var p=[],h=0,d=e["!rows"]||(e["!rows"]=[]),m=0,u=0,b=0,T=0,S=0,g=0;for(e["!cols"]||(e["!cols"]=[]);m<o.length&&u<c;++m){var x=o[m];if(qi(x)){if(a.display)continue;d[u]={hidden:!0}}var A=x.children;for(b=T=0;b<A.length;++b){var P=A[b];if(!(a.display&&qi(P))){var R=P.hasAttribute("data-v")?P.getAttribute("data-v"):P.hasAttribute("v")?P.getAttribute("v"):Ao(P.innerHTML),U=P.getAttribute("data-z")||P.getAttribute("z");for(h=0;h<p.length;++h){var I=p[h];I.s.c==T+s&&I.s.r<u+n&&u+n<=I.e.r&&(T=I.e.c+1-s,h=-1)}g=+P.getAttribute("colspan")||1,((S=+P.getAttribute("rowspan")||1)>1||g>1)&&p.push({s:{r:u+n,c:T+s},e:{r:u+n+(S||1)-1,c:T+s+(g||1)-1}});var G={t:"s",v:R},W=P.getAttribute("data-t")||P.getAttribute("t")||"";R!=null&&(R.length==0?G.t=W||"z":a.raw||R.trim().length==0||W=="s"||(R==="TRUE"?G={t:"b",v:!0}:R==="FALSE"?G={t:"b",v:!1}:isNaN(Xr(R))?isNaN(ea(R).getDate())||(G={t:"d",v:We(R)},a.cellDates||(G={t:"n",v:nr(G.v)}),G.z=a.dateNF||be[14]):G={t:"n",v:Xr(R)})),G.z===void 0&&U!=null&&(G.z=U);var D="",te=P.getElementsByTagName("A");if(te&&te.length)for(var fe=0;fe<te.length&&!(te[fe].hasAttribute("href")&&(D=te[fe].getAttribute("href"),D.charAt(0)!="#"));++fe);D&&D.charAt(0)!="#"&&(G.l={Target:D}),a.dense?(e[u+n]||(e[u+n]=[]),e[u+n][T+s]=G):e[ve({c:T+s,r:u+n})]=G,l.e.c<T+s&&(l.e.c=T+s),T+=g}}++u}return p.length&&(e["!merges"]=(e["!merges"]||[]).concat(p)),l.e.r=Math.max(l.e.r,u-1+n),e["!ref"]=we(l),u>=c&&(e["!fullref"]=we((l.e.r=o.length-m+u-1+n,l))),e}function il(e,t){var r=t||{},a=r.dense?[]:{};return sl(a,e,t)}function jv(e,t){return Et(il(e,t),t)}function qi(e){var t="",r=$v(e);return r&&(t=r(e).getPropertyValue("display")),t||(t=e.style&&e.style.display),t==="none"}function $v(e){return e.ownerDocument.defaultView&&typeof e.ownerDocument.defaultView.getComputedStyle=="function"?e.ownerDocument.defaultView.getComputedStyle:typeof getComputedStyle=="function"?getComputedStyle:null}function Xv(e){var t=e.replace(/[\t\r\n]/g," ").trim().replace(/ +/g," ").replace(/<text:s\/>/g," ").replace(/<text:s text:c="(\d+)"\/>/g,function(a,n){return Array(parseInt(n,10)+1).join(" ")}).replace(/<text:tab[^>]*\/>/g,"	").replace(/<text:line-break\/>/g,`
`),r=Ce(t.replace(/<[^>]*>/g,""));return[r]}var Qi={day:["d","dd"],month:["m","mm"],year:["y","yy"],hours:["h","hh"],minutes:["m","mm"],seconds:["s","ss"],"am-pm":["A/P","AM/PM"],"day-of-week":["ddd","dddd"],era:["e","ee"],quarter:["\\Qm",'m\\"th quarter"']};function ol(e,t){var r=t||{},a=vs(e),n=[],s,i,o={name:""},c="",l=0,f,p,h={},d=[],m=r.dense?[]:{},u,b,T={value:""},S="",g=0,x=[],A=-1,P=-1,R={s:{r:1e6,c:1e7},e:{r:0,c:0}},U=0,I={},G=[],W={},D=0,te=0,fe=[],se=1,de=1,ue=[],K={Names:[]},ee={},me=["",""],Te=[],C={},L="",F=0,N=!1,X=!1,ne=0;for(Ra.lastIndex=0,a=a.replace(/<!--([\s\S]*?)-->/mg,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"");u=Ra.exec(a);)switch(u[3]=u[3].replace(/_.*$/,"")){case"table":case"工作表":u[1]==="/"?(R.e.c>=R.s.c&&R.e.r>=R.s.r?m["!ref"]=we(R):m["!ref"]="A1:A1",r.sheetRows>0&&r.sheetRows<=R.e.r&&(m["!fullref"]=m["!ref"],R.e.r=r.sheetRows-1,m["!ref"]=we(R)),G.length&&(m["!merges"]=G),fe.length&&(m["!rows"]=fe),f.name=f.名称||f.name,typeof JSON<"u"&&JSON.stringify(f),d.push(f.name),h[f.name]=m,X=!1):u[0].charAt(u[0].length-2)!=="/"&&(f=ge(u[0],!1),A=P=-1,R.s.r=R.s.c=1e7,R.e.r=R.e.c=0,m=r.dense?[]:{},G=[],fe=[],X=!0);break;case"table-row-group":u[1]==="/"?--U:++U;break;case"table-row":case"行":if(u[1]==="/"){A+=se,se=1;break}if(p=ge(u[0],!1),p.行号?A=p.行号-1:A==-1&&(A=0),se=+p["number-rows-repeated"]||1,se<10)for(ne=0;ne<se;++ne)U>0&&(fe[A+ne]={level:U});P=-1;break;case"covered-table-cell":u[1]!=="/"&&++P,r.sheetStubs&&(r.dense?(m[A]||(m[A]=[]),m[A][P]={t:"z"}):m[ve({r:A,c:P})]={t:"z"}),S="",x=[];break;case"table-cell":case"数据":if(u[0].charAt(u[0].length-2)==="/")++P,T=ge(u[0],!1),de=parseInt(T["number-columns-repeated"]||"1",10),b={t:"z",v:null},T.formula&&r.cellFormula!=!1&&(b.f=Yi(Ce(T.formula))),(T.数据类型||T["value-type"])=="string"&&(b.t="s",b.v=Ce(T["string-value"]||""),r.dense?(m[A]||(m[A]=[]),m[A][P]=b):m[ve({r:A,c:P})]=b),P+=de-1;else if(u[1]!=="/"){++P,S="",g=0,x=[],de=1;var J=se?A+se-1:A;if(P>R.e.c&&(R.e.c=P),P<R.s.c&&(R.s.c=P),A<R.s.r&&(R.s.r=A),J>R.e.r&&(R.e.r=J),T=ge(u[0],!1),Te=[],C={},b={t:T.数据类型||T["value-type"],v:null},r.cellFormula)if(T.formula&&(T.formula=Ce(T.formula)),T["number-matrix-columns-spanned"]&&T["number-matrix-rows-spanned"]&&(D=parseInt(T["number-matrix-rows-spanned"],10)||0,te=parseInt(T["number-matrix-columns-spanned"],10)||0,W={s:{r:A,c:P},e:{r:A+D-1,c:P+te-1}},b.F=we(W),ue.push([W,b.F])),T.formula)b.f=Yi(T.formula);else for(ne=0;ne<ue.length;++ne)A>=ue[ne][0].s.r&&A<=ue[ne][0].e.r&&P>=ue[ne][0].s.c&&P<=ue[ne][0].e.c&&(b.F=ue[ne][1]);switch((T["number-columns-spanned"]||T["number-rows-spanned"])&&(D=parseInt(T["number-rows-spanned"],10)||0,te=parseInt(T["number-columns-spanned"],10)||0,W={s:{r:A,c:P},e:{r:A+D-1,c:P+te-1}},G.push(W)),T["number-columns-repeated"]&&(de=parseInt(T["number-columns-repeated"],10)),b.t){case"boolean":b.t="b",b.v=Be(T["boolean-value"]);break;case"float":b.t="n",b.v=parseFloat(T.value);break;case"percentage":b.t="n",b.v=parseFloat(T.value);break;case"currency":b.t="n",b.v=parseFloat(T.value);break;case"date":b.t="d",b.v=We(T["date-value"]),r.cellDates||(b.t="n",b.v=nr(b.v)),b.z="m/d/yy";break;case"time":b.t="n",b.v=kf(T["time-value"])/86400,r.cellDates&&(b.t="d",b.v=Tn(b.v)),b.z="HH:MM:SS";break;case"number":b.t="n",b.v=parseFloat(T.数据数值);break;default:if(b.t==="string"||b.t==="text"||!b.t)b.t="s",T["string-value"]!=null&&(S=Ce(T["string-value"]),x=[]);else throw new Error("Unsupported value type "+b.t)}}else{if(N=!1,b.t==="s"&&(b.v=S||"",x.length&&(b.R=x),N=g==0),ee.Target&&(b.l=ee),Te.length>0&&(b.c=Te,Te=[]),S&&r.cellText!==!1&&(b.w=S),N&&(b.t="z",delete b.v),(!N||r.sheetStubs)&&!(r.sheetRows&&r.sheetRows<=A))for(var Q=0;Q<se;++Q){if(de=parseInt(T["number-columns-repeated"]||"1",10),r.dense)for(m[A+Q]||(m[A+Q]=[]),m[A+Q][P]=Q==0?b:ze(b);--de>0;)m[A+Q][P+de]=ze(b);else for(m[ve({r:A+Q,c:P})]=b;--de>0;)m[ve({r:A+Q,c:P+de})]=ze(b);R.e.c<=P&&(R.e.c=P)}de=parseInt(T["number-columns-repeated"]||"1",10),P+=de-1,de=0,b={},S="",x=[]}ee={};break;case"document":case"document-content":case"电子表格文档":case"spreadsheet":case"主体":case"scripts":case"styles":case"font-face-decls":case"master-styles":if(u[1]==="/"){if((s=n.pop())[0]!==u[3])throw"Bad state: "+s}else u[0].charAt(u[0].length-2)!=="/"&&n.push([u[3],!0]);break;case"annotation":if(u[1]==="/"){if((s=n.pop())[0]!==u[3])throw"Bad state: "+s;C.t=S,x.length&&(C.R=x),C.a=L,Te.push(C)}else u[0].charAt(u[0].length-2)!=="/"&&n.push([u[3],!1]);L="",F=0,S="",g=0,x=[];break;case"creator":u[1]==="/"?L=a.slice(F,u.index):F=u.index+u[0].length;break;case"meta":case"元数据":case"settings":case"config-item-set":case"config-item-map-indexed":case"config-item-map-entry":case"config-item-map-named":case"shapes":case"frame":case"text-box":case"image":case"data-pilot-tables":case"list-style":case"form":case"dde-links":case"event-listeners":case"chart":if(u[1]==="/"){if((s=n.pop())[0]!==u[3])throw"Bad state: "+s}else u[0].charAt(u[0].length-2)!=="/"&&n.push([u[3],!1]);S="",g=0,x=[];break;case"scientific-number":break;case"currency-symbol":break;case"currency-style":break;case"number-style":case"percentage-style":case"date-style":case"time-style":if(u[1]==="/"){if(I[o.name]=c,(s=n.pop())[0]!==u[3])throw"Bad state: "+s}else u[0].charAt(u[0].length-2)!=="/"&&(c="",o=ge(u[0],!1),n.push([u[3],!0]));break;case"script":break;case"libraries":break;case"automatic-styles":break;case"default-style":case"page-layout":break;case"style":break;case"map":break;case"font-face":break;case"paragraph-properties":break;case"table-properties":break;case"table-column-properties":break;case"table-row-properties":break;case"table-cell-properties":break;case"number":switch(n[n.length-1][0]){case"time-style":case"date-style":i=ge(u[0],!1),c+=Qi[u[3]][i.style==="long"?1:0];break}break;case"fraction":break;case"day":case"month":case"year":case"era":case"day-of-week":case"week-of-year":case"quarter":case"hours":case"minutes":case"seconds":case"am-pm":switch(n[n.length-1][0]){case"time-style":case"date-style":i=ge(u[0],!1),c+=Qi[u[3]][i.style==="long"?1:0];break}break;case"boolean-style":break;case"boolean":break;case"text-style":break;case"text":if(u[0].slice(-2)==="/>")break;if(u[1]==="/")switch(n[n.length-1][0]){case"number-style":case"date-style":case"time-style":c+=a.slice(l,u.index);break}else l=u.index+u[0].length;break;case"named-range":i=ge(u[0],!1),me=Bn(i["cell-range-address"]);var q={Name:i.name,Ref:me[0]+"!"+me[1]};X&&(q.Sheet=d.length),K.Names.push(q);break;case"text-content":break;case"text-properties":break;case"embedded-text":break;case"body":case"电子表格":break;case"forms":break;case"table-column":break;case"table-header-rows":break;case"table-rows":break;case"table-column-group":break;case"table-header-columns":break;case"table-columns":break;case"null-date":break;case"graphic-properties":break;case"calculation-settings":break;case"named-expressions":break;case"label-range":break;case"label-ranges":break;case"named-expression":break;case"sort":break;case"sort-by":break;case"sort-groups":break;case"tab":break;case"line-break":break;case"span":break;case"p":case"文本串":if(["master-styles"].indexOf(n[n.length-1][0])>-1)break;if(u[1]==="/"&&(!T||!T["string-value"])){var Ee=Xv(a.slice(g,u.index));S=(S.length>0?S+`
`:"")+Ee[0]}else ge(u[0],!1),g=u.index+u[0].length;break;case"s":break;case"database-range":if(u[1]==="/")break;try{me=Bn(ge(u[0])["target-range-address"]),h[me[0]]["!autofilter"]={ref:me[1]}}catch{}break;case"date":break;case"object":break;case"title":case"标题":break;case"desc":break;case"binary-data":break;case"table-source":break;case"scenario":break;case"iteration":break;case"content-validations":break;case"content-validation":break;case"help-message":break;case"error-message":break;case"database-ranges":break;case"filter":break;case"filter-and":break;case"filter-or":break;case"filter-condition":break;case"list-level-style-bullet":break;case"list-level-style-number":break;case"list-level-properties":break;case"sender-firstname":case"sender-lastname":case"sender-initials":case"sender-title":case"sender-position":case"sender-email":case"sender-phone-private":case"sender-fax":case"sender-company":case"sender-phone-work":case"sender-street":case"sender-city":case"sender-postal-code":case"sender-country":case"sender-state-or-province":case"author-name":case"author-initials":case"chapter":case"file-name":case"template-name":case"sheet-name":break;case"event-listener":break;case"initial-creator":case"creation-date":case"print-date":case"generator":case"document-statistic":case"user-defined":case"editing-duration":case"editing-cycles":break;case"config-item":break;case"page-number":break;case"page-count":break;case"time":break;case"cell-range-source":break;case"detective":break;case"operation":break;case"highlighted-range":break;case"data-pilot-table":case"source-cell-range":case"source-service":case"data-pilot-field":case"data-pilot-level":case"data-pilot-subtotals":case"data-pilot-subtotal":case"data-pilot-members":case"data-pilot-member":case"data-pilot-display-info":case"data-pilot-sort-info":case"data-pilot-layout-info":case"data-pilot-field-reference":case"data-pilot-groups":case"data-pilot-group":case"data-pilot-group-member":break;case"rect":break;case"dde-connection-decls":case"dde-connection-decl":case"dde-link":case"dde-source":break;case"properties":break;case"property":break;case"a":if(u[1]!=="/"){if(ee=ge(u[0],!1),!ee.href)break;ee.Target=Ce(ee.href),delete ee.href,ee.Target.charAt(0)=="#"&&ee.Target.indexOf(".")>-1?(me=Bn(ee.Target.slice(1)),ee.Target="#"+me[0]+"!"+me[1]):ee.Target.match(/^\.\.[\\\/]/)&&(ee.Target=ee.Target.slice(3))}break;case"table-protection":break;case"data-pilot-grand-total":break;case"office-document-common-attrs":break;default:switch(u[2]){case"dc:":case"calcext:":case"loext:":case"ooo:":case"chartooo:":case"draw:":case"style:":case"chart:":case"form:":case"uof:":case"表:":case"字:":break;default:if(r.WTF)throw new Error(u)}}var O={Sheets:h,SheetNames:d,Workbook:K};return r.bookSheets&&delete O.Sheets,O}function Yv(e,t){t=t||{},Ur(e,"META-INF/manifest.xml")&&k1(Qe(e,"META-INF/manifest.xml"),t);var r=Nr(e,"content.xml");if(!r)throw new Error("Missing content.xml in ODS / UOF file");var a=ol(Le(r),t);return Ur(e,"meta.xml")&&(a.Props=Zo(Qe(e,"meta.xml"))),a}function Kv(e,t){return ol(e,t)}var Jv=function(){var e=["<office:master-styles>",'<style:master-page style:name="mp1" style:page-layout-name="mp1">',"<style:header/>",'<style:header-left style:display="false"/>',"<style:footer/>",'<style:footer-left style:display="false"/>',"</style:master-page>","</office:master-styles>"].join(""),t="<office:document-styles "+Ca({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","office:version":"1.2"})+">"+e+"</office:document-styles>";return function(){return Ze+t}}(),eo=function(){var e=function(s){return Pe(s).replace(/  +/g,function(i){return'<text:s text:c="'+i.length+'"/>'}).replace(/\t/g,"<text:tab/>").replace(/\n/g,"</text:p><text:p>").replace(/^ /,"<text:s/>").replace(/ $/,"<text:s/>")},t=`          <table:table-cell />
`,r=`          <table:covered-table-cell/>
`,a=function(s,i,o){var c=[];c.push('      <table:table table:name="'+Pe(i.SheetNames[o])+`" table:style-name="ta1">
`);var l=0,f=0,p=xr(s["!ref"]||"A1"),h=s["!merges"]||[],d=0,m=Array.isArray(s);if(s["!cols"])for(f=0;f<=p.e.c;++f)c.push("        <table:table-column"+(s["!cols"][f]?' table:style-name="co'+s["!cols"][f].ods+'"':"")+`></table:table-column>
`);var u="",b=s["!rows"]||[];for(l=0;l<p.s.r;++l)u=b[l]?' table:style-name="ro'+b[l].ods+'"':"",c.push("        <table:table-row"+u+`></table:table-row>
`);for(;l<=p.e.r;++l){for(u=b[l]?' table:style-name="ro'+b[l].ods+'"':"",c.push("        <table:table-row"+u+`>
`),f=0;f<p.s.c;++f)c.push(t);for(;f<=p.e.c;++f){var T=!1,S={},g="";for(d=0;d!=h.length;++d)if(!(h[d].s.c>f)&&!(h[d].s.r>l)&&!(h[d].e.c<f)&&!(h[d].e.r<l)){(h[d].s.c!=f||h[d].s.r!=l)&&(T=!0),S["table:number-columns-spanned"]=h[d].e.c-h[d].s.c+1,S["table:number-rows-spanned"]=h[d].e.r-h[d].s.r+1;break}if(T){c.push(r);continue}var x=ve({r:l,c:f}),A=m?(s[l]||[])[f]:s[x];if(A&&A.f&&(S["table:formula"]=Pe(pp(A.f)),A.F&&A.F.slice(0,x.length)==x)){var P=xr(A.F);S["table:number-matrix-columns-spanned"]=P.e.c-P.s.c+1,S["table:number-matrix-rows-spanned"]=P.e.r-P.s.r+1}if(!A){c.push(t);continue}switch(A.t){case"b":g=A.v?"TRUE":"FALSE",S["office:value-type"]="boolean",S["office:boolean-value"]=A.v?"true":"false";break;case"n":g=A.w||String(A.v||0),S["office:value-type"]="float",S["office:value"]=A.v||0;break;case"s":case"str":g=A.v==null?"":A.v,S["office:value-type"]="string";break;case"d":g=A.w||We(A.v).toISOString(),S["office:value-type"]="date",S["office:date-value"]=We(A.v).toISOString(),S["table:style-name"]="ce1";break;default:c.push(t);continue}var R=e(g);if(A.l&&A.l.Target){var U=A.l.Target;U=U.charAt(0)=="#"?"#"+mp(U.slice(1)):U,U.charAt(0)!="#"&&!U.match(/^\w+:/)&&(U="../"+U),R=re("text:a",R,{"xlink:href":U.replace(/&/g,"&amp;")})}c.push("          "+re("table:table-cell",re("text:p",R,{}),S)+`
`)}c.push(`        </table:table-row>
`)}return c.push(`      </table:table>
`),c.join("")},n=function(s,i){s.push(` <office:automatic-styles>
`),s.push(`  <number:date-style style:name="N37" number:automatic-order="true">
`),s.push(`   <number:month number:style="long"/>
`),s.push(`   <number:text>/</number:text>
`),s.push(`   <number:day number:style="long"/>
`),s.push(`   <number:text>/</number:text>
`),s.push(`   <number:year/>
`),s.push(`  </number:date-style>
`);var o=0;i.SheetNames.map(function(l){return i.Sheets[l]}).forEach(function(l){if(l&&l["!cols"]){for(var f=0;f<l["!cols"].length;++f)if(l["!cols"][f]){var p=l["!cols"][f];if(p.width==null&&p.wpx==null&&p.wch==null)continue;gt(p),p.ods=o;var h=l["!cols"][f].wpx+"px";s.push('  <style:style style:name="co'+o+`" style:family="table-column">
`),s.push('   <style:table-column-properties fo:break-before="auto" style:column-width="'+h+`"/>
`),s.push(`  </style:style>
`),++o}}});var c=0;i.SheetNames.map(function(l){return i.Sheets[l]}).forEach(function(l){if(l&&l["!rows"]){for(var f=0;f<l["!rows"].length;++f)if(l["!rows"][f]){l["!rows"][f].ods=c;var p=l["!rows"][f].hpx+"px";s.push('  <style:style style:name="ro'+c+`" style:family="table-row">
`),s.push('   <style:table-row-properties fo:break-before="auto" style:row-height="'+p+`"/>
`),s.push(`  </style:style>
`),++c}}}),s.push(`  <style:style style:name="ta1" style:family="table" style:master-page-name="mp1">
`),s.push(`   <style:table-properties table:display="true" style:writing-mode="lr-tb"/>
`),s.push(`  </style:style>
`),s.push(`  <style:style style:name="ce1" style:family="table-cell" style:parent-style-name="Default" style:data-style-name="N37"/>
`),s.push(` </office:automatic-styles>
`)};return function(s,i){var o=[Ze],c=Ca({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:meta":"urn:oasis:names:tc:opendocument:xmlns:meta:1.0","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:presentation":"urn:oasis:names:tc:opendocument:xmlns:presentation:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:chart":"urn:oasis:names:tc:opendocument:xmlns:chart:1.0","xmlns:dr3d":"urn:oasis:names:tc:opendocument:xmlns:dr3d:1.0","xmlns:math":"http://www.w3.org/1998/Math/MathML","xmlns:form":"urn:oasis:names:tc:opendocument:xmlns:form:1.0","xmlns:script":"urn:oasis:names:tc:opendocument:xmlns:script:1.0","xmlns:ooo":"http://openoffice.org/2004/office","xmlns:ooow":"http://openoffice.org/2004/writer","xmlns:oooc":"http://openoffice.org/2004/calc","xmlns:dom":"http://www.w3.org/2001/xml-events","xmlns:xforms":"http://www.w3.org/2002/xforms","xmlns:xsd":"http://www.w3.org/2001/XMLSchema","xmlns:xsi":"http://www.w3.org/2001/XMLSchema-instance","xmlns:sheet":"urn:oasis:names:tc:opendocument:sh33tjs:1.0","xmlns:rpt":"http://openoffice.org/2005/report","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","xmlns:xhtml":"http://www.w3.org/1999/xhtml","xmlns:grddl":"http://www.w3.org/2003/g/data-view#","xmlns:tableooo":"http://openoffice.org/2009/table","xmlns:drawooo":"http://openoffice.org/2010/draw","xmlns:calcext":"urn:org:documentfoundation:names:experimental:calc:xmlns:calcext:1.0","xmlns:loext":"urn:org:documentfoundation:names:experimental:office:xmlns:loext:1.0","xmlns:field":"urn:openoffice:names:experimental:ooo-ms-interop:xmlns:field:1.0","xmlns:formx":"urn:openoffice:names:experimental:ooxml-odf-interop:xmlns:form:1.0","xmlns:css3t":"http://www.w3.org/TR/css3-text/","office:version":"1.2"}),l=Ca({"xmlns:config":"urn:oasis:names:tc:opendocument:xmlns:config:1.0","office:mimetype":"application/vnd.oasis.opendocument.spreadsheet"});i.bookType=="fods"?(o.push("<office:document"+c+l+`>
`),o.push(Jo().replace(/office:document-meta/g,"office:meta"))):o.push("<office:document-content"+c+`>
`),n(o,s),o.push(`  <office:body>
`),o.push(`    <office:spreadsheet>
`);for(var f=0;f!=s.SheetNames.length;++f)o.push(a(s.Sheets[s.SheetNames[f]],s,f));return o.push(`    </office:spreadsheet>
`),o.push(`  </office:body>
`),i.bookType=="fods"?o.push("</office:document>"):o.push("</office:document-content>"),o.join("")}}();function cl(e,t){if(t.bookType=="fods")return eo(e,t);var r=ds(),a="",n=[],s=[];return a="mimetype",ke(r,a,"application/vnd.oasis.opendocument.spreadsheet"),a="content.xml",ke(r,a,eo(e,t)),n.push([a,"text/xml"]),s.push([a,"ContentFile"]),a="styles.xml",ke(r,a,Jv(e,t)),n.push([a,"text/xml"]),s.push([a,"StylesFile"]),a="meta.xml",ke(r,a,Ze+Jo()),n.push([a,"text/xml"]),s.push([a,"MetadataFile"]),a="manifest.rdf",ke(r,a,y1(s)),n.push([a,"application/rdf+xml"]),a="META-INF/manifest.xml",ke(r,a,T1(n)),r}/*! sheetjs (C) 2013-present SheetJS -- http://sheetjs.com */function Lt(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function ts(e){return typeof TextDecoder<"u"?new TextDecoder().decode(e):Le(Tt(e))}function Zv(e){return typeof TextEncoder<"u"?new TextEncoder().encode(e):Or(qr(e))}function qv(e,t){e:for(var r=0;r<=e.length-t.length;++r){for(var a=0;a<t.length;++a)if(e[r+a]!=t[a])continue e;return!0}return!1}function kt(e){var t=e.reduce(function(n,s){return n+s.length},0),r=new Uint8Array(t),a=0;return e.forEach(function(n){r.set(n,a),a+=n.length}),r}function ro(e){return e-=e>>1&1431655765,e=(e&858993459)+(e>>2&858993459),(e+(e>>4)&252645135)*16843009>>>24}function Qv(e,t){for(var r=(e[t+15]&127)<<7|e[t+14]>>1,a=e[t+14]&1,n=t+13;n>=t;--n)a=a*256+e[n];return(e[t+15]&128?-a:a)*Math.pow(10,r-6176)}function eg(e,t,r){var a=Math.floor(r==0?0:Math.LOG10E*Math.log(Math.abs(r)))+6176-20,n=r/Math.pow(10,a-6176);e[t+15]|=a>>7,e[t+14]|=(a&127)<<1;for(var s=0;n>=1;++s,n/=256)e[t+s]=n&255;e[t+15]|=r>=0?0:128}function La(e,t){var r=t?t[0]:0,a=e[r]&127;e:if(e[r++]>=128&&(a|=(e[r]&127)<<7,e[r++]<128||(a|=(e[r]&127)<<14,e[r++]<128)||(a|=(e[r]&127)<<21,e[r++]<128)||(a+=(e[r]&127)*Math.pow(2,28),++r,e[r++]<128)||(a+=(e[r]&127)*Math.pow(2,35),++r,e[r++]<128)||(a+=(e[r]&127)*Math.pow(2,42),++r,e[r++]<128)))break e;return t&&(t[0]=r),a}function Ie(e){var t=new Uint8Array(7);t[0]=e&127;var r=1;e:if(e>127){if(t[r-1]|=128,t[r]=e>>7&127,++r,e<=16383||(t[r-1]|=128,t[r]=e>>14&127,++r,e<=2097151)||(t[r-1]|=128,t[r]=e>>21&127,++r,e<=268435455)||(t[r-1]|=128,t[r]=e/256>>>21&127,++r,e<=34359738367)||(t[r-1]|=128,t[r]=e/65536>>>21&127,++r,e<=4398046511103))break e;t[r-1]|=128,t[r]=e/16777216>>>21&127,++r}return t.slice(0,r)}function Xe(e){var t=0,r=e[t]&127;e:if(e[t++]>=128){if(r|=(e[t]&127)<<7,e[t++]<128||(r|=(e[t]&127)<<14,e[t++]<128)||(r|=(e[t]&127)<<21,e[t++]<128))break e;r|=(e[t]&127)<<28}return r}function Ne(e){for(var t=[],r=[0];r[0]<e.length;){var a=r[0],n=La(e,r),s=n&7;n=Math.floor(n/8);var i=0,o;if(n==0)break;switch(s){case 0:{for(var c=r[0];e[r[0]++]>=128;);o=e.slice(c,r[0])}break;case 5:i=4,o=e.slice(r[0],r[0]+i),r[0]+=i;break;case 1:i=8,o=e.slice(r[0],r[0]+i),r[0]+=i;break;case 2:i=La(e,r),o=e.slice(r[0],r[0]+i),r[0]+=i;break;case 3:case 4:default:throw new Error("PB Type ".concat(s," for Field ").concat(n," at offset ").concat(a))}var l={data:o,type:s};t[n]==null?t[n]=[l]:t[n].push(l)}return t}function cr(e){var t=[];return e.forEach(function(r,a){r.forEach(function(n){n.data&&(t.push(Ie(a*8+n.type)),n.type==2&&t.push(Ie(n.data.length)),t.push(n.data))})}),kt(t)}function Ws(e,t){return(e==null?void 0:e.map(function(r){return t(r.data)}))||[]}function Lr(e){for(var t,r=[],a=[0];a[0]<e.length;){var n=La(e,a),s=Ne(e.slice(a[0],a[0]+n));a[0]+=n;var i={id:Xe(s[1][0].data),messages:[]};s[2].forEach(function(o){var c=Ne(o.data),l=Xe(c[3][0].data);i.messages.push({meta:c,data:e.slice(a[0],a[0]+l)}),a[0]+=l}),(t=s[3])!=null&&t[0]&&(i.merge=Xe(s[3][0].data)>>>0>0),r.push(i)}return r}function jt(e){var t=[];return e.forEach(function(r){var a=[];a[1]=[{data:Ie(r.id),type:0}],a[2]=[],r.merge!=null&&(a[3]=[{data:Ie(+!!r.merge),type:0}]);var n=[];r.messages.forEach(function(i){n.push(i.data),i.meta[3]=[{type:0,data:Ie(i.data.length)}],a[2].push({data:cr(i.meta),type:2})});var s=cr(a);t.push(Ie(s.length)),t.push(s),n.forEach(function(i){return t.push(i)})}),kt(t)}function rg(e,t){if(e!=0)throw new Error("Unexpected Snappy chunk type ".concat(e));for(var r=[0],a=La(t,r),n=[];r[0]<t.length;){var s=t[r[0]]&3;if(s==0){var i=t[r[0]++]>>2;if(i<60)++i;else{var o=i-59;i=t[r[0]],o>1&&(i|=t[r[0]+1]<<8),o>2&&(i|=t[r[0]+2]<<16),o>3&&(i|=t[r[0]+3]<<24),i>>>=0,i++,r[0]+=o}n.push(t.slice(r[0],r[0]+i)),r[0]+=i;continue}else{var c=0,l=0;if(s==1?(l=(t[r[0]]>>2&7)+4,c=(t[r[0]++]&224)<<3,c|=t[r[0]++]):(l=(t[r[0]++]>>2)+1,s==2?(c=t[r[0]]|t[r[0]+1]<<8,r[0]+=2):(c=(t[r[0]]|t[r[0]+1]<<8|t[r[0]+2]<<16|t[r[0]+3]<<24)>>>0,r[0]+=4)),n=[kt(n)],c==0)throw new Error("Invalid offset 0");if(c>n[0].length)throw new Error("Invalid offset beyond length");if(l>=c)for(n.push(n[0].slice(-c)),l-=c;l>=n[n.length-1].length;)n.push(n[n.length-1]),l-=n[n.length-1].length;n.push(n[0].slice(-c,-c+l))}}var f=kt(n);if(f.length!=a)throw new Error("Unexpected length: ".concat(f.length," != ").concat(a));return f}function Mr(e){for(var t=[],r=0;r<e.length;){var a=e[r++],n=e[r]|e[r+1]<<8|e[r+2]<<16;r+=3,t.push(rg(a,e.slice(r,r+n))),r+=n}if(r!==e.length)throw new Error("data is not a valid framed stream!");return kt(t)}function $t(e){for(var t=[],r=0;r<e.length;){var a=Math.min(e.length-r,268435455),n=new Uint8Array(4);t.push(n);var s=Ie(a),i=s.length;t.push(s),a<=60?(i++,t.push(new Uint8Array([a-1<<2]))):a<=256?(i+=2,t.push(new Uint8Array([240,a-1&255]))):a<=65536?(i+=3,t.push(new Uint8Array([244,a-1&255,a-1>>8&255]))):a<=16777216?(i+=4,t.push(new Uint8Array([248,a-1&255,a-1>>8&255,a-1>>16&255]))):a<=4294967296&&(i+=5,t.push(new Uint8Array([252,a-1&255,a-1>>8&255,a-1>>16&255,a-1>>>24&255]))),t.push(e.slice(r,r+a)),i+=a,n[0]=0,n[1]=i&255,n[2]=i>>8&255,n[3]=i>>16&255,r+=a}return kt(t)}function tg(e,t,r,a){var n=Lt(e),s=n.getUint32(4,!0),i=(a>1?12:8)+ro(s&(a>1?3470:398))*4,o=-1,c=-1,l=NaN,f=new Date(2001,0,1);s&512&&(o=n.getUint32(i,!0),i+=4),i+=ro(s&(a>1?12288:4096))*4,s&16&&(c=n.getUint32(i,!0),i+=4),s&32&&(l=n.getFloat64(i,!0),i+=8),s&64&&(f.setTime(f.getTime()+n.getFloat64(i,!0)*1e3),i+=8);var p;switch(e[2]){case 0:break;case 2:p={t:"n",v:l};break;case 3:p={t:"s",v:t[c]};break;case 5:p={t:"d",v:f};break;case 6:p={t:"b",v:l>0};break;case 7:p={t:"n",v:l/86400};break;case 8:p={t:"e",v:0};break;case 9:if(o>-1)p={t:"s",v:r[o]};else if(c>-1)p={t:"s",v:t[c]};else if(!isNaN(l))p={t:"n",v:l};else throw new Error("Unsupported cell type ".concat(e.slice(0,4)));break;default:throw new Error("Unsupported cell type ".concat(e.slice(0,4)))}return p}function ag(e,t,r){var a=Lt(e),n=a.getUint32(8,!0),s=12,i=-1,o=-1,c=NaN,l=NaN,f=new Date(2001,0,1);n&1&&(c=Qv(e,s),s+=16),n&2&&(l=a.getFloat64(s,!0),s+=8),n&4&&(f.setTime(f.getTime()+a.getFloat64(s,!0)*1e3),s+=8),n&8&&(o=a.getUint32(s,!0),s+=4),n&16&&(i=a.getUint32(s,!0),s+=4);var p;switch(e[1]){case 0:break;case 2:p={t:"n",v:c};break;case 3:p={t:"s",v:t[o]};break;case 5:p={t:"d",v:f};break;case 6:p={t:"b",v:l>0};break;case 7:p={t:"n",v:l/86400};break;case 8:p={t:"e",v:0};break;case 9:if(i>-1)p={t:"s",v:r[i]};else throw new Error("Unsupported cell type ".concat(e[1]," : ").concat(n&31," : ").concat(e.slice(0,4)));break;case 10:p={t:"n",v:c};break;default:throw new Error("Unsupported cell type ".concat(e[1]," : ").concat(n&31," : ").concat(e.slice(0,4)))}return p}function zn(e,t){var r=new Uint8Array(32),a=Lt(r),n=12,s=0;switch(r[0]=5,e.t){case"n":r[1]=2,eg(r,n,e.v),s|=1,n+=16;break;case"b":r[1]=6,a.setFloat64(n,e.v?1:0,!0),s|=2,n+=8;break;case"s":if(t.indexOf(e.v)==-1)throw new Error("Value ".concat(e.v," missing from SST!"));r[1]=3,a.setUint32(n,t.indexOf(e.v),!0),s|=8,n+=4;break;default:throw"unsupported cell type "+e.t}return a.setUint32(8,s,!0),r.slice(0,n)}function Hn(e,t){var r=new Uint8Array(32),a=Lt(r),n=12,s=0;switch(r[0]=3,e.t){case"n":r[2]=2,a.setFloat64(n,e.v,!0),s|=32,n+=8;break;case"b":r[2]=6,a.setFloat64(n,e.v?1:0,!0),s|=32,n+=8;break;case"s":if(t.indexOf(e.v)==-1)throw new Error("Value ".concat(e.v," missing from SST!"));r[2]=3,a.setUint32(n,t.indexOf(e.v),!0),s|=16,n+=4;break;default:throw"unsupported cell type "+e.t}return a.setUint32(4,s,!0),r.slice(0,n)}function ng(e,t,r){switch(e[0]){case 0:case 1:case 2:case 3:return tg(e,t,r,e[0]);case 5:return ag(e,t,r);default:throw new Error("Unsupported payload version ".concat(e[0]))}}function ur(e){var t=Ne(e);return La(t[1][0].data)}function to(e,t){var r=Ne(t.data),a=Xe(r[1][0].data),n=r[3],s=[];return(n||[]).forEach(function(i){var o=Ne(i.data),c=Xe(o[1][0].data)>>>0;switch(a){case 1:s[c]=ts(o[3][0].data);break;case 8:{var l=e[ur(o[9][0].data)][0],f=Ne(l.data),p=e[ur(f[1][0].data)][0],h=Xe(p.meta[1][0].data);if(h!=2001)throw new Error("2000 unexpected reference to ".concat(h));var d=Ne(p.data);s[c]=d[3].map(function(m){return ts(m.data)}).join("")}break}}),s}function sg(e,t){var r,a,n,s,i,o,c,l,f,p,h,d,m,u,b=Ne(e),T=Xe(b[1][0].data)>>>0,S=Xe(b[2][0].data)>>>0,g=((a=(r=b[8])==null?void 0:r[0])==null?void 0:a.data)&&Xe(b[8][0].data)>0||!1,x,A;if((s=(n=b[7])==null?void 0:n[0])!=null&&s.data&&t!=0)x=(o=(i=b[7])==null?void 0:i[0])==null?void 0:o.data,A=(l=(c=b[6])==null?void 0:c[0])==null?void 0:l.data;else if((p=(f=b[4])==null?void 0:f[0])!=null&&p.data&&t!=1)x=(d=(h=b[4])==null?void 0:h[0])==null?void 0:d.data,A=(u=(m=b[3])==null?void 0:m[0])==null?void 0:u.data;else throw"NUMBERS Tile missing ".concat(t," cell storage");for(var P=g?4:1,R=Lt(x),U=[],I=0;I<x.length/2;++I){var G=R.getUint16(I*2,!0);G<65535&&U.push([I,G])}if(U.length!=S)throw"Expected ".concat(S," cells, found ").concat(U.length);var W=[];for(I=0;I<U.length-1;++I)W[U[I][0]]=A.subarray(U[I][1]*P,U[I+1][1]*P);return U.length>=1&&(W[U[U.length-1][0]]=A.subarray(U[U.length-1][1]*P)),{R:T,cells:W}}function ig(e,t){var r,a=Ne(t.data),n=(r=a==null?void 0:a[7])!=null&&r[0]?Xe(a[7][0].data)>>>0>0?1:0:-1,s=Ws(a[5],function(i){return sg(i,n)});return{nrows:Xe(a[4][0].data)>>>0,data:s.reduce(function(i,o){return i[o.R]||(i[o.R]=[]),o.cells.forEach(function(c,l){if(i[o.R][l])throw new Error("Duplicate cell r=".concat(o.R," c=").concat(l));i[o.R][l]=c}),i},[])}}function og(e,t,r){var a,n=Ne(t.data),s={s:{r:0,c:0},e:{r:0,c:0}};if(s.e.r=(Xe(n[6][0].data)>>>0)-1,s.e.r<0)throw new Error("Invalid row varint ".concat(n[6][0].data));if(s.e.c=(Xe(n[7][0].data)>>>0)-1,s.e.c<0)throw new Error("Invalid col varint ".concat(n[7][0].data));r["!ref"]=we(s);var i=Ne(n[4][0].data),o=to(e,e[ur(i[4][0].data)][0]),c=(a=i[17])!=null&&a[0]?to(e,e[ur(i[17][0].data)][0]):[],l=Ne(i[3][0].data),f=0;l[1].forEach(function(p){var h=Ne(p.data),d=e[ur(h[2][0].data)][0],m=Xe(d.meta[1][0].data);if(m!=6002)throw new Error("6001 unexpected reference to ".concat(m));var u=ig(e,d);u.data.forEach(function(b,T){b.forEach(function(S,g){var x=ve({r:f+T,c:g}),A=ng(S,o,c);A&&(r[x]=A)})}),f+=u.nrows})}function cg(e,t){var r=Ne(t.data),a={"!ref":"A1"},n=e[ur(r[2][0].data)],s=Xe(n[0].meta[1][0].data);if(s!=6001)throw new Error("6000 unexpected reference to ".concat(s));return og(e,n[0],a),a}function lg(e,t){var r,a=Ne(t.data),n={name:(r=a[1])!=null&&r[0]?ts(a[1][0].data):"",sheets:[]},s=Ws(a[2],ur);return s.forEach(function(i){e[i].forEach(function(o){var c=Xe(o.meta[1][0].data);c==6e3&&n.sheets.push(cg(e,o))})}),n}function fg(e,t){var r=js(),a=Ne(t.data),n=Ws(a[1],ur);if(n.forEach(function(s){e[s].forEach(function(i){var o=Xe(i.meta[1][0].data);if(o==2){var c=lg(e,i);c.sheets.forEach(function(l,f){$s(r,l,f==0?c.name:c.name+"_"+f,!0)})}})}),r.SheetNames.length==0)throw new Error("Empty NUMBERS file");return r}function Gn(e){var t,r,a,n,s={},i=[];if(e.FullPaths.forEach(function(c){if(c.match(/\.iwpv2/))throw new Error("Unsupported password protection")}),e.FileIndex.forEach(function(c){if(c.name.match(/\.iwa$/)){var l;try{l=Mr(c.content)}catch(p){return console.log("?? "+c.content.length+" "+(p.message||p))}var f;try{f=Lr(l)}catch(p){return console.log("## "+(p.message||p))}f.forEach(function(p){s[p.id]=p.messages,i.push(p.id)})}}),!i.length)throw new Error("File has no messages");var o=((n=(a=(r=(t=s==null?void 0:s[1])==null?void 0:t[0])==null?void 0:r.meta)==null?void 0:a[1])==null?void 0:n[0].data)&&Xe(s[1][0].meta[1][0].data)==1&&s[1][0];if(o||i.forEach(function(c){s[c].forEach(function(l){var f=Xe(l.meta[1][0].data)>>>0;if(f==1)if(!o)o=l;else throw new Error("Document has multiple roots")})}),!o)throw new Error("Cannot find Document root");return fg(s,o)}function hg(e,t,r){var a,n,s,i;if(!((a=e[6])!=null&&a[0])||!((n=e[7])!=null&&n[0]))throw"Mutation only works on post-BNC storages!";var o=((i=(s=e[8])==null?void 0:s[0])==null?void 0:i.data)&&Xe(e[8][0].data)>0||!1;if(o)throw"Math only works with normal offsets";for(var c=0,l=Lt(e[7][0].data),f=0,p=[],h=Lt(e[4][0].data),d=0,m=[],u=0;u<t.length;++u){if(t[u]==null){l.setUint16(u*2,65535,!0),h.setUint16(u*2,65535);continue}l.setUint16(u*2,f,!0),h.setUint16(u*2,d,!0);var b,T;switch(typeof t[u]){case"string":b=zn({t:"s",v:t[u]},r),T=Hn({t:"s",v:t[u]},r);break;case"number":b=zn({t:"n",v:t[u]},r),T=Hn({t:"n",v:t[u]},r);break;case"boolean":b=zn({t:"b",v:t[u]},r),T=Hn({t:"b",v:t[u]},r);break;default:throw new Error("Unsupported value "+t[u])}p.push(b),f+=b.length,m.push(T),d+=T.length,++c}for(e[2][0].data=Ie(c);u<e[7][0].data.length/2;++u)l.setUint16(u*2,65535,!0),h.setUint16(u*2,65535,!0);return e[6][0].data=kt(p),e[3][0].data=kt(m),c}function ug(e,t){if(!t||!t.numbers)throw new Error("Must pass a `numbers` option -- check the README");var r=e.Sheets[e.SheetNames[0]];e.SheetNames.length>1&&console.error("The Numbers writer currently writes only the first table");var a=xr(r["!ref"]);a.s.r=a.s.c=0;var n=!1;a.e.c>9&&(n=!0,a.e.c=9),a.e.r>49&&(n=!0,a.e.r=49),n&&console.error("The Numbers writer is currently limited to ".concat(we(a)));var s=bn(r,{range:a,header:1}),i=["~Sh33tJ5~"];s.forEach(function(L){return L.forEach(function(F){typeof F=="string"&&i.push(F)})});var o={},c=[],l=pe.read(t.numbers,{type:"base64"});l.FileIndex.map(function(L,F){return[L,l.FullPaths[F]]}).forEach(function(L){var F=L[0],N=L[1];if(F.type==2&&F.name.match(/\.iwa/)){var X=F.content,ne=Mr(X),J=Lr(ne);J.forEach(function(Q){c.push(Q.id),o[Q.id]={deps:[],location:N,type:Xe(Q.messages[0].meta[1][0].data)}})}}),c.sort(function(L,F){return L-F});var f=c.filter(function(L){return L>1}).map(function(L){return[L,Ie(L)]});l.FileIndex.map(function(L,F){return[L,l.FullPaths[F]]}).forEach(function(L){var F=L[0];if(L[1],!!F.name.match(/\.iwa/)){var N=Lr(Mr(F.content));N.forEach(function(X){X.messages.forEach(function(ne){f.forEach(function(J){X.messages.some(function(Q){return Xe(Q.meta[1][0].data)!=11006&&qv(Q.data,J[1])})&&o[J[0]].deps.push(X.id)})})})}});for(var p=pe.find(l,o[1].location),h=Lr(Mr(p.content)),d,m=0;m<h.length;++m){var u=h[m];u.id==1&&(d=u)}var b=ur(Ne(d.messages[0].data)[1][0].data);for(p=pe.find(l,o[b].location),h=Lr(Mr(p.content)),m=0;m<h.length;++m)u=h[m],u.id==b&&(d=u);for(b=ur(Ne(d.messages[0].data)[2][0].data),p=pe.find(l,o[b].location),h=Lr(Mr(p.content)),m=0;m<h.length;++m)u=h[m],u.id==b&&(d=u);for(b=ur(Ne(d.messages[0].data)[2][0].data),p=pe.find(l,o[b].location),h=Lr(Mr(p.content)),m=0;m<h.length;++m)u=h[m],u.id==b&&(d=u);var T=Ne(d.messages[0].data);{T[6][0].data=Ie(a.e.r+1),T[7][0].data=Ie(a.e.c+1);var S=ur(T[46][0].data),g=pe.find(l,o[S].location),x=Lr(Mr(g.content));{for(var A=0;A<x.length&&x[A].id!=S;++A);if(x[A].id!=S)throw"Bad ColumnRowUIDMapArchive";var P=Ne(x[A].messages[0].data);P[1]=[],P[2]=[],P[3]=[];for(var R=0;R<=a.e.c;++R){var U=[];U[1]=U[2]=[{type:0,data:Ie(R+420690)}],P[1].push({type:2,data:cr(U)}),P[2].push({type:0,data:Ie(R)}),P[3].push({type:0,data:Ie(R)})}P[4]=[],P[5]=[],P[6]=[];for(var I=0;I<=a.e.r;++I)U=[],U[1]=U[2]=[{type:0,data:Ie(I+726270)}],P[4].push({type:2,data:cr(U)}),P[5].push({type:0,data:Ie(I)}),P[6].push({type:0,data:Ie(I)});x[A].messages[0].data=cr(P)}g.content=$t(jt(x)),g.size=g.content.length,delete T[46];var G=Ne(T[4][0].data);{G[7][0].data=Ie(a.e.r+1);var W=Ne(G[1][0].data),D=ur(W[2][0].data);g=pe.find(l,o[D].location),x=Lr(Mr(g.content));{if(x[0].id!=D)throw"Bad HeaderStorageBucket";var te=Ne(x[0].messages[0].data);for(I=0;I<s.length;++I){var fe=Ne(te[2][0].data);fe[1][0].data=Ie(I),fe[4][0].data=Ie(s[I].length),te[2][I]={type:te[2][0].type,data:cr(fe)}}x[0].messages[0].data=cr(te)}g.content=$t(jt(x)),g.size=g.content.length;var se=ur(G[2][0].data);g=pe.find(l,o[se].location),x=Lr(Mr(g.content));{if(x[0].id!=se)throw"Bad HeaderStorageBucket";for(te=Ne(x[0].messages[0].data),R=0;R<=a.e.c;++R)fe=Ne(te[2][0].data),fe[1][0].data=Ie(R),fe[4][0].data=Ie(a.e.r+1),te[2][R]={type:te[2][0].type,data:cr(fe)};x[0].messages[0].data=cr(te)}g.content=$t(jt(x)),g.size=g.content.length;var de=ur(G[4][0].data);(function(){for(var L=pe.find(l,o[de].location),F=Lr(Mr(L.content)),N,X=0;X<F.length;++X){var ne=F[X];ne.id==de&&(N=ne)}var J=Ne(N.messages[0].data);{J[3]=[];var Q=[];i.forEach(function(O,Me){Q[1]=[{type:0,data:Ie(Me)}],Q[2]=[{type:0,data:Ie(1)}],Q[3]=[{type:2,data:Zv(O)}],J[3].push({type:2,data:cr(Q)})})}N.messages[0].data=cr(J);var q=jt(F),Ee=$t(q);L.content=Ee,L.size=L.content.length})();var ue=Ne(G[3][0].data);{var K=ue[1][0];delete ue[2];var ee=Ne(K.data);{var me=ur(ee[2][0].data);(function(){for(var L=pe.find(l,o[me].location),F=Lr(Mr(L.content)),N,X=0;X<F.length;++X){var ne=F[X];ne.id==me&&(N=ne)}var J=Ne(N.messages[0].data);{delete J[6],delete ue[7];var Q=new Uint8Array(J[5][0].data);J[5]=[];for(var q=0,Ee=0;Ee<=a.e.r;++Ee){var O=Ne(Q);q+=hg(O,s[Ee],i),O[1][0].data=Ie(Ee),J[5].push({data:cr(O),type:2})}J[1]=[{type:0,data:Ie(a.e.c+1)}],J[2]=[{type:0,data:Ie(a.e.r+1)}],J[3]=[{type:0,data:Ie(q)}],J[4]=[{type:0,data:Ie(a.e.r+1)}]}N.messages[0].data=cr(J);var Me=jt(F),Oe=$t(Me);L.content=Oe,L.size=L.content.length})()}K.data=cr(ee)}G[3][0].data=cr(ue)}T[4][0].data=cr(G)}d.messages[0].data=cr(T);var Te=jt(h),C=$t(Te);return p.content=C,p.size=p.content.length,l}function ll(e){return function(t){for(var r=0;r!=e.length;++r){var a=e[r];t[a[0]]===void 0&&(t[a[0]]=a[1]),a[2]==="n"&&(t[a[0]]=Number(t[a[0]]))}}}function zs(e){ll([["cellNF",!1],["cellHTML",!0],["cellFormula",!0],["cellStyles",!1],["cellText",!0],["cellDates",!1],["sheetStubs",!1],["sheetRows",0,"n"],["bookDeps",!1],["bookSheets",!1],["bookProps",!1],["bookFiles",!1],["bookVBA",!1],["password",""],["WTF",!1]])(e)}function Hs(e){ll([["cellDates",!1],["bookSST",!1],["bookType","xlsx"],["compression",!1],["WTF",!1]])(e)}function dg(e){return _e.WS.indexOf(e)>-1?"sheet":e==_e.CS?"chart":e==_e.DS?"dialog":e==_e.MS?"macro":e&&e.length?e:"sheet"}function pg(e,t){if(!e)return 0;try{e=t.map(function(r){return r.id||(r.id=r.strRelID),[r.name,e["!id"][r.id].Target,dg(e["!id"][r.id].Type)]})}catch{return null}return!e||e.length===0?null:e}function mg(e,t,r,a,n,s,i,o,c,l,f,p){try{s[a]=ka(Nr(e,r,!0),t);var h=Qe(e,t),d;switch(o){case"sheet":d=Db(h,t,n,c,s[a],l,f,p);break;case"chart":if(d=Lb(h,t,n,c,s[a],l,f,p),!d||!d["!drawel"])break;var m=ba(d["!drawel"].Target,t),u=Oa(m),b=Ed(Nr(e,m,!0),ka(Nr(e,u,!0),m)),T=ba(b,m),S=Oa(T);d=lb(Nr(e,T,!0),T,c,ka(Nr(e,S,!0),T),l,d);break;case"macro":d=Mb(h,t,n,c,s[a],l,f,p);break;case"dialog":d=Ub(h,t,n,c,s[a],l,f,p);break;default:throw new Error("Unrecognized sheet type "+o)}i[a]=d;var g=[];s&&s[a]&&Ye(s[a]).forEach(function(x){var A="";if(s[a][x].Type==_e.CMNT){A=ba(s[a][x].Target,t);var P=Hb(Qe(e,A,!0),A,c);if(!P||!P.length)return;Gi(d,P,!1)}s[a][x].Type==_e.TCMNT&&(A=ba(s[a][x].Target,t),g=g.concat(Sd(Qe(e,A,!0),c)))}),g&&g.length&&Gi(d,g,!0,c.people||[])}catch(x){if(c.WTF)throw x}}function Pr(e){return e.charAt(0)=="/"?e.slice(1):e}function bg(e,t){if(sa(),t=t||{},zs(t),Ur(e,"META-INF/manifest.xml")||Ur(e,"objectdata.xml"))return Yv(e,t);if(Ur(e,"Index/Document.iwa")){if(typeof Uint8Array>"u")throw new Error("NUMBERS file parsing requires Uint8Array support");if(typeof Gn<"u"){if(e.FileIndex)return Gn(e);var r=pe.utils.cfb_new();return fi(e).forEach(function(fe){ke(r,fe,_f(e,fe))}),Gn(r)}throw new Error("Unsupported NUMBERS file")}if(!Ur(e,"[Content_Types].xml"))throw Ur(e,"index.xml.gz")?new Error("Unsupported NUMBERS 08 file"):Ur(e,"index.xml")?new Error("Unsupported NUMBERS 09 file"):new Error("Unsupported ZIP file");var a=fi(e),n=g1(Nr(e,"[Content_Types].xml")),s=!1,i,o;if(n.workbooks.length===0&&(o="xl/workbook.xml",Qe(e,o,!0)&&n.workbooks.push(o)),n.workbooks.length===0){if(o="xl/workbook.bin",!Qe(e,o,!0))throw new Error("Could not find workbook");n.workbooks.push(o),s=!0}n.workbooks[0].slice(-3)=="bin"&&(s=!0);var c={},l={};if(!t.bookSheets&&!t.bookProps){if(Ta=[],n.sst)try{Ta=zb(Qe(e,Pr(n.sst)),n.sst,t)}catch(fe){if(t.WTF)throw fe}t.cellStyles&&n.themes.length&&(c=Wb(Nr(e,n.themes[0].replace(/^\//,""),!0)||"",n.themes[0],t)),n.style&&(l=Bb(Qe(e,Pr(n.style)),n.style,c,t))}n.links.map(function(fe){try{var se=ka(Nr(e,Oa(Pr(fe))),fe);return Vb(Qe(e,Pr(fe)),se,fe,t)}catch{}});var f=Pb(Qe(e,Pr(n.workbooks[0])),n.workbooks[0],t),p={},h="";n.coreprops.length&&(h=Qe(e,Pr(n.coreprops[0]),!0),h&&(p=Zo(h)),n.extprops.length!==0&&(h=Qe(e,Pr(n.extprops[0]),!0),h&&_1(h,p,t)));var d={};(!t.bookSheets||t.bookProps)&&n.custprops.length!==0&&(h=Nr(e,Pr(n.custprops[0]),!0),h&&(d=A1(h,t)));var m={};if((t.bookSheets||t.bookProps)&&(f.Sheets?i=f.Sheets.map(function(fe){return fe.name}):p.Worksheets&&p.SheetNames.length>0&&(i=p.SheetNames),t.bookProps&&(m.Props=p,m.Custprops=d),t.bookSheets&&typeof i<"u"&&(m.SheetNames=i),t.bookSheets?m.SheetNames:t.bookProps))return m;i={};var u={};t.bookDeps&&n.calcchain&&(u=Gb(Qe(e,Pr(n.calcchain)),n.calcchain));var b=0,T={},S,g;{var x=f.Sheets;p.Worksheets=x.length,p.SheetNames=[];for(var A=0;A!=x.length;++A)p.SheetNames[A]=x[A].name}var P=s?"bin":"xml",R=n.workbooks[0].lastIndexOf("/"),U=(n.workbooks[0].slice(0,R+1)+"_rels/"+n.workbooks[0].slice(R+1)+".rels").replace(/^\//,"");Ur(e,U)||(U="xl/_rels/workbook."+P+".rels");var I=ka(Nr(e,U,!0),U.replace(/_rels.*/,"s5s"));(n.metadata||[]).length>=1&&(t.xlmeta=jb(Qe(e,Pr(n.metadata[0])),n.metadata[0],t)),(n.people||[]).length>=1&&(t.people=xd(Qe(e,Pr(n.people[0])),t)),I&&(I=pg(I,f.Sheets));var G=Qe(e,"xl/worksheets/sheet.xml",!0)?1:0;e:for(b=0;b!=p.Worksheets;++b){var W="sheet";if(I&&I[b]?(S="xl/"+I[b][1].replace(/[\/]?xl\//,""),Ur(e,S)||(S=I[b][1]),Ur(e,S)||(S=U.replace(/_rels\/.*$/,"")+I[b][1]),W=I[b][2]):(S="xl/worksheets/sheet"+(b+1-G)+"."+P,S=S.replace(/sheet0\./,"sheet.")),g=S.replace(/^(.*)(\/)([^\/]*)$/,"$1/_rels/$3.rels"),t&&t.sheets!=null)switch(typeof t.sheets){case"number":if(b!=t.sheets)continue e;break;case"string":if(p.SheetNames[b].toLowerCase()!=t.sheets.toLowerCase())continue e;break;default:if(Array.isArray&&Array.isArray(t.sheets)){for(var D=!1,te=0;te!=t.sheets.length;++te)typeof t.sheets[te]=="number"&&t.sheets[te]==b&&(D=1),typeof t.sheets[te]=="string"&&t.sheets[te].toLowerCase()==p.SheetNames[b].toLowerCase()&&(D=1);if(!D)continue e}}mg(e,S,g,p.SheetNames[b],b,T,i,W,t,f,c,l)}return m={Directory:n,Workbook:f,Props:p,Custprops:d,Deps:u,Sheets:i,SheetNames:p.SheetNames,Strings:Ta,Styles:l,Themes:c,SSF:ze(be)},t&&t.bookFiles&&(e.files?(m.keys=a,m.files=e.files):(m.keys=[],m.files={},e.FullPaths.forEach(function(fe,se){fe=fe.replace(/^Root Entry[\/]/,""),m.keys.push(fe),m.files[fe]=e.FileIndex[se]}))),t&&t.bookVBA&&(n.vba.length>0?m.vbaraw=Qe(e,Pr(n.vba[0]),!0):n.defaults&&n.defaults.bin===Pd&&(m.vbaraw=Qe(e,"xl/vbaProject.bin",!0))),m}function vg(e,t){var r=t||{},a="Workbook",n=pe.find(e,a);try{if(a="/!DataSpaces/Version",n=pe.find(e,a),!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);if(ju(n.content),a="/!DataSpaces/DataSpaceMap",n=pe.find(e,a),!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);var s=Xu(n.content);if(s.length!==1||s[0].comps.length!==1||s[0].comps[0].t!==0||s[0].name!=="StrongEncryptionDataSpace"||s[0].comps[0].v!=="EncryptedPackage")throw new Error("ECMA-376 Encrypted file bad "+a);if(a="/!DataSpaces/DataSpaceInfo/StrongEncryptionDataSpace",n=pe.find(e,a),!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);var i=Yu(n.content);if(i.length!=1||i[0]!="StrongEncryptionTransform")throw new Error("ECMA-376 Encrypted file bad "+a);if(a="/!DataSpaces/TransformInfo/StrongEncryptionTransform/!Primary",n=pe.find(e,a),!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);Ju(n.content)}catch{}if(a="/EncryptionInfo",n=pe.find(e,a),!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);var o=Zu(n.content);if(a="/EncryptedPackage",n=pe.find(e,a),!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);if(o[0]==4&&typeof decrypt_agile<"u")return decrypt_agile(o[1],n.content,r.password||"",r);if(o[0]==2&&typeof decrypt_std76<"u")return decrypt_std76(o[1],n.content,r.password||"",r);throw new Error("File is password-protected")}function gg(e,t){return t.bookType=="ods"?cl(e,t):t.bookType=="numbers"?ug(e,t):t.bookType=="xlsb"?wg(e,t):kg(e,t)}function wg(e,t){Yt=1024,e&&!e.SSF&&(e.SSF=ze(be)),e&&e.SSF&&(sa(),gn(e.SSF),t.revssf=kn(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,Ea?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r=t.bookType=="xlsb"?"bin":"xml",a=Lc.indexOf(t.bookType)>-1,n=As();Hs(t=t||{});var s=ds(),i="",o=0;if(t.cellXfs=[],yt(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),i="docProps/core.xml",ke(s,i,qo(e.Props,t)),n.coreprops.push(i),Fe(t.rels,2,i,_e.CORE_PROPS),i="docProps/app.xml",!(e.Props&&e.Props.SheetNames))if(!e.Workbook||!e.Workbook.Sheets)e.Props.SheetNames=e.SheetNames;else{for(var c=[],l=0;l<e.SheetNames.length;++l)(e.Workbook.Sheets[l]||{}).Hidden!=2&&c.push(e.SheetNames[l]);e.Props.SheetNames=c}for(e.Props.Worksheets=e.Props.SheetNames.length,ke(s,i,rc(e.Props)),n.extprops.push(i),Fe(t.rels,3,i,_e.EXT_PROPS),e.Custprops!==e.Props&&Ye(e.Custprops||{}).length>0&&(i="docProps/custom.xml",ke(s,i,tc(e.Custprops)),n.custprops.push(i),Fe(t.rels,4,i,_e.CUST_PROPS)),o=1;o<=e.SheetNames.length;++o){var f={"!id":{}},p=e.Sheets[e.SheetNames[o-1]],h=(p||{})["!type"]||"sheet";switch(h){case"chart":default:i="xl/worksheets/sheet"+o+"."+r,ke(s,i,Xb(o-1,i,t,e,f)),n.sheets.push(i),Fe(t.wbrels,-1,"worksheets/sheet"+o+"."+r,_e.WS[0])}if(p){var d=p["!comments"],m=!1,u="";d&&d.length>0&&(u="xl/comments"+o+"."+r,ke(s,u,Jb(d,u)),n.comments.push(u),Fe(f,-1,"../comments"+o+"."+r,_e.CMNT),m=!0),p["!legacy"]&&m&&ke(s,"xl/drawings/vmlDrawing"+o+".vml",Pc(o,p["!comments"])),delete p["!comments"],delete p["!legacy"]}f["!id"].rId1&&ke(s,Oa(i),Zt(f))}return t.Strings!=null&&t.Strings.length>0&&(i="xl/sharedStrings."+r,ke(s,i,Kb(t.Strings,i,t)),n.strs.push(i),Fe(t.wbrels,-1,"sharedStrings."+r,_e.SST)),i="xl/workbook."+r,ke(s,i,$b(e,i)),n.workbooks.push(i),Fe(t.rels,1,i,_e.WB),i="xl/theme/theme1.xml",ke(s,i,Fs(e.Themes,t)),n.themes.push(i),Fe(t.wbrels,-1,"theme/theme1.xml",_e.THEME),i="xl/styles."+r,ke(s,i,Yb(e,i,t)),n.styles.push(i),Fe(t.wbrels,-1,"styles."+r,_e.STY),e.vbaraw&&a&&(i="xl/vbaProject.bin",ke(s,i,e.vbaraw),n.vba.push(i),Fe(t.wbrels,-1,"vbaProject.bin",_e.VBA)),i="xl/metadata."+r,ke(s,i,Zb(i)),n.metadata.push(i),Fe(t.wbrels,-1,"metadata."+r,_e.XLMETA),ke(s,"[Content_Types].xml",Ko(n,t)),ke(s,"_rels/.rels",Zt(t.rels)),ke(s,"xl/_rels/workbook."+r+".rels",Zt(t.wbrels)),delete t.revssf,delete t.ssf,s}function kg(e,t){Yt=1024,e&&!e.SSF&&(e.SSF=ze(be)),e&&e.SSF&&(sa(),gn(e.SSF),t.revssf=kn(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,Ea?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r="xml",a=Lc.indexOf(t.bookType)>-1,n=As();Hs(t=t||{});var s=ds(),i="",o=0;if(t.cellXfs=[],yt(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),i="docProps/core.xml",ke(s,i,qo(e.Props,t)),n.coreprops.push(i),Fe(t.rels,2,i,_e.CORE_PROPS),i="docProps/app.xml",!(e.Props&&e.Props.SheetNames))if(!e.Workbook||!e.Workbook.Sheets)e.Props.SheetNames=e.SheetNames;else{for(var c=[],l=0;l<e.SheetNames.length;++l)(e.Workbook.Sheets[l]||{}).Hidden!=2&&c.push(e.SheetNames[l]);e.Props.SheetNames=c}e.Props.Worksheets=e.Props.SheetNames.length,ke(s,i,rc(e.Props)),n.extprops.push(i),Fe(t.rels,3,i,_e.EXT_PROPS),e.Custprops!==e.Props&&Ye(e.Custprops||{}).length>0&&(i="docProps/custom.xml",ke(s,i,tc(e.Custprops)),n.custprops.push(i),Fe(t.rels,4,i,_e.CUST_PROPS));var f=["SheetJ5"];for(t.tcid=0,o=1;o<=e.SheetNames.length;++o){var p={"!id":{}},h=e.Sheets[e.SheetNames[o-1]],d=(h||{})["!type"]||"sheet";switch(d){case"chart":default:i="xl/worksheets/sheet"+o+"."+r,ke(s,i,Xc(o-1,t,e,p)),n.sheets.push(i),Fe(t.wbrels,-1,"worksheets/sheet"+o+"."+r,_e.WS[0])}if(h){var m=h["!comments"],u=!1,b="";if(m&&m.length>0){var T=!1;m.forEach(function(S){S[1].forEach(function(g){g.T==!0&&(T=!0)})}),T&&(b="xl/threadedComments/threadedComment"+o+"."+r,ke(s,b,_d(m,f,t)),n.threadedcomments.push(b),Fe(p,-1,"../threadedComments/threadedComment"+o+"."+r,_e.TCMNT)),b="xl/comments"+o+"."+r,ke(s,b,Dc(m)),n.comments.push(b),Fe(p,-1,"../comments"+o+"."+r,_e.CMNT),u=!0}h["!legacy"]&&u&&ke(s,"xl/drawings/vmlDrawing"+o+".vml",Pc(o,h["!comments"])),delete h["!comments"],delete h["!legacy"]}p["!id"].rId1&&ke(s,Oa(i),Zt(p))}return t.Strings!=null&&t.Strings.length>0&&(i="xl/sharedStrings."+r,ke(s,i,Ec(t.Strings,t)),n.strs.push(i),Fe(t.wbrels,-1,"sharedStrings."+r,_e.SST)),i="xl/workbook."+r,ke(s,i,qc(e)),n.workbooks.push(i),Fe(t.rels,1,i,_e.WB),i="xl/theme/theme1.xml",ke(s,i,Fs(e.Themes,t)),n.themes.push(i),Fe(t.wbrels,-1,"theme/theme1.xml",_e.THEME),i="xl/styles."+r,ke(s,i,Oc(e,t)),n.styles.push(i),Fe(t.wbrels,-1,"styles."+r,_e.STY),e.vbaraw&&a&&(i="xl/vbaProject.bin",ke(s,i,e.vbaraw),n.vba.push(i),Fe(t.wbrels,-1,"vbaProject.bin",_e.VBA)),i="xl/metadata."+r,ke(s,i,Fc()),n.metadata.push(i),Fe(t.wbrels,-1,"metadata."+r,_e.XLMETA),f.length>1&&(i="xl/persons/person.xml",ke(s,i,Ad(f)),n.people.push(i),Fe(t.wbrels,-1,"persons/person.xml",_e.PEOPLE)),ke(s,"[Content_Types].xml",Ko(n,t)),ke(s,"_rels/.rels",Zt(t.rels)),ke(s,"xl/_rels/workbook."+r+".rels",Zt(t.wbrels)),delete t.revssf,delete t.ssf,s}function Gs(e,t){var r="";switch((t||{}).type||"base64"){case"buffer":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];case"base64":r=Ir(e.slice(0,12));break;case"binary":r=e;break;case"array":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];default:throw new Error("Unrecognized type "+(t&&t.type||"undefined"))}return[r.charCodeAt(0),r.charCodeAt(1),r.charCodeAt(2),r.charCodeAt(3),r.charCodeAt(4),r.charCodeAt(5),r.charCodeAt(6),r.charCodeAt(7)]}function Tg(e,t){return pe.find(e,"EncryptedPackage")?vg(e,t):tl(e,t)}function Eg(e,t){var r,a=e,n=t||{};return n.type||(n.type=Se&&Buffer.isBuffer(e)?"buffer":"base64"),r=So(a,n),bg(r,n)}function fl(e,t){var r=0;e:for(;r<e.length;)switch(e.charCodeAt(r)){case 10:case 13:case 32:++r;break;case 60:return es(e.slice(r),t);default:break e}return ra.to_workbook(e,t)}function yg(e,t){var r="",a=Gs(e,t);switch(t.type){case"base64":r=Ir(e);break;case"binary":r=e;break;case"buffer":r=e.toString("binary");break;case"array":r=It(e);break;default:throw new Error("Unrecognized type "+t.type)}return a[0]==239&&a[1]==187&&a[2]==191&&(r=Le(r)),t.type="binary",fl(r,t)}function Sg(e,t){var r=e;return t.type=="base64"&&(r=Ir(r)),r=jn.utils.decode(1200,r.slice(2),"str"),t.type="binary",fl(r,t)}function _g(e){return e.match(/[^\x00-\x7F]/)?qr(e):e}function Vn(e,t,r,a){return a?(r.type="string",ra.to_workbook(e,r)):ra.to_workbook(t,r)}function as(e,t){cs();var r=t||{};if(typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer)return as(new Uint8Array(e),(r=ze(r),r.type="array",r));typeof Uint8Array<"u"&&e instanceof Uint8Array&&!r.type&&(r.type=typeof Deno<"u"?"buffer":"array");var a=e,n=[0,0,0,0],s=!1;if(r.cellStyles&&(r.cellNF=!0,r.sheetStubs=!0),Qt={},r.dateNF&&(Qt.dateNF=r.dateNF),r.type||(r.type=Se&&Buffer.isBuffer(e)?"buffer":"base64"),r.type=="file"&&(r.type=Se?"buffer":"binary",a=vf(e),typeof Uint8Array<"u"&&!Se&&(r.type="array")),r.type=="string"&&(s=!0,r.type="binary",r.codepage=65001,a=_g(e)),r.type=="array"&&typeof Uint8Array<"u"&&e instanceof Uint8Array&&typeof ArrayBuffer<"u"){var i=new ArrayBuffer(3),o=new Uint8Array(i);if(o.foo="bar",!o.foo)return r=ze(r),r.type="array",as(ls(a),r)}switch((n=Gs(a,r))[0]){case 208:if(n[1]===207&&n[2]===17&&n[3]===224&&n[4]===161&&n[5]===177&&n[6]===26&&n[7]===225)return Tg(pe.read(a,r),r);break;case 9:if(n[1]<=8)return tl(a,r);break;case 60:return es(a,r);case 73:if(n[1]===73&&n[2]===42&&n[3]===0)throw new Error("TIFF Image File is not a spreadsheet");if(n[1]===68)return Cu(a,r);break;case 84:if(n[1]===65&&n[2]===66&&n[3]===76)return kc.to_workbook(a,r);break;case 80:return n[1]===75&&n[2]<9&&n[3]<9?Eg(a,r):Vn(e,a,r,s);case 239:return n[3]===60?es(a,r):Vn(e,a,r,s);case 255:if(n[1]===254)return Sg(a,r);if(n[1]===0&&n[2]===2&&n[3]===0)return ta.to_workbook(a,r);break;case 0:if(n[1]===0&&(n[2]>=2&&n[3]===0||n[2]===0&&(n[3]===8||n[3]===9)))return ta.to_workbook(a,r);break;case 3:case 131:case 139:case 140:return Qn.to_workbook(a,r);case 123:if(n[1]===92&&n[2]===114&&n[3]===116)return Ac.to_workbook(a,r);break;case 10:case 13:case 32:return yg(a,r);case 137:if(n[1]===80&&n[2]===78&&n[3]===71)throw new Error("PNG Image File is not a spreadsheet");break}return Au.indexOf(n[0])>-1&&n[2]<=12&&n[3]<=31?Qn.to_workbook(a,r):Vn(e,a,r,s)}function hl(e,t){switch(t.type){case"base64":case"binary":break;case"buffer":case"array":t.type="";break;case"file":return Ua(t.file,pe.write(e,{type:Se?"buffer":""}));case"string":throw new Error("'string' output type invalid for '"+t.bookType+"' files");default:throw new Error("Unrecognized type "+t.type)}return pe.write(e,t)}function xg(e,t){var r=ze(t||{}),a=gg(e,r);return Ag(a,r)}function Ag(e,t){var r={},a=Se?"nodebuffer":typeof Uint8Array<"u"?"array":"string";if(t.compression&&(r.compression="DEFLATE"),t.password)r.type=a;else switch(t.type){case"base64":r.type="base64";break;case"binary":r.type="string";break;case"string":throw new Error("'string' output type invalid for '"+t.bookType+"' files");case"buffer":case"file":r.type=a;break;default:throw new Error("Unrecognized type "+t.type)}var n=e.FullPaths?pe.write(e,{fileType:"zip",type:{nodebuffer:"buffer",string:"binary"}[r.type]||r.type,compression:!!t.compression}):e.generate(r);if(typeof Deno<"u"&&typeof n=="string"){if(t.type=="binary"||t.type=="base64")return n;n=new Uint8Array(vn(n))}return t.password&&typeof encrypt_agile<"u"?hl(encrypt_agile(n,t.password),t):t.type==="file"?Ua(t.file,n):t.type=="string"?Le(n):n}function Cg(e,t){var r=t||{},a=yv(e,r);return hl(a,r)}function Zr(e,t,r){r||(r="");var a=r+e;switch(t.type){case"base64":return _a(qr(a));case"binary":return qr(a);case"string":return e;case"file":return Ua(t.file,a,"utf8");case"buffer":return Se?lt(a,"utf8"):typeof TextEncoder<"u"?new TextEncoder().encode(a):Zr(a,{type:"binary"}).split("").map(function(n){return n.charCodeAt(0)})}throw new Error("Unrecognized type "+t.type)}function Rg(e,t){switch(t.type){case"base64":return _a(e);case"binary":return e;case"string":return e;case"file":return Ua(t.file,e,"binary");case"buffer":return Se?lt(e,"binary"):e.split("").map(function(r){return r.charCodeAt(0)})}throw new Error("Unrecognized type "+t.type)}function tn(e,t){switch(t.type){case"string":case"base64":case"binary":for(var r="",a=0;a<e.length;++a)r+=String.fromCharCode(e[a]);return t.type=="base64"?_a(r):t.type=="string"?Le(r):r;case"file":return Ua(t.file,e);case"buffer":return e;default:throw new Error("Unrecognized type "+t.type)}}function ul(e,t){cs(),wb(e);var r=ze(t||{});if(r.cellStyles&&(r.cellNF=!0,r.sheetStubs=!0),r.type=="array"){r.type="binary";var a=ul(e,r);return r.type="array",vn(a)}var n=0;if(r.sheet&&(typeof r.sheet=="number"?n=r.sheet:n=e.SheetNames.indexOf(r.sheet),!e.SheetNames[n]))throw new Error("Sheet not found: "+r.sheet+" : "+typeof r.sheet);switch(r.bookType||"xlsb"){case"xml":case"xlml":return Zr(bv(e,r),r);case"slk":case"sylk":return Zr(wc.from_sheet(e.Sheets[e.SheetNames[n]],r),r);case"htm":case"html":return Zr(nl(e.Sheets[e.SheetNames[n]],r),r);case"txt":return Rg(dl(e.Sheets[e.SheetNames[n]],r),r);case"csv":return Zr(Vs(e.Sheets[e.SheetNames[n]],r),r,"\uFEFF");case"dif":return Zr(kc.from_sheet(e.Sheets[e.SheetNames[n]],r),r);case"dbf":return tn(Qn.from_sheet(e.Sheets[e.SheetNames[n]],r),r);case"prn":return Zr(ra.from_sheet(e.Sheets[e.SheetNames[n]],r),r);case"rtf":return Zr(Ac.from_sheet(e.Sheets[e.SheetNames[n]],r),r);case"eth":return Zr(Tc.from_sheet(e.Sheets[e.SheetNames[n]],r),r);case"fods":return Zr(cl(e,r),r);case"wk1":return tn(ta.sheet_to_wk1(e.Sheets[e.SheetNames[n]],r),r);case"wk3":return tn(ta.book_to_wk3(e,r),r);case"biff2":r.biff||(r.biff=2);case"biff3":r.biff||(r.biff=3);case"biff4":return r.biff||(r.biff=4),tn(al(e,r),r);case"biff5":r.biff||(r.biff=5);case"biff8":case"xla":case"xls":return r.biff||(r.biff=8),Cg(e,r);case"xlsx":case"xlsm":case"xlam":case"xlsb":case"numbers":case"ods":return xg(e,r);default:throw new Error("Unrecognized bookType |"+r.bookType+"|")}}function Og(e,t,r,a,n,s,i,o){var c=Je(r),l=o.defval,f=o.raw||!Object.prototype.hasOwnProperty.call(o,"raw"),p=!0,h=n===1?[]:{};if(n!==1)if(Object.defineProperty)try{Object.defineProperty(h,"__rowNum__",{value:r,enumerable:!1})}catch{h.__rowNum__=r}else h.__rowNum__=r;if(!i||e[r])for(var d=t.s.c;d<=t.e.c;++d){var m=i?e[r][d]:e[a[d]+c];if(m===void 0||m.t===void 0){if(l===void 0)continue;s[d]!=null&&(h[s[d]]=l);continue}var u=m.v;switch(m.t){case"z":if(u==null)break;continue;case"e":u=u==0?null:void 0;break;case"s":case"d":case"b":case"n":break;default:throw new Error("unrecognized type "+m.t)}if(s[d]!=null){if(u==null)if(m.t=="e"&&u===null)h[s[d]]=null;else if(l!==void 0)h[s[d]]=l;else if(f&&u===null)h[s[d]]=null;else continue;else h[s[d]]=f&&(m.t!=="n"||m.t==="n"&&o.rawNumbers!==!1)?u:ct(m,u,o);u!=null&&(p=!1)}}return{row:h,isempty:p}}function bn(e,t){if(e==null||e["!ref"]==null)return[];var r={t:"n",v:0},a=0,n=1,s=[],i=0,o="",c={s:{r:0,c:0},e:{r:0,c:0}},l=t||{},f=l.range!=null?l.range:e["!ref"];switch(l.header===1?a=1:l.header==="A"?a=2:Array.isArray(l.header)?a=3:l.header==null&&(a=0),typeof f){case"string":c=Re(f);break;case"number":c=Re(e["!ref"]),c.s.r=f;break;default:c=f}a>0&&(n=0);var p=Je(c.s.r),h=[],d=[],m=0,u=0,b=Array.isArray(e),T=c.s.r,S=0,g={};b&&!e[T]&&(e[T]=[]);var x=l.skipHidden&&e["!cols"]||[],A=l.skipHidden&&e["!rows"]||[];for(S=c.s.c;S<=c.e.c;++S)if(!(x[S]||{}).hidden)switch(h[S]=Ve(S),r=b?e[T][S]:e[h[S]+p],a){case 1:s[S]=S-c.s.c;break;case 2:s[S]=h[S];break;case 3:s[S]=l.header[S-c.s.c];break;default:if(r==null&&(r={w:"__EMPTY",t:"s"}),o=i=ct(r,null,l),u=g[i]||0,!u)g[i]=1;else{do o=i+"_"+u++;while(g[o]);g[i]=u,g[o]=1}s[S]=o}for(T=c.s.r+n;T<=c.e.r;++T)if(!(A[T]||{}).hidden){var P=Og(e,c,T,h,a,s,b,l);(P.isempty===!1||(a===1?l.blankrows!==!1:l.blankrows))&&(d[m++]=P.row)}return d.length=m,d}var ao=/"/g;function Ng(e,t,r,a,n,s,i,o){for(var c=!0,l=[],f="",p=Je(r),h=t.s.c;h<=t.e.c;++h)if(a[h]){var d=o.dense?(e[r]||[])[h]:e[a[h]+p];if(d==null)f="";else if(d.v!=null){c=!1,f=""+(o.rawNumbers&&d.t=="n"?d.v:ct(d,null,o));for(var m=0,u=0;m!==f.length;++m)if((u=f.charCodeAt(m))===n||u===s||u===34||o.forceQuotes){f='"'+f.replace(ao,'""')+'"';break}f=="ID"&&(f='"ID"')}else d.f!=null&&!d.F?(c=!1,f="="+d.f,f.indexOf(",")>=0&&(f='"'+f.replace(ao,'""')+'"')):f="";l.push(f)}return o.blankrows===!1&&c?null:l.join(i)}function Vs(e,t){var r=[],a=t??{};if(e==null||e["!ref"]==null)return"";var n=Re(e["!ref"]),s=a.FS!==void 0?a.FS:",",i=s.charCodeAt(0),o=a.RS!==void 0?a.RS:`
`,c=o.charCodeAt(0),l=new RegExp((s=="|"?"\\|":s)+"+$"),f="",p=[];a.dense=Array.isArray(e);for(var h=a.skipHidden&&e["!cols"]||[],d=a.skipHidden&&e["!rows"]||[],m=n.s.c;m<=n.e.c;++m)(h[m]||{}).hidden||(p[m]=Ve(m));for(var u=0,b=n.s.r;b<=n.e.r;++b)(d[b]||{}).hidden||(f=Ng(e,n,b,p,i,c,s,a),f!=null&&(a.strip&&(f=f.replace(l,"")),(f||a.blankrows!==!1)&&r.push((u++?o:"")+f)));return delete a.dense,r.join("")}function dl(e,t){t||(t={}),t.FS="	",t.RS=`
`;var r=Vs(e,t);return r}function Ig(e){var t="",r,a="";if(e==null||e["!ref"]==null)return[];var n=Re(e["!ref"]),s="",i=[],o,c=[],l=Array.isArray(e);for(o=n.s.c;o<=n.e.c;++o)i[o]=Ve(o);for(var f=n.s.r;f<=n.e.r;++f)for(s=Je(f),o=n.s.c;o<=n.e.c;++o)if(t=i[o]+s,r=l?(e[f]||[])[o]:e[t],a="",r!==void 0){if(r.F!=null){if(t=r.F,!r.f)continue;a=r.f,t.indexOf(":")==-1&&(t=t+":"+t)}if(r.f!=null)a=r.f;else{if(r.t=="z")continue;if(r.t=="n"&&r.v!=null)a=""+r.v;else if(r.t=="b")a=r.v?"TRUE":"FALSE";else if(r.w!==void 0)a="'"+r.w;else{if(r.v===void 0)continue;r.t=="s"?a="'"+r.v:a=""+r.v}}c[c.length]=t+"="+a}return c}function pl(e,t,r){var a=r||{},n=+!a.skipHeader,s=e||{},i=0,o=0;if(s&&a.origin!=null)if(typeof a.origin=="number")i=a.origin;else{var c=typeof a.origin=="string"?je(a.origin):a.origin;i=c.r,o=c.c}var l,f={s:{c:0,r:0},e:{c:o,r:i+t.length-1+n}};if(s["!ref"]){var p=Re(s["!ref"]);f.e.c=Math.max(f.e.c,p.e.c),f.e.r=Math.max(f.e.r,p.e.r),i==-1&&(i=p.e.r+1,f.e.r=i+t.length-1+n)}else i==-1&&(i=0,f.e.r=t.length-1+n);var h=a.header||[],d=0;t.forEach(function(u,b){Ye(u).forEach(function(T){(d=h.indexOf(T))==-1&&(h[d=h.length]=T);var S=u[T],g="z",x="",A=ve({c:o+d,r:i+b+n});l=Ma(s,A),S&&typeof S=="object"&&!(S instanceof Date)?s[A]=S:(typeof S=="number"?g="n":typeof S=="boolean"?g="b":typeof S=="string"?g="s":S instanceof Date?(g="d",a.cellDates||(g="n",S=nr(S)),x=a.dateNF||be[14]):S===null&&a.nullError&&(g="e",S=0),l?(l.t=g,l.v=S,delete l.w,delete l.R,x&&(l.z=x)):s[A]=l={t:g,v:S},x&&(l.z=x))})}),f.e.c=Math.max(f.e.c,o+h.length-1);var m=Je(i);if(n)for(d=0;d<h.length;++d)s[Ve(d+o)+m]={t:"s",v:h[d]};return s["!ref"]=we(f),s}function Fg(e,t){return pl(null,e,t)}function Ma(e,t,r){if(typeof t=="string"){if(Array.isArray(e)){var a=je(t);return e[a.r]||(e[a.r]=[]),e[a.r][a.c]||(e[a.r][a.c]={t:"z"})}return e[t]||(e[t]={t:"z"})}return typeof t!="number"?Ma(e,ve(t)):Ma(e,ve({r:t,c:r||0}))}function Pg(e,t){if(typeof t=="number"){if(t>=0&&e.SheetNames.length>t)return t;throw new Error("Cannot find sheet # "+t)}else if(typeof t=="string"){var r=e.SheetNames.indexOf(t);if(r>-1)return r;throw new Error("Cannot find sheet name |"+t+"|")}else throw new Error("Cannot find sheet |"+t+"|")}function js(){return{SheetNames:[],Sheets:{}}}function $s(e,t,r,a){var n=1;if(!r)for(;n<=65535&&e.SheetNames.indexOf(r="Sheet"+n)!=-1;++n,r=void 0);if(!r||e.SheetNames.length>=65535)throw new Error("Too many worksheets");if(a&&e.SheetNames.indexOf(r)>=0){var s=r.match(/(^.*?)(\d+)$/);n=s&&+s[2]||0;var i=s&&s[1]||r;for(++n;n<=65535&&e.SheetNames.indexOf(r=i+n)!=-1;++n);}if(Zc(r),e.SheetNames.indexOf(r)>=0)throw new Error("Worksheet with name |"+r+"| already exists!");return e.SheetNames.push(r),e.Sheets[r]=t,r}function Dg(e,t,r){e.Workbook||(e.Workbook={}),e.Workbook.Sheets||(e.Workbook.Sheets=[]);var a=Pg(e,t);switch(e.Workbook.Sheets[a]||(e.Workbook.Sheets[a]={}),r){case 0:case 1:case 2:break;default:throw new Error("Bad sheet visibility setting "+r)}e.Workbook.Sheets[a].Hidden=r}function Lg(e,t){return e.z=t,e}function ml(e,t,r){return t?(e.l={Target:t},r&&(e.l.Tooltip=r)):delete e.l,e}function Mg(e,t,r){return ml(e,"#"+t,r)}function Ug(e,t,r){e.c||(e.c=[]),e.c.push({t,a:r||"SheetJS"})}function Bg(e,t,r,a){for(var n=typeof t!="string"?t:Re(t),s=typeof t=="string"?t:we(t),i=n.s.r;i<=n.e.r;++i)for(var o=n.s.c;o<=n.e.c;++o){var c=Ma(e,i,o);c.t="n",c.F=s,delete c.v,i==n.s.r&&o==n.s.c&&(c.f=r,a&&(c.D=!0))}return e}var ns={encode_col:Ve,encode_row:Je,encode_cell:ve,encode_range:we,decode_col:Ts,decode_row:ks,split_cell:Kf,decode_cell:je,decode_range:xr,format_cell:ct,sheet_add_aoa:Ho,sheet_add_json:pl,sheet_add_dom:sl,aoa_to_sheet:ia,json_to_sheet:Fg,table_to_sheet:il,table_to_book:jv,sheet_to_csv:Vs,sheet_to_txt:dl,sheet_to_json:bn,sheet_to_html:nl,sheet_to_formulae:Ig,sheet_to_row_object_array:bn,sheet_get_cell:Ma,book_new:js,book_append_sheet:$s,book_set_sheet_visibility:Dg,cell_set_number_format:Lg,cell_set_hyperlink:ml,cell_set_internal_link:Mg,cell_add_comment:Ug,sheet_set_array_formula:Bg,consts:{SHEET_VISIBLE:0,SHEET_HIDDEN:1,SHEET_VERY_HIDDEN:2}};function Wg(e,t){var r=Date.parse(e);return(r-new Date(Date.UTC(1899,11,30)))/(24*60*60*1e3)}function zg(e,t={}){for(var r={},a={s:{c:1e7,r:1e7},e:{c:0,r:0}},n=0;n!=e.length;++n)for(var s=0;s!=e[n].length;++s){a.s.r>n&&(a.s.r=n),a.s.c>s&&(a.s.c=s),a.e.r<n&&(a.e.r=n),a.e.c<s&&(a.e.c=s);var i={v:e[n][s]};if(i.v!=null){var o=ns.encode_cell({c:s,r:n});typeof i.v=="number"?i.t="n":typeof i.v=="boolean"?i.t="b":i.v instanceof Date?(i.t="n",i.z=hf._table[14],i.v=Wg(i.v)):i.t="s",r[o]=i}}return a.s.c<1e7&&(r["!ref"]=ns.encode_range(a)),r}function ss(){if(!(this instanceof ss))return new ss;this.SheetNames=[],this.Sheets={}}function Hg(e){for(var t=new ArrayBuffer(e.length),r=new Uint8Array(t),a=0;a!=e.length;++a)r[a]=e.charCodeAt(a)&255;return t}function Gg(e,t,r,a={merges:[],header:null,cols:null}){var n=t;n.unshift(e),a.header&&n.unshift([a.header]);var s="SheetJS";const i={};var o=new ss,c=zg(n,i);let{merges:l}=a;typeof l[0]=="string"&&l.length==2&&(l=[l]),l=l.map(h=>h instanceof Array?{s:h[0],e:h[1]}:h),c["!merges"]=l,a.cols&&(c["!cols"]=a.cols),o.SheetNames.push(s),o.Sheets[s]=c;var f=ul(o,{bookType:"xlsx",bookSST:!1,type:"binary"}),p=r||"列表";so.saveAs(new Blob([Hg(f)],{type:"application/octet-stream"}),p+".xlsx")}const Vg=e=>{const t=e.split(/\r\n|\n/),r=t[0].split(/,(?![^"]*"(?:(?:[^"]*"){2})*[^"]*$)/),a=[];for(let s=1;s<t.length;s++){const i=t[s].split(/,(?![^"]*"(?:(?:[^"]*"){2})*[^"]*$)/);if(r&&i.length==r.length){const o={};for(let c=0;c<r.length;c++){let l=i[c];l.length>0&&(l[0]=='"'&&(l=l.substring(1,l.length-1)),l[l.length-1]=='"'&&(l=l.substring(l.length-2,1))),r[c]&&(o[r[c]]=l)}Object.values(o).filter(c=>c).length>0&&a.push(o)}}const n=r.map(s=>({title:s,key:s}));return{data:a,columns:n}};async function jg(e){const t=new FileReader;return new Promise((r,a)=>{t.onload=n=>{const s=n.target.result,i=as(s,{type:"binary"}),o=i.SheetNames[0],c=i.Sheets[o],l=ns.sheet_to_csv(c),f=Vg(l);r(f)},t.onerror=n=>{a(n)},t.readAsBinaryString(e)})}function $g(e){return e.map(t=>({prop:t.key,label:t.title}))}const Kg={csv(e){return new Promise((t,r)=>{const a=Object.assign({},{columns:[],data:[],filename:"table",noHeader:!1},e),n=Dl($g(a.columns),a.data,e,a.noHeader);Il.download(a.filename,n),t()})},excel(e){return new Promise((t,r)=>{const a=Object.assign({},{columns:[],data:[],filename:"table",header:null,merges:[]},e),n=a.columns.map(o=>o.title),s=a.data.map(o=>a.columns.map(c=>o[c.key])),i=a.columns.map(o=>{const c={...o};return delete c.title,delete c.key,c});Gg(n,s,a.filename,{merges:a.merges,header:a.header,cols:i}),t()})},txt(e){return new Promise((t,r)=>{const a=Object.assign({},{text:"",filename:"文本"},e),n=new Blob([a.text],{type:"text/plain;charset=utf-8"});so.saveAs(n,a.filename+".txt"),t()})}},Jg={async csv(e){return await jg(e)}};export{Kg as exportUtil,Jg as importUtil};
