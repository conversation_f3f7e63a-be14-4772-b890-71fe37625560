import{r as a,v as n,g as s}from"./index.BHZI5pdK.js";import"./vue.BNx9QYep.js";const l="/api/system/file/";function u(t){return a({url:l,method:"get",params:t})}function d(t){return a({url:l,method:"post",data:t})}function c(t){return a({url:l+t.id+"/",method:"put",data:t})}function m(t){return a({url:l+t+"/",method:"delete",data:{id:t}})}const g=function({crudExpose:t,context:r}){return{crudOptions:{actionbar:{buttons:{add:{show:!0,click:()=>{var e;return(e=r.openAddHandle)==null?void 0:e.call(r)}}}},request:{pageRequest:async e=>await u(e),addRequest:async({form:e})=>await d(e),editRequest:async({form:e,row:i})=>(e.id=i.id,await c(e)),delRequest:async({row:e})=>await m(e.id)},tabs:{show:!0,name:"file_type",type:"",options:[{value:0,label:"图片"},{value:1,label:"视频"},{value:2,label:"音频"},{value:3,label:"其他"}]},rowHandle:{fixed:"right",width:200,show:!1,buttons:{view:{show:!1},edit:{iconRight:"Edit",type:"text"},remove:{iconRight:"Delete",type:"text"}}},columns:{_index:{title:"序号",form:{show:!1},column:{align:"center",width:"70px",columnSetDisabled:!0,formatter:e=>{let i=e.index??1,o=t.crudBinding.value.pagination;return((o.currentPage??1)-1)*o.pageSize+i+1}}},search:{title:"关键词",column:{show:!1},search:{show:!0,component:{props:{clearable:!0},placeholder:"请输入关键词"}},form:{show:!1,component:{props:{clearable:!0}}}},name:{title:"文件名称",search:{show:!0},type:"input",column:{minWidth:200},form:{component:{placeholder:"请输入文件名称",clearable:!0}}},preview:{title:"预览",column:{minWidth:120,align:"center"},form:{show:!1}},url:{title:"文件地址",type:"file-uploader",search:{disabled:!0},column:{minWidth:360,component:{async buildUrl(e){return s(e)}}}},md5sum:{title:"文件MD5",search:{disabled:!0},column:{minWidth:300},form:{disabled:!1}},mime_type:{title:"文件类型",type:"input",form:{show:!1},column:{minWidth:160}},file_type:{title:"文件类型",type:"dict-select",dict:n({data:[{label:"图片",value:0,color:"success"},{label:"视频",value:1,color:"warning"},{label:"音频",value:2,color:"danger"},{label:"其他",value:3,color:"primary"}]}),column:{show:!1},search:{show:!0},form:{show:!1,component:{placeholder:"请选择文件类型"}}},size:{title:"文件大小",column:{minWidth:120},form:{show:!1}},upload_method:{title:"上传方式",type:"dict-select",dict:n({data:[{label:"默认上传",value:0,color:"primary"},{label:"文件选择器上传",value:1,color:"warning"}]}),column:{minWidth:140},search:{show:!0}},create_datetime:{title:"创建时间",column:{minWidth:160},form:{show:!1}}}}}};export{g as createCrudOptions};
