import{d as N,M as R,Q as A,R as D,g as V,h as E,c as $,j as O,a0 as j,k as t,a as P,o as k,l as a,m as q,w as o,b as z,e as F,f as L,s as Q,_ as J,F as G}from"./vue.BNx9QYep.js";import{a7 as H,u as W,a6 as U,O as T,g as X,a8 as Y,S as Z,a9 as x,aa as ee,N as ae,D as oe,e as se,Q as ne}from"./index.BHZI5pdK.js";import{a as te}from"./formatTime.in1fXasu.js";import{g as K,l as le}from"./api.DfN45YxI.js";import{M as re}from"./md5.DLPczxzP.js";import{_ as ce}from"./_plugin-vue_export-helper.DlAUqK2U.js";const ie=N({name:"loginAccount",setup(){const{t:e}=H.useI18n(),s=W(),{themeConfig:S}=R(s),{userInfos:I}=R(U()),p=A(),_=D(),n=V({isShowPassword:!1,ruleForm:{username:"",password:"",captcha:"",captchaKey:"",captchaImgBase:""},loading:{signIn:!1}}),m=V({username:[{required:!0,message:"请填写账号",trigger:"blur"}],password:[{required:!0,message:"请填写密码",trigger:"blur"}],captcha:[{required:!0,message:"请填写验证码",trigger:"blur"}]}),r=E(),i=$(()=>te(new Date)),w=$(()=>T().systemConfig["base.captcha_state"]),C=async()=>{K().then(l=>{n.ruleForm.captchaImgBase=l.data.image_base,n.ruleForm.captchaKey=l.data.key})},d=async()=>{window.open(X("/api/system/apply_for_trial/"))},y=async()=>{n.ruleForm.captcha="",K().then(l=>{n.ruleForm.captchaImgBase=l.data.image_base,n.ruleForm.captchaKey=l.data.key})},g=async()=>{r.value&&await r.value.validate(l=>{l?le({...n.ruleForm,password:re.hashStr(n.ruleForm.password)}).then(u=>{if(u.code===2e3){const{data:h}=u;if(Y.set("username",u.data.username),Z.set("token",u.data.access),U().setPwdChangeCount(h.pwd_change_count),h.pwd_change_count==0)return _.push("/login");S.value.isRequestRoutes?(ee(),f()):(x(),f())}}).catch(u=>{y()}):ae("请填写登录信息")})},f=()=>{var h,b,B,v;oe().getSystemDictionarys();let l=i.value;if(I.value.pwd_change_count>0){(h=p.query)!=null&&h.redirect?_.push({path:(b=p.query)==null?void 0:b.redirect,query:Object.keys((B=p.query)==null?void 0:B.params).length>0?JSON.parse((v=p.query)==null?void 0:v.params):""}):_.push("/"),n.loading.signIn=!0;const M=e("message.signInText");se.success(`${l}，${M}`)}ne.start()};return O(()=>{C(),T().getSystemConfigs()}),{refreshCaptcha:y,loginClick:g,loginSuccess:f,isShowCaptcha:w,state:n,formRef:r,rules:m,applyBtnClick:d,showApply:()=>window.location.href.indexOf("public")!=-1,...j(n)}}}),ue={key:0,style:{"text-align":"center"}};function pe(e,s,S,I,p,_){const n=t("ele-User"),m=t("el-icon"),r=t("el-input"),i=t("el-form-item"),w=t("ele-Unlock"),C=t("ele-Position"),d=t("el-col"),y=t("el-image"),g=t("el-button"),f=t("el-form");return k(),P(G,null,[a(f,{ref:"formRef",size:"large",class:"login-content-form",model:e.state.ruleForm,rules:e.rules,onKeyup:J(e.loginClick,["enter"])},{default:o(()=>[a(i,{class:"login-animation1",prop:"username"},{default:o(()=>[a(r,{type:"text",placeholder:e.$t("message.account.accountPlaceholder1"),modelValue:e.ruleForm.username,"onUpdate:modelValue":s[0]||(s[0]=c=>e.ruleForm.username=c),clearable:"",autocomplete:"off"},{prefix:o(()=>[a(m,{class:"el-input__icon"},{default:o(()=>[a(n)]),_:1})]),_:1},8,["placeholder","modelValue"])]),_:1}),a(i,{class:"login-animation2",prop:"password"},{default:o(()=>[a(r,{type:e.isShowPassword?"text":"password",placeholder:e.$t("message.account.accountPlaceholder2"),modelValue:e.ruleForm.password,"onUpdate:modelValue":s[2]||(s[2]=c=>e.ruleForm.password=c)},{prefix:o(()=>[a(m,{class:"el-input__icon"},{default:o(()=>[a(w)]),_:1})]),suffix:o(()=>[F("i",{class:L(["iconfont el-input__icon login-content-password",e.isShowPassword?"icon-yincangmima":"icon-xianshimima"]),onClick:s[1]||(s[1]=c=>e.isShowPassword=!e.isShowPassword)},null,2)]),_:1},8,["type","placeholder","modelValue"])]),_:1}),e.isShowCaptcha?(k(),z(i,{key:0,class:"login-animation3",prop:"captcha"},{default:o(()=>[a(d,{span:15},{default:o(()=>[a(r,{type:"text",maxlength:"4",placeholder:e.$t("message.account.accountPlaceholder3"),modelValue:e.ruleForm.captcha,"onUpdate:modelValue":s[3]||(s[3]=c=>e.ruleForm.captcha=c),clearable:"",autocomplete:"off"},{prefix:o(()=>[a(m,{class:"el-input__icon"},{default:o(()=>[a(C)]),_:1})]),_:1},8,["placeholder","modelValue"])]),_:1}),a(d,{span:1}),a(d,{span:8},{default:o(()=>[a(g,{class:"login-content-captcha"},{default:o(()=>[a(y,{src:e.ruleForm.captchaImgBase,onClick:e.refreshCaptcha},null,8,["src","onClick"])]),_:1})]),_:1})]),_:1})):q("",!0),a(i,{class:"login-animation4"},{default:o(()=>[a(g,{type:"primary",class:"login-content-submit",round:"",onClick:e.loginClick,loading:e.loading.signIn},{default:o(()=>[F("span",null,Q(e.$t("message.account.accountBtnText")),1)]),_:1},8,["onClick","loading"])]),_:1})]),_:1},8,["model","rules","onKeyup"]),e.showApply()?(k(),P("div",ue,[a(g,{class:"login-content-apply",link:"",type:"primary",plain:"",round:"",onClick:e.applyBtnClick},{default:o(()=>s[4]||(s[4]=[F("span",null,"申请试用",-1)])),_:1,__:[4]},8,["onClick"])])):q("",!0)],64)}const ye=ce(ie,[["render",pe],["__scopeId","data-v-834944f6"]]);export{ye as default};
