import { pluginsAll } from '/@/views/plugins/index';

/**
 * @description 校验是否为租户模式。租户模式把域名替换成 域名 加端口
 */
export const getBaseURL = function (url: null | string = null, isHost: null | boolean = null) {
	let baseURL = import.meta.env.VITE_API_URL as any;
	// 如果需要host返回，时，返回地址前缀加http地址
	if (isHost && !baseURL.startsWith('http')) {
		baseURL = window.location.protocol + '//' + window.location.host + baseURL
	}
	let param = baseURL.split('/')[3] || '';
	// @ts-ignore
	if (pluginsAll && pluginsAll.indexOf('dvadmin3-tenants-web') !== -1 && (!param || baseURL.startsWith('/'))) {
		// 1.把127.0.0.1 替换成和前端一样域名
		// 2.把 ip 地址替换成和前端一样域名
		// 3.把 /api 或其他类似的替换成和前端一样域名
		// document.domain

		var host = baseURL.split('/')[2];
		if (host) {
			var port = baseURL.split(':')[2] || 80;
			if (port === 80 || port === 443) {
				host = document.domain;
			} else {
				host = document.domain + ':' + port;
			}
			baseURL = baseURL.split('/')[0] + '//' + baseURL.split('/')[1] + host + '/' + param;
		} else {
			baseURL = location.protocol + '//' + location.hostname + (location.port ? ':' : '') + location.port + baseURL;
		}
	}
	if (url) {
		const regex = /^(http|https):\/\//;
		if (regex.test(url)) {
			return url
		} else {
			// js判断是否是斜杠结尾
			return baseURL.replace(/\/$/, '') + '/' + url.replace(/^\//, '');
		}
	}
	if (!baseURL.endsWith('/')) {
		baseURL += '/';
	}
	return baseURL;
};

export const getWsBaseURL = function () {
	// 直接使用当前页面的主机和端口，简化WebSocket连接
	const protocol = location.protocol === 'https:' ? 'wss://' : 'ws://';
	const host = location.hostname;
	const port = location.port ? `:${location.port}` : '';

	// 直接构建WebSocket URL，指向后端服务器
	const baseURL = `${protocol}${host}${port}/`;

	console.log('WebSocket基础URL:', baseURL);
	return baseURL;
};
