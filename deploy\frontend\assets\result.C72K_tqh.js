import{ae as dr,aB as mr}from"./index.BHZI5pdK.js";import{d as yr,h as ie,v as pr,j as gr,a as Fe,o as Ve,e as Dt,s as Et,u as wr,F as Tr,p as Sr}from"./vue.BNx9QYep.js";import{_ as Or}from"./_plugin-vue_export-helper.DlAUqK2U.js";var W={};Object.defineProperty(W,"__esModule",{value:!0});class se extends Error{}class kr extends se{constructor(e){super(`Invalid DateTime: ${e.toMessage()}`)}}class vr extends se{constructor(e){super(`Invalid Interval: ${e.toMessage()}`)}}class Mr extends se{constructor(e){super(`Invalid Duration: ${e.toMessage()}`)}}class le extends se{}class an extends se{constructor(e){super(`Invalid unit ${e}`)}}class C extends se{}class P extends se{constructor(){super("Zone is an abstract class")}}const f="numeric",H="short",V="long",Re={year:f,month:f,day:f},on={year:f,month:H,day:f},Nr={year:f,month:H,day:f,weekday:H},un={year:f,month:V,day:f},ln={year:f,month:V,day:f,weekday:V},cn={hour:f,minute:f},fn={hour:f,minute:f,second:f},hn={hour:f,minute:f,second:f,timeZoneName:H},dn={hour:f,minute:f,second:f,timeZoneName:V},mn={hour:f,minute:f,hourCycle:"h23"},yn={hour:f,minute:f,second:f,hourCycle:"h23"},pn={hour:f,minute:f,second:f,hourCycle:"h23",timeZoneName:H},gn={hour:f,minute:f,second:f,hourCycle:"h23",timeZoneName:V},wn={year:f,month:f,day:f,hour:f,minute:f},Tn={year:f,month:f,day:f,hour:f,minute:f,second:f},Sn={year:f,month:H,day:f,hour:f,minute:f},On={year:f,month:H,day:f,hour:f,minute:f,second:f},Dr={year:f,month:H,day:f,weekday:H,hour:f,minute:f},kn={year:f,month:V,day:f,hour:f,minute:f,timeZoneName:H},vn={year:f,month:V,day:f,hour:f,minute:f,second:f,timeZoneName:H},Mn={year:f,month:V,day:f,weekday:V,hour:f,minute:f,timeZoneName:V},Nn={year:f,month:V,day:f,weekday:V,hour:f,minute:f,second:f,timeZoneName:V};class me{get type(){throw new P}get name(){throw new P}get ianaName(){return this.name}get isUniversal(){throw new P}offsetName(e,n){throw new P}formatOffset(e,n){throw new P}offset(e){throw new P}equals(e){throw new P}get isValid(){throw new P}}let Qe=null;class Ie extends me{static get instance(){return Qe===null&&(Qe=new Ie),Qe}get type(){return"system"}get name(){return new Intl.DateTimeFormat().resolvedOptions().timeZone}get isUniversal(){return!1}offsetName(e,{format:n,locale:r}){return $n(e,n,r)}formatOffset(e,n){return Ee(this.offset(e),n)}offset(e){return-new Date(e).getTimezoneOffset()}equals(e){return e.type==="system"}get isValid(){return!0}}const ot=new Map;function Er(t){let e=ot.get(t);return e===void 0&&(e=new Intl.DateTimeFormat("en-US",{hour12:!1,timeZone:t,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",era:"short"}),ot.set(t,e)),e}const xr={year:0,month:1,day:2,era:3,hour:4,minute:5,second:6};function Ir(t,e){const n=t.format(e).replace(/\u200E/g,""),r=/(\d+)\/(\d+)\/(\d+) (AD|BC),? (\d+):(\d+):(\d+)/.exec(n),[,s,a,i,o,u,l,c]=r;return[i,s,a,o,u,l,c]}function _r(t,e){const n=t.formatToParts(e),r=[];for(let s=0;s<n.length;s++){const{type:a,value:i}=n[s],o=xr[a];a==="era"?r[o]=i:w(o)||(r[o]=parseInt(i,10))}return r}const Ke=new Map;class q extends me{static create(e){let n=Ke.get(e);return n===void 0&&Ke.set(e,n=new q(e)),n}static resetCache(){Ke.clear(),ot.clear()}static isValidSpecifier(e){return this.isValidZone(e)}static isValidZone(e){if(!e)return!1;try{return new Intl.DateTimeFormat("en-US",{timeZone:e}).format(),!0}catch{return!1}}constructor(e){super(),this.zoneName=e,this.valid=q.isValidZone(e)}get type(){return"iana"}get name(){return this.zoneName}get isUniversal(){return!1}offsetName(e,{format:n,locale:r}){return $n(e,n,r,this.name)}formatOffset(e,n){return Ee(this.offset(e),n)}offset(e){if(!this.valid)return NaN;const n=new Date(e);if(isNaN(n))return NaN;const r=Er(this.name);let[s,a,i,o,u,l,c]=r.formatToParts?_r(r,n):Ir(r,n);o==="BC"&&(s=-Math.abs(s)+1);const p=Je({year:s,month:a,day:i,hour:u===24?0:u,minute:l,second:c,millisecond:0});let h=+n;const y=h%1e3;return h-=y>=0?y:1e3+y,(p-h)/(60*1e3)}equals(e){return e.type==="iana"&&e.name===this.name}get isValid(){return this.valid}}let xt={};function Cr(t,e={}){const n=JSON.stringify([t,e]);let r=xt[n];return r||(r=new Intl.ListFormat(t,e),xt[n]=r),r}const ut=new Map;function lt(t,e={}){const n=JSON.stringify([t,e]);let r=ut.get(n);return r===void 0&&(r=new Intl.DateTimeFormat(t,e),ut.set(n,r)),r}const ct=new Map;function br(t,e={}){const n=JSON.stringify([t,e]);let r=ct.get(n);return r===void 0&&(r=new Intl.NumberFormat(t,e),ct.set(n,r)),r}const ft=new Map;function Fr(t,e={}){const{base:n,...r}=e,s=JSON.stringify([t,r]);let a=ft.get(s);return a===void 0&&(a=new Intl.RelativeTimeFormat(t,e),ft.set(s,a)),a}let ve=null;function Vr(){return ve||(ve=new Intl.DateTimeFormat().resolvedOptions().locale,ve)}const ht=new Map;function Dn(t){let e=ht.get(t);return e===void 0&&(e=new Intl.DateTimeFormat(t).resolvedOptions(),ht.set(t,e)),e}const dt=new Map;function Wr(t){let e=dt.get(t);if(!e){const n=new Intl.Locale(t);e="getWeekInfo"in n?n.getWeekInfo():n.weekInfo,"minimalDays"in e||(e={...En,...e}),dt.set(t,e)}return e}function Lr(t){const e=t.indexOf("-x-");e!==-1&&(t=t.substring(0,e));const n=t.indexOf("-u-");if(n===-1)return[t];{let r,s;try{r=lt(t).resolvedOptions(),s=t}catch{const u=t.substring(0,n);r=lt(u).resolvedOptions(),s=u}const{numberingSystem:a,calendar:i}=r;return[s,a,i]}}function $r(t,e,n){return(n||e)&&(t.includes("-u-")||(t+="-u"),n&&(t+=`-ca-${n}`),e&&(t+=`-nu-${e}`)),t}function Ar(t){const e=[];for(let n=1;n<=12;n++){const r=g.utc(2009,n,1);e.push(t(r))}return e}function Zr(t){const e=[];for(let n=1;n<=7;n++){const r=g.utc(2016,11,13+n);e.push(t(r))}return e}function We(t,e,n,r){const s=t.listingMode();return s==="error"?null:s==="en"?n(e):r(e)}function zr(t){return t.numberingSystem&&t.numberingSystem!=="latn"?!1:t.numberingSystem==="latn"||!t.locale||t.locale.startsWith("en")||Dn(t.locale).numberingSystem==="latn"}class Ur{constructor(e,n,r){this.padTo=r.padTo||0,this.floor=r.floor||!1;const{padTo:s,floor:a,...i}=r;if(!n||Object.keys(i).length>0){const o={useGrouping:!1,...r};r.padTo>0&&(o.minimumIntegerDigits=r.padTo),this.inf=br(e,o)}}format(e){if(this.inf){const n=this.floor?Math.floor(e):e;return this.inf.format(n)}else{const n=this.floor?Math.floor(e):kt(e,3);return I(n,this.padTo)}}}class Rr{constructor(e,n,r){this.opts=r,this.originalZone=void 0;let s;if(this.opts.timeZone)this.dt=e;else if(e.zone.type==="fixed"){const i=-1*(e.offset/60),o=i>=0?`Etc/GMT+${i}`:`Etc/GMT${i}`;e.offset!==0&&q.create(o).valid?(s=o,this.dt=e):(s="UTC",this.dt=e.offset===0?e:e.setZone("UTC").plus({minutes:e.offset}),this.originalZone=e.zone)}else e.zone.type==="system"?this.dt=e:e.zone.type==="iana"?(this.dt=e,s=e.zone.name):(s="UTC",this.dt=e.setZone("UTC").plus({minutes:e.offset}),this.originalZone=e.zone);const a={...this.opts};a.timeZone=a.timeZone||s,this.dtf=lt(n,a)}format(){return this.originalZone?this.formatToParts().map(({value:e})=>e).join(""):this.dtf.format(this.dt.toJSDate())}formatToParts(){const e=this.dtf.formatToParts(this.dt.toJSDate());return this.originalZone?e.map(n=>{if(n.type==="timeZoneName"){const r=this.originalZone.offsetName(this.dt.ts,{locale:this.dt.locale,format:this.opts.timeZoneName});return{...n,value:r}}else return n}):e}resolvedOptions(){return this.dtf.resolvedOptions()}}class Hr{constructor(e,n,r){this.opts={style:"long",...r},!n&&Wn()&&(this.rtf=Fr(e,r))}format(e,n){return this.rtf?this.rtf.format(e,n):cs(n,e,this.opts.numeric,this.opts.style!=="long")}formatToParts(e,n){return this.rtf?this.rtf.formatToParts(e,n):[]}}const En={firstDay:1,minimalDays:4,weekend:[6,7]};class M{static fromOpts(e){return M.create(e.locale,e.numberingSystem,e.outputCalendar,e.weekSettings,e.defaultToEN)}static create(e,n,r,s,a=!1){const i=e||x.defaultLocale,o=i||(a?"en-US":Vr()),u=n||x.defaultNumberingSystem,l=r||x.defaultOutputCalendar,c=yt(s)||x.defaultWeekSettings;return new M(o,u,l,c,i)}static resetCache(){ve=null,ut.clear(),ct.clear(),ft.clear(),ht.clear(),dt.clear()}static fromObject({locale:e,numberingSystem:n,outputCalendar:r,weekSettings:s}={}){return M.create(e,n,r,s)}constructor(e,n,r,s,a){const[i,o,u]=Lr(e);this.locale=i,this.numberingSystem=n||o||null,this.outputCalendar=r||u||null,this.weekSettings=s,this.intl=$r(this.locale,this.numberingSystem,this.outputCalendar),this.weekdaysCache={format:{},standalone:{}},this.monthsCache={format:{},standalone:{}},this.meridiemCache=null,this.eraCache={},this.specifiedLocale=a,this.fastNumbersCached=null}get fastNumbers(){return this.fastNumbersCached==null&&(this.fastNumbersCached=zr(this)),this.fastNumbersCached}listingMode(){const e=this.isEnglish(),n=(this.numberingSystem===null||this.numberingSystem==="latn")&&(this.outputCalendar===null||this.outputCalendar==="gregory");return e&&n?"en":"intl"}clone(e){return!e||Object.getOwnPropertyNames(e).length===0?this:M.create(e.locale||this.specifiedLocale,e.numberingSystem||this.numberingSystem,e.outputCalendar||this.outputCalendar,yt(e.weekSettings)||this.weekSettings,e.defaultToEN||!1)}redefaultToEN(e={}){return this.clone({...e,defaultToEN:!0})}redefaultToSystem(e={}){return this.clone({...e,defaultToEN:!1})}months(e,n=!1){return We(this,e,zn,()=>{const r=n?{month:e,day:"numeric"}:{month:e},s=n?"format":"standalone";return this.monthsCache[s][e]||(this.monthsCache[s][e]=Ar(a=>this.extract(a,r,"month"))),this.monthsCache[s][e]})}weekdays(e,n=!1){return We(this,e,Hn,()=>{const r=n?{weekday:e,year:"numeric",month:"long",day:"numeric"}:{weekday:e},s=n?"format":"standalone";return this.weekdaysCache[s][e]||(this.weekdaysCache[s][e]=Zr(a=>this.extract(a,r,"weekday"))),this.weekdaysCache[s][e]})}meridiems(){return We(this,void 0,()=>qn,()=>{if(!this.meridiemCache){const e={hour:"numeric",hourCycle:"h12"};this.meridiemCache=[g.utc(2016,11,13,9),g.utc(2016,11,13,19)].map(n=>this.extract(n,e,"dayperiod"))}return this.meridiemCache})}eras(e){return We(this,e,Yn,()=>{const n={era:e};return this.eraCache[e]||(this.eraCache[e]=[g.utc(-40,1,1),g.utc(2017,1,1)].map(r=>this.extract(r,n,"era"))),this.eraCache[e]})}extract(e,n,r){const s=this.dtFormatter(e,n),a=s.formatToParts(),i=a.find(o=>o.type.toLowerCase()===r);return i?i.value:null}numberFormatter(e={}){return new Ur(this.intl,e.forceSimple||this.fastNumbers,e)}dtFormatter(e,n={}){return new Rr(e,this.intl,n)}relFormatter(e={}){return new Hr(this.intl,this.isEnglish(),e)}listFormatter(e={}){return Cr(this.intl,e)}isEnglish(){return this.locale==="en"||this.locale.toLowerCase()==="en-us"||Dn(this.intl).locale.startsWith("en-us")}getWeekSettings(){return this.weekSettings?this.weekSettings:Ln()?Wr(this.locale):En}getStartOfWeek(){return this.getWeekSettings().firstDay}getMinDaysInFirstWeek(){return this.getWeekSettings().minimalDays}getWeekendDays(){return this.getWeekSettings().weekend}equals(e){return this.locale===e.locale&&this.numberingSystem===e.numberingSystem&&this.outputCalendar===e.outputCalendar}toString(){return`Locale(${this.locale}, ${this.numberingSystem}, ${this.outputCalendar})`}}let Xe=null;class F extends me{static get utcInstance(){return Xe===null&&(Xe=new F(0)),Xe}static instance(e){return e===0?F.utcInstance:new F(e)}static parseSpecifier(e){if(e){const n=e.match(/^utc(?:([+-]\d{1,2})(?::(\d{2}))?)?$/i);if(n)return new F(je(n[1],n[2]))}return null}constructor(e){super(),this.fixed=e}get type(){return"fixed"}get name(){return this.fixed===0?"UTC":`UTC${Ee(this.fixed,"narrow")}`}get ianaName(){return this.fixed===0?"Etc/UTC":`Etc/GMT${Ee(-this.fixed,"narrow")}`}offsetName(){return this.name}formatOffset(e,n){return Ee(this.fixed,n)}get isUniversal(){return!0}offset(){return this.fixed}equals(e){return e.type==="fixed"&&e.fixed===this.fixed}get isValid(){return!0}}class xn extends me{constructor(e){super(),this.zoneName=e}get type(){return"invalid"}get name(){return this.zoneName}get isUniversal(){return!1}offsetName(){return null}formatOffset(){return""}offset(){return NaN}equals(){return!1}get isValid(){return!1}}function j(t,e){if(w(t)||t===null)return e;if(t instanceof me)return t;if(jr(t)){const n=t.toLowerCase();return n==="default"?e:n==="local"||n==="system"?Ie.instance:n==="utc"||n==="gmt"?F.utcInstance:F.parseSpecifier(n)||q.create(t)}else return B(t)?F.instance(t):typeof t=="object"&&"offset"in t&&typeof t.offset=="function"?t:new xn(t)}const wt={arab:"[٠-٩]",arabext:"[۰-۹]",bali:"[᭐-᭙]",beng:"[০-৯]",deva:"[०-९]",fullwide:"[０-９]",gujr:"[૦-૯]",hanidec:"[〇|一|二|三|四|五|六|七|八|九]",khmr:"[០-៩]",knda:"[೦-೯]",laoo:"[໐-໙]",limb:"[᥆-᥏]",mlym:"[൦-൯]",mong:"[᠐-᠙]",mymr:"[၀-၉]",orya:"[୦-୯]",tamldec:"[௦-௯]",telu:"[౦-౯]",thai:"[๐-๙]",tibt:"[༠-༩]",latn:"\\d"},It={arab:[1632,1641],arabext:[1776,1785],bali:[6992,7001],beng:[2534,2543],deva:[2406,2415],fullwide:[65296,65303],gujr:[2790,2799],khmr:[6112,6121],knda:[3302,3311],laoo:[3792,3801],limb:[6470,6479],mlym:[3430,3439],mong:[6160,6169],mymr:[4160,4169],orya:[2918,2927],tamldec:[3046,3055],telu:[3174,3183],thai:[3664,3673],tibt:[3872,3881]},qr=wt.hanidec.replace(/[\[|\]]/g,"").split("");function Yr(t){let e=parseInt(t,10);if(isNaN(e)){e="";for(let n=0;n<t.length;n++){const r=t.charCodeAt(n);if(t[n].search(wt.hanidec)!==-1)e+=qr.indexOf(t[n]);else for(const s in It){const[a,i]=It[s];r>=a&&r<=i&&(e+=r-a)}}return parseInt(e,10)}else return e}const mt=new Map;function Pr(){mt.clear()}function z({numberingSystem:t},e=""){const n=t||"latn";let r=mt.get(n);r===void 0&&(r=new Map,mt.set(n,r));let s=r.get(e);return s===void 0&&(s=new RegExp(`${wt[n]}${e}`),r.set(e,s)),s}let _t=()=>Date.now(),Ct="system",bt=null,Ft=null,Vt=null,Wt=60,Lt,$t=null;class x{static get now(){return _t}static set now(e){_t=e}static set defaultZone(e){Ct=e}static get defaultZone(){return j(Ct,Ie.instance)}static get defaultLocale(){return bt}static set defaultLocale(e){bt=e}static get defaultNumberingSystem(){return Ft}static set defaultNumberingSystem(e){Ft=e}static get defaultOutputCalendar(){return Vt}static set defaultOutputCalendar(e){Vt=e}static get defaultWeekSettings(){return $t}static set defaultWeekSettings(e){$t=yt(e)}static get twoDigitCutoffYear(){return Wt}static set twoDigitCutoffYear(e){Wt=e%100}static get throwOnInvalid(){return Lt}static set throwOnInvalid(e){Lt=e}static resetCaches(){M.resetCache(),q.resetCache(),g.resetCache(),Pr()}}class R{constructor(e,n){this.reason=e,this.explanation=n}toMessage(){return this.explanation?`${this.reason}: ${this.explanation}`:this.reason}}const In=[0,31,59,90,120,151,181,212,243,273,304,334],_n=[0,31,60,91,121,152,182,213,244,274,305,335];function $(t,e){return new R("unit out of range",`you specified ${e} (of type ${typeof e}) as a ${t}, which is invalid`)}function Tt(t,e,n){const r=new Date(Date.UTC(t,e-1,n));t<100&&t>=0&&r.setUTCFullYear(r.getUTCFullYear()-1900);const s=r.getUTCDay();return s===0?7:s}function Cn(t,e,n){return n+(_e(t)?_n:In)[e-1]}function bn(t,e){const n=_e(t)?_n:In,r=n.findIndex(a=>a<e),s=e-n[r];return{month:r+1,day:s}}function St(t,e){return(t-e+7)%7+1}function He(t,e=4,n=1){const{year:r,month:s,day:a}=t,i=Cn(r,s,a),o=St(Tt(r,s,a),n);let u=Math.floor((i-o+14-e)/7),l;return u<1?(l=r-1,u=xe(l,e,n)):u>xe(r,e,n)?(l=r+1,u=1):l=r,{weekYear:l,weekNumber:u,weekday:o,...Be(t)}}function At(t,e=4,n=1){const{weekYear:r,weekNumber:s,weekday:a}=t,i=St(Tt(r,1,e),n),o=fe(r);let u=s*7+a-i-7+e,l;u<1?(l=r-1,u+=fe(l)):u>o?(l=r+1,u-=fe(r)):l=r;const{month:c,day:m}=bn(l,u);return{year:l,month:c,day:m,...Be(t)}}function et(t){const{year:e,month:n,day:r}=t,s=Cn(e,n,r);return{year:e,ordinal:s,...Be(t)}}function Zt(t){const{year:e,ordinal:n}=t,{month:r,day:s}=bn(e,n);return{year:e,month:r,day:s,...Be(t)}}function zt(t,e){if(!w(t.localWeekday)||!w(t.localWeekNumber)||!w(t.localWeekYear)){if(!w(t.weekday)||!w(t.weekNumber)||!w(t.weekYear))throw new le("Cannot mix locale-based week fields with ISO-based week fields");return w(t.localWeekday)||(t.weekday=t.localWeekday),w(t.localWeekNumber)||(t.weekNumber=t.localWeekNumber),w(t.localWeekYear)||(t.weekYear=t.localWeekYear),delete t.localWeekday,delete t.localWeekNumber,delete t.localWeekYear,{minDaysInFirstWeek:e.getMinDaysInFirstWeek(),startOfWeek:e.getStartOfWeek()}}else return{minDaysInFirstWeek:4,startOfWeek:1}}function Gr(t,e=4,n=1){const r=Ge(t.weekYear),s=A(t.weekNumber,1,xe(t.weekYear,e,n)),a=A(t.weekday,1,7);return r?s?a?!1:$("weekday",t.weekday):$("week",t.weekNumber):$("weekYear",t.weekYear)}function Jr(t){const e=Ge(t.year),n=A(t.ordinal,1,fe(t.year));return e?n?!1:$("ordinal",t.ordinal):$("year",t.year)}function Fn(t){const e=Ge(t.year),n=A(t.month,1,12),r=A(t.day,1,qe(t.year,t.month));return e?n?r?!1:$("day",t.day):$("month",t.month):$("year",t.year)}function Vn(t){const{hour:e,minute:n,second:r,millisecond:s}=t,a=A(e,0,23)||e===24&&n===0&&r===0&&s===0,i=A(n,0,59),o=A(r,0,59),u=A(s,0,999);return a?i?o?u?!1:$("millisecond",s):$("second",r):$("minute",n):$("hour",e)}function w(t){return typeof t>"u"}function B(t){return typeof t=="number"}function Ge(t){return typeof t=="number"&&t%1===0}function jr(t){return typeof t=="string"}function Br(t){return Object.prototype.toString.call(t)==="[object Date]"}function Wn(){try{return typeof Intl<"u"&&!!Intl.RelativeTimeFormat}catch{return!1}}function Ln(){try{return typeof Intl<"u"&&!!Intl.Locale&&("weekInfo"in Intl.Locale.prototype||"getWeekInfo"in Intl.Locale.prototype)}catch{return!1}}function Qr(t){return Array.isArray(t)?t:[t]}function Ut(t,e,n){if(t.length!==0)return t.reduce((r,s)=>{const a=[e(s),s];return r&&n(r[0],a[0])===r[0]?r:a},null)[1]}function Kr(t,e){return e.reduce((n,r)=>(n[r]=t[r],n),{})}function de(t,e){return Object.prototype.hasOwnProperty.call(t,e)}function yt(t){if(t==null)return null;if(typeof t!="object")throw new C("Week settings must be an object");if(!A(t.firstDay,1,7)||!A(t.minimalDays,1,7)||!Array.isArray(t.weekend)||t.weekend.some(e=>!A(e,1,7)))throw new C("Invalid week settings");return{firstDay:t.firstDay,minimalDays:t.minimalDays,weekend:Array.from(t.weekend)}}function A(t,e,n){return Ge(t)&&t>=e&&t<=n}function Xr(t,e){return t-e*Math.floor(t/e)}function I(t,e=2){const n=t<0;let r;return n?r="-"+(""+-t).padStart(e,"0"):r=(""+t).padStart(e,"0"),r}function J(t){if(!(w(t)||t===null||t===""))return parseInt(t,10)}function K(t){if(!(w(t)||t===null||t===""))return parseFloat(t)}function Ot(t){if(!(w(t)||t===null||t==="")){const e=parseFloat("0."+t)*1e3;return Math.floor(e)}}function kt(t,e,n=!1){const r=10**e;return(n?Math.trunc:Math.round)(t*r)/r}function _e(t){return t%4===0&&(t%100!==0||t%400===0)}function fe(t){return _e(t)?366:365}function qe(t,e){const n=Xr(e-1,12)+1,r=t+(e-n)/12;return n===2?_e(r)?29:28:[31,null,31,30,31,30,31,31,30,31,30,31][n-1]}function Je(t){let e=Date.UTC(t.year,t.month-1,t.day,t.hour,t.minute,t.second,t.millisecond);return t.year<100&&t.year>=0&&(e=new Date(e),e.setUTCFullYear(t.year,t.month-1,t.day)),+e}function Rt(t,e,n){return-St(Tt(t,1,e),n)+e-1}function xe(t,e=4,n=1){const r=Rt(t,e,n),s=Rt(t+1,e,n);return(fe(t)-r+s)/7}function pt(t){return t>99?t:t>x.twoDigitCutoffYear?1900+t:2e3+t}function $n(t,e,n,r=null){const s=new Date(t),a={hourCycle:"h23",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"};r&&(a.timeZone=r);const i={timeZoneName:e,...a},o=new Intl.DateTimeFormat(n,i).formatToParts(s).find(u=>u.type.toLowerCase()==="timezonename");return o?o.value:null}function je(t,e){let n=parseInt(t,10);Number.isNaN(n)&&(n=0);const r=parseInt(e,10)||0,s=n<0||Object.is(n,-0)?-r:r;return n*60+s}function An(t){const e=Number(t);if(typeof t=="boolean"||t===""||Number.isNaN(e))throw new C(`Invalid unit value ${t}`);return e}function Ye(t,e){const n={};for(const r in t)if(de(t,r)){const s=t[r];if(s==null)continue;n[e(r)]=An(s)}return n}function Ee(t,e){const n=Math.trunc(Math.abs(t/60)),r=Math.trunc(Math.abs(t%60)),s=t>=0?"+":"-";switch(e){case"short":return`${s}${I(n,2)}:${I(r,2)}`;case"narrow":return`${s}${n}${r>0?`:${r}`:""}`;case"techie":return`${s}${I(n,2)}${I(r,2)}`;default:throw new RangeError(`Value format ${e} is out of range for property format`)}}function Be(t){return Kr(t,["hour","minute","second","millisecond"])}const es=["January","February","March","April","May","June","July","August","September","October","November","December"],Zn=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],ts=["J","F","M","A","M","J","J","A","S","O","N","D"];function zn(t){switch(t){case"narrow":return[...ts];case"short":return[...Zn];case"long":return[...es];case"numeric":return["1","2","3","4","5","6","7","8","9","10","11","12"];case"2-digit":return["01","02","03","04","05","06","07","08","09","10","11","12"];default:return null}}const Un=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],Rn=["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],ns=["M","T","W","T","F","S","S"];function Hn(t){switch(t){case"narrow":return[...ns];case"short":return[...Rn];case"long":return[...Un];case"numeric":return["1","2","3","4","5","6","7"];default:return null}}const qn=["AM","PM"],rs=["Before Christ","Anno Domini"],ss=["BC","AD"],is=["B","A"];function Yn(t){switch(t){case"narrow":return[...is];case"short":return[...ss];case"long":return[...rs];default:return null}}function as(t){return qn[t.hour<12?0:1]}function os(t,e){return Hn(e)[t.weekday-1]}function us(t,e){return zn(e)[t.month-1]}function ls(t,e){return Yn(e)[t.year<0?0:1]}function cs(t,e,n="always",r=!1){const s={years:["year","yr."],quarters:["quarter","qtr."],months:["month","mo."],weeks:["week","wk."],days:["day","day","days"],hours:["hour","hr."],minutes:["minute","min."],seconds:["second","sec."]},a=["hours","minutes","seconds"].indexOf(t)===-1;if(n==="auto"&&a){const m=t==="days";switch(e){case 1:return m?"tomorrow":`next ${s[t][0]}`;case-1:return m?"yesterday":`last ${s[t][0]}`;case 0:return m?"today":`this ${s[t][0]}`}}const i=Object.is(e,-0)||e<0,o=Math.abs(e),u=o===1,l=s[t],c=r?u?l[1]:l[2]||l[1]:u?s[t][0]:t;return i?`${o} ${c} ago`:`in ${o} ${c}`}function Ht(t,e){let n="";for(const r of t)r.literal?n+=r.val:n+=e(r.val);return n}const fs={D:Re,DD:on,DDD:un,DDDD:ln,t:cn,tt:fn,ttt:hn,tttt:dn,T:mn,TT:yn,TTT:pn,TTTT:gn,f:wn,ff:Sn,fff:kn,ffff:Mn,F:Tn,FF:On,FFF:vn,FFFF:Nn};class b{static create(e,n={}){return new b(e,n)}static parseFormat(e){let n=null,r="",s=!1;const a=[];for(let i=0;i<e.length;i++){const o=e.charAt(i);o==="'"?(r.length>0&&a.push({literal:s||/^\s+$/.test(r),val:r}),n=null,r="",s=!s):s||o===n?r+=o:(r.length>0&&a.push({literal:/^\s+$/.test(r),val:r}),r=o,n=o)}return r.length>0&&a.push({literal:s||/^\s+$/.test(r),val:r}),a}static macroTokenToFormatOpts(e){return fs[e]}constructor(e,n){this.opts=n,this.loc=e,this.systemLoc=null}formatWithSystemDefault(e,n){return this.systemLoc===null&&(this.systemLoc=this.loc.redefaultToSystem()),this.systemLoc.dtFormatter(e,{...this.opts,...n}).format()}dtFormatter(e,n={}){return this.loc.dtFormatter(e,{...this.opts,...n})}formatDateTime(e,n){return this.dtFormatter(e,n).format()}formatDateTimeParts(e,n){return this.dtFormatter(e,n).formatToParts()}formatInterval(e,n){return this.dtFormatter(e.start,n).dtf.formatRange(e.start.toJSDate(),e.end.toJSDate())}resolvedOptions(e,n){return this.dtFormatter(e,n).resolvedOptions()}num(e,n=0){if(this.opts.forceSimple)return I(e,n);const r={...this.opts};return n>0&&(r.padTo=n),this.loc.numberFormatter(r).format(e)}formatDateTimeFromString(e,n){const r=this.loc.listingMode()==="en",s=this.loc.outputCalendar&&this.loc.outputCalendar!=="gregory",a=(h,y)=>this.loc.extract(e,h,y),i=h=>e.isOffsetFixed&&e.offset===0&&h.allowZ?"Z":e.isValid?e.zone.formatOffset(e.ts,h.format):"",o=()=>r?as(e):a({hour:"numeric",hourCycle:"h12"},"dayperiod"),u=(h,y)=>r?us(e,h):a(y?{month:h}:{month:h,day:"numeric"},"month"),l=(h,y)=>r?os(e,h):a(y?{weekday:h}:{weekday:h,month:"long",day:"numeric"},"weekday"),c=h=>{const y=b.macroTokenToFormatOpts(h);return y?this.formatWithSystemDefault(e,y):h},m=h=>r?ls(e,h):a({era:h},"era"),p=h=>{switch(h){case"S":return this.num(e.millisecond);case"u":case"SSS":return this.num(e.millisecond,3);case"s":return this.num(e.second);case"ss":return this.num(e.second,2);case"uu":return this.num(Math.floor(e.millisecond/10),2);case"uuu":return this.num(Math.floor(e.millisecond/100));case"m":return this.num(e.minute);case"mm":return this.num(e.minute,2);case"h":return this.num(e.hour%12===0?12:e.hour%12);case"hh":return this.num(e.hour%12===0?12:e.hour%12,2);case"H":return this.num(e.hour);case"HH":return this.num(e.hour,2);case"Z":return i({format:"narrow",allowZ:this.opts.allowZ});case"ZZ":return i({format:"short",allowZ:this.opts.allowZ});case"ZZZ":return i({format:"techie",allowZ:this.opts.allowZ});case"ZZZZ":return e.zone.offsetName(e.ts,{format:"short",locale:this.loc.locale});case"ZZZZZ":return e.zone.offsetName(e.ts,{format:"long",locale:this.loc.locale});case"z":return e.zoneName;case"a":return o();case"d":return s?a({day:"numeric"},"day"):this.num(e.day);case"dd":return s?a({day:"2-digit"},"day"):this.num(e.day,2);case"c":return this.num(e.weekday);case"ccc":return l("short",!0);case"cccc":return l("long",!0);case"ccccc":return l("narrow",!0);case"E":return this.num(e.weekday);case"EEE":return l("short",!1);case"EEEE":return l("long",!1);case"EEEEE":return l("narrow",!1);case"L":return s?a({month:"numeric",day:"numeric"},"month"):this.num(e.month);case"LL":return s?a({month:"2-digit",day:"numeric"},"month"):this.num(e.month,2);case"LLL":return u("short",!0);case"LLLL":return u("long",!0);case"LLLLL":return u("narrow",!0);case"M":return s?a({month:"numeric"},"month"):this.num(e.month);case"MM":return s?a({month:"2-digit"},"month"):this.num(e.month,2);case"MMM":return u("short",!1);case"MMMM":return u("long",!1);case"MMMMM":return u("narrow",!1);case"y":return s?a({year:"numeric"},"year"):this.num(e.year);case"yy":return s?a({year:"2-digit"},"year"):this.num(e.year.toString().slice(-2),2);case"yyyy":return s?a({year:"numeric"},"year"):this.num(e.year,4);case"yyyyyy":return s?a({year:"numeric"},"year"):this.num(e.year,6);case"G":return m("short");case"GG":return m("long");case"GGGGG":return m("narrow");case"kk":return this.num(e.weekYear.toString().slice(-2),2);case"kkkk":return this.num(e.weekYear,4);case"W":return this.num(e.weekNumber);case"WW":return this.num(e.weekNumber,2);case"n":return this.num(e.localWeekNumber);case"nn":return this.num(e.localWeekNumber,2);case"ii":return this.num(e.localWeekYear.toString().slice(-2),2);case"iiii":return this.num(e.localWeekYear,4);case"o":return this.num(e.ordinal);case"ooo":return this.num(e.ordinal,3);case"q":return this.num(e.quarter);case"qq":return this.num(e.quarter,2);case"X":return this.num(Math.floor(e.ts/1e3));case"x":return this.num(e.ts);default:return c(h)}};return Ht(b.parseFormat(n),p)}formatDurationFromString(e,n){const r=u=>{switch(u[0]){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":return"hour";case"d":return"day";case"w":return"week";case"M":return"month";case"y":return"year";default:return null}},s=u=>l=>{const c=r(l);return c?this.num(u.get(c),l.length):l},a=b.parseFormat(n),i=a.reduce((u,{literal:l,val:c})=>l?u:u.concat(c),[]),o=e.shiftTo(...i.map(r).filter(u=>u));return Ht(a,s(o))}}const Pn=/[A-Za-z_+-]{1,256}(?::?\/[A-Za-z0-9_+-]{1,256}(?:\/[A-Za-z0-9_+-]{1,256})?)?/;function ye(...t){const e=t.reduce((n,r)=>n+r.source,"");return RegExp(`^${e}$`)}function pe(...t){return e=>t.reduce(([n,r,s],a)=>{const[i,o,u]=a(e,s);return[{...n,...i},o||r,u]},[{},null,1]).slice(0,2)}function ge(t,...e){if(t==null)return[null,null];for(const[n,r]of e){const s=n.exec(t);if(s)return r(s)}return[null,null]}function Gn(...t){return(e,n)=>{const r={};let s;for(s=0;s<t.length;s++)r[t[s]]=J(e[n+s]);return[r,null,n+s]}}const Jn=/(?:(Z)|([+-]\d\d)(?::?(\d\d))?)/,hs=`(?:${Jn.source}?(?:\\[(${Pn.source})\\])?)?`,vt=/(\d\d)(?::?(\d\d)(?::?(\d\d)(?:[.,](\d{1,30}))?)?)?/,jn=RegExp(`${vt.source}${hs}`),Mt=RegExp(`(?:T${jn.source})?`),ds=/([+-]\d{6}|\d{4})(?:-?(\d\d)(?:-?(\d\d))?)?/,ms=/(\d{4})-?W(\d\d)(?:-?(\d))?/,ys=/(\d{4})-?(\d{3})/,ps=Gn("weekYear","weekNumber","weekDay"),gs=Gn("year","ordinal"),ws=/(\d{4})-(\d\d)-(\d\d)/,Bn=RegExp(`${vt.source} ?(?:${Jn.source}|(${Pn.source}))?`),Ts=RegExp(`(?: ${Bn.source})?`);function he(t,e,n){const r=t[e];return w(r)?n:J(r)}function Ss(t,e){return[{year:he(t,e),month:he(t,e+1,1),day:he(t,e+2,1)},null,e+3]}function we(t,e){return[{hours:he(t,e,0),minutes:he(t,e+1,0),seconds:he(t,e+2,0),milliseconds:Ot(t[e+3])},null,e+4]}function Ce(t,e){const n=!t[e]&&!t[e+1],r=je(t[e+1],t[e+2]),s=n?null:F.instance(r);return[{},s,e+3]}function be(t,e){const n=t[e]?q.create(t[e]):null;return[{},n,e+1]}const Os=RegExp(`^T?${vt.source}$`),ks=/^-?P(?:(?:(-?\d{1,20}(?:\.\d{1,20})?)Y)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20}(?:\.\d{1,20})?)W)?(?:(-?\d{1,20}(?:\.\d{1,20})?)D)?(?:T(?:(-?\d{1,20}(?:\.\d{1,20})?)H)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20})(?:[.,](-?\d{1,20}))?S)?)?)$/;function vs(t){const[e,n,r,s,a,i,o,u,l]=t,c=e[0]==="-",m=u&&u[0]==="-",p=(h,y=!1)=>h!==void 0&&(y||h&&c)?-h:h;return[{years:p(K(n)),months:p(K(r)),weeks:p(K(s)),days:p(K(a)),hours:p(K(i)),minutes:p(K(o)),seconds:p(K(u),u==="-0"),milliseconds:p(Ot(l),m)}]}const Ms={GMT:0,EDT:-4*60,EST:-5*60,CDT:-5*60,CST:-6*60,MDT:-6*60,MST:-7*60,PDT:-7*60,PST:-8*60};function Nt(t,e,n,r,s,a,i){const o={year:e.length===2?pt(J(e)):J(e),month:Zn.indexOf(n)+1,day:J(r),hour:J(s),minute:J(a)};return i&&(o.second=J(i)),t&&(o.weekday=t.length>3?Un.indexOf(t)+1:Rn.indexOf(t)+1),o}const Ns=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|(?:([+-]\d\d)(\d\d)))$/;function Ds(t){const[,e,n,r,s,a,i,o,u,l,c,m]=t,p=Nt(e,s,r,n,a,i,o);let h;return u?h=Ms[u]:l?h=0:h=je(c,m),[p,new F(h)]}function Es(t){return t.replace(/\([^()]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").trim()}const xs=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\d\d) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4}) (\d\d):(\d\d):(\d\d) GMT$/,Is=/^(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\d\d)-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d\d) (\d\d):(\d\d):(\d\d) GMT$/,_s=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( \d|\d\d) (\d\d):(\d\d):(\d\d) (\d{4})$/;function qt(t){const[,e,n,r,s,a,i,o]=t;return[Nt(e,s,r,n,a,i,o),F.utcInstance]}function Cs(t){const[,e,n,r,s,a,i,o]=t;return[Nt(e,o,n,r,s,a,i),F.utcInstance]}const bs=ye(ds,Mt),Fs=ye(ms,Mt),Vs=ye(ys,Mt),Ws=ye(jn),Qn=pe(Ss,we,Ce,be),Ls=pe(ps,we,Ce,be),$s=pe(gs,we,Ce,be),As=pe(we,Ce,be);function Zs(t){return ge(t,[bs,Qn],[Fs,Ls],[Vs,$s],[Ws,As])}function zs(t){return ge(Es(t),[Ns,Ds])}function Us(t){return ge(t,[xs,qt],[Is,qt],[_s,Cs])}function Rs(t){return ge(t,[ks,vs])}const Hs=pe(we);function qs(t){return ge(t,[Os,Hs])}const Ys=ye(ws,Ts),Ps=ye(Bn),Gs=pe(we,Ce,be);function Js(t){return ge(t,[Ys,Qn],[Ps,Gs])}const Yt="Invalid Duration",Kn={weeks:{days:7,hours:7*24,minutes:7*24*60,seconds:7*24*60*60,milliseconds:7*24*60*60*1e3},days:{hours:24,minutes:24*60,seconds:24*60*60,milliseconds:24*60*60*1e3},hours:{minutes:60,seconds:60*60,milliseconds:60*60*1e3},minutes:{seconds:60,milliseconds:60*1e3},seconds:{milliseconds:1e3}},js={years:{quarters:4,months:12,weeks:52,days:365,hours:365*24,minutes:365*24*60,seconds:365*24*60*60,milliseconds:365*24*60*60*1e3},quarters:{months:3,weeks:13,days:91,hours:91*24,minutes:91*24*60,seconds:91*24*60*60,milliseconds:91*24*60*60*1e3},months:{weeks:4,days:30,hours:30*24,minutes:30*24*60,seconds:30*24*60*60,milliseconds:30*24*60*60*1e3},...Kn},L=146097/400,ae=146097/4800,Bs={years:{quarters:4,months:12,weeks:L/7,days:L,hours:L*24,minutes:L*24*60,seconds:L*24*60*60,milliseconds:L*24*60*60*1e3},quarters:{months:3,weeks:L/28,days:L/4,hours:L*24/4,minutes:L*24*60/4,seconds:L*24*60*60/4,milliseconds:L*24*60*60*1e3/4},months:{weeks:ae/7,days:ae,hours:ae*24,minutes:ae*24*60,seconds:ae*24*60*60,milliseconds:ae*24*60*60*1e3},...Kn},ne=["years","quarters","months","weeks","days","hours","minutes","seconds","milliseconds"],Qs=ne.slice(0).reverse();function G(t,e,n=!1){const r={values:n?e.values:{...t.values,...e.values||{}},loc:t.loc.clone(e.loc),conversionAccuracy:e.conversionAccuracy||t.conversionAccuracy,matrix:e.matrix||t.matrix};return new O(r)}function Xn(t,e){var n;let r=(n=e.milliseconds)!=null?n:0;for(const s of Qs.slice(1))e[s]&&(r+=e[s]*t[s].milliseconds);return r}function Pt(t,e){const n=Xn(t,e)<0?-1:1;ne.reduceRight((r,s)=>{if(w(e[s]))return r;if(r){const a=e[r]*n,i=t[s][r],o=Math.floor(a/i);e[s]+=o*n,e[r]-=o*i*n}return s},null),ne.reduce((r,s)=>{if(w(e[s]))return r;if(r){const a=e[r]%1;e[r]-=a,e[s]+=a*t[r][s]}return s},null)}function Ks(t){const e={};for(const[n,r]of Object.entries(t))r!==0&&(e[n]=r);return e}class O{constructor(e){const n=e.conversionAccuracy==="longterm"||!1;let r=n?Bs:js;e.matrix&&(r=e.matrix),this.values=e.values,this.loc=e.loc||M.create(),this.conversionAccuracy=n?"longterm":"casual",this.invalid=e.invalid||null,this.matrix=r,this.isLuxonDuration=!0}static fromMillis(e,n){return O.fromObject({milliseconds:e},n)}static fromObject(e,n={}){if(e==null||typeof e!="object")throw new C(`Duration.fromObject: argument expected to be an object, got ${e===null?"null":typeof e}`);return new O({values:Ye(e,O.normalizeUnit),loc:M.fromObject(n),conversionAccuracy:n.conversionAccuracy,matrix:n.matrix})}static fromDurationLike(e){if(B(e))return O.fromMillis(e);if(O.isDuration(e))return e;if(typeof e=="object")return O.fromObject(e);throw new C(`Unknown duration argument ${e} of type ${typeof e}`)}static fromISO(e,n){const[r]=Rs(e);return r?O.fromObject(r,n):O.invalid("unparsable",`the input "${e}" can't be parsed as ISO 8601`)}static fromISOTime(e,n){const[r]=qs(e);return r?O.fromObject(r,n):O.invalid("unparsable",`the input "${e}" can't be parsed as ISO 8601`)}static invalid(e,n=null){if(!e)throw new C("need to specify a reason the Duration is invalid");const r=e instanceof R?e:new R(e,n);if(x.throwOnInvalid)throw new Mr(r);return new O({invalid:r})}static normalizeUnit(e){const n={year:"years",years:"years",quarter:"quarters",quarters:"quarters",month:"months",months:"months",week:"weeks",weeks:"weeks",day:"days",days:"days",hour:"hours",hours:"hours",minute:"minutes",minutes:"minutes",second:"seconds",seconds:"seconds",millisecond:"milliseconds",milliseconds:"milliseconds"}[e&&e.toLowerCase()];if(!n)throw new an(e);return n}static isDuration(e){return e&&e.isLuxonDuration||!1}get locale(){return this.isValid?this.loc.locale:null}get numberingSystem(){return this.isValid?this.loc.numberingSystem:null}toFormat(e,n={}){const r={...n,floor:n.round!==!1&&n.floor!==!1};return this.isValid?b.create(this.loc,r).formatDurationFromString(this,e):Yt}toHuman(e={}){if(!this.isValid)return Yt;const n=ne.map(r=>{const s=this.values[r];return w(s)?null:this.loc.numberFormatter({style:"unit",unitDisplay:"long",...e,unit:r.slice(0,-1)}).format(s)}).filter(r=>r);return this.loc.listFormatter({type:"conjunction",style:e.listStyle||"narrow",...e}).format(n)}toObject(){return this.isValid?{...this.values}:{}}toISO(){if(!this.isValid)return null;let e="P";return this.years!==0&&(e+=this.years+"Y"),(this.months!==0||this.quarters!==0)&&(e+=this.months+this.quarters*3+"M"),this.weeks!==0&&(e+=this.weeks+"W"),this.days!==0&&(e+=this.days+"D"),(this.hours!==0||this.minutes!==0||this.seconds!==0||this.milliseconds!==0)&&(e+="T"),this.hours!==0&&(e+=this.hours+"H"),this.minutes!==0&&(e+=this.minutes+"M"),(this.seconds!==0||this.milliseconds!==0)&&(e+=kt(this.seconds+this.milliseconds/1e3,3)+"S"),e==="P"&&(e+="T0S"),e}toISOTime(e={}){if(!this.isValid)return null;const n=this.toMillis();return n<0||n>=864e5?null:(e={suppressMilliseconds:!1,suppressSeconds:!1,includePrefix:!1,format:"extended",...e,includeOffset:!1},g.fromMillis(n,{zone:"UTC"}).toISOTime(e))}toJSON(){return this.toISO()}toString(){return this.toISO()}[Symbol.for("nodejs.util.inspect.custom")](){return this.isValid?`Duration { values: ${JSON.stringify(this.values)} }`:`Duration { Invalid, reason: ${this.invalidReason} }`}toMillis(){return this.isValid?Xn(this.matrix,this.values):NaN}valueOf(){return this.toMillis()}plus(e){if(!this.isValid)return this;const n=O.fromDurationLike(e),r={};for(const s of ne)(de(n.values,s)||de(this.values,s))&&(r[s]=n.get(s)+this.get(s));return G(this,{values:r},!0)}minus(e){if(!this.isValid)return this;const n=O.fromDurationLike(e);return this.plus(n.negate())}mapUnits(e){if(!this.isValid)return this;const n={};for(const r of Object.keys(this.values))n[r]=An(e(this.values[r],r));return G(this,{values:n},!0)}get(e){return this[O.normalizeUnit(e)]}set(e){if(!this.isValid)return this;const n={...this.values,...Ye(e,O.normalizeUnit)};return G(this,{values:n})}reconfigure({locale:e,numberingSystem:n,conversionAccuracy:r,matrix:s}={}){const i={loc:this.loc.clone({locale:e,numberingSystem:n}),matrix:s,conversionAccuracy:r};return G(this,i)}as(e){return this.isValid?this.shiftTo(e).get(e):NaN}normalize(){if(!this.isValid)return this;const e=this.toObject();return Pt(this.matrix,e),G(this,{values:e},!0)}rescale(){if(!this.isValid)return this;const e=Ks(this.normalize().shiftToAll().toObject());return G(this,{values:e},!0)}shiftTo(...e){if(!this.isValid)return this;if(e.length===0)return this;e=e.map(i=>O.normalizeUnit(i));const n={},r={},s=this.toObject();let a;for(const i of ne)if(e.indexOf(i)>=0){a=i;let o=0;for(const l in r)o+=this.matrix[l][i]*r[l],r[l]=0;B(s[i])&&(o+=s[i]);const u=Math.trunc(o);n[i]=u,r[i]=(o*1e3-u*1e3)/1e3}else B(s[i])&&(r[i]=s[i]);for(const i in r)r[i]!==0&&(n[a]+=i===a?r[i]:r[i]/this.matrix[a][i]);return Pt(this.matrix,n),G(this,{values:n},!0)}shiftToAll(){return this.isValid?this.shiftTo("years","months","weeks","days","hours","minutes","seconds","milliseconds"):this}negate(){if(!this.isValid)return this;const e={};for(const n of Object.keys(this.values))e[n]=this.values[n]===0?0:-this.values[n];return G(this,{values:e},!0)}get years(){return this.isValid?this.values.years||0:NaN}get quarters(){return this.isValid?this.values.quarters||0:NaN}get months(){return this.isValid?this.values.months||0:NaN}get weeks(){return this.isValid?this.values.weeks||0:NaN}get days(){return this.isValid?this.values.days||0:NaN}get hours(){return this.isValid?this.values.hours||0:NaN}get minutes(){return this.isValid?this.values.minutes||0:NaN}get seconds(){return this.isValid?this.values.seconds||0:NaN}get milliseconds(){return this.isValid?this.values.milliseconds||0:NaN}get isValid(){return this.invalid===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}equals(e){if(!this.isValid||!e.isValid||!this.loc.equals(e.loc))return!1;function n(r,s){return r===void 0||r===0?s===void 0||s===0:r===s}for(const r of ne)if(!n(this.values[r],e.values[r]))return!1;return!0}}const oe="Invalid Interval";function Xs(t,e){return!t||!t.isValid?E.invalid("missing or invalid start"):!e||!e.isValid?E.invalid("missing or invalid end"):e<t?E.invalid("end before start",`The end of an interval must be after its start, but you had start=${t.toISO()} and end=${e.toISO()}`):null}class E{constructor(e){this.s=e.start,this.e=e.end,this.invalid=e.invalid||null,this.isLuxonInterval=!0}static invalid(e,n=null){if(!e)throw new C("need to specify a reason the Interval is invalid");const r=e instanceof R?e:new R(e,n);if(x.throwOnInvalid)throw new vr(r);return new E({invalid:r})}static fromDateTimes(e,n){const r=Oe(e),s=Oe(n),a=Xs(r,s);return a??new E({start:r,end:s})}static after(e,n){const r=O.fromDurationLike(n),s=Oe(e);return E.fromDateTimes(s,s.plus(r))}static before(e,n){const r=O.fromDurationLike(n),s=Oe(e);return E.fromDateTimes(s.minus(r),s)}static fromISO(e,n){const[r,s]=(e||"").split("/",2);if(r&&s){let a,i;try{a=g.fromISO(r,n),i=a.isValid}catch{i=!1}let o,u;try{o=g.fromISO(s,n),u=o.isValid}catch{u=!1}if(i&&u)return E.fromDateTimes(a,o);if(i){const l=O.fromISO(s,n);if(l.isValid)return E.after(a,l)}else if(u){const l=O.fromISO(r,n);if(l.isValid)return E.before(o,l)}}return E.invalid("unparsable",`the input "${e}" can't be parsed as ISO 8601`)}static isInterval(e){return e&&e.isLuxonInterval||!1}get start(){return this.isValid?this.s:null}get end(){return this.isValid?this.e:null}get lastDateTime(){return this.isValid&&this.e?this.e.minus(1):null}get isValid(){return this.invalidReason===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}length(e="milliseconds"){return this.isValid?this.toDuration(e).get(e):NaN}count(e="milliseconds",n){if(!this.isValid)return NaN;const r=this.start.startOf(e,n);let s;return n!=null&&n.useLocaleWeeks?s=this.end.reconfigure({locale:r.locale}):s=this.end,s=s.startOf(e,n),Math.floor(s.diff(r,e).get(e))+(s.valueOf()!==this.end.valueOf())}hasSame(e){return this.isValid?this.isEmpty()||this.e.minus(1).hasSame(this.s,e):!1}isEmpty(){return this.s.valueOf()===this.e.valueOf()}isAfter(e){return this.isValid?this.s>e:!1}isBefore(e){return this.isValid?this.e<=e:!1}contains(e){return this.isValid?this.s<=e&&this.e>e:!1}set({start:e,end:n}={}){return this.isValid?E.fromDateTimes(e||this.s,n||this.e):this}splitAt(...e){if(!this.isValid)return[];const n=e.map(Oe).filter(i=>this.contains(i)).sort((i,o)=>i.toMillis()-o.toMillis()),r=[];let{s}=this,a=0;for(;s<this.e;){const i=n[a]||this.e,o=+i>+this.e?this.e:i;r.push(E.fromDateTimes(s,o)),s=o,a+=1}return r}splitBy(e){const n=O.fromDurationLike(e);if(!this.isValid||!n.isValid||n.as("milliseconds")===0)return[];let{s:r}=this,s=1,a;const i=[];for(;r<this.e;){const o=this.start.plus(n.mapUnits(u=>u*s));a=+o>+this.e?this.e:o,i.push(E.fromDateTimes(r,a)),r=a,s+=1}return i}divideEqually(e){return this.isValid?this.splitBy(this.length()/e).slice(0,e):[]}overlaps(e){return this.e>e.s&&this.s<e.e}abutsStart(e){return this.isValid?+this.e==+e.s:!1}abutsEnd(e){return this.isValid?+e.e==+this.s:!1}engulfs(e){return this.isValid?this.s<=e.s&&this.e>=e.e:!1}equals(e){return!this.isValid||!e.isValid?!1:this.s.equals(e.s)&&this.e.equals(e.e)}intersection(e){if(!this.isValid)return this;const n=this.s>e.s?this.s:e.s,r=this.e<e.e?this.e:e.e;return n>=r?null:E.fromDateTimes(n,r)}union(e){if(!this.isValid)return this;const n=this.s<e.s?this.s:e.s,r=this.e>e.e?this.e:e.e;return E.fromDateTimes(n,r)}static merge(e){const[n,r]=e.sort((s,a)=>s.s-a.s).reduce(([s,a],i)=>a?a.overlaps(i)||a.abutsStart(i)?[s,a.union(i)]:[s.concat([a]),i]:[s,i],[[],null]);return r&&n.push(r),n}static xor(e){let n=null,r=0;const s=[],a=e.map(u=>[{time:u.s,type:"s"},{time:u.e,type:"e"}]),i=Array.prototype.concat(...a),o=i.sort((u,l)=>u.time-l.time);for(const u of o)r+=u.type==="s"?1:-1,r===1?n=u.time:(n&&+n!=+u.time&&s.push(E.fromDateTimes(n,u.time)),n=null);return E.merge(s)}difference(...e){return E.xor([this].concat(e)).map(n=>this.intersection(n)).filter(n=>n&&!n.isEmpty())}toString(){return this.isValid?`[${this.s.toISO()} – ${this.e.toISO()})`:oe}[Symbol.for("nodejs.util.inspect.custom")](){return this.isValid?`Interval { start: ${this.s.toISO()}, end: ${this.e.toISO()} }`:`Interval { Invalid, reason: ${this.invalidReason} }`}toLocaleString(e=Re,n={}){return this.isValid?b.create(this.s.loc.clone(n),e).formatInterval(this):oe}toISO(e){return this.isValid?`${this.s.toISO(e)}/${this.e.toISO(e)}`:oe}toISODate(){return this.isValid?`${this.s.toISODate()}/${this.e.toISODate()}`:oe}toISOTime(e){return this.isValid?`${this.s.toISOTime(e)}/${this.e.toISOTime(e)}`:oe}toFormat(e,{separator:n=" – "}={}){return this.isValid?`${this.s.toFormat(e)}${n}${this.e.toFormat(e)}`:oe}toDuration(e,n){return this.isValid?this.e.diff(this.s,e,n):O.invalid(this.invalidReason)}mapEndpoints(e){return E.fromDateTimes(e(this.s),e(this.e))}}class Me{static hasDST(e=x.defaultZone){const n=g.now().setZone(e).set({month:12});return!e.isUniversal&&n.offset!==n.set({month:6}).offset}static isValidIANAZone(e){return q.isValidZone(e)}static normalizeZone(e){return j(e,x.defaultZone)}static getStartOfWeek({locale:e=null,locObj:n=null}={}){return(n||M.create(e)).getStartOfWeek()}static getMinimumDaysInFirstWeek({locale:e=null,locObj:n=null}={}){return(n||M.create(e)).getMinDaysInFirstWeek()}static getWeekendWeekdays({locale:e=null,locObj:n=null}={}){return(n||M.create(e)).getWeekendDays().slice()}static months(e="long",{locale:n=null,numberingSystem:r=null,locObj:s=null,outputCalendar:a="gregory"}={}){return(s||M.create(n,r,a)).months(e)}static monthsFormat(e="long",{locale:n=null,numberingSystem:r=null,locObj:s=null,outputCalendar:a="gregory"}={}){return(s||M.create(n,r,a)).months(e,!0)}static weekdays(e="long",{locale:n=null,numberingSystem:r=null,locObj:s=null}={}){return(s||M.create(n,r,null)).weekdays(e)}static weekdaysFormat(e="long",{locale:n=null,numberingSystem:r=null,locObj:s=null}={}){return(s||M.create(n,r,null)).weekdays(e,!0)}static meridiems({locale:e=null}={}){return M.create(e).meridiems()}static eras(e="short",{locale:n=null}={}){return M.create(n,null,"gregory").eras(e)}static features(){return{relative:Wn(),localeWeek:Ln()}}}function Gt(t,e){const n=s=>s.toUTC(0,{keepLocalTime:!0}).startOf("day").valueOf(),r=n(e)-n(t);return Math.floor(O.fromMillis(r).as("days"))}function ei(t,e,n){const r=[["years",(u,l)=>l.year-u.year],["quarters",(u,l)=>l.quarter-u.quarter+(l.year-u.year)*4],["months",(u,l)=>l.month-u.month+(l.year-u.year)*12],["weeks",(u,l)=>{const c=Gt(u,l);return(c-c%7)/7}],["days",Gt]],s={},a=t;let i,o;for(const[u,l]of r)n.indexOf(u)>=0&&(i=u,s[u]=l(t,e),o=a.plus(s),o>e?(s[u]--,t=a.plus(s),t>e&&(o=t,s[u]--,t=a.plus(s))):t=o);return[t,s,o,i]}function ti(t,e,n,r){let[s,a,i,o]=ei(t,e,n);const u=e-s,l=n.filter(m=>["hours","minutes","seconds","milliseconds"].indexOf(m)>=0);l.length===0&&(i<e&&(i=s.plus({[o]:1})),i!==s&&(a[o]=(a[o]||0)+u/(i-s)));const c=O.fromObject(a,r);return l.length>0?O.fromMillis(u,r).shiftTo(...l).plus(c):c}const ni="missing Intl.DateTimeFormat.formatToParts support";function k(t,e=n=>n){return{regex:t,deser:([n])=>e(Yr(n))}}const ri=" ",er=`[ ${ri}]`,tr=new RegExp(er,"g");function si(t){return t.replace(/\./g,"\\.?").replace(tr,er)}function Jt(t){return t.replace(/\./g,"").replace(tr," ").toLowerCase()}function U(t,e){return t===null?null:{regex:RegExp(t.map(si).join("|")),deser:([n])=>t.findIndex(r=>Jt(n)===Jt(r))+e}}function jt(t,e){return{regex:t,deser:([,n,r])=>je(n,r),groups:e}}function Le(t){return{regex:t,deser:([e])=>e}}function ii(t){return t.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&")}function ai(t,e){const n=z(e),r=z(e,"{2}"),s=z(e,"{3}"),a=z(e,"{4}"),i=z(e,"{6}"),o=z(e,"{1,2}"),u=z(e,"{1,3}"),l=z(e,"{1,6}"),c=z(e,"{1,9}"),m=z(e,"{2,4}"),p=z(e,"{4,6}"),h=D=>({regex:RegExp(ii(D.val)),deser:([S])=>S,literal:!0}),N=(D=>{if(t.literal)return h(D);switch(D.val){case"G":return U(e.eras("short"),0);case"GG":return U(e.eras("long"),0);case"y":return k(l);case"yy":return k(m,pt);case"yyyy":return k(a);case"yyyyy":return k(p);case"yyyyyy":return k(i);case"M":return k(o);case"MM":return k(r);case"MMM":return U(e.months("short",!0),1);case"MMMM":return U(e.months("long",!0),1);case"L":return k(o);case"LL":return k(r);case"LLL":return U(e.months("short",!1),1);case"LLLL":return U(e.months("long",!1),1);case"d":return k(o);case"dd":return k(r);case"o":return k(u);case"ooo":return k(s);case"HH":return k(r);case"H":return k(o);case"hh":return k(r);case"h":return k(o);case"mm":return k(r);case"m":return k(o);case"q":return k(o);case"qq":return k(r);case"s":return k(o);case"ss":return k(r);case"S":return k(u);case"SSS":return k(s);case"u":return Le(c);case"uu":return Le(o);case"uuu":return k(n);case"a":return U(e.meridiems(),0);case"kkkk":return k(a);case"kk":return k(m,pt);case"W":return k(o);case"WW":return k(r);case"E":case"c":return k(n);case"EEE":return U(e.weekdays("short",!1),1);case"EEEE":return U(e.weekdays("long",!1),1);case"ccc":return U(e.weekdays("short",!0),1);case"cccc":return U(e.weekdays("long",!0),1);case"Z":case"ZZ":return jt(new RegExp(`([+-]${o.source})(?::(${r.source}))?`),2);case"ZZZ":return jt(new RegExp(`([+-]${o.source})(${r.source})?`),2);case"z":return Le(/[a-z_+-/]{1,256}?/i);case" ":return Le(/[^\S\n\r]/);default:return h(D)}})(t)||{invalidReason:ni};return N.token=t,N}const oi={year:{"2-digit":"yy",numeric:"yyyyy"},month:{numeric:"M","2-digit":"MM",short:"MMM",long:"MMMM"},day:{numeric:"d","2-digit":"dd"},weekday:{short:"EEE",long:"EEEE"},dayperiod:"a",dayPeriod:"a",hour12:{numeric:"h","2-digit":"hh"},hour24:{numeric:"H","2-digit":"HH"},minute:{numeric:"m","2-digit":"mm"},second:{numeric:"s","2-digit":"ss"},timeZoneName:{long:"ZZZZZ",short:"ZZZ"}};function ui(t,e,n){const{type:r,value:s}=t;if(r==="literal"){const u=/^\s+$/.test(s);return{literal:!u,val:u?" ":s}}const a=e[r];let i=r;r==="hour"&&(e.hour12!=null?i=e.hour12?"hour12":"hour24":e.hourCycle!=null?e.hourCycle==="h11"||e.hourCycle==="h12"?i="hour12":i="hour24":i=n.hour12?"hour12":"hour24");let o=oi[i];if(typeof o=="object"&&(o=o[a]),o)return{literal:!1,val:o}}function li(t){return[`^${t.map(n=>n.regex).reduce((n,r)=>`${n}(${r.source})`,"")}$`,t]}function ci(t,e,n){const r=t.match(e);if(r){const s={};let a=1;for(const i in n)if(de(n,i)){const o=n[i],u=o.groups?o.groups+1:1;!o.literal&&o.token&&(s[o.token.val[0]]=o.deser(r.slice(a,a+u))),a+=u}return[r,s]}else return[r,{}]}function fi(t){const e=a=>{switch(a){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":case"H":return"hour";case"d":return"day";case"o":return"ordinal";case"L":case"M":return"month";case"y":return"year";case"E":case"c":return"weekday";case"W":return"weekNumber";case"k":return"weekYear";case"q":return"quarter";default:return null}};let n=null,r;return w(t.z)||(n=q.create(t.z)),w(t.Z)||(n||(n=new F(t.Z)),r=t.Z),w(t.q)||(t.M=(t.q-1)*3+1),w(t.h)||(t.h<12&&t.a===1?t.h+=12:t.h===12&&t.a===0&&(t.h=0)),t.G===0&&t.y&&(t.y=-t.y),w(t.u)||(t.S=Ot(t.u)),[Object.keys(t).reduce((a,i)=>{const o=e(i);return o&&(a[o]=t[i]),a},{}),n,r]}let tt=null;function hi(){return tt||(tt=g.fromMillis(1555555555555)),tt}function di(t,e){if(t.literal)return t;const n=b.macroTokenToFormatOpts(t.val),r=ir(n,e);return r==null||r.includes(void 0)?t:r}function nr(t,e){return Array.prototype.concat(...t.map(n=>di(n,e)))}class rr{constructor(e,n){if(this.locale=e,this.format=n,this.tokens=nr(b.parseFormat(n),e),this.units=this.tokens.map(r=>ai(r,e)),this.disqualifyingUnit=this.units.find(r=>r.invalidReason),!this.disqualifyingUnit){const[r,s]=li(this.units);this.regex=RegExp(r,"i"),this.handlers=s}}explainFromTokens(e){if(this.isValid){const[n,r]=ci(e,this.regex,this.handlers),[s,a,i]=r?fi(r):[null,null,void 0];if(de(r,"a")&&de(r,"H"))throw new le("Can't include meridiem when specifying 24-hour format");return{input:e,tokens:this.tokens,regex:this.regex,rawMatches:n,matches:r,result:s,zone:a,specificOffset:i}}else return{input:e,tokens:this.tokens,invalidReason:this.invalidReason}}get isValid(){return!this.disqualifyingUnit}get invalidReason(){return this.disqualifyingUnit?this.disqualifyingUnit.invalidReason:null}}function sr(t,e,n){return new rr(t,n).explainFromTokens(e)}function mi(t,e,n){const{result:r,zone:s,specificOffset:a,invalidReason:i}=sr(t,e,n);return[r,s,a,i]}function ir(t,e){if(!t)return null;const r=b.create(e,t).dtFormatter(hi()),s=r.formatToParts(),a=r.resolvedOptions();return s.map(i=>ui(i,t,a))}const nt="Invalid DateTime",yi=864e13;function Ne(t){return new R("unsupported zone",`the zone "${t.name}" is not supported`)}function rt(t){return t.weekData===null&&(t.weekData=He(t.c)),t.weekData}function st(t){return t.localWeekData===null&&(t.localWeekData=He(t.c,t.loc.getMinDaysInFirstWeek(),t.loc.getStartOfWeek())),t.localWeekData}function X(t,e){const n={ts:t.ts,zone:t.zone,c:t.c,o:t.o,loc:t.loc,invalid:t.invalid};return new g({...n,...e,old:n})}function ar(t,e,n){let r=t-e*60*1e3;const s=n.offset(r);if(e===s)return[r,e];r-=(s-e)*60*1e3;const a=n.offset(r);return s===a?[r,s]:[t-Math.min(s,a)*60*1e3,Math.max(s,a)]}function $e(t,e){t+=e*60*1e3;const n=new Date(t);return{year:n.getUTCFullYear(),month:n.getUTCMonth()+1,day:n.getUTCDate(),hour:n.getUTCHours(),minute:n.getUTCMinutes(),second:n.getUTCSeconds(),millisecond:n.getUTCMilliseconds()}}function Ue(t,e,n){return ar(Je(t),e,n)}function Bt(t,e){const n=t.o,r=t.c.year+Math.trunc(e.years),s=t.c.month+Math.trunc(e.months)+Math.trunc(e.quarters)*3,a={...t.c,year:r,month:s,day:Math.min(t.c.day,qe(r,s))+Math.trunc(e.days)+Math.trunc(e.weeks)*7},i=O.fromObject({years:e.years-Math.trunc(e.years),quarters:e.quarters-Math.trunc(e.quarters),months:e.months-Math.trunc(e.months),weeks:e.weeks-Math.trunc(e.weeks),days:e.days-Math.trunc(e.days),hours:e.hours,minutes:e.minutes,seconds:e.seconds,milliseconds:e.milliseconds}).as("milliseconds"),o=Je(a);let[u,l]=ar(o,n,t.zone);return i!==0&&(u+=i,l=t.zone.offset(u)),{ts:u,o:l}}function ue(t,e,n,r,s,a){const{setZone:i,zone:o}=n;if(t&&Object.keys(t).length!==0||e){const u=e||o,l=g.fromObject(t,{...n,zone:u,specificOffset:a});return i?l:l.setZone(o)}else return g.invalid(new R("unparsable",`the input "${s}" can't be parsed as ${r}`))}function Ae(t,e,n=!0){return t.isValid?b.create(M.create("en-US"),{allowZ:n,forceSimple:!0}).formatDateTimeFromString(t,e):null}function it(t,e){const n=t.c.year>9999||t.c.year<0;let r="";return n&&t.c.year>=0&&(r+="+"),r+=I(t.c.year,n?6:4),e?(r+="-",r+=I(t.c.month),r+="-",r+=I(t.c.day)):(r+=I(t.c.month),r+=I(t.c.day)),r}function Qt(t,e,n,r,s,a){let i=I(t.c.hour);return e?(i+=":",i+=I(t.c.minute),(t.c.millisecond!==0||t.c.second!==0||!n)&&(i+=":")):i+=I(t.c.minute),(t.c.millisecond!==0||t.c.second!==0||!n)&&(i+=I(t.c.second),(t.c.millisecond!==0||!r)&&(i+=".",i+=I(t.c.millisecond,3))),s&&(t.isOffsetFixed&&t.offset===0&&!a?i+="Z":t.o<0?(i+="-",i+=I(Math.trunc(-t.o/60)),i+=":",i+=I(Math.trunc(-t.o%60))):(i+="+",i+=I(Math.trunc(t.o/60)),i+=":",i+=I(Math.trunc(t.o%60)))),a&&(i+="["+t.zone.ianaName+"]"),i}const or={month:1,day:1,hour:0,minute:0,second:0,millisecond:0},pi={weekNumber:1,weekday:1,hour:0,minute:0,second:0,millisecond:0},gi={ordinal:1,hour:0,minute:0,second:0,millisecond:0},ur=["year","month","day","hour","minute","second","millisecond"],wi=["weekYear","weekNumber","weekday","hour","minute","second","millisecond"],Ti=["year","ordinal","hour","minute","second","millisecond"];function Si(t){const e={year:"year",years:"year",month:"month",months:"month",day:"day",days:"day",hour:"hour",hours:"hour",minute:"minute",minutes:"minute",quarter:"quarter",quarters:"quarter",second:"second",seconds:"second",millisecond:"millisecond",milliseconds:"millisecond",weekday:"weekday",weekdays:"weekday",weeknumber:"weekNumber",weeksnumber:"weekNumber",weeknumbers:"weekNumber",weekyear:"weekYear",weekyears:"weekYear",ordinal:"ordinal"}[t.toLowerCase()];if(!e)throw new an(t);return e}function Kt(t){switch(t.toLowerCase()){case"localweekday":case"localweekdays":return"localWeekday";case"localweeknumber":case"localweeknumbers":return"localWeekNumber";case"localweekyear":case"localweekyears":return"localWeekYear";default:return Si(t)}}function Oi(t){if(De===void 0&&(De=x.now()),t.type!=="iana")return t.offset(De);const e=t.name;let n=gt.get(e);return n===void 0&&(n=t.offset(De),gt.set(e,n)),n}function Xt(t,e){const n=j(e.zone,x.defaultZone);if(!n.isValid)return g.invalid(Ne(n));const r=M.fromObject(e);let s,a;if(w(t.year))s=x.now();else{for(const u of ur)w(t[u])&&(t[u]=or[u]);const i=Fn(t)||Vn(t);if(i)return g.invalid(i);const o=Oi(n);[s,a]=Ue(t,o,n)}return new g({ts:s,zone:n,loc:r,o:a})}function en(t,e,n){const r=w(n.round)?!0:n.round,s=(i,o)=>(i=kt(i,r||n.calendary?0:2,!0),e.loc.clone(n).relFormatter(n).format(i,o)),a=i=>n.calendary?e.hasSame(t,i)?0:e.startOf(i).diff(t.startOf(i),i).get(i):e.diff(t,i).get(i);if(n.unit)return s(a(n.unit),n.unit);for(const i of n.units){const o=a(i);if(Math.abs(o)>=1)return s(o,i)}return s(t>e?-0:0,n.units[n.units.length-1])}function tn(t){let e={},n;return t.length>0&&typeof t[t.length-1]=="object"?(e=t[t.length-1],n=Array.from(t).slice(0,t.length-1)):n=Array.from(t),[e,n]}let De;const gt=new Map;class g{constructor(e){const n=e.zone||x.defaultZone;let r=e.invalid||(Number.isNaN(e.ts)?new R("invalid input"):null)||(n.isValid?null:Ne(n));this.ts=w(e.ts)?x.now():e.ts;let s=null,a=null;if(!r)if(e.old&&e.old.ts===this.ts&&e.old.zone.equals(n))[s,a]=[e.old.c,e.old.o];else{const o=B(e.o)&&!e.old?e.o:n.offset(this.ts);s=$e(this.ts,o),r=Number.isNaN(s.year)?new R("invalid input"):null,s=r?null:s,a=r?null:o}this._zone=n,this.loc=e.loc||M.create(),this.invalid=r,this.weekData=null,this.localWeekData=null,this.c=s,this.o=a,this.isLuxonDateTime=!0}static now(){return new g({})}static local(){const[e,n]=tn(arguments),[r,s,a,i,o,u,l]=n;return Xt({year:r,month:s,day:a,hour:i,minute:o,second:u,millisecond:l},e)}static utc(){const[e,n]=tn(arguments),[r,s,a,i,o,u,l]=n;return e.zone=F.utcInstance,Xt({year:r,month:s,day:a,hour:i,minute:o,second:u,millisecond:l},e)}static fromJSDate(e,n={}){const r=Br(e)?e.valueOf():NaN;if(Number.isNaN(r))return g.invalid("invalid input");const s=j(n.zone,x.defaultZone);return s.isValid?new g({ts:r,zone:s,loc:M.fromObject(n)}):g.invalid(Ne(s))}static fromMillis(e,n={}){if(B(e))return e<-864e13||e>yi?g.invalid("Timestamp out of range"):new g({ts:e,zone:j(n.zone,x.defaultZone),loc:M.fromObject(n)});throw new C(`fromMillis requires a numerical input, but received a ${typeof e} with value ${e}`)}static fromSeconds(e,n={}){if(B(e))return new g({ts:e*1e3,zone:j(n.zone,x.defaultZone),loc:M.fromObject(n)});throw new C("fromSeconds requires a numerical input")}static fromObject(e,n={}){e=e||{};const r=j(n.zone,x.defaultZone);if(!r.isValid)return g.invalid(Ne(r));const s=M.fromObject(n),a=Ye(e,Kt),{minDaysInFirstWeek:i,startOfWeek:o}=zt(a,s),u=x.now(),l=w(n.specificOffset)?r.offset(u):n.specificOffset,c=!w(a.ordinal),m=!w(a.year),p=!w(a.month)||!w(a.day),h=m||p,y=a.weekYear||a.weekNumber;if((h||c)&&y)throw new le("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(p&&c)throw new le("Can't mix ordinal dates with month/day");const N=y||a.weekday&&!h;let D,S,v=$e(u,l);N?(D=wi,S=pi,v=He(v,i,o)):c?(D=Ti,S=gi,v=et(v)):(D=ur,S=or);let _=!1;for(const Se of D){const hr=a[Se];w(hr)?_?a[Se]=S[Se]:a[Se]=v[Se]:_=!0}const Z=N?Gr(a,i,o):c?Jr(a):Fn(a),Y=Z||Vn(a);if(Y)return g.invalid(Y);const lr=N?At(a,i,o):c?Zt(a):a,[cr,fr]=Ue(lr,l,r),Te=new g({ts:cr,zone:r,o:fr,loc:s});return a.weekday&&h&&e.weekday!==Te.weekday?g.invalid("mismatched weekday",`you can't specify both a weekday of ${a.weekday} and a date of ${Te.toISO()}`):Te.isValid?Te:g.invalid(Te.invalid)}static fromISO(e,n={}){const[r,s]=Zs(e);return ue(r,s,n,"ISO 8601",e)}static fromRFC2822(e,n={}){const[r,s]=zs(e);return ue(r,s,n,"RFC 2822",e)}static fromHTTP(e,n={}){const[r,s]=Us(e);return ue(r,s,n,"HTTP",n)}static fromFormat(e,n,r={}){if(w(e)||w(n))throw new C("fromFormat requires an input string and a format");const{locale:s=null,numberingSystem:a=null}=r,i=M.fromOpts({locale:s,numberingSystem:a,defaultToEN:!0}),[o,u,l,c]=mi(i,e,n);return c?g.invalid(c):ue(o,u,r,`format ${n}`,e,l)}static fromString(e,n,r={}){return g.fromFormat(e,n,r)}static fromSQL(e,n={}){const[r,s]=Js(e);return ue(r,s,n,"SQL",e)}static invalid(e,n=null){if(!e)throw new C("need to specify a reason the DateTime is invalid");const r=e instanceof R?e:new R(e,n);if(x.throwOnInvalid)throw new kr(r);return new g({invalid:r})}static isDateTime(e){return e&&e.isLuxonDateTime||!1}static parseFormatForOpts(e,n={}){const r=ir(e,M.fromObject(n));return r?r.map(s=>s?s.val:null).join(""):null}static expandFormat(e,n={}){return nr(b.parseFormat(e),M.fromObject(n)).map(s=>s.val).join("")}static resetCache(){De=void 0,gt.clear()}get(e){return this[e]}get isValid(){return this.invalid===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}get locale(){return this.isValid?this.loc.locale:null}get numberingSystem(){return this.isValid?this.loc.numberingSystem:null}get outputCalendar(){return this.isValid?this.loc.outputCalendar:null}get zone(){return this._zone}get zoneName(){return this.isValid?this.zone.name:null}get year(){return this.isValid?this.c.year:NaN}get quarter(){return this.isValid?Math.ceil(this.c.month/3):NaN}get month(){return this.isValid?this.c.month:NaN}get day(){return this.isValid?this.c.day:NaN}get hour(){return this.isValid?this.c.hour:NaN}get minute(){return this.isValid?this.c.minute:NaN}get second(){return this.isValid?this.c.second:NaN}get millisecond(){return this.isValid?this.c.millisecond:NaN}get weekYear(){return this.isValid?rt(this).weekYear:NaN}get weekNumber(){return this.isValid?rt(this).weekNumber:NaN}get weekday(){return this.isValid?rt(this).weekday:NaN}get isWeekend(){return this.isValid&&this.loc.getWeekendDays().includes(this.weekday)}get localWeekday(){return this.isValid?st(this).weekday:NaN}get localWeekNumber(){return this.isValid?st(this).weekNumber:NaN}get localWeekYear(){return this.isValid?st(this).weekYear:NaN}get ordinal(){return this.isValid?et(this.c).ordinal:NaN}get monthShort(){return this.isValid?Me.months("short",{locObj:this.loc})[this.month-1]:null}get monthLong(){return this.isValid?Me.months("long",{locObj:this.loc})[this.month-1]:null}get weekdayShort(){return this.isValid?Me.weekdays("short",{locObj:this.loc})[this.weekday-1]:null}get weekdayLong(){return this.isValid?Me.weekdays("long",{locObj:this.loc})[this.weekday-1]:null}get offset(){return this.isValid?+this.o:NaN}get offsetNameShort(){return this.isValid?this.zone.offsetName(this.ts,{format:"short",locale:this.locale}):null}get offsetNameLong(){return this.isValid?this.zone.offsetName(this.ts,{format:"long",locale:this.locale}):null}get isOffsetFixed(){return this.isValid?this.zone.isUniversal:null}get isInDST(){return this.isOffsetFixed?!1:this.offset>this.set({month:1,day:1}).offset||this.offset>this.set({month:5}).offset}getPossibleOffsets(){if(!this.isValid||this.isOffsetFixed)return[this];const e=864e5,n=6e4,r=Je(this.c),s=this.zone.offset(r-e),a=this.zone.offset(r+e),i=this.zone.offset(r-s*n),o=this.zone.offset(r-a*n);if(i===o)return[this];const u=r-i*n,l=r-o*n,c=$e(u,i),m=$e(l,o);return c.hour===m.hour&&c.minute===m.minute&&c.second===m.second&&c.millisecond===m.millisecond?[X(this,{ts:u}),X(this,{ts:l})]:[this]}get isInLeapYear(){return _e(this.year)}get daysInMonth(){return qe(this.year,this.month)}get daysInYear(){return this.isValid?fe(this.year):NaN}get weeksInWeekYear(){return this.isValid?xe(this.weekYear):NaN}get weeksInLocalWeekYear(){return this.isValid?xe(this.localWeekYear,this.loc.getMinDaysInFirstWeek(),this.loc.getStartOfWeek()):NaN}resolvedLocaleOptions(e={}){const{locale:n,numberingSystem:r,calendar:s}=b.create(this.loc.clone(e),e).resolvedOptions(this);return{locale:n,numberingSystem:r,outputCalendar:s}}toUTC(e=0,n={}){return this.setZone(F.instance(e),n)}toLocal(){return this.setZone(x.defaultZone)}setZone(e,{keepLocalTime:n=!1,keepCalendarTime:r=!1}={}){if(e=j(e,x.defaultZone),e.equals(this.zone))return this;if(e.isValid){let s=this.ts;if(n||r){const a=e.offset(this.ts),i=this.toObject();[s]=Ue(i,a,e)}return X(this,{ts:s,zone:e})}else return g.invalid(Ne(e))}reconfigure({locale:e,numberingSystem:n,outputCalendar:r}={}){const s=this.loc.clone({locale:e,numberingSystem:n,outputCalendar:r});return X(this,{loc:s})}setLocale(e){return this.reconfigure({locale:e})}set(e){if(!this.isValid)return this;const n=Ye(e,Kt),{minDaysInFirstWeek:r,startOfWeek:s}=zt(n,this.loc),a=!w(n.weekYear)||!w(n.weekNumber)||!w(n.weekday),i=!w(n.ordinal),o=!w(n.year),u=!w(n.month)||!w(n.day),l=o||u,c=n.weekYear||n.weekNumber;if((l||i)&&c)throw new le("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(u&&i)throw new le("Can't mix ordinal dates with month/day");let m;a?m=At({...He(this.c,r,s),...n},r,s):w(n.ordinal)?(m={...this.toObject(),...n},w(n.day)&&(m.day=Math.min(qe(m.year,m.month),m.day))):m=Zt({...et(this.c),...n});const[p,h]=Ue(m,this.o,this.zone);return X(this,{ts:p,o:h})}plus(e){if(!this.isValid)return this;const n=O.fromDurationLike(e);return X(this,Bt(this,n))}minus(e){if(!this.isValid)return this;const n=O.fromDurationLike(e).negate();return X(this,Bt(this,n))}startOf(e,{useLocaleWeeks:n=!1}={}){if(!this.isValid)return this;const r={},s=O.normalizeUnit(e);switch(s){case"years":r.month=1;case"quarters":case"months":r.day=1;case"weeks":case"days":r.hour=0;case"hours":r.minute=0;case"minutes":r.second=0;case"seconds":r.millisecond=0;break}if(s==="weeks")if(n){const a=this.loc.getStartOfWeek(),{weekday:i}=this;i<a&&(r.weekNumber=this.weekNumber-1),r.weekday=a}else r.weekday=1;if(s==="quarters"){const a=Math.ceil(this.month/3);r.month=(a-1)*3+1}return this.set(r)}endOf(e,n){return this.isValid?this.plus({[e]:1}).startOf(e,n).minus(1):this}toFormat(e,n={}){return this.isValid?b.create(this.loc.redefaultToEN(n)).formatDateTimeFromString(this,e):nt}toLocaleString(e=Re,n={}){return this.isValid?b.create(this.loc.clone(n),e).formatDateTime(this):nt}toLocaleParts(e={}){return this.isValid?b.create(this.loc.clone(e),e).formatDateTimeParts(this):[]}toISO({format:e="extended",suppressSeconds:n=!1,suppressMilliseconds:r=!1,includeOffset:s=!0,extendedZone:a=!1}={}){if(!this.isValid)return null;const i=e==="extended";let o=it(this,i);return o+="T",o+=Qt(this,i,n,r,s,a),o}toISODate({format:e="extended"}={}){return this.isValid?it(this,e==="extended"):null}toISOWeekDate(){return Ae(this,"kkkk-'W'WW-c")}toISOTime({suppressMilliseconds:e=!1,suppressSeconds:n=!1,includeOffset:r=!0,includePrefix:s=!1,extendedZone:a=!1,format:i="extended"}={}){return this.isValid?(s?"T":"")+Qt(this,i==="extended",n,e,r,a):null}toRFC2822(){return Ae(this,"EEE, dd LLL yyyy HH:mm:ss ZZZ",!1)}toHTTP(){return Ae(this.toUTC(),"EEE, dd LLL yyyy HH:mm:ss 'GMT'")}toSQLDate(){return this.isValid?it(this,!0):null}toSQLTime({includeOffset:e=!0,includeZone:n=!1,includeOffsetSpace:r=!0}={}){let s="HH:mm:ss.SSS";return(n||e)&&(r&&(s+=" "),n?s+="z":e&&(s+="ZZ")),Ae(this,s,!0)}toSQL(e={}){return this.isValid?`${this.toSQLDate()} ${this.toSQLTime(e)}`:null}toString(){return this.isValid?this.toISO():nt}[Symbol.for("nodejs.util.inspect.custom")](){return this.isValid?`DateTime { ts: ${this.toISO()}, zone: ${this.zone.name}, locale: ${this.locale} }`:`DateTime { Invalid, reason: ${this.invalidReason} }`}valueOf(){return this.toMillis()}toMillis(){return this.isValid?this.ts:NaN}toSeconds(){return this.isValid?this.ts/1e3:NaN}toUnixInteger(){return this.isValid?Math.floor(this.ts/1e3):NaN}toJSON(){return this.toISO()}toBSON(){return this.toJSDate()}toObject(e={}){if(!this.isValid)return{};const n={...this.c};return e.includeConfig&&(n.outputCalendar=this.outputCalendar,n.numberingSystem=this.loc.numberingSystem,n.locale=this.loc.locale),n}toJSDate(){return new Date(this.isValid?this.ts:NaN)}diff(e,n="milliseconds",r={}){if(!this.isValid||!e.isValid)return O.invalid("created by diffing an invalid DateTime");const s={locale:this.locale,numberingSystem:this.numberingSystem,...r},a=Qr(n).map(O.normalizeUnit),i=e.valueOf()>this.valueOf(),o=i?this:e,u=i?e:this,l=ti(o,u,a,s);return i?l.negate():l}diffNow(e="milliseconds",n={}){return this.diff(g.now(),e,n)}until(e){return this.isValid?E.fromDateTimes(this,e):this}hasSame(e,n,r){if(!this.isValid)return!1;const s=e.valueOf(),a=this.setZone(e.zone,{keepLocalTime:!0});return a.startOf(n,r)<=s&&s<=a.endOf(n,r)}equals(e){return this.isValid&&e.isValid&&this.valueOf()===e.valueOf()&&this.zone.equals(e.zone)&&this.loc.equals(e.loc)}toRelative(e={}){if(!this.isValid)return null;const n=e.base||g.fromObject({},{zone:this.zone}),r=e.padding?this<n?-e.padding:e.padding:0;let s=["years","months","days","hours","minutes","seconds"],a=e.unit;return Array.isArray(e.unit)&&(s=e.unit,a=void 0),en(n,this.plus(r),{...e,numeric:"always",units:s,unit:a})}toRelativeCalendar(e={}){return this.isValid?en(e.base||g.fromObject({},{zone:this.zone}),this,{...e,numeric:"auto",units:["years","months","days"],calendary:!0}):null}static min(...e){if(!e.every(g.isDateTime))throw new C("min requires all arguments be DateTimes");return Ut(e,n=>n.valueOf(),Math.min)}static max(...e){if(!e.every(g.isDateTime))throw new C("max requires all arguments be DateTimes");return Ut(e,n=>n.valueOf(),Math.max)}static fromFormatExplain(e,n,r={}){const{locale:s=null,numberingSystem:a=null}=r,i=M.fromOpts({locale:s,numberingSystem:a,defaultToEN:!0});return sr(i,e,n)}static fromStringExplain(e,n,r={}){return g.fromFormatExplain(e,n,r)}static buildFormatParser(e,n={}){const{locale:r=null,numberingSystem:s=null}=n,a=M.fromOpts({locale:r,numberingSystem:s,defaultToEN:!0});return new rr(a,e)}static fromFormatParser(e,n,r={}){if(w(e)||w(n))throw new C("fromFormatParser requires an input string and a format parser");const{locale:s=null,numberingSystem:a=null}=r,i=M.fromOpts({locale:s,numberingSystem:a,defaultToEN:!0});if(!i.equals(n.locale))throw new C(`fromFormatParser called with a locale of ${i}, but the format parser was created for ${n.locale}`);const{result:o,zone:u,specificOffset:l,invalidReason:c}=n.explainFromTokens(e);return c?g.invalid(c):ue(o,u,r,`format ${n.format}`,e,l)}static get DATE_SHORT(){return Re}static get DATE_MED(){return on}static get DATE_MED_WITH_WEEKDAY(){return Nr}static get DATE_FULL(){return un}static get DATE_HUGE(){return ln}static get TIME_SIMPLE(){return cn}static get TIME_WITH_SECONDS(){return fn}static get TIME_WITH_SHORT_OFFSET(){return hn}static get TIME_WITH_LONG_OFFSET(){return dn}static get TIME_24_SIMPLE(){return mn}static get TIME_24_WITH_SECONDS(){return yn}static get TIME_24_WITH_SHORT_OFFSET(){return pn}static get TIME_24_WITH_LONG_OFFSET(){return gn}static get DATETIME_SHORT(){return wn}static get DATETIME_SHORT_WITH_SECONDS(){return Tn}static get DATETIME_MED(){return Sn}static get DATETIME_MED_WITH_SECONDS(){return On}static get DATETIME_MED_WITH_WEEKDAY(){return Dr}static get DATETIME_FULL(){return kn}static get DATETIME_FULL_WITH_SECONDS(){return vn}static get DATETIME_HUGE(){return Mn}static get DATETIME_HUGE_WITH_SECONDS(){return Nn}}function Oe(t){if(g.isDateTime(t))return t;if(t&&t.valueOf&&B(t.valueOf()))return g.fromJSDate(t);if(t&&typeof t=="object")return g.fromObject(t);throw new C(`Unknown datetime argument: ${t}, of type ${typeof t}`)}const ki="3.6.1";W.DateTime=g;W.Duration=O;W.FixedOffsetZone=F;W.IANAZone=q;W.Info=Me;W.Interval=E;W.InvalidZone=xn;W.Settings=x;W.SystemZone=Ie;W.VERSION=ki;W.Zone=me;var ee=W;T.prototype.addYear=function(){this._date=this._date.plus({years:1})};T.prototype.addMonth=function(){this._date=this._date.plus({months:1}).startOf("month")};T.prototype.addDay=function(){this._date=this._date.plus({days:1}).startOf("day")};T.prototype.addHour=function(){var t=this._date;this._date=this._date.plus({hours:1}).startOf("hour"),this._date<=t&&(this._date=this._date.plus({hours:1}))};T.prototype.addMinute=function(){var t=this._date;this._date=this._date.plus({minutes:1}).startOf("minute"),this._date<t&&(this._date=this._date.plus({hours:1}))};T.prototype.addSecond=function(){var t=this._date;this._date=this._date.plus({seconds:1}).startOf("second"),this._date<t&&(this._date=this._date.plus({hours:1}))};T.prototype.subtractYear=function(){this._date=this._date.minus({years:1})};T.prototype.subtractMonth=function(){this._date=this._date.minus({months:1}).endOf("month").startOf("second")};T.prototype.subtractDay=function(){this._date=this._date.minus({days:1}).endOf("day").startOf("second")};T.prototype.subtractHour=function(){var t=this._date;this._date=this._date.minus({hours:1}).endOf("hour").startOf("second"),this._date>=t&&(this._date=this._date.minus({hours:1}))};T.prototype.subtractMinute=function(){var t=this._date;this._date=this._date.minus({minutes:1}).endOf("minute").startOf("second"),this._date>t&&(this._date=this._date.minus({hours:1}))};T.prototype.subtractSecond=function(){var t=this._date;this._date=this._date.minus({seconds:1}).startOf("second"),this._date>t&&(this._date=this._date.minus({hours:1}))};T.prototype.getDate=function(){return this._date.day};T.prototype.getFullYear=function(){return this._date.year};T.prototype.getDay=function(){var t=this._date.weekday;return t==7?0:t};T.prototype.getMonth=function(){return this._date.month-1};T.prototype.getHours=function(){return this._date.hour};T.prototype.getMinutes=function(){return this._date.minute};T.prototype.getSeconds=function(){return this._date.second};T.prototype.getMilliseconds=function(){return this._date.millisecond};T.prototype.getTime=function(){return this._date.valueOf()};T.prototype.getUTCDate=function(){return this._getUTC().day};T.prototype.getUTCFullYear=function(){return this._getUTC().year};T.prototype.getUTCDay=function(){var t=this._getUTC().weekday;return t==7?0:t};T.prototype.getUTCMonth=function(){return this._getUTC().month-1};T.prototype.getUTCHours=function(){return this._getUTC().hour};T.prototype.getUTCMinutes=function(){return this._getUTC().minute};T.prototype.getUTCSeconds=function(){return this._getUTC().second};T.prototype.toISOString=function(){return this._date.toUTC().toISO()};T.prototype.toJSON=function(){return this._date.toJSON()};T.prototype.setDate=function(t){this._date=this._date.set({day:t})};T.prototype.setFullYear=function(t){this._date=this._date.set({year:t})};T.prototype.setDay=function(t){this._date=this._date.set({weekday:t})};T.prototype.setMonth=function(t){this._date=this._date.set({month:t+1})};T.prototype.setHours=function(t){this._date=this._date.set({hour:t})};T.prototype.setMinutes=function(t){this._date=this._date.set({minute:t})};T.prototype.setSeconds=function(t){this._date=this._date.set({second:t})};T.prototype.setMilliseconds=function(t){this._date=this._date.set({millisecond:t})};T.prototype._getUTC=function(){return this._date.toUTC()};T.prototype.toString=function(){return this.toDate().toString()};T.prototype.toDate=function(){return this._date.toJSDate()};T.prototype.isLastDayOfMonth=function(){var t=this._date.plus({days:1}).startOf("day");return this._date.month!==t.month};T.prototype.isLastWeekdayOfMonth=function(){var t=this._date.plus({days:7}).startOf("day");return this._date.month!==t.month};function T(t,e){var n={zone:e};if(t?t instanceof T?this._date=t._date:t instanceof Date?this._date=ee.DateTime.fromJSDate(t,n):typeof t=="number"?this._date=ee.DateTime.fromMillis(t,n):typeof t=="string"&&(this._date=ee.DateTime.fromISO(t,n),this._date.isValid||(this._date=ee.DateTime.fromRFC2822(t,n)),this._date.isValid||(this._date=ee.DateTime.fromSQL(t,n)),this._date.isValid||(this._date=ee.DateTime.fromFormat(t,"EEE, d MMM yyyy HH:mm:ss",n))):this._date=ee.DateTime.local(),!this._date||!this._date.isValid)throw new Error("CronDate: unhandled timestamp: "+JSON.stringify(t));e&&e!==this._date.zoneName&&(this._date=this._date.setZone(e))}var vi=T;function te(t){return{start:t,count:1}}function nn(t,e){t.end=e,t.step=e-t.start,t.count=2}function at(t,e,n){e&&(e.count===2?(t.push(te(e.start)),t.push(te(e.end))):t.push(e)),n&&t.push(n)}function Mi(t){for(var e=[],n=void 0,r=0;r<t.length;r++){var s=t[r];typeof s!="number"?(at(e,n,te(s)),n=void 0):n?n.count===1?nn(n,s):n.step===s-n.end?(n.count++,n.end=s):n.count===2?(e.push(te(n.start)),n=te(n.end),nn(n,s)):(at(e,n),n=te(s)):n=te(s)}return at(e,n),e}var Ni=Mi,Di=Ni;function Ei(t,e,n){var r=Di(t);if(r.length===1){var s=r[0],a=s.step;if(a===1&&s.start===e&&s.end===n)return"*";if(a!==1&&s.start===e&&s.end===n-a+1)return"*/"+a}for(var i=[],o=0,u=r.length;o<u;++o){var l=r[o];if(l.count===1){i.push(l.start);continue}var a=l.step;if(l.step===1){i.push(l.start+"-"+l.end);continue}var c=l.start==0?l.count-1:l.count;l.step*c>l.end?i=i.concat(Array.from({length:l.end-l.start+1}).map(function(p,h){var y=l.start+h;return(y-l.start)%l.step===0?y:null}).filter(function(p){return p!=null})):l.end===n-l.step+1?i.push(l.start+"/"+l.step):i.push(l.start+"-"+l.end+"/"+l.step)}return i.join(",")}var xi=Ei,re=vi,Ii=xi,rn=1e4;function d(t,e){this._options=e,this._utc=e.utc||!1,this._tz=this._utc?"UTC":e.tz,this._currentDate=new re(e.currentDate,this._tz),this._startDate=e.startDate?new re(e.startDate,this._tz):null,this._endDate=e.endDate?new re(e.endDate,this._tz):null,this._isIterator=e.iterator||!1,this._hasIterated=!1,this._nthDayOfWeek=e.nthDayOfWeek||0,this.fields=d._freezeFields(t)}d.map=["second","minute","hour","dayOfMonth","month","dayOfWeek"];d.predefined={"@yearly":"0 0 1 1 *","@monthly":"0 0 1 * *","@weekly":"0 0 * * 0","@daily":"0 0 * * *","@hourly":"0 * * * *"};d.constraints=[{min:0,max:59,chars:[]},{min:0,max:59,chars:[]},{min:0,max:23,chars:[]},{min:1,max:31,chars:["L"]},{min:1,max:12,chars:[]},{min:0,max:7,chars:["L"]}];d.daysInMonth=[31,29,31,30,31,30,31,31,30,31,30,31];d.aliases={month:{jan:1,feb:2,mar:3,apr:4,may:5,jun:6,jul:7,aug:8,sep:9,oct:10,nov:11,dec:12},dayOfWeek:{sun:0,mon:1,tue:2,wed:3,thu:4,fri:5,sat:6}};d.parseDefaults=["0","*","*","*","*","*"];d.standardValidCharacters=/^[,*\d/-]+$/;d.dayOfWeekValidCharacters=/^[?,*\dL#/-]+$/;d.dayOfMonthValidCharacters=/^[?,*\dL/-]+$/;d.validCharacters={second:d.standardValidCharacters,minute:d.standardValidCharacters,hour:d.standardValidCharacters,dayOfMonth:d.dayOfMonthValidCharacters,month:d.standardValidCharacters,dayOfWeek:d.dayOfWeekValidCharacters};d._isValidConstraintChar=function(e,n){return typeof n!="string"?!1:e.chars.some(function(r){return n.indexOf(r)>-1})};d._parseField=function(e,n,r){switch(e){case"month":case"dayOfWeek":var s=d.aliases[e];n=n.replace(/[a-z]{3}/gi,function(u){if(u=u.toLowerCase(),typeof s[u]<"u")return s[u];throw new Error('Validation error, cannot resolve alias "'+u+'"')});break}if(!d.validCharacters[e].test(n))throw new Error("Invalid characters, got value: "+n);n.indexOf("*")!==-1?n=n.replace(/\*/g,r.min+"-"+r.max):n.indexOf("?")!==-1&&(n=n.replace(/\?/g,r.min+"-"+r.max));function a(u){var l=[];function c(y){if(y instanceof Array)for(var N=0,D=y.length;N<D;N++){var S=y[N];if(d._isValidConstraintChar(r,S)){l.push(S);continue}if(typeof S!="number"||Number.isNaN(S)||S<r.min||S>r.max)throw new Error("Constraint error, got value "+S+" expected range "+r.min+"-"+r.max);l.push(S)}else{if(d._isValidConstraintChar(r,y)){l.push(y);return}var v=+y;if(Number.isNaN(v)||v<r.min||v>r.max)throw new Error("Constraint error, got value "+y+" expected range "+r.min+"-"+r.max);e==="dayOfWeek"&&(v=v%7),l.push(v)}}var m=u.split(",");if(!m.every(function(y){return y.length>0}))throw new Error("Invalid list value format");if(m.length>1)for(var p=0,h=m.length;p<h;p++)c(i(m[p]));else c(i(u));return l.sort(d._sortCompareFn),l}function i(u){var l=1,c=u.split("/");if(c.length>2)throw new Error("Invalid repeat: "+u);return c.length>1?(c[0]==+c[0]&&(c=[c[0]+"-"+r.max,c[1]]),o(c[0],c[c.length-1])):o(u,l)}function o(u,l){var c=[],m=u.split("-");if(m.length>1){if(m.length<2)return+u;if(!m[0].length){if(!m[1].length)throw new Error("Invalid range: "+u);return+u}var p=+m[0],h=+m[1];if(Number.isNaN(p)||Number.isNaN(h)||p<r.min||h>r.max)throw new Error("Constraint error, got range "+p+"-"+h+" expected range "+r.min+"-"+r.max);if(p>h)throw new Error("Invalid range: "+u);var y=+l;if(Number.isNaN(y)||y<=0)throw new Error("Constraint error, cannot repeat at every "+y+" time.");e==="dayOfWeek"&&h%7===0&&c.push(0);for(var N=p,D=h;N<=D;N++){var S=c.indexOf(N)!==-1;!S&&y>0&&y%l===0?(y=1,c.push(N)):y++}return c}return Number.isNaN(+u)?u:+u}return a(n)};d._sortCompareFn=function(t,e){var n=typeof t=="number",r=typeof e=="number";return n&&r?t-e:!n&&r?1:n&&!r?-1:t.localeCompare(e)};d._handleMaxDaysInMonth=function(t){if(t.month.length===1){var e=d.daysInMonth[t.month[0]-1];if(t.dayOfMonth[0]>e)throw new Error("Invalid explicit day of month definition");return t.dayOfMonth.filter(function(n){return n==="L"?!0:n<=e}).sort(d._sortCompareFn)}};d._freezeFields=function(t){for(var e=0,n=d.map.length;e<n;++e){var r=d.map[e],s=t[r];t[r]=Object.freeze(s)}return Object.freeze(t)};d.prototype._applyTimezoneShift=function(t,e,n){if(n==="Month"||n==="Day"){var r=t.getTime();t[e+n]();var s=t.getTime();r===s&&(t.getMinutes()===0&&t.getSeconds()===0?t.addHour():t.getMinutes()===59&&t.getSeconds()===59&&t.subtractHour())}else{var a=t.getHours();t[e+n]();var i=t.getHours(),o=i-a;o===2?this.fields.hour.length!==24&&(this._dstStart=i):o===0&&t.getMinutes()===0&&t.getSeconds()===0&&this.fields.hour.length!==24&&(this._dstEnd=i)}};d.prototype._findSchedule=function(e){function n(S,v){for(var _=0,Z=v.length;_<Z;_++)if(v[_]>=S)return v[_]===S;return v[0]===S}function r(S,v){if(v<6){if(S.getDate()<8&&v===1)return!0;var _=S.getDate()%7?1:0,Z=S.getDate()-S.getDate()%7,Y=Math.floor(Z/7)+_;return Y===v}return!1}function s(S){return S.length>0&&S.some(function(v){return typeof v=="string"&&v.indexOf("L")>=0})}e=e||!1;var a=e?"subtract":"add",i=new re(this._currentDate,this._tz),o=this._startDate,u=this._endDate,l=i.getTime(),c=0;function m(S){return S.some(function(v){if(!s([v]))return!1;var _=Number.parseInt(v[0])%7;if(Number.isNaN(_))throw new Error("Invalid last weekday of the month expression: "+v);return i.getDay()===_&&i.isLastWeekdayOfMonth()})}for(;c<rn;){if(c++,e){if(o&&i.getTime()-o.getTime()<0)throw new Error("Out of the timespan range")}else if(u&&u.getTime()-i.getTime()<0)throw new Error("Out of the timespan range");var p=n(i.getDate(),this.fields.dayOfMonth);s(this.fields.dayOfMonth)&&(p=p||i.isLastDayOfMonth());var h=n(i.getDay(),this.fields.dayOfWeek);s(this.fields.dayOfWeek)&&(h=h||m(this.fields.dayOfWeek));var y=this.fields.dayOfMonth.length>=d.daysInMonth[i.getMonth()],N=this.fields.dayOfWeek.length===d.constraints[5].max-d.constraints[5].min+1,D=i.getHours();if(!p&&(!h||N)){this._applyTimezoneShift(i,a,"Day");continue}if(!y&&N&&!p){this._applyTimezoneShift(i,a,"Day");continue}if(y&&!N&&!h){this._applyTimezoneShift(i,a,"Day");continue}if(this._nthDayOfWeek>0&&!r(i,this._nthDayOfWeek)){this._applyTimezoneShift(i,a,"Day");continue}if(!n(i.getMonth()+1,this.fields.month)){this._applyTimezoneShift(i,a,"Month");continue}if(n(D,this.fields.hour)){if(this._dstEnd===D&&!e){this._dstEnd=null,this._applyTimezoneShift(i,"add","Hour");continue}}else if(this._dstStart!==D){this._dstStart=null,this._applyTimezoneShift(i,a,"Hour");continue}else if(!n(D-1,this.fields.hour)){i[a+"Hour"]();continue}if(!n(i.getMinutes(),this.fields.minute)){this._applyTimezoneShift(i,a,"Minute");continue}if(!n(i.getSeconds(),this.fields.second)){this._applyTimezoneShift(i,a,"Second");continue}if(l===i.getTime()){a==="add"||i.getMilliseconds()===0?this._applyTimezoneShift(i,a,"Second"):i.setMilliseconds(0);continue}break}if(c>=rn)throw new Error("Invalid expression, loop limit exceeded");return this._currentDate=new re(i,this._tz),this._hasIterated=!0,i};d.prototype.next=function(){var e=this._findSchedule();return this._isIterator?{value:e,done:!this.hasNext()}:e};d.prototype.prev=function(){var e=this._findSchedule(!0);return this._isIterator?{value:e,done:!this.hasPrev()}:e};d.prototype.hasNext=function(){var t=this._currentDate,e=this._hasIterated;try{return this._findSchedule(),!0}catch{return!1}finally{this._currentDate=t,this._hasIterated=e}};d.prototype.hasPrev=function(){var t=this._currentDate,e=this._hasIterated;try{return this._findSchedule(!0),!0}catch{return!1}finally{this._currentDate=t,this._hasIterated=e}};d.prototype.iterate=function(e,n){var r=[];if(e>=0)for(var s=0,a=e;s<a;s++)try{var i=this.next();r.push(i),n&&n(i,s)}catch{break}else for(var s=0,a=e;s>a;s--)try{var i=this.prev();r.push(i),n&&n(i,s)}catch{break}return r};d.prototype.reset=function(e){this._currentDate=new re(e||this._options.currentDate)};d.prototype.stringify=function(e){for(var n=[],r=e?0:1,s=d.map.length;r<s;++r){var a=d.map[r],i=this.fields[a],o=d.constraints[r];a==="dayOfMonth"&&this.fields.month.length===1?o={min:1,max:d.daysInMonth[this.fields.month[0]-1]}:a==="dayOfWeek"&&(o={min:0,max:6},i=i[i.length-1]===7?i.slice(0,-1):i),n.push(Ii(i,o.min,o.max))}return n.join(" ")};d.parse=function(e,n){var r=this;typeof n=="function"&&(n={});function s(a,i){i||(i={}),typeof i.currentDate>"u"&&(i.currentDate=new re(void 0,r._tz)),d.predefined[a]&&(a=d.predefined[a]);var o=[],u=(a+"").trim().split(/\s+/);if(u.length>6)throw new Error("Invalid cron expression");for(var l=d.map.length-u.length,c=0,m=d.map.length;c<m;++c){var p=d.map[c],h=u[u.length>m?c:c-l];if(c<l||!h)o.push(d._parseField(p,d.parseDefaults[c],d.constraints[c]));else{var y=p==="dayOfWeek"?v(h):h;o.push(d._parseField(p,y,d.constraints[c]))}}for(var N={},c=0,m=d.map.length;c<m;c++){var D=d.map[c];N[D]=o[c]}var S=d._handleMaxDaysInMonth(N);return N.dayOfMonth=S||N.dayOfMonth,new d(N,i);function v(_){var Z=_.split("#");if(Z.length>1){var Y=+Z[Z.length-1];if(/,/.test(_))throw new Error("Constraint error, invalid dayOfWeek `#` and `,` special characters are incompatible");if(/\//.test(_))throw new Error("Constraint error, invalid dayOfWeek `#` and `/` special characters are incompatible");if(/-/.test(_))throw new Error("Constraint error, invalid dayOfWeek `#` and `-` special characters are incompatible");if(Z.length>2||Number.isNaN(Y)||Y<1||Y>5)throw new Error("Constraint error, invalid dayOfWeek occurrence number (#)");return i.nthDayOfWeek=Y,Z[0]}return _}}return s(e,n)};d.fieldsToExpression=function(e,n){function r(p,h,y){if(!h)throw new Error("Validation error, Field "+p+" is missing");if(h.length===0)throw new Error("Validation error, Field "+p+" contains no values");for(var N=0,D=h.length;N<D;N++){var S=h[N];if(!d._isValidConstraintChar(y,S)&&(typeof S!="number"||Number.isNaN(S)||S<y.min||S>y.max))throw new Error("Constraint error, got value "+S+" expected range "+y.min+"-"+y.max)}}for(var s={},a=0,i=d.map.length;a<i;++a){var o=d.map[a],u=e[o];r(o,u,d.constraints[a]);for(var l=[],c=-1;++c<u.length;)l[c]=u[c];if(u=l.sort(d._sortCompareFn).filter(function(p,h,y){return!h||p!==y[h-1]}),u.length!==l.length)throw new Error("Validation error, Field "+o+" contains duplicate values");s[o]=u}var m=d._handleMaxDaysInMonth(s);return s.dayOfMonth=m||s.dayOfMonth,new d(s,n||{})};var _i=d,Pe=_i;function Q(){}Q._parseEntry=function(e){var n=e.split(" ");if(n.length===6)return{interval:Pe.parse(e)};if(n.length>6)return{interval:Pe.parse(n.slice(0,6).join(" ")),command:n.slice(6,n.length)};throw new Error("Invalid entry: "+e)};Q.parseExpression=function(e,n){return Pe.parse(e,n)};Q.fieldsToExpression=function(e,n){return Pe.fieldsToExpression(e,n)};Q.parseString=function(e){for(var n=e.split(`
`),r={variables:{},expressions:[],errors:{}},s=0,a=n.length;s<a;s++){var i=n[s],o=null,u=i.trim();if(u.length>0){if(u.match(/^#/))continue;if(o=u.match(/^(.*)=(.*)$/))r.variables[o[1]]=o[2];else{var l=null;try{l=Q._parseEntry("0 "+u),r.expressions.push(l.interval)}catch(c){r.errors[u]=c}}}}return r};Q.parseFile=function(e,n){mr.readFile(e,function(r,s){if(r){n(r);return}return n(null,Q.parseString(s.toString()))})};var Ci=Q;const bi=dr(Ci);function Fi(t){const e=t.trim().split(" "),n={time:{minute:ke(e[0]),hour:ke(e[1])},date:{dayInMonth:ke(e[2]),month:ke(e[3]),dayInWeek:ke(e[4])}};return n.date=Vi(n.date),n.time=Wi(n.time),n.date.anyCount===3&&n.time.text[0]==="每"&&(n.date.text=""),n.date.text+n.time.text}function ke(t){const e={raw:t};return e.isAny=t==="*",e.hasStepping=t.indexOf("/")>=0,e.hasList=t.indexOf(",")>=0,e.hasRange=t.indexOf("-")>=0,e.values=t.split(","),e}function Vi(t){return t.anyCount=t.month.isAny+t.dayInMonth.isAny+t.dayInWeek.isAny,t.anyCount===3?t.text="每日":t.anyCount===2?t.month.isAny===!1?t.text=Ze(t.month.raw)+"每日":t.dayInMonth.isAny===!1?t.text="每月"+ze(t.dayInMonth.raw):t.text="每"+ce(t.dayInWeek.raw):t.anyCount===1?t.month.isAny?t.text="每月"+ze(t.dayInMonth.raw)+"或"+ce(t.dayInWeek.raw):t.dayInMonth.isAny?t.text=Ze(t.month.raw)+"的每"+ce(t.dayInWeek.raw):t.text=Ze(t.month.raw)+ze(t.dayInMonth.raw):t.text=Ze(t.month.raw)+ze(t.dayInMonth.raw)+"或"+ce(t.dayInWeek.raw),t}function Wi(t){if(t.anyCount=t.hour.isAny+t.minute.isAny,t.anyCount===2)t.text="每分钟";else if(t.anyCount===1)if(t.hour.isAny)if(t.minute.hasStepping){const e=t.minute.raw.split("/");t.minute.hasRange||t.minute.hasList?t.text="每小时的第"+e[0]+"分钟(间隔"+e[1]+"分钟)":t.text="每隔"+t.minute.raw.split("/")[1]+"分钟"}else t.text="每小时的第"+t.minute.raw+"分钟";else if(t.hour.hasStepping){const e=t.hour.raw.split("/");t.hour.hasRange||t.hour.hasList?t.text=e[0]+"时的每一分钟(间隔"+e[1]+"小时)":t.text="每隔"+t.minute.raw.split("/")[1]+"分钟"}else t.text=t.hour.raw+"时的每一分钟";else{if(t.hour.hasStepping||t.minute.hasStepping){let e;if(t.hour.hasStepping){const r=t.hour.raw.split("/");t.hour.hasList||t.hour.hasRange?e=r[0]+"时(间隔"+r[1]+"小时)":e="每"+r[1]+"小时"}else e=t.hour.raw+"时";let n;if(t.minute.hasStepping){const r=t.minute.raw.split("/");t.minute.hasRange||t.minute.hasList?n="第"+r[0]+"分钟(间隔"+r[1]+"分钟)":n="每"+r[1]+"分钟"}else n="第"+t.minute.raw+"分钟";return t.text=e+"的"+n,t}if(!t.hour.hasList&&!t.hour.hasRange)if(t.minute.hasList)t.text=t.hour.raw+"时的第"+t.minute.raw+"分钟";else if(t.minute.hasRange){const e=t.minute.raw.split("-");t.text=t.hour.raw.padStart(2,"0")+":"+e[0].padStart(2,"0")+"-"+t.hour.raw.padStart(2,"0")+":"+e[1].padStart(2,"0")}else t.text=t.hour.raw.padStart(2,"0")+":"+t.minute.raw.padStart(2,"0");else t.text=t.hour.raw+"时的第"+t.minute.raw+"分钟"}return t}function Ze(t){if($i.forEach((e,n)=>{t=t.replace(e,(n+1).toString())}),t.indexOf("/")>=0){const e=t.split("/");return e[0]==="*"?"每"+e[1]+"月":e[0]+"月(间隔"+e[1]+"月)"}return t+"月"}function ze(t){if(t.indexOf("/")>=0){const e=t.split("/");return e[0]==="*"?"每"+e[1]+"日":e[1]+"日(间隔"+e[1]+"日)"}return t+"日"}function ce(t){return Li.forEach((e,n)=>{t=t.replace(e,n.toString())}),t.indexOf(",")>=0?t.indexOf("-")<0?"周"+t.split(",").map(e=>sn[Number(e)]).join("、"):t.split(",").map(e=>ce(e)).join(","):t.indexOf("-")>=0?t.split("-").map(e=>ce(e)).join("~"):"周"+sn[Number(t)]}const sn=["日","一","二","三","四","五","六"],Li=["SUN","MON","TUE","WEB","THU","FRI","SAT"].map(t=>new RegExp(t,"ig")),$i=["JAN","FEB","MAR","APR","MAY","JUN","JUL","AUG","SEP","OCT","NOV","DEC"].map(t=>new RegExp(t,"ig")),Ai={class:"popup-result"},Zi={class:"title"},zi={class:"popup-result-scroll"},Ui={key:1},Ri=yr({__name:"result",props:{expression:{}},setup(t){const e=t;ie(""),ie(""),ie([[]]);const n=ie([]),r=ie(!1);ie(e.expression),pr(e,(a,i)=>{console.log("监控表达式："+a.expression+"-"+(i==null?void 0:i.expression)),s(a.expression)},{deep:!0,immediate:!0}),gr(()=>{s(e.expression)});function s(a){let i=[];try{const o=bi.parseExpression(a),u=[];for(let l=0;l<20;l++){const m=o.next().toDate();m.setHours(m.getHours()+8);const p=m.toISOString().slice(0,16).replace("T"," ");u.push(p)}i=u}catch(o){console.error("无效的 Cron 表达式:",o.message)}i.length==0?n.value=["没有达到条件的结果！"]:n.value=i,r.value=!0}return(a,i)=>(Ve(),Fe("div",Ai,[Dt("p",Zi,"最近20次运行时间："+Et(wr(Fi)(e.expression)),1),Dt("ul",zi,[r.value?(Ve(!0),Fe(Tr,{key:0},Sr(n.value,o=>(Ve(),Fe("li",{key:o},Et(o),1))),128)):(Ve(),Fe("li",Ui,"计算结果中..."))])]))}}),Hi=Or(Ri,[["__scopeId","data-v-6e2f21c6"]]),Gi=Object.freeze(Object.defineProperty({__proto__:null,default:Hi},Symbol.toStringTag,{value:"Module"}));export{Hi as C,Fi as h,Gi as r};
