import{d as s,M as n,k as c,a as r,m as u,u as _,o as i,e as m,l as d}from"./vue.BNx9QYep.js";import{T as f}from"./index.BHZI5pdK.js";import{_ as p}from"./_plugin-vue_export-helper.DlAUqK2U.js";const C={key:0,class:"layout-navbars-close-full"},v={class:"layout-navbars-close-full-icon"},g=s({name:"layoutCloseFull"}),F=s({...g,setup(V){const e=f(),{isTagsViewCurrenFull:o}=n(e),t=()=>{e.setCurrenFullscreen(!1)};return(l,k)=>{const a=c("SvgIcon");return _(o)?(i(),r("div",C,[m("div",v,[d(a,{name:"ele-Close",title:l.$t("message.tagsView.closeFullscreen"),onClick:t},null,8,["title"])])])):u("",!0)}}}),w=p(F,[["__scopeId","data-v-c5e48991"]]);export{w as default};
