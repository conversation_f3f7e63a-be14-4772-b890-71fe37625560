const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/breadcrumb.B-D8z9oC.js","assets/vue.BNx9QYep.js","assets/index.BHZI5pdK.js","assets/index.Dg-OhEXY.css","assets/_plugin-vue_export-helper.DlAUqK2U.js","assets/breadcrumb.BJHFkKjg.css","assets/user.DvNl5iqb.js","assets/user.2qYBDzdn.css","assets/index.CFJPG_54.js","assets/favicon.nG7g_iy4.js","assets/index.B33z1et1.css","assets/horizontal.DQpIwp57.js","assets/horizontal.9SsUuwyv.css"])))=>i.map(i=>d[i]);
import{R as D,u as O,l as d,_ as r}from"./index.BHZI5pdK.js";import{d as C,M as p,Q as V,g as A,c as f,j as F,O as M,a as j,o as _,b as h,m as L,l as v,u as a,P as i}from"./vue.BNx9QYep.js";import{_ as w}from"./_plugin-vue_export-helper.DlAUqK2U.js";const H={class:"layout-navbars-breadcrumb-index"},N=C({name:"layoutBreadcrumbIndex"}),U=C({...N,setup(z){const R=i(()=>r(()=>import("./breadcrumb.B-D8z9oC.js"),__vite__mapDeps([0,1,2,3,4,5]))),g=i(()=>r(()=>import("./user.DvNl5iqb.js"),__vite__mapDeps([6,2,1,3,4,7]))),b=i(()=>r(()=>import("./index.CFJPG_54.js"),__vite__mapDeps([8,1,2,3,9,4,10]))),y=i(()=>r(()=>import("./horizontal.DQpIwp57.js"),__vite__mapDeps([11,2,1,3,4,12]))),E=D(),I=O(),{themeConfig:c}=p(I),{routesList:u}=p(E),S=V(),l=A({menuList:[]}),x=f(()=>{let{isShowLogo:t,layout:e}=c.value;return t&&e==="classic"||t&&e==="transverse"}),T=f(()=>{let{layout:t,isClassicSplitMenu:e}=c.value;return t==="transverse"||e&&t==="classic"}),m=()=>{let{layout:t,isClassicSplitMenu:e}=c.value;if(t==="classic"&&e){l.menuList=B(n(u.value));const s=P(S.path);d.emit("setSendClassicChildren",s)}else l.menuList=n(u.value)},B=t=>(t.map(e=>{e.children&&delete e.children}),t),n=t=>t.filter(e=>{var s;return!((s=e.meta)!=null&&s.isHide)}).map(e=>(e=Object.assign({},e),e.children&&(e.children=n(e.children)),e)),P=t=>{const e=t.split("/");let s={children:[]};return n(u.value).map((o,k)=>{o.path===`/${e[1]}`&&(o.k=k,s.item={...o},s.children=[{...o}],o.children&&(s.children=o.children))}),s};return F(()=>{m(),d.on("getBreadcrumbIndexSetFilterRoutes",()=>{m()})}),M(()=>{d.off("getBreadcrumbIndexSetFilterRoutes",()=>{})}),(t,e)=>(_(),j("div",H,[x.value?(_(),h(a(b),{key:0})):L("",!0),v(a(R)),T.value?(_(),h(a(y),{key:1,menuList:l.menuList},null,8,["menuList"])):L("",!0),v(a(g))]))}}),G=w(U,[["__scopeId","data-v-12036a87"]]);export{G as default};
