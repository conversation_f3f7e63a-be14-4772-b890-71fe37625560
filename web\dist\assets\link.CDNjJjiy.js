import{d as c,Q as u,g as d,v as p,k as _,a as k,o as m,e as n,l as f,s as y,w}from"./vue.BNx9QYep.js";import{y as g,a0 as v}from"./index.BHZI5pdK.js";import{_ as L}from"./_plugin-vue_export-helper.DlAUqK2U.js";const C={class:"layout-padding layout-link-container"},h={class:"layout-padding-auto layout-padding-view"},q={class:"layout-link-warp"},x={class:"layout-link-msg"},U=c({name:"layoutLinkView"}),$=c({...U,setup(b){const e=u(),t=d({title:"",isLink:"",query:null}),r=()=>{const{origin:i,pathname:o}=window.location;if(t.isLink.includes("{{token}}")&&(t.isLink=t.isLink.replace("{{token}}",g.cookie.get("token"))),v(t.isLink))window.open(t.isLink);else{let s=function(a){return Object.keys(a).map(l=>encodeURIComponent(l)+"="+encodeURIComponent(a[l])).join("&")};window.open(`${i}${o}#${t.isLink}?${s(t.query)}`)}};return p(()=>e.path,()=>{t.title=e.meta.title,t.isLink=e.meta.isLink,t.query=e.query},{immediate:!0}),(i,o)=>{const s=_("el-button");return m(),k("div",C,[n("div",h,[n("div",q,[o[1]||(o[1]=n("i",{class:"layout-link-icon iconfont icon-xingqiu"},null,-1)),n("div",x,'页面 "'+y(i.$t(t.title))+'" 已在新窗口中打开',1),f(s,{class:"mt30",round:"",size:"default",onClick:r},{default:w(()=>o[0]||(o[0]=[n("i",{class:"iconfont icon-lianjie"},null,-1),n("span",null,"立即前往体验",-1)])),_:1,__:[0]})])])])}}}),R=L($,[["__scopeId","data-v-1a495f5b"]]);export{R as default};
