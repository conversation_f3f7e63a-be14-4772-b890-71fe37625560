var ee=Object.defineProperty;var re=(e,o,d)=>o in e?ee(e,o,{enumerable:!0,configurable:!0,writable:!0,value:d}):e[o]=d;var W=(e,o,d)=>re(e,typeof o!="symbol"?o+"":o,d);var Z;(()=>{var e={975:s=>{function t(r){if(typeof r!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(r))}function a(r,n){for(var i,u="",h=0,l=-1,w=0,y=0;y<=r.length;++y){if(y<r.length)i=r.charCodeAt(y);else{if(i===47)break;i=47}if(i===47){if(!(l===y-1||w===1))if(l!==y-1&&w===2){if(u.length<2||h!==2||u.charCodeAt(u.length-1)!==46||u.charCodeAt(u.length-2)!==46){if(u.length>2){var P=u.lastIndexOf("/");if(P!==u.length-1){P===-1?(u="",h=0):h=(u=u.slice(0,P)).length-1-u.lastIndexOf("/"),l=y,w=0;continue}}else if(u.length===2||u.length===1){u="",h=0,l=y,w=0;continue}}n&&(u.length>0?u+="/..":u="..",h=2)}else u.length>0?u+="/"+r.slice(l+1,y):u=r.slice(l+1,y),h=y-l-1;l=y,w=0}else i===46&&w!==-1?++w:w=-1}return u}var f={resolve:function(){for(var r,n="",i=!1,u=arguments.length-1;u>=-1&&!i;u--){var h;u>=0?h=arguments[u]:(r===void 0&&(r=process.cwd()),h=r),t(h),h.length!==0&&(n=h+"/"+n,i=h.charCodeAt(0)===47)}return n=a(n,!i),i?n.length>0?"/"+n:"/":n.length>0?n:"."},normalize:function(r){if(t(r),r.length===0)return".";var n=r.charCodeAt(0)===47,i=r.charCodeAt(r.length-1)===47;return(r=a(r,!n)).length!==0||n||(r="."),r.length>0&&i&&(r+="/"),n?"/"+r:r},isAbsolute:function(r){return t(r),r.length>0&&r.charCodeAt(0)===47},join:function(){if(arguments.length===0)return".";for(var r,n=0;n<arguments.length;++n){var i=arguments[n];t(i),i.length>0&&(r===void 0?r=i:r+="/"+i)}return r===void 0?".":f.normalize(r)},relative:function(r,n){if(t(r),t(n),r===n||(r=f.resolve(r))===(n=f.resolve(n)))return"";for(var i=1;i<r.length&&r.charCodeAt(i)===47;++i);for(var u=r.length,h=u-i,l=1;l<n.length&&n.charCodeAt(l)===47;++l);for(var w=n.length-l,y=h<w?h:w,P=-1,b=0;b<=y;++b){if(b===y){if(w>y){if(n.charCodeAt(l+b)===47)return n.slice(l+b+1);if(b===0)return n.slice(l+b)}else h>y&&(r.charCodeAt(i+b)===47?P=b:b===0&&(P=0));break}var _=r.charCodeAt(i+b);if(_!==n.charCodeAt(l+b))break;_===47&&(P=b)}var O="";for(b=i+P+1;b<=u;++b)b!==u&&r.charCodeAt(b)!==47||(O.length===0?O+="..":O+="/..");return O.length>0?O+n.slice(l+P):(l+=P,n.charCodeAt(l)===47&&++l,n.slice(l))},_makeLong:function(r){return r},dirname:function(r){if(t(r),r.length===0)return".";for(var n=r.charCodeAt(0),i=n===47,u=-1,h=!0,l=r.length-1;l>=1;--l)if((n=r.charCodeAt(l))===47){if(!h){u=l;break}}else h=!1;return u===-1?i?"/":".":i&&u===1?"//":r.slice(0,u)},basename:function(r,n){if(n!==void 0&&typeof n!="string")throw new TypeError('"ext" argument must be a string');t(r);var i,u=0,h=-1,l=!0;if(n!==void 0&&n.length>0&&n.length<=r.length){if(n.length===r.length&&n===r)return"";var w=n.length-1,y=-1;for(i=r.length-1;i>=0;--i){var P=r.charCodeAt(i);if(P===47){if(!l){u=i+1;break}}else y===-1&&(l=!1,y=i+1),w>=0&&(P===n.charCodeAt(w)?--w==-1&&(h=i):(w=-1,h=y))}return u===h?h=y:h===-1&&(h=r.length),r.slice(u,h)}for(i=r.length-1;i>=0;--i)if(r.charCodeAt(i)===47){if(!l){u=i+1;break}}else h===-1&&(l=!1,h=i+1);return h===-1?"":r.slice(u,h)},extname:function(r){t(r);for(var n=-1,i=0,u=-1,h=!0,l=0,w=r.length-1;w>=0;--w){var y=r.charCodeAt(w);if(y!==47)u===-1&&(h=!1,u=w+1),y===46?n===-1?n=w:l!==1&&(l=1):n!==-1&&(l=-1);else if(!h){i=w+1;break}}return n===-1||u===-1||l===0||l===1&&n===u-1&&n===i+1?"":r.slice(n,u)},format:function(r){if(r===null||typeof r!="object")throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof r);return function(n,i){var u=i.dir||i.root,h=i.base||(i.name||"")+(i.ext||"");return u?u===i.root?u+h:u+"/"+h:h}(0,r)},parse:function(r){t(r);var n={root:"",dir:"",base:"",ext:"",name:""};if(r.length===0)return n;var i,u=r.charCodeAt(0),h=u===47;h?(n.root="/",i=1):i=0;for(var l=-1,w=0,y=-1,P=!0,b=r.length-1,_=0;b>=i;--b)if((u=r.charCodeAt(b))!==47)y===-1&&(P=!1,y=b+1),u===46?l===-1?l=b:_!==1&&(_=1):l!==-1&&(_=-1);else if(!P){w=b+1;break}return l===-1||y===-1||_===0||_===1&&l===y-1&&l===w+1?y!==-1&&(n.base=n.name=w===0&&h?r.slice(1,y):r.slice(w,y)):(w===0&&h?(n.name=r.slice(1,l),n.base=r.slice(1,y)):(n.name=r.slice(w,l),n.base=r.slice(w,y)),n.ext=r.slice(l,y)),w>0?n.dir=r.slice(0,w-1):h&&(n.dir="/"),n},sep:"/",delimiter:":",win32:null,posix:null};f.posix=f,s.exports=f}},o={};function d(s){var t=o[s];if(t!==void 0)return t.exports;var a=o[s]={exports:{}};return e[s](a,a.exports,d),a.exports}d.d=(s,t)=>{for(var a in t)d.o(t,a)&&!d.o(s,a)&&Object.defineProperty(s,a,{enumerable:!0,get:t[a]})},d.o=(s,t)=>Object.prototype.hasOwnProperty.call(s,t),d.r=s=>{typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(s,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(s,"__esModule",{value:!0})};var p={};let k;d.r(p),d.d(p,{URI:()=>v,Utils:()=>q}),typeof process=="object"?k=process.platform==="win32":typeof navigator=="object"&&(k=navigator.userAgent.indexOf("Windows")>=0);const T=/^\w[\w\d+.-]*$/,D=/^\//,g=/^\/\//;function C(s,t){if(!s.scheme&&t)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${s.authority}", path: "${s.path}", query: "${s.query}", fragment: "${s.fragment}"}`);if(s.scheme&&!T.test(s.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(s.path){if(s.authority){if(!D.test(s.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(g.test(s.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}}const c="",x="/",m=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;class v{constructor(t,a,f,r,n,i=!1){W(this,"scheme");W(this,"authority");W(this,"path");W(this,"query");W(this,"fragment");typeof t=="object"?(this.scheme=t.scheme||c,this.authority=t.authority||c,this.path=t.path||c,this.query=t.query||c,this.fragment=t.fragment||c):(this.scheme=function(u,h){return u||h?u:"file"}(t,i),this.authority=a||c,this.path=function(u,h){switch(u){case"https":case"http":case"file":h?h[0]!==x&&(h=x+h):h=x}return h}(this.scheme,f||c),this.query=r||c,this.fragment=n||c,C(this,i))}static isUri(t){return t instanceof v||!!t&&typeof t.authority=="string"&&typeof t.fragment=="string"&&typeof t.path=="string"&&typeof t.query=="string"&&typeof t.scheme=="string"&&typeof t.fsPath=="string"&&typeof t.with=="function"&&typeof t.toString=="function"}get fsPath(){return z(this)}with(t){if(!t)return this;let{scheme:a,authority:f,path:r,query:n,fragment:i}=t;return a===void 0?a=this.scheme:a===null&&(a=c),f===void 0?f=this.authority:f===null&&(f=c),r===void 0?r=this.path:r===null&&(r=c),n===void 0?n=this.query:n===null&&(n=c),i===void 0?i=this.fragment:i===null&&(i=c),a===this.scheme&&f===this.authority&&r===this.path&&n===this.query&&i===this.fragment?this:new S(a,f,r,n,i)}static parse(t,a=!1){const f=m.exec(t);return f?new S(f[2]||c,L(f[4]||c),L(f[5]||c),L(f[7]||c),L(f[9]||c),a):new S(c,c,c,c,c)}static file(t){let a=c;if(k&&(t=t.replace(/\\/g,x)),t[0]===x&&t[1]===x){const f=t.indexOf(x,2);f===-1?(a=t.substring(2),t=x):(a=t.substring(2,f),t=t.substring(f)||x)}return new S("file",a,t,c,c)}static from(t){const a=new S(t.scheme,t.authority,t.path,t.query,t.fragment);return C(a,!0),a}toString(t=!1){return N(this,t)}toJSON(){return this}static revive(t){if(t){if(t instanceof v)return t;{const a=new S(t);return a._formatted=t.external,a._fsPath=t._sep===R?t.fsPath:null,a}}return t}}const R=k?1:void 0;class S extends v{constructor(){super(...arguments);W(this,"_formatted",null);W(this,"_fsPath",null)}get fsPath(){return this._fsPath||(this._fsPath=z(this)),this._fsPath}toString(a=!1){return a?N(this,!0):(this._formatted||(this._formatted=N(this,!1)),this._formatted)}toJSON(){const a={$mid:1};return this._fsPath&&(a.fsPath=this._fsPath,a._sep=R),this._formatted&&(a.external=this._formatted),this.path&&(a.path=this.path),this.scheme&&(a.scheme=this.scheme),this.authority&&(a.authority=this.authority),this.query&&(a.query=this.query),this.fragment&&(a.fragment=this.fragment),a}}const $={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function F(s,t,a){let f,r=-1;for(let n=0;n<s.length;n++){const i=s.charCodeAt(n);if(i>=97&&i<=122||i>=65&&i<=90||i>=48&&i<=57||i===45||i===46||i===95||i===126||t&&i===47||a&&i===91||a&&i===93||a&&i===58)r!==-1&&(f+=encodeURIComponent(s.substring(r,n)),r=-1),f!==void 0&&(f+=s.charAt(n));else{f===void 0&&(f=s.substr(0,n));const u=$[i];u!==void 0?(r!==-1&&(f+=encodeURIComponent(s.substring(r,n)),r=-1),f+=u):r===-1&&(r=n)}}return r!==-1&&(f+=encodeURIComponent(s.substring(r))),f!==void 0?f:s}function K(s){let t;for(let a=0;a<s.length;a++){const f=s.charCodeAt(a);f===35||f===63?(t===void 0&&(t=s.substr(0,a)),t+=$[f]):t!==void 0&&(t+=s[a])}return t!==void 0?t:s}function z(s,t){let a;return a=s.authority&&s.path.length>1&&s.scheme==="file"?`//${s.authority}${s.path}`:s.path.charCodeAt(0)===47&&(s.path.charCodeAt(1)>=65&&s.path.charCodeAt(1)<=90||s.path.charCodeAt(1)>=97&&s.path.charCodeAt(1)<=122)&&s.path.charCodeAt(2)===58?s.path[1].toLowerCase()+s.path.substr(2):s.path,k&&(a=a.replace(/\//g,"\\")),a}function N(s,t){const a=t?K:F;let f="",{scheme:r,authority:n,path:i,query:u,fragment:h}=s;if(r&&(f+=r,f+=":"),(n||r==="file")&&(f+=x,f+=x),n){let l=n.indexOf("@");if(l!==-1){const w=n.substr(0,l);n=n.substr(l+1),l=w.lastIndexOf(":"),l===-1?f+=a(w,!1,!1):(f+=a(w.substr(0,l),!1,!1),f+=":",f+=a(w.substr(l+1),!1,!0)),f+="@"}n=n.toLowerCase(),l=n.lastIndexOf(":"),l===-1?f+=a(n,!1,!0):(f+=a(n.substr(0,l),!1,!0),f+=n.substr(l))}if(i){if(i.length>=3&&i.charCodeAt(0)===47&&i.charCodeAt(2)===58){const l=i.charCodeAt(1);l>=65&&l<=90&&(i=`/${String.fromCharCode(l+32)}:${i.substr(3)}`)}else if(i.length>=2&&i.charCodeAt(1)===58){const l=i.charCodeAt(0);l>=65&&l<=90&&(i=`${String.fromCharCode(l+32)}:${i.substr(2)}`)}f+=a(i,!0,!1)}return u&&(f+="?",f+=a(u,!1,!1)),h&&(f+="#",f+=t?h:F(h,!1,!1)),f}function V(s){try{return decodeURIComponent(s)}catch{return s.length>3?s.substr(0,3)+V(s.substr(3)):s}}const B=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function L(s){return s.match(B)?s.replace(B,t=>V(t)):s}var H=d(975);const E=H.posix||H,I="/";var q;(function(s){s.joinPath=function(t,...a){return t.with({path:E.join(t.path,...a)})},s.resolvePath=function(t,...a){let f=t.path,r=!1;f[0]!==I&&(f=I+f,r=!0);let n=E.resolve(f,...a);return r&&n[0]===I&&!t.authority&&(n=n.substring(1)),t.with({path:n})},s.dirname=function(t){if(t.path.length===0||t.path===I)return t;let a=E.dirname(t.path);return a.length===1&&a.charCodeAt(0)===46&&(a=""),t.with({path:a})},s.basename=function(t){return E.basename(t.path)},s.extname=function(t){return E.extname(t.path)}})(q||(q={})),Z=p})();const{URI:M,Utils:Re}=Z;function te(e){return e===4?1:e===3?2:e===2?4:8}function ne(e){return e}function ie(e){return{start:{line:e.startLineNumber-1,character:e.startColumn-1},end:{line:e.endLineNumber-1,character:e.endColumn-1}}}function A(e){return{startLineNumber:e.start.line+1,startColumn:e.start.character+1,endLineNumber:e.end.line+1,endColumn:e.end.character+1}}function ae(e){return{...A(e.location.range),message:e.message,resource:M.parse(e.location.uri)}}function G(e){const o={...A(e.range),message:e.message,severity:e.severity?te(e.severity):8};return e.code!=null&&(o.code=e.codeDescription==null?String(e.code):{value:String(e.code),target:M.parse(e.codeDescription.href)}),e.relatedInformation&&(o.relatedInformation=e.relatedInformation.map(ae)),e.tags&&(o.tags=e.tags.map(ne)),e.source!=null&&(o.source=e.source),o}function j(e){return{range:A(e.range),text:e.newText}}function se(e){const o={};return e.ignoreIfExists!=null&&(o.ignoreIfExists=e.ignoreIfExists),e.ignoreIfNotExists!=null&&(o.ignoreIfNotExists=e.ignoreIfNotExists),e.overwrite!=null&&(o.overwrite=e.overwrite),e.recursive!=null&&(o.recursive=e.recursive),o}function oe(e){const o=e.kind==="create"?{newResource:M.parse(e.uri)}:e.kind==="delete"?{oldResource:M.parse(e.uri)}:{oldResource:M.parse(e.oldUri),newResource:M.parse(e.newUri)};return e.options&&(o.options=se(e.options)),o}function J(e,o,d){return{resource:M.parse(o),versionId:d,textEdit:j(e)}}function ue(e){var o;const d=[];if(e.changes)for(const[p,k]of Object.entries(e.changes))for(const T of k)d.push(J(T,p));if(e.documentChanges)for(const p of e.documentChanges)if("textDocument"in p)for(const k of p.edits)d.push(J(k,p.textDocument.uri,(o=p.textDocument.version)!==null&&o!==void 0?o:void 0));else d.push(oe(p));return{edits:d}}function le(e){const o={title:e.title,isPreferred:e.isPreferred};return e.diagnostics&&(o.diagnostics=e.diagnostics.map(G)),e.disabled&&(o.disabled=e.disabled.reason),e.edit&&(o.edit=ue(e.edit)),e.isPreferred!=null&&(o.isPreferred=e.isPreferred),e.kind&&(o.kind=e.kind),o}function fe(e){const o={title:e.title,id:e.command};return e.arguments&&(o.arguments=e.arguments),o}function ce(e){return e===1?18:e===2?0:e===3?1:e===4?2:e===5?3:e===6?4:e===7?5:e===8?7:e===9?8:e===10?9:e===11?12:e===12?13:e===13?15:e===14?17:e===15?27:e===16?19:e===17?20:e===18?21:e===19?23:e===20?16:e===21?14:e===22?6:e===23?10:e===24?11:24}function ge(e){return e}function Q(e){return{value:e.value}}function he(e){return{range:A(e.range),text:e.newText}}function de(e){return"range"in e?A(e.range):"insert"in e&&"replace"in e?{insert:A(e.insert),replace:A(e.replace)}:A(e)}function pe(e,o){var d,p,k,T,D;const g=(d=o.itemDefaults)!==null&&d!==void 0?d:{},C=(p=e.textEdit)!==null&&p!==void 0?p:g.editRange,c=(k=e.commitCharacters)!==null&&k!==void 0?k:g.commitCharacters,x=(T=e.insertTextFormat)!==null&&T!==void 0?T:g.insertTextFormat,m=(D=e.insertTextMode)!==null&&D!==void 0?D:g.insertTextMode;let v=e.insertText,R;C?(R=de(C),"newText"in C&&(v=C.newText)):R={...o.range};const S={insertText:v??e.label,kind:e.kind==null?18:ce(e.kind),label:e.label,range:R};return e.additionalTextEdits&&(S.additionalTextEdits=e.additionalTextEdits.map(he)),e.command&&(S.command=fe(e.command)),c&&(S.commitCharacters=c),e.detail!=null&&(S.detail=e.detail),typeof e.documentation=="string"?S.documentation=e.documentation:e.documentation&&(S.documentation=Q(e.documentation)),e.filterText!=null&&(S.filterText=e.filterText),x===2?S.insertTextRules=4:m===2&&(S.insertTextRules=1),e.preselect!=null&&(S.preselect=e.preselect),e.sortText!=null&&(S.sortText=e.sortText),e.tags&&(S.tags=e.tags.map(ge)),S}function me(e,o){return{incomplete:!!e.isIncomplete,suggestions:e.items.map(d=>pe(d,{range:o.range,itemDefaults:e.itemDefaults}))}}function ve(e){return e===1?0:e===2?1:e===3?2:e===4?3:e===5?4:e===6?5:e===7?6:e===8?7:e===9?8:e===10?9:e===11?10:e===12?11:e===13?12:e===14?13:e===15?14:e===16?15:e===17?16:e===18?17:e===19?18:e===20?19:e===21?20:e===22?21:e===23?22:e===24?23:e===25?24:25}function we(e){return e}function X(e){var o,d,p;const k={detail:(o=e.detail)!==null&&o!==void 0?o:"",kind:ve(e.kind),name:e.name,range:A(e.range),selectionRange:A(e.selectionRange),tags:(p=(d=e.tags)===null||d===void 0?void 0:d.map(we))!==null&&p!==void 0?p:[]};return e.children&&(k.children=e.children.map(X)),k}function ye(e){const o={start:e.startLine+1,end:e.endLine+1};return e.kind!=null&&(o.kind={value:e.kind}),o}function Ce(e){return{insertSpaces:e.insertSpaces,tabSize:e.tabSize}}function Y(e){return typeof e=="string"?{value:e}:{value:`\`\`\`${e.language}
${e.value}
\`\`\``}}function xe(e){return typeof e=="string"||"language"in e?[Y(e)]:Array.isArray(e)?e.map(Y):[Q(e)]}function be(e){const o={contents:xe(e.contents)};return e.range&&(o.range=A(e.range)),o}function U(e){return{character:e.column-1,line:e.lineNumber-1}}function ke(e){const o={range:A(e.range)};return e.tooltip!=null&&(o.tooltip=e.tooltip),e.target!=null&&(o.url=M.parse(e.target)),o}function Se(e){const o={range:A(e.targetRange),targetSelectionRange:A(e.targetSelectionRange),uri:M.parse(e.targetUri)};return e.originSelectionRange&&(o.originSelectionRange=A(e.originSelectionRange)),o}function Ae(e){const o=[];let d=e;for(;d;)o.push({range:A(d.range)}),d=d.parent;return o}function Te(e,o,d){const p=new Map,k=m=>{const v=m.getLanguageId();return Array.isArray(o)?o.includes(v):o===v},T=async m=>{const v=m.getVersionId(),R=await d.provideMarkerData(m);!m.isDisposed()&&v===m.getVersionId()&&k(m)&&e.editor.setModelMarkers(m,d.owner,R??[])},D=m=>{if(!k(m))return;let v;const R=m.onDidChangeContent(()=>{clearTimeout(v),v=setTimeout(()=>{T(m)},500)});p.set(m,{dispose(){clearTimeout(v),R.dispose()}}),T(m)},g=m=>{e.editor.setModelMarkers(m,d.owner,[]);const v=p.get(m);v&&(v.dispose(),p.delete(m))},C=e.editor.onDidCreateModel(D),c=e.editor.onWillDisposeModel(m=>{var v;g(m),(v=d.doReset)==null||v.call(d,m)}),x=e.editor.onDidChangeModelLanguage(({model:m})=>{var v;g(m),D(m),(v=d.doReset)==null||v.call(d,m)});for(const m of e.editor.getModels())D(m);return{dispose(){for(const m of p.keys())g(m);C.dispose(),c.dispose(),x.dispose()},async revalidate(){await Promise.all(e.editor.getModels().map(T))}}}function Pe(e,o){let{createData:d,interval:p=3e4,label:k,moduleId:T,stopWhenIdleFor:D=12e4}=o,g,C=0,c=!1;const x=()=>{g&&(g.dispose(),g=void 0)},m=setInterval(()=>{if(!g)return;Date.now()-C>D&&x()},p);return{dispose(){c=!0,clearInterval(m),x()},getWorker(...v){if(c)throw new Error("Worker manager has been disposed");return C=Date.now(),g||(g=e.editor.createWebWorker({createData:d,label:k,moduleId:T})),g.withSyncedResources(v)},updateCreateData(v){d=v,x()}}}function Me(e,o){const d={completion:!0,customTags:[],enableSchemaRequest:!1,format:!0,isKubernetes:!1,hover:!0,schemas:[],validate:!0,yamlVersion:"1.2",...o};e.languages.register({id:"yaml",extensions:[".yaml",".yml"],aliases:["YAML","yaml","YML","yml"],mimetypes:["application/x-yaml"]});const p=Pe(e,{label:"yaml",moduleId:"monaco-yaml/yaml.worker",createData:d}),k=new WeakMap,T=Te(e,"yaml",{owner:"yaml",async provideMarkerData(g){const c=await(await p.getWorker(g.uri)).doValidation(String(g.uri));return k.set(g,c),c==null?void 0:c.map(G)},async doReset(g){await(await p.getWorker(g.uri)).resetSchema(String(g.uri))}}),D=[p,T,e.languages.registerCompletionItemProvider("yaml",{triggerCharacters:[" ",":"],async provideCompletionItems(g,C){const c=g.getWordUntilPosition(C),m=await(await p.getWorker(g.uri)).doComplete(String(g.uri),U(C));if(m)return me(m,{range:{startLineNumber:C.lineNumber,startColumn:c.startColumn,endLineNumber:C.lineNumber,endColumn:c.endColumn}})}}),e.languages.registerHoverProvider("yaml",{async provideHover(g,C){const x=await(await p.getWorker(g.uri)).doHover(String(g.uri),U(C));if(x)return be(x)}}),e.languages.registerDefinitionProvider("yaml",{async provideDefinition(g,C){const x=await(await p.getWorker(g.uri)).doDefinition(String(g.uri),U(C));return x==null?void 0:x.map(Se)}}),e.languages.registerDocumentSymbolProvider("yaml",{displayName:"yaml",async provideDocumentSymbols(g){const c=await(await p.getWorker(g.uri)).findDocumentSymbols(String(g.uri));return c==null?void 0:c.map(X)}}),e.languages.registerDocumentFormattingEditProvider("yaml",{displayName:"yaml",async provideDocumentFormattingEdits(g){const c=await(await p.getWorker(g.uri)).format(String(g.uri));return c==null?void 0:c.map(j)}}),e.languages.registerLinkProvider("yaml",{async provideLinks(g){const c=await(await p.getWorker(g.uri)).findLinks(String(g.uri));if(c)return{links:c.map(ke)}}}),e.languages.registerCodeActionProvider("yaml",{async provideCodeActions(g,C,c){var x;const v=await(await p.getWorker(g.uri)).getCodeAction(String(g.uri),ie(C),{diagnostics:((x=k.get(g))==null?void 0:x.filter(R=>C.intersectRanges(A(R.range))))||[],only:c.only?[c.only]:void 0,triggerKind:c.trigger});if(v)return{actions:v.map(le),dispose(){}}}}),e.languages.registerFoldingRangeProvider("yaml",{async provideFoldingRanges(g){const c=await(await p.getWorker(g.uri)).getFoldingRanges(String(g.uri));return c==null?void 0:c.map(ye)}}),e.languages.setLanguageConfiguration("yaml",{comments:{lineComment:"#"},brackets:[["{","}"],["[","]"],["(",")"]],autoClosingPairs:[{open:"{",close:"}"},{open:"[",close:"]"},{open:"(",close:")"},{open:'"',close:'"'},{open:"'",close:"'"}],surroundingPairs:[{open:"{",close:"}"},{open:"[",close:"]"},{open:"(",close:")"},{open:'"',close:'"'},{open:"'",close:"'"}]}),e.languages.registerOnTypeFormattingEditProvider("yaml",{autoFormatTriggerCharacters:[`
`],async provideOnTypeFormattingEdits(g,C,c,x){const v=await(await p.getWorker(g.uri)).doDocumentOnTypeFormatting(String(g.uri),U(C),c,Ce(x));return v==null?void 0:v.map(j)}}),e.languages.registerSelectionRangeProvider("yaml",{async provideSelectionRanges(g,C){const x=await(await p.getWorker(g.uri)).getSelectionRanges(String(g.uri),C.map(U));return x==null?void 0:x.map(Ae)}})];return{dispose(){for(const g of D)g.dispose()},async update(g){p.updateCreateData(Object.assign(d,g)),await T.revalidate()}}}export{Me as configureMonacoYaml};
