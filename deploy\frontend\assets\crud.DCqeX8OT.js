import{r as o,O as y,v as s,A as b,E as v,s as c}from"./index.BHZI5pdK.js";import{d as i}from"./dictionary.DNsEqk19.js";import{a}from"./authFunction.D3Be3hRy.js";import{M as x,c as d}from"./vue.BNx9QYep.js";import{M as _}from"./md5.DLPczxzP.js";import{c as q}from"./commonCrud.Cpd55lBt.js";const n="/api/system/user/";function B(t){return o({url:"/api/system/dept/all_dept/",method:"get",params:t})}function R(t){return o({url:n,method:"get",params:t})}function T(t){return o({url:n,method:"post",data:t})}function m(t){return o({url:n+t.id+"/",method:"put",data:t})}function C(t){return o({url:n+t+"/",method:"delete",data:{id:t}})}function O(t){return o({url:n+t+"/reset_to_default_password/",method:"put"})}const S=function({crudExpose:t}){const p=async e=>await R(e),h=async({form:e,row:r})=>(e.id=r.id,await m(e)),f=async({row:e})=>await C(e.id),w=async({form:e})=>await T(e),l=async e=>{await O(e.id),c("重置密码成功")},g=y(),{systemConfig:u}=x(g);return d(()=>u.value),{crudOptions:{table:{remove:{confirmMessage:"是否删除该用户？"}},request:{pageRequest:p,addRequest:w,editRequest:h,delRequest:f},form:{initialForm:{password:d(()=>u.value["base.default_password"])}},actionbar:{buttons:{add:{show:a("user:Create")},export:{text:"导出",title:"导出",show:a("user:Export"),click:e=>v.confirm("确定重设密码吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>l(e.row))}}},rowHandle:{fixed:"right",width:200,buttons:{view:{show:!1},edit:{iconRight:"Edit",type:"text",show:a("user:Update")},remove:{iconRight:"Delete",type:"text",show:a("user:Delete")},custom:{text:"重设密码",type:"text",show:a("user:ResetPassword"),tooltip:{placement:"top",content:"重设密码"},click:e=>{const{row:r}=e;l(r)}}}},columns:{_index:{title:"序号",form:{show:!1},column:{type:"index",align:"center",width:"70px",columnSetDisabled:!0}},username:{title:"账号",search:{show:!0},type:"input",column:{minWidth:100},form:{rules:[{required:!0,message:"账号必填项"}],component:{placeholder:"请输入账号"}}},password:{title:"密码",type:"password",column:{show:!1},editForm:{show:!1},form:{rules:[{required:!0,message:"密码必填项"}],component:{span:12,showPassword:!0,placeholder:"请输入密码"}},valueResolve({form:e}){e.password&&(e.password=_.hashStr(e.password))}},name:{title:"姓名",search:{show:!0},type:"input",column:{minWidth:100},form:{rules:[{required:!0,message:"姓名必填项"}],component:{span:12,placeholder:"请输入姓名"}}},dept:{title:"部门",search:{disabled:!0},type:"dict-tree",dict:s({isTree:!0,url:"/api/system/dept/all_dept/",value:"id",label:"name"}),column:{minWidth:200,formatter({value:e,row:r,index:D}){return r.dept_name_all}},form:{rules:[{required:!0,message:"必填项"}],component:{filterable:!0,placeholder:"请选择",props:{checkStrictly:!0,props:{value:"id",label:"name"}}}}},role:{title:"角色",search:{disabled:!0},type:"dict-select",dict:s({url:"/api/system/role/",value:"id",label:"name"}),column:{minWidth:200},form:{rules:[{required:!0,message:"必填项"}],component:{multiple:!0,filterable:!0,placeholder:"请选择角色"}}},mobile:{title:"手机号码",search:{show:!0},type:"input",column:{minWidth:120},form:{rules:[{max:20,message:"请输入正确的手机号码",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码"}],component:{placeholder:"请输入手机号码"}}},email:{title:"邮箱",column:{width:260},form:{rules:[{type:"email",message:"请输入正确的邮箱地址",trigger:["blur","change"]}],component:{placeholder:"请输入邮箱"}}},gender:{title:"性别",type:"dict-select",dict:s({data:i("gender")}),form:{value:1,component:{span:12}},component:{props:{color:"auto"}}},user_type:{title:"用户类型",search:{show:!0},type:"dict-select",dict:s({data:i("user_type")}),column:{minWidth:100},form:{show:!1,value:0,component:{span:12}}},is_active:{title:"状态",search:{show:!0},type:"dict-radio",column:{component:{name:"fs-dict-switch",activeText:"",inactiveText:"",style:"--el-switch-on-color: var(--el-color-primary); --el-switch-off-color: #dcdfe6",onChange:b(e=>()=>{m(e.row).then(r=>{c(r.msg)})})}},dict:s({data:i("button_status_bool")})},avatar:{title:"头像",type:"avatar-uploader",align:"center",form:{show:!1},column:{minWidth:100}},...q({dept_belong_id:{form:!0,table:!0}})}}}},G=Object.freeze(Object.defineProperty({__proto__:null,createCrudOptions:S},Symbol.toStringTag,{value:"Module"}));export{B as G,G as a,S as c};
