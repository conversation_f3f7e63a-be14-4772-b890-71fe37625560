{"version": 3, "sources": ["../../.pnpm/screenfull@6.0.2/node_modules/screenfull/index.js"], "sourcesContent": ["/* eslint-disable promise/prefer-await-to-then */\n\nconst methodMap = [\n\t[\n\t\t'requestFullscreen',\n\t\t'exitFullscreen',\n\t\t'fullscreenElement',\n\t\t'fullscreenEnabled',\n\t\t'fullscreenchange',\n\t\t'fullscreenerror',\n\t],\n\t// New WebKit\n\t[\n\t\t'webkitRequestFullscreen',\n\t\t'webkitExitFullscreen',\n\t\t'webkitFullscreenElement',\n\t\t'webkitFullscreenEnabled',\n\t\t'webkitfullscreenchange',\n\t\t'webkitfullscreenerror',\n\n\t],\n\t// Old WebKit\n\t[\n\t\t'webkitRequestFullScreen',\n\t\t'webkitCancelFullScreen',\n\t\t'webkitCurrentFullScreenElement',\n\t\t'webkitCancelFullScreen',\n\t\t'webkitfullscreenchange',\n\t\t'webkitfullscreenerror',\n\n\t],\n\t[\n\t\t'mozRequestFullScreen',\n\t\t'mozCancelFullScreen',\n\t\t'mozFullScreenElement',\n\t\t'mozFullScreenEnabled',\n\t\t'mozfullscreenchange',\n\t\t'mozfullscreenerror',\n\t],\n\t[\n\t\t'msRequestFullscreen',\n\t\t'msExitFullscreen',\n\t\t'msFullscreenElement',\n\t\t'msFullscreenEnabled',\n\t\t'MSFullscreenChange',\n\t\t'MSFullscreenError',\n\t],\n];\n\nconst nativeAPI = (() => {\n\tif (typeof document === 'undefined') {\n\t\treturn false;\n\t}\n\n\tconst unprefixedMethods = methodMap[0];\n\tconst returnValue = {};\n\n\tfor (const methodList of methodMap) {\n\t\tconst exitFullscreenMethod = methodList?.[1];\n\t\tif (exitFullscreenMethod in document) {\n\t\t\tfor (const [index, method] of methodList.entries()) {\n\t\t\t\treturnValue[unprefixedMethods[index]] = method;\n\t\t\t}\n\n\t\t\treturn returnValue;\n\t\t}\n\t}\n\n\treturn false;\n})();\n\nconst eventNameMap = {\n\tchange: nativeAPI.fullscreenchange,\n\terror: nativeAPI.fullscreenerror,\n};\n\n// eslint-disable-next-line import/no-mutable-exports\nlet screenfull = {\n\t// eslint-disable-next-line default-param-last\n\trequest(element = document.documentElement, options) {\n\t\treturn new Promise((resolve, reject) => {\n\t\t\tconst onFullScreenEntered = () => {\n\t\t\t\tscreenfull.off('change', onFullScreenEntered);\n\t\t\t\tresolve();\n\t\t\t};\n\n\t\t\tscreenfull.on('change', onFullScreenEntered);\n\n\t\t\tconst returnPromise = element[nativeAPI.requestFullscreen](options);\n\n\t\t\tif (returnPromise instanceof Promise) {\n\t\t\t\treturnPromise.then(onFullScreenEntered).catch(reject);\n\t\t\t}\n\t\t});\n\t},\n\texit() {\n\t\treturn new Promise((resolve, reject) => {\n\t\t\tif (!screenfull.isFullscreen) {\n\t\t\t\tresolve();\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst onFullScreenExit = () => {\n\t\t\t\tscreenfull.off('change', onFullScreenExit);\n\t\t\t\tresolve();\n\t\t\t};\n\n\t\t\tscreenfull.on('change', onFullScreenExit);\n\n\t\t\tconst returnPromise = document[nativeAPI.exitFullscreen]();\n\n\t\t\tif (returnPromise instanceof Promise) {\n\t\t\t\treturnPromise.then(onFullScreenExit).catch(reject);\n\t\t\t}\n\t\t});\n\t},\n\ttoggle(element, options) {\n\t\treturn screenfull.isFullscreen ? screenfull.exit() : screenfull.request(element, options);\n\t},\n\tonchange(callback) {\n\t\tscreenfull.on('change', callback);\n\t},\n\tonerror(callback) {\n\t\tscreenfull.on('error', callback);\n\t},\n\ton(event, callback) {\n\t\tconst eventName = eventNameMap[event];\n\t\tif (eventName) {\n\t\t\tdocument.addEventListener(eventName, callback, false);\n\t\t}\n\t},\n\toff(event, callback) {\n\t\tconst eventName = eventNameMap[event];\n\t\tif (eventName) {\n\t\t\tdocument.removeEventListener(eventName, callback, false);\n\t\t}\n\t},\n\traw: nativeAPI,\n};\n\nObject.defineProperties(screenfull, {\n\tisFullscreen: {\n\t\tget: () => Boolean(document[nativeAPI.fullscreenElement]),\n\t},\n\telement: {\n\t\tenumerable: true,\n\t\tget: () => document[nativeAPI.fullscreenElement] ?? undefined,\n\t},\n\tisEnabled: {\n\t\tenumerable: true,\n\t\t// Coerce to boolean in case of old WebKit.\n\t\tget: () => Boolean(document[nativeAPI.fullscreenEnabled]),\n\t},\n});\n\nif (!nativeAPI) {\n\tscreenfull = {isEnabled: false};\n}\n\nexport default screenfull;\n"], "mappings": ";;;AAEA,IAAM,YAAY;AAAA,EACjB;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA;AAAA,EAEA;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EAED;AAAA;AAAA,EAEA;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EAED;AAAA,EACA;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA,EACA;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACD;AAEA,IAAM,aAAa,MAAM;AACxB,MAAI,OAAO,aAAa,aAAa;AACpC,WAAO;AAAA,EACR;AAEA,QAAM,oBAAoB,UAAU,CAAC;AACrC,QAAM,cAAc,CAAC;AAErB,aAAW,cAAc,WAAW;AACnC,UAAM,uBAAuB,yCAAa;AAC1C,QAAI,wBAAwB,UAAU;AACrC,iBAAW,CAAC,OAAO,MAAM,KAAK,WAAW,QAAQ,GAAG;AACnD,oBAAY,kBAAkB,KAAK,CAAC,IAAI;AAAA,MACzC;AAEA,aAAO;AAAA,IACR;AAAA,EACD;AAEA,SAAO;AACR,GAAG;AAEH,IAAM,eAAe;AAAA,EACpB,QAAQ,UAAU;AAAA,EAClB,OAAO,UAAU;AAClB;AAGA,IAAI,aAAa;AAAA;AAAA,EAEhB,QAAQ,UAAU,SAAS,iBAAiB,SAAS;AACpD,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvC,YAAM,sBAAsB,MAAM;AACjC,mBAAW,IAAI,UAAU,mBAAmB;AAC5C,gBAAQ;AAAA,MACT;AAEA,iBAAW,GAAG,UAAU,mBAAmB;AAE3C,YAAM,gBAAgB,QAAQ,UAAU,iBAAiB,EAAE,OAAO;AAElE,UAAI,yBAAyB,SAAS;AACrC,sBAAc,KAAK,mBAAmB,EAAE,MAAM,MAAM;AAAA,MACrD;AAAA,IACD,CAAC;AAAA,EACF;AAAA,EACA,OAAO;AACN,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvC,UAAI,CAAC,WAAW,cAAc;AAC7B,gBAAQ;AACR;AAAA,MACD;AAEA,YAAM,mBAAmB,MAAM;AAC9B,mBAAW,IAAI,UAAU,gBAAgB;AACzC,gBAAQ;AAAA,MACT;AAEA,iBAAW,GAAG,UAAU,gBAAgB;AAExC,YAAM,gBAAgB,SAAS,UAAU,cAAc,EAAE;AAEzD,UAAI,yBAAyB,SAAS;AACrC,sBAAc,KAAK,gBAAgB,EAAE,MAAM,MAAM;AAAA,MAClD;AAAA,IACD,CAAC;AAAA,EACF;AAAA,EACA,OAAO,SAAS,SAAS;AACxB,WAAO,WAAW,eAAe,WAAW,KAAK,IAAI,WAAW,QAAQ,SAAS,OAAO;AAAA,EACzF;AAAA,EACA,SAAS,UAAU;AAClB,eAAW,GAAG,UAAU,QAAQ;AAAA,EACjC;AAAA,EACA,QAAQ,UAAU;AACjB,eAAW,GAAG,SAAS,QAAQ;AAAA,EAChC;AAAA,EACA,GAAG,OAAO,UAAU;AACnB,UAAM,YAAY,aAAa,KAAK;AACpC,QAAI,WAAW;AACd,eAAS,iBAAiB,WAAW,UAAU,KAAK;AAAA,IACrD;AAAA,EACD;AAAA,EACA,IAAI,OAAO,UAAU;AACpB,UAAM,YAAY,aAAa,KAAK;AACpC,QAAI,WAAW;AACd,eAAS,oBAAoB,WAAW,UAAU,KAAK;AAAA,IACxD;AAAA,EACD;AAAA,EACA,KAAK;AACN;AAEA,OAAO,iBAAiB,YAAY;AAAA,EACnC,cAAc;AAAA,IACb,KAAK,MAAM,QAAQ,SAAS,UAAU,iBAAiB,CAAC;AAAA,EACzD;AAAA,EACA,SAAS;AAAA,IACR,YAAY;AAAA,IACZ,KAAK,MAAM,SAAS,UAAU,iBAAiB,KAAK;AAAA,EACrD;AAAA,EACA,WAAW;AAAA,IACV,YAAY;AAAA;AAAA,IAEZ,KAAK,MAAM,QAAQ,SAAS,UAAU,iBAAiB,CAAC;AAAA,EACzD;AACD,CAAC;AAED,IAAI,CAAC,WAAW;AACf,eAAa,EAAC,WAAW,MAAK;AAC/B;AAEA,IAAO,qBAAQ;", "names": []}