<template>
  <div class="customer-analysis-container">
    <!-- 页面标题 -->
    <el-card class="header-card">
      <template #header>
        <div class="card-header">
          <div class="header-info">
            <span class="update-time">更新时间: {{ updateTime }}</span>
            <el-button type="primary" size="small" @click="refreshData" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新数据
            </el-button>
          </div>
        </div>
      </template>

      <div class="welcome-content">
        <h2>客户下单情况分析</h2>
        <p>分析客户最后下单时间，识别流失风险，优化客户关系管理</p>
      </div>
    </el-card>

    <!-- 查询条件 -->
    <el-card class="filter-card">
      <div class="filter-form">
        <el-row :gutter="16" style="align-items: center;">
          <el-col :span="5">
            <div class="filter-item">
              <label>阈值天数：</label>
              <el-select v-model="queryParams.thresholdDays" placeholder="选择阈值天数" @change="handleQuery" style="width: 100%;">
                <el-option label="3天" :value="3" />
                <el-option label="5天" :value="5" />
                <el-option label="7天" :value="7" />
                <el-option label="10天" :value="10" />
                <el-option label="15天" :value="15" />
                <el-option label="30天" :value="30" />
              </el-select>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="filter-item">
              <label>公司选择：</label>
              <el-select
                v-model="queryParams.company"
                placeholder="选择公司名称"
                clearable
                @change="handleQuery"
                @clear="handleQuery"
                style="width: 100%;"
              >
                <el-option label="全部公司" value="" />
                <el-option
                  v-for="company in companyList"
                  :key="company.value"
                  :label="company.label"
                  :value="company.value"
                />
              </el-select>
            </div>
          </el-col>
          <el-col :span="5">
            <div class="filter-item">
              <label>客户名称：</label>
              <el-input
                v-model="queryParams.customerName"
                placeholder="输入客户名称"
                clearable
                @clear="handleQuery"
                @keyup.enter="handleQuery"
                style="width: 100%;"
              />
            </div>
          </el-col>
          <el-col :span="8">
            <div class="filter-actions">
              <el-button type="primary" @click="handleQuery" :loading="loading">
                <el-icon><Search /></el-icon>
                查询
              </el-button>
              <el-button @click="handleReset">
                <el-icon><RefreshLeft /></el-icon>
                重置
              </el-button>
              <el-button type="success" @click="handleExport" :loading="exportLoading">
                <el-icon><Download /></el-icon>
                导出
              </el-button>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card total-customers" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon size="32"><UserFilled /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-title">客户总数</div>
                <div class="stat-value">{{ statistics.totalCustomers }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card high-risk" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon size="32"><WarningFilled /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-title">高风险客户</div>
                <div class="stat-value">{{ statistics.highRiskCustomers }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card medium-risk" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon size="32"><Warning /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-title">中风险客户</div>
                <div class="stat-value">{{ statistics.mediumRiskCustomers }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card low-risk" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon size="32"><CircleCheckFilled /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-title">低风险客户</div>
                <div class="stat-value">{{ statistics.lowRiskCustomers }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 客户状态分布图表 -->
    <el-card class="chart-card">
      <template #header>
        <div class="card-header">
          <span>客户状态分布</span>
        </div>
      </template>
      <div ref="statusChartRef" class="chart-container"></div>
    </el-card>

    <!-- 客户列表 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>客户分析详情 (阈值: {{ queryParams.thresholdDays }}天)</span>
          <div class="table-info">
            <span>共 {{ statistics.totalCustomers }} 条记录</span>
          </div>
        </div>
      </template>

      <el-table 
        :data="customerList" 
        v-loading="loading"
        stripe
        border
        height="600"
        style="width: 100%"
      >
        <el-table-column prop="公司名称" label="公司名称" width="250" show-overflow-tooltip />
        <el-table-column prop="客户名称" label="客户名称" width="200" show-overflow-tooltip />
        <el-table-column prop="最后下单时间" label="最后下单时间" width="120" align="center" />
        <el-table-column prop="距离最后下单天数" label="距离天数" width="100" align="center">
          <template #default="scope">
            <span v-if="scope.row.距离最后下单天数 === 999999">从未下单</span>
            <span v-else>{{ scope.row.距离最后下单天数 }}天</span>
          </template>
        </el-table-column>
        <el-table-column prop="客户状态" label="客户状态" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.客户状态)" size="small">
              {{ scope.row.客户状态 }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="流失风险" label="流失风险" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getRiskTagType(scope.row.流失风险)" size="small">
              {{ scope.row.流失风险 }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="历史订单总数" label="历史订单数" width="120" align="center" />
        <el-table-column prop="历史订单总额" label="历史订单总额" width="150" align="right">
          <template #default="scope">
            ¥{{ formatCurrency(scope.row.历史订单总额) }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :small="false"
          :disabled="loading"
          :background="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { 
  User, 
  Refresh, 
  Search, 
  RefreshLeft, 
  Download,
  UserFilled,
  WarningFilled,
  Warning,
  CircleCheckFilled
} from '@element-plus/icons-vue';
import * as echarts from 'echarts';
import {
  customerAnalysisQuery,
  customerAnalysisExport,
  getCompanyList,
  formatCurrency,
  getStatusTagType,
  getRiskTagType
} from '/@/api/customer-analysis/index';

// 响应式数据
const loading = ref(false);
const exportLoading = ref(false);
const updateTime = ref('');
const statusChartRef = ref<HTMLElement>();

// 查询参数
const queryParams = reactive({
  thresholdDays: 7,
  company: '',
  customerName: ''
});

// 分页参数
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
});

// 统计数据
const statistics = reactive({
  totalCustomers: 0,
  highRiskCustomers: 0,
  mediumRiskCustomers: 0,
  lowRiskCustomers: 0,
  statusStats: {} as Record<string, number>
});

// 客户列表
const customerList = ref<any[]>([]);

// 公司列表
const companyList = ref<any[]>([]);

// 图表实例
let statusChart: echarts.ECharts | null = null;

// 查询数据
const handleQuery = async () => {
  loading.value = true;
  try {
    const params = {
      ...queryParams,
      page: pagination.currentPage,
      pageSize: pagination.pageSize
    };

    const response = await customerAnalysisQuery(params);

    if (response.code === 200) {
      const data = response.data;

      // 更新统计数据
      Object.assign(statistics, data.statistics);

      // 更新客户列表
      customerList.value = data.list || [];

      // 更新分页信息
      pagination.total = data.total || 0;

      // 更新时间
      updateTime.value = new Date().toLocaleString();

      // 更新图表
      await nextTick();
      updateStatusChart();

      ElMessage.success('查询成功');
    } else {
      ElMessage.error(response.message || '查询失败');
    }
  } catch (error) {
    console.error('查询失败:', error);
    ElMessage.error('查询失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// 重置查询条件
const handleReset = () => {
  queryParams.thresholdDays = 7;
  queryParams.company = '';
  queryParams.customerName = '';
  pagination.currentPage = 1;
  handleQuery();
};

// 分页大小改变
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  pagination.currentPage = 1;
  handleQuery();
};

// 当前页改变
const handleCurrentChange = (val: number) => {
  pagination.currentPage = val;
  handleQuery();
};

// 刷新数据
const refreshData = () => {
  handleQuery();
};

// 导出数据
const handleExport = async () => {
  if (customerList.value.length === 0) {
    ElMessage.warning('没有数据可以导出');
    return;
  }

  try {
    await ElMessageBox.confirm('确认导出客户分析数据？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    });

    exportLoading.value = true;
    
    const response = await customerAnalysisExport(queryParams);
    
    // 创建下载链接
    const blob = new Blob([response], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    
    const currentTime = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
    link.download = `客户分析报告_${queryParams.thresholdDays}天阈值_${currentTime}.xlsx`;
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    
    ElMessage.success('导出成功');
  } catch (error) {
    if (error !== 'cancel') {
      console.error('导出失败:', error);
      ElMessage.error('导出失败，请稍后重试');
    }
  } finally {
    exportLoading.value = false;
  }
};

// 更新状态分布图表
const updateStatusChart = () => {
  if (!statusChartRef.value) return;
  
  if (!statusChart) {
    statusChart = echarts.init(statusChartRef.value);
  }
  
  const statusData = Object.entries(statistics.statusStats).map(([name, value]) => ({
    name,
    value
  }));
  
  const option = {
    title: {
      text: '客户状态分布',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'middle'
    },
    series: [
      {
        name: '客户状态',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: statusData
      }
    ],
    color: ['#67C23A', '#E6A23C', '#F56C6C', '#909399', '#409EFF', '#F78989']
  };
  
  statusChart.setOption(option);
};

// 加载公司列表
const loadCompanyList = async () => {
  try {
    const response = await getCompanyList();
    if (response.code === 200) {
      companyList.value = response.data || [];
    } else {
      ElMessage.error(response.message || '获取公司列表失败');
    }
  } catch (error) {
    console.error('获取公司列表失败:', error);
    ElMessage.error('获取公司列表失败');
  }
};

// 组件挂载
onMounted(() => {
  loadCompanyList();  // 先加载公司列表
  handleQuery();

  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    if (statusChart) {
      statusChart.resize();
    }
  });
});

// 导出函数供模板使用
defineExpose({
  formatCurrency,
  getStatusTagType,
  getRiskTagType
});
</script>

<style scoped>
.customer-analysis-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-header .title {
  font-size: 18px;
  font-weight: bold;
  margin-left: 8px;
  color: #303133;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.update-time {
  color: #909399;
  font-size: 14px;
}

.welcome-content {
  text-align: center;
  padding: 20px 0;
}

.welcome-content h2 {
  color: #303133;
  margin-bottom: 10px;
  font-size: 24px;
}

.welcome-content p {
  color: #606266;
  font-size: 16px;
  margin: 0;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-form {
  padding: 10px 0;
}

.filter-item {
  display: flex;
  align-items: center;
  margin-bottom: 0;
  white-space: nowrap;
}

.filter-item label {
  width: 80px;
  text-align: right;
  margin-right: 8px;
  color: #606266;
  font-weight: 500;
  flex-shrink: 0;
}

.filter-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 0;
}

.stats-section {
  margin-bottom: 20px;
}

.stat-card {
  height: 120px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 20px;
}

.stat-icon {
  margin-right: 20px;
  padding: 15px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-info {
  flex: 1;
}

.stat-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
}

/* 统计卡片主题色 */
.total-customers .stat-icon {
  background-color: #e3f2fd;
  color: #1976d2;
}

.high-risk .stat-icon {
  background-color: #ffebee;
  color: #d32f2f;
}

.medium-risk .stat-icon {
  background-color: #fff3e0;
  color: #f57c00;
}

.low-risk .stat-icon {
  background-color: #e8f5e8;
  color: #388e3c;
}

.chart-card {
  margin-bottom: 20px;
}

.chart-container {
  height: 400px;
  width: 100%;
}

.table-card {
  margin-bottom: 20px;
}

.table-info {
  color: #909399;
  font-size: 14px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .customer-analysis-container {
    padding: 15px;
  }

  .stat-card {
    margin-bottom: 15px;
  }
}

@media (max-width: 768px) {
  .customer-analysis-container {
    padding: 10px;
  }

  .filter-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .filter-item label {
    width: auto;
    margin-bottom: 5px;
    margin-right: 0;
  }

  .filter-actions {
    justify-content: center;
    flex-wrap: wrap;
  }

  .stat-content {
    padding: 15px;
  }

  .stat-icon {
    margin-right: 15px;
    padding: 10px;
  }

  .stat-value {
    font-size: 24px;
  }
}
</style>
