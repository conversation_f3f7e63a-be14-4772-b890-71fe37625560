import{d as U,M as F,Q as B,R as T,g as k,h as b,c as M,j as N,a0 as A,k as u,a as R,o as V,l as s,m as D,w as o,e as y,f as $,s as O,_ as j,F as z}from"./vue.BNx9QYep.js";import{a7 as K,u as L,a6 as Q,a8 as J,O as Z,g as G,a9 as H,aa as W,N as I,D as X,e as Y,Q as x}from"./index.BHZI5pdK.js";import{a as ee}from"./formatTime.in1fXasu.js";import{a as se}from"./api.DfN45YxI.js";import{M as q}from"./md5.DLPczxzP.js";import{_ as oe}from"./_plugin-vue_export-helper.DlAUqK2U.js";const ae=U({name:"changePwd",setup(){const{t:e}=K.useI18n(),a=L(),{themeConfig:P}=F(a),{userInfos:E}=F(Q()),d=B(),_=T(),n=k({isShowPassword:!1,ruleForm:{username:"",password:"",password_regain:""},loading:{signIn:!1}}),p=k({username:[{required:!0,message:"请填写账号",trigger:"blur"}],password:[{required:!0,message:"请填写密码",trigger:"blur"},{validator:(m,r,t)=>{const w=new RegExp("(?=.*[0-9])(?=.*[a-zA-Z]).{8,30}");r===""?t(new Error("请输入密码")):w.test(r)?(n.ruleForm.password!==""&&i.value.validateField("password"),t()):t(new Error("您的密码复杂度太低(密码中必须包含字母、数字)"))},trigger:"blur"}],password_regain:[{required:!0,message:"请填写密码",trigger:"blur"},{validator:(m,r,t)=>{r===""?t(new Error("请再次输入密码")):r!==n.ruleForm.password?t(new Error("两次输入密码不一致!")):t()},trigger:"blur"}]}),i=b(),f=M(()=>ee(new Date)),h=async()=>{window.open(G("/api/system/apply_for_trial/"))},l=async()=>{i.value&&await i.value.validate(m=>{m?se({...n.ruleForm,password:q.hashStr(n.ruleForm.password),password_regain:q.hashStr(n.ruleForm.password_regain)}).then(r=>{r.code===2e3&&(P.value.isRequestRoutes?(W(),C()):(H(),C()))}).catch(r=>{I("登录失败")}):I("请填写登录信息")})},C=()=>{var t,w,S,v;X().getSystemDictionarys();let m=f.value;(t=d.query)!=null&&t.redirect?_.push({path:(w=d.query)==null?void 0:w.redirect,query:Object.keys((S=d.query)==null?void 0:S.params).length>0?JSON.parse((v=d.query)==null?void 0:v.params):""}):_.push("/"),n.loading.signIn=!0;const r=e("message.signInText");Y.success(`${m}，${r}`),x.start()};return N(()=>{n.ruleForm.username=J.get("username"),Z().getSystemConfigs()}),{loginClick:l,loginSuccess:C,state:n,formRef:i,rules:p,applyBtnClick:h,showApply:()=>window.location.href.indexOf("public")!=-1,...A(n)}}}),ne={key:0,style:{"text-align":"center"}};function re(e,a,P,E,d,_){const n=u("ele-User"),c=u("el-icon"),g=u("el-input"),p=u("el-form-item"),i=u("ele-Unlock"),f=u("el-button"),h=u("el-form");return V(),R(z,null,[s(h,{ref:"formRef",size:"large",class:"login-content-form",model:e.state.ruleForm,rules:e.rules,onKeyup:j(e.loginClick,["enter"])},{default:o(()=>[s(p,{class:"login-animation1",prop:"username"},{default:o(()=>[s(g,{type:"text",placeholder:e.$t("message.account.accountPlaceholder1"),readonly:"",modelValue:e.ruleForm.username,"onUpdate:modelValue":a[0]||(a[0]=l=>e.ruleForm.username=l),clearable:"",autocomplete:"off"},{prefix:o(()=>[s(c,{class:"el-input__icon"},{default:o(()=>[s(n)]),_:1})]),_:1},8,["placeholder","modelValue"])]),_:1}),s(p,{class:"login-animation2",prop:"password"},{default:o(()=>[s(g,{type:e.isShowPassword?"text":"password",placeholder:e.$t("message.account.accountPlaceholder4"),modelValue:e.ruleForm.password,"onUpdate:modelValue":a[2]||(a[2]=l=>e.ruleForm.password=l)},{prefix:o(()=>[s(c,{class:"el-input__icon"},{default:o(()=>[s(i)]),_:1})]),suffix:o(()=>[y("i",{class:$(["iconfont el-input__icon login-content-password",e.isShowPassword?"icon-yincangmima":"icon-xianshimima"]),onClick:a[1]||(a[1]=l=>e.isShowPassword=!e.isShowPassword)},null,2)]),_:1},8,["type","placeholder","modelValue"])]),_:1}),s(p,{class:"login-animation3",prop:"password_regain"},{default:o(()=>[s(g,{type:e.isShowPassword?"text":"password",placeholder:e.$t("message.account.accountPlaceholder5"),modelValue:e.ruleForm.password_regain,"onUpdate:modelValue":a[4]||(a[4]=l=>e.ruleForm.password_regain=l)},{prefix:o(()=>[s(c,{class:"el-input__icon"},{default:o(()=>[s(i)]),_:1})]),suffix:o(()=>[y("i",{class:$(["iconfont el-input__icon login-content-password",e.isShowPassword?"icon-yincangmima":"icon-xianshimima"]),onClick:a[3]||(a[3]=l=>e.isShowPassword=!e.isShowPassword)},null,2)]),_:1},8,["type","placeholder","modelValue"])]),_:1}),s(p,{class:"login-animation4"},{default:o(()=>[s(f,{type:"primary",class:"login-content-submit",round:"",onClick:e.loginClick,loading:e.loading.signIn},{default:o(()=>[y("span",null,O(e.$t("message.account.accountBtnText")),1)]),_:1},8,["onClick","loading"])]),_:1})]),_:1},8,["model","rules","onKeyup"]),e.showApply()?(V(),R("div",ne,[s(f,{class:"login-content-apply",link:"",type:"primary",plain:"",round:"",onClick:e.applyBtnClick},{default:o(()=>a[5]||(a[5]=[y("span",null,"申请试用",-1)])),_:1,__:[5]},8,["onClick"])])):D("",!0)],64)}const ce=oe(ae,[["render",re],["__scopeId","data-v-15827e38"]]);export{ce as default};
