from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from datetime import datetime, timedelta
from django.db import connections
import logging

logger = logging.getLogger(__name__)


@api_view(['GET'])
@permission_classes([AllowAny])
def dashboard_overview(request):
    """
    数据分析首页概览接口
    返回当月销售金额汇总、各公司销售金额汇总和图表数据
    """
    try:
        # 获取当前年月
        current_date = datetime.now()
        current_year = current_date.year
        current_month = current_date.month
        
        # 获取上月数据用于对比
        last_month_date = current_date.replace(day=1) - timedelta(days=1)
        last_year = last_month_date.year
        last_month = last_month_date.month
        
        # 当月销售金额汇总SQL - 查询所有公司数据
        current_month_sql = f"""
        SELECT
            COUNT(DISTINCT o.FID) AS 订单数量,
            COUNT(DISTINCT o.FCUSTID) AS 客户数量,
            COUNT(DISTINCT e.FMATERIALID) AS 产品种类,
            ROUND(SUM(f.FALLAMOUNT), 2) AS 销售总额,
            ROUND(SUM(e.FBASEUNITQTY), 2) AS 销售数量
        FROM T_SAL_ORDER o WITH (NOLOCK)
        INNER JOIN T_SAL_ORDERENTRY e WITH (NOLOCK) ON o.FID = e.FID
        INNER JOIN T_SAL_ORDERENTRY_F f WITH (NOLOCK) ON e.FENTRYID = f.FENTRYID
        WHERE YEAR(o.FDATE) = {current_year}
            AND MONTH(o.FDATE) = {current_month}
            AND ISNULL(e.F_JSHL_CHECKBOX_QTR, '0') != '1'
            AND e.FBASEUNITQTY > 0
            AND f.FALLAMOUNT > 0
        """
        
        # 上月销售金额汇总SQL（用于对比）- 查询所有公司数据
        last_month_sql = f"""
        SELECT
            ROUND(SUM(f.FALLAMOUNT), 2) AS 上月销售总额
        FROM T_SAL_ORDER o WITH (NOLOCK)
        INNER JOIN T_SAL_ORDERENTRY e WITH (NOLOCK) ON o.FID = e.FID
        INNER JOIN T_SAL_ORDERENTRY_F f WITH (NOLOCK) ON e.FENTRYID = f.FENTRYID
        WHERE YEAR(o.FDATE) = {last_year}
            AND MONTH(o.FDATE) = {last_month}
            AND ISNULL(e.F_JSHL_CHECKBOX_QTR, '0') != '1'
            AND e.FBASEUNITQTY > 0
            AND f.FALLAMOUNT > 0
        """
        
        # 各公司销售金额汇总SQL - 查询所有公司数据
        company_sales_sql = f"""
        SELECT
            ISNULL(org_l.FNAME, '未知公司') AS 公司名称,
            COUNT(DISTINCT o.FID) AS 订单数量,
            COUNT(DISTINCT o.FCUSTID) AS 客户数量,
            ROUND(SUM(f.FALLAMOUNT), 2) AS 销售金额,
            ROUND(SUM(e.FBASEUNITQTY), 2) AS 销售数量
        FROM T_SAL_ORDER o WITH (NOLOCK)
        INNER JOIN T_SAL_ORDERENTRY e WITH (NOLOCK) ON o.FID = e.FID
        INNER JOIN T_SAL_ORDERENTRY_F f WITH (NOLOCK) ON e.FENTRYID = f.FENTRYID
        LEFT JOIN T_ORG_ORGANIZATIONS org WITH (NOLOCK) ON o.FSALEORGID = org.FORGID
        LEFT JOIN T_ORG_ORGANIZATIONS_L org_l WITH (NOLOCK) ON org.FORGID = org_l.FORGID AND org_l.FLOCALEID = 2052
        WHERE YEAR(o.FDATE) = {current_year}
            AND MONTH(o.FDATE) = {current_month}
            AND ISNULL(e.F_JSHL_CHECKBOX_QTR, '0') != '1'
            AND e.FBASEUNITQTY > 0
            AND f.FALLAMOUNT > 0
        GROUP BY org_l.FNAME
        ORDER BY 销售金额 DESC
        """
        
        # 近6个月销售趋势SQL - 查询所有公司数据
        trend_sql = f"""
        SELECT
            CONVERT(VARCHAR(7), o.FDATE, 120) AS 月份,
            ROUND(SUM(f.FALLAMOUNT), 2) AS 销售金额
        FROM T_SAL_ORDER o WITH (NOLOCK)
        INNER JOIN T_SAL_ORDERENTRY e WITH (NOLOCK) ON o.FID = e.FID
        INNER JOIN T_SAL_ORDERENTRY_F f WITH (NOLOCK) ON e.FENTRYID = f.FENTRYID
        WHERE o.FDATE >= DATEADD(MONTH, -6, '{current_year}-{current_month:02d}-01')
            AND o.FDATE < DATEADD(MONTH, 1, '{current_year}-{current_month:02d}-01')
            AND ISNULL(e.F_JSHL_CHECKBOX_QTR, '0') != '1'
            AND e.FBASEUNITQTY > 0
            AND f.FALLAMOUNT > 0
        GROUP BY CONVERT(VARCHAR(7), o.FDATE, 120)
        ORDER BY 月份
        """
        
        # 执行查询 - 使用Django的SQL Server连接，每个查询单独执行
        # 当月汇总数据
        with connections['sqlserver'].cursor() as cursor:
            cursor.execute(current_month_sql)
            current_month_result = cursor.fetchone()

        # 上月汇总数据
        with connections['sqlserver'].cursor() as cursor:
            cursor.execute(last_month_sql)
            last_month_result = cursor.fetchone()

        # 各公司销售数据
        with connections['sqlserver'].cursor() as cursor:
            cursor.execute(company_sales_sql)
            company_columns = [col[0] for col in cursor.description]
            company_results = cursor.fetchall()
            company_sales = [dict(zip(company_columns, row)) for row in company_results]

        # 销售趋势数据
        with connections['sqlserver'].cursor() as cursor:
            cursor.execute(trend_sql)
            trend_columns = [col[0] for col in cursor.description]
            trend_results = cursor.fetchall()
            trend_data = [dict(zip(trend_columns, row)) for row in trend_results]
        
        # 处理当月汇总数据
        current_stats = {
            '订单数量': current_month_result[0] if current_month_result and current_month_result[0] else 0,
            '客户数量': current_month_result[1] if current_month_result and current_month_result[1] else 0,
            '产品种类': current_month_result[2] if current_month_result and current_month_result[2] else 0,
            '销售总额': current_month_result[3] if current_month_result and current_month_result[3] else 0,
            '销售数量': current_month_result[4] if current_month_result and current_month_result[4] else 0,
        }

        # 计算环比增长
        last_month_amount = last_month_result[0] if last_month_result and last_month_result[0] else 0
        growth_rate = 0
        if last_month_amount > 0:
            growth_rate = round((current_stats['销售总额'] - last_month_amount) / last_month_amount * 100, 2)
        
        # 构建响应数据
        response_data = {
            'code': 200,
            'message': '查询成功',
            'data': {
                'currentMonth': {
                    'year': current_year,
                    'month': current_month,
                    'statistics': current_stats,
                    'growthRate': growth_rate,
                    'lastMonthAmount': last_month_amount
                },
                'companySales': company_sales,
                'trendData': trend_data,
                'updateTime': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        }
        
        return Response(response_data)
        
    except Exception as e:
        logger.error(f"数据分析首页查询失败: {str(e)}")
        return Response({
            'code': 500,
            'message': f'查询失败: {str(e)}',
            'data': {
                'currentMonth': {
                    'year': datetime.now().year,
                    'month': datetime.now().month,
                    'statistics': {
                        '订单数量': 0,
                        '客户数量': 0,
                        '产品种类': 0,
                        '销售总额': 0,
                        '销售数量': 0,
                    },
                    'growthRate': 0,
                    'lastMonthAmount': 0
                },
                'companySales': [],
                'trendData': [],
                'updateTime': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
