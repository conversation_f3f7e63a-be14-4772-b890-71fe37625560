import {
  Ct,
  bt
} from "./chunk-KSP3GDI3.js";
import "./chunk-NXSV4UZF.js";
import "./chunk-NROEEZWH.js";
import "./chunk-TMUISJ4F.js";
import "./chunk-ISH6AKKV.js";
import "./chunk-WPKI25LT.js";
import "./chunk-UOYEQWWP.js";
import {
  cloneDeep_default,
  merge_default
} from "./chunk-YNRHTVZR.js";
import "./chunk-YLGFFQGZ.js";
import "./chunk-NHLVTWLD.js";
import {
  __commonJS,
  __toESM
} from "./chunk-LK32TJAX.js";

// node_modules/.pnpm/spark-md5@3.0.2/node_modules/spark-md5/spark-md5.js
var require_spark_md5 = __commonJS({
  "node_modules/.pnpm/spark-md5@3.0.2/node_modules/spark-md5/spark-md5.js"(exports, module) {
    (function(factory) {
      if (typeof exports === "object") {
        module.exports = factory();
      } else if (typeof define === "function" && define.amd) {
        define(factory);
      } else {
        var glob;
        try {
          glob = window;
        } catch (e) {
          glob = self;
        }
        glob.SparkMD5 = factory();
      }
    })(function(undefined2) {
      "use strict";
      var add32 = function(a, b2) {
        return a + b2 & 4294967295;
      }, hex_chr = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "a", "b", "c", "d", "e", "f"];
      function cmn(q2, a, b2, x, s, t) {
        a = add32(add32(a, q2), add32(x, t));
        return add32(a << s | a >>> 32 - s, b2);
      }
      function md5cycle(x, k) {
        var a = x[0], b2 = x[1], c = x[2], d = x[3];
        a += (b2 & c | ~b2 & d) + k[0] - 680876936 | 0;
        a = (a << 7 | a >>> 25) + b2 | 0;
        d += (a & b2 | ~a & c) + k[1] - 389564586 | 0;
        d = (d << 12 | d >>> 20) + a | 0;
        c += (d & a | ~d & b2) + k[2] + 606105819 | 0;
        c = (c << 17 | c >>> 15) + d | 0;
        b2 += (c & d | ~c & a) + k[3] - 1044525330 | 0;
        b2 = (b2 << 22 | b2 >>> 10) + c | 0;
        a += (b2 & c | ~b2 & d) + k[4] - 176418897 | 0;
        a = (a << 7 | a >>> 25) + b2 | 0;
        d += (a & b2 | ~a & c) + k[5] + 1200080426 | 0;
        d = (d << 12 | d >>> 20) + a | 0;
        c += (d & a | ~d & b2) + k[6] - 1473231341 | 0;
        c = (c << 17 | c >>> 15) + d | 0;
        b2 += (c & d | ~c & a) + k[7] - 45705983 | 0;
        b2 = (b2 << 22 | b2 >>> 10) + c | 0;
        a += (b2 & c | ~b2 & d) + k[8] + 1770035416 | 0;
        a = (a << 7 | a >>> 25) + b2 | 0;
        d += (a & b2 | ~a & c) + k[9] - 1958414417 | 0;
        d = (d << 12 | d >>> 20) + a | 0;
        c += (d & a | ~d & b2) + k[10] - 42063 | 0;
        c = (c << 17 | c >>> 15) + d | 0;
        b2 += (c & d | ~c & a) + k[11] - 1990404162 | 0;
        b2 = (b2 << 22 | b2 >>> 10) + c | 0;
        a += (b2 & c | ~b2 & d) + k[12] + 1804603682 | 0;
        a = (a << 7 | a >>> 25) + b2 | 0;
        d += (a & b2 | ~a & c) + k[13] - 40341101 | 0;
        d = (d << 12 | d >>> 20) + a | 0;
        c += (d & a | ~d & b2) + k[14] - 1502002290 | 0;
        c = (c << 17 | c >>> 15) + d | 0;
        b2 += (c & d | ~c & a) + k[15] + 1236535329 | 0;
        b2 = (b2 << 22 | b2 >>> 10) + c | 0;
        a += (b2 & d | c & ~d) + k[1] - 165796510 | 0;
        a = (a << 5 | a >>> 27) + b2 | 0;
        d += (a & c | b2 & ~c) + k[6] - 1069501632 | 0;
        d = (d << 9 | d >>> 23) + a | 0;
        c += (d & b2 | a & ~b2) + k[11] + 643717713 | 0;
        c = (c << 14 | c >>> 18) + d | 0;
        b2 += (c & a | d & ~a) + k[0] - 373897302 | 0;
        b2 = (b2 << 20 | b2 >>> 12) + c | 0;
        a += (b2 & d | c & ~d) + k[5] - 701558691 | 0;
        a = (a << 5 | a >>> 27) + b2 | 0;
        d += (a & c | b2 & ~c) + k[10] + 38016083 | 0;
        d = (d << 9 | d >>> 23) + a | 0;
        c += (d & b2 | a & ~b2) + k[15] - 660478335 | 0;
        c = (c << 14 | c >>> 18) + d | 0;
        b2 += (c & a | d & ~a) + k[4] - 405537848 | 0;
        b2 = (b2 << 20 | b2 >>> 12) + c | 0;
        a += (b2 & d | c & ~d) + k[9] + 568446438 | 0;
        a = (a << 5 | a >>> 27) + b2 | 0;
        d += (a & c | b2 & ~c) + k[14] - 1019803690 | 0;
        d = (d << 9 | d >>> 23) + a | 0;
        c += (d & b2 | a & ~b2) + k[3] - 187363961 | 0;
        c = (c << 14 | c >>> 18) + d | 0;
        b2 += (c & a | d & ~a) + k[8] + 1163531501 | 0;
        b2 = (b2 << 20 | b2 >>> 12) + c | 0;
        a += (b2 & d | c & ~d) + k[13] - 1444681467 | 0;
        a = (a << 5 | a >>> 27) + b2 | 0;
        d += (a & c | b2 & ~c) + k[2] - 51403784 | 0;
        d = (d << 9 | d >>> 23) + a | 0;
        c += (d & b2 | a & ~b2) + k[7] + 1735328473 | 0;
        c = (c << 14 | c >>> 18) + d | 0;
        b2 += (c & a | d & ~a) + k[12] - 1926607734 | 0;
        b2 = (b2 << 20 | b2 >>> 12) + c | 0;
        a += (b2 ^ c ^ d) + k[5] - 378558 | 0;
        a = (a << 4 | a >>> 28) + b2 | 0;
        d += (a ^ b2 ^ c) + k[8] - 2022574463 | 0;
        d = (d << 11 | d >>> 21) + a | 0;
        c += (d ^ a ^ b2) + k[11] + 1839030562 | 0;
        c = (c << 16 | c >>> 16) + d | 0;
        b2 += (c ^ d ^ a) + k[14] - 35309556 | 0;
        b2 = (b2 << 23 | b2 >>> 9) + c | 0;
        a += (b2 ^ c ^ d) + k[1] - 1530992060 | 0;
        a = (a << 4 | a >>> 28) + b2 | 0;
        d += (a ^ b2 ^ c) + k[4] + 1272893353 | 0;
        d = (d << 11 | d >>> 21) + a | 0;
        c += (d ^ a ^ b2) + k[7] - 155497632 | 0;
        c = (c << 16 | c >>> 16) + d | 0;
        b2 += (c ^ d ^ a) + k[10] - 1094730640 | 0;
        b2 = (b2 << 23 | b2 >>> 9) + c | 0;
        a += (b2 ^ c ^ d) + k[13] + 681279174 | 0;
        a = (a << 4 | a >>> 28) + b2 | 0;
        d += (a ^ b2 ^ c) + k[0] - 358537222 | 0;
        d = (d << 11 | d >>> 21) + a | 0;
        c += (d ^ a ^ b2) + k[3] - 722521979 | 0;
        c = (c << 16 | c >>> 16) + d | 0;
        b2 += (c ^ d ^ a) + k[6] + 76029189 | 0;
        b2 = (b2 << 23 | b2 >>> 9) + c | 0;
        a += (b2 ^ c ^ d) + k[9] - 640364487 | 0;
        a = (a << 4 | a >>> 28) + b2 | 0;
        d += (a ^ b2 ^ c) + k[12] - 421815835 | 0;
        d = (d << 11 | d >>> 21) + a | 0;
        c += (d ^ a ^ b2) + k[15] + 530742520 | 0;
        c = (c << 16 | c >>> 16) + d | 0;
        b2 += (c ^ d ^ a) + k[2] - 995338651 | 0;
        b2 = (b2 << 23 | b2 >>> 9) + c | 0;
        a += (c ^ (b2 | ~d)) + k[0] - 198630844 | 0;
        a = (a << 6 | a >>> 26) + b2 | 0;
        d += (b2 ^ (a | ~c)) + k[7] + 1126891415 | 0;
        d = (d << 10 | d >>> 22) + a | 0;
        c += (a ^ (d | ~b2)) + k[14] - 1416354905 | 0;
        c = (c << 15 | c >>> 17) + d | 0;
        b2 += (d ^ (c | ~a)) + k[5] - 57434055 | 0;
        b2 = (b2 << 21 | b2 >>> 11) + c | 0;
        a += (c ^ (b2 | ~d)) + k[12] + 1700485571 | 0;
        a = (a << 6 | a >>> 26) + b2 | 0;
        d += (b2 ^ (a | ~c)) + k[3] - 1894986606 | 0;
        d = (d << 10 | d >>> 22) + a | 0;
        c += (a ^ (d | ~b2)) + k[10] - 1051523 | 0;
        c = (c << 15 | c >>> 17) + d | 0;
        b2 += (d ^ (c | ~a)) + k[1] - 2054922799 | 0;
        b2 = (b2 << 21 | b2 >>> 11) + c | 0;
        a += (c ^ (b2 | ~d)) + k[8] + 1873313359 | 0;
        a = (a << 6 | a >>> 26) + b2 | 0;
        d += (b2 ^ (a | ~c)) + k[15] - 30611744 | 0;
        d = (d << 10 | d >>> 22) + a | 0;
        c += (a ^ (d | ~b2)) + k[6] - 1560198380 | 0;
        c = (c << 15 | c >>> 17) + d | 0;
        b2 += (d ^ (c | ~a)) + k[13] + 1309151649 | 0;
        b2 = (b2 << 21 | b2 >>> 11) + c | 0;
        a += (c ^ (b2 | ~d)) + k[4] - 145523070 | 0;
        a = (a << 6 | a >>> 26) + b2 | 0;
        d += (b2 ^ (a | ~c)) + k[11] - 1120210379 | 0;
        d = (d << 10 | d >>> 22) + a | 0;
        c += (a ^ (d | ~b2)) + k[2] + 718787259 | 0;
        c = (c << 15 | c >>> 17) + d | 0;
        b2 += (d ^ (c | ~a)) + k[9] - 343485551 | 0;
        b2 = (b2 << 21 | b2 >>> 11) + c | 0;
        x[0] = a + x[0] | 0;
        x[1] = b2 + x[1] | 0;
        x[2] = c + x[2] | 0;
        x[3] = d + x[3] | 0;
      }
      function md5blk(s) {
        var md5blks = [], i;
        for (i = 0; i < 64; i += 4) {
          md5blks[i >> 2] = s.charCodeAt(i) + (s.charCodeAt(i + 1) << 8) + (s.charCodeAt(i + 2) << 16) + (s.charCodeAt(i + 3) << 24);
        }
        return md5blks;
      }
      function md5blk_array(a) {
        var md5blks = [], i;
        for (i = 0; i < 64; i += 4) {
          md5blks[i >> 2] = a[i] + (a[i + 1] << 8) + (a[i + 2] << 16) + (a[i + 3] << 24);
        }
        return md5blks;
      }
      function md51(s) {
        var n = s.length, state = [1732584193, -271733879, -1732584194, 271733878], i, length, tail, tmp, lo, hi;
        for (i = 64; i <= n; i += 64) {
          md5cycle(state, md5blk(s.substring(i - 64, i)));
        }
        s = s.substring(i - 64);
        length = s.length;
        tail = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
        for (i = 0; i < length; i += 1) {
          tail[i >> 2] |= s.charCodeAt(i) << (i % 4 << 3);
        }
        tail[i >> 2] |= 128 << (i % 4 << 3);
        if (i > 55) {
          md5cycle(state, tail);
          for (i = 0; i < 16; i += 1) {
            tail[i] = 0;
          }
        }
        tmp = n * 8;
        tmp = tmp.toString(16).match(/(.*?)(.{0,8})$/);
        lo = parseInt(tmp[2], 16);
        hi = parseInt(tmp[1], 16) || 0;
        tail[14] = lo;
        tail[15] = hi;
        md5cycle(state, tail);
        return state;
      }
      function md51_array(a) {
        var n = a.length, state = [1732584193, -271733879, -1732584194, 271733878], i, length, tail, tmp, lo, hi;
        for (i = 64; i <= n; i += 64) {
          md5cycle(state, md5blk_array(a.subarray(i - 64, i)));
        }
        a = i - 64 < n ? a.subarray(i - 64) : new Uint8Array(0);
        length = a.length;
        tail = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
        for (i = 0; i < length; i += 1) {
          tail[i >> 2] |= a[i] << (i % 4 << 3);
        }
        tail[i >> 2] |= 128 << (i % 4 << 3);
        if (i > 55) {
          md5cycle(state, tail);
          for (i = 0; i < 16; i += 1) {
            tail[i] = 0;
          }
        }
        tmp = n * 8;
        tmp = tmp.toString(16).match(/(.*?)(.{0,8})$/);
        lo = parseInt(tmp[2], 16);
        hi = parseInt(tmp[1], 16) || 0;
        tail[14] = lo;
        tail[15] = hi;
        md5cycle(state, tail);
        return state;
      }
      function rhex(n) {
        var s = "", j;
        for (j = 0; j < 4; j += 1) {
          s += hex_chr[n >> j * 8 + 4 & 15] + hex_chr[n >> j * 8 & 15];
        }
        return s;
      }
      function hex(x) {
        var i;
        for (i = 0; i < x.length; i += 1) {
          x[i] = rhex(x[i]);
        }
        return x.join("");
      }
      if (hex(md51("hello")) !== "5d41402abc4b2a76b9719d911017c592") {
        add32 = function(x, y) {
          var lsw = (x & 65535) + (y & 65535), msw = (x >> 16) + (y >> 16) + (lsw >> 16);
          return msw << 16 | lsw & 65535;
        };
      }
      if (typeof ArrayBuffer !== "undefined" && !ArrayBuffer.prototype.slice) {
        (function() {
          function clamp(val, length) {
            val = val | 0 || 0;
            if (val < 0) {
              return Math.max(val + length, 0);
            }
            return Math.min(val, length);
          }
          ArrayBuffer.prototype.slice = function(from, to) {
            var length = this.byteLength, begin = clamp(from, length), end = length, num, target, targetArray, sourceArray;
            if (to !== undefined2) {
              end = clamp(to, length);
            }
            if (begin > end) {
              return new ArrayBuffer(0);
            }
            num = end - begin;
            target = new ArrayBuffer(num);
            targetArray = new Uint8Array(target);
            sourceArray = new Uint8Array(this, begin, num);
            targetArray.set(sourceArray);
            return target;
          };
        })();
      }
      function toUtf8(str) {
        if (/[\u0080-\uFFFF]/.test(str)) {
          str = unescape(encodeURIComponent(str));
        }
        return str;
      }
      function utf8Str2ArrayBuffer(str, returnUInt8Array) {
        var length = str.length, buff = new ArrayBuffer(length), arr = new Uint8Array(buff), i;
        for (i = 0; i < length; i += 1) {
          arr[i] = str.charCodeAt(i);
        }
        return returnUInt8Array ? arr : buff;
      }
      function arrayBuffer2Utf8Str(buff) {
        return String.fromCharCode.apply(null, new Uint8Array(buff));
      }
      function concatenateArrayBuffers(first, second, returnUInt8Array) {
        var result = new Uint8Array(first.byteLength + second.byteLength);
        result.set(new Uint8Array(first));
        result.set(new Uint8Array(second), first.byteLength);
        return returnUInt8Array ? result : result.buffer;
      }
      function hexToBinaryString(hex2) {
        var bytes = [], length = hex2.length, x;
        for (x = 0; x < length - 1; x += 2) {
          bytes.push(parseInt(hex2.substr(x, 2), 16));
        }
        return String.fromCharCode.apply(String, bytes);
      }
      function SparkMD52() {
        this.reset();
      }
      SparkMD52.prototype.append = function(str) {
        this.appendBinary(toUtf8(str));
        return this;
      };
      SparkMD52.prototype.appendBinary = function(contents) {
        this._buff += contents;
        this._length += contents.length;
        var length = this._buff.length, i;
        for (i = 64; i <= length; i += 64) {
          md5cycle(this._hash, md5blk(this._buff.substring(i - 64, i)));
        }
        this._buff = this._buff.substring(i - 64);
        return this;
      };
      SparkMD52.prototype.end = function(raw) {
        var buff = this._buff, length = buff.length, i, tail = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], ret;
        for (i = 0; i < length; i += 1) {
          tail[i >> 2] |= buff.charCodeAt(i) << (i % 4 << 3);
        }
        this._finish(tail, length);
        ret = hex(this._hash);
        if (raw) {
          ret = hexToBinaryString(ret);
        }
        this.reset();
        return ret;
      };
      SparkMD52.prototype.reset = function() {
        this._buff = "";
        this._length = 0;
        this._hash = [1732584193, -271733879, -1732584194, 271733878];
        return this;
      };
      SparkMD52.prototype.getState = function() {
        return {
          buff: this._buff,
          length: this._length,
          hash: this._hash.slice()
        };
      };
      SparkMD52.prototype.setState = function(state) {
        this._buff = state.buff;
        this._length = state.length;
        this._hash = state.hash;
        return this;
      };
      SparkMD52.prototype.destroy = function() {
        delete this._hash;
        delete this._buff;
        delete this._length;
      };
      SparkMD52.prototype._finish = function(tail, length) {
        var i = length, tmp, lo, hi;
        tail[i >> 2] |= 128 << (i % 4 << 3);
        if (i > 55) {
          md5cycle(this._hash, tail);
          for (i = 0; i < 16; i += 1) {
            tail[i] = 0;
          }
        }
        tmp = this._length * 8;
        tmp = tmp.toString(16).match(/(.*?)(.{0,8})$/);
        lo = parseInt(tmp[2], 16);
        hi = parseInt(tmp[1], 16) || 0;
        tail[14] = lo;
        tail[15] = hi;
        md5cycle(this._hash, tail);
      };
      SparkMD52.hash = function(str, raw) {
        return SparkMD52.hashBinary(toUtf8(str), raw);
      };
      SparkMD52.hashBinary = function(content, raw) {
        var hash = md51(content), ret = hex(hash);
        return raw ? hexToBinaryString(ret) : ret;
      };
      SparkMD52.ArrayBuffer = function() {
        this.reset();
      };
      SparkMD52.ArrayBuffer.prototype.append = function(arr) {
        var buff = concatenateArrayBuffers(this._buff.buffer, arr, true), length = buff.length, i;
        this._length += arr.byteLength;
        for (i = 64; i <= length; i += 64) {
          md5cycle(this._hash, md5blk_array(buff.subarray(i - 64, i)));
        }
        this._buff = i - 64 < length ? new Uint8Array(buff.buffer.slice(i - 64)) : new Uint8Array(0);
        return this;
      };
      SparkMD52.ArrayBuffer.prototype.end = function(raw) {
        var buff = this._buff, length = buff.length, tail = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], i, ret;
        for (i = 0; i < length; i += 1) {
          tail[i >> 2] |= buff[i] << (i % 4 << 3);
        }
        this._finish(tail, length);
        ret = hex(this._hash);
        if (raw) {
          ret = hexToBinaryString(ret);
        }
        this.reset();
        return ret;
      };
      SparkMD52.ArrayBuffer.prototype.reset = function() {
        this._buff = new Uint8Array(0);
        this._length = 0;
        this._hash = [1732584193, -271733879, -1732584194, 271733878];
        return this;
      };
      SparkMD52.ArrayBuffer.prototype.getState = function() {
        var state = SparkMD52.prototype.getState.call(this);
        state.buff = arrayBuffer2Utf8Str(state.buff);
        return state;
      };
      SparkMD52.ArrayBuffer.prototype.setState = function(state) {
        state.buff = utf8Str2ArrayBuffer(state.buff, true);
        return SparkMD52.prototype.setState.call(this, state);
      };
      SparkMD52.ArrayBuffer.prototype.destroy = SparkMD52.prototype.destroy;
      SparkMD52.ArrayBuffer.prototype._finish = SparkMD52.prototype._finish;
      SparkMD52.ArrayBuffer.hash = function(arr, raw) {
        var hash = md51_array(new Uint8Array(arr)), ret = hex(hash);
        return raw ? hexToBinaryString(ret) : ret;
      };
      return SparkMD52;
    });
  }
});

// node_modules/.pnpm/querystring@0.2.1/node_modules/querystring/decode.js
var require_decode = __commonJS({
  "node_modules/.pnpm/querystring@0.2.1/node_modules/querystring/decode.js"(exports, module) {
    "use strict";
    function hasOwnProperty(obj, prop) {
      return Object.prototype.hasOwnProperty.call(obj, prop);
    }
    module.exports = function(qs, sep, eq, options) {
      sep = sep || "&";
      eq = eq || "=";
      var obj = {};
      if (typeof qs !== "string" || qs.length === 0) {
        return obj;
      }
      var regexp = /\+/g;
      qs = qs.split(sep);
      var maxKeys = 1e3;
      if (options && typeof options.maxKeys === "number") {
        maxKeys = options.maxKeys;
      }
      var len = qs.length;
      if (maxKeys > 0 && len > maxKeys) {
        len = maxKeys;
      }
      for (var i = 0; i < len; ++i) {
        var x = qs[i].replace(regexp, "%20"), idx = x.indexOf(eq), kstr, vstr, k, v;
        if (idx >= 0) {
          kstr = x.substr(0, idx);
          vstr = x.substr(idx + 1);
        } else {
          kstr = x;
          vstr = "";
        }
        k = decodeURIComponent(kstr);
        v = decodeURIComponent(vstr);
        if (!hasOwnProperty(obj, k)) {
          obj[k] = v;
        } else if (Array.isArray(obj[k])) {
          obj[k].push(v);
        } else {
          obj[k] = [obj[k], v];
        }
      }
      return obj;
    };
  }
});

// node_modules/.pnpm/querystring@0.2.1/node_modules/querystring/encode.js
var require_encode = __commonJS({
  "node_modules/.pnpm/querystring@0.2.1/node_modules/querystring/encode.js"(exports, module) {
    "use strict";
    var stringifyPrimitive = function(v) {
      switch (typeof v) {
        case "string":
          return v;
        case "boolean":
          return v ? "true" : "false";
        case "number":
          return isFinite(v) ? v : "";
        default:
          return "";
      }
    };
    module.exports = function(obj, sep, eq, name) {
      sep = sep || "&";
      eq = eq || "=";
      if (obj === null) {
        obj = void 0;
      }
      if (typeof obj === "object") {
        return Object.keys(obj).map(function(k) {
          var ks = encodeURIComponent(stringifyPrimitive(k)) + eq;
          if (Array.isArray(obj[k])) {
            return obj[k].map(function(v) {
              return ks + encodeURIComponent(stringifyPrimitive(v));
            }).join(sep);
          } else {
            return ks + encodeURIComponent(stringifyPrimitive(obj[k]));
          }
        }).filter(Boolean).join(sep);
      }
      if (!name) return "";
      return encodeURIComponent(stringifyPrimitive(name)) + eq + encodeURIComponent(stringifyPrimitive(obj));
    };
  }
});

// node_modules/.pnpm/querystring@0.2.1/node_modules/querystring/index.js
var require_querystring = __commonJS({
  "node_modules/.pnpm/querystring@0.2.1/node_modules/querystring/index.js"(exports) {
    "use strict";
    exports.decode = exports.parse = require_decode();
    exports.encode = exports.stringify = require_encode();
  }
});

// node_modules/.pnpm/qiniu-js@3.4.1/node_modules/qiniu-js/esm/errors/index.js
var __extends = /* @__PURE__ */ function() {
  var extendStatics = function(d, b2) {
    extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d2, b3) {
      d2.__proto__ = b3;
    } || function(d2, b3) {
      for (var p in b3) if (b3.hasOwnProperty(p)) d2[p] = b3[p];
    };
    return extendStatics(d, b2);
  };
  return function(d, b2) {
    extendStatics(d, b2);
    function __() {
      this.constructor = d;
    }
    d.prototype = b2 === null ? Object.create(b2) : (__.prototype = b2.prototype, new __());
  };
}();
var QiniuErrorName;
(function(QiniuErrorName2) {
  QiniuErrorName2["InvalidFile"] = "InvalidFile";
  QiniuErrorName2["InvalidToken"] = "InvalidToken";
  QiniuErrorName2["InvalidMetadata"] = "InvalidMetadata";
  QiniuErrorName2["InvalidChunkSize"] = "InvalidChunkSize";
  QiniuErrorName2["InvalidCustomVars"] = "InvalidCustomVars";
  QiniuErrorName2["NotAvailableUploadHost"] = "NotAvailableUploadHost";
  QiniuErrorName2["ReadCacheFailed"] = "ReadCacheFailed";
  QiniuErrorName2["InvalidCacheData"] = "InvalidCacheData";
  QiniuErrorName2["WriteCacheFailed"] = "WriteCacheFailed";
  QiniuErrorName2["RemoveCacheFailed"] = "RemoveCacheFailed";
  QiniuErrorName2["GetCanvasContextFailed"] = "GetCanvasContextFailed";
  QiniuErrorName2["UnsupportedFileType"] = "UnsupportedFileType";
  QiniuErrorName2["FileReaderReadFailed"] = "FileReaderReadFailed";
  QiniuErrorName2["NotAvailableXMLHttpRequest"] = "NotAvailableXMLHttpRequest";
  QiniuErrorName2["InvalidProgressEventTarget"] = "InvalidProgressEventTarget";
  QiniuErrorName2["RequestError"] = "RequestError";
})(QiniuErrorName || (QiniuErrorName = {}));
var QiniuError = (
  /** @class */
  /* @__PURE__ */ function() {
    function QiniuError2(name, message) {
      this.name = name;
      this.message = message;
      this.stack = new Error().stack;
    }
    return QiniuError2;
  }()
);
var QiniuRequestError = (
  /** @class */
  function(_super) {
    __extends(QiniuRequestError2, _super);
    function QiniuRequestError2(code, reqId, message, data) {
      var _this = _super.call(this, QiniuErrorName.RequestError, message) || this;
      _this.code = code;
      _this.reqId = reqId;
      _this.isRequestError = true;
      _this.data = data;
      return _this;
    }
    return QiniuRequestError2;
  }(QiniuError)
);
var QiniuNetworkError = (
  /** @class */
  function(_super) {
    __extends(QiniuNetworkError2, _super);
    function QiniuNetworkError2(message, reqId) {
      if (reqId === void 0) {
        reqId = "";
      }
      return _super.call(this, 0, reqId, message) || this;
    }
    return QiniuNetworkError2;
  }(QiniuRequestError)
);

// node_modules/.pnpm/qiniu-js@3.4.1/node_modules/qiniu-js/esm/utils/pool.js
var Pool = (
  /** @class */
  function() {
    function Pool2(runTask, limit) {
      this.runTask = runTask;
      this.limit = limit;
      this.aborted = false;
      this.queue = [];
      this.processing = [];
    }
    Pool2.prototype.enqueue = function(task) {
      var _this = this;
      return new Promise(function(resolve, reject) {
        _this.queue.push({
          task,
          resolve,
          reject
        });
        _this.check();
      });
    };
    Pool2.prototype.run = function(item) {
      var _this = this;
      this.queue = this.queue.filter(function(v) {
        return v !== item;
      });
      this.processing.push(item);
      this.runTask(item.task).then(function() {
        _this.processing = _this.processing.filter(function(v) {
          return v !== item;
        });
        item.resolve();
        _this.check();
      }, function(err) {
        return item.reject(err);
      });
    };
    Pool2.prototype.check = function() {
      var _this = this;
      if (this.aborted)
        return;
      var processingNum = this.processing.length;
      var availableNum = this.limit - processingNum;
      this.queue.slice(0, availableNum).forEach(function(item) {
        _this.run(item);
      });
    };
    Pool2.prototype.abort = function() {
      this.queue = [];
      this.aborted = true;
    };
    return Pool2;
  }()
);

// node_modules/.pnpm/qiniu-js@3.4.1/node_modules/qiniu-js/esm/utils/observable.js
var __extends2 = /* @__PURE__ */ function() {
  var extendStatics = function(d, b2) {
    extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d2, b3) {
      d2.__proto__ = b3;
    } || function(d2, b3) {
      for (var p in b3) if (b3.hasOwnProperty(p)) d2[p] = b3[p];
    };
    return extendStatics(d, b2);
  };
  return function(d, b2) {
    extendStatics(d, b2);
    function __() {
      this.constructor = d;
    }
    d.prototype = b2 === null ? Object.create(b2) : (__.prototype = b2.prototype, new __());
  };
}();
var __assign = function() {
  __assign = Object.assign || function(t) {
    for (var s, i = 1, n = arguments.length; i < n; i++) {
      s = arguments[i];
      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
        t[p] = s[p];
    }
    return t;
  };
  return __assign.apply(this, arguments);
};
var Subscription = (
  /** @class */
  function() {
    function Subscription2() {
      this.closed = false;
    }
    Subscription2.prototype.unsubscribe = function() {
      if (this.closed) {
        return;
      }
      this.closed = true;
      if (this._unsubscribe) {
        this._unsubscribe();
      }
    };
    Subscription2.prototype.add = function(teardown) {
      this._unsubscribe = teardown;
    };
    return Subscription2;
  }()
);
var Subscriber = (
  /** @class */
  function(_super) {
    __extends2(Subscriber2, _super);
    function Subscriber2(observerOrNext, error, complete) {
      var _this = _super.call(this) || this;
      _this.isStopped = false;
      if (observerOrNext && typeof observerOrNext === "object") {
        _this.destination = observerOrNext;
      } else {
        _this.destination = __assign(__assign(__assign({}, observerOrNext && { next: observerOrNext }), error && { error }), complete && { complete });
      }
      return _this;
    }
    Subscriber2.prototype.unsubscribe = function() {
      if (this.closed) {
        return;
      }
      this.isStopped = true;
      _super.prototype.unsubscribe.call(this);
    };
    Subscriber2.prototype.next = function(value) {
      if (!this.isStopped && this.destination.next) {
        this.destination.next(value);
      }
    };
    Subscriber2.prototype.error = function(err) {
      if (!this.isStopped && this.destination.error) {
        this.isStopped = true;
        this.destination.error(err);
      }
    };
    Subscriber2.prototype.complete = function(result) {
      if (!this.isStopped && this.destination.complete) {
        this.isStopped = true;
        this.destination.complete(result);
      }
    };
    return Subscriber2;
  }(Subscription)
);
var Observable = (
  /** @class */
  function() {
    function Observable2(_subscribe) {
      this._subscribe = _subscribe;
    }
    Observable2.prototype.subscribe = function(observerOrNext, error, complete) {
      var sink = new Subscriber(observerOrNext, error, complete);
      sink.add(this._subscribe(sink));
      return sink;
    };
    return Observable2;
  }()
);

// node_modules/.pnpm/qiniu-js@3.4.1/node_modules/qiniu-js/esm/utils/base64.js
function utf8Encode(argString) {
  if (argString === null || typeof argString === "undefined") {
    return "";
  }
  var string = argString + "";
  var utftext = "", start, end, stringl = 0;
  start = end = 0;
  stringl = string.length;
  for (var n = 0; n < stringl; n++) {
    var c1 = string.charCodeAt(n);
    var enc = null;
    if (c1 < 128) {
      end++;
    } else if (c1 > 127 && c1 < 2048) {
      enc = String.fromCharCode(c1 >> 6 | 192, c1 & 63 | 128);
    } else if ((c1 & 63488 ^ 55296) > 0) {
      enc = String.fromCharCode(c1 >> 12 | 224, c1 >> 6 & 63 | 128, c1 & 63 | 128);
    } else {
      if ((c1 & 64512 ^ 55296) > 0) {
        throw new RangeError("Unmatched trail surrogate at " + n);
      }
      var c2 = string.charCodeAt(++n);
      if ((c2 & 64512 ^ 56320) > 0) {
        throw new RangeError("Unmatched lead surrogate at " + (n - 1));
      }
      c1 = ((c1 & 1023) << 10) + (c2 & 1023) + 65536;
      enc = String.fromCharCode(c1 >> 18 | 240, c1 >> 12 & 63 | 128, c1 >> 6 & 63 | 128, c1 & 63 | 128);
    }
    if (enc !== null) {
      if (end > start) {
        utftext += string.slice(start, end);
      }
      utftext += enc;
      start = end = n + 1;
    }
  }
  if (end > start) {
    utftext += string.slice(start, stringl);
  }
  return utftext;
}
function utf8Decode(strData) {
  var tmpArr = [];
  var i = 0;
  var c1 = 0;
  var seqlen = 0;
  strData += "";
  while (i < strData.length) {
    c1 = strData.charCodeAt(i) & 255;
    seqlen = 0;
    if (c1 <= 191) {
      c1 = c1 & 127;
      seqlen = 1;
    } else if (c1 <= 223) {
      c1 = c1 & 31;
      seqlen = 2;
    } else if (c1 <= 239) {
      c1 = c1 & 15;
      seqlen = 3;
    } else {
      c1 = c1 & 7;
      seqlen = 4;
    }
    for (var ai = 1; ai < seqlen; ++ai) {
      c1 = c1 << 6 | strData.charCodeAt(ai + i) & 63;
    }
    if (seqlen === 4) {
      c1 -= 65536;
      tmpArr.push(String.fromCharCode(55296 | c1 >> 10 & 1023));
      tmpArr.push(String.fromCharCode(56320 | c1 & 1023));
    } else {
      tmpArr.push(String.fromCharCode(c1));
    }
    i += seqlen;
  }
  return tmpArr.join("");
}
function base64Encode(data) {
  var b64 = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
  var o1, o2, o3, h1, h2, h3, h4, bits, i = 0, ac = 0, enc = "", tmp_arr = [];
  if (!data) {
    return data;
  }
  data = utf8Encode(data + "");
  do {
    o1 = data.charCodeAt(i++);
    o2 = data.charCodeAt(i++);
    o3 = data.charCodeAt(i++);
    bits = o1 << 16 | o2 << 8 | o3;
    h1 = bits >> 18 & 63;
    h2 = bits >> 12 & 63;
    h3 = bits >> 6 & 63;
    h4 = bits & 63;
    tmp_arr[ac++] = b64.charAt(h1) + b64.charAt(h2) + b64.charAt(h3) + b64.charAt(h4);
  } while (i < data.length);
  enc = tmp_arr.join("");
  switch (data.length % 3) {
    case 1:
      enc = enc.slice(0, -2) + "==";
      break;
    case 2:
      enc = enc.slice(0, -1) + "=";
      break;
  }
  return enc;
}
function base64Decode(data) {
  var b64 = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
  var o1, o2, o3, h1, h2, h3, h4, bits, i = 0, ac = 0, dec = "", tmp_arr = [];
  if (!data) {
    return data;
  }
  data += "";
  do {
    h1 = b64.indexOf(data.charAt(i++));
    h2 = b64.indexOf(data.charAt(i++));
    h3 = b64.indexOf(data.charAt(i++));
    h4 = b64.indexOf(data.charAt(i++));
    bits = h1 << 18 | h2 << 12 | h3 << 6 | h4;
    o1 = bits >> 16 & 255;
    o2 = bits >> 8 & 255;
    o3 = bits & 255;
    if (h3 === 64) {
      tmp_arr[ac++] = String.fromCharCode(o1);
    } else if (h4 === 64) {
      tmp_arr[ac++] = String.fromCharCode(o1, o2);
    } else {
      tmp_arr[ac++] = String.fromCharCode(o1, o2, o3);
    }
  } while (i < data.length);
  dec = tmp_arr.join("");
  return utf8Decode(dec);
}
function urlSafeBase64Encode(v) {
  v = base64Encode(v);
  return v.replace(/\//g, "_").replace(/\+/g, "-");
}
function urlSafeBase64Decode(v) {
  v = v.replace(/_/g, "/").replace(/-/g, "+");
  return base64Decode(v);
}

// node_modules/.pnpm/qiniu-js@3.4.1/node_modules/qiniu-js/esm/utils/helper.js
var import_spark_md5 = __toESM(require_spark_md5());
var __assign2 = function() {
  __assign2 = Object.assign || function(t) {
    for (var s, i = 1, n = arguments.length; i < n; i++) {
      s = arguments[i];
      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
        t[p] = s[p];
    }
    return t;
  };
  return __assign2.apply(this, arguments);
};
var __awaiter = function(thisArg, _arguments, P, generator) {
  function adopt(value) {
    return value instanceof P ? value : new P(function(resolve) {
      resolve(value);
    });
  }
  return new (P || (P = Promise))(function(resolve, reject) {
    function fulfilled(value) {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    }
    function rejected(value) {
      try {
        step(generator["throw"](value));
      } catch (e) {
        reject(e);
      }
    }
    function step(result) {
      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
    }
    step((generator = generator.apply(thisArg, _arguments || [])).next());
  });
};
var __generator = function(thisArg, body) {
  var _ = { label: 0, sent: function() {
    if (t[0] & 1) throw t[1];
    return t[1];
  }, trys: [], ops: [] }, f2, y, t, g;
  return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
    return this;
  }), g;
  function verb(n) {
    return function(v) {
      return step([n, v]);
    };
  }
  function step(op) {
    if (f2) throw new TypeError("Generator is already executing.");
    while (_) try {
      if (f2 = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
      if (y = 0, t) op = [op[0] & 2, t.value];
      switch (op[0]) {
        case 0:
        case 1:
          t = op;
          break;
        case 4:
          _.label++;
          return { value: op[1], done: false };
        case 5:
          _.label++;
          y = op[1];
          op = [0];
          continue;
        case 7:
          op = _.ops.pop();
          _.trys.pop();
          continue;
        default:
          if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
            _ = 0;
            continue;
          }
          if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
            _.label = op[1];
            break;
          }
          if (op[0] === 6 && _.label < t[1]) {
            _.label = t[1];
            t = op;
            break;
          }
          if (t && _.label < t[2]) {
            _.label = t[2];
            _.ops.push(op);
            break;
          }
          if (t[2]) _.ops.pop();
          _.trys.pop();
          continue;
      }
      op = body.call(thisArg, _);
    } catch (e) {
      op = [6, e];
      y = 0;
    } finally {
      f2 = t = 0;
    }
    if (op[0] & 5) throw op[1];
    return { value: op[0] ? op[1] : void 0, done: true };
  }
};
var MB = Math.pow(1024, 2);
function getChunks(file, blockSize) {
  var chunkByteSize = blockSize * MB;
  if (chunkByteSize > file.size) {
    chunkByteSize = file.size;
  } else {
    while (file.size > chunkByteSize * 1e4) {
      chunkByteSize *= 2;
    }
  }
  var chunks = [];
  var count = Math.ceil(file.size / chunkByteSize);
  for (var i = 0; i < count; i++) {
    var chunk = file.slice(chunkByteSize * i, i === count - 1 ? file.size : chunkByteSize * (i + 1));
    chunks.push(chunk);
  }
  return chunks;
}
function isMetaDataValid(params) {
  return Object.keys(params).every(function(key) {
    return key.indexOf("x-qn-meta-") === 0;
  });
}
function isCustomVarsValid(params) {
  return Object.keys(params).every(function(key) {
    return key.indexOf("x:") === 0;
  });
}
function sum(list) {
  return list.reduce(function(data, loaded) {
    return data + loaded;
  }, 0);
}
function setLocalFileInfo(localKey, info, logger) {
  try {
    localStorage.setItem(localKey, JSON.stringify(info));
  } catch (err) {
    logger.warn(new QiniuError(QiniuErrorName.WriteCacheFailed, "setLocalFileInfo failed: " + localKey));
  }
}
function createLocalKey(name, key, size) {
  var localKey = key == null ? "_" : "_key_" + key + "_";
  return "qiniu_js_sdk_upload_file_name_" + name + localKey + "size_" + size;
}
function removeLocalFileInfo(localKey, logger) {
  try {
    localStorage.removeItem(localKey);
  } catch (err) {
    logger.warn(new QiniuError(QiniuErrorName.RemoveCacheFailed, "removeLocalFileInfo failed. key: " + localKey));
  }
}
function getLocalFileInfo(localKey, logger) {
  var localInfoString = null;
  try {
    localInfoString = localStorage.getItem(localKey);
  } catch (_a2) {
    logger.warn(new QiniuError(QiniuErrorName.ReadCacheFailed, "getLocalFileInfo failed. key: " + localKey));
  }
  if (localInfoString == null) {
    return null;
  }
  var localInfo = null;
  try {
    localInfo = JSON.parse(localInfoString);
  } catch (_b) {
    removeLocalFileInfo(localKey, logger);
    logger.warn(new QiniuError(QiniuErrorName.InvalidCacheData, "getLocalFileInfo failed to parse. key: " + localKey));
  }
  return localInfo;
}
function getAuthHeaders(token) {
  var auth = "UpToken " + token;
  return { Authorization: auth };
}
function getHeadersForChunkUpload(token) {
  var header = getAuthHeaders(token);
  return __assign2({ "content-type": "application/octet-stream" }, header);
}
function getHeadersForMkFile(token) {
  var header = getAuthHeaders(token);
  return __assign2({ "content-type": "application/json" }, header);
}
function createXHR() {
  if (window.XMLHttpRequest) {
    return new XMLHttpRequest();
  }
  if (window.ActiveXObject) {
    return new window.ActiveXObject("Microsoft.XMLHTTP");
  }
  throw new QiniuError(QiniuErrorName.NotAvailableXMLHttpRequest, "the current environment does not support.");
}
function computeMd5(data) {
  return __awaiter(this, void 0, void 0, function() {
    var buffer, spark;
    return __generator(this, function(_a2) {
      switch (_a2.label) {
        case 0:
          return [4, readAsArrayBuffer(data)];
        case 1:
          buffer = _a2.sent();
          spark = new import_spark_md5.default.ArrayBuffer();
          spark.append(buffer);
          return [2, spark.end()];
      }
    });
  });
}
function readAsArrayBuffer(data) {
  return new Promise(function(resolve, reject) {
    var reader = new FileReader();
    reader.onload = function(evt) {
      if (evt.target) {
        var body = evt.target.result;
        resolve(body);
      } else {
        reject(new QiniuError(QiniuErrorName.InvalidProgressEventTarget, "progress event target is undefined"));
      }
    };
    reader.onerror = function() {
      reject(new QiniuError(QiniuErrorName.FileReaderReadFailed, "fileReader read failed"));
    };
    reader.readAsArrayBuffer(data);
  });
}
function request(url, options) {
  return new Promise(function(resolve, reject) {
    var xhr = createXHR();
    xhr.open(options.method, url);
    if (options.onCreate) {
      options.onCreate(xhr);
    }
    if (options.headers) {
      var headers_1 = options.headers;
      Object.keys(headers_1).forEach(function(k) {
        xhr.setRequestHeader(k, headers_1[k]);
      });
    }
    xhr.upload.addEventListener("progress", function(evt) {
      if (evt.lengthComputable && options.onProgress) {
        options.onProgress({
          loaded: evt.loaded,
          total: evt.total
        });
      }
    });
    xhr.onreadystatechange = function() {
      var responseText = xhr.responseText;
      if (xhr.readyState !== 4) {
        return;
      }
      var reqId = xhr.getResponseHeader("x-reqId") || "";
      if (xhr.status === 0) {
        reject(new QiniuNetworkError("network error.", reqId));
        return;
      }
      if (xhr.status !== 200) {
        var message = "xhr request failed, code: " + xhr.status;
        if (responseText) {
          message += " response: " + responseText;
        }
        var data = void 0;
        try {
          data = JSON.parse(responseText);
        } catch (_a2) {
        }
        reject(new QiniuRequestError(xhr.status, reqId, message, data));
        return;
      }
      try {
        resolve({
          data: JSON.parse(responseText),
          reqId
        });
      } catch (err) {
        reject(err);
      }
    };
    xhr.send(options.body);
  });
}
function getPortFromUrl(url) {
  if (url && url.match) {
    var groups = url.match(/(^https?)/);
    if (!groups) {
      return "";
    }
    var type = groups[1];
    groups = url.match(/^https?:\/\/([^:^/]*):(\d*)/);
    if (groups) {
      return groups[2];
    }
    if (type === "http") {
      return "80";
    }
    return "443";
  }
  return "";
}
function getDomainFromUrl(url) {
  if (url && url.match) {
    var groups = url.match(/^https?:\/\/([^:^/]*)/);
    return groups ? groups[1] : "";
  }
  return "";
}
function getPutPolicy(token) {
  if (!token)
    throw new QiniuError(QiniuErrorName.InvalidToken, "invalid token.");
  var segments = token.split(":");
  if (segments.length === 1)
    throw new QiniuError(QiniuErrorName.InvalidToken, "invalid token segments.");
  var assessKey = segments.length > 3 ? segments[1] : segments[0];
  if (!assessKey)
    throw new QiniuError(QiniuErrorName.InvalidToken, "missing assess key field.");
  var putPolicy = null;
  try {
    putPolicy = JSON.parse(urlSafeBase64Decode(segments[segments.length - 1]));
  } catch (error) {
    throw new QiniuError(QiniuErrorName.InvalidToken, "token parse failed.");
  }
  if (putPolicy == null) {
    throw new QiniuError(QiniuErrorName.InvalidToken, "putPolicy is null.");
  }
  if (putPolicy.scope == null) {
    throw new QiniuError(QiniuErrorName.InvalidToken, "scope field is null.");
  }
  var bucketName = putPolicy.scope.split(":")[0];
  if (!bucketName) {
    throw new QiniuError(QiniuErrorName.InvalidToken, "resolve bucketName failed.");
  }
  return { assessKey, bucketName, scope: putPolicy.scope };
}
function createObjectURL(file) {
  var URL = window.URL || window.webkitURL || window.mozURL;
  return URL.createObjectURL(file);
}

// node_modules/.pnpm/qiniu-js@3.4.1/node_modules/qiniu-js/esm/config/region.js
var _a;
var region = {
  z0: "z0",
  z1: "z1",
  z2: "z2",
  na0: "na0",
  as0: "as0",
  cnEast2: "cn-east-2"
};
var regionUphostMap = (_a = {}, _a[region.z0] = {
  srcUphost: ["up.qiniup.com"],
  cdnUphost: ["upload.qiniup.com"]
}, _a[region.z1] = {
  srcUphost: ["up-z1.qiniup.com"],
  cdnUphost: ["upload-z1.qiniup.com"]
}, _a[region.z2] = {
  srcUphost: ["up-z2.qiniup.com"],
  cdnUphost: ["upload-z2.qiniup.com"]
}, _a[region.na0] = {
  srcUphost: ["up-na0.qiniup.com"],
  cdnUphost: ["upload-na0.qiniup.com"]
}, _a[region.as0] = {
  srcUphost: ["up-as0.qiniup.com"],
  cdnUphost: ["upload-as0.qiniup.com"]
}, _a[region.cnEast2] = {
  srcUphost: ["up-cn-east-2.qiniup.com"],
  cdnUphost: ["upload-cn-east-2.qiniup.com"]
}, _a);

// node_modules/.pnpm/qiniu-js@3.4.1/node_modules/qiniu-js/esm/api/index.js
var import_querystring = __toESM(require_querystring());
var __assign3 = function() {
  __assign3 = Object.assign || function(t) {
    for (var s, i = 1, n = arguments.length; i < n; i++) {
      s = arguments[i];
      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
        t[p] = s[p];
    }
    return t;
  };
  return __assign3.apply(this, arguments);
};
var __awaiter2 = function(thisArg, _arguments, P, generator) {
  function adopt(value) {
    return value instanceof P ? value : new P(function(resolve) {
      resolve(value);
    });
  }
  return new (P || (P = Promise))(function(resolve, reject) {
    function fulfilled(value) {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    }
    function rejected(value) {
      try {
        step(generator["throw"](value));
      } catch (e) {
        reject(e);
      }
    }
    function step(result) {
      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
    }
    step((generator = generator.apply(thisArg, _arguments || [])).next());
  });
};
var __generator2 = function(thisArg, body) {
  var _ = { label: 0, sent: function() {
    if (t[0] & 1) throw t[1];
    return t[1];
  }, trys: [], ops: [] }, f2, y, t, g;
  return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
    return this;
  }), g;
  function verb(n) {
    return function(v) {
      return step([n, v]);
    };
  }
  function step(op) {
    if (f2) throw new TypeError("Generator is already executing.");
    while (_) try {
      if (f2 = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
      if (y = 0, t) op = [op[0] & 2, t.value];
      switch (op[0]) {
        case 0:
        case 1:
          t = op;
          break;
        case 4:
          _.label++;
          return { value: op[1], done: false };
        case 5:
          _.label++;
          y = op[1];
          op = [0];
          continue;
        case 7:
          op = _.ops.pop();
          _.trys.pop();
          continue;
        default:
          if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
            _ = 0;
            continue;
          }
          if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
            _.label = op[1];
            break;
          }
          if (op[0] === 6 && _.label < t[1]) {
            _.label = t[1];
            t = op;
            break;
          }
          if (t && _.label < t[2]) {
            _.label = t[2];
            _.ops.push(op);
            break;
          }
          if (t[2]) _.ops.pop();
          _.trys.pop();
          continue;
      }
      op = body.call(thisArg, _);
    } catch (e) {
      op = [6, e];
      y = 0;
    } finally {
      f2 = t = 0;
    }
    if (op[0] & 5) throw op[1];
    return { value: op[0] ? op[1] : void 0, done: true };
  }
};
function getUpHosts(accessKey, bucketName, protocol) {
  return __awaiter2(this, void 0, void 0, function() {
    var params, url;
    return __generator2(this, function(_a2) {
      params = (0, import_querystring.stringify)({ ak: accessKey, bucket: bucketName });
      url = protocol + "://api.qiniu.com/v2/query?" + params;
      return [2, request(url, { method: "GET" })];
    });
  });
}
function getBaseUrl(bucket, key, uploadInfo) {
  var url = uploadInfo.url, id = uploadInfo.id;
  return url + "/buckets/" + bucket + "/objects/" + (key != null ? urlSafeBase64Encode(key) : "~") + "/uploads/" + id;
}
function initUploadParts(token, bucket, key, uploadUrl) {
  var url = uploadUrl + "/buckets/" + bucket + "/objects/" + (key != null ? urlSafeBase64Encode(key) : "~") + "/uploads";
  return request(url, {
    method: "POST",
    headers: getAuthHeaders(token)
  });
}
function uploadChunk(token, key, index, uploadInfo, options) {
  var bucket = getPutPolicy(token).bucketName;
  var url = getBaseUrl(bucket, key, uploadInfo) + ("/" + index);
  var headers = getHeadersForChunkUpload(token);
  if (options.md5)
    headers["Content-MD5"] = options.md5;
  return request(url, __assign3(__assign3({}, options), { method: "PUT", headers }));
}
function uploadComplete(token, key, uploadInfo, options) {
  var bucket = getPutPolicy(token).bucketName;
  var url = getBaseUrl(bucket, key, uploadInfo);
  return request(url, __assign3(__assign3({}, options), { method: "POST", headers: getHeadersForMkFile(token) }));
}
function direct(url, data, options) {
  return request(url, __assign3({ method: "POST", body: data }, options));
}

// node_modules/.pnpm/qiniu-js@3.4.1/node_modules/qiniu-js/esm/upload/base.js
var __assign4 = function() {
  __assign4 = Object.assign || function(t) {
    for (var s, i = 1, n = arguments.length; i < n; i++) {
      s = arguments[i];
      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
        t[p] = s[p];
    }
    return t;
  };
  return __assign4.apply(this, arguments);
};
var __awaiter3 = function(thisArg, _arguments, P, generator) {
  function adopt(value) {
    return value instanceof P ? value : new P(function(resolve) {
      resolve(value);
    });
  }
  return new (P || (P = Promise))(function(resolve, reject) {
    function fulfilled(value) {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    }
    function rejected(value) {
      try {
        step(generator["throw"](value));
      } catch (e) {
        reject(e);
      }
    }
    function step(result) {
      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
    }
    step((generator = generator.apply(thisArg, _arguments || [])).next());
  });
};
var __generator3 = function(thisArg, body) {
  var _ = { label: 0, sent: function() {
    if (t[0] & 1) throw t[1];
    return t[1];
  }, trys: [], ops: [] }, f2, y, t, g;
  return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
    return this;
  }), g;
  function verb(n) {
    return function(v) {
      return step([n, v]);
    };
  }
  function step(op) {
    if (f2) throw new TypeError("Generator is already executing.");
    while (_) try {
      if (f2 = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
      if (y = 0, t) op = [op[0] & 2, t.value];
      switch (op[0]) {
        case 0:
        case 1:
          t = op;
          break;
        case 4:
          _.label++;
          return { value: op[1], done: false };
        case 5:
          _.label++;
          y = op[1];
          op = [0];
          continue;
        case 7:
          op = _.ops.pop();
          _.trys.pop();
          continue;
        default:
          if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
            _ = 0;
            continue;
          }
          if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
            _.label = op[1];
            break;
          }
          if (op[0] === 6 && _.label < t[1]) {
            _.label = t[1];
            t = op;
            break;
          }
          if (t && _.label < t[2]) {
            _.label = t[2];
            _.ops.push(op);
            break;
          }
          if (t[2]) _.ops.pop();
          _.trys.pop();
          continue;
      }
      op = body.call(thisArg, _);
    } catch (e) {
      op = [6, e];
      y = 0;
    } finally {
      f2 = t = 0;
    }
    if (op[0] & 5) throw op[1];
    return { value: op[0] ? op[1] : void 0, done: true };
  }
};
var __read = function(o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o), r, ar = [], e;
  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
  } catch (error) {
    e = { error };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }
  return ar;
};
var __spread = function() {
  for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));
  return ar;
};
var DEFAULT_CHUNK_SIZE = 4;
var FREEZE_CODE_LIST = [0, 502, 503, 504, 599];
var RETRY_CODE_LIST = __spread(FREEZE_CODE_LIST, [612]);
var GB = Math.pow(1024, 3);
var Base = (
  /** @class */
  function() {
    function Base2(options, handlers, hostPool, logger) {
      this.hostPool = hostPool;
      this.logger = logger;
      this.aborted = false;
      this.retryCount = 0;
      this.xhrList = [];
      this.config = options.config;
      logger.info("config inited.", this.config);
      this.putExtra = __assign4({ fname: "" }, options.putExtra);
      logger.info("putExtra inited.", this.putExtra);
      this.key = options.key;
      this.file = options.file;
      this.token = options.token;
      this.onData = handlers.onData;
      this.onError = handlers.onError;
      this.onComplete = handlers.onComplete;
      try {
        var putPolicy = getPutPolicy(this.token);
        this.bucketName = putPolicy.bucketName;
        this.assessKey = putPolicy.assessKey;
      } catch (error) {
        logger.error("get putPolicy from token failed.", error);
        this.onError(error);
      }
    }
    Base2.prototype.checkAndUpdateUploadHost = function() {
      return __awaiter3(this, void 0, void 0, function() {
        var newHost;
        return __generator3(this, function(_a2) {
          switch (_a2.label) {
            case 0:
              this.logger.info("get available upload host.");
              return [4, this.hostPool.getUp(this.assessKey, this.bucketName, this.config.upprotocol)];
            case 1:
              newHost = _a2.sent();
              if (newHost == null) {
                throw new QiniuError(QiniuErrorName.NotAvailableUploadHost, "no available upload host.");
              }
              if (this.uploadHost != null && this.uploadHost.host !== newHost.host) {
                this.logger.warn("host switches from " + this.uploadHost.host + " to " + newHost.host + ".");
              } else {
                this.logger.info("use host " + newHost.host + ".");
              }
              this.uploadHost = newHost;
              return [
                2
                /*return*/
              ];
          }
        });
      });
    };
    Base2.prototype.checkAndUnfreezeHost = function() {
      this.logger.info("check unfreeze host.");
      if (this.uploadHost != null && this.uploadHost.isFrozen()) {
        this.logger.warn(this.uploadHost.host + " will be unfrozen.");
        this.uploadHost.unfreeze();
      }
    };
    Base2.prototype.checkAndFreezeHost = function(error) {
      this.logger.info("check freeze host.");
      if (error instanceof QiniuRequestError && this.uploadHost != null) {
        if (FREEZE_CODE_LIST.includes(error.code)) {
          this.logger.warn(this.uploadHost.host + " will be temporarily frozen.");
          this.uploadHost.freeze();
        }
      }
    };
    Base2.prototype.handleError = function(error) {
      this.logger.error(error.message);
      this.onError(error);
    };
    Base2.prototype.putFile = function() {
      return __awaiter3(this, void 0, void 0, function() {
        var result, err_1, notReachRetryCount, needRetry;
        return __generator3(this, function(_a2) {
          switch (_a2.label) {
            case 0:
              this.aborted = false;
              if (!this.putExtra.fname) {
                this.logger.info("use file.name as fname.");
                this.putExtra.fname = this.file.name;
              }
              if (this.file.size > 1e4 * GB) {
                this.handleError(new QiniuError(QiniuErrorName.InvalidFile, "file size exceed maximum value 10000G"));
                return [
                  2
                  /*return*/
                ];
              }
              if (this.putExtra.customVars) {
                if (!isCustomVarsValid(this.putExtra.customVars)) {
                  this.handleError(new QiniuError(
                    QiniuErrorName.InvalidCustomVars,
                    // FIXME: width => with
                    "customVars key should start width x:"
                  ));
                  return [
                    2
                    /*return*/
                  ];
                }
              }
              if (this.putExtra.metadata) {
                if (!isMetaDataValid(this.putExtra.metadata)) {
                  this.handleError(new QiniuError(QiniuErrorName.InvalidMetadata, "metadata key should start with x-qn-meta-"));
                  return [
                    2
                    /*return*/
                  ];
                }
              }
              _a2.label = 1;
            case 1:
              _a2.trys.push([1, 4, , 5]);
              this.uploadAt = (/* @__PURE__ */ new Date()).getTime();
              return [4, this.checkAndUpdateUploadHost()];
            case 2:
              _a2.sent();
              return [4, this.run()];
            case 3:
              result = _a2.sent();
              this.onComplete(result.data);
              this.checkAndUnfreezeHost();
              this.sendLog(result.reqId, 200);
              return [
                2
                /*return*/
              ];
            case 4:
              err_1 = _a2.sent();
              if (this.aborted) {
                this.logger.warn("upload is aborted.");
                this.sendLog("", -2);
                return [
                  2
                  /*return*/
                ];
              }
              this.clear();
              this.logger.error(err_1);
              if (err_1 instanceof QiniuRequestError) {
                this.sendLog(err_1.reqId, err_1.code);
                this.checkAndFreezeHost(err_1);
                notReachRetryCount = ++this.retryCount <= this.config.retryCount;
                needRetry = RETRY_CODE_LIST.includes(err_1.code);
                if (needRetry && notReachRetryCount) {
                  this.logger.warn("error auto retry: " + this.retryCount + "/" + this.config.retryCount + ".");
                  this.putFile();
                  return [
                    2
                    /*return*/
                  ];
                }
              }
              this.onError(err_1);
              return [3, 5];
            case 5:
              return [
                2
                /*return*/
              ];
          }
        });
      });
    };
    Base2.prototype.clear = function() {
      this.xhrList.forEach(function(xhr) {
        xhr.onreadystatechange = null;
        xhr.abort();
      });
      this.xhrList = [];
      this.logger.info("cleanup uploading xhr.");
    };
    Base2.prototype.stop = function() {
      this.logger.info("aborted.");
      this.clear();
      this.aborted = true;
    };
    Base2.prototype.addXhr = function(xhr) {
      this.xhrList.push(xhr);
    };
    Base2.prototype.sendLog = function(reqId, code) {
      var _a2, _b;
      this.logger.report({
        code,
        reqId,
        remoteIp: "",
        upType: "jssdk-h5",
        size: this.file.size,
        time: Math.floor(this.uploadAt / 1e3),
        port: getPortFromUrl((_a2 = this.uploadHost) === null || _a2 === void 0 ? void 0 : _a2.getUrl()),
        host: getDomainFromUrl((_b = this.uploadHost) === null || _b === void 0 ? void 0 : _b.getUrl()),
        bytesSent: this.progress ? this.progress.total.loaded : 0,
        duration: Math.floor(((/* @__PURE__ */ new Date()).getTime() - this.uploadAt) / 1e3)
      });
    };
    Base2.prototype.getProgressInfoItem = function(loaded, size, fromCache) {
      return __assign4({
        size,
        loaded,
        percent: loaded / size * 100
      }, fromCache == null ? {} : { fromCache });
    };
    return Base2;
  }()
);
var base_default = Base;

// node_modules/.pnpm/qiniu-js@3.4.1/node_modules/qiniu-js/esm/upload/resume.js
var __extends3 = /* @__PURE__ */ function() {
  var extendStatics = function(d, b2) {
    extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d2, b3) {
      d2.__proto__ = b3;
    } || function(d2, b3) {
      for (var p in b3) if (b3.hasOwnProperty(p)) d2[p] = b3[p];
    };
    return extendStatics(d, b2);
  };
  return function(d, b2) {
    extendStatics(d, b2);
    function __() {
      this.constructor = d;
    }
    d.prototype = b2 === null ? Object.create(b2) : (__.prototype = b2.prototype, new __());
  };
}();
var __assign5 = function() {
  __assign5 = Object.assign || function(t) {
    for (var s, i = 1, n = arguments.length; i < n; i++) {
      s = arguments[i];
      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
        t[p] = s[p];
    }
    return t;
  };
  return __assign5.apply(this, arguments);
};
var __awaiter4 = function(thisArg, _arguments, P, generator) {
  function adopt(value) {
    return value instanceof P ? value : new P(function(resolve) {
      resolve(value);
    });
  }
  return new (P || (P = Promise))(function(resolve, reject) {
    function fulfilled(value) {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    }
    function rejected(value) {
      try {
        step(generator["throw"](value));
      } catch (e) {
        reject(e);
      }
    }
    function step(result) {
      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
    }
    step((generator = generator.apply(thisArg, _arguments || [])).next());
  });
};
var __generator4 = function(thisArg, body) {
  var _ = { label: 0, sent: function() {
    if (t[0] & 1) throw t[1];
    return t[1];
  }, trys: [], ops: [] }, f2, y, t, g;
  return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
    return this;
  }), g;
  function verb(n) {
    return function(v) {
      return step([n, v]);
    };
  }
  function step(op) {
    if (f2) throw new TypeError("Generator is already executing.");
    while (_) try {
      if (f2 = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
      if (y = 0, t) op = [op[0] & 2, t.value];
      switch (op[0]) {
        case 0:
        case 1:
          t = op;
          break;
        case 4:
          _.label++;
          return { value: op[1], done: false };
        case 5:
          _.label++;
          y = op[1];
          op = [0];
          continue;
        case 7:
          op = _.ops.pop();
          _.trys.pop();
          continue;
        default:
          if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
            _ = 0;
            continue;
          }
          if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
            _.label = op[1];
            break;
          }
          if (op[0] === 6 && _.label < t[1]) {
            _.label = t[1];
            t = op;
            break;
          }
          if (t && _.label < t[2]) {
            _.label = t[2];
            _.ops.push(op);
            break;
          }
          if (t[2]) _.ops.pop();
          _.trys.pop();
          continue;
      }
      op = body.call(thisArg, _);
    } catch (e) {
      op = [6, e];
      y = 0;
    } finally {
      f2 = t = 0;
    }
    if (op[0] & 5) throw op[1];
    return { value: op[0] ? op[1] : void 0, done: true };
  }
};
function isPositiveInteger(n) {
  var re = /^[1-9]\d*$/;
  return re.test(String(n));
}
var Resume = (
  /** @class */
  function(_super) {
    __extends3(Resume2, _super);
    function Resume2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    Resume2.prototype.run = function() {
      return __awaiter4(this, void 0, void 0, function() {
        var pool, mkFileResponse, localKey, uploadChunks, error_1;
        var _this = this;
        return __generator4(this, function(_a2) {
          switch (_a2.label) {
            case 0:
              this.logger.info("start run Resume.");
              if (!this.config.chunkSize || !isPositiveInteger(this.config.chunkSize)) {
                throw new QiniuError(QiniuErrorName.InvalidChunkSize, "chunkSize must be a positive integer");
              }
              if (this.config.chunkSize > 1024) {
                throw new QiniuError(QiniuErrorName.InvalidChunkSize, "chunkSize maximum value is 1024");
              }
              return [4, this.initBeforeUploadChunks()];
            case 1:
              _a2.sent();
              pool = new Pool(function(chunkInfo) {
                return __awaiter4(_this, void 0, void 0, function() {
                  return __generator4(this, function(_a3) {
                    switch (_a3.label) {
                      case 0:
                        if (this.aborted) {
                          pool.abort();
                          throw new Error("pool is aborted");
                        }
                        return [4, this.uploadChunk(chunkInfo)];
                      case 1:
                        _a3.sent();
                        return [
                          2
                          /*return*/
                        ];
                    }
                  });
                });
              }, this.config.concurrentRequestLimit);
              mkFileResponse = null;
              localKey = this.getLocalKey();
              uploadChunks = this.chunks.map(function(chunk, index) {
                return pool.enqueue({ chunk, index });
              });
              _a2.label = 2;
            case 2:
              _a2.trys.push([2, 5, , 6]);
              return [4, Promise.all(uploadChunks)];
            case 3:
              _a2.sent();
              return [4, this.mkFileReq()];
            case 4:
              mkFileResponse = _a2.sent();
              return [3, 6];
            case 5:
              error_1 = _a2.sent();
              if (error_1 instanceof QiniuRequestError && (error_1.code === 612 || error_1.code === 400)) {
                removeLocalFileInfo(localKey, this.logger);
              }
              throw error_1;
            case 6:
              removeLocalFileInfo(localKey, this.logger);
              return [2, mkFileResponse];
          }
        });
      });
    };
    Resume2.prototype.uploadChunk = function(chunkInfo) {
      return __awaiter4(this, void 0, void 0, function() {
        var index, chunk, cachedInfo, shouldCheckMD5, reuseSaved, md5, onProgress, requestOptions, response;
        var _this = this;
        return __generator4(this, function(_a2) {
          switch (_a2.label) {
            case 0:
              index = chunkInfo.index, chunk = chunkInfo.chunk;
              cachedInfo = this.cachedUploadedList[index];
              this.logger.info("upload part " + index + ", cache:", cachedInfo);
              shouldCheckMD5 = this.config.checkByMD5;
              reuseSaved = function() {
                _this.usedCacheList[index] = true;
                _this.updateChunkProgress(chunk.size, index);
                _this.uploadedList[index] = cachedInfo;
                _this.updateLocalCache();
              };
              if (cachedInfo && !shouldCheckMD5) {
                reuseSaved();
                return [
                  2
                  /*return*/
                ];
              }
              return [4, computeMd5(chunk)];
            case 1:
              md5 = _a2.sent();
              this.logger.info("computed part md5.", md5);
              if (cachedInfo && md5 === cachedInfo.md5) {
                reuseSaved();
                return [
                  2
                  /*return*/
                ];
              }
              this.usedCacheList[index] = false;
              onProgress = function(data) {
                _this.updateChunkProgress(data.loaded, index);
              };
              requestOptions = {
                body: chunk,
                md5: this.config.checkByServer ? md5 : void 0,
                onProgress,
                onCreate: function(xhr) {
                  return _this.addXhr(xhr);
                }
              };
              this.logger.info("part " + index + " start uploading.");
              return [4, uploadChunk(this.token, this.key, chunkInfo.index + 1, this.getUploadInfo(), requestOptions)];
            case 2:
              response = _a2.sent();
              this.logger.info("part " + index + " upload completed.");
              onProgress({
                loaded: chunk.size,
                total: chunk.size
              });
              this.uploadedList[index] = {
                etag: response.data.etag,
                md5: response.data.md5,
                size: chunk.size
              };
              this.updateLocalCache();
              return [
                2
                /*return*/
              ];
          }
        });
      });
    };
    Resume2.prototype.mkFileReq = function() {
      return __awaiter4(this, void 0, void 0, function() {
        var data, result;
        var _this = this;
        return __generator4(this, function(_a2) {
          switch (_a2.label) {
            case 0:
              data = __assign5(__assign5(__assign5({ parts: this.uploadedList.map(function(value, index) {
                return {
                  etag: value.etag,
                  // 接口要求 index 需要从 1 开始，所以需要整体 + 1
                  partNumber: index + 1
                };
              }), fname: this.putExtra.fname }, this.putExtra.mimeType && { mimeType: this.putExtra.mimeType }), this.putExtra.customVars && { customVars: this.putExtra.customVars }), this.putExtra.metadata && { metadata: this.putExtra.metadata });
              this.logger.info("parts upload completed, make file.", data);
              return [4, uploadComplete(this.token, this.key, this.getUploadInfo(), {
                onCreate: function(xhr) {
                  return _this.addXhr(xhr);
                },
                body: JSON.stringify(data)
              })];
            case 1:
              result = _a2.sent();
              this.logger.info("finish Resume Progress.");
              this.updateMkFileProgress(1);
              return [2, result];
          }
        });
      });
    };
    Resume2.prototype.initBeforeUploadChunks = function() {
      return __awaiter4(this, void 0, void 0, function() {
        var cachedInfo, res, infoMessage;
        return __generator4(this, function(_a2) {
          switch (_a2.label) {
            case 0:
              this.uploadedList = [];
              this.usedCacheList = [];
              cachedInfo = getLocalFileInfo(this.getLocalKey(), this.logger);
              if (!!cachedInfo) return [3, 2];
              this.logger.info("init upload parts from api.");
              return [4, initUploadParts(this.token, this.bucketName, this.key, this.uploadHost.getUrl())];
            case 1:
              res = _a2.sent();
              this.logger.info("initd upload parts of id: " + res.data.uploadId + ".");
              this.uploadId = res.data.uploadId;
              this.cachedUploadedList = [];
              return [3, 3];
            case 2:
              infoMessage = [
                "resume upload parts from local cache,",
                "total " + cachedInfo.data.length + " part,",
                "id is " + cachedInfo.id + "."
              ];
              this.logger.info(infoMessage.join(" "));
              this.cachedUploadedList = cachedInfo.data;
              this.uploadId = cachedInfo.id;
              _a2.label = 3;
            case 3:
              this.chunks = getChunks(this.file, this.config.chunkSize);
              this.loaded = {
                mkFileProgress: 0,
                chunks: this.chunks.map(function(_) {
                  return 0;
                })
              };
              this.notifyResumeProgress();
              return [
                2
                /*return*/
              ];
          }
        });
      });
    };
    Resume2.prototype.getUploadInfo = function() {
      return {
        id: this.uploadId,
        url: this.uploadHost.getUrl()
      };
    };
    Resume2.prototype.getLocalKey = function() {
      return createLocalKey(this.file.name, this.key, this.file.size);
    };
    Resume2.prototype.updateLocalCache = function() {
      setLocalFileInfo(this.getLocalKey(), {
        id: this.uploadId,
        data: this.uploadedList
      }, this.logger);
    };
    Resume2.prototype.updateChunkProgress = function(loaded, index) {
      this.loaded.chunks[index] = loaded;
      this.notifyResumeProgress();
    };
    Resume2.prototype.updateMkFileProgress = function(progress) {
      this.loaded.mkFileProgress = progress;
      this.notifyResumeProgress();
    };
    Resume2.prototype.notifyResumeProgress = function() {
      var _this = this;
      this.progress = {
        total: this.getProgressInfoItem(
          sum(this.loaded.chunks) + this.loaded.mkFileProgress,
          // FIXME: 不准确的 fileSize
          this.file.size + 1
          // 防止在 complete 未调用的时候进度显示 100%
        ),
        chunks: this.chunks.map(function(chunk, index) {
          var fromCache = _this.usedCacheList[index];
          return _this.getProgressInfoItem(_this.loaded.chunks[index], chunk.size, fromCache);
        }),
        uploadInfo: {
          id: this.uploadId,
          url: this.uploadHost.getUrl()
        }
      };
      this.onData(this.progress);
    };
    return Resume2;
  }(base_default)
);
var resume_default = Resume;

// node_modules/.pnpm/qiniu-js@3.4.1/node_modules/qiniu-js/esm/utils/crc32.js
var __awaiter5 = function(thisArg, _arguments, P, generator) {
  function adopt(value) {
    return value instanceof P ? value : new P(function(resolve) {
      resolve(value);
    });
  }
  return new (P || (P = Promise))(function(resolve, reject) {
    function fulfilled(value) {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    }
    function rejected(value) {
      try {
        step(generator["throw"](value));
      } catch (e) {
        reject(e);
      }
    }
    function step(result) {
      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
    }
    step((generator = generator.apply(thisArg, _arguments || [])).next());
  });
};
var __generator5 = function(thisArg, body) {
  var _ = { label: 0, sent: function() {
    if (t[0] & 1) throw t[1];
    return t[1];
  }, trys: [], ops: [] }, f2, y, t, g;
  return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
    return this;
  }), g;
  function verb(n) {
    return function(v) {
      return step([n, v]);
    };
  }
  function step(op) {
    if (f2) throw new TypeError("Generator is already executing.");
    while (_) try {
      if (f2 = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
      if (y = 0, t) op = [op[0] & 2, t.value];
      switch (op[0]) {
        case 0:
        case 1:
          t = op;
          break;
        case 4:
          _.label++;
          return { value: op[1], done: false };
        case 5:
          _.label++;
          y = op[1];
          op = [0];
          continue;
        case 7:
          op = _.ops.pop();
          _.trys.pop();
          continue;
        default:
          if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
            _ = 0;
            continue;
          }
          if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
            _.label = op[1];
            break;
          }
          if (op[0] === 6 && _.label < t[1]) {
            _.label = t[1];
            t = op;
            break;
          }
          if (t && _.label < t[2]) {
            _.label = t[2];
            _.ops.push(op);
            break;
          }
          if (t[2]) _.ops.pop();
          _.trys.pop();
          continue;
      }
      op = body.call(thisArg, _);
    } catch (e) {
      op = [6, e];
      y = 0;
    } finally {
      f2 = t = 0;
    }
    if (op[0] & 5) throw op[1];
    return { value: op[0] ? op[1] : void 0, done: true };
  }
};
var CRC32 = (
  /** @class */
  function() {
    function CRC322() {
      this.crc = -1;
      this.table = this.makeTable();
    }
    CRC322.prototype.makeTable = function() {
      var table = new Array();
      for (var i = 0; i < 256; i++) {
        var t = i;
        for (var j = 0; j < 8; j++) {
          if (t & 1) {
            t = t >>> 1 ^ 3988292384;
          } else {
            t >>>= 1;
          }
        }
        table[i] = t;
      }
      return table;
    };
    CRC322.prototype.append = function(data) {
      var crc = this.crc;
      for (var offset = 0; offset < data.byteLength; offset++) {
        crc = crc >>> 8 ^ this.table[(crc ^ data[offset]) & 255];
      }
      this.crc = crc;
    };
    CRC322.prototype.compute = function() {
      return (this.crc ^ -1) >>> 0;
    };
    CRC322.prototype.readAsUint8Array = function(file) {
      return __awaiter5(this, void 0, void 0, function() {
        var _a2;
        return __generator5(this, function(_b) {
          switch (_b.label) {
            case 0:
              if (!(typeof file.arrayBuffer === "function")) return [3, 2];
              _a2 = Uint8Array.bind;
              return [4, file.arrayBuffer()];
            case 1:
              return [2, new (_a2.apply(Uint8Array, [void 0, _b.sent()]))()];
            case 2:
              return [2, new Promise(function(resolve, reject) {
                var reader = new FileReader();
                reader.onload = function() {
                  if (reader.result == null) {
                    reject();
                    return;
                  }
                  if (typeof reader.result === "string") {
                    reject();
                    return;
                  }
                  resolve(new Uint8Array(reader.result));
                };
                reader.readAsArrayBuffer(file);
              })];
          }
        });
      });
    };
    CRC322.prototype.file = function(file) {
      return __awaiter5(this, void 0, void 0, function() {
        var _a2, count, index, start, end, chuck;
        return __generator5(this, function(_b) {
          switch (_b.label) {
            case 0:
              if (!(file.size <= MB)) return [3, 2];
              _a2 = this.append;
              return [4, this.readAsUint8Array(file)];
            case 1:
              _a2.apply(this, [_b.sent()]);
              return [2, this.compute()];
            case 2:
              count = Math.ceil(file.size / MB);
              index = 0;
              _b.label = 3;
            case 3:
              if (!(index < count)) return [3, 6];
              start = index * MB;
              end = index === count - 1 ? file.size : start + MB;
              return [4, this.readAsUint8Array(file.slice(start, end))];
            case 4:
              chuck = _b.sent();
              this.append(new Uint8Array(chuck));
              _b.label = 5;
            case 5:
              index++;
              return [3, 3];
            case 6:
              return [2, this.compute()];
          }
        });
      });
    };
    CRC322.file = function(file) {
      var crc = new CRC322();
      return crc.file(file);
    };
    return CRC322;
  }()
);

// node_modules/.pnpm/qiniu-js@3.4.1/node_modules/qiniu-js/esm/upload/direct.js
var __extends4 = /* @__PURE__ */ function() {
  var extendStatics = function(d, b2) {
    extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d2, b3) {
      d2.__proto__ = b3;
    } || function(d2, b3) {
      for (var p in b3) if (b3.hasOwnProperty(p)) d2[p] = b3[p];
    };
    return extendStatics(d, b2);
  };
  return function(d, b2) {
    extendStatics(d, b2);
    function __() {
      this.constructor = d;
    }
    d.prototype = b2 === null ? Object.create(b2) : (__.prototype = b2.prototype, new __());
  };
}();
var __awaiter6 = function(thisArg, _arguments, P, generator) {
  function adopt(value) {
    return value instanceof P ? value : new P(function(resolve) {
      resolve(value);
    });
  }
  return new (P || (P = Promise))(function(resolve, reject) {
    function fulfilled(value) {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    }
    function rejected(value) {
      try {
        step(generator["throw"](value));
      } catch (e) {
        reject(e);
      }
    }
    function step(result) {
      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
    }
    step((generator = generator.apply(thisArg, _arguments || [])).next());
  });
};
var __generator6 = function(thisArg, body) {
  var _ = { label: 0, sent: function() {
    if (t[0] & 1) throw t[1];
    return t[1];
  }, trys: [], ops: [] }, f2, y, t, g;
  return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
    return this;
  }), g;
  function verb(n) {
    return function(v) {
      return step([n, v]);
    };
  }
  function step(op) {
    if (f2) throw new TypeError("Generator is already executing.");
    while (_) try {
      if (f2 = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
      if (y = 0, t) op = [op[0] & 2, t.value];
      switch (op[0]) {
        case 0:
        case 1:
          t = op;
          break;
        case 4:
          _.label++;
          return { value: op[1], done: false };
        case 5:
          _.label++;
          y = op[1];
          op = [0];
          continue;
        case 7:
          op = _.ops.pop();
          _.trys.pop();
          continue;
        default:
          if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
            _ = 0;
            continue;
          }
          if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
            _.label = op[1];
            break;
          }
          if (op[0] === 6 && _.label < t[1]) {
            _.label = t[1];
            t = op;
            break;
          }
          if (t && _.label < t[2]) {
            _.label = t[2];
            _.ops.push(op);
            break;
          }
          if (t[2]) _.ops.pop();
          _.trys.pop();
          continue;
      }
      op = body.call(thisArg, _);
    } catch (e) {
      op = [6, e];
      y = 0;
    } finally {
      f2 = t = 0;
    }
    if (op[0] & 5) throw op[1];
    return { value: op[0] ? op[1] : void 0, done: true };
  }
};
var Direct = (
  /** @class */
  function(_super) {
    __extends4(Direct2, _super);
    function Direct2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    Direct2.prototype.run = function() {
      return __awaiter6(this, void 0, void 0, function() {
        var formData, crcSign, customVars_1, result;
        var _this = this;
        return __generator6(this, function(_a2) {
          switch (_a2.label) {
            case 0:
              this.logger.info("start run Direct.");
              formData = new FormData();
              formData.append("file", this.file);
              formData.append("token", this.token);
              if (this.key != null) {
                formData.append("key", this.key);
              }
              formData.append("fname", this.putExtra.fname);
              if (!this.config.checkByServer) return [3, 2];
              return [4, CRC32.file(this.file)];
            case 1:
              crcSign = _a2.sent();
              formData.append("crc32", crcSign.toString());
              _a2.label = 2;
            case 2:
              if (this.putExtra.customVars) {
                this.logger.info("init customVars.");
                customVars_1 = this.putExtra.customVars;
                Object.keys(customVars_1).forEach(function(key) {
                  return formData.append(key, customVars_1[key].toString());
                });
                this.logger.info("customVars inited.");
              }
              this.logger.info("formData inited.");
              return [4, direct(this.uploadHost.getUrl(), formData, {
                onProgress: function(data) {
                  _this.updateDirectProgress(data.loaded, data.total);
                },
                onCreate: function(xhr) {
                  return _this.addXhr(xhr);
                }
              })];
            case 3:
              result = _a2.sent();
              this.logger.info("Direct progress finish.");
              this.finishDirectProgress();
              return [2, result];
          }
        });
      });
    };
    Direct2.prototype.updateDirectProgress = function(loaded, total) {
      this.progress = { total: this.getProgressInfoItem(loaded, total + 1) };
      this.onData(this.progress);
    };
    Direct2.prototype.finishDirectProgress = function() {
      if (!this.progress) {
        this.logger.warn("progress is null.");
        this.progress = { total: this.getProgressInfoItem(this.file.size, this.file.size) };
        this.onData(this.progress);
        return;
      }
      var total = this.progress.total;
      this.progress = { total: this.getProgressInfoItem(total.loaded + 1, total.size) };
      this.onData(this.progress);
    };
    return Direct2;
  }(base_default)
);
var direct_default = Direct;

// node_modules/.pnpm/qiniu-js@3.4.1/node_modules/qiniu-js/esm/logger/report-v3.js
function reportV3(token, data, retry) {
  if (retry === void 0) {
    retry = 3;
  }
  var xhr = createXHR();
  xhr.open("POST", "https://uplog.qbox.me/log/3");
  xhr.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
  xhr.setRequestHeader("Authorization", getAuthHeaders(token).Authorization);
  xhr.onreadystatechange = function() {
    if (xhr.readyState === 4 && xhr.status !== 200 && retry > 0) {
      reportV3(token, data, retry - 1);
    }
  };
  var stringifyData = [
    data.code || "",
    data.reqId || "",
    data.host || "",
    data.remoteIp || "",
    data.port || "",
    data.duration || "",
    data.time || "",
    data.bytesSent || "",
    data.upType || "",
    data.size || ""
  ].join(",");
  xhr.send(stringifyData);
}

// node_modules/.pnpm/qiniu-js@3.4.1/node_modules/qiniu-js/esm/logger/index.js
var __read2 = function(o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o), r, ar = [], e;
  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
  } catch (error) {
    e = { error };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }
  return ar;
};
var __spread2 = function() {
  for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read2(arguments[i]));
  return ar;
};
var Logger = (
  /** @class */
  function() {
    function Logger2(token, disableReport, level, prefix) {
      if (disableReport === void 0) {
        disableReport = true;
      }
      if (level === void 0) {
        level = "OFF";
      }
      if (prefix === void 0) {
        prefix = "UPLOAD";
      }
      this.token = token;
      this.disableReport = disableReport;
      this.level = level;
      this.prefix = prefix;
      this.id = ++Logger2.id;
    }
    Logger2.prototype.getPrintPrefix = function(level) {
      return "Qiniu-JS-SDK [" + level + "][" + this.prefix + "#" + this.id + "]:";
    };
    Logger2.prototype.report = function(data, retry) {
      if (this.disableReport)
        return;
      try {
        reportV3(this.token, data, retry);
      } catch (error) {
        this.warn(error);
      }
    };
    Logger2.prototype.info = function() {
      var args = [];
      for (var _i = 0; _i < arguments.length; _i++) {
        args[_i] = arguments[_i];
      }
      var allowLevel = ["INFO"];
      if (allowLevel.includes(this.level)) {
        console.log.apply(console, __spread2([this.getPrintPrefix("INFO")], args));
      }
    };
    Logger2.prototype.warn = function() {
      var args = [];
      for (var _i = 0; _i < arguments.length; _i++) {
        args[_i] = arguments[_i];
      }
      var allowLevel = ["INFO", "WARN"];
      if (allowLevel.includes(this.level)) {
        console.warn.apply(console, __spread2([this.getPrintPrefix("WARN")], args));
      }
    };
    Logger2.prototype.error = function() {
      var args = [];
      for (var _i = 0; _i < arguments.length; _i++) {
        args[_i] = arguments[_i];
      }
      var allowLevel = ["INFO", "WARN", "ERROR"];
      if (allowLevel.includes(this.level)) {
        console.error.apply(console, __spread2([this.getPrintPrefix("ERROR")], args));
      }
    };
    Logger2.id = 0;
    return Logger2;
  }()
);
var logger_default = Logger;

// node_modules/.pnpm/qiniu-js@3.4.1/node_modules/qiniu-js/esm/upload/hosts.js
var __awaiter7 = function(thisArg, _arguments, P, generator) {
  function adopt(value) {
    return value instanceof P ? value : new P(function(resolve) {
      resolve(value);
    });
  }
  return new (P || (P = Promise))(function(resolve, reject) {
    function fulfilled(value) {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    }
    function rejected(value) {
      try {
        step(generator["throw"](value));
      } catch (e) {
        reject(e);
      }
    }
    function step(result) {
      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
    }
    step((generator = generator.apply(thisArg, _arguments || [])).next());
  });
};
var __generator7 = function(thisArg, body) {
  var _ = { label: 0, sent: function() {
    if (t[0] & 1) throw t[1];
    return t[1];
  }, trys: [], ops: [] }, f2, y, t, g;
  return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
    return this;
  }), g;
  function verb(n) {
    return function(v) {
      return step([n, v]);
    };
  }
  function step(op) {
    if (f2) throw new TypeError("Generator is already executing.");
    while (_) try {
      if (f2 = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
      if (y = 0, t) op = [op[0] & 2, t.value];
      switch (op[0]) {
        case 0:
        case 1:
          t = op;
          break;
        case 4:
          _.label++;
          return { value: op[1], done: false };
        case 5:
          _.label++;
          y = op[1];
          op = [0];
          continue;
        case 7:
          op = _.ops.pop();
          _.trys.pop();
          continue;
        default:
          if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
            _ = 0;
            continue;
          }
          if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
            _.label = op[1];
            break;
          }
          if (op[0] === 6 && _.label < t[1]) {
            _.label = t[1];
            t = op;
            break;
          }
          if (t && _.label < t[2]) {
            _.label = t[2];
            _.ops.push(op);
            break;
          }
          if (t[2]) _.ops.pop();
          _.trys.pop();
          continue;
      }
      op = body.call(thisArg, _);
    } catch (e) {
      op = [6, e];
      y = 0;
    } finally {
      f2 = t = 0;
    }
    if (op[0] & 5) throw op[1];
    return { value: op[0] ? op[1] : void 0, done: true };
  }
};
var __read3 = function(o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o), r, ar = [], e;
  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
  } catch (error) {
    e = { error };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }
  return ar;
};
var __spread3 = function() {
  for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read3(arguments[i]));
  return ar;
};
var unfreezeTimeMap = /* @__PURE__ */ new Map();
var Host = (
  /** @class */
  function() {
    function Host2(host, protocol) {
      this.host = host;
      this.protocol = protocol;
    }
    Host2.prototype.isFrozen = function() {
      var currentTime = (/* @__PURE__ */ new Date()).getTime();
      var unfreezeTime = unfreezeTimeMap.get(this.host);
      return unfreezeTime != null && unfreezeTime >= currentTime;
    };
    Host2.prototype.freeze = function(time) {
      if (time === void 0) {
        time = 20;
      }
      var unfreezeTime = (/* @__PURE__ */ new Date()).getTime() + time * 1e3;
      unfreezeTimeMap.set(this.host, unfreezeTime);
    };
    Host2.prototype.unfreeze = function() {
      unfreezeTimeMap["delete"](this.host);
    };
    Host2.prototype.getUrl = function() {
      return this.protocol + "://" + this.host;
    };
    Host2.prototype.getUnfreezeTime = function() {
      return unfreezeTimeMap.get(this.host);
    };
    return Host2;
  }()
);
var HostPool = (
  /** @class */
  function() {
    function HostPool2(initHosts) {
      if (initHosts === void 0) {
        initHosts = [];
      }
      this.initHosts = initHosts;
      this.cachedHostsMap = /* @__PURE__ */ new Map();
    }
    HostPool2.prototype.register = function(accessKey, bucketName, hosts, protocol) {
      this.cachedHostsMap.set(accessKey + "@" + bucketName, hosts.map(function(host) {
        return new Host(host, protocol);
      }));
    };
    HostPool2.prototype.refresh = function(accessKey, bucketName, protocol) {
      var _a2, _b, _c, _d;
      return __awaiter7(this, void 0, void 0, function() {
        var cachedHostList, response, stashHosts;
        return __generator7(this, function(_e) {
          switch (_e.label) {
            case 0:
              cachedHostList = this.cachedHostsMap.get(accessKey + "@" + bucketName) || [];
              if (cachedHostList.length > 0)
                return [
                  2
                  /*return*/
                ];
              if (this.initHosts.length > 0) {
                this.register(accessKey, bucketName, this.initHosts, protocol);
                return [
                  2
                  /*return*/
                ];
              }
              return [4, getUpHosts(accessKey, bucketName, protocol)];
            case 1:
              response = _e.sent();
              if ((response === null || response === void 0 ? void 0 : response.data) != null) {
                stashHosts = __spread3(((_b = (_a2 = response.data.up) === null || _a2 === void 0 ? void 0 : _a2.acc) === null || _b === void 0 ? void 0 : _b.main) || [], ((_d = (_c = response.data.up) === null || _c === void 0 ? void 0 : _c.acc) === null || _d === void 0 ? void 0 : _d.backup) || []);
                this.register(accessKey, bucketName, stashHosts, protocol);
              }
              return [
                2
                /*return*/
              ];
          }
        });
      });
    };
    HostPool2.prototype.getUp = function(accessKey, bucketName, protocol) {
      return __awaiter7(this, void 0, void 0, function() {
        var cachedHostList, availableHostList, priorityQueue;
        return __generator7(this, function(_a2) {
          switch (_a2.label) {
            case 0:
              return [4, this.refresh(accessKey, bucketName, protocol)];
            case 1:
              _a2.sent();
              cachedHostList = this.cachedHostsMap.get(accessKey + "@" + bucketName) || [];
              if (cachedHostList.length === 0)
                return [2, null];
              availableHostList = cachedHostList.filter(function(host) {
                return !host.isFrozen();
              });
              if (availableHostList.length > 0)
                return [
                  2,
                  availableHostList[0]
                  // 无可用的，去取离解冻最近的 host
                ];
              priorityQueue = cachedHostList.slice().sort(function(hostA, hostB) {
                return (hostA.getUnfreezeTime() || 0) - (hostB.getUnfreezeTime() || 0);
              });
              return [2, priorityQueue[0]];
          }
        });
      });
    };
    return HostPool2;
  }()
);

// node_modules/.pnpm/qiniu-js@3.4.1/node_modules/qiniu-js/esm/upload/index.js
function createUploadManager(options, handlers, hostPool, logger) {
  if (options.config && options.config.forceDirect) {
    logger.info("ues forceDirect mode.");
    return new direct_default(options, handlers, hostPool, logger);
  }
  if (options.file.size > 4 * MB) {
    logger.info("file size over 4M, use Resume.");
    return new resume_default(options, handlers, hostPool, logger);
  }
  logger.info("file size less or equal than 4M, use Direct.");
  return new direct_default(options, handlers, hostPool, logger);
}
function upload(file, key, token, putExtra, config) {
  var logger = new logger_default(token, config === null || config === void 0 ? void 0 : config.disableStatisticsReport, config === null || config === void 0 ? void 0 : config.debugLogLevel, file.name);
  var options = {
    file,
    key,
    token,
    putExtra,
    config: normalizeUploadConfig(config, logger)
  };
  var hostPool = new HostPool(options.config.uphost);
  return new Observable(function(observer) {
    var manager = createUploadManager(options, {
      onData: function(data) {
        return observer.next(data);
      },
      onError: function(err) {
        return observer.error(err);
      },
      onComplete: function(res) {
        return observer.complete(res);
      }
    }, hostPool, logger);
    manager.putFile();
    return manager.stop.bind(manager);
  });
}

// node_modules/.pnpm/qiniu-js@3.4.1/node_modules/qiniu-js/esm/utils/config.js
var __assign6 = function() {
  __assign6 = Object.assign || function(t) {
    for (var s, i = 1, n = arguments.length; i < n; i++) {
      s = arguments[i];
      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
        t[p] = s[p];
    }
    return t;
  };
  return __assign6.apply(this, arguments);
};
var __rest = function(s, e) {
  var t = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
    t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function")
    for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
      if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
        t[p[i]] = s[p[i]];
    }
  return t;
};
var __read4 = function(o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o), r, ar = [], e;
  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
  } catch (error) {
    e = { error };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }
  return ar;
};
var __spread4 = function() {
  for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read4(arguments[i]));
  return ar;
};
function normalizeUploadConfig(config, logger) {
  var _a2 = __assign6({}, config), upprotocol = _a2.upprotocol, uphost = _a2.uphost, otherConfig = __rest(_a2, ["upprotocol", "uphost"]);
  var normalizeConfig = __assign6({ uphost: [], retryCount: 3, checkByMD5: false, forceDirect: false, useCdnDomain: true, checkByServer: false, concurrentRequestLimit: 3, chunkSize: DEFAULT_CHUNK_SIZE, upprotocol: "https", debugLogLevel: "OFF", disableStatisticsReport: false }, otherConfig);
  if (upprotocol) {
    normalizeConfig.upprotocol = upprotocol.replace(/:$/, "");
  }
  var hostList = [];
  if (logger && (config === null || config === void 0 ? void 0 : config.uphost) != null && (config === null || config === void 0 ? void 0 : config.region) != null) {
    logger.warn("do not use both the uphost and region config.");
  }
  if (uphost) {
    if (Array.isArray(uphost)) {
      hostList.push.apply(hostList, __spread4(uphost));
    } else {
      hostList.push(uphost);
    }
  } else if (normalizeConfig === null || normalizeConfig === void 0 ? void 0 : normalizeConfig.region) {
    var hostMap = regionUphostMap[normalizeConfig === null || normalizeConfig === void 0 ? void 0 : normalizeConfig.region];
    if (normalizeConfig.useCdnDomain) {
      hostList.push.apply(hostList, __spread4(hostMap.cdnUphost));
    } else {
      hostList.push.apply(hostList, __spread4(hostMap.srcUphost));
    }
  }
  return __assign6(__assign6({}, normalizeConfig), { uphost: hostList.filter(Boolean) });
}

// node_modules/.pnpm/qiniu-js@3.4.1/node_modules/qiniu-js/esm/utils/compress.js
var __assign7 = function() {
  __assign7 = Object.assign || function(t) {
    for (var s, i = 1, n = arguments.length; i < n; i++) {
      s = arguments[i];
      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
        t[p] = s[p];
    }
    return t;
  };
  return __assign7.apply(this, arguments);
};
var __awaiter8 = function(thisArg, _arguments, P, generator) {
  function adopt(value) {
    return value instanceof P ? value : new P(function(resolve) {
      resolve(value);
    });
  }
  return new (P || (P = Promise))(function(resolve, reject) {
    function fulfilled(value) {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    }
    function rejected(value) {
      try {
        step(generator["throw"](value));
      } catch (e) {
        reject(e);
      }
    }
    function step(result) {
      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
    }
    step((generator = generator.apply(thisArg, _arguments || [])).next());
  });
};
var __generator8 = function(thisArg, body) {
  var _ = { label: 0, sent: function() {
    if (t[0] & 1) throw t[1];
    return t[1];
  }, trys: [], ops: [] }, f2, y, t, g;
  return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
    return this;
  }), g;
  function verb(n) {
    return function(v) {
      return step([n, v]);
    };
  }
  function step(op) {
    if (f2) throw new TypeError("Generator is already executing.");
    while (_) try {
      if (f2 = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
      if (y = 0, t) op = [op[0] & 2, t.value];
      switch (op[0]) {
        case 0:
        case 1:
          t = op;
          break;
        case 4:
          _.label++;
          return { value: op[1], done: false };
        case 5:
          _.label++;
          y = op[1];
          op = [0];
          continue;
        case 7:
          op = _.ops.pop();
          _.trys.pop();
          continue;
        default:
          if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
            _ = 0;
            continue;
          }
          if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
            _.label = op[1];
            break;
          }
          if (op[0] === 6 && _.label < t[1]) {
            _.label = t[1];
            t = op;
            break;
          }
          if (t && _.label < t[2]) {
            _.label = t[2];
            _.ops.push(op);
            break;
          }
          if (t[2]) _.ops.pop();
          _.trys.pop();
          continue;
      }
      op = body.call(thisArg, _);
    } catch (e) {
      op = [6, e];
      y = 0;
    } finally {
      f2 = t = 0;
    }
    if (op[0] & 5) throw op[1];
    return { value: op[0] ? op[1] : void 0, done: true };
  }
};
var mimeTypes = {
  PNG: "image/png",
  JPEG: "image/jpeg",
  WEBP: "image/webp",
  BMP: "image/bmp"
};
var maxSteps = 4;
var scaleFactor = Math.log(2);
var supportMimeTypes = Object.keys(mimeTypes).map(function(type) {
  return mimeTypes[type];
});
var defaultType = mimeTypes.JPEG;
function isSupportedType(type) {
  return supportMimeTypes.includes(type);
}
var Compress = (
  /** @class */
  function() {
    function Compress2(file, config) {
      this.file = file;
      this.config = config;
      this.config = __assign7({ quality: 0.92, noCompressIfLarger: false }, this.config);
    }
    Compress2.prototype.process = function() {
      return __awaiter8(this, void 0, void 0, function() {
        var srcDimension, originImage, canvas, scale, scaleCanvas, distBlob;
        return __generator8(this, function(_a2) {
          switch (_a2.label) {
            case 0:
              this.outputType = this.file.type;
              srcDimension = {};
              if (!isSupportedType(this.file.type)) {
                throw new QiniuError(QiniuErrorName.UnsupportedFileType, "unsupported file type: " + this.file.type);
              }
              return [4, this.getOriginImage()];
            case 1:
              originImage = _a2.sent();
              return [4, this.getCanvas(originImage)];
            case 2:
              canvas = _a2.sent();
              scale = 1;
              if (this.config.maxWidth) {
                scale = Math.min(1, this.config.maxWidth / canvas.width);
              }
              if (this.config.maxHeight) {
                scale = Math.min(1, scale, this.config.maxHeight / canvas.height);
              }
              srcDimension.width = canvas.width;
              srcDimension.height = canvas.height;
              return [4, this.doScale(canvas, scale)];
            case 3:
              scaleCanvas = _a2.sent();
              distBlob = this.toBlob(scaleCanvas);
              if (distBlob.size > this.file.size && this.config.noCompressIfLarger) {
                return [2, {
                  dist: this.file,
                  width: srcDimension.width,
                  height: srcDimension.height
                }];
              }
              return [2, {
                dist: distBlob,
                width: scaleCanvas.width,
                height: scaleCanvas.height
              }];
          }
        });
      });
    };
    Compress2.prototype.clear = function(ctx, width, height) {
      if (this.outputType === defaultType) {
        ctx.fillStyle = "#fff";
        ctx.fillRect(0, 0, width, height);
      } else {
        ctx.clearRect(0, 0, width, height);
      }
    };
    Compress2.prototype.getOriginImage = function() {
      var _this = this;
      return new Promise(function(resolve, reject) {
        var url = createObjectURL(_this.file);
        var img = new Image();
        img.onload = function() {
          resolve(img);
        };
        img.onerror = function() {
          reject("image load error");
        };
        img.src = url;
      });
    };
    Compress2.prototype.getCanvas = function(img) {
      var _this = this;
      return new Promise(function(resolve, reject) {
        var canvas = document.createElement("canvas");
        var context = canvas.getContext("2d");
        if (!context) {
          reject(new QiniuError(QiniuErrorName.GetCanvasContextFailed, "context is null"));
          return;
        }
        var width = img.width, height = img.height;
        canvas.height = height;
        canvas.width = width;
        _this.clear(context, width, height);
        context.drawImage(img, 0, 0);
        resolve(canvas);
      });
    };
    Compress2.prototype.doScale = function(source, scale) {
      return __awaiter8(this, void 0, void 0, function() {
        var sctx, steps, factor, mirror, mctx, width, height, originWidth, originHeight, src, context, i, dw, dh, canvas, data;
        return __generator8(this, function(_a2) {
          if (scale === 1) {
            return [2, source];
          }
          sctx = source.getContext("2d");
          steps = Math.min(maxSteps, Math.ceil(1 / scale / scaleFactor));
          factor = Math.pow(scale, 1 / steps);
          mirror = document.createElement("canvas");
          mctx = mirror.getContext("2d");
          width = source.width, height = source.height;
          originWidth = width;
          originHeight = height;
          mirror.width = width;
          mirror.height = height;
          if (!mctx || !sctx) {
            throw new QiniuError(QiniuErrorName.GetCanvasContextFailed, "mctx or sctx can't be null");
          }
          for (i = 0; i < steps; i++) {
            dw = width * factor | 0;
            dh = height * factor | 0;
            if (i === steps - 1) {
              dw = originWidth * scale;
              dh = originHeight * scale;
            }
            if (i % 2 === 0) {
              src = source;
              context = mctx;
            } else {
              src = mirror;
              context = sctx;
            }
            this.clear(context, width, height);
            context.drawImage(src, 0, 0, width, height, 0, 0, dw, dh);
            width = dw;
            height = dh;
          }
          canvas = src === source ? mirror : source;
          data = context.getImageData(0, 0, width, height);
          canvas.width = width;
          canvas.height = height;
          context.putImageData(data, 0, 0);
          return [2, canvas];
        });
      });
    };
    Compress2.prototype.toBlob = function(result) {
      var dataURL = result.toDataURL(this.outputType, this.config.quality);
      var buffer = atob(dataURL.split(",")[1]).split("").map(function(char) {
        return char.charCodeAt(0);
      });
      var blob = new Blob([new Uint8Array(buffer)], { type: this.outputType });
      return blob;
    };
    return Compress2;
  }()
);

// node_modules/.pnpm/@fast-crud+fast-extends@1.2_48d83ac4990201ef9a8c3e5855a79123/node_modules/@fast-crud/fast-extends/dist/uploader-qiniu-185b6498.mjs
async function b(n, o, a, e) {
  const t = await e.getToken({
    fileName: o,
    key: a,
    file: n,
    ...e.custom,
    ...e
  });
  let r = null;
  return typeof t == "string" ? r = { token: t, expires: 3600 } : r = t, r.expiresTime = (/* @__PURE__ */ new Date()).getTime() + r.expires * 1e3, r.token;
}
async function f({ file: n, fileName: o, onProgress: a, options: e }) {
  const t = await bt(n, o, e), r = await b(n, o, t, e);
  return new Promise((u, c) => {
    upload(n, t, r, e.putExtra, e.putConfig).subscribe({
      next(s) {
        s && a(s.total);
      },
      error(s) {
        c(s);
      },
      async complete(s) {
        let i = { url: e.domain + "/" + t, key: t };
        if (e.successHandle) {
          i = await e.successHandle(i), u(i);
          return;
        }
        u(i);
      }
    });
  });
}
async function q(n) {
  const { getConfig: o } = Ct(), a = o("qiniu"), e = n.options, t = merge_default(cloneDeep_default(a), e);
  return n.options = t, await f(n);
}
export {
  b as getToken,
  q as upload
};
//# sourceMappingURL=uploader-qiniu-185b6498-HMG2VG4L.js.map
