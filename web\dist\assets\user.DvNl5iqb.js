const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/userNews.R_uN0Xup.js","assets/vue.BNx9QYep.js","assets/index.BHZI5pdK.js","assets/index.Dg-OhEXY.css","assets/_plugin-vue_export-helper.DlAUqK2U.js","assets/userNews.ZSiEwXxT.css","assets/search.V8C1-b5s.js","assets/search.m-zLAaZ_.css"])))=>i.map(i=>d[i]);
import{a7 as ae,a6 as L,u as re,L as C,at as ie,g as ue,aq as q,au as ce,_ as x,e as de,l as me,av as B,E as fe,S as ge,V as _e}from"./index.BHZI5pdK.js";import{d as j,R as be,M as P,h as A,g as pe,c as he,j as we,k as g,a as R,o as $,l as n,e as c,w as o,q as f,s as w,f as F,u as s,P as N,m as V,n as ve}from"./vue.BNx9QYep.js";import{_ as Ce}from"./_plugin-vue_export-helper.DlAUqK2U.js";const D=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],_=(()=>{if(typeof document>"u")return!1;const a=D[0],u={};for(const t of D)if((t==null?void 0:t[1])in document){for(const[m,v]of t.entries())u[a[m]]=v;return u}return!1})(),U={change:_.fullscreenchange,error:_.fullscreenerror};let i={request(a=document.documentElement,u){return new Promise((t,b)=>{const m=()=>{i.off("change",m),t()};i.on("change",m);const v=a[_.requestFullscreen](u);v instanceof Promise&&v.then(m).catch(b)})},exit(){return new Promise((a,u)=>{if(!i.isFullscreen){a();return}const t=()=>{i.off("change",t),a()};i.on("change",t);const b=document[_.exitFullscreen]();b instanceof Promise&&b.then(t).catch(u)})},toggle(a,u){return i.isFullscreen?i.exit():i.request(a,u)},onchange(a){i.on("change",a)},onerror(a){i.on("error",a)},on(a,u){const t=U[a];t&&document.addEventListener(t,u,!1)},off(a,u){const t=U[a];t&&document.removeEventListener(t,u,!1)},raw:_};Object.defineProperties(i,{isFullscreen:{get:()=>!!document[_.fullscreenElement]},element:{enumerable:!0,get:()=>document[_.fullscreenElement]??void 0},isEnabled:{enumerable:!0,get:()=>!!document[_.fullscreenEnabled]}});_||(i={isEnabled:!1});const Se={class:"layout-navbars-breadcrumb-user-icon"},Ee=["title"],ke={class:"layout-navbars-breadcrumb-user-icon"},Fe=["title"],ye=["title"],ze={class:"layout-navbars-breadcrumb-user-icon"},Ie=["title"],Be={key:0,class:"online-status-span"},Pe=["src"],Re={class:"layout-navbars-breadcrumb-user-link"},$e={key:0},Me=["src"],Oe=j({name:"layoutBreadcrumbUser"}),Te=j({...Oe,setup(a){const u=N(()=>x(()=>import("./userNews.R_uN0Xup.js"),__vite__mapDeps([0,1,2,3,4,5]))),t=N(()=>x(()=>import("./search.V8C1-b5s.js"),__vite__mapDeps([6,1,2,3,4,7]))),{locale:b,t:m}=ae.useI18n(),v=be(),H=L(),G=re(),{userInfos:E}=P(H),{themeConfig:S}=P(G),M=A(),d=pe({isScreenfull:!1,disabledI18n:"zh-cn",disabledSize:"large"}),J=he(()=>{let e="";const{layout:l,isClassicSplitMenu:r}=S.value;return["defaults","columns"].includes(l)||l==="classic"&&!r?e="1":e="",e}),{isSocketOpen:p}=P(L()),O=A(),K=()=>{var e,l;p.value||(B.is_reonnect=!0,B.reconnect_current=1,B.reconnect()),(l=(e=s(O).popperRef)==null?void 0:e.delayHide)==null||l.call(e)},Q=()=>{if(!i.isEnabled)return de.warning("暂不不支持全屏"),!1;i.toggle(),i.on("change",()=>{i.isFullscreen?d.isScreenfull=!0:d.isScreenfull=!1})},W=()=>{me.emit("openSetingsDrawer")},X=e=>{e==="logOut"?fe({closeOnClickModal:!1,closeOnPressEscape:!1,title:m("message.user.logOutTitle"),message:m("message.user.logOutMessage"),showCancelButton:!0,confirmButtonText:m("message.user.logOutConfirm"),cancelButtonText:m("message.user.logOutCancel"),buttonSize:"default",beforeClose:(l,r,h)=>{l==="confirm"?(r.confirmButtonLoading=!0,r.confirmButtonText=m("message.user.logOutExit"),setTimeout(()=>{h(),setTimeout(()=>{r.confirmButtonLoading=!1},300)},700)):h()}}).then(async()=>{ge.clear(),window.location.reload()}).catch(()=>{}):e==="wareHouse"?window.open("https://gitee.com/huge-dream/django-vue3-admin"):v.push(e)},Y=()=>{M.value.openSearch()},Z=e=>{C.remove("themeConfig"),S.value.globalComponentSize=e,C.set("themeConfig",S.value),k("globalComponentSize","disabledSize"),window.location.reload()},ee=e=>{C.remove("themeConfig"),S.value.globalI18n=e,C.set("themeConfig",S.value),b.value=e,_e.useTitle(),k("globalI18n","disabledI18n")},k=(e,l)=>{d[l]=C.get("themeConfig")[e]};we(()=>{C.get("themeConfig")&&(k("globalComponentSize","disabledSize"),k("globalI18n","disabledI18n"))});const T=ie();return(e,l)=>{const r=g("el-dropdown-item"),h=g("el-dropdown-menu"),y=g("el-dropdown"),ne=g("ele-Search"),z=g("el-icon"),oe=g("ele-Bell"),I=g("el-badge"),se=g("el-popover"),te=g("el-popconfirm"),le=g("ele-ArrowDown");return $(),R("div",{class:"layout-navbars-breadcrumb-user pr15",style:ve({flex:J.value})},[n(y,{"show-timeout":70,"hide-timeout":50,trigger:"click",onCommand:Z},{dropdown:o(()=>[n(h,null,{default:o(()=>[n(r,{command:"large",disabled:d.disabledSize==="large"},{default:o(()=>[f(w(e.$t("message.user.dropdownLarge")),1)]),_:1},8,["disabled"]),n(r,{command:"default",disabled:d.disabledSize==="default"},{default:o(()=>[f(w(e.$t("message.user.dropdownDefault")),1)]),_:1},8,["disabled"]),n(r,{command:"small",disabled:d.disabledSize==="small"},{default:o(()=>[f(w(e.$t("message.user.dropdownSmall")),1)]),_:1},8,["disabled"])]),_:1})]),default:o(()=>[c("div",Se,[c("i",{class:"iconfont icon-ziti",title:e.$t("message.user.title0")},null,8,Ee)])]),_:1}),n(y,{"show-timeout":70,"hide-timeout":50,trigger:"click",onCommand:ee},{dropdown:o(()=>[n(h,null,{default:o(()=>[n(r,{command:"zh-cn",disabled:d.disabledI18n==="zh-cn"},{default:o(()=>l[0]||(l[0]=[f("简体中文")])),_:1,__:[0]},8,["disabled"]),n(r,{command:"en",disabled:d.disabledI18n==="en"},{default:o(()=>l[1]||(l[1]=[f("English")])),_:1,__:[1]},8,["disabled"]),n(r,{command:"zh-tw",disabled:d.disabledI18n==="zh-tw"},{default:o(()=>l[2]||(l[2]=[f("繁體中文")])),_:1,__:[2]},8,["disabled"])]),_:1})]),default:o(()=>[c("div",ke,[c("i",{class:F(["iconfont",d.disabledI18n==="en"?"icon-fuhao-yingwen":"icon-fuhao-zhongwen"]),title:e.$t("message.user.title1")},null,10,Fe)])]),_:1}),c("div",{class:"layout-navbars-breadcrumb-user-icon",onClick:Y},[n(z,{title:e.$t("message.user.title2")},{default:o(()=>[n(ne)]),_:1},8,["title"])]),c("div",{class:"layout-navbars-breadcrumb-user-icon",onClick:W},[c("i",{class:"icon-skin iconfont",title:e.$t("message.user.title3")},null,8,ye)]),c("div",ze,[n(se,{placement:"bottom",trigger:"hover",transition:"el-zoom-in-top",width:300,persistent:!1},{reference:o(()=>[n(I,{value:s(T).unread,hidden:s(T).unread===0},{default:o(()=>[n(z,{title:e.$t("message.user.title4")},{default:o(()=>[n(oe)]),_:1},8,["title"])]),_:1},8,["value","hidden"])]),default:o(()=>[n(s(u))]),_:1})]),c("div",{class:"layout-navbars-breadcrumb-user-icon mr10",onClick:Q},[c("i",{class:F(["iconfont",d.isScreenfull?"icon-tuichuquanping":"icon-fullscreen"]),title:d.isScreenfull?e.$t("message.user.title6"):e.$t("message.user.title5")},null,10,Ie)]),c("div",null,[s(p)?V("",!0):($(),R("span",Be,[n(te,{width:"250",ref_key:"onlinePopoverRef",ref:O,"confirm-button-text":e.$t("message.user.retry"),icon:s(ce),trigger:"hover","icon-color":"#626AEF",title:e.$t("message.user.onlinePrompt"),onConfirm:K},{reference:o(()=>[n(I,{"is-dot":"",class:F(["item",{"online-status":s(p),"online-down":!s(p)}])},{default:o(()=>[c("img",{src:s(ue)(s(E).avatar)||s(q),class:"layout-navbars-breadcrumb-user-link-photo mr5"},null,8,Pe)]),_:1},8,["class"])]),_:1},8,["confirm-button-text","icon","title"])]))]),n(y,{"show-timeout":70,"hide-timeout":50,onCommand:X},{dropdown:o(()=>[n(h,null,{default:o(()=>[n(r,{command:"/home"},{default:o(()=>[f(w(e.$t("message.user.dropdown1")),1)]),_:1}),n(r,{command:"/personal"},{default:o(()=>[f(w(e.$t("message.user.dropdown2")),1)]),_:1}),n(r,{command:"/versionUpgradeLog"},{default:o(()=>l[3]||(l[3]=[f("更新日志")])),_:1,__:[3]}),n(r,{divided:"",command:"logOut"},{default:o(()=>[f(w(e.$t("message.user.dropdown5")),1)]),_:1})]),_:1})]),default:o(()=>[c("span",Re,[s(p)?($(),R("span",$e,[n(I,{"is-dot":"",class:F(["item",{"online-status":s(p),"online-down":!s(p)}])},{default:o(()=>[c("img",{src:s(E).avatar||s(q),class:"layout-navbars-breadcrumb-user-link-photo mr5"},null,8,Me)]),_:1},8,["class"])])):V("",!0),f(" "+w(s(E).username===""?"common":s(E).username)+" ",1),n(z,{class:"el-icon--right"},{default:o(()=>[n(le)]),_:1})])]),_:1}),n(s(t),{ref_key:"searchRef",ref:M},null,512)],4)}}}),Ae=Ce(Te,[["__scopeId","data-v-2a5d6533"]]);export{Ae as default};
