import{a as v}from"./index.BHZI5pdK.js";import x from"./crud.CJe3iZL7.js";import{d as l,h as C,j as g,k as t,b as k,o as h,w as n,l as a,x as R,u as V}from"./vue.BNx9QYep.js";import"./index.tdFpgju_.js";import"./_plugin-vue_export-helper.DlAUqK2U.js";import"./authFunction.D3Be3hRy.js";const B=l({name:"messageCenter"}),O=l({...B,setup(w){const e=C("send"),d=r=>{const{paneName:o}=r;e.value=o,s.doRefresh()},p={tabActivted:e},{crudRef:m,crudBinding:_,crudExpose:s}=v({createCrudOptions:x,context:p});return g(()=>{s.doRefresh()}),(r,o)=>{const c=t("el-tab-pane"),u=t("el-tabs"),f=t("fs-crud"),i=t("fs-page");return h(),k(i,null,{default:n(()=>[a(f,R({ref_key:"crudRef",ref:m},V(_)),{"header-middle":n(()=>[a(u,{modelValue:e.value,"onUpdate:modelValue":o[0]||(o[0]=b=>e.value=b),onTabClick:d},{default:n(()=>[a(c,{label:"我的发布",name:"send"}),a(c,{label:"我的接收",name:"receive"})]),_:1},8,["modelValue"])]),_:1},16)]),_:1})}}});export{O as default};
