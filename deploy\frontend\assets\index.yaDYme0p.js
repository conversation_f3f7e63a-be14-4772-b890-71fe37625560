import{a as E}from"./index.BHZI5pdK.js";import C from"./crud.Ch-bT32q.js";import{_ as h}from"./index.vue_vue_type_script_setup_true_name_importExcel_lang.COJSjT1E.js";import{d as v,C as N,j as $,k as r,y as w,b as c,o as p,w as t,l as i,x as B,A as I,q as m,s as M}from"./vue.BNx9QYep.js";import{_ as S}from"./_plugin-vue_export-helper.DlAUqK2U.js";import"./authFunction.D3Be3hRy.js";const V=v({name:"VIEWSETNAME",components:{importExcel:h},setup(){const e=N(),o={componentName:e==null?void 0:e.type.name},{crudBinding:s,crudRef:n,crudExpose:a,resetCrudOptions:_}=E({createCrudOptions:C,context:o});return $(()=>{a.doRefresh()}),{crudBinding:s,crudRef:n}}});function k(e,o,s,n,a,_){const u=r("el-tag"),d=r("importExcel"),f=r("fs-crud"),l=r("fs-page"),g=w("auth");return p(),c(l,{class:"PageFeatureSearchMulti"},{default:t(()=>[i(f,B({ref:"crudRef"},e.crudBinding),{cell_url:t(x=>[i(u,{size:"small"},{default:t(()=>[m(M(x.row.url),1)]),_:2},1024)]),"actionbar-right":t(()=>[I((p(),c(d,{api:"api/VIEWSETNAME/"},{default:t(()=>o[0]||(o[0]=[m("导入")])),_:1,__:[0]})),[[g,"user:Import"]])]),_:1},16)]),_:1})}const O=S(V,[["render",k]]);export{O as default};
