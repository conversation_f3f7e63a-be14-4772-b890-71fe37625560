import{d as g,M as c,c as s,a as r,o as u,e as t,s as p,u as y}from"./vue.BNx9QYep.js";import{u as v,O as C,ah as h}from"./index.BHZI5pdK.js";import{l as k}from"./favicon.nG7g_iy4.js";import{_ as S}from"./_plugin-vue_export-helper.DlAUqK2U.js";const x=["src"],T={style:{"font-size":"x-large"}},b=["src"],z=g({name:"layoutLogo"}),B=g({...z,setup(L){const m=v(),{themeConfig:o}=c(m),f=s(()=>{let{isCollapse:n,layout:i}=o.value;return!n||i==="classic"||document.body.clientWidth<1e3}),a=()=>{if(o.value.layout==="transverse")return!1;o.value.isCollapse=!o.value.isCollapse},_=C(),{systemConfig:d}=c(_),e=s(()=>d.value),l=s(()=>h.isEmpty(e.value["login.site_logo"])?k:e.value["login.site_logo"]);return(n,i)=>f.value?(u(),r("div",{key:0,class:"layout-logo",onClick:a},[t("img",{src:l.value,class:"layout-logo-medium-img"},null,8,x),t("span",T,p(e.value["login.site_title"]||y(o).globalTitle),1)])):(u(),r("div",{key:1,class:"layout-logo-size",onClick:a},[t("img",{src:l.value,class:"layout-logo-size-img"},null,8,b)]))}}),I=S(B,[["__scopeId","data-v-a82bfacd"]]);export{I as default};
