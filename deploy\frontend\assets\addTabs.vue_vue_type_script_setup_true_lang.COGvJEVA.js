import{A as V}from"./api.FR9XnnSw.js";import{s as b}from"./index.BHZI5pdK.js";import{d as g,g as f,h as v,i as w,k as u,a as x,o as C,l as t,w as o,u as s,q as p}from"./vue.BNx9QYep.js";const q={style:{padding:"20px"}},R=g({__name:"addTabs",setup(A){let l=f({title:null,key:null});const i=v(),_=f({title:[{required:!0,message:"请输入"}],key:[{required:!0,message:"请输入"},{pattern:/^[A-Za-z0-9]+$/,message:"只能是英文和数字"}]}),c=w("refreshView"),k=async m=>{m&&await m.validate((e,r)=>{e?V(l).then(n=>{n.code==2e3&&(b("新增成功"),c())}):console.log("error submit!",r)})};return(m,e)=>{const r=u("el-input"),n=u("el-form-item"),d=u("el-button"),y=u("el-form");return C(),x("div",q,[t(y,{ref_key:"formRef",ref:i,model:s(l),rules:_,"label-width":"80px"},{default:o(()=>[t(n,{label:"标题",prop:"title"},{default:o(()=>[t(r,{modelValue:s(l).title,"onUpdate:modelValue":e[0]||(e[0]=a=>s(l).title=a)},null,8,["modelValue"])]),_:1}),t(n,{label:"key值",prop:"key"},{default:o(()=>[t(r,{modelValue:s(l).key,"onUpdate:modelValue":e[1]||(e[1]=a=>s(l).key=a)},null,8,["modelValue"])]),_:1}),t(n,null,{default:o(()=>[t(d,{type:"primary",onClick:e[2]||(e[2]=a=>k(i.value))},{default:o(()=>e[3]||(e[3]=[p("立即创建")])),_:1,__:[3]}),t(d,null,{default:o(()=>e[4]||(e[4]=[p("取消")])),_:1,__:[4]})]),_:1})]),_:1},8,["model","rules"])])}}});export{R as _};
