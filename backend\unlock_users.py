#!/usr/bin/env python
"""
用户账号解锁脚本
用于解锁因登录失败次数过多而被锁定的用户账号
"""

import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings')
django.setup()

from dvadmin.system.models import Users

def unlock_all_users():
    """解锁所有被锁定的用户"""
    print("正在查找被锁定的用户...")
    
    # 查找所有被锁定的用户
    locked_users = Users.objects.filter(is_active=False)
    
    if not locked_users.exists():
        print("✅ 没有发现被锁定的用户")
        return
    
    print(f"发现 {locked_users.count()} 个被锁定的用户:")
    for user in locked_users:
        print(f"  - 用户名: {user.username}, 姓名: {user.name}, 登录错误次数: {user.login_error_count}")
    
    print("\n开始解锁用户...")
    
    # 解锁所有用户
    unlocked_count = 0
    for user in locked_users:
        user.is_active = True
        user.login_error_count = 0
        user.save()
        print(f"✅ 已解锁用户: {user.username} ({user.name})")
        unlocked_count += 1
    
    print(f"\n🎉 解锁完成！共解锁了 {unlocked_count} 个用户")

def unlock_specific_user(username):
    """解锁指定用户"""
    print(f"正在查找用户: {username}")
    
    try:
        user = Users.objects.get(username=username)
        
        if user.is_active:
            print(f"✅ 用户 {username} 当前状态正常，无需解锁")
            return
        
        print(f"发现被锁定的用户: {user.username} ({user.name})")
        print(f"登录错误次数: {user.login_error_count}")
        
        # 解锁用户
        user.is_active = True
        user.login_error_count = 0
        user.save()
        
        print(f"✅ 用户 {username} 解锁成功！")
        
    except Users.DoesNotExist:
        print(f"❌ 用户 {username} 不存在")

def show_locked_users():
    """显示所有被锁定的用户"""
    print("查询被锁定的用户...")
    
    locked_users = Users.objects.filter(is_active=False)
    
    if not locked_users.exists():
        print("✅ 没有发现被锁定的用户")
        return
    
    print(f"发现 {locked_users.count()} 个被锁定的用户:")
    print("-" * 60)
    print(f"{'用户名':<15} {'姓名':<15} {'错误次数':<10} {'最后登录'}")
    print("-" * 60)
    
    for user in locked_users:
        last_login = user.last_login.strftime('%Y-%m-%d %H:%M:%S') if user.last_login else '从未登录'
        print(f"{user.username:<15} {user.name:<15} {user.login_error_count:<10} {last_login}")

def main():
    """主函数"""
    print("=" * 50)
    print("华绿系统 - 用户账号解锁工具")
    print("=" * 50)
    
    if len(sys.argv) == 1:
        # 没有参数，显示帮助信息
        print("使用方法:")
        print("  python unlock_users.py --all          # 解锁所有被锁定的用户")
        print("  python unlock_users.py --user admin   # 解锁指定用户")
        print("  python unlock_users.py --list         # 查看被锁定的用户列表")
        print("")
        show_locked_users()
        
    elif len(sys.argv) == 2:
        if sys.argv[1] == '--all':
            unlock_all_users()
        elif sys.argv[1] == '--list':
            show_locked_users()
        else:
            print("❌ 无效的参数，请使用 --all, --list 或 --user <用户名>")
            
    elif len(sys.argv) == 3:
        if sys.argv[1] == '--user':
            unlock_specific_user(sys.argv[2])
        else:
            print("❌ 无效的参数，请使用 --user <用户名>")
    else:
        print("❌ 参数过多")

if __name__ == '__main__':
    main()
