import{r as o}from"./index.BHZI5pdK.js";import"./vue.BNx9QYep.js";const r="/api/system/operation_log/";function i(e){return o({url:r,method:"get",params:e})}function s(e){return o({url:r,method:"post",data:e})}function d(e){return o({url:r+e.id+"/",method:"put",data:e})}function l(e){return o({url:r+e+"/",method:"delete",data:{id:e}})}const b=function({crudExpose:e}){return{crudOptions:{request:{pageRequest:async t=>await i(t),addRequest:async({form:t})=>await s(t),editRequest:async({form:t,row:n})=>(t.id=n.id,await d(t)),delRequest:async({row:t})=>await l(t.id)},actionbar:{buttons:{add:{show:!1}}},rowHandle:{fixed:"right",width:100,buttons:{view:{type:"text"},edit:{show:!1},remove:{show:!1}}},columns:{_index:{title:"序号",form:{show:!1},column:{align:"center",width:"70px",columnSetDisabled:!0,formatter:t=>{let n=t.index??1,a=e.crudBinding.value.pagination;return((a.currentPage??1)-1)*a.pageSize+n+1}}},search:{title:"关键词",column:{show:!1},search:{show:!0,component:{props:{clearable:!0},placeholder:"请输入关键词"}},form:{show:!1,component:{props:{clearable:!0}}}},request_modular:{title:"请求模块",search:{disabled:!1},type:"input",column:{minWidth:100},form:{disabled:!0,component:{placeholder:"请输入请求模块"}}},request_path:{title:"请求地址",search:{disabled:!1},type:"input",column:{minWidth:200},form:{disabled:!0,component:{placeholder:"请输入请求地址"}}},request_body:{column:{showOverflowTooltip:!0,width:200,minWidth:100},title:"请求参数",search:{disabled:!0},disabled:!0,type:"textarea",form:{component:{props:{type:"textarea"},autosize:{minRows:2,maxRows:8},placeholder:"请输入关键词"}}},request_method:{title:"请求方法",type:"input",search:{disabled:!1},column:{minWidth:100},form:{disabled:!0,component:{placeholder:"请输入请求方法"}},component:{props:{color:"auto"}}},request_msg:{title:"操作说明",disabled:!0,form:{component:{span:12}}},request_ip:{title:"IP地址",search:{disabled:!1},type:"input",column:{minWidth:100},form:{disabled:!0,component:{placeholder:"请输入IP地址"}},component:{props:{color:"auto"}}},request_browser:{title:"请求浏览器",type:"input",column:{minWidth:120},form:{disabled:!0},component:{props:{color:"auto"}}},response_code:{title:"响应码",search:{disabled:!0},type:"input",column:{minWidth:100},form:{disabled:!0},component:{props:{color:"auto"}}},request_os:{title:"操作系统",disabled:!0,search:{disabled:!0},type:"input",column:{minWidth:120},form:{disabled:!0},component:{props:{color:"auto"}}},json_result:{title:"返回信息",search:{disabled:!0},type:"input",column:{minWidth:150},form:{disabled:!0},component:{props:{color:"auto"}}},creator_name:{title:"操作人",column:{minWidth:100},form:{disabled:!0}}}}}};export{b as createCrudOptions};
