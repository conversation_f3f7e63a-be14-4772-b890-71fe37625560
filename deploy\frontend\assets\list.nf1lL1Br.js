import{d as m,k as t,a,o,l as r,w as l,b as c,m as i,F as w,p as v,e as S,f as h}from"./vue.BNx9QYep.js";import{_ as b}from"./_plugin-vue_export-helper.DlAUqK2U.js";const B={class:"icon-selector-warp-row"},I=m({name:"iconSelectorList"}),N=m({...I,props:{list:{type:Array,default:()=>[]},empty:{type:String,default:()=>"无相关图标"},prefix:{type:String,default:()=>""}},emits:["get-icon"],setup(e,{emit:p}){const _=e,d=p,u=s=>{d("get-icon",s)};return(s,V)=>{const f=t("SvgIcon"),g=t("el-col"),y=t("el-row"),k=t("el-empty"),C=t("el-scrollbar");return o(),a("div",B,[r(C,{ref:"selectorScrollbarRef"},{default:l(()=>[_.list.length>0?(o(),c(y,{key:0,gutter:10},{default:l(()=>[(o(!0),a(w,null,v(e.list,(n,x)=>(o(),c(g,{xs:6,sm:4,md:4,lg:4,xl:4,key:x,onClick:z=>u(n)},{default:l(()=>[S("div",{class:h(["icon-selector-warp-item",{"icon-selector-active":e.prefix===n}])},[r(f,{name:n},null,8,["name"])],2)]),_:2},1032,["onClick"]))),128))]),_:1})):i("",!0),e.list.length<=0?(o(),c(k,{key:1,"image-size":100,description:e.empty},null,8,["description"])):i("",!0)]),_:1},512)])}}}),A=b(N,[["__scopeId","data-v-80a22ee8"]]);export{A as default};
