# Generated by Django 4.2.14 on 2025-07-22 10:45

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PerformanceAnalysis',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('description', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.Char<PERSON>ield(blank=True, help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('salesperson', models.CharField(max_length=100, verbose_name='业务员')),
                ('region', models.CharField(blank=True, max_length=100, null=True, verbose_name='负责地区')),
                ('query_type', models.CharField(max_length=20, verbose_name='查询类型')),
                ('query_year', models.IntegerField(verbose_name='查询年份')),
                ('query_month', models.IntegerField(blank=True, null=True, verbose_name='查询月份')),
                ('query_quarter', models.IntegerField(blank=True, null=True, verbose_name='查询季度')),
                ('order_count', models.IntegerField(default=0, verbose_name='订单数量')),
                ('customer_count', models.IntegerField(default=0, verbose_name='客户数量')),
                ('product_count', models.IntegerField(default=0, verbose_name='产品种类')),
                ('total_quantity', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='总销量')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='总销售金额')),
                ('avg_price', models.DecimalField(decimal_places=4, default=0, max_digits=10, verbose_name='平均单价')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '绩效分析',
                'verbose_name_plural': '绩效分析',
                'db_table': 'performance_analysis',
                'ordering': ('-create_datetime',),
            },
        ),
    ]
