import{d as h,c as g,k as c,a as n,o as t,p as w,F as i,b as l,w as r,l as s,e as u,s as m,Z as d,q as k}from"./vue.BNx9QYep.js";import{V as I}from"./index.BHZI5pdK.js";const B={key:0,href:"#/templateCenter",target:"_blank"},S=["onClick"],V=["onClick"],x=h({name:"navMenuSubItem"}),M=h({...x,props:{chil:{type:Array,default:()=>[]}},setup(f){const y=f,C=g(()=>y.chil),p=o=>{I.handleOpenLink(o)};return(o,N)=>{const a=c("SvgIcon"),b=c("sub-item",!0),L=c("el-sub-menu"),_=c("el-menu-item");return t(!0),n(i,null,w(C.value,e=>(t(),n(i,null,[e.children&&e.children.length>0?(t(),l(L,{index:e.path,key:e.path},{title:r(()=>[s(a,{name:e.meta.icon},null,8,["name"]),u("span",null,m(o.$t(e.meta.title)),1)]),default:r(()=>[s(b,{chil:e.children},null,8,["chil"])]),_:2},1032,["index"])):(t(),n(i,{key:1},[e.name==="templateCenter"?(t(),n("a",B,[(t(),l(_,{key:e.path},{default:r(()=>[!e.meta.isLink||e.meta.isLink&&e.meta.isIframe?(t(),n(i,{key:0},[s(a,{name:e.meta.icon},null,8,["name"]),u("span",null,m(o.$t(e.meta.title)),1)],64)):(t(),n("a",{key:1,class:"w100",onClick:d($=>p(e),["prevent"])},[s(a,{name:e.meta.icon},null,8,["name"]),k(" "+m(o.$t(e.meta.title)),1)],8,S))]),_:2},1024))])):(t(),l(_,{index:e.path,key:e.path},{default:r(()=>[!e.meta.isLink||e.meta.isLink&&e.meta.isIframe?(t(),n(i,{key:0},[s(a,{name:e.meta.icon},null,8,["name"]),u("span",null,m(o.$t(e.meta.title)),1)],64)):(t(),n("a",{key:1,class:"w100",onClick:d($=>p(e),["prevent"])},[s(a,{name:e.meta.icon},null,8,["name"]),k(" "+m(o.$t(e.meta.title)),1)],8,V))]),_:2},1032,["index"]))],64))],64))),256)}}});export{M as default};
