[2025-05-16 14:20:02,303][django.request.log_response():241] [ERROR] Internal Server Error: /api/hl/departments/
[2025-05-16 14:20:02,304][django.server.log_message():212] [ERROR] "GET /api/hl/departments/?page=1&limit=20 HTTP/1.1" 500 290
[2025-05-16 14:20:05,653][django.request.log_response():241] [ERROR] Internal Server Error: /api/hl/departments/
[2025-05-16 14:20:05,654][django.server.log_message():212] [ERROR] "GET /api/hl/departments/?page=1&limit=20 HTTP/1.1" 500 290
[2025-05-16 14:20:06,486][django.request.log_response():241] [ERROR] Internal Server Error: /api/hl/departments/
[2025-05-16 14:20:06,486][django.server.log_message():212] [ERROR] "GET /api/hl/departments/?page=1&limit=20 HTTP/1.1" 500 290
[2025-05-16 14:20:47,524][django.request.log_response():241] [ERROR] Internal Server Error: /api/hl/departments/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 497, in dispatch
    self.initial(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 415, in initial
    self.check_permissions(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 333, in check_permissions
    self.permission_denied(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 174, in permission_denied
    raise exceptions.NotAuthenticated()
rest_framework.exceptions.NotAuthenticated: 身份认证信息未提供。

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\utils.py", line 113, in load_backend
    return import_module("%s.base" % backend_name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 992, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'mssql'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 466, in handle_exception
    response = exception_handler(exc, context)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\exception.py", line 39, in CustomExceptionHandler
    response = exception_handler(ex, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 98, in exception_handler
    set_rollback()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 66, in set_rollback
    for db in connections.all():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\utils\connection.py", line 76, in all
    return [
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\utils\connection.py", line 77, in <listcomp>
    self[alias]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\utils\connection.py", line 62, in __getitem__
    conn = self.create_connection(alias)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\utils.py", line 193, in create_connection
    backend = load_backend(db["ENGINE"])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\utils.py", line 126, in load_backend
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: 'mssql' isn't an available database backend or couldn't be imported. Check the above exception. To use one of the built-in backends, use 'django.db.backends.XXX', where XXX is one of:
    'mysql', 'oracle', 'postgresql', 'sqlite3'
[2025-05-16 14:20:47,530][django.server.log_message():212] [ERROR] "GET /api/hl/departments/ HTTP/1.1" 500 179424
[2025-05-16 14:21:04,584][django.request.log_response():241] [ERROR] Internal Server Error: /api/hl/departments/
[2025-05-16 14:21:04,587][django.server.log_message():212] [ERROR] "GET /api/hl/departments/?page=1&limit=20 HTTP/1.1" 500 290
[2025-05-16 14:21:14,528][django.request.log_response():241] [ERROR] Internal Server Error: /api/hl/departments/
[2025-05-16 14:21:14,529][django.server.log_message():212] [ERROR] "GET /api/hl/departments/?page=1&limit=20 HTTP/1.1" 500 290
[2025-05-16 14:25:04,991][django.request.log_response():241] [ERROR] Internal Server Error: /api/hl/departments/
[2025-05-16 14:25:04,992][django.server.log_message():212] [ERROR] "GET /api/hl/departments/?page=1&limit=20 HTTP/1.1" 500 290
[2025-05-16 14:32:49,900][django.request.log_response():241] [ERROR] Internal Server Error: /api/hl/departments/
[2025-05-16 14:32:49,902][django.server.log_message():212] [ERROR] "GET /api/hl/departments/?page=1&limit=20 HTTP/1.1" 500 297
[2025-05-16 14:33:01,561][django.request.log_response():241] [ERROR] Internal Server Error: /api/hl/departments/
[2025-05-16 14:33:01,562][django.server.log_message():212] [ERROR] "GET /api/hl/departments/?page=1&limit=20 HTTP/1.1" 500 297
[2025-05-16 14:33:01,975][django.request.log_response():241] [ERROR] Internal Server Error: /api/hl/departments/
[2025-05-16 14:33:01,977][django.server.log_message():212] [ERROR] "GET /api/hl/departments/?page=1&limit=20 HTTP/1.1" 500 297
[2025-05-16 14:33:04,548][django.request.log_response():241] [ERROR] Internal Server Error: /api/hl/departments/
[2025-05-16 14:33:04,549][django.server.log_message():212] [ERROR] "GET /api/hl/departments/?page=1&limit=20 HTTP/1.1" 500 297
[2025-05-16 14:33:14,654][django.request.log_response():241] [ERROR] Internal Server Error: /api/hl/departments/
[2025-05-16 14:33:14,655][django.server.log_message():212] [ERROR] "GET /api/hl/departments/?page=1&limit=20 HTTP/1.1" 500 297
[2025-05-16 14:33:24,570][django.request.log_response():241] [ERROR] Internal Server Error: /api/hl/departments/
[2025-05-16 14:33:24,571][django.server.log_message():212] [ERROR] "GET /api/hl/departments/?page=1&limit=20 HTTP/1.1" 500 297
[2025-05-16 14:34:09,232][django.request.log_response():241] [ERROR] Internal Server Error: /api/hl/departments/
[2025-05-16 14:34:09,234][django.server.log_message():212] [ERROR] "GET /api/hl/departments/?page=1&limit=20 HTTP/1.1" 500 297
[2025-05-16 14:38:31,103][django.request.log_response():241] [ERROR] Internal Server Error: /api/hl/departments/
[2025-05-16 14:38:31,104][django.server.log_message():212] [ERROR] "GET /api/hl/departments/?page=1&limit=20 HTTP/1.1" 500 302
[2025-05-16 14:39:44,126][django.request.log_response():241] [ERROR] Internal Server Error: /api/hl/departments/
[2025-05-16 14:39:44,127][django.server.log_message():212] [ERROR] "GET /api/hl/departments/?page=1&limit=20 HTTP/1.1" 500 302
[2025-05-16 14:50:59,080][django.request.log_response():241] [ERROR] Internal Server Error: /api/hl/departments/
[2025-05-16 14:50:59,081][django.server.log_message():212] [ERROR] "GET /api/hl/departments/?page=1&limit=20 HTTP/1.1" 500 59
[2025-05-16 14:51:00,350][django.request.log_response():241] [ERROR] Internal Server Error: /api/hl/departments/
[2025-05-16 14:51:00,351][django.server.log_message():212] [ERROR] "GET /api/hl/departments/?page=1&limit=20 HTTP/1.1" 500 59
[2025-05-16 14:51:08,136][django.request.log_response():241] [ERROR] Internal Server Error: /api/hl/departments/
[2025-05-16 14:51:08,139][django.server.log_message():212] [ERROR] "GET /api/hl/departments/?page=1&limit=20 HTTP/1.1" 500 59
[2025-05-19 14:13:13,744][hl_web.views.department_view.get():196] [ERROR] 获取部门列表失败: ('28000', "[28000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]用户 'AIS2018101755337' 登录失败。 (18456) (SQLDriverConnect); [28000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]用户 'AIS2018101755337' 登录失败。 (18456)")
Traceback (most recent call last):
  File "C:\HL_python\django-vue3-admin-master\backend\hl_web\views\department_view.py", line 96, in get
    conn = pyodbc.connect(connection_string)
pyodbc.InterfaceError: ('28000', "[28000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]用户 'AIS2018101755337' 登录失败。 (18456) (SQLDriverConnect); [28000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]用户 'AIS2018101755337' 登录失败。 (18456)")

[2025-05-19 14:15:00,388][hl_web.views.department_view.get():196] [ERROR] 获取部门列表失败: ('28000', "[28000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]用户 'AIS2018101755337' 登录失败。 (18456) (SQLDriverConnect); [28000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]用户 'AIS2018101755337' 登录失败。 (18456)")
Traceback (most recent call last):
  File "C:\HL_python\django-vue3-admin-master\backend\hl_web\views\department_view.py", line 96, in get
    conn = pyodbc.connect(connection_string)
pyodbc.InterfaceError: ('28000', "[28000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]用户 'AIS2018101755337' 登录失败。 (18456) (SQLDriverConnect); [28000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]用户 'AIS2018101755337' 登录失败。 (18456)")

[2025-05-19 14:15:05,323][hl_web.views.department_view.get():196] [ERROR] 获取部门列表失败: ('28000', "[28000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]用户 'AIS2018101755337' 登录失败。 (18456) (SQLDriverConnect); [28000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]用户 'AIS2018101755337' 登录失败。 (18456)")
Traceback (most recent call last):
  File "C:\HL_python\django-vue3-admin-master\backend\hl_web\views\department_view.py", line 96, in get
    conn = pyodbc.connect(connection_string)
pyodbc.InterfaceError: ('28000', "[28000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]用户 'AIS2018101755337' 登录失败。 (18456) (SQLDriverConnect); [28000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]用户 'AIS2018101755337' 登录失败。 (18456)")

[2025-05-19 14:15:18,526][hl_web.views.department_view.get():196] [ERROR] 获取部门列表失败: ('28000', "[28000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]用户 'AIS2018101755337' 登录失败。 (18456) (SQLDriverConnect); [28000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]用户 'AIS2018101755337' 登录失败。 (18456)")
Traceback (most recent call last):
  File "C:\HL_python\django-vue3-admin-master\backend\hl_web\views\department_view.py", line 96, in get
    conn = pyodbc.connect(connection_string)
pyodbc.InterfaceError: ('28000', "[28000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]用户 'AIS2018101755337' 登录失败。 (18456) (SQLDriverConnect); [28000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]用户 'AIS2018101755337' 登录失败。 (18456)")

[2025-05-19 14:20:16,048][hl_web.views.department_view.get():112] [ERROR] 查询部门表失败: ('42S02', "[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]对象名 '部门' 无效。 (208) (SQLExecDirectW)")
[2025-05-19 14:20:16,049][hl_web.views.department_view.get():196] [ERROR] 获取部门列表失败: 查询部门表失败: ('42S02', "[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]对象名 '部门' 无效。 (208) (SQLExecDirectW)")
Traceback (most recent call last):
  File "C:\HL_python\django-vue3-admin-master\backend\hl_web\views\department_view.py", line 103, in get
    cursor.execute("SELECT * FROM 部门")
pyodbc.ProgrammingError: ('42S02', "[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]对象名 '部门' 无效。 (208) (SQLExecDirectW)")

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\HL_python\django-vue3-admin-master\backend\hl_web\views\department_view.py", line 108, in get
    cursor.execute("SELECT * FROM [部门]")
pyodbc.ProgrammingError: ('42S02', "[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]对象名 '部门' 无效。 (208) (SQLExecDirectW)")

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\HL_python\django-vue3-admin-master\backend\hl_web\views\department_view.py", line 113, in get
    raise Exception(error_msg)
Exception: 查询部门表失败: ('42S02', "[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]对象名 '部门' 无效。 (208) (SQLExecDirectW)")

[2025-05-19 14:36:47,519][hl_web.views.department_view.get():112] [ERROR] 查询部门表失败: ('42S02', "[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]对象名 '部门' 无效。 (208) (SQLExecDirectW)")
[2025-05-19 14:36:47,522][hl_web.views.department_view.get():196] [ERROR] 获取部门列表失败: 查询部门表失败: ('42S02', "[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]对象名 '部门' 无效。 (208) (SQLExecDirectW)")
Traceback (most recent call last):
  File "C:\HL_python\django-vue3-admin-master\backend\hl_web\views\department_view.py", line 103, in get
    cursor.execute("SELECT * FROM 部门")
pyodbc.ProgrammingError: ('42S02', "[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]对象名 '部门' 无效。 (208) (SQLExecDirectW)")

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\HL_python\django-vue3-admin-master\backend\hl_web\views\department_view.py", line 108, in get
    cursor.execute("SELECT * FROM [部门]")
pyodbc.ProgrammingError: ('42S02', "[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]对象名 '部门' 无效。 (208) (SQLExecDirectW)")

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\HL_python\django-vue3-admin-master\backend\hl_web\views\department_view.py", line 113, in get
    raise Exception(error_msg)
Exception: 查询部门表失败: ('42S02', "[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]对象名 '部门' 无效。 (208) (SQLExecDirectW)")

[2025-05-19 14:59:55,916][hl_web.views.get():112] [ERROR] 查询部门表失败: ('42S02', "[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]对象名 '部门' 无效。 (208) (SQLExecDirectW)")
[2025-05-19 14:59:55,919][hl_web.views.get():196] [ERROR] 获取部门列表失败: 查询部门表失败: ('42S02', "[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]对象名 '部门' 无效。 (208) (SQLExecDirectW)")
Traceback (most recent call last):
  File "C:\HL_python\django-vue3-admin-master\backend\hl_web\views.py", line 103, in get
    cursor.execute("SELECT * FROM 部门")
pyodbc.ProgrammingError: ('42S02', "[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]对象名 '部门' 无效。 (208) (SQLExecDirectW)")

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\HL_python\django-vue3-admin-master\backend\hl_web\views.py", line 108, in get
    cursor.execute("SELECT * FROM [部门]")
pyodbc.ProgrammingError: ('42S02', "[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]对象名 '部门' 无效。 (208) (SQLExecDirectW)")

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\HL_python\django-vue3-admin-master\backend\hl_web\views.py", line 113, in get
    raise Exception(error_msg)
Exception: 查询部门表失败: ('42S02', "[42S02] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]对象名 '部门' 无效。 (208) (SQLExecDirectW)")

[2025-05-19 15:48:33,594][dvadmin.utils.exception.CustomExceptionHandler():68] [ERROR] Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\connections.py", line 255, in query
    _mysql.connection.query(self, query)
MySQLdb.ProgrammingError: (1146, "Table 'django-vue3-admin.hl_sales_order' doesn't exist")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\viewset.py", line 110, in list
    page = self.paginate_queryset(queryset)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\generics.py", line 175, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\pagination.py", line 41, in paginate_queryset
    self.page = paginator.page(page_number)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\paginator.py", line 72, in page
    number = self.validate_number(number)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\paginator.py", line 53, in validate_number
    if number > self.num_pages:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\utils\functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\paginator.py", line 99, in num_pages
    if self.count == 0 and not self.allow_empty_first_page:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\utils\functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\paginator.py", line 93, in count
    return c()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 608, in count
    return self.query.get_count(using=self.db)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 568, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 554, in get_aggregation
    result = compiler.execute_sql(SINGLE)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\connections.py", line 255, in query
    _mysql.connection.query(self, query)
django.db.utils.ProgrammingError: (1146, "Table 'django-vue3-admin.hl_sales_order' doesn't exist")
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\connections.py", line 255, in query
    _mysql.connection.query(self, query)
MySQLdb.ProgrammingError: (1146, "Table 'django-vue3-admin.hl_sales_order' doesn't exist")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\viewset.py", line 110, in list
    page = self.paginate_queryset(queryset)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\generics.py", line 175, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\pagination.py", line 41, in paginate_queryset
    self.page = paginator.page(page_number)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\paginator.py", line 72, in page
    number = self.validate_number(number)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\paginator.py", line 53, in validate_number
    if number > self.num_pages:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\utils\functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\paginator.py", line 99, in num_pages
    if self.count == 0 and not self.allow_empty_first_page:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\utils\functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\paginator.py", line 93, in count
    return c()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 608, in count
    return self.query.get_count(using=self.db)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 568, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 554, in get_aggregation
    result = compiler.execute_sql(SINGLE)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\connections.py", line 255, in query
    _mysql.connection.query(self, query)
django.db.utils.ProgrammingError: (1146, "Table 'django-vue3-admin.hl_sales_order' doesn't exist")
[2025-05-19 15:48:33,596][dvadmin.utils.exception.CustomExceptionHandler():68] [ERROR] Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\connections.py", line 255, in query
    _mysql.connection.query(self, query)
MySQLdb.ProgrammingError: (1146, "Table 'django-vue3-admin.hl_sales_order' doesn't exist")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\hl_web\views.py", line 77, in dashboard_summary
    total_sales = SalesOrder.objects.aggregate(total=Sum('total_amount'))['total'] or 0
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 592, in aggregate
    return self.query.chain().get_aggregation(self.db, kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 554, in get_aggregation
    result = compiler.execute_sql(SINGLE)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\connections.py", line 255, in query
    _mysql.connection.query(self, query)
django.db.utils.ProgrammingError: (1146, "Table 'django-vue3-admin.hl_sales_order' doesn't exist")
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\connections.py", line 255, in query
    _mysql.connection.query(self, query)
MySQLdb.ProgrammingError: (1146, "Table 'django-vue3-admin.hl_sales_order' doesn't exist")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\hl_web\views.py", line 77, in dashboard_summary
    total_sales = SalesOrder.objects.aggregate(total=Sum('total_amount'))['total'] or 0
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 592, in aggregate
    return self.query.chain().get_aggregation(self.db, kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 554, in get_aggregation
    result = compiler.execute_sql(SINGLE)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\connections.py", line 255, in query
    _mysql.connection.query(self, query)
django.db.utils.ProgrammingError: (1146, "Table 'django-vue3-admin.hl_sales_order' doesn't exist")
[2025-05-19 15:48:47,347][dvadmin.utils.exception.CustomExceptionHandler():68] [ERROR] Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\connections.py", line 255, in query
    _mysql.connection.query(self, query)
MySQLdb.ProgrammingError: (1146, "Table 'django-vue3-admin.hl_sales_order' doesn't exist")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\viewset.py", line 110, in list
    page = self.paginate_queryset(queryset)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\generics.py", line 175, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\pagination.py", line 41, in paginate_queryset
    self.page = paginator.page(page_number)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\paginator.py", line 72, in page
    number = self.validate_number(number)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\paginator.py", line 53, in validate_number
    if number > self.num_pages:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\utils\functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\paginator.py", line 99, in num_pages
    if self.count == 0 and not self.allow_empty_first_page:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\utils\functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\paginator.py", line 93, in count
    return c()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 608, in count
    return self.query.get_count(using=self.db)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 568, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 554, in get_aggregation
    result = compiler.execute_sql(SINGLE)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\connections.py", line 255, in query
    _mysql.connection.query(self, query)
django.db.utils.ProgrammingError: (1146, "Table 'django-vue3-admin.hl_sales_order' doesn't exist")
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\connections.py", line 255, in query
    _mysql.connection.query(self, query)
MySQLdb.ProgrammingError: (1146, "Table 'django-vue3-admin.hl_sales_order' doesn't exist")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\viewset.py", line 110, in list
    page = self.paginate_queryset(queryset)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\generics.py", line 175, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\pagination.py", line 41, in paginate_queryset
    self.page = paginator.page(page_number)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\paginator.py", line 72, in page
    number = self.validate_number(number)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\paginator.py", line 53, in validate_number
    if number > self.num_pages:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\utils\functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\paginator.py", line 99, in num_pages
    if self.count == 0 and not self.allow_empty_first_page:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\utils\functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\paginator.py", line 93, in count
    return c()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 608, in count
    return self.query.get_count(using=self.db)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 568, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 554, in get_aggregation
    result = compiler.execute_sql(SINGLE)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\connections.py", line 255, in query
    _mysql.connection.query(self, query)
django.db.utils.ProgrammingError: (1146, "Table 'django-vue3-admin.hl_sales_order' doesn't exist")
[2025-05-19 15:48:47,443][dvadmin.utils.exception.CustomExceptionHandler():68] [ERROR] Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\connections.py", line 255, in query
    _mysql.connection.query(self, query)
MySQLdb.ProgrammingError: (1146, "Table 'django-vue3-admin.hl_sales_order' doesn't exist")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\hl_web\views.py", line 77, in dashboard_summary
    total_sales = SalesOrder.objects.aggregate(total=Sum('total_amount'))['total'] or 0
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 592, in aggregate
    return self.query.chain().get_aggregation(self.db, kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 554, in get_aggregation
    result = compiler.execute_sql(SINGLE)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\connections.py", line 255, in query
    _mysql.connection.query(self, query)
django.db.utils.ProgrammingError: (1146, "Table 'django-vue3-admin.hl_sales_order' doesn't exist")
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\connections.py", line 255, in query
    _mysql.connection.query(self, query)
MySQLdb.ProgrammingError: (1146, "Table 'django-vue3-admin.hl_sales_order' doesn't exist")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\hl_web\views.py", line 77, in dashboard_summary
    total_sales = SalesOrder.objects.aggregate(total=Sum('total_amount'))['total'] or 0
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 592, in aggregate
    return self.query.chain().get_aggregation(self.db, kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 554, in get_aggregation
    result = compiler.execute_sql(SINGLE)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\connections.py", line 255, in query
    _mysql.connection.query(self, query)
django.db.utils.ProgrammingError: (1146, "Table 'django-vue3-admin.hl_sales_order' doesn't exist")
[2025-05-19 15:48:54,749][dvadmin.utils.exception.CustomExceptionHandler():68] [ERROR] Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\connections.py", line 255, in query
    _mysql.connection.query(self, query)
MySQLdb.ProgrammingError: (1146, "Table 'django-vue3-admin.hl_sales_order' doesn't exist")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\viewset.py", line 110, in list
    page = self.paginate_queryset(queryset)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\generics.py", line 175, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\pagination.py", line 41, in paginate_queryset
    self.page = paginator.page(page_number)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\paginator.py", line 72, in page
    number = self.validate_number(number)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\paginator.py", line 53, in validate_number
    if number > self.num_pages:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\utils\functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\paginator.py", line 99, in num_pages
    if self.count == 0 and not self.allow_empty_first_page:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\utils\functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\paginator.py", line 93, in count
    return c()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 608, in count
    return self.query.get_count(using=self.db)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 568, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 554, in get_aggregation
    result = compiler.execute_sql(SINGLE)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\connections.py", line 255, in query
    _mysql.connection.query(self, query)
django.db.utils.ProgrammingError: (1146, "Table 'django-vue3-admin.hl_sales_order' doesn't exist")
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\connections.py", line 255, in query
    _mysql.connection.query(self, query)
MySQLdb.ProgrammingError: (1146, "Table 'django-vue3-admin.hl_sales_order' doesn't exist")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\viewset.py", line 110, in list
    page = self.paginate_queryset(queryset)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\generics.py", line 175, in paginate_queryset
    return self.paginator.paginate_queryset(queryset, self.request, view=self)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\pagination.py", line 41, in paginate_queryset
    self.page = paginator.page(page_number)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\paginator.py", line 72, in page
    number = self.validate_number(number)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\paginator.py", line 53, in validate_number
    if number > self.num_pages:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\utils\functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\paginator.py", line 99, in num_pages
    if self.count == 0 and not self.allow_empty_first_page:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\utils\functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\paginator.py", line 93, in count
    return c()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 608, in count
    return self.query.get_count(using=self.db)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 568, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 554, in get_aggregation
    result = compiler.execute_sql(SINGLE)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\connections.py", line 255, in query
    _mysql.connection.query(self, query)
django.db.utils.ProgrammingError: (1146, "Table 'django-vue3-admin.hl_sales_order' doesn't exist")
[2025-05-19 18:46:38,591][django.request.log_response():241] [ERROR] Internal Server Error: /api/sales_record/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 497, in dispatch
    self.initial(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 415, in initial
    self.check_permissions(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 333, in check_permissions
    self.permission_denied(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 174, in permission_denied
    raise exceptions.NotAuthenticated()
rest_framework.exceptions.NotAuthenticated: 身份认证信息未提供。

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\utils.py", line 113, in load_backend
    return import_module("%s.base" % backend_name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 992, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'mssql'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\viewsets.py", line 124, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 466, in handle_exception
    response = exception_handler(exc, context)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\exception.py", line 39, in CustomExceptionHandler
    response = exception_handler(ex, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 98, in exception_handler
    set_rollback()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 66, in set_rollback
    for db in connections.all():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\utils\connection.py", line 76, in all
    return [
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\utils\connection.py", line 77, in <listcomp>
    self[alias]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\utils\connection.py", line 62, in __getitem__
    conn = self.create_connection(alias)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\utils.py", line 193, in create_connection
    backend = load_backend(db["ENGINE"])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\utils.py", line 126, in load_backend
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: 'mssql' isn't an available database backend or couldn't be imported. Check the above exception. To use one of the built-in backends, use 'django.db.backends.XXX', where XXX is one of:
    'mysql', 'oracle', 'postgresql', 'sqlite3'
[2025-05-19 18:46:38,596][django.server.log_message():212] [ERROR] "GET /api/sales_record/?page=1&limit=10 HTTP/1.1" 500 180485
[2025-05-19 18:46:38,644][django.request.log_response():241] [ERROR] Internal Server Error: /api/sales_record/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 497, in dispatch
    self.initial(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 415, in initial
    self.check_permissions(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 333, in check_permissions
    self.permission_denied(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 174, in permission_denied
    raise exceptions.NotAuthenticated()
rest_framework.exceptions.NotAuthenticated: 身份认证信息未提供。

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\utils.py", line 113, in load_backend
    return import_module("%s.base" % backend_name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 992, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'mssql'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\viewsets.py", line 124, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 466, in handle_exception
    response = exception_handler(exc, context)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\exception.py", line 39, in CustomExceptionHandler
    response = exception_handler(ex, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 98, in exception_handler
    set_rollback()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 66, in set_rollback
    for db in connections.all():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\utils\connection.py", line 76, in all
    return [
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\utils\connection.py", line 77, in <listcomp>
    self[alias]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\utils\connection.py", line 62, in __getitem__
    conn = self.create_connection(alias)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\utils.py", line 193, in create_connection
    backend = load_backend(db["ENGINE"])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\utils.py", line 126, in load_backend
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: 'mssql' isn't an available database backend or couldn't be imported. Check the above exception. To use one of the built-in backends, use 'django.db.backends.XXX', where XXX is one of:
    'mysql', 'oracle', 'postgresql', 'sqlite3'
[2025-05-19 18:46:38,649][django.server.log_message():212] [ERROR] "GET /api/sales_record/?page=1&limit=10 HTTP/1.1" 500 180505
[2025-05-19 18:49:19,104][sales_management.views.list():49] [ERROR] SQL Server 连接测试失败: 'mssql' isn't an available database backend or couldn't be imported. Check the above exception. To use one of the built-in backends, use 'django.db.backends.XXX', where XXX is one of:
    'mysql', 'oracle', 'postgresql', 'sqlite3'
[2025-05-19 18:49:19,116][sales_management.views.list():49] [ERROR] SQL Server 连接测试失败: 'mssql' isn't an available database backend or couldn't be imported. Check the above exception. To use one of the built-in backends, use 'django.db.backends.XXX', where XXX is one of:
    'mysql', 'oracle', 'postgresql', 'sqlite3'
[2025-05-19 18:55:47,017][sales_management.views.list():49] [ERROR] SQL Server 连接测试失败: 'mssql' isn't an available database backend or couldn't be imported. Check the above exception. To use one of the built-in backends, use 'django.db.backends.XXX', where XXX is one of:
    'mysql', 'oracle', 'postgresql', 'sqlite3'
[2025-05-19 19:04:36,756][sales_management.views.list():70] [ERROR] SQL Server 标准连接测试失败: 'mssql.pyodbc' isn't an available database backend or couldn't be imported. Check the above exception. To use one of the built-in backends, use 'django.db.backends.XXX', where XXX is one of:
    'mysql', 'oracle', 'postgresql', 'sqlite3'
[2025-05-19 19:04:36,757][sales_management.views.list():71] [ERROR] 错误类型: ImproperlyConfigured
[2025-05-19 19:04:36,760][sales_management.views.list():72] [ERROR] 错误详情: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\utils.py", line 113, in load_backend
    return import_module("%s.base" % backend_name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 992, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 992, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'mssql'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\HL_python\django-vue3-admin-master\backend\sales_management\views.py", line 55, in list
    with connections['sqlserver_direct'].cursor() as cursor:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\utils\connection.py", line 62, in __getitem__
    conn = self.create_connection(alias)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\utils.py", line 193, in create_connection
    backend = load_backend(db["ENGINE"])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\utils.py", line 126, in load_backend
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: 'mssql.pyodbc' isn't an available database backend or couldn't be imported. Check the above exception. To use one of the built-in backends, use 'django.db.backends.XXX', where XXX is one of:
    'mysql', 'oracle', 'postgresql', 'sqlite3'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\utils.py", line 113, in load_backend
    return import_module("%s.base" % backend_name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 992, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 992, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'mssql'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\HL_python\django-vue3-admin-master\backend\sales_management\views.py", line 64, in list
    with connections['sqlserver'].cursor() as cursor:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\utils\connection.py", line 62, in __getitem__
    conn = self.create_connection(alias)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\utils.py", line 193, in create_connection
    backend = load_backend(db["ENGINE"])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\utils.py", line 126, in load_backend
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: 'mssql.pyodbc' isn't an available database backend or couldn't be imported. Check the above exception. To use one of the built-in backends, use 'django.db.backends.XXX', where XXX is one of:
    'mysql', 'oracle', 'postgresql', 'sqlite3'

[2025-05-19 21:12:11,161][sales_management.views.list():175] [ERROR] SQL查询执行失败: 'int' object has no attribute 'total'
[2025-05-19 21:12:11,162][sales_management.views.list():176] [ERROR] 错误类型: AttributeError
[2025-05-19 21:12:11,163][sales_management.views.list():177] [ERROR] 错误详情: Traceback (most recent call last):
  File "C:\HL_python\django-vue3-admin-master\backend\sales_management\views.py", line 173, in list
    page.total = total_count
AttributeError: 'int' object has no attribute 'total'

[2025-05-20 01:47:43,337][sales_management.views.list():232] [ERROR] SQL查询执行失败: ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FALLAMOUNT' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FALLAMOUNT' 无效。 (207)")
[2025-05-20 01:47:43,338][sales_management.views.list():233] [ERROR] 错误类型: ProgrammingError
[2025-05-20 01:47:43,418][sales_management.views.list():234] [ERROR] 错误详情: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 87, in _execute
    return self.cursor.execute(sql)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sql_server\pyodbc\base.py", line 553, in execute
    return self.cursor.execute(sql, params)
pyodbc.ProgrammingError: ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FALLAMOUNT' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FALLAMOUNT' 无效。 (207)")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\HL_python\django-vue3-admin-master\backend\sales_management\views.py", line 198, in list
    cursor.execute(material_category_query)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 87, in _execute
    return self.cursor.execute(sql)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\sql_server\pyodbc\base.py", line 553, in execute
    return self.cursor.execute(sql, params)
django.db.utils.ProgrammingError: ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FALLAMOUNT' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FALLAMOUNT' 无效。 (207)")

[2025-05-20 08:53:07,720][sales_management.views.price_analysis():691] [ERROR] 历年同月价格查询失败，已达到最大重试次数: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]表 'T_SAL_ORDER' 的索引 'IX_SYS_WORKFLOW_F_DOCUMENTSTATUS' (在 FROM 子句中指定)不存在。 (308) (SQLExecDirectW)")
[2025-05-20 08:53:07,720][sales_management.views.price_analysis():773] [ERROR] 查询价格数据失败: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]表 'T_SAL_ORDER' 的索引 'IX_SYS_WORKFLOW_F_DOCUMENTSTATUS' (在 FROM 子句中指定)不存在。 (308) (SQLExecDirectW)")
[2025-05-20 09:03:57,949][sales_management.views.price_analysis():691] [ERROR] 历年同月价格查询失败，已达到最大重试次数: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]表 'T_SAL_ORDER' 的索引 'IX_SYS_WORKFLOW_F_DOCUMENTSTATUS' (在 FROM 子句中指定)不存在。 (308) (SQLExecDirectW)")
[2025-05-20 09:03:57,951][sales_management.views.price_analysis():773] [ERROR] 查询价格数据失败: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]表 'T_SAL_ORDER' 的索引 'IX_SYS_WORKFLOW_F_DOCUMENTSTATUS' (在 FROM 子句中指定)不存在。 (308) (SQLExecDirectW)")
[2025-05-20 09:04:20,071][sales_management.views.price_analysis():691] [ERROR] 历年同月价格查询失败，已达到最大重试次数: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]表 'T_SAL_ORDER' 的索引 'IX_SYS_WORKFLOW_F_DOCUMENTSTATUS' (在 FROM 子句中指定)不存在。 (308) (SQLExecDirectW)")
[2025-05-20 09:04:20,099][sales_management.views.price_analysis():773] [ERROR] 查询价格数据失败: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]表 'T_SAL_ORDER' 的索引 'IX_SYS_WORKFLOW_F_DOCUMENTSTATUS' (在 FROM 子句中指定)不存在。 (308) (SQLExecDirectW)")
[2025-05-20 11:17:41,282][sales_management.views.price_analysis():798] [ERROR] 获取价格分析数据失败: local variable 'selected_month' referenced before assignment
[2025-05-20 11:18:19,421][sales_management.views.price_analysis():798] [ERROR] 获取价格分析数据失败: local variable 'selected_month' referenced before assignment
[2025-05-20 11:31:06,008][sales_management.views.price_analysis():700] [ERROR] 查询价格数据失败: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]表 'T_SAL_ORDER' 的索引 'IX_T_SAL_ORDER_FDATE' (在 FROM 子句中指定)不存在。 (308) (SQLExecDirectW)")
[2025-05-21 10:04:38,713][home.views.get():47] [ERROR] SSO登录参数不完整
[2025-05-21 10:04:39,026][django.request.log_response():241] [ERROR] Internal Server Error: /api/home/<USER>/login
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\response.py", line 74, in rendered_content
    ret = renderer.render(self.data, accepted_media_type, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\renderers.py", line 728, in render
    ret = template.render(context, request=renderer_context['request'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 132, in render
    compiled_parent = self.get_parent(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 129, in get_parent
    return self.find_template(parent, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 107, in find_template
    template, origin = context.template.engine.find_template(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\library.py", line 130, in compile_func
    args, kwargs = parse_bits(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\library.py", line 362, in parse_bits
    raise TemplateSyntaxError(
django.template.exceptions.TemplateSyntaxError: 'optional_logout' did not receive value(s) for the argument(s): 'csrf_token'
[2025-05-21 10:04:39,037][django.server.log_message():212] [ERROR] "GET /api/home/<USER>/login?username=&timestamp=1747793077&nonce=4612e2c47fa643518cbd18fa8bd471a5&token=5581edab80e87690d1eb78f84d9c45592482fd8f0a676f54916502e97041652c&data=NIPNu5%2B20cHsBvXFiC8RJhx9Q5dJXHgUYDDV1vqHqEHV2Y%2FMQp0DGwGBSy6oWQaJ3ULCav0ilzMRPd%2B6G3ZRII9h4Aei7rGgDIsC9gmGAwXK3j9yd8oWXsvHXLqvG9PW HTTP/1.1" **********
[2025-05-21 10:05:26,542][django.request.log_response():241] [ERROR] Internal Server Error: /api/home/<USER>/login
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\response.py", line 74, in rendered_content
    ret = renderer.render(self.data, accepted_media_type, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\renderers.py", line 728, in render
    ret = template.render(context, request=renderer_context['request'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 132, in render
    compiled_parent = self.get_parent(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 129, in get_parent
    return self.find_template(parent, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 107, in find_template
    template, origin = context.template.engine.find_template(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\library.py", line 130, in compile_func
    args, kwargs = parse_bits(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\library.py", line 362, in parse_bits
    raise TemplateSyntaxError(
django.template.exceptions.TemplateSyntaxError: 'optional_logout' did not receive value(s) for the argument(s): 'csrf_token'
[2025-05-21 10:05:26,548][django.server.log_message():212] [ERROR] "GET /api/home/<USER>/login?username=*********&timestamp=1747793125&nonce=4ad10ffe069f4868bcc1575d60df8196&token=4ba6094853ec5aac029063bec597dbd5186ed624da3aeb7d62ebd48861f20a63&data=YK6VCYdJ8lndYWuWBaaAsRhX8qtBOZnTZ3eyefVJ5qk%2BjpcLzHBxLqaaSRGp8sgNKiAS9%2F3cikNUVhFCRo8GiTapAw2y89wBZJtpIyLG6ekU9RSUQ7JCJhO3stPNdlU9E4Th10XYMwHucJgfNGgYew%3D%3D HTTP/1.1" **********
[2025-05-21 10:05:37,322][home.views.get():47] [ERROR] SSO登录参数不完整
[2025-05-21 10:05:37,372][django.request.log_response():241] [ERROR] Internal Server Error: /api/home/<USER>/login
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\response.py", line 74, in rendered_content
    ret = renderer.render(self.data, accepted_media_type, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\renderers.py", line 728, in render
    ret = template.render(context, request=renderer_context['request'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 132, in render
    compiled_parent = self.get_parent(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 129, in get_parent
    return self.find_template(parent, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 107, in find_template
    template, origin = context.template.engine.find_template(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\library.py", line 130, in compile_func
    args, kwargs = parse_bits(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\library.py", line 362, in parse_bits
    raise TemplateSyntaxError(
django.template.exceptions.TemplateSyntaxError: 'optional_logout' did not receive value(s) for the argument(s): 'csrf_token'
[2025-05-21 10:05:37,378][django.server.log_message():212] [ERROR] "GET /api/home/<USER>/login?username=&timestamp=1747793136&nonce=8bd51061132943b58e5408566b207fde&token=29ab136a7b1b85cb7957bc45f6bbdc62c4eea53999bb00ce8115f505c2d444fc&data=EKw320FJbok%2Fr5t8K0ZK8nUE%2FFcBw7dZwZV2H7uCASbe1tODWUvurDwCvcGwsKZYkOOm5y2FwBbRfT3qfHsLvJUbIH%2Fo%2Bw3J%2FKSd7eqTXSKCO0rj33INWe9noHyOvm15 HTTP/1.1" **********
[2025-05-21 10:06:07,266][home.views.get():47] [ERROR] SSO登录参数不完整
[2025-05-21 10:06:07,317][django.request.log_response():241] [ERROR] Internal Server Error: /api/home/<USER>/login
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\response.py", line 74, in rendered_content
    ret = renderer.render(self.data, accepted_media_type, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\renderers.py", line 728, in render
    ret = template.render(context, request=renderer_context['request'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 132, in render
    compiled_parent = self.get_parent(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 129, in get_parent
    return self.find_template(parent, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 107, in find_template
    template, origin = context.template.engine.find_template(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\library.py", line 130, in compile_func
    args, kwargs = parse_bits(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\library.py", line 362, in parse_bits
    raise TemplateSyntaxError(
django.template.exceptions.TemplateSyntaxError: 'optional_logout' did not receive value(s) for the argument(s): 'csrf_token'
[2025-05-21 10:06:07,322][django.server.log_message():212] [ERROR] "GET /api/home/<USER>/login?username=&timestamp=1747793166&nonce=c199b76abdf84e02ab7ff10cb1b039ba&token=b9c23c01b0502ee29d57203971f69fb105a146e29b6d526a666ae9acc7c2f7f1&data=7tgVIBbjheeJCvR9RQklSrAPTWgigTLvFcpNIv9%2BudV8G9ctiPc4qX7NoD12Xe7ltT0naw3Whe8ub7hCL7I7q%2FipdbLeSeYpasWqoBX130UfoRkIsqbGaZL9vQWQKR6h HTTP/1.1" **********
[2025-05-21 10:08:56,812][django.request.log_response():241] [ERROR] Internal Server Error: /api/home/<USER>/login/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\response.py", line 74, in rendered_content
    ret = renderer.render(self.data, accepted_media_type, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\renderers.py", line 728, in render
    ret = template.render(context, request=renderer_context['request'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 132, in render
    compiled_parent = self.get_parent(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 129, in get_parent
    return self.find_template(parent, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 107, in find_template
    template, origin = context.template.engine.find_template(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\library.py", line 130, in compile_func
    args, kwargs = parse_bits(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\library.py", line 362, in parse_bits
    raise TemplateSyntaxError(
django.template.exceptions.TemplateSyntaxError: 'optional_logout' did not receive value(s) for the argument(s): 'csrf_token'
[2025-05-21 10:08:56,822][django.server.log_message():212] [ERROR] "GET /api/home/<USER>/login/?username=*********&timestamp=1747793336&nonce=138240bd4857473c856f2944fe048194&token=31a78a5fcd836d618e9a296d10d55a86c2d4deabf3fdc63db29c3a92c5be2b0e&data=mYssnDlr6EyKfKYBP5Qnyhwkvkzs%2Fxt1yxv2GysBtg%2FcrExmY2yNzInCsq6hXF7Ohrodex4iFMv6toV5LAzhrzaObnn%2Bhls3exOBNLY4b5bbKNTlaeKfBEPHVvX%2BMBzY%2BbZ8cd2dgacTulkRqMc8Ww%3D%3D HTTP/1.1" **********
[2025-05-21 10:12:57,909][django.request.log_response():241] [ERROR] Internal Server Error: /web/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\application\urls.py", line 64, in web_view
    return render(request, 'web/index.html')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: web/index.html
[2025-05-21 10:12:57,911][django.server.log_message():212] [ERROR] "GET /web/ HTTP/1.1" 500 113592
[2025-05-21 10:14:30,210][django.request.log_response():241] [ERROR] Internal Server Error: /web/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\application\urls.py", line 64, in web_view
    return render(request, 'web/index.html')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: web/index.html
[2025-05-21 10:14:30,213][django.server.log_message():212] [ERROR] "GET /web/ HTTP/1.1" 500 113600
[2025-05-21 10:14:47,809][django.request.log_response():241] [ERROR] Internal Server Error: /web/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\application\urls.py", line 64, in web_view
    return render(request, 'web/index.html')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: web/index.html
[2025-05-21 10:14:47,813][django.server.log_message():212] [ERROR] "GET /web/ HTTP/1.1" 500 113596
[2025-05-21 10:15:42,340][django.request.log_response():241] [ERROR] Internal Server Error: /web/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\application\urls.py", line 64, in web_view
    return render(request, 'web/index.html')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: web/index.html
[2025-05-21 10:15:42,348][django.server.log_message():212] [ERROR] "GET /web/ HTTP/1.1" 500 113751
[2025-05-21 10:26:01,444][home.views.get():71] [ERROR] 非法的请求来源: 
[2025-05-21 11:26:00,322][home.views.get():76] [ERROR] SSO登录参数不完整
[2025-05-26 11:33:14,877][sales_management.views.get_organizations():923] [ERROR] 获取销售组织列表失败: The connection 'kingdee' doesn't exist.
[2025-05-26 11:33:14,887][django.request.log_response():241] [ERROR] Internal Server Error: /api/sales_record/get_organizations/
[2025-05-26 11:33:14,888][django.server.log_message():212] [ERROR] "GET /api/sales_record/get_organizations/ HTTP/1.1" 500 102
[2025-05-26 11:33:50,348][sales_management.views.grade_proportion():891] [ERROR] 等级占比查询失败: The connection 'kingdee' doesn't exist.
[2025-05-26 11:33:50,381][sales_management.views.grade_proportion():892] [ERROR] Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\asgiref\local.py", line 89, in _lock_storage
    asyncio.get_running_loop()
RuntimeError: no running event loop

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\utils\connection.py", line 58, in __getitem__
    return getattr(self._connections, alias)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\asgiref\local.py", line 118, in __getattr__
    return getattr(storage, key)
AttributeError: '_thread._local' object has no attribute 'kingdee'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\HL_python\django-vue3-admin-master\backend\sales_management\views.py", line 862, in grade_proportion
    cursor = connections['kingdee'].cursor()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\utils\connection.py", line 61, in __getitem__
    raise self.exception_class(f"The connection '{alias}' doesn't exist.")
django.utils.connection.ConnectionDoesNotExist: The connection 'kingdee' doesn't exist.

[2025-05-26 11:33:50,383][django.request.log_response():241] [ERROR] Internal Server Error: /api/sales_record/grade_proportion/
[2025-05-26 11:33:50,384][django.server.log_message():212] [ERROR] "GET /api/sales_record/grade_proportion/?sale_date=2025-05-19&org_name= HTTP/1.1" 500 96
[2025-05-26 11:34:16,456][sales_management.views.get_organizations():923] [ERROR] 获取销售组织列表失败: The connection 'kingdee' doesn't exist.
[2025-05-26 11:34:16,458][django.request.log_response():241] [ERROR] Internal Server Error: /api/sales_record/get_organizations/
[2025-05-26 11:34:16,459][django.server.log_message():212] [ERROR] "GET /api/sales_record/get_organizations/ HTTP/1.1" 500 102
[2025-05-27 17:00:43,595][home.views.get():76] [ERROR] SSO登录参数不完整
[2025-05-28 08:41:56,560][sales_management.views.debt_inquiry():1320] [ERROR] 客户欠款查询失败: 'CustomerDebtViewSet' object has no attribute '_get_customer_detail'
[2025-05-28 08:43:16,970][sales_management.views.debt_inquiry():1320] [ERROR] 客户欠款查询失败: 'CustomerDebtViewSet' object has no attribute 'get_customer_detail'
[2025-05-28 08:43:23,203][sales_management.views.debt_inquiry():1320] [ERROR] 客户欠款查询失败: 'CustomerDebtViewSet' object has no attribute 'get_customer_detail'
[2025-05-28 08:43:27,376][sales_management.views.get_customer_detail():1469] [ERROR] 获取客户明细失败: ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FSETTLETYPEID' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FSETTLETYPEID' 无效。 (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FSETTLETYPEID' 无效。 (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FNOTE' 无效。 (207)")
[2025-05-28 08:44:06,750][sales_management.views.get_customer_detail():1469] [ERROR] 获取客户明细失败: ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FSETTLETYPEID' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FSETTLETYPEID' 无效。 (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FSETTLETYPEID' 无效。 (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FNOTE' 无效。 (207)")
[2025-05-28 08:45:19,291][sales_management.views.get_customer_detail():1469] [ERROR] 获取客户明细失败: ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FSETTLETYPEID' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FSETTLETYPEID' 无效。 (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FSETTLETYPEID' 无效。 (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FNOTE' 无效。 (207)")
[2025-05-28 09:40:59,350][sales_management.views.get_customer_detail():1509] [ERROR] 获取客户明细失败: ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FNOTE' 无效。 (207) (SQLExecDirectW)")
[2025-05-28 09:41:01,875][sales_management.views.payment_inquiry():1737] [ERROR] 回款查询失败: ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FNOTE' 无效。 (207) (SQLExecDirectW)")
[2025-05-28 09:41:18,505][sales_management.views.payment_inquiry():1737] [ERROR] 回款查询失败: ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FNOTE' 无效。 (207) (SQLExecDirectW)")
[2025-05-28 09:41:19,741][sales_management.views.payment_inquiry():1737] [ERROR] 回款查询失败: ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FNOTE' 无效。 (207) (SQLExecDirectW)")
[2025-05-28 09:43:10,419][sales_management.views.get_customer_detail():1506] [ERROR] 获取客户明细失败: ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FSETTLETYPEID' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FSETTLETYPEID' 无效。 (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FSETTLETYPEID' 无效。 (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FNOTE' 无效。 (207)")
[2025-05-28 09:59:15,836][sales_management.views.get_customer_detail():1506] [ERROR] 获取客户明细失败: ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FSETTLETYPEID' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FSETTLETYPEID' 无效。 (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FSETTLETYPEID' 无效。 (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FNOTE' 无效。 (207)")
[2025-05-28 10:01:01,373][sales_management.views.get_customer_detail():1506] [ERROR] 获取客户明细失败: ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FSETTLETYPEID' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FSETTLETYPEID' 无效。 (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FSETTLETYPEID' 无效。 (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FNOTE' 无效。 (207)")
[2025-05-28 10:04:56,789][sales_management.views.get_customer_detail():1512] [ERROR] 获取客户明细失败: ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FSETTLETYPEID' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FSETTLETYPEID' 无效。 (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FSETTLETYPEID' 无效。 (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FNOTE' 无效。 (207)")
[2025-05-28 10:06:04,231][sales_management.views.get_customer_detail():1512] [ERROR] 获取客户明细失败: ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FSETTLETYPEID' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FSETTLETYPEID' 无效。 (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FSETTLETYPEID' 无效。 (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FNOTE' 无效。 (207)")
[2025-05-28 10:06:06,314][sales_management.views.get_customer_detail():1512] [ERROR] 获取客户明细失败: ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FSETTLETYPEID' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FSETTLETYPEID' 无效。 (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FSETTLETYPEID' 无效。 (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FNOTE' 无效。 (207)")
[2025-06-18 09:39:31,408][customer_analysis.views.customer_trend_analysis():428] [ERROR] 客户趋势分析失败: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]'FORMAT' 不是可以识别的 内置函数名称。 (195) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]关键字 'with' 附近有语法错误。如果此语句是公用表表达式、xmlnamespaces 子句或者更改跟踪上下文子句，那么前一个语句必须以分号结尾。 (319); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]无法预定义语句。 (8180)")
[2025-06-18 09:39:31,409][django.request.log_response():241] [ERROR] Internal Server Error: /api/customer_analysis/customer_trend_analysis/
[2025-06-18 09:39:31,410][django.server.log_message():212] [ERROR] "GET /api/customer_analysis/customer_trend_analysis/?days=90&group_by=week HTTP/1.1" 500 555
[2025-06-25 14:41:24,219][dvadmin.utils.exception.CustomExceptionHandler():68] [ERROR] Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\fields\__init__.py", line 2053, in get_prep_value
    return int(value)
ValueError: invalid literal for int() with base 10: 'admin'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\system\views\menu.py", line 115, in list
    data = serializer.data
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\serializers.py", line 795, in data
    ret = super().data
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\serializers.py", line 249, in data
    self._data = self.to_representation(self.instance)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\serializers.py", line 713, in to_representation
    return [
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\serializers.py", line 714, in <listcomp>
    self.child.to_representation(item) for item in iterable
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django_restql\mixins.py", line 150, in to_representation
    return super().to_representation(instance)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\serializers.py", line 538, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\fields.py", line 1870, in to_representation
    return method(value)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\serializers.py", line 34, in get_modifier_name
    Users.objects.filter(id=instance.modifier)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1546, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1492, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1319, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\lookups.py", line 27, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\lookups.py", line 341, in get_prep_lookup
    return super().get_prep_lookup()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\lookups.py", line 85, in get_prep_lookup
    return self.lhs.output_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\fields\__init__.py", line 2055, in get_prep_value
    raise e.__class__(
ValueError: Field 'id' expected a number but got 'admin'.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\fields\__init__.py", line 2053, in get_prep_value
    return int(value)
ValueError: invalid literal for int() with base 10: 'admin'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\system\views\menu.py", line 115, in list
    data = serializer.data
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\serializers.py", line 795, in data
    ret = super().data
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\serializers.py", line 249, in data
    self._data = self.to_representation(self.instance)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\serializers.py", line 713, in to_representation
    return [
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\serializers.py", line 714, in <listcomp>
    self.child.to_representation(item) for item in iterable
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django_restql\mixins.py", line 150, in to_representation
    return super().to_representation(instance)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\serializers.py", line 538, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\fields.py", line 1870, in to_representation
    return method(value)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\serializers.py", line 34, in get_modifier_name
    Users.objects.filter(id=instance.modifier)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1546, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1492, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1319, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\lookups.py", line 27, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\lookups.py", line 341, in get_prep_lookup
    return super().get_prep_lookup()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\lookups.py", line 85, in get_prep_lookup
    return self.lhs.output_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\fields\__init__.py", line 2055, in get_prep_value
    raise e.__class__(
ValueError: Field 'id' expected a number but got 'admin'.
[2025-06-25 14:41:35,341][dvadmin.utils.exception.CustomExceptionHandler():68] [ERROR] Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\fields\__init__.py", line 2053, in get_prep_value
    return int(value)
ValueError: invalid literal for int() with base 10: 'admin'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\system\views\menu.py", line 115, in list
    data = serializer.data
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\serializers.py", line 795, in data
    ret = super().data
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\serializers.py", line 249, in data
    self._data = self.to_representation(self.instance)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\serializers.py", line 713, in to_representation
    return [
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\serializers.py", line 714, in <listcomp>
    self.child.to_representation(item) for item in iterable
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django_restql\mixins.py", line 150, in to_representation
    return super().to_representation(instance)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\serializers.py", line 538, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\fields.py", line 1870, in to_representation
    return method(value)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\serializers.py", line 34, in get_modifier_name
    Users.objects.filter(id=instance.modifier)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1546, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1492, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1319, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\lookups.py", line 27, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\lookups.py", line 341, in get_prep_lookup
    return super().get_prep_lookup()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\lookups.py", line 85, in get_prep_lookup
    return self.lhs.output_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\fields\__init__.py", line 2055, in get_prep_value
    raise e.__class__(
ValueError: Field 'id' expected a number but got 'admin'.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\fields\__init__.py", line 2053, in get_prep_value
    return int(value)
ValueError: invalid literal for int() with base 10: 'admin'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\system\views\menu.py", line 115, in list
    data = serializer.data
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\serializers.py", line 795, in data
    ret = super().data
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\serializers.py", line 249, in data
    self._data = self.to_representation(self.instance)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\serializers.py", line 713, in to_representation
    return [
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\serializers.py", line 714, in <listcomp>
    self.child.to_representation(item) for item in iterable
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django_restql\mixins.py", line 150, in to_representation
    return super().to_representation(instance)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\serializers.py", line 538, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\fields.py", line 1870, in to_representation
    return method(value)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\serializers.py", line 34, in get_modifier_name
    Users.objects.filter(id=instance.modifier)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1546, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1492, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1319, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\lookups.py", line 27, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\lookups.py", line 341, in get_prep_lookup
    return super().get_prep_lookup()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\lookups.py", line 85, in get_prep_lookup
    return self.lhs.output_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\fields\__init__.py", line 2055, in get_prep_value
    raise e.__class__(
ValueError: Field 'id' expected a number but got 'admin'.
[2025-06-25 14:53:10,072][django.request.log_response():241] [ERROR] Internal Server Error: /api/sales_analysis/price_analysis/
[2025-06-25 14:53:10,073][django.server.log_message():212] [ERROR] "GET /api/sales_analysis/price_analysis/?org_name=%E6%B1%9F%E8%8B%8F%E5%8D%8E%E7%BB%BF%E7%94%9F%E7%89%A9%E7%A7%91%E6%8A%80%E9%9B%86%E5%9B%A2%E8%82%A1%E4%BB%BD%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8&material_code=7001010064&start_date=yyyy-02-Th&end_date=yyyy-03-Tu HTTP/1.1" 500 213
[2025-06-25 15:00:03,565][django.request.log_response():241] [ERROR] Internal Server Error: /api/sales_analysis/price_analysis/
[2025-06-25 15:00:03,567][django.server.log_message():212] [ERROR] "GET /api/sales_analysis/price_analysis/?start_date=2025-06-01&end_date=2025-06-30&analysis_type=week HTTP/1.1" 500 482
[2025-06-25 15:00:52,443][django.request.log_response():241] [ERROR] Internal Server Error: /api/sales_analysis/price_analysis/
[2025-06-25 15:00:52,444][django.server.log_message():212] [ERROR] "GET /api/sales_analysis/price_analysis/?start_date=2025-06-01&end_date=2025-06-30&analysis_type=week HTTP/1.1" 500 368
[2025-06-25 15:01:55,324][django.request.log_response():241] [ERROR] Internal Server Error: /api/sales_analysis/price_analysis/
[2025-06-25 15:01:55,325][django.server.log_message():212] [ERROR] "GET /api/sales_analysis/price_analysis/?start_date=2025-06-01&end_date=2025-06-30&analysis_type=week HTTP/1.1" 500 978
[2025-06-25 15:09:44,464][customer_analysis.views.customer_trend_analysis():465] [ERROR] 客户趋势分析失败: invalid literal for int() with base 10: ''
[2025-06-25 15:09:44,466][django.request.log_response():241] [ERROR] Internal Server Error: /api/customer_analysis/customer_trend_analysis/
[2025-06-25 15:09:44,467][django.server.log_message():212] [ERROR] "GET /api/customer_analysis/customer_trend_analysis/?org_name=%E5%B9%BF%E8%A5%BF%E5%8D%8E%E7%BB%BF%E7%94%9F%E7%89%A9%E7%A7%91%E6%8A%80%E6%9C%89%E9%99%90%E8%B4%A3%E4%BB%BB%E5%85%AC%E5%8F%B8&customer_name=%E6%98%8C%E5%8D%87%EF%BC%88%E5%B9%BF%E5%B7%9E%EF%BC%89%E5%86%9C%E4%B8%9A%E7%A7%91%E6%8A%80%E5%8F%91%E5%B1%95%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8&days=&group_by=week&start_date=yyyy-05-Th&end_date=yyyy-05-Sa HTTP/1.1" 500 99
[2025-06-25 16:36:34,957][sales_management.views.search_materials():1409] [ERROR] 物料搜索失败: not enough arguments for format string
[2025-06-25 16:36:36,452][sales_management.views.search_materials():1409] [ERROR] 物料搜索失败: not enough arguments for format string
[2025-06-25 17:04:49,958][sales_management.views.material_price_analysis():1609] [ERROR] 物料价格分析失败: ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FQTY' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FQTY' 无效。 (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FQTY' 无效。 (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FQTY' 无效。 (207)")
[2025-06-26 11:08:29,481][sales_management.views.search_materials():1433] [ERROR] 物料搜索失败: ('42000', '[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]如果指定了 SELECT DISTINCT，那么 ORDER BY 子句中的项就必须出现在选择列表中。 (145) (SQLExecDirectW)')
[2025-06-26 11:08:30,456][sales_management.views.search_materials():1433] [ERROR] 物料搜索失败: ('42000', '[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]如果指定了 SELECT DISTINCT，那么 ORDER BY 子句中的项就必须出现在选择列表中。 (145) (SQLExecDirectW)')
[2025-06-26 11:08:31,615][sales_management.views.search_materials():1433] [ERROR] 物料搜索失败: ('42000', '[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]如果指定了 SELECT DISTINCT，那么 ORDER BY 子句中的项就必须出现在选择列表中。 (145) (SQLExecDirectW)')
[2025-06-26 11:08:33,746][sales_management.views.search_materials():1433] [ERROR] 物料搜索失败: ('42000', '[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]如果指定了 SELECT DISTINCT，那么 ORDER BY 子句中的项就必须出现在选择列表中。 (145) (SQLExecDirectW)')
[2025-06-26 11:08:35,864][sales_management.views.search_materials():1433] [ERROR] 物料搜索失败: ('42000', '[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]如果指定了 SELECT DISTINCT，那么 ORDER BY 子句中的项就必须出现在选择列表中。 (145) (SQLExecDirectW)')
[2025-06-26 11:08:37,966][sales_management.views.search_materials():1433] [ERROR] 物料搜索失败: ('42000', '[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]如果指定了 SELECT DISTINCT，那么 ORDER BY 子句中的项就必须出现在选择列表中。 (145) (SQLExecDirectW)')
[2025-06-30 11:16:04,406][customer_analysis.tasks.generate_customer_trend_report():183] [ERROR] 生成客户趋势分析报表失败: ('28000', '[28000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]用户 \'hldbuser\' 登录失败。 (18456) (SQLDriverConnect); [28000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]无法打开登录所请求的数据库 "AIS20181017553373"。登录失败。 (4060); [28000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]用户 \'hldbuser\' 登录失败。 (18456); [28000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]无法打开登录所请求的数据库 "AIS20181017553373"。登录失败。 (4060)')
[2025-06-30 11:17:43,848][customer_analysis.tasks.generate_customer_trend_report():182] [ERROR] 生成客户趋势分析报表失败: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]'LAG' 不是可以识别的 内置函数名称。 (195) (SQLExecDirectW)")
[2025-06-30 11:18:41,424][customer_analysis.tasks.generate_customer_trend_report():222] [ERROR] 生成客户趋势分析报表失败: ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FCUSTOMERID' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FCUSTOMERID' 无效。 (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FALLAMOUNT' 无效。 (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FCUSTOMERID' 无效。 (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FCUSTOMERID' 无效。 (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FALLAMOUNT' 无效。 (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FCUSTOMERID' 无效。 (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FCUSTOMERID' 无效。 (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FALLAMOUNT' 无效。 (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FCUSTOMERID' 无效。 (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FCUSTOMERID' 无效。 (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FALLAMOUNT' 无效。 (207)")
[2025-06-30 11:20:20,100][customer_analysis.tasks.generate_customer_trend_report():244] [ERROR] 生成客户趋势分析报表失败: 'MergedCell' object has no attribute 'column_letter'
[2025-06-30 11:21:12,518][customer_analysis.tasks.generate_customer_trend_report():245] [ERROR] 生成客户趋势分析报表失败: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmprilvb74x.xlsx'
[2025-06-30 11:22:30,755][customer_analysis.tasks.generate_customer_price_analysis_report():416] [ERROR] 生成客户价格分析报表失败: ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FCUSTOMERID' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FCUSTOMERID' 无效。 (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FALLAMOUNT' 无效。 (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FPRICE' 无效。 (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FALLAMOUNT' 无效。 (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FPRICE' 无效。 (207)")
[2025-06-30 11:23:14,065][customer_analysis.tasks.generate_customer_price_analysis_report():423] [ERROR] 生成客户价格分析报表失败: ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FPRICE' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FPRICE' 无效。 (207)")
[2025-06-30 11:34:44,727][dvadmin.utils.exception.CustomExceptionHandler():68] [ERROR] Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\fields\__init__.py", line 2053, in get_prep_value
    return int(value)
ValueError: invalid literal for int() with base 10: '每月1号生成客户销售趋势下降分析报表'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\dvadmin3_celery\views\task.py", line 120, in destroy
    CrontabSchedule.objects.filter(id=instance.description).delete()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1546, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1492, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1319, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\lookups.py", line 27, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\lookups.py", line 341, in get_prep_lookup
    return super().get_prep_lookup()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\lookups.py", line 85, in get_prep_lookup
    return self.lhs.output_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\fields\__init__.py", line 2055, in get_prep_value
    raise e.__class__(
ValueError: Field 'id' expected a number but got '每月1号生成客户销售趋势下降分析报表'.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\fields\__init__.py", line 2053, in get_prep_value
    return int(value)
ValueError: invalid literal for int() with base 10: '每月1号生成客户销售趋势下降分析报表'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\dvadmin3_celery\views\task.py", line 120, in destroy
    CrontabSchedule.objects.filter(id=instance.description).delete()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1546, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1492, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1319, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\lookups.py", line 27, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\lookups.py", line 341, in get_prep_lookup
    return super().get_prep_lookup()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\lookups.py", line 85, in get_prep_lookup
    return self.lhs.output_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\fields\__init__.py", line 2055, in get_prep_value
    raise e.__class__(
ValueError: Field 'id' expected a number but got '每月1号生成客户销售趋势下降分析报表'.
[2025-06-30 12:29:27,389][customer_analysis.views.generate_report():565] [ERROR] 生成报表失败: Error 10061 connecting to 127.0.0.1:6379. 由于目标计算机积极拒绝，无法连接。.
[2025-06-30 12:29:27,392][django.request.log_response():241] [ERROR] Internal Server Error: /api/customer_analysis/generate_report/
[2025-06-30 12:29:27,392][django.server.log_message():212] [ERROR] "POST /api/customer_analysis/generate_report/ HTTP/1.1" 500 145
[2025-07-03 08:53:59,566][dvadmin.utils.exception.CustomExceptionHandler():68] [ERROR] Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\utils\functional.py", line 32, in __call__
    return self.__value__
AttributeError: 'ChannelPromise' object has no attribute '__value__'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 951, in create_channel
    return self._avail_channels.pop()
IndexError: pop from empty list

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\redis\connection.py", line 385, in connect_check_health
    sock = self.retry.call_with_retry(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\redis\retry.py", line 87, in call_with_retry
    return do()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\redis\connection.py", line 386, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\redis\connection.py", line 797, in _connect
    raise err
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\redis\connection.py", line 781, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\connection.py", line 472, in _reraise_as_library_errors
    yield
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\connection.py", line 459, in _ensure_connection
    return retry_over_time(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\utils\functional.py", line 318, in retry_over_time
    return fun(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\connection.py", line 938, in _connection_factory
    self._connection = self._establish_connection()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\connection.py", line 860, in _establish_connection
    conn = self.transport.establish_connection()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 975, in establish_connection
    self._avail_channels.append(self.create_channel(self))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 953, in create_channel
    channel = self.Channel(connection)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 744, in __init__
    self.client.ping()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\redis\commands\core.py", line 1219, in ping
    return self.execute_command("PING", **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\redis\client.py", line 622, in execute_command
    return self._execute_command(*args, **options)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\redis\client.py", line 628, in _execute_command
    conn = self.connection or pool.get_connection()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\redis\utils.py", line 188, in wrapper
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\redis\connection.py", line 1522, in get_connection
    connection.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\redis\connection.py", line 379, in connect
    self.connect_check_health(check_health=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\redis\connection.py", line 391, in connect_check_health
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 10061 connecting to 127.0.0.1:6379. 由于目标计算机积极拒绝，无法连接。.

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\dvadmin3_celery\views\task.py", line 139, in run_task
    current_app.send_task(instance.task, kwargs=task_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\app\base.py", line 922, in send_task
    amqp.send_task_message(P, name, message, **options)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\app\amqp.py", line 523, in send_task_message
    ret = producer.publish(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\messaging.py", line 190, in publish
    return _publish(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\connection.py", line 556, in _ensured
    return fun(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\messaging.py", line 200, in _publish
    channel = self.channel
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\messaging.py", line 224, in _get_channel
    channel = self._channel = channel()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\utils\functional.py", line 34, in __call__
    value = self.__value__ = self.__contract__()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\messaging.py", line 240, in <lambda>
    channel = ChannelPromise(lambda: connection.default_channel)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\connection.py", line 957, in default_channel
    self._ensure_connection(**conn_opts)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\connection.py", line 458, in _ensure_connection
    with ctx():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\connection.py", line 476, in _reraise_as_library_errors
    raise ConnectionError(str(exc)) from exc
kombu.exceptions.OperationalError: Error 10061 connecting to 127.0.0.1:6379. 由于目标计算机积极拒绝，无法连接。.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\utils\functional.py", line 32, in __call__
    return self.__value__
AttributeError: 'ChannelPromise' object has no attribute '__value__'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 951, in create_channel
    return self._avail_channels.pop()
IndexError: pop from empty list

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\redis\connection.py", line 385, in connect_check_health
    sock = self.retry.call_with_retry(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\redis\retry.py", line 87, in call_with_retry
    return do()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\redis\connection.py", line 386, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\redis\connection.py", line 797, in _connect
    raise err
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\redis\connection.py", line 781, in _connect
    sock.connect(socket_address)
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\connection.py", line 472, in _reraise_as_library_errors
    yield
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\connection.py", line 459, in _ensure_connection
    return retry_over_time(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\utils\functional.py", line 318, in retry_over_time
    return fun(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\connection.py", line 938, in _connection_factory
    self._connection = self._establish_connection()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\connection.py", line 860, in _establish_connection
    conn = self.transport.establish_connection()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 975, in establish_connection
    self._avail_channels.append(self.create_channel(self))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 953, in create_channel
    channel = self.Channel(connection)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 744, in __init__
    self.client.ping()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\redis\commands\core.py", line 1219, in ping
    return self.execute_command("PING", **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\redis\client.py", line 622, in execute_command
    return self._execute_command(*args, **options)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\redis\client.py", line 628, in _execute_command
    conn = self.connection or pool.get_connection()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\redis\utils.py", line 188, in wrapper
    return func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\redis\connection.py", line 1522, in get_connection
    connection.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\redis\connection.py", line 379, in connect
    self.connect_check_health(check_health=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\redis\connection.py", line 391, in connect_check_health
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 10061 connecting to 127.0.0.1:6379. 由于目标计算机积极拒绝，无法连接。.

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\dvadmin3_celery\views\task.py", line 139, in run_task
    current_app.send_task(instance.task, kwargs=task_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\app\base.py", line 922, in send_task
    amqp.send_task_message(P, name, message, **options)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\app\amqp.py", line 523, in send_task_message
    ret = producer.publish(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\messaging.py", line 190, in publish
    return _publish(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\connection.py", line 556, in _ensured
    return fun(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\messaging.py", line 200, in _publish
    channel = self.channel
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\messaging.py", line 224, in _get_channel
    channel = self._channel = channel()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\utils\functional.py", line 34, in __call__
    value = self.__value__ = self.__contract__()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\messaging.py", line 240, in <lambda>
    channel = ChannelPromise(lambda: connection.default_channel)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\connection.py", line 957, in default_channel
    self._ensure_connection(**conn_opts)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\connection.py", line 458, in _ensure_connection
    with ctx():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\contextlib.py", line 153, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\connection.py", line 476, in _reraise_as_library_errors
    raise ConnectionError(str(exc)) from exc
kombu.exceptions.OperationalError: Error 10061 connecting to 127.0.0.1:6379. 由于目标计算机积极拒绝，无法连接。.
[2025-07-03 08:54:03,050][dvadmin.utils.exception.CustomExceptionHandler():68] [ERROR] Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\fields\__init__.py", line 2053, in get_prep_value
    return int(value)
ValueError: invalid literal for int() with base 10: '每周一生成客户销售趋势分析周报'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\dvadmin3_celery\views\task.py", line 120, in destroy
    CrontabSchedule.objects.filter(id=instance.description).delete()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1546, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1492, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1319, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\lookups.py", line 27, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\lookups.py", line 341, in get_prep_lookup
    return super().get_prep_lookup()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\lookups.py", line 85, in get_prep_lookup
    return self.lhs.output_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\fields\__init__.py", line 2055, in get_prep_value
    raise e.__class__(
ValueError: Field 'id' expected a number but got '每周一生成客户销售趋势分析周报'.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\fields\__init__.py", line 2053, in get_prep_value
    return int(value)
ValueError: invalid literal for int() with base 10: '每周一生成客户销售趋势分析周报'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\dvadmin3_celery\views\task.py", line 120, in destroy
    CrontabSchedule.objects.filter(id=instance.description).delete()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1546, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1492, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1319, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\lookups.py", line 27, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\lookups.py", line 341, in get_prep_lookup
    return super().get_prep_lookup()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\lookups.py", line 85, in get_prep_lookup
    return self.lhs.output_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\fields\__init__.py", line 2055, in get_prep_value
    raise e.__class__(
ValueError: Field 'id' expected a number but got '每周一生成客户销售趋势分析周报'.
[2025-07-03 08:54:08,071][dvadmin.utils.exception.CustomExceptionHandler():68] [ERROR] Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\fields\__init__.py", line 2053, in get_prep_value
    return int(value)
ValueError: invalid literal for int() with base 10: '每周一生成客户销售趋势分析周报'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\dvadmin3_celery\views\task.py", line 120, in destroy
    CrontabSchedule.objects.filter(id=instance.description).delete()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1546, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1492, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1319, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\lookups.py", line 27, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\lookups.py", line 341, in get_prep_lookup
    return super().get_prep_lookup()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\lookups.py", line 85, in get_prep_lookup
    return self.lhs.output_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\fields\__init__.py", line 2055, in get_prep_value
    raise e.__class__(
ValueError: Field 'id' expected a number but got '每周一生成客户销售趋势分析周报'.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\fields\__init__.py", line 2053, in get_prep_value
    return int(value)
ValueError: invalid literal for int() with base 10: '每周一生成客户销售趋势分析周报'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\dvadmin3_celery\views\task.py", line 120, in destroy
    CrontabSchedule.objects.filter(id=instance.description).delete()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1546, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1492, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1319, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\lookups.py", line 27, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\lookups.py", line 341, in get_prep_lookup
    return super().get_prep_lookup()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\lookups.py", line 85, in get_prep_lookup
    return self.lhs.output_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\fields\__init__.py", line 2055, in get_prep_value
    raise e.__class__(
ValueError: Field 'id' expected a number but got '每周一生成客户销售趋势分析周报'.
[2025-07-03 08:54:15,463][dvadmin.utils.exception.CustomExceptionHandler():68] [ERROR] Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\fields\__init__.py", line 2053, in get_prep_value
    return int(value)
ValueError: invalid literal for int() with base 10: '每月1号生成客户价格偏低分析报表'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\dvadmin3_celery\views\task.py", line 120, in destroy
    CrontabSchedule.objects.filter(id=instance.description).delete()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1546, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1492, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1319, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\lookups.py", line 27, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\lookups.py", line 341, in get_prep_lookup
    return super().get_prep_lookup()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\lookups.py", line 85, in get_prep_lookup
    return self.lhs.output_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\fields\__init__.py", line 2055, in get_prep_value
    raise e.__class__(
ValueError: Field 'id' expected a number but got '每月1号生成客户价格偏低分析报表'.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\fields\__init__.py", line 2053, in get_prep_value
    return int(value)
ValueError: invalid literal for int() with base 10: '每月1号生成客户价格偏低分析报表'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\dvadmin3_celery\views\task.py", line 120, in destroy
    CrontabSchedule.objects.filter(id=instance.description).delete()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1546, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1492, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\query.py", line 1319, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\lookups.py", line 27, in __init__
    self.rhs = self.get_prep_lookup()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\lookups.py", line 341, in get_prep_lookup
    return super().get_prep_lookup()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\lookups.py", line 85, in get_prep_lookup
    return self.lhs.output_field.get_prep_value(self.rhs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\fields\__init__.py", line 2055, in get_prep_value
    raise e.__class__(
ValueError: Field 'id' expected a number but got '每月1号生成客户价格偏低分析报表'.
[2025-07-03 11:43:09,883][django.request.log_response():241] [ERROR] Internal Server Error: /api/price_anomaly/records/statistics/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\response.py", line 74, in rendered_content
    ret = renderer.render(self.data, accepted_media_type, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\renderers.py", line 728, in render
    ret = template.render(context, request=renderer_context['request'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 132, in render
    compiled_parent = self.get_parent(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 129, in get_parent
    return self.find_template(parent, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 107, in find_template
    template, origin = context.template.engine.find_template(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\library.py", line 130, in compile_func
    args, kwargs = parse_bits(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\library.py", line 362, in parse_bits
    raise TemplateSyntaxError(
django.template.exceptions.TemplateSyntaxError: 'optional_logout' did not receive value(s) for the argument(s): 'csrf_token'
[2025-07-03 11:43:09,891][django.server.log_message():212] [ERROR] "GET /api/price_anomaly/records/statistics/ HTTP/1.1" **********
[2025-07-03 14:46:15,027][price_anomaly_analysis.services.identify_price_anomalies():65] [ERROR] 价格异常分析失败: PriceAnomalyRecord() got unexpected keyword arguments: 'company_name', 'specification', 'material_group', 'market_price_difference', 'market_price_difference_percent', 'company_price_difference', 'company_price_difference_percent', 'customer_level', 'customer_value', 'first_transaction_date', 'last_transaction_date', 'relationship_days', 'frequency_level', 'sales_rank_in_company', 'price_rank_in_company', 'sales_rank_in_market'
[2025-07-04 10:33:36,145][django.request.log_response():241] [ERROR] Internal Server Error: /api/price_anomaly_analysis/price_anomaly_record/statistics/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\response.py", line 74, in rendered_content
    ret = renderer.render(self.data, accepted_media_type, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\renderers.py", line 728, in render
    ret = template.render(context, request=renderer_context['request'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 132, in render
    compiled_parent = self.get_parent(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 129, in get_parent
    return self.find_template(parent, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 107, in find_template
    template, origin = context.template.engine.find_template(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\library.py", line 130, in compile_func
    args, kwargs = parse_bits(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\template\library.py", line 362, in parse_bits
    raise TemplateSyntaxError(
django.template.exceptions.TemplateSyntaxError: 'optional_logout' did not receive value(s) for the argument(s): 'csrf_token'
[2025-07-04 10:33:36,155][django.server.log_message():212] [ERROR] "GET /api/price_anomaly_analysis/price_anomaly_record/statistics/?analysis_month=2025-06 HTTP/1.1" **********
[2025-07-04 11:28:41,210][django.request.log_response():241] [ERROR] Internal Server Error: /api/sales_record/material_categories/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\viewsets.py", line 124, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 511, in dispatch
    self.response = self.finalize_response(request, response, *args, **kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\viewset.py", line 170, in finalize_response
    if hasattr(response.data, 'get') and response.data.get('results'):
AttributeError: 'JsonResponse' object has no attribute 'data'
[2025-07-04 11:28:41,223][django.server.log_message():212] [ERROR] "GET /api/sales_record/material_categories/ HTTP/1.1" 500 113508
[2025-07-04 11:29:45,126][django.request.log_response():241] [ERROR] Internal Server Error: /api/sales_record/material_categories/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\viewsets.py", line 124, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 511, in dispatch
    self.response = self.finalize_response(request, response, *args, **kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\viewset.py", line 170, in finalize_response
    if hasattr(response.data, 'get') and response.data.get('results'):
AttributeError: 'JsonResponse' object has no attribute 'data'
[2025-07-04 11:29:45,130][django.server.log_message():212] [ERROR] "GET /api/sales_record/material_categories/ HTTP/1.1" 500 113508
[2025-07-04 11:37:58,640][django.request.log_response():241] [ERROR] Internal Server Error: /api/sales_record/material_categories/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\viewsets.py", line 124, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 511, in dispatch
    self.response = self.finalize_response(request, response, *args, **kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\viewset.py", line 170, in finalize_response
    if hasattr(response.data, 'get') and response.data.get('results'):
AttributeError: 'JsonResponse' object has no attribute 'data'
[2025-07-04 11:37:58,642][django.server.log_message():212] [ERROR] "GET /api/sales_record/material_categories/ HTTP/1.1" 500 113508
[2025-07-04 11:38:06,579][django.request.log_response():241] [ERROR] Internal Server Error: /api/sales_record/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\viewsets.py", line 124, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 511, in dispatch
    self.response = self.finalize_response(request, response, *args, **kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\viewset.py", line 170, in finalize_response
    if hasattr(response.data, 'get') and response.data.get('results'):
AttributeError: 'JsonResponse' object has no attribute 'data'
[2025-07-04 11:38:06,581][django.server.log_message():212] [ERROR] "GET /api/sales_record/ HTTP/1.1" 500 113091
[2025-07-04 11:38:07,059][django.request.log_response():241] [ERROR] Internal Server Error: /api/sales_record/material_categories/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\viewsets.py", line 124, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 511, in dispatch
    self.response = self.finalize_response(request, response, *args, **kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\viewset.py", line 170, in finalize_response
    if hasattr(response.data, 'get') and response.data.get('results'):
AttributeError: 'JsonResponse' object has no attribute 'data'
[2025-07-04 11:38:07,062][django.server.log_message():212] [ERROR] "GET /api/sales_record/material_categories/ HTTP/1.1" 500 113502
[2025-07-04 11:48:02,560][django.request.log_response():241] [ERROR] Internal Server Error: /api/sales_record/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\viewsets.py", line 124, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 511, in dispatch
    self.response = self.finalize_response(request, response, *args, **kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\viewset.py", line 170, in finalize_response
    if hasattr(response.data, 'get') and response.data.get('results'):
AttributeError: 'JsonResponse' object has no attribute 'data'
[2025-07-04 11:48:02,568][django.server.log_message():212] [ERROR] "GET /api/sales_record/ HTTP/1.1" 500 113210
[2025-07-04 11:48:03,167][django.request.log_response():241] [ERROR] Internal Server Error: /api/sales_record/material_categories/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\viewsets.py", line 124, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 511, in dispatch
    self.response = self.finalize_response(request, response, *args, **kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\viewset.py", line 170, in finalize_response
    if hasattr(response.data, 'get') and response.data.get('results'):
AttributeError: 'JsonResponse' object has no attribute 'data'
[2025-07-04 11:48:03,169][django.server.log_message():212] [ERROR] "GET /api/sales_record/material_categories/ HTTP/1.1" 500 113621
[2025-07-04 11:50:52,693][django.request.log_response():241] [ERROR] Internal Server Error: /api/sales_record/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\viewsets.py", line 124, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 511, in dispatch
    self.response = self.finalize_response(request, response, *args, **kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\viewset.py", line 170, in finalize_response
    if hasattr(response.data, 'get') and response.data.get('results'):
AttributeError: 'JsonResponse' object has no attribute 'data'
[2025-07-04 11:50:52,699][django.server.log_message():212] [ERROR] "GET /api/sales_record/ HTTP/1.1" 500 113210
[2025-07-04 11:50:53,317][django.request.log_response():241] [ERROR] Internal Server Error: /api/sales_record/material_categories/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\viewsets.py", line 124, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 511, in dispatch
    self.response = self.finalize_response(request, response, *args, **kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\viewset.py", line 170, in finalize_response
    if hasattr(response.data, 'get') and response.data.get('results'):
AttributeError: 'JsonResponse' object has no attribute 'data'
[2025-07-04 11:50:53,320][django.server.log_message():212] [ERROR] "GET /api/sales_record/material_categories/ HTTP/1.1" 500 113621
[2025-07-04 14:52:11,738][price_analysis.views.price_analysis_query():213] [ERROR] 价格分析查询失败: 
[2025-07-04 14:52:11,740][django.request.log_response():241] [ERROR] Internal Server Error: /api/price-analysis/query/
[2025-07-04 14:52:11,741][django.server.log_message():212] [ERROR] "POST /api/price-analysis/query/ HTTP/1.1" 500 52
[2025-07-04 15:49:39,579][price_analysis.views.price_analysis_query():219] [ERROR] 价格分析查询失败: unsupported format character '?' (0x6d59) at index 279
[2025-07-04 15:49:39,580][django.request.log_response():241] [ERROR] Internal Server Error: /api/price-analysis/query/
[2025-07-04 15:49:39,581][django.server.log_message():212] [ERROR] "POST /api/price-analysis/query/ HTTP/1.1" 500 205
[2025-07-04 15:50:11,967][price_analysis.views.price_analysis_query():219] [ERROR] 价格分析查询失败: not enough arguments for format string
[2025-07-04 15:50:11,968][django.request.log_response():241] [ERROR] Internal Server Error: /api/price-analysis/query/
[2025-07-04 15:50:11,969][django.server.log_message():212] [ERROR] "POST /api/price-analysis/query/ HTTP/1.1" 500 189
[2025-07-04 15:51:28,486][price_analysis.views.price_analysis_query():136] [ERROR] 价格分析查询失败: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]'FORMAT' 不是可以识别的 内置函数名称。 (195) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]关键字 'with' 附近有语法错误。如果此语句是公用表表达式、xmlnamespaces 子句或者更改跟踪上下文子句，那么前一个语句必须以分号结尾。 (319); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]关键字 'with' 附近有语法错误。如果此语句是公用表表达式、xmlnamespaces 子句或者更改跟踪上下文子句，那么前一个语句必须以分号结尾。 (319); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]关键字 'with' 附近有语法错误。如果此语句是公用表表达式、xmlnamespaces 子句或者更改跟踪上下文子句，那么前一个语句必须以分号结尾。 (319); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]关键字 'with' 附近有语法错误。如果此语句是公用表表达式、xmlnamespaces 子句或者更改跟踪上下文子句，那么前一个语句必须以分号结尾。 (319); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]关键字 'with' 附近有语法错误。如果此语句是公用表表达式、xmlnamespaces 子句或者更改跟踪上下文子句，那么前一个语句必须以分号结尾。 (319); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]关键字 'with' 附近有语法错误。如果此语句是公用表表达式、xmlnamespaces 子句或者更改跟踪上下文子句，那么前一个语句必须以分号结尾。 (319); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]关键字 'with' 附近有语法错误。如果此语句是公用表表达式、xmlnamespaces 子句或者更改跟踪上下文子句，那么前一个语句必须以分号结尾。 (319); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]关键字 'with' 附近有语法错误。如果此语句是公用表表达式、xmlnamespaces 子句或者更改跟踪上下文子句，那么前一个语句必须以分号结尾。 (319); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]关键字 'with' 附近有语法错误。如果此语句是公用表表达式、xmlnamespaces 子句或者更改跟踪上下文子句，那么前一个语句必须以分号结尾。 (319); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]关键字 'with' 附近有语法错误。如果此语句是公用表表达式、xmlnamespaces 子句或者更改跟踪上下文子句，那么前一个语句必须以分号结尾。 (319); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]关键字 'with' 附近有语法错误。如果此语句是公用表表达式、xmlnamespaces 子句或者更改跟踪上下文子句，那么前一个语句必须以分号结尾。 (319)")
[2025-07-04 15:51:28,488][django.request.log_response():241] [ERROR] Internal Server Error: /api/price-analysis/query/
[2025-07-04 15:51:28,489][django.server.log_message():212] [ERROR] "POST /api/price-analysis/query/ HTTP/1.1" 500 3064
[2025-07-04 15:52:29,330][price_analysis.views.price_analysis_query():136] [ERROR] 价格分析查询失败: ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FCATEGORYID' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FCUSTOMERID' 无效。 (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FCUSTOMERID' 无效。 (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FCUSTOMERID' 无效。 (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FCUSTOMERID' 无效。 (207)")
[2025-07-04 15:52:29,331][django.request.log_response():241] [ERROR] Internal Server Error: /api/price-analysis/query/
[2025-07-04 15:52:29,332][django.server.log_message():212] [ERROR] "POST /api/price-analysis/query/ HTTP/1.1" 500 681
[2025-07-04 16:22:38,934][price_analysis.views.price_analysis_query():310] [ERROR] 价格分析查询失败: ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 '销售日期' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 '销售日期' 无效。 (207)")
[2025-07-04 16:22:38,935][django.request.log_response():241] [ERROR] Internal Server Error: /api/price-analysis/query/
[2025-07-04 16:22:38,936][django.server.log_message():212] [ERROR] "POST /api/price-analysis/query/ HTTP/1.1" 500 383
[2025-07-04 16:22:46,595][price_analysis.views.price_analysis_query():310] [ERROR] 价格分析查询失败: ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 '销售日期' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 '销售日期' 无效。 (207)")
[2025-07-04 16:22:46,596][django.request.log_response():241] [ERROR] Internal Server Error: /api/price-analysis/query/
[2025-07-04 16:22:46,597][django.server.log_message():212] [ERROR] "POST /api/price-analysis/query/ HTTP/1.1" 500 383
[2025-07-04 16:28:15,186][price_analysis.views.price_analysis_query():214] [ERROR] 价格分析查询失败: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]选择列表中的列 'T_SAL_ORDERENTRY_F.FALLAMOUNT' 无效，因为该列没有包含在聚合函数或 GROUP BY 子句中。 (8120) (SQLExecDirectW)")
[2025-07-04 16:28:15,187][django.request.log_response():241] [ERROR] Internal Server Error: /api/price-analysis/query/
[2025-07-04 16:28:15,188][django.server.log_message():212] [ERROR] "POST /api/price-analysis/query/ HTTP/1.1" 500 379
[2025-07-04 16:35:40,736][price_analysis.views.price_analysis_query():186] [ERROR] 价格分析查询失败: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]'FORMAT' 不是可以识别的 内置函数名称。 (195) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]'cd' 附近有语法错误。 (102); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]'ma' 附近有语法错误。 (102)")
[2025-07-04 16:35:40,738][django.request.log_response():241] [ERROR] Internal Server Error: /api/price-analysis/query/
[2025-07-04 16:35:40,738][django.server.log_message():212] [ERROR] "POST /api/price-analysis/query/ HTTP/1.1" 500 501
[2025-07-04 16:36:06,713][price_analysis.views.price_analysis_query():186] [ERROR] 价格分析查询失败: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]'FORMAT' 不是可以识别的 内置函数名称。 (195) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]'cd' 附近有语法错误。 (102); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]'ma' 附近有语法错误。 (102)")
[2025-07-04 16:36:06,715][django.request.log_response():241] [ERROR] Internal Server Error: /api/price-analysis/query/
[2025-07-04 16:36:06,715][django.server.log_message():212] [ERROR] "POST /api/price-analysis/query/ HTTP/1.1" 500 501
[2025-07-07 11:46:33,854][price_analysis.views.price_analysis_query():186] [ERROR] 价格分析查询失败: Unknown format code 'd' for object of type 'str'
[2025-07-07 11:46:33,856][django.request.log_response():241] [ERROR] Internal Server Error: /api/price-analysis/export/
[2025-07-07 11:46:33,856][django.server.log_message():212] [ERROR] "GET /api/price-analysis/export/?year=2025&month=6&threshold=0.1&materialCategory=7005 HTTP/1.1" 500 199
[2025-07-07 11:46:38,388][price_analysis.views.price_analysis_query():186] [ERROR] 价格分析查询失败: Unknown format code 'd' for object of type 'str'
[2025-07-07 11:46:38,389][django.request.log_response():241] [ERROR] Internal Server Error: /api/price-analysis/export/
[2025-07-07 11:46:38,389][django.server.log_message():212] [ERROR] "GET /api/price-analysis/export/?year=2025&month=6&threshold=0.1&materialCategory=7005 HTTP/1.1" 500 199
[2025-07-07 11:50:17,517][price_analysis.views.price_analysis_export():365] [ERROR] 价格分析导出失败: (1064, "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'WITH (NOLOCK)\n            INNER JOIN T_SAL_ORDERENTRY e WITH (NOLOCK) ON o.FID =' at line 14")
[2025-07-07 11:50:17,519][django.request.log_response():241] [ERROR] Internal Server Error: /api/price-analysis/export/
[2025-07-07 11:50:17,519][django.server.log_message():212] [ERROR] "POST /api/price-analysis/export/ HTTP/1.1" 500 292
[2025-07-07 11:50:19,555][price_analysis.views.price_analysis_export():365] [ERROR] 价格分析导出失败: (1064, "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'WITH (NOLOCK)\n            INNER JOIN T_SAL_ORDERENTRY e WITH (NOLOCK) ON o.FID =' at line 14")
[2025-07-07 11:50:19,556][django.request.log_response():241] [ERROR] Internal Server Error: /api/price-analysis/export/
[2025-07-07 11:50:19,557][django.server.log_message():212] [ERROR] "POST /api/price-analysis/export/ HTTP/1.1" 500 292
[2025-07-07 11:50:21,601][price_analysis.views.price_analysis_export():365] [ERROR] 价格分析导出失败: (1064, "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'WITH (NOLOCK)\n            INNER JOIN T_SAL_ORDERENTRY e WITH (NOLOCK) ON o.FID =' at line 14")
[2025-07-07 11:50:21,602][django.request.log_response():241] [ERROR] Internal Server Error: /api/price-analysis/export/
[2025-07-07 11:50:21,602][django.server.log_message():212] [ERROR] "POST /api/price-analysis/export/ HTTP/1.1" 500 292
[2025-07-07 16:15:31,425][data_analysis.views.dashboard_overview():197] [ERROR] 数据分析首页查询失败: (1146, "Table 'django-vue3-admin.t_sal_order' doesn't exist")
[2025-07-07 16:15:31,428][django.request.log_response():241] [ERROR] Internal Server Error: /api/data-analysis/dashboard/
[2025-07-07 16:15:31,429][django.server.log_message():212] [ERROR] "GET /api/data-analysis/dashboard/ HTTP/1.1" 500 353
[2025-07-07 16:23:39,871][data_analysis.views.dashboard_overview():197] [ERROR] 数据分析首页查询失败: (1064, "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'WITH (NOLOCK)\n        INNER JOIN T_SAL_ORDERENTRY e WITH (NOLOCK) ON o.FID = e.F' at line 7")
[2025-07-07 16:23:39,873][django.request.log_response():241] [ERROR] Internal Server Error: /api/data-analysis/dashboard/
[2025-07-07 16:23:39,873][django.server.log_message():212] [ERROR] "GET /api/data-analysis/dashboard/ HTTP/1.1" 500 530
[2025-07-07 16:26:52,436][data_analysis.views.dashboard_overview():197] [ERROR] 数据分析首页查询失败: (1146, "Table 'django-vue3-admin.t_sal_order' doesn't exist")
[2025-07-07 16:26:52,438][django.request.log_response():241] [ERROR] Internal Server Error: /api/data-analysis/dashboard/
[2025-07-07 16:26:52,438][django.server.log_message():212] [ERROR] "GET /api/data-analysis/dashboard/ HTTP/1.1" 500 353
[2025-07-07 16:30:01,532][data_analysis.views.dashboard_overview():208] [ERROR] 数据分析首页查询失败: No module named 'pymssql'
[2025-07-07 16:30:01,533][django.request.log_response():241] [ERROR] Internal Server Error: /api/data-analysis/dashboard/
[2025-07-07 16:30:01,534][django.server.log_message():212] [ERROR] "GET /api/data-analysis/dashboard/ HTTP/1.1" 500 315
[2025-07-07 16:30:43,479][data_analysis.views.dashboard_overview():208] [ERROR] 数据分析首页查询失败: No module named 'pymssql'
[2025-07-07 16:30:43,480][django.request.log_response():241] [ERROR] Internal Server Error: /api/data-analysis/dashboard/
[2025-07-07 16:30:43,481][django.server.log_message():212] [ERROR] "GET /api/data-analysis/dashboard/ HTTP/1.1" 500 315
[2025-07-07 16:31:37,153][data_analysis.views.dashboard_overview():208] [ERROR] 数据分析首页查询失败: (20002, b'DB-Lib error message 20002, severity 9:\nAdaptive Server connection failed (192.168.1.250)\nDB-Lib error message 20002, severity 9:\nAdaptive Server connection failed (192.168.1.250)\n')
[2025-07-07 16:31:37,154][django.request.log_response():241] [ERROR] Internal Server Error: /api/data-analysis/dashboard/
[2025-07-07 16:31:37,155][django.server.log_message():212] [ERROR] "GET /api/data-analysis/dashboard/ HTTP/1.1" 500 490
[2025-07-07 16:36:26,485][data_analysis.views.dashboard_overview():197] [ERROR] 数据分析首页查询失败: ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FCUSTOMERID' 无效。 (207) (SQLExecDirectW)")
[2025-07-07 16:36:26,487][django.request.log_response():241] [ERROR] Internal Server Error: /api/data-analysis/dashboard/
[2025-07-07 16:36:26,488][django.server.log_message():212] [ERROR] "GET /api/data-analysis/dashboard/ HTTP/1.1" 500 420
[2025-07-07 16:40:01,406][data_analysis.views.dashboard_overview():197] [ERROR] 数据分析首页查询失败: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]'FORMAT' 不是可以识别的 内置函数名称。 (195) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]关键字 'with' 附近有语法错误。如果此语句是公用表表达式、xmlnamespaces 子句或者更改跟踪上下文子句，那么前一个语句必须以分号结尾。 (319); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]关键字 'with' 附近有语法错误。如果此语句是公用表表达式、xmlnamespaces 子句或者更改跟踪上下文子句，那么前一个语句必须以分号结尾。 (319); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]关键字 'with' 附近有语法错误。如果此语句是公用表表达式、xmlnamespaces 子句或者更改跟踪上下文子句，那么前一个语句必须以分号结尾。 (319); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]关键字 'with' 附近有语法错误。如果此语句是公用表表达式、xmlnamespaces 子句或者更改跟踪上下文子句，那么前一个语句必须以分号结尾。 (319); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]关键字 'with' 附近有语法错误。如果此语句是公用表表达式、xmlnamespaces 子句或者更改跟踪上下文子句，那么前一个语句必须以分号结尾。 (319)")
[2025-07-07 16:40:01,409][django.request.log_response():241] [ERROR] Internal Server Error: /api/data-analysis/dashboard/
[2025-07-07 16:40:01,410][django.server.log_message():212] [ERROR] "GET /api/data-analysis/dashboard/ HTTP/1.1" 500 1697
[2025-07-08 14:00:12,591][customer_analysis.views.customer_analysis_query():155] [ERROR] 客户分析查询失败: JSON parse error - Expecting property name enclosed in double quotes: line 1 column 2 (char 1)
[2025-07-08 14:00:12,598][dvadmin.utils.exception.CustomExceptionHandler():68] [ERROR] Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\parsers.py", line 66, in parse
    return json.load(decoded_stream, parse_constant=parse_constant)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\utils\json.py", line 31, in load
    return json.load(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 293, in load
    return loads(fp.read(),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 359, in loads
    return cls(**kw).decode(s)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\decoder.py", line 353, in raw_decode
    obj, end = self.scan_once(s, idx)
json.decoder.JSONDecodeError: Expecting property name enclosed in double quotes: line 1 column 2 (char 1)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\HL_python\django-vue3-admin-master\backend\customer_analysis\views.py", line 19, in customer_analysis_query
    data = request.data
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\request.py", line 220, in data
    self._load_data_and_files()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\request.py", line 283, in _load_data_and_files
    self._data, self._files = self._parse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\request.py", line 358, in _parse
    parsed = parser.parse(stream, media_type, self.parser_context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\parsers.py", line 68, in parse
    raise ParseError('JSON parse error - %s' % str(exc))
rest_framework.exceptions.ParseError: JSON parse error - Expecting property name enclosed in double quotes: line 1 column 2 (char 1)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\customer_analysis\views.py", line 165, in customer_analysis_query
    'thresholdDays': threshold_days,
UnboundLocalError: local variable 'threshold_days' referenced before assignment
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\parsers.py", line 66, in parse
    return json.load(decoded_stream, parse_constant=parse_constant)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\utils\json.py", line 31, in load
    return json.load(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 293, in load
    return loads(fp.read(),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 359, in loads
    return cls(**kw).decode(s)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\decoder.py", line 353, in raw_decode
    obj, end = self.scan_once(s, idx)
json.decoder.JSONDecodeError: Expecting property name enclosed in double quotes: line 1 column 2 (char 1)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\HL_python\django-vue3-admin-master\backend\customer_analysis\views.py", line 19, in customer_analysis_query
    data = request.data
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\request.py", line 220, in data
    self._load_data_and_files()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\request.py", line 283, in _load_data_and_files
    self._data, self._files = self._parse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\request.py", line 358, in _parse
    parsed = parser.parse(stream, media_type, self.parser_context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\parsers.py", line 68, in parse
    raise ParseError('JSON parse error - %s' % str(exc))
rest_framework.exceptions.ParseError: JSON parse error - Expecting property name enclosed in double quotes: line 1 column 2 (char 1)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\customer_analysis\views.py", line 165, in customer_analysis_query
    'thresholdDays': threshold_days,
UnboundLocalError: local variable 'threshold_days' referenced before assignment
[2025-07-08 14:00:51,985][customer_analysis.views.customer_analysis_query():158] [ERROR] 客户分析查询失败: JSON parse error - Expecting property name enclosed in double quotes: line 1 column 2 (char 1)
[2025-07-08 14:00:51,988][dvadmin.utils.exception.CustomExceptionHandler():68] [ERROR] Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\parsers.py", line 66, in parse
    return json.load(decoded_stream, parse_constant=parse_constant)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\utils\json.py", line 31, in load
    return json.load(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 293, in load
    return loads(fp.read(),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 359, in loads
    return cls(**kw).decode(s)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\decoder.py", line 353, in raw_decode
    obj, end = self.scan_once(s, idx)
json.decoder.JSONDecodeError: Expecting property name enclosed in double quotes: line 1 column 2 (char 1)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\HL_python\django-vue3-admin-master\backend\customer_analysis\views.py", line 22, in customer_analysis_query
    data = request.data
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\request.py", line 220, in data
    self._load_data_and_files()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\request.py", line 283, in _load_data_and_files
    self._data, self._files = self._parse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\request.py", line 358, in _parse
    parsed = parser.parse(stream, media_type, self.parser_context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\parsers.py", line 68, in parse
    raise ParseError('JSON parse error - %s' % str(exc))
rest_framework.exceptions.ParseError: JSON parse error - Expecting property name enclosed in double quotes: line 1 column 2 (char 1)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\customer_analysis\views.py", line 173, in customer_analysis_query
    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
UnboundLocalError: local variable 'status' referenced before assignment
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\parsers.py", line 66, in parse
    return json.load(decoded_stream, parse_constant=parse_constant)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\utils\json.py", line 31, in load
    return json.load(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 293, in load
    return loads(fp.read(),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 359, in loads
    return cls(**kw).decode(s)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\decoder.py", line 353, in raw_decode
    obj, end = self.scan_once(s, idx)
json.decoder.JSONDecodeError: Expecting property name enclosed in double quotes: line 1 column 2 (char 1)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\HL_python\django-vue3-admin-master\backend\customer_analysis\views.py", line 22, in customer_analysis_query
    data = request.data
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\request.py", line 220, in data
    self._load_data_and_files()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\request.py", line 283, in _load_data_and_files
    self._data, self._files = self._parse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\request.py", line 358, in _parse
    parsed = parser.parse(stream, media_type, self.parser_context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\parsers.py", line 68, in parse
    raise ParseError('JSON parse error - %s' % str(exc))
rest_framework.exceptions.ParseError: JSON parse error - Expecting property name enclosed in double quotes: line 1 column 2 (char 1)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\customer_analysis\views.py", line 173, in customer_analysis_query
    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
UnboundLocalError: local variable 'status' referenced before assignment
[2025-07-08 14:38:24,552][customer_analysis.views.customer_analysis_query():204] [ERROR] 客户分析查询失败: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]'OFFSET' 附近有语法错误。 (102) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]在 FETCH 语句中选项 NEXT 的用法无效。 (153)")
[2025-07-08 14:38:24,553][django.request.log_response():241] [ERROR] Internal Server Error: /api/customer-analysis/query/
[2025-07-08 14:38:24,554][django.server.log_message():212] [ERROR] "POST /api/customer-analysis/query/ HTTP/1.1" 500 446
[2025-07-08 14:38:43,525][customer_analysis.views.customer_analysis_query():204] [ERROR] 客户分析查询失败: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]'OFFSET' 附近有语法错误。 (102) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]在 FETCH 语句中选项 NEXT 的用法无效。 (153)")
[2025-07-08 14:38:43,527][django.request.log_response():241] [ERROR] Internal Server Error: /api/customer-analysis/query/
[2025-07-08 14:38:43,527][django.server.log_message():212] [ERROR] "POST /api/customer-analysis/query/ HTTP/1.1" 500 446
[2025-07-08 14:41:42,242][customer_analysis.views.customer_analysis_query():204] [ERROR] 客户分析查询失败: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]'OFFSET' 附近有语法错误。 (102) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]在 FETCH 语句中选项 NEXT 的用法无效。 (153)")
[2025-07-08 14:41:42,243][django.request.log_response():241] [ERROR] Internal Server Error: /api/customer-analysis/query/
[2025-07-08 14:41:42,244][django.server.log_message():212] [ERROR] "POST /api/customer-analysis/query/ HTTP/1.1" 500 446
[2025-07-10 14:59:05,739][dvadmin.utils.exception.CustomExceptionHandler():68] [ERROR] Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\connections.py", line 255, in query
    _mysql.connection.query(self, query)
MySQLdb.ProgrammingError: (1146, "Table 'django-vue3-admin.price_analysis_result' doesn't exist")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\viewset.py", line 106, in create
    self.perform_create(serializer)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\viewset.py", line 111, in perform_create
    self.perform_destroy(instance)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\mixins.py", line 95, in perform_destroy
    instance.delete()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\base.py", line 1132, in delete
    return collector.delete()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\deletion.py", line 490, in delete
    combined_updates.update(**{field.name: value})
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 1206, in update
    rows = query.get_compiler(self.db).execute_sql(CURSOR)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\compiler.py", line 1984, in execute_sql
    cursor = super().execute_sql(result_type)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\connections.py", line 255, in query
    _mysql.connection.query(self, query)
django.db.utils.ProgrammingError: (1146, "Table 'django-vue3-admin.price_analysis_result' doesn't exist")
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\connections.py", line 255, in query
    _mysql.connection.query(self, query)
MySQLdb.ProgrammingError: (1146, "Table 'django-vue3-admin.price_analysis_result' doesn't exist")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\viewset.py", line 106, in create
    self.perform_create(serializer)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\viewset.py", line 111, in perform_create
    self.perform_destroy(instance)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\mixins.py", line 95, in perform_destroy
    instance.delete()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\base.py", line 1132, in delete
    return collector.delete()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\deletion.py", line 490, in delete
    combined_updates.update(**{field.name: value})
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 1206, in update
    rows = query.get_compiler(self.db).execute_sql(CURSOR)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\compiler.py", line 1984, in execute_sql
    cursor = super().execute_sql(result_type)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\connections.py", line 255, in query
    _mysql.connection.query(self, query)
django.db.utils.ProgrammingError: (1146, "Table 'django-vue3-admin.price_analysis_result' doesn't exist")
[2025-07-10 14:59:59,304][dvadmin.utils.exception.CustomExceptionHandler():68] [ERROR] Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\connections.py", line 255, in query
    _mysql.connection.query(self, query)
MySQLdb.ProgrammingError: (1146, "Table 'django-vue3-admin.price_analysis_result' doesn't exist")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\viewset.py", line 106, in create
    self.perform_create(serializer)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\viewset.py", line 111, in perform_create
    self.perform_destroy(instance)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\mixins.py", line 95, in perform_destroy
    instance.delete()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\base.py", line 1132, in delete
    return collector.delete()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\deletion.py", line 490, in delete
    combined_updates.update(**{field.name: value})
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 1206, in update
    rows = query.get_compiler(self.db).execute_sql(CURSOR)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\compiler.py", line 1984, in execute_sql
    cursor = super().execute_sql(result_type)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\connections.py", line 255, in query
    _mysql.connection.query(self, query)
django.db.utils.ProgrammingError: (1146, "Table 'django-vue3-admin.price_analysis_result' doesn't exist")
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\connections.py", line 255, in query
    _mysql.connection.query(self, query)
MySQLdb.ProgrammingError: (1146, "Table 'django-vue3-admin.price_analysis_result' doesn't exist")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\viewset.py", line 106, in create
    self.perform_create(serializer)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\viewset.py", line 111, in perform_create
    self.perform_destroy(instance)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\mixins.py", line 95, in perform_destroy
    instance.delete()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\base.py", line 1132, in delete
    return collector.delete()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\deletion.py", line 490, in delete
    combined_updates.update(**{field.name: value})
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\query.py", line 1206, in update
    rows = query.get_compiler(self.db).execute_sql(CURSOR)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\compiler.py", line 1984, in execute_sql
    cursor = super().execute_sql(result_type)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 179, in execute
    res = self._query(mogrified_query)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\cursors.py", line 330, in _query
    db.query(q)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\MySQLdb\connections.py", line 255, in query
    _mysql.connection.query(self, query)
django.db.utils.ProgrammingError: (1146, "Table 'django-vue3-admin.price_analysis_result' doesn't exist")
[2025-07-22 10:52:31,162][dvadmin.utils.exception.CustomExceptionHandler():68] [ERROR] Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\import_export_mixin.py", line 304, in export_data
    assert self.export_field_label, "'%s' 请配置对应的导出模板字段。" % self.__class__.__name__
AssertionError: 'PerformanceAnalysisViewSet' 请配置对应的导出模板字段。
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\import_export_mixin.py", line 304, in export_data
    assert self.export_field_label, "'%s' 请配置对应的导出模板字段。" % self.__class__.__name__
AssertionError: 'PerformanceAnalysisViewSet' 请配置对应的导出模板字段。
[2025-07-22 10:52:37,075][dvadmin.utils.exception.CustomExceptionHandler():68] [ERROR] Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\import_export_mixin.py", line 304, in export_data
    assert self.export_field_label, "'%s' 请配置对应的导出模板字段。" % self.__class__.__name__
AssertionError: 'PerformanceAnalysisViewSet' 请配置对应的导出模板字段。
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\import_export_mixin.py", line 304, in export_data
    assert self.export_field_label, "'%s' 请配置对应的导出模板字段。" % self.__class__.__name__
AssertionError: 'PerformanceAnalysisViewSet' 请配置对应的导出模板字段。
[2025-07-22 10:52:37,811][dvadmin.utils.exception.CustomExceptionHandler():68] [ERROR] Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\import_export_mixin.py", line 304, in export_data
    assert self.export_field_label, "'%s' 请配置对应的导出模板字段。" % self.__class__.__name__
AssertionError: 'PerformanceAnalysisViewSet' 请配置对应的导出模板字段。
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\HL_python\django-vue3-admin-master\backend\dvadmin\utils\import_export_mixin.py", line 304, in export_data
    assert self.export_field_label, "'%s' 请配置对应的导出模板字段。" % self.__class__.__name__
AssertionError: 'PerformanceAnalysisViewSet' 请配置对应的导出模板字段。
[2025-07-22 11:02:47,314][performance_analysis.views.get_salesperson_list():293] [ERROR] 获取业务员列表失败: ('42000', '[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]如果指定了 SELECT DISTINCT，那么 ORDER BY 子句中的项就必须出现在选择列表中。 (145) (SQLExecDirectW)')
[2025-07-22 11:02:47,315][django.request.log_response():241] [ERROR] Internal Server Error: /api/performance-analysis/salesperson-list/
[2025-07-22 11:02:47,316][django.server.log_message():212] [ERROR] "GET /api/performance-analysis/salesperson-list/ HTTP/1.1" 500 263
[2025-07-22 11:14:49,844][performance_analysis.views.get_salesperson_list():293] [ERROR] 获取业务员列表失败: ('42000', '[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]如果指定了 SELECT DISTINCT，那么 ORDER BY 子句中的项就必须出现在选择列表中。 (145) (SQLExecDirectW)')
[2025-07-22 11:14:49,845][django.request.log_response():241] [ERROR] Internal Server Error: /api/performance-analysis/salesperson-list/
[2025-07-22 11:14:49,847][django.server.log_message():212] [ERROR] "GET /api/performance-analysis/salesperson-list/ HTTP/1.1" 500 263
[2025-07-22 11:36:52,233][performance_analysis.views.get_salesperson_list():293] [ERROR] 获取业务员列表失败: ('42000', '[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]如果指定了 SELECT DISTINCT，那么 ORDER BY 子句中的项就必须出现在选择列表中。 (145) (SQLExecDirectW)')
[2025-07-22 11:36:52,235][django.request.log_response():241] [ERROR] Internal Server Error: /api/performance-analysis/salesperson-list/
[2025-07-22 11:36:52,236][django.server.log_message():212] [ERROR] "GET /api/performance-analysis/salesperson-list/ HTTP/1.1" 500 263
[2025-07-22 11:37:16,604][performance_analysis.views.get_salesperson_list():293] [ERROR] 获取业务员列表失败: ('42000', '[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]如果指定了 SELECT DISTINCT，那么 ORDER BY 子句中的项就必须出现在选择列表中。 (145) (SQLExecDirectW)')
[2025-07-22 11:37:16,606][django.request.log_response():241] [ERROR] Internal Server Error: /api/performance-analysis/salesperson-list/
[2025-07-22 11:37:16,607][django.server.log_message():212] [ERROR] "GET /api/performance-analysis/salesperson-list/ HTTP/1.1" 500 263
[2025-07-22 11:42:31,241][performance_analysis.views.get_salesperson_list():295] [ERROR] 获取业务员列表失败: ('42000', '[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]如果指定了 SELECT DISTINCT，那么 ORDER BY 子句中的项就必须出现在选择列表中。 (145) (SQLExecDirectW)')
[2025-07-22 11:42:31,243][django.request.log_response():241] [ERROR] Internal Server Error: /api/performance-analysis/salesperson-list/
[2025-07-22 11:42:31,244][django.server.log_message():212] [ERROR] "GET /api/performance-analysis/salesperson-list/ HTTP/1.1" 500 263
