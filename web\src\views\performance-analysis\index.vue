<template>
  <div class="performance-analysis-container">
    <!-- 页面标题 -->
    <el-card class="header-card">
      <template #header>
        <div class="card-header">
          <div class="header-info">
            <span class="update-time">更新时间: {{ updateTime }}</span>
            <el-button type="primary" size="small" @click="refreshData" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新数据
            </el-button>
          </div>
        </div>
      </template>

      <div class="welcome-content">
        <h2>业务员绩效分析</h2>
        <p>统计分析业务员销售绩效，支持月度、季度、年度多维度分析</p>
      </div>
    </el-card>

    <!-- 查询条件 -->
    <el-card class="filter-card">
      <div class="filter-form">
        <el-row :gutter="16" style="align-items: center;">
          <el-col :span="4">
            <div class="filter-item">
              <label>查询类型：</label>
              <el-select v-model="queryForm.queryType" @change="onQueryTypeChange" style="width: 100%;">
                <el-option label="月度分析" value="monthly" />
                <el-option label="季度分析" value="quarterly" />
                <el-option label="年度分析" value="yearly" />
              </el-select>
            </div>
          </el-col>
          <el-col :span="3">
            <div class="filter-item">
              <label>年份：</label>
              <el-date-picker
                v-model="queryForm.year"
                type="year"
                placeholder="选择年份"
                format="YYYY"
                value-format="YYYY"
                style="width: 100%;"
              />
            </div>
          </el-col>
          <el-col :span="3" v-if="queryForm.queryType === 'monthly'">
            <div class="filter-item">
              <label>月份：</label>
              <el-select v-model="queryForm.month" style="width: 100%;">
                <el-option
                  v-for="month in 12"
                  :key="month"
                  :label="`${month}月`"
                  :value="month"
                />
              </el-select>
            </div>
          </el-col>
          <el-col :span="3" v-if="queryForm.queryType === 'quarterly'">
            <div class="filter-item">
              <label>季度：</label>
              <el-select v-model="queryForm.quarter" style="width: 100%;">
                <el-option label="第一季度" :value="1" />
                <el-option label="第二季度" :value="2" />
                <el-option label="第三季度" :value="3" />
                <el-option label="第四季度" :value="4" />
              </el-select>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="filter-item">
              <label>业务员：</label>
              <el-select v-model="queryForm.salesperson" clearable placeholder="全部业务员" style="width: 100%;">
                <el-option
                  v-for="item in salespersonList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="filter-item">
              <label>地区：</label>
              <el-select v-model="queryForm.region" clearable placeholder="全部地区" style="width: 100%;">
                <el-option
                  v-for="item in regionList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </el-col>
          <el-col :span="3">
            <div class="filter-buttons">
              <el-button type="primary" @click="handlePerformanceQuery" :loading="performanceLoading">
                查询绩效
              </el-button>
              <el-button @click="handleResetQuery">重置</el-button>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 统计卡片 -->
    <div v-if="statistics" class="stats-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon size="32"><User /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-title">业务员总数</div>
                <div class="stat-value">{{ statistics.totalSalespersons }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon size="32"><Money /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-title">总销售金额</div>
                <div class="stat-value">{{ formatNumber(statistics.totalSalesAmount) }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon size="32"><Box /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-title">总销售数量</div>
                <div class="stat-value">{{ formatNumber(statistics.totalSalesQuantity) }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon size="32"><TrendCharts /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-title">人均销售金额</div>
                <div class="stat-value">{{ formatNumber(statistics.averageSalesAmount) }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 绩效数据表格 -->
    <el-card v-if="performanceData.length > 0" class="table-card">
      <template #header>
        <div class="table-header">
          <h3>绩效分析结果</h3>
          <el-button type="success" @click="exportData" :loading="exportLoading">
            <el-icon><Download /></el-icon>
            导出Excel
          </el-button>
        </div>
      </template>

      <el-table
        :data="performanceData"
        border
        stripe
        style="width: 100%"
        :max-height="600"
        v-loading="performanceLoading"
      >
        <el-table-column prop="业务员" label="业务员" width="120" fixed="left" />
        <el-table-column prop="负责地区" label="负责地区" width="120" />

        <!-- 根据查询类型显示不同列 -->
        <template v-if="queryForm.queryType === 'monthly'">
          <el-table-column prop="订单数量" label="订单数量" width="100" />
          <el-table-column prop="客户数量" label="客户数量" width="100" />
          <el-table-column prop="产品种类" label="产品种类" width="100" />
          <el-table-column prop="总销量" label="总销量" width="120">
            <template #default="{ row }">
              {{ formatNumber(row.总销量) }}
            </template>
          </el-table-column>
          <el-table-column prop="总销售金额" label="总销售金额" width="140">
            <template #default="{ row }">
              {{ formatNumber(row.总销售金额) }}
            </template>
          </el-table-column>
          <el-table-column prop="平均单价" label="平均单价" width="120">
            <template #default="{ row }">
              {{ formatNumber(row.平均单价) }}
            </template>
          </el-table-column>
          <el-table-column prop="客户平均贡献" label="客户平均贡献" width="140">
            <template #default="{ row }">
              {{ formatNumber(row.客户平均贡献) }}
            </template>
          </el-table-column>
          <el-table-column prop="单均销量" label="单均销量" width="120">
            <template #default="{ row }">
              {{ formatNumber(row.单均销量) }}
            </template>
          </el-table-column>
          <el-table-column prop="统计月份" label="统计月份" width="120" />
        </template>

        <template v-if="queryForm.queryType === 'quarterly'">
          <el-table-column prop="季度" label="季度" width="80" />
          <el-table-column prop="季度订单数" label="季度订单数" width="120" />
          <el-table-column prop="季度客户数" label="季度客户数" width="120" />
          <el-table-column prop="季度总销量" label="季度总销量" width="140">
            <template #default="{ row }">
              {{ formatNumber(row.季度总销量) }}
            </template>
          </el-table-column>
          <el-table-column prop="季度总销售金额" label="季度总销售金额" width="160">
            <template #default="{ row }">
              {{ formatNumber(row.季度总销售金额) }}
            </template>
          </el-table-column>
          <el-table-column prop="季度平均单价" label="季度平均单价" width="140">
            <template #default="{ row }">
              {{ formatNumber(row.季度平均单价) }}
            </template>
          </el-table-column>
          <el-table-column prop="月均销售金额" label="月均销售金额" width="140">
            <template #default="{ row }">
              {{ formatNumber(row.月均销售金额) }}
            </template>
          </el-table-column>
          <el-table-column prop="统计年份" label="统计年份" width="120" />
        </template>

        <template v-if="queryForm.queryType === 'yearly'">
          <el-table-column prop="年度订单数" label="年度订单数" width="120" />
          <el-table-column prop="年度客户数" label="年度客户数" width="120" />
          <el-table-column prop="年度产品种类" label="年度产品种类" width="140" />
          <el-table-column prop="年度总销量" label="年度总销量" width="140">
            <template #default="{ row }">
              {{ formatNumber(row.年度总销量) }}
            </template>
          </el-table-column>
          <el-table-column prop="年度总销售金额" label="年度总销售金额" width="160">
            <template #default="{ row }">
              {{ formatNumber(row.年度总销售金额) }}
            </template>
          </el-table-column>
          <el-table-column prop="年度平均单价" label="年度平均单价" width="140">
            <template #default="{ row }">
              {{ formatNumber(row.年度平均单价) }}
            </template>
          </el-table-column>
          <el-table-column prop="月均销售金额" label="月均销售金额" width="140">
            <template #default="{ row }">
              {{ formatNumber(row.月均销售金额) }}
            </template>
          </el-table-column>
          <el-table-column prop="月均销量" label="月均销量" width="120">
            <template #default="{ row }">
              {{ formatNumber(row.月均销量) }}
            </template>
          </el-table-column>
          <el-table-column prop="销售金额排名" label="销售金额排名" width="140" />
          <el-table-column prop="销量排名" label="销量排名" width="120" />
          <el-table-column prop="统计年份" label="统计年份" width="120" />
        </template>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Refresh,
  User,
  Money,
  Box,
  TrendCharts,
  Download
} from '@element-plus/icons-vue'
import { request } from '/@/utils/service'

// 响应式数据
const loading = ref(false)
const performanceLoading = ref(false)
const exportLoading = ref(false)
const performanceData = ref([])
const statistics = ref(null)
const salespersonList = ref([])
const regionList = ref([])

// 查询表单
const queryForm = reactive({
  queryType: 'monthly',
  year: new Date().getFullYear(),
  month: new Date().getMonth() + 1,
  quarter: 1,
  salesperson: '',
  region: ''
})

// 更新时间
const updateTime = computed(() => {
  return new Date().toLocaleString('zh-CN')
})

// 格式化数字
const formatNumber = (value) => {
  if (value === null || value === undefined || value === '') return '0'
  const num = parseFloat(value)
  if (isNaN(num)) return '0'
  return num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}

// 查询类型改变
const onQueryTypeChange = () => {
  if (queryForm.queryType === 'monthly') {
    queryForm.month = new Date().getMonth() + 1
  } else if (queryForm.queryType === 'quarterly') {
    queryForm.quarter = Math.ceil((new Date().getMonth() + 1) / 3)
  }
}

// 绩效查询
const handlePerformanceQuery = async () => {
  performanceLoading.value = true
  try {
    const response = await request({
      url: '/api/performance-analysis/query/',
      method: 'post',
      data: queryForm
    })

    if (response.code === 200) {
      performanceData.value = response.data.list
      statistics.value = response.data.statistics
      ElMessage.success('查询成功')
    } else {
      ElMessage.error(response.message || '查询失败')
    }
  } catch (error) {
    console.error('查询失败:', error)
    ElMessage.error('查询失败，请稍后重试')
  } finally {
    performanceLoading.value = false
  }
}

// 重置查询
const handleResetQuery = () => {
  queryForm.queryType = 'monthly'
  queryForm.year = new Date().getFullYear()
  queryForm.month = new Date().getMonth() + 1
  queryForm.quarter = 1
  queryForm.salesperson = ''
  queryForm.region = ''
  performanceData.value = []
  statistics.value = null
}

// 刷新数据
const refreshData = () => {
  if (performanceData.value.length > 0) {
    handlePerformanceQuery()
  }
}

// 导出数据
const exportData = async () => {
  if (!performanceData.value.length) {
    ElMessage.warning('没有数据可导出')
    return
  }

  exportLoading.value = true
  try {
    const response = await request({
      url: '/api/performance-analysis/export/',
      method: 'post',
      data: queryForm,
      responseType: 'blob'
    })

    // 创建下载链接
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url

    // 生成文件名
    const queryTypeNames = { monthly: '月度', quarterly: '季度', yearly: '年度' }
    const typeName = queryTypeNames[queryForm.queryType] || '绩效'

    let period = ''
    if (queryForm.queryType === 'monthly') {
      period = `${queryForm.year}年${queryForm.month}月`
    } else if (queryForm.queryType === 'quarterly') {
      period = `${queryForm.year}年Q${queryForm.quarter}`
    } else {
      period = `${queryForm.year}年`
    }

    link.download = `业务员${typeName}绩效分析_${period}_${new Date().toISOString().slice(0, 10)}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请稍后重试')
  } finally {
    exportLoading.value = false
  }
}

// 获取业务员列表
const loadSalespersonList = async () => {
  try {
    const response = await request({
      url: '/api/performance-analysis/salesperson-list/',
      method: 'get'
    })

    if (response.code === 200) {
      salespersonList.value = response.data
    }
  } catch (error) {
    console.error('获取业务员列表失败:', error)
  }
}

// 获取地区列表
const loadRegionList = async () => {
  try {
    const response = await request({
      url: '/api/performance-analysis/region-list/',
      method: 'get'
    })

    if (response.code === 200) {
      regionList.value = response.data
    }
  } catch (error) {
    console.error('获取地区列表失败:', error)
  }
}

// 页面加载时执行
onMounted(() => {
  loadSalespersonList()
  loadRegionList()
})
</script>

<style scoped>
.performance-analysis-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.header-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.update-time {
  color: #909399;
  font-size: 14px;
}

.welcome-content h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.welcome-content p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-form {
  padding: 10px 0;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-item label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.filter-buttons {
  display: flex;
  gap: 10px;
  align-items: flex-end;
  height: 100%;
  padding-top: 22px;
}

.stats-section {
  margin-bottom: 20px;
}

.stat-card {
  height: 120px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 20px;
}

.stat-icon {
  margin-right: 20px;
  padding: 15px;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-info {
  flex: 1;
}

.stat-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.table-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-header h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

:deep(.el-table) {
  font-size: 14px;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  color: #303133;
  font-weight: 600;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: #fafafa;
}

:deep(.el-table td) {
  white-space: nowrap;
}

:deep(.el-card__header) {
  padding: 18px 20px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-card__body) {
  padding: 20px;
}

@media (max-width: 1200px) {
  .filter-form .el-col {
    margin-bottom: 15px;
  }

  .stats-section .el-col {
    margin-bottom: 15px;
  }
}
</style>