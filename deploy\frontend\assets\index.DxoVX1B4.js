import{a as i,E as _}from"./index.BHZI5pdK.js";import{createCrudOptions as x}from"./crud.DpZl0TC1.js";import{d as h,h as w,j as B,k as n,b as C,o as g,w as k,l as R,x as V,u as v}from"./vue.BNx9QYep.js";import"./dictionary.DNsEqk19.js";const j=h({__name:"index",setup(y,{expose:s}){const e=w(!1),a=r=>{_.confirm("您确定要关闭?",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{r()}).catch(()=>{})},{crudBinding:c,crudRef:d,crudExpose:o}=i({createCrudOptions:x,context:{}}),{setSearchFormData:l,doRefresh:u}=o;return s({drawer:e,setSearchFormData:l,doRefresh:u}),B(()=>{o.doRefresh()}),(r,t)=>{const f=n("fs-crud"),m=n("el-drawer");return g(),C(m,{size:"70%",modelValue:e.value,"onUpdate:modelValue":t[0]||(t[0]=p=>e.value=p),direction:"rtl","destroy-on-close":"","before-close":a},{default:k(()=>[R(f,V({ref_key:"crudRef",ref:d},v(c)),null,16)]),_:1},8,["modelValue"])}}});export{j as default};
