@echo off
chcp 65001 >nul
echo ====================================================
echo 华绿系统 - 生产环境构建脚本
echo ====================================================
echo.

:: 设置颜色
color 0A

:: 检查Node.js是否安装
echo [1/6] 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未检测到Node.js，请先安装Node.js
    echo 下载地址：https://nodejs.org/
    pause
    exit /b 1
)
echo ✅ Node.js环境检查通过

:: 检查Python环境
echo.
echo [2/6] 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未检测到Python，请先安装Python
    pause
    exit /b 1
)
echo ✅ Python环境检查通过

:: 构建前端
echo.
echo [3/6] 构建Vue3前端...
cd web
echo 正在安装前端依赖...
call npm install
if %errorlevel% neq 0 (
    echo ❌ 前端依赖安装失败
    pause
    exit /b 1
)

echo 正在构建生产版本...
call npm run build
if %errorlevel% neq 0 (
    echo ❌ 前端构建失败
    pause
    exit /b 1
)
echo ✅ 前端构建完成

:: 检查构建结果
if not exist "dist" (
    echo ❌ 错误：构建目录不存在
    pause
    exit /b 1
)
echo ✅ 前端构建文件已生成到 web/dist 目录

:: 处理后端
cd ..\backend
echo.
echo [4/6] 准备Django后端...

:: 安装后端依赖
echo 正在安装后端依赖...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ 后端依赖安装失败
    pause
    exit /b 1
)

:: 收集静态文件
echo 正在收集静态文件...
python manage.py collectstatic --noinput
if %errorlevel% neq 0 (
    echo ❌ 静态文件收集失败
    pause
    exit /b 1
)
echo ✅ 后端准备完成

:: 创建部署包
cd ..
echo.
echo [5/6] 创建部署包...

:: 创建部署目录
if exist "deploy" rmdir /s /q deploy
mkdir deploy
mkdir deploy\backend
mkdir deploy\frontend

:: 复制后端文件
echo 正在复制后端文件...
xcopy backend deploy\backend /E /I /H /Y >nul
echo ✅ 后端文件复制完成

:: 复制前端构建文件
echo 正在复制前端构建文件...
xcopy web\dist deploy\frontend /E /I /H /Y >nul
echo ✅ 前端文件复制完成

:: 创建部署说明文件
echo.
echo [6/6] 生成部署文档...
(
echo 华绿系统 - 部署包说明
echo ========================
echo.
echo 目录结构：
echo   deploy/
echo   ├── backend/          # Django后端代码
echo   └── frontend/         # Vue3前端构建文件
echo.
echo 部署步骤：
echo 1. 将整个deploy文件夹上传到服务器
echo 2. 配置Web服务器（Nginx/Apache）
echo 3. 配置Python环境和依赖
echo 4. 配置数据库连接
echo 5. 启动Django服务
echo.
echo 详细部署说明请参考项目文档。
echo.
echo 构建时间：%date% %time%
) > deploy\部署说明.txt

:: 创建服务器启动脚本
(
echo #!/bin/bash
echo # 华绿系统服务器启动脚本
echo echo "启动华绿系统..."
echo cd backend
echo python manage.py runserver 0.0.0.0:8000
) > deploy\start_server.sh

:: 创建Windows启动脚本
(
echo @echo off
echo echo 启动华绿系统...
echo cd backend
echo python manage.py runserver 0.0.0.0:8000
echo pause
) > deploy\start_server.bat

echo ✅ 部署文档生成完成

:: 显示结果
echo.
echo ====================================================
echo 🎉 构建完成！
echo ====================================================
echo.
echo 📁 部署文件位置：%cd%\deploy
echo 📄 部署说明：%cd%\deploy\部署说明.txt
echo.
echo 📊 构建统计：
for /f %%i in ('dir deploy\backend /s /-c ^| find "个文件"') do echo   后端文件：%%i
for /f %%i in ('dir deploy\frontend /s /-c ^| find "个文件"') do echo   前端文件：%%i
echo.
echo 🚀 下一步：
echo   1. 将 deploy 文件夹上传到服务器
echo   2. 按照部署说明配置服务器环境
echo   3. 启动系统服务
echo.
echo ✅ 准备就绪，可以部署到生产环境！
echo.
pause
