import f from"./index.Dr6Zgea_.js";import x from"./index.mYMWMx6i.js";import{r as p}from"./index.BHZI5pdK.js";import{d as b,h as k,g as w,k as c,b as y,o as C,w as l,l as s,e as r,u as v}from"./vue.BNx9QYep.js";import{_ as I}from"./_plugin-vue_export-helper.DlAUqK2U.js";import"./index.1YhuLAe9.js";import"./api.BEBRHkAk.js";function D(m){return p({url:"/api/system/role/",method:"get",params:m})}function T(){return p({url:"/api/system/column/get_models/",method:"get"})}const R={class:"columns-box columns-left"},B={class:"columns-box columns-center"},$={class:"columns-box columns-right"},q=b({__name:"index",setup(m){const u=k(null);let o=w({role:"",model:"",app:"",menu:""});const _=async(e,t)=>{const a=await D(e);t(a)},d=async(e,t)=>{const a=await T();a.data.forEach(n=>{n.showText=`${n.app}-${n.title}(${n.key})`}),t(a)},h=()=>{var e;if(o.role&&o.model&&o.app){(e=u.value)==null||e.fetchData(o);return}},i=(e,t)=>{e==="role"&&(o.role=t.id),e==="menu"&&(o.menu=t.id),e==="model"&&(o.model=t.key,o.app=t.app),h()};return(e,t)=>{const a=c("el-col"),n=c("el-row"),g=c("fs-page");return C(),y(g,{class:"columns"},{default:l(()=>[s(n,{class:"columns-el-row",gutter:10},{default:l(()=>[s(a,{span:6},{default:l(()=>[r("div",R,[s(f,{title:"角色",type:"role",showPagination:"",onFetchData:_,onItemClick:i})])]),_:1}),s(a,{span:8},{default:l(()=>[r("div",B,[s(f,{title:"模型表",type:"model",label:"showText",value:"key",onFetchData:d,onItemClick:i})])]),_:1}),s(a,{span:10},{default:l(()=>[r("div",$,[s(x,{ref_key:"columnsTableRef",ref:u,currentInfo:v(o)},null,8,["currentInfo"])])]),_:1})]),_:1})]),_:1})}}}),j=I(q,[["__scopeId","data-v-cdb3dd1a"]]);export{j as default};
