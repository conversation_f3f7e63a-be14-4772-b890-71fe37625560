import{a4 as I,a3 as L}from"./index.BHZI5pdK.js";import{G as $}from"./api.FR9XnnSw.js";import{_ as j}from"./addTabs.vue_vue_type_script_setup_true_lang.COGvJEVA.js";import{_ as v}from"./addContent.vue_vue_type_script_setup_true_lang.1fd88dGK.js";import q from"./formContent.C3IjUmZh.js";import{d as g,h as p,j as E,k as o,b as i,o as r,w as l,e as d,l as a,q as f,$ as _,u,m as c,a as w,F as M,p as R,f as A}from"./vue.BNx9QYep.js";import"./associationTable.vue_vue_type_script_setup_true_lang.sUPUexxV.js";import"./dictionary.DNsEqk19.js";import"./crudTable.vue_vue_type_script_setup_true_lang.D7IoA8Cq.js";import"./_plugin-vue_export-helper.DlAUqK2U.js";const H={class:"yxt-flex-between"},J={key:0,slot:"label"},K=g({name:"config"}),le=g({...K,setup(O){let n=p(!1),s=p(!1),m=p("base"),b=p([]);const C=()=>{$({limit:999,parent__isnull:!0}).then(k=>{let e=k.data;e.push({title:"无",icon:"el-icon-plus",key:"null"}),b.value=e})};return E(()=>{C()}),(k,e)=>{const x=o("el-tag"),y=o("el-button"),z=o("el-button-group"),T=o("el-header"),V=o("el-drawer"),B=o("el-col"),N=o("el-row"),U=o("el-tab-pane"),D=o("el-tabs"),F=o("el-card");return r(),i(F,null,{default:l(()=>[d("div",null,[a(T,null,{default:l(()=>[d("div",H,[d("div",null,[a(x,null,{default:l(()=>e[5]||(e[5]=[f("系统配置:您可以对您的网站进行自定义配置")])),_:1,__:[5]})]),d("div",null,[a(z,null,{default:l(()=>[a(y,{type:"primary",size:"small",icon:u(I),onClick:e[0]||(e[0]=t=>_(n)?n.value=!0:n=!0)},{default:l(()=>e[6]||(e[6]=[f(" 添加分组 ")])),_:1,__:[6]},8,["icon"]),a(y,{size:"small",type:"warning",icon:u(L),onClick:e[1]||(e[1]=t=>_(s)?s.value=!0:s=!0)},{default:l(()=>e[7]||(e[7]=[f(" 添加内容 ")])),_:1,__:[7]},8,["icon"])]),_:1})])])]),_:1})]),d("div",null,[u(n)?(r(),i(V,{key:0,title:"添加分组",modelValue:u(n),"onUpdate:modelValue":e[2]||(e[2]=t=>_(n)?n.value=t:n=t),direction:"rtl",size:"30%"},{default:l(()=>[a(j)]),_:1},8,["modelValue"])):c("",!0)]),d("div",null,[u(s)?(r(),i(V,{key:0,title:"添加内容",modelValue:u(s),"onUpdate:modelValue":e[3]||(e[3]=t=>_(s)?s.value=t:s=t),direction:"rtl",size:"30%"},{default:l(()=>[a(v)]),_:1},8,["modelValue"])):c("",!0)]),a(D,{type:"border-card",modelValue:u(m),"onUpdate:modelValue":e[4]||(e[4]=t=>_(m)?m.value=t:m=t)},{default:l(()=>[(r(!0),w(M,null,R(u(b),(t,G)=>(r(),i(U,{key:G,label:t.title,name:t.key},{default:l(()=>[t.icon?(r(),w("span",J,[d("i",{class:A(t.icon),style:{"font-weight":"1000","font-size":"16px"}},null,2)])):c("",!0),t.icon?(r(),i(N,{key:1},{default:l(()=>[a(B,{offset:4,span:8},{default:l(()=>[a(v)]),_:1})]),_:1})):(r(),i(q,{key:2,options:t,editableTabsItem:t},null,8,["options","editableTabsItem"]))]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])]),_:1})}}});export{le as default};
