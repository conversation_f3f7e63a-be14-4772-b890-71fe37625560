import{r as d,v as b,X as a}from"./index.BHZI5pdK.js";import{a as u}from"./authFunction.D3Be3hRy.js";import{h as v,I as c}from"./vue.BNx9QYep.js";function R(t){return t.authorized=1,d({url:"/api/system/role/get_role_users/",method:"get",params:t})}function y(t,n){return d({url:`/api/system/role/${t}/remove_role_user/`,method:"delete",data:{user_id:n}})}const _=function({crudExpose:t,context:n}){const f=async e=>await R(e),m=async({form:e,row:l})=>{},h=async({row:e})=>await y(t.crudRef.value.getSearchFormData().role_id,[e.id]),p=async({form:e})=>{},o=v([]),g=e=>{const s=t.getTableData().filter(r=>!e.includes(r));a.arrayEach(e,r=>{a.pluck(o.value,"id").includes(r.id)||(o.value=a.union(o.value,[r]))}),a.arrayEach(s,r=>{o.value=a.remove(o.value,i=>i.id!==r.id)})},w=()=>{const e=t.getBaseTableRef(),l=t.getTableData(),s=a.filter(l,r=>a.pluck(o.value,"id").includes(r.id));c(()=>{a.arrayEach(s,r=>{e.toggleRowSelection(r,!0)})})};return{selectedRows:o,crudOptions:{request:{pageRequest:f,addRequest:p,editRequest:m,delRequest:h},actionbar:{buttons:{add:{show:u("role:AuthorizedAdd"),click:e=>{n.subUserRef.value.dialog=!0,c(()=>{n.subUserRef.value.setSearchFormData({form:{role_id:t.crudRef.value.getSearchFormData().role_id}}),n.subUserRef.value.doRefresh()})}}}},rowHandle:{fixed:"left",width:120,show:u("role:AuthorizedDel"),buttons:{view:{show:!1},edit:{show:!1},remove:{iconRight:"Delete",show:!0}}},table:{rowKey:"id",onSelectionChange:g,onRefreshed:()=>w()},columns:{$checked:{title:"选择",form:{show:!1},column:{show:u("role:AuthorizedDel"),type:"selection",align:"center",width:"55px",columnSetDisabled:!0}},_index:{title:"序号",form:{show:!1},column:{align:"center",width:"70px",columnSetDisabled:!0,formatter:e=>{let l=e.index??1,s=t.crudBinding.value.pagination;return((s.currentPage??1)-1)*s.pageSize+l+1}}},name:{title:"用户名",search:{show:!0,component:{props:{clearable:!0}}},type:"text",form:{show:!1}},dept:{title:"部门",show:!0,type:"dict-tree",column:{name:"text",formatter({value:e,row:l,index:s}){return l.dept__name}},search:{show:!0,disabled:!0,col:{span:6},component:{multiple:!1,props:{checkStrictly:!0,clearable:!0,filterable:!0}}},form:{show:!1},dict:b({isTree:!0,url:"/api/system/dept/all_dept/",value:"id",label:"name"})}}}}},k=Object.freeze(Object.defineProperty({__proto__:null,createCrudOptions:_},Symbol.toStringTag,{value:"Module"}));export{k as a,_ as c,y as r};
