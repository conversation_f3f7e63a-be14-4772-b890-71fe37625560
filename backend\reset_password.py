#!/usr/bin/env python
"""
用户密码重置脚本
用于重置用户密码为默认密码
"""

import os
import sys
import django
import hashlib

# 设置Django环境
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings')
django.setup()

from dvadmin.system.models import Users
from django.contrib.auth.hashers import make_password

def reset_password_to_default(username=None, new_password="admin123"):
    """重置密码为默认密码"""
    print("=" * 50)
    print("华绿系统 - 密码重置工具")
    print("=" * 50)
    
    # MD5加密密码
    md5_password = hashlib.md5(new_password.encode('utf-8')).hexdigest()
    hashed_password = make_password(md5_password)
    
    if username:
        # 重置指定用户密码
        try:
            user = Users.objects.get(username=username)
            user.password = hashed_password
            user.save()
            print(f"✅ 已重置用户 {username} 的密码为: {new_password}")
        except Users.DoesNotExist:
            print(f"❌ 用户 {username} 不存在")
    else:
        # 重置所有管理员用户密码
        admin_users = ['superadmin', 'admin', 'hladmin', 'superhl']
        
        print("正在重置管理员用户密码...")
        reset_count = 0
        
        for admin_username in admin_users:
            try:
                user = Users.objects.get(username=admin_username)
                user.password = hashed_password
                user.save()
                print(f"✅ 已重置用户 {admin_username} 的密码为: {new_password}")
                reset_count += 1
            except Users.DoesNotExist:
                print(f"❌ 用户 {admin_username} 不存在")
        
        print(f"\n🎉 密码重置完成！共重置了 {reset_count} 个用户的密码")

def show_all_users():
    """显示所有用户信息"""
    print("系统中的所有用户:")
    print("-" * 80)
    print(f"{'用户名':<15} {'姓名':<15} {'超级用户':<10} {'激活状态':<10} {'最后登录'}")
    print("-" * 80)
    
    users = Users.objects.all()
    for user in users:
        last_login = user.last_login.strftime('%Y-%m-%d %H:%M:%S') if user.last_login else '从未登录'
        is_super = '是' if user.is_superuser else '否'
        is_active = '是' if user.is_active else '否'
        print(f"{user.username:<15} {user.name:<15} {is_super:<10} {is_active:<10} {last_login}")

def main():
    """主函数"""
    if len(sys.argv) == 1:
        # 没有参数，显示帮助信息
        print("使用方法:")
        print("  python reset_password.py --all                    # 重置所有管理员密码为 admin123")
        print("  python reset_password.py --user admin             # 重置指定用户密码为 admin123")
        print("  python reset_password.py --user admin --pwd 123   # 重置指定用户密码为指定密码")
        print("  python reset_password.py --list                   # 查看所有用户列表")
        print("")
        show_all_users()
        
    elif len(sys.argv) == 2:
        if sys.argv[1] == '--all':
            reset_password_to_default()
        elif sys.argv[1] == '--list':
            show_all_users()
        else:
            print("❌ 无效的参数")
            
    elif len(sys.argv) == 3:
        if sys.argv[1] == '--user':
            reset_password_to_default(username=sys.argv[2])
        else:
            print("❌ 无效的参数")
            
    elif len(sys.argv) == 5:
        if sys.argv[1] == '--user' and sys.argv[3] == '--pwd':
            reset_password_to_default(username=sys.argv[2], new_password=sys.argv[4])
        else:
            print("❌ 无效的参数")
    else:
        print("❌ 参数错误")

if __name__ == '__main__':
    main()
