import "./chunk-LK32TJAX.js";

// node_modules/.pnpm/json-editor-vue3@1.1.1/node_modules/json-editor-vue3/lib/index.js
import JsonEditorVue from "C:/HL_python/django-vue3-admin-master/web/node_modules/.pnpm/json-editor-vue3@1.1.1/node_modules/json-editor-vue3/lib/json-editor.vue";
var lib_default = JsonEditorVue;
JsonEditorVue.install = function install(Vue) {
  Vue.component(JsonEditorVue.name, JsonEditorVue);
};
export {
  lib_default as default
};
//# sourceMappingURL=json-editor-vue3.js.map
