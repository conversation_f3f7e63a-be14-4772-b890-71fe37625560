import{<PERSON><PERSON>bj as r,UpdateObj as i,AddObj as o,GetList as d}from"./api.CkJHuGUE.js";import{v as c}from"./index.BHZI5pdK.js";import"./vue.BNx9QYep.js";const k=function({crudExpose:l,context:s}){return{crudOptions:{actionbar:{show:!1},toolbar:{show:!1},request:{pageRequest:async e=>{var a;const t=(a=s==null?void 0:s.taskItem)==null?void 0:a.name;return t&&(e.periodic_task_name=t),await d({...e})},addRequest:async({form:e})=>await o(e),editRequest:async({form:e,row:t})=>(e.id=t.id,await i(e)),delRequest:async({row:e})=>await r(e.id)},rowHandle:{show:!1},form:{col:{span:24},labelWidth:"110px",wrapper:{is:"el-dialog",width:"600px"}},columns:{_index:{title:"序号",form:{show:!1},column:{align:"center",width:"60px",columnSetDisabled:!0,formatter:e=>{let t=e.index??1,a=l.crudBinding.value.pagination;return((a.currentPage??1)-1)*a.pageSize+t+1}}},task_id:{title:"任务ID",search:{show:!0},column:{width:200},type:"text"},task_name:{title:"任务名称",search:{show:!0},column:{minWidth:200},type:"text"},periodic_task_name:{title:"周期任务名称",search:{show:!1},column:{width:200},type:"text"},task_kwargs:{title:"请求参数",search:{show:!1},column:{width:120},type:"text"},status:{title:"执行状态",search:{show:!0},type:"dict-select",column:{width:100},dict:c({data:[{label:"执行成功",value:"SUCCESS",color:"success",effect:"dark"},{label:"已开始",value:"STARTED",effect:"dark"},{label:"已取消",value:"REVOKED",effect:"dark"},{label:"重试中",value:"RETRY",effect:"dark"},{label:"已收到",value:"RECEIVED",effect:"dark"},{label:"待定中",value:"PENDING",effect:"dark"},{label:"执行失败",value:"FAILURE",effect:"dark",color:"error"}]})},result:{title:"执行结果",search:{show:!1},column:{width:120},type:"text"},date_done:{title:"执行完成时间",type:"datetime",search:{show:!1,col:{span:8}},column:{width:160},form:{show:!1},viewForm:{show:!0}},date_created:{title:"创建时间",type:"datetime",search:{show:!1,col:{span:8}},column:{width:160},form:{show:!1},viewForm:{show:!0}}}}}};export{k as createCrudOptions};
