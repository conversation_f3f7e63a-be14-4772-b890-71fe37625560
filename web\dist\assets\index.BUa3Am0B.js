const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.DxoVX1B4.js","assets/index.BHZI5pdK.js","assets/vue.BNx9QYep.js","assets/index.Dg-OhEXY.css","assets/crud.DpZl0TC1.js","assets/dictionary.DNsEqk19.js"])))=>i.map(i=>d[i]);
import{a as p,_ as d}from"./index.BHZI5pdK.js";import{createCrudOptions as i}from"./crud.fNJVDPOh.js";import{d as s,h as m,j as l,k as o,b as x,o as R,w as k,l as t,x as C,u as r,P as b}from"./vue.BNx9QYep.js";import"./dictionary.DNsEqk19.js";import"./authFunction.D3Be3hRy.js";const g=s({name:"dictionary"}),A=s({...g,setup(h){const n=b(()=>d(()=>import("./index.DxoVX1B4.js"),__vite__mapDeps([0,1,2,3,4,5]))),e=m(),{crudBinding:c,crudRef:a,crudExpose:_}=p({createCrudOptions:i,context:{subDictRef:e}});return l(()=>{_.doRefresh()}),(y,D)=>{const f=o("fs-crud"),u=o("fs-page");return R(),x(u,null,{default:k(()=>[t(f,C({ref_key:"crudRef",ref:a},r(c)),null,16),t(r(n),{ref_key:"subDictRef",ref:e},null,512)]),_:1})}}});export{A as default};
