{"version": 3, "sources": ["../../.pnpm/monaco-editor@0.52.2/node_modules/monaco-editor/esm/vs/basic-languages/r/r.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/r/r.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".r\",\n  roxygen: [\n    \"@alias\",\n    \"@aliases\",\n    \"@assignee\",\n    \"@author\",\n    \"@backref\",\n    \"@callGraph\",\n    \"@callGraphDepth\",\n    \"@callGraphPrimitives\",\n    \"@concept\",\n    \"@describeIn\",\n    \"@description\",\n    \"@details\",\n    \"@docType\",\n    \"@encoding\",\n    \"@evalNamespace\",\n    \"@evalRd\",\n    \"@example\",\n    \"@examples\",\n    \"@export\",\n    \"@exportClass\",\n    \"@exportMethod\",\n    \"@exportPattern\",\n    \"@family\",\n    \"@field\",\n    \"@formals\",\n    \"@format\",\n    \"@import\",\n    \"@importClassesFrom\",\n    \"@importFrom\",\n    \"@importMethodsFrom\",\n    \"@include\",\n    \"@inherit\",\n    \"@inheritDotParams\",\n    \"@inheritParams\",\n    \"@inheritSection\",\n    \"@keywords\",\n    \"@md\",\n    \"@method\",\n    \"@name\",\n    \"@noMd\",\n    \"@noRd\",\n    \"@note\",\n    \"@param\",\n    \"@rawNamespace\",\n    \"@rawRd\",\n    \"@rdname\",\n    \"@references\",\n    \"@return\",\n    \"@S3method\",\n    \"@section\",\n    \"@seealso\",\n    \"@setClass\",\n    \"@slot\",\n    \"@source\",\n    \"@template\",\n    \"@templateVar\",\n    \"@title\",\n    \"@TODO\",\n    \"@usage\",\n    \"@useDynLib\"\n  ],\n  constants: [\n    \"NULL\",\n    \"FALSE\",\n    \"TRUE\",\n    \"NA\",\n    \"Inf\",\n    \"NaN\",\n    \"NA_integer_\",\n    \"NA_real_\",\n    \"NA_complex_\",\n    \"NA_character_\",\n    \"T\",\n    \"F\",\n    \"LETTERS\",\n    \"letters\",\n    \"month.abb\",\n    \"month.name\",\n    \"pi\",\n    \"R.version.string\"\n  ],\n  keywords: [\n    \"break\",\n    \"next\",\n    \"return\",\n    \"if\",\n    \"else\",\n    \"for\",\n    \"in\",\n    \"repeat\",\n    \"while\",\n    \"array\",\n    \"category\",\n    \"character\",\n    \"complex\",\n    \"double\",\n    \"function\",\n    \"integer\",\n    \"list\",\n    \"logical\",\n    \"matrix\",\n    \"numeric\",\n    \"vector\",\n    \"data.frame\",\n    \"factor\",\n    \"library\",\n    \"require\",\n    \"attach\",\n    \"detach\",\n    \"source\"\n  ],\n  special: [\"\\\\n\", \"\\\\r\", \"\\\\t\", \"\\\\b\", \"\\\\a\", \"\\\\f\", \"\\\\v\", \"\\\\'\", '\\\\\"', \"\\\\\\\\\"],\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.bracket\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  tokenizer: {\n    root: [\n      { include: \"@numbers\" },\n      { include: \"@strings\" },\n      [/[{}\\[\\]()]/, \"@brackets\"],\n      { include: \"@operators\" },\n      [/#'$/, \"comment.doc\"],\n      [/#'/, \"comment.doc\", \"@roxygen\"],\n      [/(^#.*$)/, \"comment\"],\n      [/\\s+/, \"white\"],\n      [/[,:;]/, \"delimiter\"],\n      [/@[a-zA-Z]\\w*/, \"tag\"],\n      [\n        /[a-zA-Z]\\w*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@constants\": \"constant\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ]\n    ],\n    // Recognize Roxygen comments\n    roxygen: [\n      [\n        /@\\w+/,\n        {\n          cases: {\n            \"@roxygen\": \"tag\",\n            \"@eos\": { token: \"comment.doc\", next: \"@pop\" },\n            \"@default\": \"comment.doc\"\n          }\n        }\n      ],\n      [\n        /\\s+/,\n        {\n          cases: {\n            \"@eos\": { token: \"comment.doc\", next: \"@pop\" },\n            \"@default\": \"comment.doc\"\n          }\n        }\n      ],\n      [/.*/, { token: \"comment.doc\", next: \"@pop\" }]\n    ],\n    // Recognize positives, negatives, decimals, imaginaries, and scientific notation\n    numbers: [\n      [/0[xX][0-9a-fA-F]+/, \"number.hex\"],\n      [/-?(\\d*\\.)?\\d+([eE][+\\-]?\\d+)?/, \"number\"]\n    ],\n    // Recognize operators\n    operators: [\n      [/<{1,2}-/, \"operator\"],\n      [/->{1,2}/, \"operator\"],\n      [/%[^%\\s]+%/, \"operator\"],\n      [/\\*\\*/, \"operator\"],\n      [/%%/, \"operator\"],\n      [/&&/, \"operator\"],\n      [/\\|\\|/, \"operator\"],\n      [/<</, \"operator\"],\n      [/>>/, \"operator\"],\n      [/[-+=&|!<>^~*/:$]/, \"operator\"]\n    ],\n    // Recognize strings, including those broken across lines\n    strings: [\n      [/'/, \"string.escape\", \"@stringBody\"],\n      [/\"/, \"string.escape\", \"@dblStringBody\"]\n    ],\n    stringBody: [\n      [\n        /\\\\./,\n        {\n          cases: {\n            \"@special\": \"string\",\n            \"@default\": \"error-token\"\n          }\n        }\n      ],\n      [/'/, \"string.escape\", \"@popall\"],\n      [/./, \"string\"]\n    ],\n    dblStringBody: [\n      [\n        /\\\\./,\n        {\n          cases: {\n            \"@special\": \"string\",\n            \"@default\": \"error-token\"\n          }\n        }\n      ],\n      [/\"/, \"string.escape\", \"@popall\"],\n      [/./, \"string\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AASA,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,SAAS;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,WAAW;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,SAAS,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,MAAM;AAAA,EAC/E,UAAU;AAAA,IACR,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,IAClD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,oBAAoB;AAAA,IACpD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,EAC1D;AAAA,EACA,WAAW;AAAA,IACT,MAAM;AAAA,MACJ,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,WAAW;AAAA,MACtB,CAAC,cAAc,WAAW;AAAA,MAC1B,EAAE,SAAS,aAAa;AAAA,MACxB,CAAC,OAAO,aAAa;AAAA,MACrB,CAAC,MAAM,eAAe,UAAU;AAAA,MAChC,CAAC,WAAW,SAAS;AAAA,MACrB,CAAC,OAAO,OAAO;AAAA,MACf,CAAC,SAAS,WAAW;AAAA,MACrB,CAAC,gBAAgB,KAAK;AAAA,MACtB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,aAAa;AAAA,YACb,cAAc;AAAA,YACd,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA;AAAA,IAEA,SAAS;AAAA,MACP;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,YAAY;AAAA,YACZ,QAAQ,EAAE,OAAO,eAAe,MAAM,OAAO;AAAA,YAC7C,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ,EAAE,OAAO,eAAe,MAAM,OAAO;AAAA,YAC7C,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,MAAM,EAAE,OAAO,eAAe,MAAM,OAAO,CAAC;AAAA,IAC/C;AAAA;AAAA,IAEA,SAAS;AAAA,MACP,CAAC,qBAAqB,YAAY;AAAA,MAClC,CAAC,iCAAiC,QAAQ;AAAA,IAC5C;AAAA;AAAA,IAEA,WAAW;AAAA,MACT,CAAC,WAAW,UAAU;AAAA,MACtB,CAAC,WAAW,UAAU;AAAA,MACtB,CAAC,aAAa,UAAU;AAAA,MACxB,CAAC,QAAQ,UAAU;AAAA,MACnB,CAAC,MAAM,UAAU;AAAA,MACjB,CAAC,MAAM,UAAU;AAAA,MACjB,CAAC,QAAQ,UAAU;AAAA,MACnB,CAAC,MAAM,UAAU;AAAA,MACjB,CAAC,MAAM,UAAU;AAAA,MACjB,CAAC,oBAAoB,UAAU;AAAA,IACjC;AAAA;AAAA,IAEA,SAAS;AAAA,MACP,CAAC,KAAK,iBAAiB,aAAa;AAAA,MACpC,CAAC,KAAK,iBAAiB,gBAAgB;AAAA,IACzC;AAAA,IACA,YAAY;AAAA,MACV;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,YAAY;AAAA,YACZ,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,KAAK,iBAAiB,SAAS;AAAA,MAChC,CAAC,KAAK,QAAQ;AAAA,IAChB;AAAA,IACA,eAAe;AAAA,MACb;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,YAAY;AAAA,YACZ,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,KAAK,iBAAiB,SAAS;AAAA,MAChC,CAAC,KAAK,QAAQ;AAAA,IAChB;AAAA,EACF;AACF;", "names": []}