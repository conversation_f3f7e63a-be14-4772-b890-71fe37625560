import{d as f,R as p,k as i,a as m,o as v,l as o,w as t,e as l,u as d}from"./vue.BNx9QYep.js";import{W as w,Z as h,w as C,$ as k}from"./index.BHZI5pdK.js";import{_ as x}from"./_plugin-vue_export-helper.DlAUqK2U.js";const y={class:"home-container"},g={class:"card-header"},z={class:"welcome-content"},A={class:"quick-actions"},E={class:"action-content"},F={class:"action-content"},B={class:"action-content"},N={class:"system-info"},R=f({__name:"index",setup(T){const u=p(),_=()=>{u.push("/data-analysis")},c=()=>{u.push("/price-analysis")};return(V,s)=>{const e=i("el-icon"),a=i("el-card"),n=i("el-col"),r=i("el-row");return v(),m("div",y,[o(a,{class:"welcome-card"},{header:t(()=>[l("div",g,[o(e,{size:"24",color:"#409EFF"},{default:t(()=>[o(d(k))]),_:1}),s[0]||(s[0]=l("span",{class:"title"},"欢迎使用华绿数据分析系统",-1))])]),default:t(()=>[l("div",z,[s[9]||(s[9]=l("h2",null,"数据驱动决策，智能分析未来",-1)),s[10]||(s[10]=l("p",null,"实时监控销售数据，深度分析价格趋势，助力企业科学决策",-1)),l("div",A,[o(r,{gutter:20},{default:t(()=>[o(n,{span:8},{default:t(()=>[o(a,{class:"action-card",shadow:"hover",onClick:_},{default:t(()=>[l("div",E,[o(e,{size:"48",color:"#409EFF"},{default:t(()=>[o(d(w))]),_:1}),s[1]||(s[1]=l("h3",null,"数据分析",-1)),s[2]||(s[2]=l("p",null,"查看销售数据概览和趋势分析",-1))])]),_:1})]),_:1}),o(n,{span:8},{default:t(()=>[o(a,{class:"action-card",shadow:"hover",onClick:c},{default:t(()=>[l("div",F,[o(e,{size:"48",color:"#67C23A"},{default:t(()=>[o(d(h))]),_:1}),s[3]||(s[3]=l("h3",null,"价格分析",-1)),s[4]||(s[4]=l("p",null,"分析客户价格偏离和异常情况",-1))])]),_:1})]),_:1}),o(n,{span:8},{default:t(()=>[o(a,{class:"action-card",shadow:"hover"},{default:t(()=>[l("div",B,[o(e,{size:"48",color:"#E6A23C"},{default:t(()=>[o(d(C))]),_:1}),s[5]||(s[5]=l("h3",null,"报表中心",-1)),s[6]||(s[6]=l("p",null,"生成各类业务报表和统计图表",-1))])]),_:1})]),_:1})]),_:1})]),l("div",N,[o(r,{gutter:20},{default:t(()=>[o(n,{span:12},{default:t(()=>s[7]||(s[7]=[l("div",{class:"info-item"},[l("h4",null,"系统特色"),l("ul",null,[l("li",null,"实时数据同步，确保数据准确性"),l("li",null,"多维度分析，支持灵活筛选"),l("li",null,"智能异常检测，及时发现问题"),l("li",null,"可视化图表，直观展示趋势")])],-1)])),_:1,__:[7]}),o(n,{span:12},{default:t(()=>s[8]||(s[8]=[l("div",{class:"info-item"},[l("h4",null,"使用指南"),l("ul",null,[l("li",null,'点击"数据分析"查看销售概览'),l("li",null,'使用"价格分析"监控价格异常'),l("li",null,"支持数据导出和报表生成"),l("li",null,"可按公司、时间等维度筛选")])],-1)])),_:1,__:[8]})]),_:1})])])]),_:1})])}}}),P=x(R,[["__scopeId","data-v-f1636d69"]]);export{P as default};
