<template>
	<div class="home-bg">
		<div class="welcome-card">
			<div class="welcome-header">
				<h1 class="welcome-title">欢迎</h1>
			</div>
			<div class="welcome-content">
				<p class="welcome-username">{{ username }}</p>
				<p class="welcome-quote">{{ randomQuote }}</p>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { storeToRefs } from 'pinia';
import { useUserInfo } from '/@/stores/userInfo';

// 获取用户名（如无则显示"用户"）
const { userInfos } = storeToRefs(useUserInfo());
const username = computed(() => userInfos.value?.name || '用户');

// 励志语句
const quotes = [
	'今天的努力，是明天的基石。',
	'成功不是偶然，而是日积月累的结果。',
	'每一次尝试都是成长的机会。',
	'坚持不懈，直到成功。',
	'态度决定高度，细节决定成败。',
	'没有口水与汗水，就没有成功的泪水。',
	'机会总是留给有准备的人。',
	'行动是成功的阶梯，行动越多，登得越高。',
	'不经历风雨，怎能见彩虹。',
	'志之所趋，无远勿届，穷山距海，不能限也。',
];
const randomQuote = ref(quotes[Math.floor(Math.random() * quotes.length)]);
</script>

<style scoped lang="scss">
.home-bg {
	min-height: 100vh;
	display: flex;
	align-items: center;
	justify-content: center;
	background: linear-gradient(135deg, #e0e7ff 0%, #f0fdfa 100%);
}
.welcome-card {
	background: rgba(255,255,255,0.95);
	border-radius: 18px;
	box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.18);
	padding: 48px 60px 36px 60px;
	min-width: 340px;
	max-width: 90vw;
	text-align: center;
	transition: box-shadow 0.3s;
}
.welcome-card:hover {
	box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.22);
}
.welcome-header {
	margin-bottom: 18px;
}
.welcome-title {
	font-size: 2.6rem;
	font-weight: 700;
	color: #3b82f6;
	letter-spacing: 2px;
}
.welcome-content {
	margin-top: 10px;
}
.welcome-username {
	font-size: 1.3rem;
	color: #374151;
	margin-bottom: 12px;
	font-weight: 500;
}
.welcome-quote {
	font-size: 1.1rem;
	color: #64748b;
	font-style: italic;
	margin-top: 8px;
}
</style>
