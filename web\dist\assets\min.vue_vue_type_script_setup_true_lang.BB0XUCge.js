import{d as E,h as v,c as U,v as V,k as p,b as A,o as C,w as o,l as n,q as m,a as G,F as H,p as I,s as J}from"./vue.BNx9QYep.js";const M=E({__name:"min",props:{cron:{},check:{type:Function}},emits:["update"],setup(B,{expose:F,emit:N}){const i=N,c=B,a=v(1),t=v(1),s=v(2),d=v(0),r=v(1),f=v([]),b=c.check;F({cycle01:t,cycle02:s,average01:d,average02:r,checkboxList:f});const _=U(()=>(t.value=b(t.value,0,58),s.value=b(s.value,t.value?t.value+1:1,59),t.value+"-"+s.value)),y=U(()=>(d.value=b(d.value,0,58),r.value=b(r.value,1,59-d.value||0),d.value+"/"+r.value)),w=U(()=>{let u=f.value.join();return u==""?"*":u});V(a,(u,e)=>{D()}),V(_,(u,e)=>{L()}),V(y,(u,e)=>{O()}),V(w,(u,e)=>{S()}),V(c,(u,e)=>{T(u.cron.min)});function T(u){u&&(u=="*"?a.value=1:typeof u=="string"&&u.indexOf("-")>-1?a.value=2:typeof u=="string"&&u.indexOf("/")>-1?a.value=3:a.value=4)}function D(){switch(a.value){case 1:i("update","min","*","min");break;case 2:i("update","min",_.value,"min");break;case 3:i("update","min",y.value,"min");break;case 4:i("update","min",w.value,"min");break}}function L(){a.value==2&&i("update","min",_.value,"min")}function O(){a.value==3&&i("update","min",y.value,"min")}function S(){a.value==4&&i("update","min",w.value,"min")}return(u,e)=>{const k=p("el-radio"),x=p("el-form-item"),g=p("el-input-number"),j=p("el-option"),q=p("el-select"),z=p("el-form");return C(),A(z,{size:"small"},{default:o(()=>[n(x,null,{default:o(()=>[n(k,{modelValue:a.value,"onUpdate:modelValue":e[0]||(e[0]=l=>a.value=l),label:1},{default:o(()=>e[9]||(e[9]=[m(" 分钟，允许的通配符[, - * /] ")])),_:1,__:[9]},8,["modelValue"])]),_:1}),n(x,null,{default:o(()=>[n(k,{modelValue:a.value,"onUpdate:modelValue":e[3]||(e[3]=l=>a.value=l),label:2},{default:o(()=>[e[10]||(e[10]=m(" 周期从 ")),n(g,{modelValue:t.value,"onUpdate:modelValue":e[1]||(e[1]=l=>t.value=l),min:0,max:58},null,8,["modelValue"]),e[11]||(e[11]=m(" - ")),n(g,{modelValue:s.value,"onUpdate:modelValue":e[2]||(e[2]=l=>s.value=l),min:t.value?t.value+1:1,max:59},null,8,["modelValue","min"]),e[12]||(e[12]=m(" 分钟 "))]),_:1,__:[10,11,12]},8,["modelValue"])]),_:1}),n(x,null,{default:o(()=>[n(k,{modelValue:a.value,"onUpdate:modelValue":e[6]||(e[6]=l=>a.value=l),label:3},{default:o(()=>[e[13]||(e[13]=m(" 从 ")),n(g,{modelValue:d.value,"onUpdate:modelValue":e[4]||(e[4]=l=>d.value=l),min:0,max:58},null,8,["modelValue"]),e[14]||(e[14]=m(" 分钟开始，每 ")),n(g,{modelValue:r.value,"onUpdate:modelValue":e[5]||(e[5]=l=>r.value=l),min:1,max:59-d.value||0},null,8,["modelValue","max"]),e[15]||(e[15]=m(" 分钟执行一次 "))]),_:1,__:[13,14,15]},8,["modelValue"])]),_:1}),n(x,null,{default:o(()=>[n(k,{modelValue:a.value,"onUpdate:modelValue":e[8]||(e[8]=l=>a.value=l),label:4},{default:o(()=>[e[16]||(e[16]=m(" 指定 ")),n(q,{clearable:"",modelValue:f.value,"onUpdate:modelValue":e[7]||(e[7]=l=>f.value=l),placeholder:"可多选",multiple:"",style:{width:"100%"}},{default:o(()=>[(C(),G(H,null,I(60,l=>n(j,{key:l,value:l-1},{default:o(()=>[m(J(l-1),1)]),_:2},1032,["value"])),64))]),_:1},8,["modelValue"])]),_:1,__:[16]},8,["modelValue"])]),_:1})]),_:1})}}});export{M as _};
